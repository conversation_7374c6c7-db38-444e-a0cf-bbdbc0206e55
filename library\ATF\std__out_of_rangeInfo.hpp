// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__out_of_range.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            
            using std__out_of_rangector_out_of_range5_ptr = void (WINAPIV*)(struct std::out_of_range*, std::basic_string<char>*);
            using std__out_of_rangector_out_of_range5_clbk = void (WINAPIV*)(struct std::out_of_range*, std::basic_string<char>*, std__out_of_rangector_out_of_range5_ptr);
            
            using std__out_of_rangector_out_of_range7_ptr = void (WINAPIV*)(struct std::out_of_range*, struct std::out_of_range*);
            using std__out_of_rangector_out_of_range7_clbk = void (WINAPIV*)(struct std::out_of_range*, struct std::out_of_range*, std__out_of_rangector_out_of_range7_ptr);
            
            using std__out_of_rangedtor_out_of_range9_ptr = void (WINAPIV*)(struct std::out_of_range*);
            using std__out_of_rangedtor_out_of_range9_clbk = void (WINAPIV*)(struct std::out_of_range*, std__out_of_rangedtor_out_of_range9_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPoint.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CPointctor_CPoint1_ptr = void (WINAPIV*)(struct CPoint*, int64_t);
        using CPointctor_CPoint1_clbk = void (WINAPIV*)(struct CPoint*, int64_t, CPointctor_CPoint1_ptr);
        
        using CPointctor_CPoint2_ptr = void (WINAPIV*)(struct CPoint*, int, int);
        using CPointctor_CPoint2_clbk = void (WINAPIV*)(struct CPoint*, int, int, CPointctor_CPoint2_ptr);
        
        using CPointctor_CPoint3_ptr = void (WINAPIV*)(struct CPoint*, struct tagPOINT);
        using CPointctor_CPoint3_clbk = void (WINAPIV*)(struct CPoint*, struct tagPOINT, CPointctor_CPoint3_ptr);
        
        using CPoint<PERSON>_CPoint4_ptr = void (WINAPIV*)(struct CPoint*, struct tagSIZE);
        using CPointctor_CPoint4_clbk = void (WINAPIV*)(struct CPoint*, struct tagSIZE, CPointctor_CPoint4_ptr);
        
        using CPointctor_CPoint5_ptr = void (WINAPIV*)(struct CPoint*);
        using CPointctor_CPoint5_clbk = void (WINAPIV*)(struct CPoint*, CPointctor_CPoint5_ptr);
        using CPointOffset6_ptr = void (WINAPIV*)(struct CPoint*, int, int);
        using CPointOffset6_clbk = void (WINAPIV*)(struct CPoint*, int, int, CPointOffset6_ptr);
        using CPointOffset7_ptr = void (WINAPIV*)(struct CPoint*, struct tagPOINT);
        using CPointOffset7_clbk = void (WINAPIV*)(struct CPoint*, struct tagPOINT, CPointOffset7_ptr);
        using CPointOffset8_ptr = void (WINAPIV*)(struct CPoint*, struct tagSIZE);
        using CPointOffset8_clbk = void (WINAPIV*)(struct CPoint*, struct tagSIZE, CPointOffset8_ptr);
        using CPointSetPoint9_ptr = void (WINAPIV*)(struct CPoint*, int, int);
        using CPointSetPoint9_clbk = void (WINAPIV*)(struct CPoint*, int, int, CPointSetPoint9_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HDC__.hpp>
#include <_charrange.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct _formatrange
    {
        HDC__ *hdc;
        HDC__ *hdcTarget;
        tagRECT rc;
        tagRECT rcPage;
        _charrange chrg;
    };
END_ATF_NAMESPACE

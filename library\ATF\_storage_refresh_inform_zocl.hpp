// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _storage_refresh_inform_zocl
    {
        char byStorageCode;
        char byItemNum;
        unsigned __int16 wSerial[100];
    public:
        _storage_refresh_inform_zocl();
        void ctor__storage_refresh_inform_zocl();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_storage_refresh_inform_zocl, 202>(), "_storage_refresh_inform_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _inven_download_result_zocl
    {
        struct  _list
        {
            char byTableCode;
            unsigned __int16 wItemIndex;
            unsigned __int64 dwDurPoint;
            char sClientIndex;
            unsigned int dwUptInfo;
            char byCsMethod;
            unsigned int dwT;
        };
        char byRetCode;
        char byBagNum;
        char bySlotNum;
        _list ItemSlotInfo[100];
    public:
        _inven_download_result_zocl();
        void ctor__inven_download_result_zocl();
        int size();
    };   
    #pragma pack(pop)
    static_assert(ATF::checkSize<_inven_download_result_zocl, 2103>(), "_inven_download_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $F8ABA08332B7A6F34100839D80E5DE1F
    {
      expuls_speedhack = 0x0,
      expuls_none_certify = 0x1,
      expuls_delay_certify = 0x2,
      expuls_sendbufferfull = 0x3,
      expuls_ccrfg_delaytime = 0x4,
      expuls_ccrfg_detect = 0x5,
      expuls_ccrfg_block = 0x6,
    };
END_ATF_NAMESPACE

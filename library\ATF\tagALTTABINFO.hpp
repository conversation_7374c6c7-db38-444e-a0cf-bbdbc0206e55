// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    struct tagALTTABINFO
    {
        unsigned int cbSize;
        int cItems;
        int cColumns;
        int cRows;
        int iColFocus;
        int iRowFocus;
        int cxItem;
        int cyItem;
        tagPOINT ptStart;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _ENTER_DUNGEON_NEW_POS
    {
        char byMapCode;
        unsigned __int16 wLayerIndex;
        float fPos[3];
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_ENTER_DUNGEON_NEW_POS, 16>(), "_ENTER_DUNGEON_NEW_POS");
END_ATF_NAMESPACE

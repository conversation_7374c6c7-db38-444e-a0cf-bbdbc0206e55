// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMapData.hpp>
#include <CRect.hpp>


START_ATF_NAMESPACE
    struct _coll_point
    {
        float m_fPosAbs[2];
        float m_fScrNor[2];
        float m_fReAbs[2];
        float m_fScrExt[2];
    public:
        void InitPoint(struct CMapData* pMap, float* pPos, struct CRect* prcWnd);
    };
END_ATF_NAMESPACE

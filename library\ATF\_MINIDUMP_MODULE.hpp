// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_MINIDUMP_LOCATION_DESCRIPTOR.hpp>
#include <tagVS_FIXEDFILEINFO.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _MINIDUMP_MODULE
    {
        unsigned __int64 BaseOfImage;
        unsigned int SizeOfImage;
        unsigned int CheckSum;
        unsigned int TimeDateStamp;
        unsigned int ModuleNameRva;
        tagVS_FIXEDFILEINFO VersionInfo;
        _MINIDUMP_LOCATION_DESCRIPTOR CvRecord;
        _MINIDUMP_LOCATION_DESCRIPTOR MiscRecord;
        unsigned __int64 Reserved0;
        unsigned __int64 Reserved1;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

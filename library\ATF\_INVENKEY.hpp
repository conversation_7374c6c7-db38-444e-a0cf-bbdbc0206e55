// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _INVENKEY
    {
        char bySlotIndex;
        char byTableCode;
        unsigned __int16 wItemIndex;
    public:
        int CovDBKey();
        bool IsFilled();
        int IsOverlapItem();
        void LoadDBKey(int pl_nKey);
        void SetRelease();
        _INVENKEY(char byInSlotIndex, char byInTableCode, uint16_t wInItemIndex);
        void ctor__INVENKEY(char byInSlotIndex, char byInTableCode, uint16_t wInItemIndex);
        _INVENKEY();
        void ctor__INVENKEY();
    };    
    static_assert(ATF::checkSize<_INVENKEY, 4>(), "_INVENKEY");
END_ATF_NAMESPACE

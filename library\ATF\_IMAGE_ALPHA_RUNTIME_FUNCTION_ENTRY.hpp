// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_ALPHA_RUNTIME_FUNCTION_ENTRY
    {
        unsigned int BeginAddress;
        unsigned int EndAddress;
        unsigned int ExceptionHandler;
        unsigned int HandlerData;
        unsigned int PrologEndAddress;
    };
END_ATF_NAMESPACE

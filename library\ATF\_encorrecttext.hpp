// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_charrange.hpp>
#include <tagNMHDR.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _encorrecttext
    {
        tagNMHDR nmhdr;
        _charrange chrg;
        unsigned __int16 seltyp;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

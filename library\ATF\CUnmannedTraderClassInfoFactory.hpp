// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderClassInfo.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderClassInfoFactory
    {
        std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > m_vecTable;
    public:
        CUnmannedTraderClassInfoFactory();
        void ctor_CUnmannedTraderClassInfoFactory();
        struct CUnmannedTraderClassInfo* Create(char* szType, unsigned int dwID);
        void Destroy();
        bool Regist(struct CUnmannedTraderClassInfo* pkType);
        ~CUnmannedTraderClassInfoFactory();
        void dtor_CUnmannedTraderClassInfoFactory();
    };    
    static_assert(ATF::checkSize<CUnmannedTraderClassInfoFactory, 40>(), "CUnmannedTraderClassInfoFactory");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_D3DORDERTYPE.hpp>


START_ATF_NAMESPACE
    struct _D3DRECTPATCH_INFO
    {
        unsigned int StartVertexOffsetWidth;
        unsigned int StartVertexOffsetHeight;
        unsigned int Width;
        unsigned int Height;
        unsigned int Stride;
        _D3DBASISTYPE Basis;
        _D3DORDERTYPE Order;
    };
END_ATF_NAMESPACE

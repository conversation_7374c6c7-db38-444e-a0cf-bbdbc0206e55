// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_IO_COUNTERS.hpp>
#include <_JOBOBJECT_BASIC_LIMIT_INFORMATION.hpp>


START_ATF_NAMESPACE
    struct _JOBOBJECT_EXTENDED_LIMIT_INFORMATION
    {
        _JOBOBJECT_BASIC_LIMIT_INFORMATION BasicLimitInformation;
        _IO_COUNTERS IoInfo;
        unsigned __int64 ProcessMemoryLimit;
        unsigned __int64 JobMemoryLimit;
        unsigned __int64 PeakProcessMemoryUsed;
        unsigned __int64 PeakJobMemoryUsed;
    };
END_ATF_NAMESPACE

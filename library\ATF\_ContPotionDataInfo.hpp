// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ContPotionData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _ContPotionDataGetEffectIndex2_ptr = unsigned int (WINAPIV*)(struct _ContPotionData*);
        using _ContPotionDataGetEffectIndex2_clbk = unsigned int (WINAPIV*)(struct _ContPotionData*, _ContPotionDataGetEffectIndex2_ptr);
        using _ContPotionDataInit4_ptr = void (WINAPIV*)(struct _ContPotionData*);
        using _ContPotionDataInit4_clbk = void (WINAPIV*)(struct _ContPotionData*, _ContPotionDataInit4_ptr);
        using _ContPotionDataIsLive6_ptr = bool (WINAPIV*)(struct _ContPotionData*);
        using _ContPotionDataIsLive6_clbk = bool (WINAPIV*)(struct _ContPotionData*, _ContPotionDataIsLive6_ptr);
        using _ContPotionDataSet8_ptr = void (WINAPIV*)(struct _ContPotionData*, unsigned int, unsigned int, unsigned int);
        using _ContPotionDataSet8_clbk = void (WINAPIV*)(struct _ContPotionData*, unsigned int, unsigned int, unsigned int, _ContPotionDataSet8_ptr);
        
        using _ContPotionDatactor__ContPotionData10_ptr = void (WINAPIV*)(struct _ContPotionData*);
        using _ContPotionDatactor__ContPotionData10_clbk = void (WINAPIV*)(struct _ContPotionData*, _ContPotionDatactor__ContPotionData10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

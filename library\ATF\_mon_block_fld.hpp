// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _mon_block_fld : _base_fld
    {
        struct _dummy_info
        {
            char m_strDummyCode[64];
            unsigned int m_dwSelectProp;
        };
        unsigned int m_dwDummyNum;
        _dummy_info m_DummyInfo[20];
        int m_nMin;
        int m_nMob;
        int m_nMax;
    };
END_ATF_NAMESPACE

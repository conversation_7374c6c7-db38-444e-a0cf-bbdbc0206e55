// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderGroupIDInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderGroupIDInfoctor_CUnmannedTraderGroupIDInfo2_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*);
        using CUnmannedTraderGroupIDInfoctor_CUnmannedTraderGroupIDInfo2_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, CUnmannedTraderGroupIDInfoctor_CUnmannedTraderGroupIDInfo2_ptr);
        using CUnmannedTraderGroupIDInfoCleanUp4_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*);
        using CUnmannedTraderGroupIDInfoCleanUp4_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, CUnmannedTraderGroupIDInfoCleanUp4_ptr);
        using CUnmannedTraderGroupIDInfoGetGroupID6_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char, uint16_t, char*, char*);
        using CUnmannedTraderGroupIDInfoGetGroupID6_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char, uint16_t, char*, char*, CUnmannedTraderGroupIDInfoGetGroupID6_ptr);
        using CUnmannedTraderGroupIDInfoGetGroupID8_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char, uint16_t, char*, char*, char*, unsigned int*);
        using CUnmannedTraderGroupIDInfoGetGroupID8_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char, uint16_t, char*, char*, char*, unsigned int*, CUnmannedTraderGroupIDInfoGetGroupID8_ptr);
        using CUnmannedTraderGroupIDInfoGetIDInfo10_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, std::vector<std::pair<unsigned long,unsigned long>>*);
        using CUnmannedTraderGroupIDInfoGetIDInfo10_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, std::vector<std::pair<unsigned long,unsigned long>>*, CUnmannedTraderGroupIDInfoGetIDInfo10_ptr);
        using CUnmannedTraderGroupIDInfoGetSortType12_ptr = struct CUnmannedTraderSortType* (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char, char);
        using CUnmannedTraderGroupIDInfoGetSortType12_clbk = struct CUnmannedTraderSortType* (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char, char, CUnmannedTraderGroupIDInfoGetSortType12_ptr);
        using CUnmannedTraderGroupIDInfoIsExistGroupID14_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char, char, char, char, unsigned int*);
        using CUnmannedTraderGroupIDInfoIsExistGroupID14_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char, char, char, char, unsigned int*, CUnmannedTraderGroupIDInfoIsExistGroupID14_ptr);
        using CUnmannedTraderGroupIDInfoIsExistID16_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, unsigned int);
        using CUnmannedTraderGroupIDInfoIsExistID16_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, unsigned int, CUnmannedTraderGroupIDInfoIsExistID16_ptr);
        using CUnmannedTraderGroupIDInfoLoadXML18_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char*);
        using CUnmannedTraderGroupIDInfoLoadXML18_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char*, CUnmannedTraderGroupIDInfoLoadXML18_ptr);
        using CUnmannedTraderGroupIDInfoLog20_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char*);
        using CUnmannedTraderGroupIDInfoLog20_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, char*, CUnmannedTraderGroupIDInfoLog20_ptr);
        using CUnmannedTraderGroupIDInfoSetLogger22_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, struct CLogFile*);
        using CUnmannedTraderGroupIDInfoSetLogger22_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, struct CLogFile*, CUnmannedTraderGroupIDInfoSetLogger22_ptr);
        
        using CUnmannedTraderGroupIDInfodtor_CUnmannedTraderGroupIDInfo24_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*);
        using CUnmannedTraderGroupIDInfodtor_CUnmannedTraderGroupIDInfo24_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupIDInfo*, CUnmannedTraderGroupIDInfodtor_CUnmannedTraderGroupIDInfo24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

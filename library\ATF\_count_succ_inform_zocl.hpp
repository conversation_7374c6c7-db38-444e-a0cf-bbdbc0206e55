// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _count_succ_inform_zocl
    {
        char byActEffectCode;
        char byAtterID;
        unsigned int dwAtterSerial;
        char byDamerID;
        unsigned int dwDamerSerial;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $C64E8308CF8E3072E30C389B70824786
    {
      Event_type_disable = 0x0,
      Event_type_wait = 0x1,
      Event_type_start = 0x2,
      Event_type_30_before = 0x3,
      Event_type_5_before = 0x4,
      Event_type_end = 0x5,
      cash_event_type_error = 0x6,
      Event_type_expire = 0x7,
      Event_type_all = 0x8,
    };
END_ATF_NAMESPACE

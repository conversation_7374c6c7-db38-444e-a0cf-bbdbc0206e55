// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct WSAData
    {
        unsigned __int16 wVersion;
        unsigned __int16 wHighVersion;
        unsigned __int16 iMaxSockets;
        unsigned __int16 iMaxUdpDg;
        char *lpVendorInfo;
        char szDescription[257];
        char szSystemStatus[129];
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<WSAData, 408>(), "WSAData");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_golden_box_item_event.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _golden_box_item_eventctor__golden_box_item_event2_ptr = void (WINAPIV*)(struct _golden_box_item_event*);
        using _golden_box_item_eventctor__golden_box_item_event2_clbk = void (WINAPIV*)(struct _golden_box_item_event*, _golden_box_item_eventctor__golden_box_item_event2_ptr);
        
        using _golden_box_item_eventdtor__golden_box_item_event4_ptr = void (WINAPIV*)(struct _golden_box_item_event*);
        using _golden_box_item_eventdtor__golden_box_item_event4_clbk = void (WINAPIV*)(struct _golden_box_item_event*, _golden_box_item_eventdtor__golden_box_item_event4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _darkhole_state_change_zocl
    {
        unsigned __int16 wIndex;
        unsigned int dwSerial;
        bool bHurry;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_darkhole_state_change_zocl, 7>(), "_darkhole_state_change_zocl");
END_ATF_NAMESPACE

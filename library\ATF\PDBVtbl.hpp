// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct PDBVtbl
    {
        unsigned int (WINAPIV *QueryInterfaceVersion)(PDB *_this);
        BYTE gap8[24];
        unsigned int (WINAPIV *QuerySignature)(PDB *_this);
        unsigned int (WINAPIV *QueryAge)(PDB *_this);
        int (WINAPIV *CreateDBI)(PDB *_this, const char *, DBI **);
        int (WINAPIV *OpenDBI)(PDB *_this, const char *, const char *, DBI **);
        int (WINAPIV *OpenTpi)(PDB *_this, const char *, TPI **);
        int (WINAPIV *Commit)(PDB *_this);
        int (WINAPIV *Close)(PDB *_this);
        int (WINAPIV *OpenStream)(PDB *_this, const char *, Stream **);
        int (WINAPIV *GetEnumStreamNameMap)(PDB *_this, Enum **);
        int (WINAPIV *GetRawBytes)(PDB *_this, int (WINAPIV *)(const void *, int));
        unsigned int (WINAPIV *QueryPdbImplementationVersion)(PDB *_this);
        int (WINAPIV *OpenDBIEx)(PDB *_this, const char *, const char *, DBI **, int (WINAPIV *)(_tagSEARCHDEBUGINFO *));
        int (WINAPIV *CopyTo)(PDB *_this, const char *, unsigned int, unsigned int);
        int (WINAPIV *OpenSrc)(PDB *_this, Src **);
        int (WINAPIV *QueryLastErrorExW)(PDB *_this, wchar_t *, unsigned __int64);
        wchar_t *(WINAPIV *QueryPDBNameExW)(PDB *_this, wchar_t *, unsigned __int64);
        int (WINAPIV *QuerySignature2)(PDB *_this, _GUID *);
        int (WINAPIV *CopyToW)(PDB *_this, const wchar_t *, unsigned int, unsigned int);
        int (WINAPIV *fIsSZPDB)(PDB *_this);
        int (WINAPIV *OpenStreamW)(PDB *_this, const wchar_t *, Stream **);
        int (WINAPIV *CopyToW2)(PDB *_this, const wchar_t *, unsigned int, int (WINAPIV *(WINAPIV *)(void *, PCC))(), void *);
        int (WINAPIV *OpenStreamEx)(PDB *_this, const char *, const char *, Stream **);
    };
END_ATF_NAMESPACE

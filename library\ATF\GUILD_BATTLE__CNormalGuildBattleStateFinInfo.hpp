// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateFin.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateFinctor_CNormalGuildBattleStateFin2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateFin*);
            using GUILD_BATTLE__CNormalGuildBattleStateFinctor_CNormalGuildBattleStateFin2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateFin*, GUILD_BATTLE__CNormalGuildBattleStateFinctor_CNormalGuildBattleStateFin2_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateFinctor_Fin4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateFin*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateFinctor_Fin4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateFin*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateFinctor_Fin4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateFinGetTerm6_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateFin*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CNormalGuildBattleStateFinGetTerm6_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateFin*, struct ATL::CTimeSpan*, GUILD_BATTLE__CNormalGuildBattleStateFinGetTerm6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateFindtor_CNormalGuildBattleStateFin8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateFin*);
            using GUILD_BATTLE__CNormalGuildBattleStateFindtor_CNormalGuildBattleStateFin8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateFin*, GUILD_BATTLE__CNormalGuildBattleStateFindtor_CNormalGuildBattleStateFin8_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _guild_honor_list_result_zocl
    {
        struct  __list
        {
            unsigned int dwGuildSerial;
            unsigned int dwEmblemBack;
            unsigned int dwEmblemMark;
            char wszGuildName[17];
            char wszMasterName[17];
            char byTaxRate;
        };
        char by<PERSON>ist<PERSON><PERSON>;
        char byUI;
        __list GuildList[5];
    public:
        _guild_honor_list_result_zocl();
        void ctor__guild_honor_list_result_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_guild_honor_list_result_zocl, 237>(), "_guild_honor_list_result_zocl");
END_ATF_NAMESPACE

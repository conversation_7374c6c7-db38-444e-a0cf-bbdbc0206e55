// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum _D3DFORMAT
    {
      D3DFMT_UNKNOWN = 0x0,
      D3DFMT_R8G8B8 = 0x14,
      D3DFMT_A8R8G8B8 = 0x15,
      D3DFMT_X8R8G8B8 = 0x16,
      D3DFMT_R5G6B5 = 0x17,
      D3DFMT_X1R5G5B5 = 0x18,
      D3DFMT_A1R5G5B5 = 0x19,
      D3DFMT_A4R4G4B4 = 0x1A,
      D3DFMT_R3G3B2 = 0x1B,
      D3DFMT_A8 = 0x1C,
      D3DFMT_A8R3G3B2 = 0x1D,
      D3DFMT_X4R4G4B4 = 0x1E,
      D3DFMT_A2B10G10R10 = 0x1F,
      D3DFMT_A8B8G8R8 = 0x20,
      D3DFMT_X8B8G8R8 = 0x21,
      D3DFMT_G16R16 = 0x22,
      D3DFMT_A2R10G10B10 = 0x23,
      D3DFMT_A16B16G16R16 = 0x24,
      D3DFMT_A8P8 = 0x28,
      D3DFMT_P8 = 0x29,
      D3DFMT_L8 = 0x32,
      D3DFMT_A8L8 = 0x33,
      D3DFMT_A4L4 = 0x34,
      D3DFMT_V8U8 = 0x3C,
      D3DFMT_L6V5U5 = 0x3D,
      D3DFMT_X8L8V8U8 = 0x3E,
      D3DFMT_Q8W8V8U8 = 0x3F,
      D3DFMT_V16U16 = 0x40,
      D3DFMT_A2W10V10U10 = 0x43,
      D3DFMT_UYVY = 0x59565955,
      D3DFMT_R8G8_B8G8 = 0x47424752,
      D3DFMT_YUY2 = 0x32595559,
      D3DFMT_G8R8_G8B8 = 0x42475247,
      D3DFMT_DXT1 = 0x31545844,
      D3DFMT_DXT2 = 0x32545844,
      D3DFMT_DXT3 = 0x33545844,
      D3DFMT_DXT4 = 0x34545844,
      D3DFMT_DXT5 = 0x35545844,
      D3DFMT_D16_LOCKABLE = 0x46,
      D3DFMT_D32 = 0x47,
      D3DFMT_D15S1 = 0x49,
      D3DFMT_D24S8 = 0x4B,
      D3DFMT_D24X8 = 0x4D,
      D3DFMT_D24X4S4 = 0x4F,
      D3DFMT_D16 = 0x50,
      D3DFMT_D32F_LOCKABLE = 0x52,
      D3DFMT_D24FS8 = 0x53,
      D3DFMT_D32_LOCKABLE = 0x54,
      D3DFMT_S8_LOCKABLE = 0x55,
      D3DFMT_L16 = 0x51,
      D3DFMT_VERTEXDATA = 0x64,
      D3DFMT_INDEX16 = 0x65,
      D3DFMT_INDEX32 = 0x66,
      D3DFMT_Q16W16V16U16 = 0x6E,
      D3DFMT_MULTI2_ARGB8 = 0x3154454D,
      D3DFMT_R16F = 0x6F,
      D3DFMT_G16R16F = 0x70,
      D3DFMT_A16B16G16R16F = 0x71,
      D3DFMT_R32F = 0x72,
      D3DFMT_G32R32F = 0x73,
      D3DFMT_A32B32G32R32F = 0x74,
      D3DFMT_CxV8U8 = 0x75,
      D3DFMT_A1 = 0x76,
      D3DFMT_A2B10G10R10_XR_BIAS = 0x77,
      D3DFMT_BINARYBUFFER = 0xC7,
      D3DFMT_FORCEDWORD = 0x7FFFFFFF,
    };
END_ATF_NAMESPACE

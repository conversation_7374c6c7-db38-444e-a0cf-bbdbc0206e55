// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace US
    {
        struct  CNoneCopyAble
        {
        public:
            CNoneCopyAble();
            void ctor_CNoneCopyAble();
            ~CNoneCopyAble();
            void dtor_CNoneCopyAble();
        };
    }; // end namespace US
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_select_guild_master_lastconn.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_select_guild_master_lastconnctor__qry_case_select_guild_master_lastconn2_ptr = void (WINAPIV*)(struct _qry_case_select_guild_master_lastconn*);
        using _qry_case_select_guild_master_lastconnctor__qry_case_select_guild_master_lastconn2_clbk = void (WINAPIV*)(struct _qry_case_select_guild_master_lastconn*, _qry_case_select_guild_master_lastconnctor__qry_case_select_guild_master_lastconn2_ptr);
        using _qry_case_select_guild_master_lastconnsize4_ptr = int (WINAPIV*)(struct _qry_case_select_guild_master_lastconn*);
        using _qry_case_select_guild_master_lastconnsize4_clbk = int (WINAPIV*)(struct _qry_case_select_guild_master_lastconn*, _qry_case_select_guild_master_lastconnsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

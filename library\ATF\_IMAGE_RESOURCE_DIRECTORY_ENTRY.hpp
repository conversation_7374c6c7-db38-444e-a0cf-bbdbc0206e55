// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$5B699EDA6822B107DB210DC177BA346E.hpp>
#include <$FC2658F21072082DBC527F77CB3413E2.hpp>


START_ATF_NAMESPACE
    struct _IMAGE_RESOURCE_DIRECTORY_ENTRY
    {
        $5B699EDA6822B107DB210DC177BA346E ___u0;
        $FC2658F21072082DBC527F77CB3413E2 ___u1;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattle.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            using GUILD_BATTLE__CNormalGuildBattleAddComplete2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, char);
            using GUILD_BATTLE__CNormalGuildBattleAddComplete2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, char, GUILD_BATTLE__CNormalGuildBattleAddComplete2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleAskJoin4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, int, unsigned int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleAskJoin4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, int, unsigned int, unsigned int, GUILD_BATTLE__CNormalGuildBattleAskJoin4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleAskJoin6_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleAskJoin6_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleAskJoin6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_ptr);
            using GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_ptr);
            using GUILD_BATTLE__CNormalGuildBattleClear12_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleClear12_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleClear12_ptr);
            using GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_ptr);
            using GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_ptr);
            using GUILD_BATTLE__CNormalGuildBattleCreateLogger18_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleCreateLogger18_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleCreateLogger18_ptr);
            using GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_ptr);
            using GUILD_BATTLE__CNormalGuildBattleDecideWin22_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleDecideWin22_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleDecideWin22_ptr);
            using GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_ptr);
            using GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGet1P28_ptr = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGet1P28_clbk = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGet1P28_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGet2P30_ptr = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGet2P30_clbk = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGet2P30_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetBlue32_ptr = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGetBlue32_clbk = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGetBlue32_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetField34_ptr = struct GUILD_BATTLE::CNormalGuildBattleField* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGetField34_clbk = struct GUILD_BATTLE::CNormalGuildBattleField* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGetField34_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, uint16_t, unsigned int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, uint16_t, unsigned int, unsigned int, GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetGuild38_ptr = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleGetGuild38_clbk = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, GUILD_BATTLE__CNormalGuildBattleGetGuild38_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetID42_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGetID42_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGetID42_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetInfo44_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct _guild_battle_current_battle_info_result_zocl*);
            using GUILD_BATTLE__CNormalGuildBattleGetInfo44_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct _guild_battle_current_battle_info_result_zocl*, GUILD_BATTLE__CNormalGuildBattleGetInfo44_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetLogger46_ptr = struct GUILD_BATTLE::CNormalGuildBattleLogger* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGetLogger46_clbk = struct GUILD_BATTLE::CNormalGuildBattleLogger* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGetLogger46_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetObjType48_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGetObjType48_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGetObjType48_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGetRed50_ptr = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGetRed50_clbk = struct GUILD_BATTLE::CNormalGuildBattleGuild* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGetRed50_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGoal52_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, int);
            using GUILD_BATTLE__CNormalGuildBattleGoal52_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, int, GUILD_BATTLE__CNormalGuildBattleGoal52_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct _qry_case_guild_battel_result_log*);
            using GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct _qry_case_guild_battel_result_log*, GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct _qry_case_guild_battel_result_log*, struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct _qry_case_guild_battel_result_log*, struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_ptr);
            using GUILD_BATTLE__CNormalGuildBattleInit60_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct CGuild*, struct CGuild*, struct GUILD_BATTLE::CNormalGuildBattleField*, char, struct GUILD_BATTLE::CNormalGuildBattleStateList*);
            using GUILD_BATTLE__CNormalGuildBattleInit60_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct CGuild*, struct CGuild*, struct GUILD_BATTLE::CNormalGuildBattleField*, char, struct GUILD_BATTLE::CNormalGuildBattleStateList*, GUILD_BATTLE__CNormalGuildBattleInit60_ptr);
            using GUILD_BATTLE__CNormalGuildBattleInit62_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, bool, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, char);
            using GUILD_BATTLE__CNormalGuildBattleInit62_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, bool, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, char, GUILD_BATTLE__CNormalGuildBattleInit62_ptr);
            using GUILD_BATTLE__CNormalGuildBattleIsEmpty64_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleIsEmpty64_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleIsEmpty64_ptr);
            using GUILD_BATTLE__CNormalGuildBattleIsInBattle66_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleIsInBattle66_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleIsInBattle66_ptr);
            using GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_ptr);
            using GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_ptr);
            using GUILD_BATTLE__CNormalGuildBattleIsProc72_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleIsProc72_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleIsProc72_ptr);
            using GUILD_BATTLE__CNormalGuildBattleIsReStart74_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleIsReStart74_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, unsigned int, GUILD_BATTLE__CNormalGuildBattleIsReStart74_ptr);
            using GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_ptr);
            using GUILD_BATTLE__CNormalGuildBattleJoin78_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleJoin78_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, unsigned int, GUILD_BATTLE__CNormalGuildBattleJoin78_ptr);
            using GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_ptr);
            using GUILD_BATTLE__CNormalGuildBattleKill82_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleKill82_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, unsigned int, GUILD_BATTLE__CNormalGuildBattleKill82_ptr);
            using GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_ptr);
            using GUILD_BATTLE__CNormalGuildBattleLogIn86_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, int, unsigned int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleLogIn86_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, int, unsigned int, unsigned int, GUILD_BATTLE__CNormalGuildBattleLogIn86_ptr);
            using GUILD_BATTLE__CNormalGuildBattleNetClose88_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleNetClose88_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleNetClose88_ptr);
            using GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_ptr);
            using GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_ptr);
            using GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, char);
            using GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, char, GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_ptr);
            using GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_ptr);
            using GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, unsigned int, GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_ptr);
            using GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, unsigned int, GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_ptr);
            using GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_ptr);
            using GUILD_BATTLE__CNormalGuildBattleProcess104_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleProcess104_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleProcess104_ptr);
            using GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_ptr);
            using GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_ptr);
            using GUILD_BATTLE__CNormalGuildBattleReStart110_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct CPlayer*, unsigned int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleReStart110_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct CPlayer*, unsigned int, unsigned int, GUILD_BATTLE__CNormalGuildBattleReStart110_ptr);
            using GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_ptr);
            using GUILD_BATTLE__CNormalGuildBattleRewardItem114_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleRewardItem114_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleRewardItem114_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSave116_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleSave116_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleSave116_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, bool, char*, struct CPlayer*, int);
            using GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, bool, char*, struct CPlayer*, int, GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSendKillInform122_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleSendKillInform122_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleSendKillInform122_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_ptr);
            using GUILD_BATTLE__CNormalGuildBattleSetReadyState134_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleSetReadyState134_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleSetReadyState134_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStart136_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct CPlayer*, unsigned int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleStart136_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, struct CPlayer*, unsigned int, unsigned int, GUILD_BATTLE__CNormalGuildBattleStart136_ptr);
            using GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, int, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, int, unsigned int, GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaCommandEx.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CLuaCommandExctor_CLuaCommandEx2_ptr = void (WINAPIV*)(struct CLuaCommandEx*);
        using CLuaCommandExctor_CLuaCommandEx2_clbk = void (WINAPIV*)(struct CLuaCommandEx*, CLuaCommandExctor_CLuaCommandEx2_ptr);
        using CLuaCommandExGetScriptName4_ptr = char* (WINAPIV*)(struct CLuaCommandEx*);
        using CLuaCommandExGetScriptName4_clbk = char* (WINAPIV*)(struct CLuaCommandEx*, CLuaCommandExGetScriptName4_ptr);
        using CLuaCommandExSetCmd6_ptr = void (WINAPIV*)(struct CLuaCommandEx*, char, char*, char*);
        using CLuaCommandExSetCmd6_clbk = void (WINAPIV*)(struct CLuaCommandEx*, char, char*, char*, CLuaCommandExSetCmd6_ptr);
        
        using CLuaCommandExdtor_CLuaCommandEx10_ptr = void (WINAPIV*)(struct CLuaCommandEx*);
        using CLuaCommandExdtor_CLuaCommandEx10_clbk = void (WINAPIV*)(struct CLuaCommandEx*, CLuaCommandExdtor_CLuaCommandEx10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

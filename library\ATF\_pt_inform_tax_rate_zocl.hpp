// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pt_inform_tax_rate_zocl
    {
        char byCurrTax;
        char byNextTax;
    public:
        _pt_inform_tax_rate_zocl();
        void ctor__pt_inform_tax_rate_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_pt_inform_tax_rate_zocl, 2>(), "_pt_inform_tax_rate_zocl");
END_ATF_NAMESPACE

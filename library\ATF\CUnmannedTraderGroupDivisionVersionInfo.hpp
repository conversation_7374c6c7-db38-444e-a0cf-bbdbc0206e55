// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CUnmannedTraderGroupDivisionVersionInfo
    {
        int m_iType;
        std::vector<unsigned long> m_vecuiVersion;
    public:
        CUnmannedTraderGroupDivisionVersionInfo(struct CUnmannedTraderGroupDivisionVersionInfo* lhs);
        void ctor_CUnmannedTraderGroupDivisionVersionInfo(struct CUnmannedTraderGroupDivisionVersionInfo* lhs);
        CUnmannedTraderGroupDivisionVersionInfo(int iType, unsigned int uiMaxCnt);
        void ctor_CUnmannedTraderGroupDivisionVersionInfo(int iType, unsigned int uiMaxCnt);
        bool GetVersion(char byClass, unsigned int* dwVer);
        bool IncreaseVersion(char byClass);
        bool IsEmpty();
        ~CUnmannedTraderGroupDivisionVersionInfo();
        void dtor_CUnmannedTraderGroupDivisionVersionInfo();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CUnmannedTraderGroupDivisionVersionInfo, 48>(), "CUnmannedTraderGroupDivisionVersionInfo");
END_ATF_NAMESPACE

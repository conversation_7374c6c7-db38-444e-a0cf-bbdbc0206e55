// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_pt_inform_punishment_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _pt_inform_punishment_zoclsize2_ptr = int (WINAPIV*)(struct _pt_inform_punishment_zocl*);
        using _pt_inform_punishment_zoclsize2_clbk = int (WINAPIV*)(struct _pt_inform_punishment_zocl*, _pt_inform_punishment_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

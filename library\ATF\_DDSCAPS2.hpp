// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$83FB4F737B5EDC31066F75BACF4F71E0.hpp>


START_ATF_NAMESPACE
    struct _DDSCAPS2
    {
        unsigned int dwCaps;
        unsigned int dwCaps2;
        unsigned int dwCaps3;
        $83FB4F737B5EDC31066F75BACF4F71E0 ___u3;
    };    
    static_assert(ATF::checkSize<_DDSCAPS2, 16>(), "_DDSCAPS2");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RECTL.hpp>
#include <tagEMR.hpp>
#include <tagSIZE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct tagEMRFRAMERGN
    {
        tagEMR emr;
        _RECTL rclBounds;
        unsigned int cbRgnData;
        unsigned int ihBrush;
        tagSIZE szlStroke;
        char RgnData[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

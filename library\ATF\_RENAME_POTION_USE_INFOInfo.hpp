// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RENAME_POTION_USE_INFO.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _RENAME_POTION_USE_INFOInit2_ptr = void (WINAPIV*)(struct _RENAME_POTION_USE_INFO*);
        using _RENAME_POTION_USE_INFOInit2_clbk = void (WINAPIV*)(struct _RENAME_POTION_USE_INFO*, _RENAME_POTION_USE_INFOInit2_ptr);
        
        using _RENAME_POTION_USE_INFOctor__RENAME_POTION_USE_INFO4_ptr = void (WINAPIV*)(struct _RENAME_POTION_USE_INFO*);
        using _RENAME_POTION_USE_INFOctor__RENAME_POTION_USE_INFO4_clbk = void (WINAPIV*)(struct _RENAME_POTION_USE_INFO*, _RENAME_POTION_USE_INFOctor__RENAME_POTION_USE_INFO4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

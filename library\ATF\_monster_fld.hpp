// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _monster_fld : _base_fld
    {
        struct __child_mon
        {
            char strChildMon[64];
            int nChildMonNum;
        };
        struct _EmotionPresentation
        {
            int m_nEmotionCon;
            int m_nEmotionClass;
            char m_strEmotionCode[64];
        };
        char m_strName[64];
        char m_strEffectCode[64];
        int m_nMobGrade;
        int m_nRaceCode;
        int m_nMobRace;
        int m_nKillPoint;
        int m_nToCombatTime;
        int m_nPincerCnt;
        int m_nPreAttRange;
        int m_nMinMoveDistance;
        int m_nMaxMoveDistance;
        int m_nMobAlienation;
        int m_nMinMoveArea;
        int m_nMaxMoveArea;
        int m_nGuardRecallTimeMS;
        int m_nGuardingArea;
        float m_fTarDecType;
        int m_nAPTime;
        int m_nAPReset;
        int m_nUglierType;
        float m_fLevel;
        int m_bMonsterCondition;
        int m_nCriticalTol;
        int m_bExpDown;
        int m_nUpLooting;
        int m_nDnLooting;
        float m_fExt;
        float m_fAttFcStd;
        int m_nAttack_DP;
        int m_nProperty;
        float m_fAttGap;
        int m_bAttRangeType;
        int m_nAttType;
        float m_fMinAFSelProb;
        float m_fMaxAFSelProb;
        float m_fAttSklUnit;
        float m_fDefSklUnit;
        float m_fWeakPart;
        int m_bUseDefence;
        float m_fStdDefFc;
        float m_fDefGap;
        float m_fDefFacing;
        int m_nShieldBlock;
        int m_nBlockPer;
        float m_fFireTol;
        float m_fWaterTol;
        float m_fSoilTol;
        float m_fWindTol;
        char m_strSPCode[15][64];
        float m_fForceLevel;
        float m_fForceMastery;
        float m_fForceAttStd;
        char m_strAttTechID1[64];
        float m_fAttTech1UseProb;
        float m_fAttTechID1MotionTime;
        char m_strAttTechID2[64];
        float m_fAttTech2UseProb;
        float m_fAttTechID2MotionTime;
        char m_strAttTechID3[64];
        float m_fAttTech3UseProb;
        float m_fAttTechID3MotionTime;
        char m_strPSecTechID[64];
        float m_fPSecTechIDMotionTime;
        char m_strMSecTechID[64];
        float m_fMSecTechIDMotionTime;
        int m_nInjuryLimit;
        float m_fMaxHP;
        float m_fHPRecDelay;
        float m_fHPRecUnit;
        float m_fAttSpd;
        float m_fAttMoTime1;
        float m_fAttMoTime2;
        float m_fCrtMoTime;
        int m_nViewAngle;
        int m_nViewAngleCap;
        int m_nCapacityValue;
        float m_fViewExt;
        float m_fAttExt;
        float m_fMRefExt;
        float m_fCopTime;
        float m_fMovSpd;
        float m_fWarMovSpd;
        float m_fScaleRate;
        int m_bScaleChange;
        float m_fWidth;
        float m_fWaitTime;
        int m_nAsitReqRate;
        int m_nAsitAptRate;
        int m_nAsitType;
        __child_mon m_Child[3];
        float m_fEmoType;
        float m_fOffensiveRate;
        int m_nOffensiveType;
        float m_fDamHPStd;
        float m_fEmoImpStdTime;
        float m_fGoodToOrdHPPer;
        float m_fOrdToBadHPPer;
        float m_fBadToWorseHPPer;
        float m_fEspTFProb;
        float m_fTypeCompTerms;
        float m_fPSecTechChat;
        float m_fPAttTechChat;
        float m_fEmo0Chat;
        float m_fEmo0ChatProb;
        float m_fEmo1Chat;
        float m_fEmo1ChatProb;
        float m_fEmo2Chat;
        float m_fEmo2ChatProb;
        float m_fEmo3Chat;
        float m_fEmo3ChatProb;
        float m_fEmo4Chat;
        float m_fEmo4ChatProb;
        float m_fAsitReqSteEspChat;
        float m_fAsitReqSteEspChatProb;
        float m_fAsitReqSteHelpChat;
        float m_fAsitReqSteHelpChatProb;
        float m_fAsitReqSteCopChat;
        float m_fAsitReqSteCopChatProb;
        _EmotionPresentation m_EmotionChecker[5];
        int m_nAttEffType;
        int m_nDefEffType;
    };
END_ATF_NAMESPACE

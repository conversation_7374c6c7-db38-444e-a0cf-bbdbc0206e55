// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaLooting_Novus_Item.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CLuaLooting_Novus_Itemctor_CLuaLooting_Novus_Item2_ptr = void (WINAPIV*)(struct CLuaLooting_Novus_Item*);
        using CLuaLooting_Novus_Itemctor_CLuaLooting_Novus_Item2_clbk = void (WINAPIV*)(struct CLuaLooting_Novus_Item*, CLuaLooting_Novus_Itemctor_CLuaLooting_Novus_Item2_ptr);
        
        using CLuaLooting_Novus_Itemdtor_CLuaLooting_Novus_Item6_ptr = void (WINAPIV*)(struct CLuaLooting_Novus_Item*);
        using CLuaLooting_Novus_Itemdtor_CLuaLooting_Novus_Item6_clbk = void (WINAPIV*)(struct CLuaLooting_Novus_Item*, CLuaLooting_Novus_Itemdtor_CLuaLooting_Novus_Item6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

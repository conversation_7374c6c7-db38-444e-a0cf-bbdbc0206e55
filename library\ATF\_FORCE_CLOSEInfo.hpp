// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FORCE_CLOSE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _FORCE_CLOSEInit2_ptr = bool (WINAPIV*)(struct _FORCE_CLOSE*, unsigned int);
        using _FORCE_CLOSEInit2_clbk = bool (WINAPIV*)(struct _FORCE_CLOSE*, unsigned int, _FORCE_CLOSEInit2_ptr);
        using _FORCE_CLOSEPushNode4_ptr = bool (WINAPIV*)(struct _FORCE_CLOSE*, unsigned int, unsigned int);
        using _FORCE_CLOSEPushNode4_clbk = bool (WINAPIV*)(struct _FORCE_CLOSE*, unsigned int, unsigned int, _FORCE_CLOSEPushNode4_ptr);
        
        using _FORCE_CLOSEctor__FORCE_CLOSE6_ptr = void (WINAPIV*)(struct _FORCE_CLOSE*);
        using _FORCE_CLOSEctor__FORCE_CLOSE6_clbk = void (WINAPIV*)(struct _FORCE_CLOSE*, _FORCE_CLOSEctor__FORCE_CLOSE6_ptr);
        
        using _FORCE_CLOSEdtor__FORCE_CLOSE8_ptr = void (WINAPIV*)(struct _FORCE_CLOSE*);
        using _FORCE_CLOSEdtor__FORCE_CLOSE8_clbk = void (WINAPIV*)(struct _FORCE_CLOSE*, _FORCE_CLOSEdtor__FORCE_CLOSE8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

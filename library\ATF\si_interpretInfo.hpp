// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <si_interpret.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using si_interpretGetCountOfEffect2_ptr = char (WINAPIV*)(struct si_interpret*, int);
        using si_interpretGetCountOfEffect2_clbk = char (WINAPIV*)(struct si_interpret*, int, si_interpretGetCountOfEffect2_ptr);
        using si_interpretGetCountOfItem4_ptr = char (WINAPIV*)(struct si_interpret*, int);
        using si_interpretGetCountOfItem4_clbk = char (WINAPIV*)(struct si_interpret*, int, si_interpretGetCountOfItem4_ptr);
        using si_interpretGetEffectCode6_ptr = int (WINAPIV*)(struct si_interpret*, int);
        using si_interpretGetEffectCode6_clbk = int (WINAPIV*)(struct si_interpret*, int, si_interpretGetEffectCode6_ptr);
        using si_interpretGetEffectTypeCount8_ptr = char (WINAPIV*)(struct si_interpret*);
        using si_interpretGetEffectTypeCount8_clbk = char (WINAPIV*)(struct si_interpret*, si_interpretGetEffectTypeCount8_ptr);
        using si_interpretGetEffectValue10_ptr = float (WINAPIV*)(struct si_interpret*, int);
        using si_interpretGetEffectValue10_clbk = float (WINAPIV*)(struct si_interpret*, int, si_interpretGetEffectValue10_ptr);
        using si_interpretinit12_ptr = void (WINAPIV*)(struct si_interpret*);
        using si_interpretinit12_clbk = void (WINAPIV*)(struct si_interpret*, si_interpretinit12_ptr);
        using si_interpretset_effect_interpret14_ptr = bool (WINAPIV*)(struct si_interpret*, struct _SetItemEff_fld*);
        using si_interpretset_effect_interpret14_clbk = bool (WINAPIV*)(struct si_interpret*, struct _SetItemEff_fld*, si_interpretset_effect_interpret14_ptr);
        
        using si_interpretctor_si_interpret16_ptr = void (WINAPIV*)(struct si_interpret*);
        using si_interpretctor_si_interpret16_clbk = void (WINAPIV*)(struct si_interpret*, si_interpretctor_si_interpret16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

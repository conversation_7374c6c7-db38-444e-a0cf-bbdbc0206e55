// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayMP3.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPlayMP3IsLoadedMP31_ptr = int64_t (WINAPIV*)(struct CPlayMP3*);
        using CPlayMP3IsLoadedMP31_clbk = int64_t (WINAPIV*)(struct CPlayMP3*, CPlayMP3IsLoadedMP31_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

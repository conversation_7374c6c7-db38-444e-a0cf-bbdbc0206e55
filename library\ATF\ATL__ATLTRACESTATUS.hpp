// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        enum ATLTRACESTATUS
        {
          ATLTRACESTATUS_INHERIT = 0x0,
          ATLTRACESTATUS_ENABLED = 0x1,
          ATLTRACESTATUS_DISABLED = 0x2,
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

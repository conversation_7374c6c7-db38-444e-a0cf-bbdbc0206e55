// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $FDD8F4F487B302A1323F7A672D081758
    {
      none = 0x0,
      genericException = 0x1,
      readOnly = 0x2,
      endOfFile = 0x3,
      writeOnly = 0x4,
      badIndex = 0x5,
      badClass = 0x6,
      badSchema = 0x7,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_forceleave
    {
        unsigned int in_leaverserial;
        unsigned int in_guildIndex;
        unsigned int in_guildserial;
        char in_apprnum;
        int in_seniornum;
        int in_MemberNum;
        bool in_bPunish;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_forceleave, 28>(), "_qry_case_forceleave");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_cash_discount_ini_.hpp>
#include <_cash_event_ini.hpp>
#include <_cash_lim_sale.hpp>
#include <_param_cash_select.hpp>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    struct  ICsSendInterface
    {
    public:
        static void SendMsg_BuyCashItem(uint16_t wSock, struct _param_cash_update* psheet, struct _param_cash_update* sheetplus);
        static void SendMsg_CashDiscountEventInform(uint16_t wSock, char byEventType, struct _cash_discount_ini_* pIni);
        static void SendMsg_CashEventInform(uint16_t wSock, char byEventType, char byStatus, struct _cash_event_ini* pIni, struct _cash_lim_sale* pLim);
        static void SendMsg_ConditionalEventInform(uint16_t wSock, char byEventType, uint16_t wCsDiscount, char byStatus, char* pEMsg);
        static void SendMsg_Error(uint16_t wSock, int eCode);
        static void SendMsg_GoodsList(uint16_t wSock, struct _param_cash_select* psheet);
        static void SendMsg_LimitedsaleEventInform(uint16_t wSock, char byTableCode, unsigned int dwIndex, uint16_t wNum);
    };
END_ATF_NAMESPACE

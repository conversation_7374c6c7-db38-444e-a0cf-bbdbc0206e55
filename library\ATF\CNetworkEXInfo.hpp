// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetworkEX.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CNetworkEXAMP_DownloadRequest2_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAMP_DownloadRequest2_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAMP_DownloadRequest2_ptr);
        using CNetworkEXATradeAdjustPriceRequest4_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXATradeAdjustPriceRequest4_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXATradeAdjustPriceRequest4_ptr);
        using CNetworkEXATradeBuyItemRequest6_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXATradeBuyItemRequest6_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXATradeBuyItemRequest6_ptr);
        using CNetworkEXATradeClearItemRequest8_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXATradeClearItemRequest8_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXATradeClearItemRequest8_ptr);
        using CNetworkEXATradeReRegistRequest10_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXATradeReRegistRequest10_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXATradeReRegistRequest10_ptr);
        using CNetworkEXATradeRegItemRequest12_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXATradeRegItemRequest12_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXATradeRegItemRequest12_ptr);
        using CNetworkEXATradeRegedListRequest14_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXATradeRegedListRequest14_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXATradeRegedListRequest14_ptr);
        using CNetworkEXATradeTaxRateRequest16_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXATradeTaxRateRequest16_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXATradeTaxRateRequest16_ptr);
        using CNetworkEXAcceptClientCheck18_ptr = void (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, unsigned int);
        using CNetworkEXAcceptClientCheck18_clbk = void (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, unsigned int, CNetworkEXAcceptClientCheck18_ptr);
        using CNetworkEXAccountLineAnalysis20_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*);
        using CNetworkEXAccountLineAnalysis20_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*, CNetworkEXAccountLineAnalysis20_ptr);
        using CNetworkEXAddBagRequest22_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAddBagRequest22_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAddBagRequest22_ptr);
        using CNetworkEXAddCharRequest24_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAddCharRequest24_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAddCharRequest24_ptr);
        using CNetworkEXAliveCharRequest26_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAliveCharRequest26_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAliveCharRequest26_ptr);
        using CNetworkEXAlterItemSlotRequest28_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAlterItemSlotRequest28_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAlterItemSlotRequest28_ptr);
        using CNetworkEXAlterLinkBoardSlotRequest30_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAlterLinkBoardSlotRequest30_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAlterLinkBoardSlotRequest30_ptr);
        using CNetworkEXAlterPartyLootShareRequest32_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAlterPartyLootShareRequest32_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAlterPartyLootShareRequest32_ptr);
        using CNetworkEXAlterWindowInfoRequest34_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAlterWindowInfoRequest34_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAlterWindowInfoRequest34_ptr);
        using CNetworkEXAnimusCommandRequest36_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAnimusCommandRequest36_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAnimusCommandRequest36_ptr);
        using CNetworkEXAnimusInvenChangeRequest38_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAnimusInvenChangeRequest38_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAnimusInvenChangeRequest38_ptr);
        using CNetworkEXAnimusRecallRequest40_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAnimusRecallRequest40_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAnimusRecallRequest40_ptr);
        using CNetworkEXAnimusReturnRequest42_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAnimusReturnRequest42_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAnimusReturnRequest42_ptr);
        using CNetworkEXAnimusTargetRequest44_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAnimusTargetRequest44_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAnimusTargetRequest44_ptr);
        using CNetworkEXAnsyncConnectComplete46_ptr = void (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, int);
        using CNetworkEXAnsyncConnectComplete46_clbk = void (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, int, CNetworkEXAnsyncConnectComplete46_ptr);
        using CNetworkEXApex_R48_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, uint16_t, char*);
        using CNetworkEXApex_R48_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, uint16_t, char*, CNetworkEXApex_R48_ptr);
        using CNetworkEXApex_T50_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, uint16_t, char*);
        using CNetworkEXApex_T50_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, uint16_t, char*, CNetworkEXApex_T50_ptr);
        using CNetworkEXAttackForceRequest52_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAttackForceRequest52_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAttackForceRequest52_ptr);
        using CNetworkEXAttackPersonalRequest54_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAttackPersonalRequest54_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAttackPersonalRequest54_ptr);
        using CNetworkEXAttackSiegeRequest56_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAttackSiegeRequest56_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAttackSiegeRequest56_ptr);
        using CNetworkEXAttackSkillRequest58_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAttackSkillRequest58_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAttackSkillRequest58_ptr);
        using CNetworkEXAttackTestRequest60_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAttackTestRequest60_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAttackTestRequest60_ptr);
        using CNetworkEXAttackUnitRequest62_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAttackUnitRequest62_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAttackUnitRequest62_ptr);
        using CNetworkEXAwayPartyInvitation64_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAwayPartyInvitation64_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAwayPartyInvitation64_ptr);
        using CNetworkEXAwayPartyInvitationAnswer66_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXAwayPartyInvitationAnswer66_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXAwayPartyInvitationAnswer66_ptr);
        using CNetworkEXBackTowerRequest68_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBackTowerRequest68_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBackTowerRequest68_ptr);
        using CNetworkEXBackTrapRequest70_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBackTrapRequest70_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBackTrapRequest70_ptr);
        using CNetworkEXBaseDownloadRequest72_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBaseDownloadRequest72_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBaseDownloadRequest72_ptr);
        using CNetworkEXBillingChangeType74_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBillingChangeType74_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBillingChangeType74_ptr);
        using CNetworkEXBillingCloseRequest76_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBillingCloseRequest76_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBillingCloseRequest76_ptr);
        using CNetworkEXBillingDestroyModule78_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBillingDestroyModule78_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBillingDestroyModule78_ptr);
        using CNetworkEXBillingExpireIPOverflow80_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBillingExpireIPOverflow80_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBillingExpireIPOverflow80_ptr);
        using CNetworkEXBillingExpirePCBang82_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBillingExpirePCBang82_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBillingExpirePCBang82_ptr);
        using CNetworkEXBillingExpirePersonal84_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBillingExpirePersonal84_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBillingExpirePersonal84_ptr);
        using CNetworkEXBillingInfoRequest86_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBillingInfoRequest86_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBillingInfoRequest86_ptr);
        using CNetworkEXBillingLineAnalysis88_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*);
        using CNetworkEXBillingLineAnalysis88_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*, CNetworkEXBillingLineAnalysis88_ptr);
        using CNetworkEXBillingRemaintimePCBang90_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBillingRemaintimePCBang90_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBillingRemaintimePCBang90_ptr);
        using CNetworkEXBillingRemaintimePersonal92_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBillingRemaintimePersonal92_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBillingRemaintimePersonal92_ptr);
        using CNetworkEXBossSMSMsgRequest94_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBossSMSMsgRequest94_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBossSMSMsgRequest94_ptr);
        using CNetworkEXBriefPassReport96_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBriefPassReport96_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBriefPassReport96_ptr);
        using CNetworkEXBuddyAddAnswer98_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBuddyAddAnswer98_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBuddyAddAnswer98_ptr);
        using CNetworkEXBuddyAddRequest100_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBuddyAddRequest100_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBuddyAddRequest100_ptr);
        using CNetworkEXBuddyDelRequest102_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBuddyDelRequest102_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBuddyDelRequest102_ptr);
        using CNetworkEXBuddyDownloadRequest104_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBuddyDownloadRequest104_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBuddyDownloadRequest104_ptr);
        using CNetworkEXBuyStoreRequest106_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXBuyStoreRequest106_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXBuyStoreRequest106_ptr);
        
        using CNetworkEXctor_CNetworkEX108_ptr = void (WINAPIV*)(struct CNetworkEX*);
        using CNetworkEXctor_CNetworkEX108_clbk = void (WINAPIV*)(struct CNetworkEX*, CNetworkEXctor_CNetworkEX108_ptr);
        using CNetworkEXCanSelectClassRequest110_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCanSelectClassRequest110_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCanSelectClassRequest110_ptr);
        using CNetworkEXCancelRaceBossSMSMsg112_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCancelRaceBossSMSMsg112_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCancelRaceBossSMSMsg112_ptr);
        using CNetworkEXCashDBInfoRecvResult114_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCashDBInfoRecvResult114_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCashDBInfoRecvResult114_ptr);
        using CNetworkEXCastVoteRequest116_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCastVoteRequest116_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCastVoteRequest116_ptr);
        using CNetworkEXCharacterRenameCash118_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCharacterRenameCash118_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCharacterRenameCash118_ptr);
        using CNetworkEXChatAllRecvYesOrNo120_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatAllRecvYesOrNo120_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatAllRecvYesOrNo120_ptr);
        using CNetworkEXChatAllRequest122_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatAllRequest122_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatAllRequest122_ptr);
        using CNetworkEXChatCheatRequest124_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatCheatRequest124_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatCheatRequest124_ptr);
        using CNetworkEXChatCircleRequest126_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatCircleRequest126_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatCircleRequest126_ptr);
        using CNetworkEXChatFarRequest128_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatFarRequest128_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatFarRequest128_ptr);
        using CNetworkEXChatGmNoticeRequest130_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatGmNoticeRequest130_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatGmNoticeRequest130_ptr);
        using CNetworkEXChatGreetingMsg_GM132_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatGreetingMsg_GM132_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatGreetingMsg_GM132_ptr);
        using CNetworkEXChatGreetingMsg_GUILD134_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatGreetingMsg_GUILD134_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatGreetingMsg_GUILD134_ptr);
        using CNetworkEXChatGreetingMsg_RACE136_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatGreetingMsg_RACE136_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatGreetingMsg_RACE136_ptr);
        using CNetworkEXChatGuildEstSenRequest138_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatGuildEstSenRequest138_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatGuildEstSenRequest138_ptr);
        using CNetworkEXChatGuildRequest140_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatGuildRequest140_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatGuildRequest140_ptr);
        using CNetworkEXChatLockCommand142_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXChatLockCommand142_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXChatLockCommand142_ptr);
        using CNetworkEXChatManageRequest144_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatManageRequest144_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatManageRequest144_ptr);
        using CNetworkEXChatMapRecvYesOrNo146_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatMapRecvYesOrNo146_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatMapRecvYesOrNo146_ptr);
        using CNetworkEXChatMapRequest148_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatMapRequest148_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatMapRequest148_ptr);
        using CNetworkEXChatMgrWhisperRequest150_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatMgrWhisperRequest150_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatMgrWhisperRequest150_ptr);
        using CNetworkEXChatMultiFarRequest152_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatMultiFarRequest152_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatMultiFarRequest152_ptr);
        using CNetworkEXChatOperatorRequest154_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatOperatorRequest154_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatOperatorRequest154_ptr);
        using CNetworkEXChatPartyRequest156_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatPartyRequest156_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatPartyRequest156_ptr);
        using CNetworkEXChatRaceBossCryRequest158_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatRaceBossCryRequest158_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatRaceBossCryRequest158_ptr);
        using CNetworkEXChatRaceBossRequest160_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatRaceBossRequest160_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatRaceBossRequest160_ptr);
        using CNetworkEXChatRaceRequest162_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatRaceRequest162_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatRaceRequest162_ptr);
        using CNetworkEXChatRePresentationRequest164_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatRePresentationRequest164_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatRePresentationRequest164_ptr);
        using CNetworkEXChatTradeRequestMsg166_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChatTradeRequestMsg166_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChatTradeRequestMsg166_ptr);
        using CNetworkEXCheckIsBlockIPResult168_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXCheckIsBlockIPResult168_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXCheckIsBlockIPResult168_ptr);
        using CNetworkEXChinaBillingChangePrimium170_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXChinaBillingChangePrimium170_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXChinaBillingChangePrimium170_ptr);
        using CNetworkEXClassSkillRecallTeleportRequest172_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXClassSkillRecallTeleportRequest172_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXClassSkillRecallTeleportRequest172_ptr);
        using CNetworkEXClassSkillRequest174_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXClassSkillRequest174_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXClassSkillRequest174_ptr);
        using CNetworkEXClientLineAnalysis176_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*);
        using CNetworkEXClientLineAnalysis176_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*, CNetworkEXClientLineAnalysis176_ptr);
        using CNetworkEXClose178_ptr = void (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, bool, char*);
        using CNetworkEXClose178_clbk = void (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, bool, char*, CNetworkEXClose178_ptr);
        using CNetworkEXCloseClientCheck180_ptr = void (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, unsigned int);
        using CNetworkEXCloseClientCheck180_clbk = void (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, unsigned int, CNetworkEXCloseClientCheck180_ptr);
        using CNetworkEXCombineExItemAcceptRequest182_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCombineExItemAcceptRequest182_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCombineExItemAcceptRequest182_ptr);
        using CNetworkEXCombineExItemRequest184_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCombineExItemRequest184_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCombineExItemRequest184_ptr);
        using CNetworkEXCombineItemRequest186_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCombineItemRequest186_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCombineItemRequest186_ptr);
        using CNetworkEXConEventTotalSalesCheck188_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXConEventTotalSalesCheck188_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXConEventTotalSalesCheck188_ptr);
        using CNetworkEXConnectionStatusRequest190_ptr = bool (WINAPIV*)(struct CNetworkEX*, int);
        using CNetworkEXConnectionStatusRequest190_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, CNetworkEXConnectionStatusRequest190_ptr);
        using CNetworkEXCumDownloadRequest192_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCumDownloadRequest192_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCumDownloadRequest192_ptr);
        using CNetworkEXCuttingCompleteRequest194_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXCuttingCompleteRequest194_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXCuttingCompleteRequest194_ptr);
        using CNetworkEXDTradeAddRequest196_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDTradeAddRequest196_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDTradeAddRequest196_ptr);
        using CNetworkEXDTradeAnswerRequest198_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDTradeAnswerRequest198_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDTradeAnswerRequest198_ptr);
        using CNetworkEXDTradeAskRequest200_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDTradeAskRequest200_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDTradeAskRequest200_ptr);
        using CNetworkEXDTradeBetRequest202_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDTradeBetRequest202_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDTradeBetRequest202_ptr);
        using CNetworkEXDTradeCancleRequest204_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDTradeCancleRequest204_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDTradeCancleRequest204_ptr);
        using CNetworkEXDTradeDelRequest206_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDTradeDelRequest206_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDTradeDelRequest206_ptr);
        using CNetworkEXDTradeLockRequest208_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDTradeLockRequest208_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDTradeLockRequest208_ptr);
        using CNetworkEXDTradeOKRequest210_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDTradeOKRequest210_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDTradeOKRequest210_ptr);
        using CNetworkEXDarkHoleAnswerReenterRequest212_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDarkHoleAnswerReenterRequest212_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDarkHoleAnswerReenterRequest212_ptr);
        using CNetworkEXDarkHoleClearOutRequest214_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDarkHoleClearOutRequest214_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDarkHoleClearOutRequest214_ptr);
        using CNetworkEXDarkHoleEnterRequest216_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDarkHoleEnterRequest216_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDarkHoleEnterRequest216_ptr);
        using CNetworkEXDarkHoleGiveupOutRequest218_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDarkHoleGiveupOutRequest218_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDarkHoleGiveupOutRequest218_ptr);
        using CNetworkEXDarkHoleOpenRequest220_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDarkHoleOpenRequest220_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDarkHoleOpenRequest220_ptr);
        using CNetworkEXDataAnalysis222_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, struct _MSG_HEADER*, char*);
        using CNetworkEXDataAnalysis222_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, struct _MSG_HEADER*, char*, CNetworkEXDataAnalysis222_ptr);
        using CNetworkEXDecideRecallRequest224_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDecideRecallRequest224_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDecideRecallRequest224_ptr);
        using CNetworkEXDelCharRequest226_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDelCharRequest226_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDelCharRequest226_ptr);
        using CNetworkEXDisconnectGuildWarCharacterRequest228_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDisconnectGuildWarCharacterRequest228_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDisconnectGuildWarCharacterRequest228_ptr);
        using CNetworkEXDownGradeItemRequest230_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXDownGradeItemRequest230_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXDownGradeItemRequest230_ptr);
        using CNetworkEXEmbellishRequest232_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXEmbellishRequest232_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXEmbellishRequest232_ptr);
        using CNetworkEXEnterReturnGateRequest234_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXEnterReturnGateRequest234_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXEnterReturnGateRequest234_ptr);
        using CNetworkEXEnterWorldRequest236_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*);
        using CNetworkEXEnterWorldRequest236_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*, CNetworkEXEnterWorldRequest236_ptr);
        using CNetworkEXEnterWorldResult238_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXEnterWorldResult238_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXEnterWorldResult238_ptr);
        using CNetworkEXEquipPartRequest240_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXEquipPartRequest240_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXEquipPartRequest240_ptr);
        using CNetworkEXExchangeDalantForGoldRequest242_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXExchangeDalantForGoldRequest242_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXExchangeDalantForGoldRequest242_ptr);
        using CNetworkEXExchangeGoldForDalantRequest244_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXExchangeGoldForDalantRequest244_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXExchangeGoldForDalantRequest244_ptr);
        using CNetworkEXExchangeGoldForPvPRequest246_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXExchangeGoldForPvPRequest246_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXExchangeGoldForPvPRequest246_ptr);
        using CNetworkEXExchangeItemRequest248_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXExchangeItemRequest248_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXExchangeItemRequest248_ptr);
        using CNetworkEXExitWorldRequest250_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXExitWorldRequest250_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXExitWorldRequest250_ptr);
        using CNetworkEXExpulsionSocket252_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, char, void*);
        using CNetworkEXExpulsionSocket252_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, unsigned int, char, void*, CNetworkEXExpulsionSocket252_ptr);
        using CNetworkEXForceCloseCommand254_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXForceCloseCommand254_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXForceCloseCommand254_ptr);
        using CNetworkEXForceDownloadRequest256_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXForceDownloadRequest256_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXForceDownloadRequest256_ptr);
        using CNetworkEXForceInvenChangeRequest258_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXForceInvenChangeRequest258_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXForceInvenChangeRequest258_ptr);
        using CNetworkEXForceRecallTeleportRequest260_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXForceRecallTeleportRequest260_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXForceRecallTeleportRequest260_ptr);
        using CNetworkEXForceRequest262_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXForceRequest262_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXForceRequest262_ptr);
        using CNetworkEXGotoAvatorRequest264_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGotoAvatorRequest264_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGotoAvatorRequest264_ptr);
        using CNetworkEXGotoBasePortalRequest266_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGotoBasePortalRequest266_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGotoBasePortalRequest266_ptr);
        using CNetworkEXGuildBattleBlockReport268_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildBattleBlockReport268_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildBattleBlockReport268_ptr);
        using CNetworkEXGuildBattleCurrentBattleInfoRequest270_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildBattleCurrentBattleInfoRequest270_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildBattleCurrentBattleInfoRequest270_ptr);
        using CNetworkEXGuildBattleGetGravityStoneRequest272_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildBattleGetGravityStoneRequest272_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildBattleGetGravityStoneRequest272_ptr);
        using CNetworkEXGuildBattleGoalRequest274_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildBattleGoalRequest274_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildBattleGoalRequest274_ptr);
        using CNetworkEXGuildBattleJoinGuildBattleRequest276_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildBattleJoinGuildBattleRequest276_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildBattleJoinGuildBattleRequest276_ptr);
        using CNetworkEXGuildBattlePossibleGuildBattleList278_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildBattlePossibleGuildBattleList278_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildBattlePossibleGuildBattleList278_ptr);
        using CNetworkEXGuildBattleRankListRequest280_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildBattleRankListRequest280_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildBattleRankListRequest280_ptr);
        using CNetworkEXGuildBattleReservedScheduleRequest282_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildBattleReservedScheduleRequest282_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildBattleReservedScheduleRequest282_ptr);
        using CNetworkEXGuildBattleTakeGravityStoneRequest284_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildBattleTakeGravityStoneRequest284_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildBattleTakeGravityStoneRequest284_ptr);
        using CNetworkEXGuildCancelSuggestRequest286_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildCancelSuggestRequest286_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildCancelSuggestRequest286_ptr);
        using CNetworkEXGuildDownloadRequest288_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildDownloadRequest288_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildDownloadRequest288_ptr);
        using CNetworkEXGuildEstablishRequest290_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildEstablishRequest290_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildEstablishRequest290_ptr);
        using CNetworkEXGuildHonorListRequest292_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildHonorListRequest292_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildHonorListRequest292_ptr);
        using CNetworkEXGuildJoinAcceptRequest294_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildJoinAcceptRequest294_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildJoinAcceptRequest294_ptr);
        using CNetworkEXGuildJoinApplyCancelRequest296_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildJoinApplyCancelRequest296_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildJoinApplyCancelRequest296_ptr);
        using CNetworkEXGuildJoinApplyRequest298_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildJoinApplyRequest298_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildJoinApplyRequest298_ptr);
        using CNetworkEXGuildListRequest300_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildListRequest300_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildListRequest300_ptr);
        using CNetworkEXGuildManageRequest302_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildManageRequest302_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildManageRequest302_ptr);
        using CNetworkEXGuildNextHonorListRequest304_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildNextHonorListRequest304_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildNextHonorListRequest304_ptr);
        using CNetworkEXGuildOfferSuggestRequest306_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildOfferSuggestRequest306_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildOfferSuggestRequest306_ptr);
        using CNetworkEXGuildPushMoneyRequest308_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildPushMoneyRequest308_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildPushMoneyRequest308_ptr);
        using CNetworkEXGuildQueryInfoRequest310_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildQueryInfoRequest310_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildQueryInfoRequest310_ptr);
        using CNetworkEXGuildRoomEnterRequest312_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildRoomEnterRequest312_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildRoomEnterRequest312_ptr);
        using CNetworkEXGuildRoomOutRequest314_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildRoomOutRequest314_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildRoomOutRequest314_ptr);
        using CNetworkEXGuildRoomRentRequest316_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildRoomRentRequest316_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildRoomRentRequest316_ptr);
        using CNetworkEXGuildRoomRestTimeRequest318_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildRoomRestTimeRequest318_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildRoomRestTimeRequest318_ptr);
        using CNetworkEXGuildSelfLeaveRequest320_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildSelfLeaveRequest320_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildSelfLeaveRequest320_ptr);
        using CNetworkEXGuildSetHonorRequest322_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildSetHonorRequest322_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildSetHonorRequest322_ptr);
        using CNetworkEXGuildVoteRequest324_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGuildVoteRequest324_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGuildVoteRequest324_ptr);
        using CNetworkEXGustureRequest326_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXGustureRequest326_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXGustureRequest326_ptr);
        using CNetworkEXInitClassCostRequest328_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXInitClassCostRequest328_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXInitClassCostRequest328_ptr);
        using CNetworkEXInitClassRequest330_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXInitClassRequest330_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXInitClassRequest330_ptr);
        using CNetworkEXInvenDownloadRequest332_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXInvenDownloadRequest332_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXInvenDownloadRequest332_ptr);
        using CNetworkEXItemboxTakeRequest334_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXItemboxTakeRequest334_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXItemboxTakeRequest334_ptr);
        using CNetworkEXLimitItemNumRequest336_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXLimitItemNumRequest336_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXLimitItemNumRequest336_ptr);
        using CNetworkEXLinkBoardDownloadRequest338_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXLinkBoardDownloadRequest338_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXLinkBoardDownloadRequest338_ptr);
        using CNetworkEXLogInControllServer340_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXLogInControllServer340_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXLogInControllServer340_ptr);
        using CNetworkEXLogInWebAgentServer342_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXLogInWebAgentServer342_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXLogInWebAgentServer342_ptr);
        using CNetworkEXMacroDownLoadRequest344_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMacroDownLoadRequest344_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMacroDownLoadRequest344_ptr);
        using CNetworkEXMakeItemRequest346_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMakeItemRequest346_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMakeItemRequest346_ptr);
        using CNetworkEXMakeTowerRequest348_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMakeTowerRequest348_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMakeTowerRequest348_ptr);
        using CNetworkEXMakeTrapRequest350_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMakeTrapRequest350_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMakeTrapRequest350_ptr);
        using CNetworkEXManageClientForceExitRequest352_ptr = bool (WINAPIV*)(struct CNetworkEX*);
        using CNetworkEXManageClientForceExitRequest352_clbk = bool (WINAPIV*)(struct CNetworkEX*, CNetworkEXManageClientForceExitRequest352_ptr);
        using CNetworkEXManageClientLimitRunRequest354_ptr = bool (WINAPIV*)(struct CNetworkEX*, char*);
        using CNetworkEXManageClientLimitRunRequest354_clbk = bool (WINAPIV*)(struct CNetworkEX*, char*, CNetworkEXManageClientLimitRunRequest354_ptr);
        using CNetworkEXMineCancleRequest356_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMineCancleRequest356_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMineCancleRequest356_ptr);
        using CNetworkEXMineStartRequest358_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMineStartRequest358_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMineStartRequest358_ptr);
        using CNetworkEXModeChangeRequest360_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXModeChangeRequest360_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXModeChangeRequest360_ptr);
        using CNetworkEXMoveInfoRequeset362_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMoveInfoRequeset362_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMoveInfoRequeset362_ptr);
        using CNetworkEXMoveLobbyRequest364_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMoveLobbyRequest364_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMoveLobbyRequest364_ptr);
        using CNetworkEXMovePortalRequest366_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMovePortalRequest366_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMovePortalRequest366_ptr);
        using CNetworkEXMoveToOwnStoneMapRequest368_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMoveToOwnStoneMapRequest368_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMoveToOwnStoneMapRequest368_ptr);
        using CNetworkEXMoveTypeChangeRequeset370_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXMoveTypeChangeRequeset370_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXMoveTypeChangeRequeset370_ptr);
        using CNetworkEXNPCDialogRequest372_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXNPCDialogRequest372_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXNPCDialogRequest372_ptr);
        using CNetworkEXNPCLinkCheckItemRequest374_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXNPCLinkCheckItemRequest374_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXNPCLinkCheckItemRequest374_ptr);
        using CNetworkEXNPCQuestListRequest376_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXNPCQuestListRequest376_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXNPCQuestListRequest376_ptr);
        using CNetworkEXNPCQuestRequest378_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXNPCQuestRequest378_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXNPCQuestRequest378_ptr);
        using CNetworkEXNPCWatchingRequest380_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXNPCWatchingRequest380_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXNPCWatchingRequest380_ptr);
        using CNetworkEXNewPosStartRequest382_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXNewPosStartRequest382_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXNewPosStartRequest382_ptr);
        using CNetworkEXNextPoint384_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXNextPoint384_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXNextPoint384_ptr);
        using CNetworkEXNotifyLocalTimeRequest386_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXNotifyLocalTimeRequest386_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXNotifyLocalTimeRequest386_ptr);
        using CNetworkEXNotifyRaceBossCryMsg388_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXNotifyRaceBossCryMsg388_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXNotifyRaceBossCryMsg388_ptr);
        using CNetworkEXObjectServerPosRequest390_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXObjectServerPosRequest390_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXObjectServerPosRequest390_ptr);
        using CNetworkEXOffPartRequest392_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXOffPartRequest392_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXOffPartRequest392_ptr);
        using CNetworkEXOpenControlInform394_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXOpenControlInform394_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXOpenControlInform394_ptr);
        using CNetworkEXOpenWorldFailureResult396_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXOpenWorldFailureResult396_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXOpenWorldFailureResult396_ptr);
        using CNetworkEXOpenWorldSuccessResult398_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXOpenWorldSuccessResult398_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXOpenWorldSuccessResult398_ptr);
        using CNetworkEXOreIntoBagRequest400_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXOreIntoBagRequest400_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXOreIntoBagRequest400_ptr);
        using CNetworkEXOtherShapeRequest402_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXOtherShapeRequest402_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXOtherShapeRequest402_ptr);
        using CNetworkEXPartyDisjointRequest404_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartyDisjointRequest404_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartyDisjointRequest404_ptr);
        using CNetworkEXPartyJoinApplicatiohAnswer406_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartyJoinApplicatiohAnswer406_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartyJoinApplicatiohAnswer406_ptr);
        using CNetworkEXPartyJoinApplication408_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartyJoinApplication408_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartyJoinApplication408_ptr);
        using CNetworkEXPartyJoinInvitation410_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartyJoinInvitation410_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartyJoinInvitation410_ptr);
        using CNetworkEXPartyJoinInvitationAnswer412_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartyJoinInvitationAnswer412_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartyJoinInvitationAnswer412_ptr);
        using CNetworkEXPartyLeaveCompulsionRequest414_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartyLeaveCompulsionRequest414_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartyLeaveCompulsionRequest414_ptr);
        using CNetworkEXPartyLeaveSelfRequest416_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartyLeaveSelfRequest416_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartyLeaveSelfRequest416_ptr);
        using CNetworkEXPartyLockRequest418_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartyLockRequest418_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartyLockRequest418_ptr);
        using CNetworkEXPartyReqBlockReport420_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartyReqBlockReport420_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartyReqBlockReport420_ptr);
        using CNetworkEXPartySuccessionRequest422_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPartySuccessionRequest422_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPartySuccessionRequest422_ptr);
        using CNetworkEXPcBangPrimiumCouponRequest424_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPcBangPrimiumCouponRequest424_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPcBangPrimiumCouponRequest424_ptr);
        using CNetworkEXPlayerInfoResult426_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPlayerInfoResult426_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPlayerInfoResult426_ptr);
        using CNetworkEXPlayerMacroUpdate428_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPlayerMacroUpdate428_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPlayerMacroUpdate428_ptr);
        using CNetworkEXPostContentRequest430_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPostContentRequest430_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPostContentRequest430_ptr);
        using CNetworkEXPostDeleteRequest432_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPostDeleteRequest432_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPostDeleteRequest432_ptr);
        using CNetworkEXPostItemGoldRequest434_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPostItemGoldRequest434_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPostItemGoldRequest434_ptr);
        using CNetworkEXPostListRequest436_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPostListRequest436_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPostListRequest436_ptr);
        using CNetworkEXPostReturnConfirmRequest438_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPostReturnConfirmRequest438_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPostReturnConfirmRequest438_ptr);
        using CNetworkEXPostSendRequest440_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPostSendRequest440_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPostSendRequest440_ptr);
        using CNetworkEXPotionSocketDivisionRequest442_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPotionSocketDivisionRequest442_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPotionSocketDivisionRequest442_ptr);
        using CNetworkEXPotionSocketSeparationRequest444_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPotionSocketSeparationRequest444_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPotionSocketSeparationRequest444_ptr);
        using CNetworkEXProposeVoteRequest446_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXProposeVoteRequest446_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXProposeVoteRequest446_ptr);
        using CNetworkEXPvpCashRecorverWithTalik448_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPvpCashRecorverWithTalik448_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPvpCashRecorverWithTalik448_ptr);
        using CNetworkEXPvpRankListRequest450_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXPvpRankListRequest450_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXPvpRankListRequest450_ptr);
        using CNetworkEXQuestDownloadRequest452_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXQuestDownloadRequest452_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXQuestDownloadRequest452_ptr);
        using CNetworkEXQuestGiveupRequest454_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXQuestGiveupRequest454_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXQuestGiveupRequest454_ptr);
        using CNetworkEXQuestSelectRewardReport456_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXQuestSelectRewardReport456_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXQuestSelectRewardReport456_ptr);
        using CNetworkEXRadarCharListRequest458_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRadarCharListRequest458_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRadarCharListRequest458_ptr);
        using CNetworkEXRealMovPosRequest460_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRealMovPosRequest460_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRealMovPosRequest460_ptr);
        using CNetworkEXRegedCharRequest462_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRegedCharRequest462_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRegedCharRequest462_ptr);
        using CNetworkEXRegistBindRequest464_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRegistBindRequest464_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRegistBindRequest464_ptr);
        using CNetworkEXReleaseGroupTargetObjectRequest466_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXReleaseGroupTargetObjectRequest466_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXReleaseGroupTargetObjectRequest466_ptr);
        using CNetworkEXReleaseSiegeModeRequest468_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXReleaseSiegeModeRequest468_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXReleaseSiegeModeRequest468_ptr);
        using CNetworkEXReleaseTargetObjectRequest470_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXReleaseTargetObjectRequest470_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXReleaseTargetObjectRequest470_ptr);
        using CNetworkEXRequestChangeTaxRate472_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRequestChangeTaxRate472_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRequestChangeTaxRate472_ptr);
        using CNetworkEXRequestPatriarchPunishment474_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRequestPatriarchPunishment474_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRequestPatriarchPunishment474_ptr);
        using CNetworkEXRequestTLLogoutTime476_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRequestTLLogoutTime476_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRequestTLLogoutTime476_ptr);
        using CNetworkEXRequestTaxRate478_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRequestTaxRate478_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRequestTaxRate478_ptr);
        using CNetworkEXRequestUILockFindPW480_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRequestUILockFindPW480_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRequestUILockFindPW480_ptr);
        using CNetworkEXRequestUILockInit482_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRequestUILockInit482_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRequestUILockInit482_ptr);
        using CNetworkEXRequestUILockUpdateInfo484_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRequestUILockUpdateInfo484_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRequestUILockUpdateInfo484_ptr);
        using CNetworkEXRequestUILockUserCertify486_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRequestUILockUserCertify486_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRequestUILockUserCertify486_ptr);
        using CNetworkEXResCuttingRequest488_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXResCuttingRequest488_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXResCuttingRequest488_ptr);
        using CNetworkEXResDivisionRequest490_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXResDivisionRequest490_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXResDivisionRequest490_ptr);
        using CNetworkEXResSeparationRequest492_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXResSeparationRequest492_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXResSeparationRequest492_ptr);
        using CNetworkEXRevival494_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXRevival494_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXRevival494_ptr);
        using CNetworkEXSelCharRequest496_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSelCharRequest496_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSelCharRequest496_ptr);
        using CNetworkEXSelectClassRequest498_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSelectClassRequest498_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSelectClassRequest498_ptr);
        using CNetworkEXSelectPcBangRewardRequest500_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSelectPcBangRewardRequest500_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSelectPcBangRewardRequest500_ptr);
        using CNetworkEXSelectWaitedQuestReport502_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSelectWaitedQuestReport502_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSelectWaitedQuestReport502_ptr);
        using CNetworkEXSellStoreRequest504_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSellStoreRequest504_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSellStoreRequest504_ptr);
        using CNetworkEXSendRaceBossMsgFromWebRequest506_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSendRaceBossMsgFromWebRequest506_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSendRaceBossMsgFromWebRequest506_ptr);
        using CNetworkEXSetGroupMapPointRequest508_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSetGroupMapPointRequest508_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSetGroupMapPointRequest508_ptr);
        using CNetworkEXSetGroupTargetObjectRequest510_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSetGroupTargetObjectRequest510_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSetGroupTargetObjectRequest510_ptr);
        using CNetworkEXSetItemCheckRequest512_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSetItemCheckRequest512_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSetItemCheckRequest512_ptr);
        using CNetworkEXSetPassablePacket514_ptr = void (WINAPIV*)(struct CNetworkEX*, unsigned int, char, char);
        using CNetworkEXSetPassablePacket514_clbk = void (WINAPIV*)(struct CNetworkEX*, unsigned int, char, char, CNetworkEXSetPassablePacket514_ptr);
        using CNetworkEXSetRaceBossCryMsgRequest516_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSetRaceBossCryMsgRequest516_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSetRaceBossCryMsgRequest516_ptr);
        using CNetworkEXSetTargetObjectRequest518_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSetTargetObjectRequest518_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSetTargetObjectRequest518_ptr);
        using CNetworkEXSkillRecallTeleportRequest520_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSkillRecallTeleportRequest520_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSkillRecallTeleportRequest520_ptr);
        using CNetworkEXSkillRequest522_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSkillRequest522_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSkillRequest522_ptr);
        using CNetworkEXSpecialDownloadRequest524_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXSpecialDownloadRequest524_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXSpecialDownloadRequest524_ptr);
        using CNetworkEXStop526_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXStop526_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXStop526_ptr);
        using CNetworkEXStoreListRequest528_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXStoreListRequest528_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXStoreListRequest528_ptr);
        using CNetworkEXTaiwanBillingUserCertify530_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTaiwanBillingUserCertify530_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTaiwanBillingUserCertify530_ptr);
        using CNetworkEXTalikCrystalExchangeRequest532_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*);
        using CNetworkEXTalikCrystalExchangeRequest532_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*, CNetworkEXTalikCrystalExchangeRequest532_ptr);
        using CNetworkEXTalikRecorverList534_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTalikRecorverList534_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTalikRecorverList534_ptr);
        using CNetworkEXThrowSkillRequest536_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXThrowSkillRequest536_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXThrowSkillRequest536_ptr);
        using CNetworkEXThrowStorageRequest538_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXThrowStorageRequest538_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXThrowStorageRequest538_ptr);
        using CNetworkEXThrowUnitRequest540_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXThrowUnitRequest540_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXThrowUnitRequest540_ptr);
        using CNetworkEXTotalGuildRankRequest542_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTotalGuildRankRequest542_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTotalGuildRankRequest542_ptr);
        using CNetworkEXTradeBlockReport544_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTradeBlockReport544_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTradeBlockReport544_ptr);
        using CNetworkEXTransAccountInform546_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXTransAccountInform546_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXTransAccountInform546_ptr);
        using CNetworkEXTransShipRenewTicketRequest548_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTransShipRenewTicketRequest548_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTransShipRenewTicketRequest548_ptr);
        using CNetworkEXTransformSiegeModeRequest550_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTransformSiegeModeRequest550_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTransformSiegeModeRequest550_ptr);
        using CNetworkEXTrunkAlterItemSlotRequest552_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkAlterItemSlotRequest552_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkAlterItemSlotRequest552_ptr);
        using CNetworkEXTrunkChangePasswdRequest554_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkChangePasswdRequest554_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkChangePasswdRequest554_ptr);
        using CNetworkEXTrunkCreateCostIsFreeRequest556_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkCreateCostIsFreeRequest556_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkCreateCostIsFreeRequest556_ptr);
        using CNetworkEXTrunkDownloadRequest558_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkDownloadRequest558_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkDownloadRequest558_ptr);
        using CNetworkEXTrunkEstRequest560_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkEstRequest560_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkEstRequest560_ptr);
        using CNetworkEXTrunkExtendRequest562_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkExtendRequest562_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkExtendRequest562_ptr);
        using CNetworkEXTrunkHintAnswerRequest564_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkHintAnswerRequest564_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkHintAnswerRequest564_ptr);
        using CNetworkEXTrunkIoMergeRequest566_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkIoMergeRequest566_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkIoMergeRequest566_ptr);
        using CNetworkEXTrunkIoMoneyRequest568_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkIoMoneyRequest568_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkIoMoneyRequest568_ptr);
        using CNetworkEXTrunkIoMoveRequest570_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkIoMoveRequest570_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkIoMoveRequest570_ptr);
        using CNetworkEXTrunkIoSwapRequest572_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkIoSwapRequest572_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkIoSwapRequest572_ptr);
        using CNetworkEXTrunkPotionDivisionRequest574_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkPotionDivisionRequest574_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkPotionDivisionRequest574_ptr);
        using CNetworkEXTrunkPwHintIndexRequest576_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkPwHintIndexRequest576_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkPwHintIndexRequest576_ptr);
        using CNetworkEXTrunkResDivisionRequest578_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTrunkResDivisionRequest578_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTrunkResDivisionRequest578_ptr);
        using CNetworkEXTutorialProcessReport580_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXTutorialProcessReport580_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXTutorialProcessReport580_ptr);
        using CNetworkEXUILockInitResult582_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXUILockInitResult582_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXUILockInitResult582_ptr);
        using CNetworkEXUILockRefreshResult584_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXUILockRefreshResult584_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXUILockRefreshResult584_ptr);
        using CNetworkEXUILockUpdateResult586_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXUILockUpdateResult586_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXUILockUpdateResult586_ptr);
        using CNetworkEXUnitBulletFillRequest588_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitBulletFillRequest588_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitBulletFillRequest588_ptr);
        using CNetworkEXUnitBulletReplaceRequest590_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitBulletReplaceRequest590_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitBulletReplaceRequest590_ptr);
        using CNetworkEXUnitDeliveryRequest592_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitDeliveryRequest592_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitDeliveryRequest592_ptr);
        using CNetworkEXUnitFrameBuyRequest594_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitFrameBuyRequest594_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitFrameBuyRequest594_ptr);
        using CNetworkEXUnitFrameRepairRequest596_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitFrameRepairRequest596_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitFrameRepairRequest596_ptr);
        using CNetworkEXUnitLeaveRequest598_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitLeaveRequest598_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitLeaveRequest598_ptr);
        using CNetworkEXUnitPackFillRequest600_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitPackFillRequest600_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitPackFillRequest600_ptr);
        using CNetworkEXUnitPartTuningRequest602_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitPartTuningRequest602_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitPartTuningRequest602_ptr);
        using CNetworkEXUnitReturnRequest604_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitReturnRequest604_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitReturnRequest604_ptr);
        using CNetworkEXUnitSellRequest606_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitSellRequest606_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitSellRequest606_ptr);
        using CNetworkEXUnitTakeRequest608_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUnitTakeRequest608_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUnitTakeRequest608_ptr);
        using CNetworkEXUpgradeItemRequest610_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUpgradeItemRequest610_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUpgradeItemRequest610_ptr);
        using CNetworkEXUseFireCrackerItemRequest612_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUseFireCrackerItemRequest612_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUseFireCrackerItemRequest612_ptr);
        using CNetworkEXUsePotionRequest614_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUsePotionRequest614_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUsePotionRequest614_ptr);
        using CNetworkEXUseRadarItemRequest616_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUseRadarItemRequest616_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUseRadarItemRequest616_ptr);
        using CNetworkEXUseRecallTeleportItemRequest618_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUseRecallTeleportItemRequest618_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUseRecallTeleportItemRequest618_ptr);
        using CNetworkEXUseRecoverLossExpItemRequest620_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUseRecoverLossExpItemRequest620_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUseRecoverLossExpItemRequest620_ptr);
        using CNetworkEXUseSoccerBallItemRequest622_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXUseSoccerBallItemRequest622_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXUseSoccerBallItemRequest622_ptr);
        using CNetworkEXUserBlockResult624_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXUserBlockResult624_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXUserBlockResult624_ptr);
        using CNetworkEXUserLoop626_ptr = void (WINAPIV*)(struct CNetworkEX*);
        using CNetworkEXUserLoop626_clbk = void (WINAPIV*)(struct CNetworkEX*, CNetworkEXUserLoop626_ptr);
        using CNetworkEXWebAgentLineAnalysis628_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*);
        using CNetworkEXWebAgentLineAnalysis628_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, struct _MSG_HEADER*, char*, CNetworkEXWebAgentLineAnalysis628_ptr);
        using CNetworkEXWeeklyGuildRankRequest630_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXWeeklyGuildRankRequest630_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXWeeklyGuildRankRequest630_ptr);
        using CNetworkEXWhisperBlockReport632_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXWhisperBlockReport632_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXWhisperBlockReport632_ptr);
        using CNetworkEXWorldExitInform634_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXWorldExitInform634_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXWorldExitInform634_ptr);
        using CNetworkEXWorldMsgInform636_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXWorldMsgInform636_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXWorldMsgInform636_ptr);
        using CNetworkEXWorldServiceInform638_ptr = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*);
        using CNetworkEXWorldServiceInform638_clbk = bool (WINAPIV*)(struct CNetworkEX*, unsigned int, char*, CNetworkEXWorldServiceInform638_ptr);
        using CNetworkEXZoneAliveCheckRequest640_ptr = bool (WINAPIV*)(struct CNetworkEX*, int, char*);
        using CNetworkEXZoneAliveCheckRequest640_clbk = bool (WINAPIV*)(struct CNetworkEX*, int, char*, CNetworkEXZoneAliveCheckRequest640_ptr);
        
        using CNetworkEXdtor_CNetworkEX645_ptr = void (WINAPIV*)(struct CNetworkEX*);
        using CNetworkEXdtor_CNetworkEX645_clbk = void (WINAPIV*)(struct CNetworkEX*, CNetworkEXdtor_CNetworkEX645_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

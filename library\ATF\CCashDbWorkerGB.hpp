// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CashDbWorker.hpp>


START_ATF_NAMESPACE
    struct  CCashDbWorkerGB : CashDbWorker
    {
        struct CEnglandBillingMgr *_pkNet;
    public:
        CCashDbWorkerGB();
        void ctor_CCashDbWorkerGB();
        void CompleteWork();
        void DoWork();
        void GetUseCashQueryStr(struct _param_cash_update* rParam, int nIdx, char* wszQuery, uint64_t tBufferSize);
        bool Initialize();
        void Release();
        bool _init_database();
        int _wait_tsk_cash_select(struct Task* pkTsk, int nIdx);
        int _wait_tsk_cash_update(struct Task* pkTsk, int nIdx);
        ~CCashDbWorkerGB();
        void dtor_CCashDbWorkerGB();
    };
END_ATF_NAMESPACE

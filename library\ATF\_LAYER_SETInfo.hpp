// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LAYER_SET.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _LAYER_SETActiveLayer2_ptr = void (WINAPIV*)(struct _LAYER_SET*, struct _MULTI_BLOCK*);
        using _LAYER_SETActiveLayer2_clbk = void (WINAPIV*)(struct _LAYER_SET*, struct _MULTI_BLOCK*, _LAYER_SETActiveLayer2_ptr);
        using _LAYER_SETCreateLayer4_ptr = void (WINAPIV*)(struct _LAYER_SET*, int);
        using _LAYER_SETCreateLayer4_clbk = void (WINAPIV*)(struct _LAYER_SET*, int, _LAYER_SETCreateLayer4_ptr);
        using _LAYER_SETInertLayer6_ptr = bool (WINAPIV*)(struct _LAYER_SET*);
        using _LAYER_SETInertLayer6_clbk = bool (WINAPIV*)(struct _LAYER_SET*, _LAYER_SETInertLayer6_ptr);
        using _LAYER_SETIsActiveLayer8_ptr = bool (WINAPIV*)(struct _LAYER_SET*);
        using _LAYER_SETIsActiveLayer8_clbk = bool (WINAPIV*)(struct _LAYER_SET*, _LAYER_SETIsActiveLayer8_ptr);
        
        using _LAYER_SETctor__LAYER_SET10_ptr = void (WINAPIV*)(struct _LAYER_SET*);
        using _LAYER_SETctor__LAYER_SET10_clbk = void (WINAPIV*)(struct _LAYER_SET*, _LAYER_SETctor__LAYER_SET10_ptr);
        
        using _LAYER_SETdtor__LAYER_SET14_ptr = void (WINAPIV*)(struct _LAYER_SET*);
        using _LAYER_SETdtor__LAYER_SET14_clbk = void (WINAPIV*)(struct _LAYER_SET*, _LAYER_SETdtor__LAYER_SET14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

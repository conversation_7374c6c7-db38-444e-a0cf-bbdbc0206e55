// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum PVP_CASH_ERR_CODE
    {
      error_no_have_item_to_recover = 0x1,
      error_no_more_recover_to_talik = 0x2,
      error_not_lose_pvp_cash_same_palyer = 0x3,
      error_not_have_pvp_cash_same_palyer = 0x4,
      error_cont_lose_limit_die = 0x5,
      error_cont_lose_limit_kill = 0x6,
      error_cont_have_limit_kill = 0x7,
      error_cont_have_limit_die = 0x8,
      error_not_have_point_position = 0x9,
      error_not_enough_condition = 0xA,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CObject.hpp>
#include <CPlex.hpp>


START_ATF_NAMESPACE
    struct  CMapPtrToWord : CObject
    {
        struct CAssoc
        {
            CAssoc *pNext;
            void *key;
            unsigned __int16 value;
        };
        CAssoc **m_pHashTable;
        unsigned int m_nHashTableSize;
        __int64 m_nCount;
        CAssoc *m_pFreeList;
        CPlex *m_pBlocks;
        __int64 m_nBlockSize;
    };
END_ATF_NAMESPACE

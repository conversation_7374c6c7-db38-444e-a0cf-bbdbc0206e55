// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _param_cash_total_selling
    {
        unsigned int dwTotalSellCash;
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_param_cash_total_selling, 4>(), "_param_cash_total_selling");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _INVEN_DB_BASE
    {
        struct  _LIST
        {
            _INVENKEY Key;
            unsigned __int64 dwDur;
            unsigned int dwUpt;
            unsigned int dwItemETSerial;
            unsigned __int64 lnUID;
            char byCsMethod;
            unsigned int dwT;
            unsigned int dwLendRegdTime;
        public:
            void Init();
            bool Release();
            bool Set(_STORAGE_LIST::_db_con* pItem);
            _LIST();
            void ctor__LIST();
        };
        _LIST m_List[100];
    public:
        void Init();
        _INVEN_DB_BASE();
        void ctor__INVEN_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_INVEN_DB_BASE, 3700>(), "_INVEN_DB_BASE");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Iterator_with_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Outit_with_base<_Iterator_base> : _Iterator_with_base<output_iterator_tag,void,void,void,void,_Iterator_base>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLevel.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CLevelCalcR3Fog1_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelCalcR3Fog1_clbk = void (WINAPIV*)(struct CLevel*, CLevelCalcR3Fog1_ptr);
        using CLevelDrawBBox2_ptr = void (WINAPIV*)(struct CLevel*, float*, float*, uint32_t);
        using CLevelDrawBBox2_clbk = void (WINAPIV*)(struct CLevel*, float*, float*, uint32_t, CLevelDrawBBox2_ptr);
        using CLevelDrawBBox3_ptr = void (WINAPIV*)(struct CLevel*, int16_t*, int16_t*, uint32_t);
        using CLevelDrawBBox3_clbk = void (WINAPIV*)(struct CLevel*, int16_t*, int16_t*, uint32_t, CLevelDrawBBox3_ptr);
        using CLevelDrawLeafBBox4_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelDrawLeafBBox4_clbk = void (WINAPIV*)(struct CLevel*, CLevelDrawLeafBBox4_ptr);
        using CLevelDrawMapAlphaRender5_ptr = void (WINAPIV*)(struct CLevel*, float*);
        using CLevelDrawMapAlphaRender5_clbk = void (WINAPIV*)(struct CLevel*, float*, CLevelDrawMapAlphaRender5_ptr);
        using CLevelDrawMapEntitiesRender6_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelDrawMapEntitiesRender6_clbk = void (WINAPIV*)(struct CLevel*, CLevelDrawMapEntitiesRender6_ptr);
        using CLevelDrawMapRender7_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelDrawMapRender7_clbk = void (WINAPIV*)(struct CLevel*, CLevelDrawMapRender7_ptr);
        using CLevelDrawMatBBox8_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelDrawMatBBox8_clbk = void (WINAPIV*)(struct CLevel*, CLevelDrawMatBBox8_ptr);
        using CLevelDrawShadowRender9_ptr = void (WINAPIV*)(struct CLevel*, float*, float*, float*);
        using CLevelDrawShadowRender9_clbk = void (WINAPIV*)(struct CLevel*, float*, float*, float*, CLevelDrawShadowRender9_ptr);
        using CLevelDrawSkyBoxRender10_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelDrawSkyBoxRender10_clbk = void (WINAPIV*)(struct CLevel*, CLevelDrawSkyBoxRender10_ptr);
        using CLevelDrawTestBox11_ptr = void (WINAPIV*)(struct CLevel*, float*, float*, uint32_t);
        using CLevelDrawTestBox11_clbk = void (WINAPIV*)(struct CLevel*, float*, float*, uint32_t, CLevelDrawTestBox11_ptr);
        using CLevelFrameMove12_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelFrameMove12_clbk = void (WINAPIV*)(struct CLevel*, CLevelFrameMove12_ptr);
        using CLevelGetEnvironment14_ptr = unsigned int (WINAPIV*)(struct CLevel*);
        using CLevelGetEnvironment14_clbk = unsigned int (WINAPIV*)(struct CLevel*, CLevelGetEnvironment14_ptr);
        using CLevelGetFirstYpos15_ptr = float (WINAPIV*)(struct CLevel*, float*, float*, float*);
        using CLevelGetFirstYpos15_clbk = float (WINAPIV*)(struct CLevel*, float*, float*, float*, CLevelGetFirstYpos15_ptr);
        using CLevelGetFirstYpos16_ptr = float (WINAPIV*)(struct CLevel*, float*, int);
        using CLevelGetFirstYpos16_clbk = float (WINAPIV*)(struct CLevel*, float*, int, CLevelGetFirstYpos16_ptr);
        using CLevelGetFirstYpos17_ptr = float (WINAPIV*)(struct CLevel*, float*, int16_t*, int16_t*);
        using CLevelGetFirstYpos17_clbk = float (WINAPIV*)(struct CLevel*, float*, int16_t*, int16_t*, CLevelGetFirstYpos17_ptr);
        using CLevelGetFrustumNormalPlane18_ptr = void (WINAPIV*)(struct CLevel*, float**);
        using CLevelGetFrustumNormalPlane18_clbk = void (WINAPIV*)(struct CLevel*, float**, CLevelGetFrustumNormalPlane18_ptr);
        using CLevelGetMapName19_ptr = char* (WINAPIV*)(struct CLevel*);
        using CLevelGetMapName19_clbk = char* (WINAPIV*)(struct CLevel*, CLevelGetMapName19_ptr);
        using CLevelGetNextYpos20_ptr = int (WINAPIV*)(struct CLevel*, float*, float*);
        using CLevelGetNextYpos20_clbk = int (WINAPIV*)(struct CLevel*, float*, float*, CLevelGetNextYpos20_ptr);
        using CLevelGetNextYposFar21_ptr = int64_t (WINAPIV*)(struct CLevel*, float*, float*, float*);
        using CLevelGetNextYposFar21_clbk = int64_t (WINAPIV*)(struct CLevel*, float*, float*, float*, CLevelGetNextYposFar21_ptr);
        using CLevelGetNextYposFarProgress22_ptr = int64_t (WINAPIV*)(struct CLevel*, float*, float*, float*);
        using CLevelGetNextYposFarProgress22_clbk = int64_t (WINAPIV*)(struct CLevel*, float*, float*, float*, CLevelGetNextYposFarProgress22_ptr);
        using CLevelGetNextYposForServer23_ptr = int (WINAPIV*)(struct CLevel*, float*, float*);
        using CLevelGetNextYposForServer23_clbk = int (WINAPIV*)(struct CLevel*, float*, float*, CLevelGetNextYposForServer23_ptr);
        using CLevelGetNextYposForServerFar24_ptr = int64_t (WINAPIV*)(struct CLevel*, float*, float*, float*);
        using CLevelGetNextYposForServerFar24_clbk = int64_t (WINAPIV*)(struct CLevel*, float*, float*, float*, CLevelGetNextYposForServerFar24_ptr);
        using CLevelGetNextYposNoAttr25_ptr = int (WINAPIV*)(struct CLevel*, float*, float*);
        using CLevelGetNextYposNoAttr25_clbk = int (WINAPIV*)(struct CLevel*, float*, float*, CLevelGetNextYposNoAttr25_ptr);
        using CLevelGetPath26_ptr = uint32_t (WINAPIV*)(struct CLevel*, float*, float*, float**, uint32_t*, int);
        using CLevelGetPath26_clbk = uint32_t (WINAPIV*)(struct CLevel*, float*, float*, float**, uint32_t*, int, CLevelGetPath26_ptr);
        using CLevelGetPathFromDepth27_ptr = uint32_t (WINAPIV*)(struct CLevel*, float*, float*, int, float**, uint32_t*);
        using CLevelGetPathFromDepth27_clbk = uint32_t (WINAPIV*)(struct CLevel*, float*, float*, int, float**, uint32_t*, CLevelGetPathFromDepth27_ptr);
        using CLevelGetPointFromScreenRay28_ptr = int64_t (WINAPIV*)(struct CLevel*, int32_t, int32_t, float**);
        using CLevelGetPointFromScreenRay28_clbk = int64_t (WINAPIV*)(struct CLevel*, int32_t, int32_t, float**, CLevelGetPointFromScreenRay28_ptr);
        using CLevelGetPointFromScreenRayFar29_ptr = int64_t (WINAPIV*)(struct CLevel*, int32_t, int32_t, float**);
        using CLevelGetPointFromScreenRayFar29_clbk = int64_t (WINAPIV*)(struct CLevel*, int32_t, int32_t, float**, CLevelGetPointFromScreenRayFar29_ptr);
        using CLevelHearMapSound30_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelHearMapSound30_clbk = void (WINAPIV*)(struct CLevel*, CLevelHearMapSound30_ptr);
        using CLevelIsCollisionRayAABB31_ptr = int (WINAPIV*)(struct CLevel*, int32_t, int32_t, float*, float*, float**);
        using CLevelIsCollisionRayAABB31_clbk = int (WINAPIV*)(struct CLevel*, int32_t, int32_t, float*, float*, float**, CLevelIsCollisionRayAABB31_ptr);
        using CLevelIsLoadedBsp33_ptr = int (WINAPIV*)(struct CLevel*);
        using CLevelIsLoadedBsp33_clbk = int (WINAPIV*)(struct CLevel*, CLevelIsLoadedBsp33_ptr);
        using CLevelLoadLevel34_ptr = void (WINAPIV*)(struct CLevel*, char*);
        using CLevelLoadLevel34_clbk = void (WINAPIV*)(struct CLevel*, char*, CLevelLoadLevel34_ptr);
        using CLevelPrepareShadowRender35_ptr = void (WINAPIV*)(struct CLevel*, float*, void*, float, uint32_t, float, float);
        using CLevelPrepareShadowRender35_clbk = void (WINAPIV*)(struct CLevel*, float*, void*, float, uint32_t, float, float, CLevelPrepareShadowRender35_ptr);
        using CLevelReLoadAllMaterial36_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelReLoadAllMaterial36_clbk = void (WINAPIV*)(struct CLevel*, CLevelReLoadAllMaterial36_ptr);
        using CLevelReleaseLevel37_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelReleaseLevel37_clbk = void (WINAPIV*)(struct CLevel*, CLevelReleaseLevel37_ptr);
        using CLevelScreenShot38_ptr = void (WINAPIV*)(struct CLevel*);
        using CLevelScreenShot38_clbk = void (WINAPIV*)(struct CLevel*, CLevelScreenShot38_ptr);
        using CLevelSetCameraPos39_ptr = void (WINAPIV*)(struct CLevel*, float*);
        using CLevelSetCameraPos39_clbk = void (WINAPIV*)(struct CLevel*, float*, CLevelSetCameraPos39_ptr);
        using CLevelSetEnvironment40_ptr = void (WINAPIV*)(struct CLevel*, uint32_t);
        using CLevelSetEnvironment40_clbk = void (WINAPIV*)(struct CLevel*, uint32_t, CLevelSetEnvironment40_ptr);
        using CLevelSetViewMatrix41_ptr = void (WINAPIV*)(struct CLevel*, struct D3DXMATRIX*);
        using CLevelSetViewMatrix41_clbk = void (WINAPIV*)(struct CLevel*, struct D3DXMATRIX*, CLevelSetViewMatrix41_ptr);
        
        using CLeveldtor_CLevel43_ptr = int64_t (WINAPIV*)(struct CLevel*);
        using CLeveldtor_CLevel43_clbk = int64_t (WINAPIV*)(struct CLevel*, CLeveldtor_CLevel43_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_cheat_player_vote_info
    {
        unsigned int dwAccountSerial;
        unsigned int dwCharSerial;
        char wszChar<PERSON>ame[17];
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_cheat_player_vote_info, 28>(), "_qry_case_cheat_player_vote_info");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <TournamentWinner.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using TournamentWinnerctor_TournamentWinner2_ptr = void (WINAPIV*)(struct TournamentWinner*);
        using TournamentWinnerctor_TournamentWinner2_clbk = void (WINAPIV*)(struct TournamentWinner*, TournamentWinnerctor_TournamentWinner2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

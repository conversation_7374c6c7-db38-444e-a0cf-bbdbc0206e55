// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGravityStoneRegener.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CGravityStoneRegenerctor_CGravityStoneRegener2_ptr = void (WINAPIV*)(struct CGravityStoneRegener*);
        using CGravityStoneRegenerctor_CGravityStoneRegener2_clbk = void (WINAPIV*)(struct CGravityStoneRegener*, CGravityStoneRegenerctor_CGravityStoneRegener2_ptr);
        using CGravityStoneRegenerCheatClearRegenState4_ptr = void (WINAPIV*)(struct CGravityStoneRegener*);
        using CGravityStoneRegenerCheatClearRegenState4_clbk = void (WINAPIV*)(struct CGravityStoneRegener*, CGravityStoneRegenerCheatClearRegenState4_ptr);
        using CGravityStoneRegenerClearRegen6_ptr = bool (WINAPIV*)(struct CGravityStoneRegener*);
        using CGravityStoneRegenerClearRegen6_clbk = bool (WINAPIV*)(struct CGravityStoneRegener*, CGravityStoneRegenerClearRegen6_ptr);
        using CGravityStoneRegenerCreate8_ptr = bool (WINAPIV*)(struct CGravityStoneRegener*, struct CMapData*);
        using CGravityStoneRegenerCreate8_clbk = bool (WINAPIV*)(struct CGravityStoneRegener*, struct CMapData*, CGravityStoneRegenerCreate8_ptr);
        using CGravityStoneRegenerDestroy10_ptr = void (WINAPIV*)(struct CGravityStoneRegener*);
        using CGravityStoneRegenerDestroy10_clbk = void (WINAPIV*)(struct CGravityStoneRegener*, CGravityStoneRegenerDestroy10_ptr);
        using CGravityStoneRegenerGetPortalInx12_ptr = int (WINAPIV*)(struct CGravityStoneRegener*);
        using CGravityStoneRegenerGetPortalInx12_clbk = int (WINAPIV*)(struct CGravityStoneRegener*, CGravityStoneRegenerGetPortalInx12_ptr);
        using CGravityStoneRegenerGetStateString14_ptr = char* (WINAPIV*)(struct CGravityStoneRegener*, struct ATL::CStringT<char>*);
        using CGravityStoneRegenerGetStateString14_clbk = char* (WINAPIV*)(struct CGravityStoneRegener*, struct ATL::CStringT<char>*, CGravityStoneRegenerGetStateString14_ptr);
        using CGravityStoneRegenerInit16_ptr = bool (WINAPIV*)(struct CGravityStoneRegener*, unsigned int, uint16_t, struct CMapData*);
        using CGravityStoneRegenerInit16_clbk = bool (WINAPIV*)(struct CGravityStoneRegener*, unsigned int, uint16_t, struct CMapData*, CGravityStoneRegenerInit16_ptr);
        using CGravityStoneRegenerIsNearPosition18_ptr = bool (WINAPIV*)(struct CGravityStoneRegener*, float*);
        using CGravityStoneRegenerIsNearPosition18_clbk = bool (WINAPIV*)(struct CGravityStoneRegener*, float*, CGravityStoneRegenerIsNearPosition18_ptr);
        using CGravityStoneRegenerRegen20_ptr = int (WINAPIV*)(struct CGravityStoneRegener*);
        using CGravityStoneRegenerRegen20_clbk = int (WINAPIV*)(struct CGravityStoneRegener*, CGravityStoneRegenerRegen20_ptr);
        using CGravityStoneRegenerSendMsgAlterState22_ptr = void (WINAPIV*)(struct CGravityStoneRegener*);
        using CGravityStoneRegenerSendMsgAlterState22_clbk = void (WINAPIV*)(struct CGravityStoneRegener*, CGravityStoneRegenerSendMsgAlterState22_ptr);
        using CGravityStoneRegenerSendMsg_FixPosition24_ptr = void (WINAPIV*)(struct CGravityStoneRegener*, int);
        using CGravityStoneRegenerSendMsg_FixPosition24_clbk = void (WINAPIV*)(struct CGravityStoneRegener*, int, CGravityStoneRegenerSendMsg_FixPosition24_ptr);
        using CGravityStoneRegenerTake26_ptr = char (WINAPIV*)(struct CGravityStoneRegener*, struct CMapData*, float*);
        using CGravityStoneRegenerTake26_clbk = char (WINAPIV*)(struct CGravityStoneRegener*, struct CMapData*, float*, CGravityStoneRegenerTake26_ptr);
        
        using CGravityStoneRegenerdtor_CGravityStoneRegener32_ptr = void (WINAPIV*)(struct CGravityStoneRegener*);
        using CGravityStoneRegenerdtor_CGravityStoneRegener32_clbk = void (WINAPIV*)(struct CGravityStoneRegener*, CGravityStoneRegenerdtor_CGravityStoneRegener32_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

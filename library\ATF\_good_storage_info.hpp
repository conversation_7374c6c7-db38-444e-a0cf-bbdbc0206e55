// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _good_storage_info
    {
        char byItemTableCode;
        unsigned __int16 wItemIndex;
        int bExist;
        char byMoneyUnit;
        int nStdPrice;
        int nStdPoint;
        int nGoldPoint;
        int nKillPoint;
        int nResPoint;
        unsigned int dwDurPoint;
        unsigned int dwUpCode;
        char byType;
        unsigned int dwLimitIndex;
    public:
        _good_storage_info();
        void ctor__good_storage_info();
    };
END_ATF_NAMESPACE

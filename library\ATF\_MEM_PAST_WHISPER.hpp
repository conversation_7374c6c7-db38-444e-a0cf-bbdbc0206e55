// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _MEM_PAST_WHISPER
    {
        bool bMemory;
        char wszName[17];
        char by<PERSON>ame<PERSON><PERSON>;
        CPlayer *pDst;
        unsigned int dwMemoryTime;
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_MEM_PAST_WHISPER, 40>(), "_MEM_PAST_WHISPER");
END_ATF_NAMESPACE

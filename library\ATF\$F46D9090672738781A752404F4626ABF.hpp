// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagDEC.hpp>


START_ATF_NAMESPACE
    struct $F46D9090672738781A752404F4626ABF
    {
        BYTE gap0[8];
        tagDEC *pdecVal;
    };    
    static_assert(ATF::checkSize<$F46D9090672738781A752404F4626ABF, 16>(), "$F46D9090672738781A752404F4626ABF");
END_ATF_NAMESPACE

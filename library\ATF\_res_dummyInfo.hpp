// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_res_dummy.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _res_dummyGetDelay2_ptr = int (WINAPIV*)(struct _res_dummy*, char, bool);
        using _res_dummyGetDelay2_clbk = int (WINAPIV*)(struct _res_dummy*, char, bool, _res_dummyGetDelay2_ptr);
        using _res_dummyGetQualityGrade4_ptr = char (WINAPIV*)(struct _res_dummy*);
        using _res_dummyGetQualityGrade4_clbk = char (WINAPIV*)(struct _res_dummy*, _res_dummyGetQualityGrade4_ptr);
        using _res_dummySetDummy6_ptr = bool (WINAPIV*)(struct _res_dummy*, struct _dummy_position*, char);
        using _res_dummySetDummy6_clbk = bool (WINAPIV*)(struct _res_dummy*, struct _dummy_position*, char, _res_dummySetDummy6_ptr);
        using _res_dummySetRangeGrade8_ptr = void (WINAPIV*)(struct _res_dummy*);
        using _res_dummySetRangeGrade8_clbk = void (WINAPIV*)(struct _res_dummy*, _res_dummySetRangeGrade8_ptr);
        
        using _res_dummyctor__res_dummy10_ptr = void (WINAPIV*)(struct _res_dummy*);
        using _res_dummyctor__res_dummy10_clbk = void (WINAPIV*)(struct _res_dummy*, _res_dummyctor__res_dummy10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

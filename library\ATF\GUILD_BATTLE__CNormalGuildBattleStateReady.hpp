// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleState.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CNormalGuildBattleStateReady : CNormalGuildBattleState
        {
        public:
            CNormalGuildBattleStateReady();
            void ctor_CNormalGuildBattleStateReady();
            int Enter(struct CNormalGuildBattle* pkBattle);
            struct ATL::CTimeSpan* GetTerm(struct ATL::CTimeSpan* result);
            ~CNormalGuildBattleStateReady();
            void dtor_CNormalGuildBattleStateReady();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CNormalGuildBattleStateReady, 8>(), "GUILD_BATTLE::CNormalGuildBattleStateReady");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

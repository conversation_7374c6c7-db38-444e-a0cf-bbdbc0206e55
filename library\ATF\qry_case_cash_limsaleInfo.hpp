// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <qry_case_cash_limsale.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using qry_case_cash_limsalector_qry_case_cash_limsale2_ptr = void (WINAPIV*)(struct qry_case_cash_limsale*);
        using qry_case_cash_limsalector_qry_case_cash_limsale2_clbk = void (WINAPIV*)(struct qry_case_cash_limsale*, qry_case_cash_limsalector_qry_case_cash_limsale2_ptr);
        using qry_case_cash_limsalesize4_ptr = int (WINAPIV*)(struct qry_case_cash_limsale*);
        using qry_case_cash_limsalesize4_clbk = int (WINAPIV*)(struct qry_case_cash_limsale*, qry_case_cash_limsalesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GuildCreateEventInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using GuildCreateEventInfoApplyModifiedGuildEventInfo2_ptr = bool (WINAPIV*)(struct GuildCreateEventInfo*);
        using GuildCreateEventInfoApplyModifiedGuildEventInfo2_clbk = bool (WINAPIV*)(struct GuildCreateEventInfo*, GuildCreateEventInfoApplyModifiedGuildEventInfo2_ptr);
        using GuildCreateEventInfoCheckEventDate4_ptr = bool (WINAPIV*)(struct GuildCreateEventInfo*);
        using GuildCreateEventInfoCheckEventDate4_clbk = bool (WINAPIV*)(struct GuildCreateEventInfo*, GuildCreateEventInfoCheckEventDate4_ptr);
        using GuildCreateEventInfoGetEmblemDalant6_ptr = unsigned int (WINAPIV*)(struct GuildCreateEventInfo*);
        using GuildCreateEventInfoGetEmblemDalant6_clbk = unsigned int (WINAPIV*)(struct GuildCreateEventInfo*, GuildCreateEventInfoGetEmblemDalant6_ptr);
        using GuildCreateEventInfoGetEstConsumeDalant8_ptr = unsigned int (WINAPIV*)(struct GuildCreateEventInfo*);
        using GuildCreateEventInfoGetEstConsumeDalant8_clbk = unsigned int (WINAPIV*)(struct GuildCreateEventInfo*, GuildCreateEventInfoGetEstConsumeDalant8_ptr);
        
        using GuildCreateEventInfoctor_GuildCreateEventInfo10_ptr = void (WINAPIV*)(struct GuildCreateEventInfo*);
        using GuildCreateEventInfoctor_GuildCreateEventInfo10_clbk = void (WINAPIV*)(struct GuildCreateEventInfo*, GuildCreateEventInfoctor_GuildCreateEventInfo10_ptr);
        using GuildCreateEventInfoInit12_ptr = void (WINAPIV*)(struct GuildCreateEventInfo*);
        using GuildCreateEventInfoInit12_clbk = void (WINAPIV*)(struct GuildCreateEventInfo*, GuildCreateEventInfoInit12_ptr);
        using GuildCreateEventInfoLoop14_ptr = void (WINAPIV*)(struct GuildCreateEventInfo*);
        using GuildCreateEventInfoLoop14_clbk = void (WINAPIV*)(struct GuildCreateEventInfo*, GuildCreateEventInfoLoop14_ptr);
        using GuildCreateEventInfoReadEventInfo16_ptr = void (WINAPIV*)(struct GuildCreateEventInfo*);
        using GuildCreateEventInfoReadEventInfo16_clbk = void (WINAPIV*)(struct GuildCreateEventInfo*, GuildCreateEventInfoReadEventInfo16_ptr);
        using GuildCreateEventInfoSetConsumeDalantFree18_ptr = void (WINAPIV*)(struct GuildCreateEventInfo*, bool);
        using GuildCreateEventInfoSetConsumeDalantFree18_clbk = void (WINAPIV*)(struct GuildCreateEventInfo*, bool, GuildCreateEventInfoSetConsumeDalantFree18_ptr);
        
        using GuildCreateEventInfodtor_GuildCreateEventInfo20_ptr = void (WINAPIV*)(struct GuildCreateEventInfo*);
        using GuildCreateEventInfodtor_GuildCreateEventInfo20_clbk = void (WINAPIV*)(struct GuildCreateEventInfo*, GuildCreateEventInfodtor_GuildCreateEventInfo20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

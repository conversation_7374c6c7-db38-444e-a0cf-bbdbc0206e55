// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _IMAGEHLP_SYMBOL64
    {
        unsigned int SizeOfStruct;
        unsigned __int64 Address;
        unsigned int Size;
        unsigned int Flags;
        unsigned int MaxNameLength;
        char Name[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

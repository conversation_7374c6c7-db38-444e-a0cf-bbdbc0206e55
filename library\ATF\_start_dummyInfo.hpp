// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_start_dummy.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _start_dummySetDummy2_ptr = bool (WINAPIV*)(struct _start_dummy*, struct _dummy_position*);
        using _start_dummySetDummy2_clbk = bool (WINAPIV*)(struct _start_dummy*, struct _dummy_position*, _start_dummySetDummy2_ptr);
        
        using _start_dummyctor__start_dummy4_ptr = void (WINAPIV*)(struct _start_dummy*);
        using _start_dummyctor__start_dummy4_clbk = void (WINAPIV*)(struct _start_dummy*, _start_dummyctor__start_dummy4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    struct _SHFILEOPSTRUCTA
    {
        HWND__ *hwnd;
        unsigned int wFunc;
        const char *pFrom;
        const char *pTo;
        unsigned __int16 fFlags;
        int fAnyOperationsAborted;
        void *hNameMappings;
        const char *lpszProgressTitle;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <INTERNET_PER_CONN_OPTIONA.hpp>


START_ATF_NAMESPACE
    struct INTERNET_PER_CONN_OPTION_LISTA
    {
        unsigned int dwSize;
        char *pszConnection;
        unsigned int dwOptionCount;
        unsigned int dwOptionError;
        INTERNET_PER_CONN_OPTIONA *pOptions;
    };
END_ATF_NAMESPACE

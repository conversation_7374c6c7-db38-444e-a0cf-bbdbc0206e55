#include <_personal_amine_fixpos_zocl.hpp>


START_ATF_NAMESPACE
    _personal_amine_fixpos_zocl::_personal_amine_fixpos_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_fixpos_zocl*);
        (org_ptr(0x1402de380L))(this);
    };
    void _personal_amine_fixpos_zocl::ctor__personal_amine_fixpos_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_fixpos_zocl*);
        (org_ptr(0x1402de380L))(this);
    };
    int _personal_amine_fixpos_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_amine_fixpos_zocl*);
        return (org_ptr(0x1402de3d0L))(this);
    };
END_ATF_NAMESPACE

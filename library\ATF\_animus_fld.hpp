// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _animus_fld : _base_fld
    {
        int m_nLevel;
        unsigned __int64 m_nForLvUpExp;
        int m_nUseFP;
        float m_fPenalty;
        float m_fAttGap;
        int m_nAttack_DP;
        int m_nAttFcStd;
        int m_nMinAFSelProb;
        int m_nMaxAFSelProb;
        int m_nAttSklUnit;
        int m_nDefSklUnit;
        float m_fWeakPart;
        int m_nStdDefFc;
        float m_fDefGap;
        float m_fDefFacing;
        int m_nFireTol;
        int m_nWaterTol;
        int m_nSoilTol;
        int m_nWindTol;
        int m_nForceLevel;
        int m_nForceMastery;
        int m_nForceAttStd;
        char m_strAttTechID1[64];
        int m_nAttTech1UseProb;
        int m_nAttTechID1MotionTime;
        char m_strPSecTechID[64];
        int m_nPSecTechIDMotionTime;
        char m_strMSecTechID[64];
        int m_nMSecTechIDMotionTime;
        int m_nMaxHP;
        int m_nHPRecDelay;
        int m_nHPRecUnit;
        int m_nMaxFP;
        int m_nFPRecDelay;
        int m_nFPRecUnit;
        int m_nAttSpd;
        int m_nAttMoTime1;
        int m_nAttMoTime2;
        int m_nCrtMoTime;
        int m_nViewExt;
        int m_nRefExt;
        int m_nAttExt;
        int m_nMovSpd;
        int m_nScaleRate;
        int m_nWidth;
        int m_nAttEffType;
        int m_nDefEffType;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <BasicType.hpp>
#include <_CONTEXT.hpp>
#include <_EXCEPTION_POINTERS.hpp>
#include <_SYMBOL_INFO.hpp>
#include <_tagSTACKFRAME64.hpp>
#include <_TI_FINDCHILDREN_PARAMS.hpp>


START_ATF_NAMESPACE
    struct  WheatyExceptionReport
    {
        struct DumpTypeIndex
        {
            struct __l7
            {
                struct FINDCHILDREN : _TI_FINDCHILDREN_PARAMS
                {
                    unsigned int MoreChildIds[1024];
                };
            };
        };
    public:
        static char* DumpTypeIndex(char* psz<PERSON>urr<PERSON>uffer, uint64_t modBase, unsigned int dwTypeIndex, unsigned int nestingLevel, uint64_t offset, bool* bHandled);
        static int EnumerateSymbolsCallback(struct _SYMBOL_INFO* pSymInfo, unsigned int SymbolSize, void* UserContext);
        static char* FormatOutputValue(char* psz<PERSON>urr<PERSON>uffer, BasicType basicType, uint64_t length, void* pAddress);
        static bool FormatSymbolValue(struct _SYMBOL_INFO* pSym, struct _tagSTACKFRAME64* sf, char* pszBuffer, unsigned int cbBuffer);
        static void GenerateExceptionReport(struct _EXCEPTION_POINTERS* pExceptionInfo);
        static BasicType GetBasicType(unsigned int typeIndex, uint64_t modBase);
        static int GetDisplayInfo(int nDeviceIndex, char* lpszDeviceInfo, char* lpszMonitorInfo);
        static char* GetExceptionString(unsigned int dwCode);
        static int GetLogicalAddress(void* addr, char* szModule, unsigned int len, unsigned int* section, unsigned int* offset);
        static char* GetOsName(unsigned int dwPlatformId, unsigned int dwMajorVersion, unsigned int dwMinorVersion);
        static char* GetOsVersion();
        void SetDescription(char* pszDescription);
        void SetFtpConnection(char* pszFtpIp, unsigned int nFtpPort, char* pszFtpId, char* pszFtpPwd, char* pszFtpDirectory);
        void SetLogName(char* pszLogName);
        void SetRunDialog(int bRun);
        WheatyExceptionReport();
        void ctor_WheatyExceptionReport();
        static int WheatyUnhandledExceptionFilter(struct _EXCEPTION_POINTERS* pExceptionInfo);
        static void WriteStackDetails(struct _CONTEXT* pContext, bool bWriteVariables);
        static int _tprintfh(void* hFile, char* format);
        static int printf(char* format);
        ~WheatyExceptionReport();
        void dtor_WheatyExceptionReport();
    };
END_ATF_NAMESPACE

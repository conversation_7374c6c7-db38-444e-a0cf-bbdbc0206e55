// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_sheet_delete
    {
        char bySlotIndex;
        char byRaceCode;
        unsigned int dwAvatorSerial;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_sheet_delete, 8>(), "_qry_sheet_delete");
END_ATF_NAMESPACE

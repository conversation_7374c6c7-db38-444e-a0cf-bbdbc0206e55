// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CClientDC.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CClientDCdtor_CClientDC1_ptr = int64_t (WINAPIV*)(struct CClientDC*);
        using CClientDCdtor_CClientDC1_clbk = int64_t (WINAPIV*)(struct CClientDC*, CClientDCdtor_CClientDC1_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ANI_CAMERA_EXT.hpp>
#include <_ANI_OBJECT.hpp>


START_ATF_NAMESPACE
    struct _ANI_CAMERA
    {
        char mName[64];
        unsigned int h_num;
        float fov;
        float tdist;
        unsigned int ext_num;
        _ANI_CAMERA_EXT *ext;
        _ANI_OBJECT *obj;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum _D3DTRANSFORMSTATETYPE
    {
      D3DTRANSFORMSTATE_WORLD = 0x1,
      D3DTRANSFORMSTATE_VIEW = 0x2,
      D3DTRANSFORMSTATE_PROJECTION = 0x3,
      D3DTRANSFORMSTATE_WORLD1 = 0x4,
      D3DTRANSFORMSTATE_WORLD2 = 0x5,
      D3DTRANSFORMSTATE_WORLD3 = 0x6,
      D3DTRANSFORMSTATE_TEXTURE0 = 0x10,
      D3DTRANSFORMSTATE_TEXTURE1 = 0x11,
      D3DTRANSFORMSTATE_TEXTURE2 = 0x12,
      D3DTRANSFORMSTATE_TEXTURE3 = 0x13,
      D3DTRANSFORMSTATE_TEXTURE4 = 0x14,
      D3DTRANSFORMSTATE_TEXTURE5 = 0x15,
      D3DTRANSFORMSTATE_TEXTURE6 = 0x16,
      D3DTRANSFORMSTATE_TEXTURE7 = 0x17,
      D3DTRANSFORMSTATE_FORCEDWORD = 0x7FFFFFFF,
    };
END_ATF_NAMESPACE

#include <_NOT_ARRANGED_AVATOR_DB.hpp>


START_ATF_NAMESPACE
    void _NOT_ARRANGED_AVATOR_DB::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _NOT_ARRANGED_AVATOR_DB*);
        (org_ptr(0x14011f070L))(this);
    };
    _NOT_ARRANGED_AVATOR_DB::_NOT_ARRANGED_AVATOR_DB()
    {
        using org_ptr = void (WINAPIV*)(struct _NOT_ARRANGED_AVATOR_DB*);
        (org_ptr(0x14011f020L))(this);
    };
    void _NOT_ARRANGED_AVATOR_DB::ctor__NOT_ARRANGED_AVATOR_DB()
    {
        using org_ptr = void (WINAPIV*)(struct _NOT_ARRANGED_AVATOR_DB*);
        (org_ptr(0x14011f020L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagBITMAP
    {
        int bmType;
        int bmWidth;
        int bmHeight;
        int bmWidthBytes;
        unsigned __int16 bmPlanes;
        unsigned __int16 bmBitsPixel;
        void *bmBits;
    };
END_ATF_NAMESPACE

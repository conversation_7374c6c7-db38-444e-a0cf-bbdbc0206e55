// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuildRoomInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CGuildRoomInfoctor_CGuildRoomInfo2_ptr = void (WINAPIV*)(struct CGuildRoomInfo*, struct CGuildRoomInfo*);
        using CGuildRoomInfoctor_CGuildRoomInfo2_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, struct CGuildRoomInfo*, CGuildRoomInfoctor_CGuildRoomInfo2_ptr);
        
        using CGuildRoomInfoctor_CGuildRoomInfo4_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoctor_CGuildRoomInfo4_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoctor_CGuildRoomInfo4_ptr);
        using CGuildRoomInfoGetGuildSerial6_ptr = unsigned int (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoGetGuildSerial6_clbk = unsigned int (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoGetGuildSerial6_ptr);
        using CGuildRoomInfoGetMapData8_ptr = struct CMapData* (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoGetMapData8_clbk = struct CMapData* (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoGetMapData8_ptr);
        using CGuildRoomInfoGetMapLayer10_ptr = uint16_t (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoGetMapLayer10_clbk = uint16_t (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoGetMapLayer10_ptr);
        using CGuildRoomInfoGetMapPos12_ptr = bool (WINAPIV*)(struct CGuildRoomInfo*, float*);
        using CGuildRoomInfoGetMapPos12_clbk = bool (WINAPIV*)(struct CGuildRoomInfo*, float*, CGuildRoomInfoGetMapPos12_ptr);
        using CGuildRoomInfoGetRestTime14_ptr = int (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoGetRestTime14_clbk = int (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoGetRestTime14_ptr);
        using CGuildRoomInfoGetRoomType16_ptr = char (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoGetRoomType16_clbk = char (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoGetRoomType16_ptr);
        using CGuildRoomInfoIsMemberIn18_ptr = bool (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int);
        using CGuildRoomInfoIsMemberIn18_clbk = bool (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int, CGuildRoomInfoIsMemberIn18_ptr);
        using CGuildRoomInfoIsRent20_ptr = bool (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoIsRent20_clbk = bool (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoIsRent20_ptr);
        using CGuildRoomInfoOutAllRoomMember22_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoOutAllRoomMember22_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoOutAllRoomMember22_ptr);
        using CGuildRoomInfoRoomIn24_ptr = int (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int);
        using CGuildRoomInfoRoomIn24_clbk = int (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int, CGuildRoomInfoRoomIn24_ptr);
        using CGuildRoomInfoRoomOut26_ptr = int (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int);
        using CGuildRoomInfoRoomOut26_clbk = int (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int, CGuildRoomInfoRoomOut26_ptr);
        using CGuildRoomInfoRoomTimer28_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoRoomTimer28_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoRoomTimer28_ptr);
        using CGuildRoomInfoRoom_Initialize30_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoRoom_Initialize30_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoRoom_Initialize30_ptr);
        using CGuildRoomInfoSendDQS_RoomInsert32_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoSendDQS_RoomInsert32_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoSendDQS_RoomInsert32_ptr);
        using CGuildRoomInfoSendDQS_RoomUpdate34_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoSendDQS_RoomUpdate34_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoSendDQS_RoomUpdate34_ptr);
        using CGuildRoomInfoSendMsg_RoomTimeOver36_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoSendMsg_RoomTimeOver36_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoSendMsg_RoomTimeOver36_ptr);
        using CGuildRoomInfoSetPlayerOut38_ptr = bool (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int, int);
        using CGuildRoomInfoSetPlayerOut38_clbk = bool (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int, int, CGuildRoomInfoSetPlayerOut38_ptr);
        using CGuildRoomInfoSetRoom40_ptr = bool (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int);
        using CGuildRoomInfoSetRoom40_clbk = bool (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int, CGuildRoomInfoSetRoom40_ptr);
        using CGuildRoomInfoSetRoomMapInfo42_ptr = void (WINAPIV*)(struct CGuildRoomInfo*, struct CMapData*, uint16_t, char, char);
        using CGuildRoomInfoSetRoomMapInfo42_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, struct CMapData*, uint16_t, char, char, CGuildRoomInfoSetRoomMapInfo42_ptr);
        using CGuildRoomInfoSetRoomTime44_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoSetRoomTime44_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoSetRoomTime44_ptr);
        using CGuildRoomInfoSetRoomTime_Restore46_ptr = void (WINAPIV*)(struct CGuildRoomInfo*, struct tagTIMESTAMP_STRUCT);
        using CGuildRoomInfoSetRoomTime_Restore46_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, struct tagTIMESTAMP_STRUCT, CGuildRoomInfoSetRoomTime_Restore46_ptr);
        using CGuildRoomInfoSetRoom_Restore48_ptr = bool (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int, struct tagTIMESTAMP_STRUCT);
        using CGuildRoomInfoSetRoom_Restore48_clbk = bool (WINAPIV*)(struct CGuildRoomInfo*, int, unsigned int, struct tagTIMESTAMP_STRUCT, CGuildRoomInfoSetRoom_Restore48_ptr);
        using CGuildRoomInfoTimeOver50_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfoTimeOver50_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfoTimeOver50_ptr);
        
        using CGuildRoomInfodtor_CGuildRoomInfo56_ptr = void (WINAPIV*)(struct CGuildRoomInfo*);
        using CGuildRoomInfodtor_CGuildRoomInfo56_clbk = void (WINAPIV*)(struct CGuildRoomInfo*, CGuildRoomInfodtor_CGuildRoomInfo56_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

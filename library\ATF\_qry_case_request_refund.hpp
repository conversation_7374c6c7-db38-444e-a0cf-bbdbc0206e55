// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_request_refund
    {
        char byRace;
        unsigned __int16 wIndex;
        unsigned int dwAvatorSerial;
        unsigned __int64 dwRefund;
    public:
        _qry_case_request_refund(char byR, uint16_t wIdx, unsigned int dwS, uint64_t dwRef);
        void ctor__qry_case_request_refund(char byR, uint16_t wIdx, unsigned int dwS, uint64_t dwRef);
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_request_refund, 16>(), "_qry_case_request_refund");
END_ATF_NAMESPACE

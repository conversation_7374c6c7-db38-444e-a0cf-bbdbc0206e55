// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_OVERLAPPED.hpp>


START_ATF_NAMESPACE
    struct $E858F86FA0700251E2C79447730AE6C1
    {
        void *hIOPort;
        unsigned int dwNumberOfBytesTransferred;
        unsigned __int64 dwCompletionKey;
        _OVERLAPPED *lpOverlapped;
    };
END_ATF_NAMESPACE

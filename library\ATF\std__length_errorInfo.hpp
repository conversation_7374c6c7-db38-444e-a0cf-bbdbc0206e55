// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__length_error.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            
            using std__length_errorctor_length_error5_ptr = void (WINAPIV*)(struct std::length_error*, std::basic_string<char>*);
            using std__length_errorctor_length_error5_clbk = void (WINAPIV*)(struct std::length_error*, std::basic_string<char>*, std__length_errorctor_length_error5_ptr);
            
            using std__length_errorctor_length_error7_ptr = void (WINAPIV*)(struct std::length_error*, struct std::length_error*);
            using std__length_errorctor_length_error7_clbk = void (WINAPIV*)(struct std::length_error*, struct std::length_error*, std__length_errorctor_length_error7_ptr);
            
            using std__length_errordtor_length_error9_ptr = void (WINAPIV*)(struct std::length_error*);
            using std__length_errordtor_length_error9_clbk = void (WINAPIV*)(struct std::length_error*, std__length_errordtor_length_error9_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

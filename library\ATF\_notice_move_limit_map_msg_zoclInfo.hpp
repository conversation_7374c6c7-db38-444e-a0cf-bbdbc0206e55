// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_notice_move_limit_map_msg_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _notice_move_limit_map_msg_zoclctor__notice_move_limit_map_msg_zocl2_ptr = void (WINAPIV*)(struct _notice_move_limit_map_msg_zocl*);
        using _notice_move_limit_map_msg_zoclctor__notice_move_limit_map_msg_zocl2_clbk = void (WINAPIV*)(struct _notice_move_limit_map_msg_zocl*, _notice_move_limit_map_msg_zoclctor__notice_move_limit_map_msg_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

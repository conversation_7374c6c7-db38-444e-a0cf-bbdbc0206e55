#include <_personal_automine_uninstall_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_uninstall_zocl::_personal_automine_uninstall_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_uninstall_zocl*);
        (org_ptr(0x1402dde80L))(this);
    };
    void _personal_automine_uninstall_zocl::ctor__personal_automine_uninstall_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_uninstall_zocl*);
        (org_ptr(0x1402dde80L))(this);
    };
    int _personal_automine_uninstall_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_uninstall_zocl*);
        return (org_ptr(0x1402dded0L))(this);
    };
    
END_ATF_NAMESPACE

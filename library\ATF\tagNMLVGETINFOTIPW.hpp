// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMHDR.hpp>



START_ATF_NAMESPACE
    struct tagNMLVGETINFOTIPW
    {
        tagNMHDR hdr;
        unsigned int dwFlags;
        wchar_t *pszText;
        int cchTextMax;
        int iItem;
        int iSubItem;
        __int64 lParam;
    };
END_ATF_NAMESPACE

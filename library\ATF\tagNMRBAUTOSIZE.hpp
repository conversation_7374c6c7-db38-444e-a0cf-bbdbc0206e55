// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMHDR.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagNMRBAUTOSIZE
    {
        tagNMHDR hdr;
        int fChanged;
        tagRECT rcTarget;
        tagRECT rcActual;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

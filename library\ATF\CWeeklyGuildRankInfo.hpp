// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuild.hpp>
#include <CWeeklyGuildRankOwnerInfo.hpp>
#include <CWeeklyGuildRankRecord.hpp>
#include <_pvppoint_guild_rank_info.hpp>
#include <_weekly_guild_rank_result_zocl.hpp>
#include <_weeklyguildrank_owner_info.hpp>


START_ATF_NAMESPACE
    struct CWeeklyGuildRankInfo
    {
        bool m_bInit;
        bool m_NoDataPrev;
        bool m_bNoDataToday;
        unsigned int m_dwRecordCnt[3];
        CWeeklyGuildRankRecord **m_ppkRaceStartPos[3];
        CWeeklyGuildRankOwnerInfo m_kOwnerInfo[3][2];
        unsigned int m_dwMaxCnt;
        CWeeklyGuildRankRecord **m_ppkInfo;
        _weekly_guild_rank_result_zocl *m_pkSendList;
    public:
        CWeeklyGuildRankInfo();
        void ctor_CWeeklyGuildRankInfo();
        bool CheckEmpty(struct _pvppoint_guild_rank_info* pkInfo);
        void Clear();
        void ClearOwner();
        void ClearRank();
        int Find(char byRace, unsigned int dwGuildSerial);
        struct CGuild* GetCurOwnerGuild(char byRace, char byNth);
        struct CGuild* GetOwnerGuild(char byRace, char byNth);
        struct CGuild* GetPrevOwnerGuild(char byRace, char byNth);
        bool Init();
        bool IsNoDataPrev();
        bool IsNoDataToday();
        bool Load(struct _pvppoint_guild_rank_info* pkInfo, bool* bNoData);
        bool LoadOwner(struct _weeklyguildrank_owner_info* pkInfo);
        bool LoadPrev(struct _pvppoint_guild_rank_info* pkInfo);
        bool LoadToday(struct _pvppoint_guild_rank_info* pkInfo);
        void Send(unsigned int dwVer, int n, char byTabRace, char bySelfRace, unsigned int dwGuildSerial);
        void SetNoDataFlagToday();
        bool Update(struct _pvppoint_guild_rank_info* pkInfo);
        bool UpdateOwner(struct _weeklyguildrank_owner_info* pkInfo);
        ~CWeeklyGuildRankInfo();
        void dtor_CWeeklyGuildRankInfo();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRecordData.hpp>
#include <si_interpret.hpp>


START_ATF_NAMESPACE
    struct CSetItemType
    {
        int m_iEffectTypeCount;
        si_interpret **m_pEffectCountInfo;
    public:
        CSetItemType();
        void ctor_CSetItemType();
        void Class_Init();
        int GetEffectTypeCount();
        struct si_interpret* Getsi_interpret(int set_pos);
        bool SetItemType_Init(struct CRecordData* prd);
        bool SetItemType_UnInit();
        ~CSetItemType();
        void dtor_CSetItemType();
    };
END_ATF_NAMESPACE

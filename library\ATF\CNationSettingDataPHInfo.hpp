// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataPH.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataPHctor_CNationSettingDataPH2_ptr = void (WINAPIV*)(struct CNationSettingDataPH*);
        using CNationSettingDataPHctor_CNationSettingDataPH2_clbk = void (WINAPIV*)(struct CNationSettingDataPH*, CNationSettingDataPHctor_CNationSettingDataPH2_ptr);
        using CNationSettingDataPHCheckEnterWorldRequest4_ptr = bool (WINAPIV*)(struct CNationSettingDataPH*, int, char*);
        using CNationSettingDataPHCheckEnterWorldRequest4_clbk = bool (WINAPIV*)(struct CNationSettingDataPH*, int, char*, CNationSettingDataPHCheckEnterWorldRequest4_ptr);
        using CNationSettingDataPHCreateBilling6_ptr = struct CBilling* (WINAPIV*)(struct CNationSettingDataPH*);
        using CNationSettingDataPHCreateBilling6_clbk = struct CBilling* (WINAPIV*)(struct CNationSettingDataPH*, CNationSettingDataPHCreateBilling6_ptr);
        using CNationSettingDataPHCreateWorker8_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataPH*);
        using CNationSettingDataPHCreateWorker8_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataPH*, CNationSettingDataPHCreateWorker8_ptr);
        using CNationSettingDataPHGetCashItemPrice10_ptr = int (WINAPIV*)(struct CNationSettingDataPH*, struct _CashShop_str_fld*);
        using CNationSettingDataPHGetCashItemPrice10_clbk = int (WINAPIV*)(struct CNationSettingDataPH*, struct _CashShop_str_fld*, CNationSettingDataPHGetCashItemPrice10_ptr);
        using CNationSettingDataPHGetItemName12_ptr = char* (WINAPIV*)(struct CNationSettingDataPH*, struct _NameTxt_fld*);
        using CNationSettingDataPHGetItemName12_clbk = char* (WINAPIV*)(struct CNationSettingDataPH*, struct _NameTxt_fld*, CNationSettingDataPHGetItemName12_ptr);
        using CNationSettingDataPHInit14_ptr = int (WINAPIV*)(struct CNationSettingDataPH*);
        using CNationSettingDataPHInit14_clbk = int (WINAPIV*)(struct CNationSettingDataPH*, CNationSettingDataPHInit14_ptr);
        using CNationSettingDataPHIsNormalString16_ptr = bool (WINAPIV*)(struct CNationSettingDataPH*, char*);
        using CNationSettingDataPHIsNormalString16_clbk = bool (WINAPIV*)(struct CNationSettingDataPH*, char*, CNationSettingDataPHIsNormalString16_ptr);
        using CNationSettingDataPHIsNormalString18_ptr = bool (WINAPIV*)(struct CNationSettingDataPH*, wchar_t*);
        using CNationSettingDataPHIsNormalString18_clbk = bool (WINAPIV*)(struct CNationSettingDataPH*, wchar_t*, CNationSettingDataPHIsNormalString18_ptr);
        using CNationSettingDataPHLoop20_ptr = void (WINAPIV*)(struct CNationSettingDataPH*);
        using CNationSettingDataPHLoop20_clbk = void (WINAPIV*)(struct CNationSettingDataPH*, CNationSettingDataPHLoop20_ptr);
        using CNationSettingDataPHReadSystemPass22_ptr = bool (WINAPIV*)(struct CNationSettingDataPH*);
        using CNationSettingDataPHReadSystemPass22_clbk = bool (WINAPIV*)(struct CNationSettingDataPH*, CNationSettingDataPHReadSystemPass22_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

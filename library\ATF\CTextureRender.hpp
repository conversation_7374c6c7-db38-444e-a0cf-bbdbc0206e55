// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CTextureRender
    {
    public:
        int64_t BeginScene(uint32_t arg_0);
        void ClearFrameBuffer(uint32_t arg_0);
        int64_t CreateTexture(struct IDirect3DDevice8* arg_0, uint32_t arg_1, uint32_t arg_2, int arg_3);
        void EndScene();
        struct IDirect3DTexture8* GetD3DTexture();
        uint32_t GetXSize();
        uint32_t GetYSize();
        int64_t IsLoaded();
        void ReleaseTexture();
        void SetClearAtOnce(int arg_0);
        ~CTextureRender();
        int64_t dtor_CTextureRender();
    }
    ;
END_ATF_NAMESPACE

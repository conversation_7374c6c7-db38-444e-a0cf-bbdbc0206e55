// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerES.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerESctor_CCashDbWorkerES2_ptr = void (WINAPIV*)(struct CCashDbWorkerES*);
        using CCashDbWorkerESctor_CCashDbWorkerES2_clbk = void (WINAPIV*)(struct CCashDbWorkerES*, CCashDbWorkerESctor_CCashDbWorkerES2_ptr);
        using CCashDbWorkerESGetUseCashQueryStr4_ptr = void (WINAPIV*)(struct CCashDbWorkerES*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDbWorkerESGetUseCashQueryStr4_clbk = void (WINAPIV*)(struct CCashDbWorkerES*, struct _param_cash_update*, int, char*, uint64_t, CCashDbWorkerESGetUseCashQueryStr4_ptr);
        
        using CCashDbWorkerESdtor_CCashDbWorkerES9_ptr = void (WINAPIV*)(struct CCashDbWorkerES*);
        using CCashDbWorkerESdtor_CCashDbWorkerES9_clbk = void (WINAPIV*)(struct CCashDbWorkerES*, CCashDbWorkerESdtor_CCashDbWorkerES9_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

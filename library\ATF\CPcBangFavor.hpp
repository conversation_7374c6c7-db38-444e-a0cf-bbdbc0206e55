// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPcBangFavorVtbl.hpp>
#include <CPlayer.hpp>
#include <CRecordData.hpp>
#include <_AVATOR_DATA.hpp>


START_ATF_NAMESPACE
    struct CPcBangFavor
    {
        CPcBangFavorVtbl *vfptr;
        unsigned int m_PcRoomIndex;
        int m_bEnable;
        CRecordData m_tblPcRoomData;
    public:
        CPcBangFavor();
        void ctor_CPcBangFavor();
        unsigned int ClassCodePasing(struct _AVATOR_DATA* pData, struct CPlayer* pOne);
        int Initialzie();
        static struct CPcBangFavor* Instance();
        int IsEnable();
        int LoadPcBangData();
        void PcBangDeleteItem(struct CPlayer* pOne);
        bool PcBangGiveItem(struct CPlayer* pOne, unsigned int dwRecIndex, char* bySeletItemIndex, int nSelectCount);
        ~CPcBangFavor();
        void dtor_CPcBangFavor();
    };
END_ATF_NAMESPACE

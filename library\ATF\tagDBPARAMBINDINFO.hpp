// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct tagDBPARAMBINDINFO
    {
        wchar_t *pwszDataSourceType;
        wchar_t *pwszName;
        unsigned __int64 ulParamSize;
        unsigned int dwFlags;
        char bPrecision;
        char bScale;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

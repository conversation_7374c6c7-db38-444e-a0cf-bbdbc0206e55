// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _JOB_INFO_1A
    {
        unsigned int JobId;
        char *pPrinterName;
        char *pMachineName;
        char *pUserName;
        char *pDocument;
        char *pDatatype;
        char *pStatus;
        unsigned int Status;
        unsigned int Priority;
        unsigned int Position;
        unsigned int TotalPages;
        unsigned int PagesPrinted;
        _SYSTEMTIME Submitted;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

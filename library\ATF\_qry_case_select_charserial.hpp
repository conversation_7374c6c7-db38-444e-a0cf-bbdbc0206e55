// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_select_charserial
    {
        char byRace;
        char byType;
        unsigned int dwAvatorSerial;
        char wszCharName[17];
        char wszContent[1280];
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_select_charserial, 1308>(), "_qry_case_select_charserial");
END_ATF_NAMESPACE

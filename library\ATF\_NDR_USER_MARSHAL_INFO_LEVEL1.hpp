// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IRpcChannelBuffer.hpp>


START_ATF_NAMESPACE
    struct _NDR_USER_MARSHAL_INFO_LEVEL1
    {
        void *Buffer;
        unsigned int BufferSize;
        void *(WINAPIV *pfnAllocate)(unsigned __int64);
        void (WINAPIV *pfnFree)(void *);
        IRpcChannelBuffer *pRpcChannelBuffer;
        unsigned __int64 Reserved[5];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CUnmannedTraderClassInfoVtbl
    {
        bool (WINAPIV *LoadXML)(struct CUnmannedTraderClassInfo *_this, struct TiXmlElement *, struct CLogFile *, unsigned int);
        bool (WINAPIV *GetGroupID)(struct CUnmannedTraderClassInfo *_this, char, unsigned __int16, char *, char *);
        bool (WINAPIV *GetGroupID2)(struct CUnmannedTraderClassInfo *_this, char, unsigned __int16, char *);
        bool (WINAPIV *IsExistGroupID)(struct CUnmannedTraderClassInfo *_this, char, char);
        struct CUnmannedTraderClassInfo *(WINAPIV *Create)(struct CUnmannedTraderClassInfo *_this, unsigned int);
    };
END_ATF_NAMESPACE

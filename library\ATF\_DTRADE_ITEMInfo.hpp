// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DTRADE_ITEM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _DTRADE_ITEMReleaseData2_ptr = void (WINAPIV*)(struct _DTRADE_ITEM*);
        using _DTRADE_ITEMReleaseData2_clbk = void (WINAPIV*)(struct _DTRADE_ITEM*, _DTRADE_ITEMReleaseData2_ptr);
        using _DTRADE_ITEMSetData4_ptr = void (WINAPIV*)(struct _DTRADE_ITEM*, char, unsigned int, char);
        using _DTRADE_ITEMSetData4_clbk = void (WINAPIV*)(struct _DTRADE_ITEM*, char, unsigned int, char, _DTRADE_ITEMSetData4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

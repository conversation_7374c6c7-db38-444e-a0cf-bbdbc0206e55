// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderTradeInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CUnmannedTraderTradeInfoAddIncome2_ptr = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, unsigned int);
        using CUnmannedTraderTradeInfoAddIncome2_clbk = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, unsigned int, CUnmannedTraderTradeInfoAddIncome2_ptr);
        
        using CUnmannedTraderTradeInfoctor_CUnmannedTraderTradeInfo4_ptr = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderTradeInfoctor_CUnmannedTraderTradeInfo4_clbk = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, CUnmannedTraderTradeInfoctor_CUnmannedTraderTradeInfo4_ptr);
        using CUnmannedTraderTradeInfoInit6_ptr = bool (WINAPIV*)(struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderTradeInfoInit6_clbk = bool (WINAPIV*)(struct CUnmannedTraderTradeInfo*, CUnmannedTraderTradeInfoInit6_ptr);
        using CUnmannedTraderTradeInfoLoadINI8_ptr = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderTradeInfoLoadINI8_clbk = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, CUnmannedTraderTradeInfoLoadINI8_ptr);
        using CUnmannedTraderTradeInfoLoop10_ptr = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderTradeInfoLoop10_clbk = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, CUnmannedTraderTradeInfoLoop10_ptr);
        using CUnmannedTraderTradeInfoNotifyIncome12_ptr = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, char, uint16_t);
        using CUnmannedTraderTradeInfoNotifyIncome12_clbk = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, char, uint16_t, CUnmannedTraderTradeInfoNotifyIncome12_ptr);
        using CUnmannedTraderTradeInfoNotifyIncome14_ptr = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderTradeInfoNotifyIncome14_clbk = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, CUnmannedTraderTradeInfoNotifyIncome14_ptr);
        using CUnmannedTraderTradeInfoSaveINI16_ptr = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderTradeInfoSaveINI16_clbk = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, CUnmannedTraderTradeInfoSaveINI16_ptr);
        using CUnmannedTraderTradeInfoUpdateIncome18_ptr = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderTradeInfoUpdateIncome18_clbk = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, CUnmannedTraderTradeInfoUpdateIncome18_ptr);
        
        using CUnmannedTraderTradeInfodtor_CUnmannedTraderTradeInfo20_ptr = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderTradeInfodtor_CUnmannedTraderTradeInfo20_clbk = void (WINAPIV*)(struct CUnmannedTraderTradeInfo*, CUnmannedTraderTradeInfodtor_CUnmannedTraderTradeInfo20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _character_disconnect_result_wrac
    {
        unsigned __int16 wClientIndex;
        char byResult;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_character_disconnect_result_wrac, 3>(), "_character_disconnect_result_wrac");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_buy_store_success_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _buy_store_success_zoclctor__buy_store_success_zocl2_ptr = void (WINAPIV*)(struct _buy_store_success_zocl*);
        using _buy_store_success_zoclctor__buy_store_success_zocl2_clbk = void (WINAPIV*)(struct _buy_store_success_zocl*, _buy_store_success_zoclctor__buy_store_success_zocl2_ptr);
        using _buy_store_success_zoclsize4_ptr = int (WINAPIV*)(struct _buy_store_success_zocl*);
        using _buy_store_success_zoclsize4_clbk = int (WINAPIV*)(struct _buy_store_success_zocl*, _buy_store_success_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

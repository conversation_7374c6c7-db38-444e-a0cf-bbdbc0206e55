// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <_PVP_RANK_DATA.hpp>
#include <_PVP_RANK_PACKED_DATA.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CPvpUserRankingInfo
    {
        std::vector<_PVP_RANK_DATA *,std::allocator<_PVP_RANK_DATA *> > m_vecPvpRankDataCurrent;
        std::vector<_PVP_RANK_DATA *,std::allocator<_PVP_RANK_DATA *> > m_vecPvpRankDataTomorrow;
        std::vector<_PVP_RANK_PACKED_DATA *,std::allocator<_PVP_RANK_PACKED_DATA *> > m_vecPackedRankData;
        char m_byPvpRankDataVersion;
        std::vector<unsigned long *,std::allocator<unsigned long *> > m_dwUpdateRaceBossSerial;
        std::vector<unsigned long *,std::allocator<unsigned long *> > m_dwCurrentRaceBossSerial;
    public:
        CPvpUserRankingInfo();
        void ctor_CPvpUserRankingInfo();
        void ClearTomorrowPvpRankData();
        void DoDayChangedWork(struct CLogFile* pkLogger);
        unsigned int FindRank(char byRaceCode, unsigned int dwAvatorSerial);
        void FlipPvPRankTop();
        char GetBossType(char byRaceCode, unsigned int dwSerial);
        struct _PVP_RANK_DATA* GetCurrentPvpRankData(char byRace, char byNth);
        unsigned int GetCurrentRaceBossSerial(char byRace, char byNth);
        char GetPvpRankDataVersion();
        struct _PVP_RANK_PACKED_DATA* GetRankPackedData(char byRace, char byPage);
        struct _PVP_RANK_DATA* GetTomorrowPvpRankData(char byRace, char byNth);
        void IncreaseVesion();
        bool IsCurrentRaceBossGroup(char byRace, unsigned int dwSerial);
        bool IsRaceViceBoss(char byRace, unsigned int dwSerial);
        bool LoadPvpRank(char* szDate);
        void PvpRankDataPacking(struct CLogFile* pkLogger);
        void PvpRankListRequest(uint16_t wIndex, char byRace, char byVersion, char byPage);
        void SendMsg_PvpRankListData(uint16_t wIndex, char byRace, char byVersion, char byPage);
        void SendMsg_PvpRankListNodata(uint16_t wIndex, char byRace, char byPage, char byRet);
        void SetCurrentRaceBossSerial(char byRace, char byNth, unsigned int dwSerial);
        void SetUpdateRaceBossSerial(char byRace, char byNth, unsigned int dwSerial);
        char UpdateRaceRankStep1(char* szData);
        char UpdateRaceRankStep10(char* szData);
        char UpdateRaceRankStep2(char* szData);
        char UpdateRaceRankStep3(char* szData);
        char UpdateRaceRankStep4(char* szData);
        char UpdateRaceRankStep5(char* szData);
        char UpdateRaceRankStep6(char* szData);
        char UpdateRaceRankStep7(char* szData);
        char UpdateRaceRankStep8(char* szData);
        char UpdateRaceRankStep9(char* szData);
        bool assign();
        ~CPvpUserRankingInfo();
        void dtor_CPvpUserRankingInfo();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _d_trade_unit_info_inform_zocl
    {
        char byTradeSlotIndex;
        char byFrame;
         unsigned int dwGauge;
        char byPart[6];
        unsigned int dwBullet[2];
        unsigned int dwSpare[8];
        int nDebtFee;
    };
END_ATF_NAMESPACE

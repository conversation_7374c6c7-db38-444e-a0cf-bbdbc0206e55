// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $419D7C85590CD0322CD6E18C3D1AEEE1
    {
      cde_type_disable = 0x0,
      cde_type_wait = 0x1,
      cde_type_start = 0x2,
      cde_type_30_before = 0x3,
      cde_type_5_before = 0x4,
      cde_type_end = 0x5,
      cash_discount_event_type_error = 0x6,
      cde_type_expire = 0x7,
      cde_type_all = 0x8,
    };
END_ATF_NAMESPACE

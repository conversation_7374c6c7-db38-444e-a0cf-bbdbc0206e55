// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct _AppBarData
    {
        unsigned int cbSize;
        HWND__ *hWnd;
        unsigned int uCallbackMessage;
        unsigned int uEdge;
        tagRECT rc;
        __int64 lParam;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$5791AEE384C27F75C9DD45F2F53B2F06.hpp>


START_ATF_NAMESPACE
    struct _RPC_SECURITY_QOS_V2_A
    {
        unsigned int Version;
        unsigned int Capabilities;
        unsigned int IdentityTracking;
        unsigned int ImpersonationType;
        unsigned int AdditionalSecurityInfoType;
        $5791AEE384C27F75C9DD45F2F53B2F06 u;
    };
END_ATF_NAMESPACE

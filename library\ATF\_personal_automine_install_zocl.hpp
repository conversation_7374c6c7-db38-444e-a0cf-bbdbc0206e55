// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _personal_automine_install_zocl
    {
        unsigned int dwObjSerial;
        unsigned __int16 wObjIndex;
        unsigned int dwOwnerSerial;
        unsigned __int16 wItemTblIndex;
        unsigned __int16 wItemSerial;
        float fPos[3];
        char byFilledSlotCnt;
    public:
        _personal_automine_install_zocl();
        void ctor__personal_automine_install_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_personal_automine_install_zocl, 27>(), "_personal_automine_install_zocl");
END_ATF_NAMESPACE

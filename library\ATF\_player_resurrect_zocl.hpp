// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _player_resurrect_zocl
    {
        char byRet;
        char byLevel;
        unsigned __int16 wCurHP;
        unsigned __int16 wCurFP;
        unsigned __int16 wCurST;
        bool bQuickPotion;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

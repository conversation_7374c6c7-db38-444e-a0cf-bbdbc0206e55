// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _NETINFOSTRUCT
    {
        unsigned int cbStructure;
        unsigned int dwProviderVersion;
        unsigned int dwStatus;
        unsigned int dwCharacteristics;
        unsigned __int64 dwHandle;
        unsigned __int16 wNetType;
        unsigned int dwPrinters;
        unsigned int dwDrives;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

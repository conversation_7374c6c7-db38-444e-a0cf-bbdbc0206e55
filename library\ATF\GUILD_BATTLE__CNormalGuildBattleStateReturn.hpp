// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleState.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CNormalGuildBattleStateReturn : CNormalGuildBattleState
        {
        public:
            CNormalGuildBattleStateReturn();
            void ctor_CNormalGuildBattleStateReturn();
            int Fin(struct CNormalGuildBattle* pkBattle);
            struct ATL::CTimeSpan* GetTerm(struct ATL::CTimeSpan* result);
            ~CNormalGuildBattleStateReturn();
            void dtor_CNormalGuildBattleStateReturn();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CNormalGuildBattleStateReturn, 8>(), "GUILD_BATTLE::CNormalGuildBattleStateReturn");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

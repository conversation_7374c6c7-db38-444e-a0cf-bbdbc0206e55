// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_COAUTHINFO.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _COSERVERINFO
    {
        unsigned int dwReserved1;
        wchar_t *pwszName;
        _COAUTHINFO *pAuthInfo;
        unsigned int dwReserved2;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

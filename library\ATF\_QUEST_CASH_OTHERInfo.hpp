// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_QUEST_CASH_OTHER.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _QUEST_CASH_OTHERctor__QUEST_CASH_OTHER2_ptr = void (WINAPIV*)(struct _QUEST_CASH_OTHER*);
        using _QUEST_CASH_OTHERctor__QUEST_CASH_OTHER2_clbk = void (WINAPIV*)(struct _QUEST_CASH_OTHER*, _QUEST_CASH_OTHERctor__QUEST_CASH_OTHER2_ptr);
        using _QUEST_CASH_OTHERinit4_ptr = void (WINAPIV*)(struct _QUEST_CASH_OTHER*);
        using _QUEST_CASH_OTHERinit4_clbk = void (WINAPIV*)(struct _QUEST_CASH_OTHER*, _QUEST_CASH_OTHERinit4_ptr);
        using _QUEST_CASH_OTHERisLoaded6_ptr = bool (WINAPIV*)(struct _QUEST_CASH_OTHER*);
        using _QUEST_CASH_OTHERisLoaded6_clbk = bool (WINAPIV*)(struct _QUEST_CASH_OTHER*, _QUEST_CASH_OTHERisLoaded6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

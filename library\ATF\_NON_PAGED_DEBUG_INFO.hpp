// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _NON_PAGED_DEBUG_INFO
    {
        unsigned __int16 Signature;
        unsigned __int16 Flags;
        unsigned int Size;
        unsigned __int16 Machine;
        unsigned __int16 Characteristics;
        unsigned int TimeDateStamp;
        unsigned int CheckSum;
        unsigned int SizeOfImage;
        unsigned __int64 ImageBase;
    };
END_ATF_NAMESPACE

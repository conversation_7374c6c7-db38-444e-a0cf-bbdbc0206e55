// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _guild_info_update_inform_zocl
    {
        unsigned int dwGuildSerial;
        char byGrade;
        unsigned int dwEmblemBack;
        unsigned int dwEmblemMark;
        unsigned int dwTotWin;
        unsigned int dwTotDraw;
        unsigned int dwTotLose;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

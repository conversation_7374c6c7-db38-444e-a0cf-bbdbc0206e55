// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataTH.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataTHctor_CNationSettingDataTH2_ptr = void (WINAPIV*)(struct CNationSettingDataTH*);
        using CNationSettingDataTHctor_CNationSettingDataTH2_clbk = void (WINAPIV*)(struct CNationSettingDataTH*, CNationSettingDataTHctor_CNationSettingDataTH2_ptr);
        using CNationSettingDataTHCreateWorker4_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataTH*);
        using CNationSettingDataTHCreateWorker4_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataTH*, CNationSettingDataTHCreateWorker4_ptr);
        using CNationSettingDataTHGetCashItemPrice6_ptr = int (WINAPIV*)(struct CNationSettingDataTH*, struct _CashShop_str_fld*);
        using CNationSettingDataTHGetCashItemPrice6_clbk = int (WINAPIV*)(struct CNationSettingDataTH*, struct _CashShop_str_fld*, CNationSettingDataTHGetCashItemPrice6_ptr);
        using CNationSettingDataTHGetItemName8_ptr = char* (WINAPIV*)(struct CNationSettingDataTH*, struct _NameTxt_fld*);
        using CNationSettingDataTHGetItemName8_clbk = char* (WINAPIV*)(struct CNationSettingDataTH*, struct _NameTxt_fld*, CNationSettingDataTHGetItemName8_ptr);
        using CNationSettingDataTHInit10_ptr = int (WINAPIV*)(struct CNationSettingDataTH*);
        using CNationSettingDataTHInit10_clbk = int (WINAPIV*)(struct CNationSettingDataTH*, CNationSettingDataTHInit10_ptr);
        using CNationSettingDataTHLoop12_ptr = void (WINAPIV*)(struct CNationSettingDataTH*);
        using CNationSettingDataTHLoop12_clbk = void (WINAPIV*)(struct CNationSettingDataTH*, CNationSettingDataTHLoop12_ptr);
        using CNationSettingDataTHReadSystemPass14_ptr = bool (WINAPIV*)(struct CNationSettingDataTH*);
        using CNationSettingDataTHReadSystemPass14_clbk = bool (WINAPIV*)(struct CNationSettingDataTH*, CNationSettingDataTHReadSystemPass14_ptr);
        using CNationSettingDataTHSetUnitPassiveValue16_ptr = void (WINAPIV*)(struct CNationSettingDataTH*, float*);
        using CNationSettingDataTHSetUnitPassiveValue16_clbk = void (WINAPIV*)(struct CNationSettingDataTH*, float*, CNationSettingDataTHSetUnitPassiveValue16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderDivisionInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderDivisionInfoctor_CUnmannedTraderDivisionInfo2_ptr = void (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, unsigned int, char*);
        using CUnmannedTraderDivisionInfoctor_CUnmannedTraderDivisionInfo2_clbk = void (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, unsigned int, char*, CUnmannedTraderDivisionInfoctor_CUnmannedTraderDivisionInfo2_ptr);
        using CUnmannedTraderDivisionInfoCleanUp4_ptr = void (WINAPIV*)(struct CUnmannedTraderDivisionInfo*);
        using CUnmannedTraderDivisionInfoCleanUp4_clbk = void (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, CUnmannedTraderDivisionInfoCleanUp4_ptr);
        using CUnmannedTraderDivisionInfoCopy6_ptr = struct CUnmannedTraderDivisionInfo* (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, struct CUnmannedTraderDivisionInfo*);
        using CUnmannedTraderDivisionInfoCopy6_clbk = struct CUnmannedTraderDivisionInfo* (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, struct CUnmannedTraderDivisionInfo*, CUnmannedTraderDivisionInfoCopy6_ptr);
        using CUnmannedTraderDivisionInfoFindSortType8_ptr = struct CUnmannedTraderSortType* (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, unsigned int);
        using CUnmannedTraderDivisionInfoFindSortType8_clbk = struct CUnmannedTraderSortType* (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, unsigned int, CUnmannedTraderDivisionInfoFindSortType8_ptr);
        using CUnmannedTraderDivisionInfoGetGroupID10_ptr = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, char, uint16_t, char*, char*);
        using CUnmannedTraderDivisionInfoGetGroupID10_clbk = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, char, uint16_t, char*, char*, CUnmannedTraderDivisionInfoGetGroupID10_ptr);
        using CUnmannedTraderDivisionInfoGetGroupID12_ptr = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, char, uint16_t, char*, char*, char*, unsigned int*);
        using CUnmannedTraderDivisionInfoGetGroupID12_clbk = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, char, uint16_t, char*, char*, char*, unsigned int*, CUnmannedTraderDivisionInfoGetGroupID12_ptr);
        using CUnmannedTraderDivisionInfoGetID14_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderDivisionInfo*);
        using CUnmannedTraderDivisionInfoGetID14_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, CUnmannedTraderDivisionInfoGetID14_ptr);
        using CUnmannedTraderDivisionInfoGetMaxClassCnt16_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderDivisionInfo*);
        using CUnmannedTraderDivisionInfoGetMaxClassCnt16_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, CUnmannedTraderDivisionInfoGetMaxClassCnt16_ptr);
        using CUnmannedTraderDivisionInfoGetSize18_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderDivisionInfo*);
        using CUnmannedTraderDivisionInfoGetSize18_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, CUnmannedTraderDivisionInfoGetSize18_ptr);
        using CUnmannedTraderDivisionInfoGetSortType20_ptr = struct CUnmannedTraderSortType* (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, char);
        using CUnmannedTraderDivisionInfoGetSortType20_clbk = struct CUnmannedTraderSortType* (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, char, CUnmannedTraderDivisionInfoGetSortType20_ptr);
        using CUnmannedTraderDivisionInfoIsExistGroupID22_ptr = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, char, char, char, char, unsigned int*);
        using CUnmannedTraderDivisionInfoIsExistGroupID22_clbk = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, char, char, char, char, unsigned int*, CUnmannedTraderDivisionInfoIsExistGroupID22_ptr);
        using CUnmannedTraderDivisionInfoIsExistSortTypeID24_ptr = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, unsigned int);
        using CUnmannedTraderDivisionInfoIsExistSortTypeID24_clbk = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, unsigned int, CUnmannedTraderDivisionInfoIsExistSortTypeID24_ptr);
        using CUnmannedTraderDivisionInfoIsValidID26_ptr = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, unsigned int);
        using CUnmannedTraderDivisionInfoIsValidID26_clbk = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, unsigned int, CUnmannedTraderDivisionInfoIsValidID26_ptr);
        using CUnmannedTraderDivisionInfoLoadXML28_ptr = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, struct TiXmlElement*, struct CLogFile*);
        using CUnmannedTraderDivisionInfoLoadXML28_clbk = bool (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, struct TiXmlElement*, struct CLogFile*, CUnmannedTraderDivisionInfoLoadXML28_ptr);
        
        using CUnmannedTraderDivisionInfodtor_CUnmannedTraderDivisionInfo32_ptr = void (WINAPIV*)(struct CUnmannedTraderDivisionInfo*);
        using CUnmannedTraderDivisionInfodtor_CUnmannedTraderDivisionInfo32_clbk = void (WINAPIV*)(struct CUnmannedTraderDivisionInfo*, CUnmannedTraderDivisionInfodtor_CUnmannedTraderDivisionInfo32_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

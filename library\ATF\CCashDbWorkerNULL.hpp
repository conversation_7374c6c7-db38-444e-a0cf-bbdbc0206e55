// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CashDbWorker.hpp>
#include <Task.hpp>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    struct  CCashDbWorkerNULL : CashDbWorker
    {
    public:
        CCashDbWorkerNULL();
        void ctor_CCashDbWorkerNULL();
        void CompleteWork();
        int ConvertErrorCode(char state);
        void DoWork();
        void GetUseCashQueryStr(struct _param_cash_update* rParam, int nIdx, char* wszQuery, uint64_t tBufferSize);
        bool Initialize();
        void Release();
        void _all_rollback(struct _param_cash_update* psheet);
        bool _init_database();
        int _wait_tsk_cash_buy_dblog(struct Task* pkTsk);
        int _wait_tsk_cash_rollback(struct Task* pkTsk);
        int _wait_tsk_cash_select(struct Task* pkTsk);
        int _wait_tsk_cash_update(struct Task* pkTsk);
        int _wait_tst_cash_total_selling_select(struct Task* pkTsk);
        ~CCashDbWorkerNULL();
        void dtor_CCashDbWorkerNULL();
    };    
    static_assert(ATF::checkSize<CCashDbWorkerNULL, 552>(), "CCashDbWorkerNULL");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuildList.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CGuildListAddList2_ptr = void (WINAPIV*)(struct CGuildList*, char, char, char*, char*);
        using CGuildListAddList2_clbk = void (WINAPIV*)(struct CGuildList*, char, char, char*, char*, CGuildListAddList2_ptr);
        
        using CGuildListctor_CGuildList4_ptr = void (WINAPIV*)(struct CGuildList*);
        using CGuildListctor_CGuildList4_clbk = void (WINAPIV*)(struct CGuildList*, CGuildListctor_CGuildList4_ptr);
        using CGuildListInit6_ptr = bool (WINAPIV*)(struct CGuildList*);
        using CGuildListInit6_clbk = bool (WINAPIV*)(struct CGuildList*, CGuildListInit6_ptr);
        using CGuildListSendList8_ptr = void (WINAPIV*)(struct CGuildList*, uint16_t, char, char);
        using CGuildListSendList8_clbk = void (WINAPIV*)(struct CGuildList*, uint16_t, char, char, CGuildListSendList8_ptr);
        using CGuildListSetGrade10_ptr = void (WINAPIV*)(struct CGuildList*, char, char*, char);
        using CGuildListSetGrade10_clbk = void (WINAPIV*)(struct CGuildList*, char, char*, char, CGuildListSetGrade10_ptr);
        using CGuildListSetGuildMaster12_ptr = void (WINAPIV*)(struct CGuildList*, char, char*, char*);
        using CGuildListSetGuildMaster12_clbk = void (WINAPIV*)(struct CGuildList*, char, char*, char*, CGuildListSetGuildMaster12_ptr);
        
        using CGuildListdtor_CGuildList17_ptr = void (WINAPIV*)(struct CGuildList*);
        using CGuildListdtor_CGuildList17_clbk = void (WINAPIV*)(struct CGuildList*, CGuildListdtor_CGuildList17_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

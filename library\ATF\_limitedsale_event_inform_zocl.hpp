// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _limitedsale_event_inform_zocl
    {
        char byTableCode;
        unsigned int dwIndex;
        unsigned __int16 wNum;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_limitedsale_event_inform_zocl, 7>(), "_limitedsale_event_inform_zocl");
END_ATF_NAMESPACE

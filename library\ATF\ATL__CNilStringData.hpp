// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringData.hpp>



START_ATF_NAMESPACE
    namespace ATL
    {
        #pragma pack(push, 8)
        struct  CNilStringData : CStringData
        {
            wchar_t achNil[2];
        };
        #pragma pack(pop)
    }; // end namespace ATL
END_ATF_NAMESPACE

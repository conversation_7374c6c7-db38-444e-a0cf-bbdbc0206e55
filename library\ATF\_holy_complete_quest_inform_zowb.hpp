// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _holy_complete_quest_inform_zowb
    {
        int nWorldCode;
        char szWorldName[33];
        char wszRaceBossName[3][17];
        char byMasterRaceCode;
        char byDestoryRaceCode;
        char wszMasterName[17];
        char by<PERSON><PERSON>sHour;
        char byChaosMin;
        char byNumOfTime;
        char byMasterLv;
        char szMasterClass[5];
        char wszMasterRaceSubBoss[5][17];
        char byStartHour;
        char byStartMin;
        unsigned __int16 wStartYear;
        char byStartMonth;
        char byStartDay;
        char byEndHour;
        char byEndMin;
        unsigned __int16 wEndYear;
        char byEnd<PERSON><PERSON>h;
        char byEndDay;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

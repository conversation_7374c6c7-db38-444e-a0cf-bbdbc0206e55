// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 16)
        template<>
        struct basic_istream<char,char_traits<char> >
        {
            template<>
            struct _Sentry_base
            {
                basic_istream<char,char_traits<char> > *_Myistr;
            };
            template<>
            struct  sentry : _Sentry_base
            {
                bool _Ok;
            };
            BYTE gap0[8];
            __int64 _Chcount;
            BYTE gap10[96];
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 16)
        template<>
        struct basic_istream<wchar_t,char_traits<wchar_t> >
        {
            template<>
            struct _Sentry_base
            {
                basic_istream<wchar_t,char_traits<wchar_t> > *_Myistr;
            };
            template<>
            struct  sentry : _Sentry_base
            {
                bool _Ok;
            };
            BYTE gap0[8];
            __int64 _Chcount;
            BYTE gap10[96];
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE

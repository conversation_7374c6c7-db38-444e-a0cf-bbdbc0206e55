// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$C2F6557BF5244D9840704C1D21365DBC.hpp>


START_ATF_NAMESPACE
    struct MiningTicket
    {
        struct _AuthKeyTicket
        {
            $C2F6557BF5244D9840704C1D21365DBC ___u0;
        public:
            void Init();
            void Set(unsigned int uiSrc);
            void Set(uint16_t byYear, char byMonth, char byDay, char byHour, char byNumofTime);
        };
        _AuthKeyTicket m_dwTakeLastMentalTicket;
        _AuthKeyTicket m_dwTakeLastCriTicket;
    public:
        int AuthLastCriTicket(uint16_t byCurrentYear, char byCurrentMonth, char byCurrentDay, char byCurrentHour, char byNumOfTime);
        int AuthLastMentalTicket(uint16_t byCur<PERSON><PERSON>ear, char byCurrent<PERSON><PERSON>h, char byCurrentDay, char byCurrentHour, char byNumOfTime);
        unsigned int GetLastCriTicket();
        unsigned int GetLastMentalTicket();
        void Init();
        MiningTicket();
        void ctor_MiningTicket();
        void SetLastCriTicket(unsigned int uiCriTicket);
        void SetLastCriTicket(uint16_t byCurrentYear, char byCurrentMonth, char byCurrentDay, char byCurrentHour, char byNumOfTime);
        void SetLastMentalTicket(unsigned int uiMentalTicket);
        void SetLastMentalTicket(uint16_t byCurrentYear, char byCurrentMonth, char byCurrentDay, char byCurrentHour, char byNumOfTime);
    };    
    static_assert(ATF::checkSize<MiningTicket, 8>(), "MiningTicket");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$0CADDF7DF877E1D606D32956C3462939.hpp>
#include <$D1843FBD53FBCF9FD8D9075CB6B13767.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  $0E9F33587488199CAADA63F425077381
    {
        unsigned int TagIndex;
        $D1843FBD53FBCF9FD8D9075CB6B13767 Misc;
        $0CADDF7DF877E1D606D32956C3462939 FcnAry;
        unsigned __int16 TvIndex;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

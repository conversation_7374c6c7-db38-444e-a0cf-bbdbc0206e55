// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct Atmosphere
    {
    public:
        void CalculateScatteringConstants();
        void Dump2(struct _iobuf* arg_0);
        struct D3DXVECTOR3* GetBetaDashMie(short retstr);
        struct D3DXVECTOR3* GetBetaDashRayleigh(short retstr);
        struct D3DXVECTOR3* GetBetaMie(short retstr);
        struct D3DXVECTOR3* GetBetaRayleigh(short retstr);
        void Interpolate(struct Atmosphere* arg_0, struct Atmosphere* arg_1, float arg_2);
        void Read2(struct _iobuf* arg_0);
        ~Atmosphere();
        int64_t dtor_Atmosphere();
    }
    ;
END_ATF_NAMESPACE

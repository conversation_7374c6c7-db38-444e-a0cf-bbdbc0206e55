// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CIndexListVtbl.hpp>
#include <CSyncCS.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CIndexList
    {
        struct _index_node
        {
            bool m_bLoad;
            unsigned int m_dwIndex;
            unsigned int m_dwInfoDataSize;
            char *m_pInfo;
            _index_node *m_pPrev;
            _index_node *m_pNext;
        public:
            bool AllocInfo(unsigned int nSize);
            _index_node();
            void ctor__index_node();
            ~_index_node();
            void dtor__index_node();
        };
        CIndexListVtbl *vfptr;
        _index_node m_Head;
        _index_node m_Tail;
        _index_node m_BufHead;
        _index_node m_BufTail;
        _index_node *m_pBufNode;
        CSyncCS m_csList;
        unsigned int m_dwCount;
        unsigned int m_dwBufCount;
        unsigned int m_dwMaxBufNum;
    public:
        CIndexList();
        void ctor_CIndexList();
        bool CopyFront(unsigned int* pdwOutIndex, char* pInfoData);
        bool FindNode(unsigned int dwIndex, char* pInfoData);
        struct _index_node* GetAllNode(unsigned int* pdwMaxNodeNum);
        int GetSize();
        bool IsInList(unsigned int dwIndex, char* pInfoData);
        bool IsSetting();
        bool PopNode_Front(unsigned int* pdwOutIndex, char* pInfoData);
        bool PushNode_Back(unsigned int dwIndex, char* pInfoData);
        void ResetList();
        bool SetList(unsigned int dwMaxBufNum, unsigned int nInfoSize, bool bUseMultiThread);
        ~CIndexList();
        void dtor_CIndexList();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CIndexList, 248>(), "CIndexList");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Iterator_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ranit_base<wchar_t,__int64,wchar_t const *,wchar_t const &,_Iterator_base> : _Iterator_base
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std___Iterator_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ranit_base<char,__int64,char const *,char const &,_Iterator_base> : _Iterator_base
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct  CCheckSum
    {
    public:
        CCheckSum();
        void ctor_CCheckSum();
        unsigned int DecodeValue(char byIndex, unsigned int dwSerial, unsigned int dwValue);
        unsigned int EncodeValue(char byIndex, unsigned int dwSerial, unsigned int dwValue);
        bool Init();
        ~CCheckSum();
        void dtor_CCheckSum();
    };
END_ATF_NAMESPACE

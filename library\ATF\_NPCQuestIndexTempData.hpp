// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _NPCQuestIndexTempData
    {
        struct _IndexData
        {
            unsigned int dwQuestHappenIndex;
            unsigned int dwQuestIndex;
        };
        int nQuestNum;
        _IndexData IndexData[30];
    public:
        void Init();
        _NPCQuestIndexTempData();
        void ctor__NPCQuestIndexTempData();
    };    
    static_assert(ATF::checkSize<_NPCQuestIndexTempData, 244>(), "_NPCQuestIndexTempData");
END_ATF_NAMESPACE

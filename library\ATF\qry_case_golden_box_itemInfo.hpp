// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <qry_case_golden_box_item.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using qry_case_golden_box_itemctor_qry_case_golden_box_item2_ptr = void (WINAPIV*)(struct qry_case_golden_box_item*);
        using qry_case_golden_box_itemctor_qry_case_golden_box_item2_clbk = void (WINAPIV*)(struct qry_case_golden_box_item*, qry_case_golden_box_itemctor_qry_case_golden_box_item2_ptr);
        using qry_case_golden_box_itemsize4_ptr = int (WINAPIV*)(struct qry_case_golden_box_item*);
        using qry_case_golden_box_itemsize4_clbk = int (WINAPIV*)(struct qry_case_golden_box_item*, qry_case_golden_box_itemsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

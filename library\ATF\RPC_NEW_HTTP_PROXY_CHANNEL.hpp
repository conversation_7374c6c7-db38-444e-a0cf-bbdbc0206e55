// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    typedef int (WINAPIV *RPC_NEW_HTTP_PROXY_CHANNEL)(_RPC_HTTP_REDIRECTOR_STAGE, unsigned __int16 *, unsigned __int16 *, unsigned __int16 *, unsigned __int16 *, void *, void *, void *, void *, void *, unsigned int, unsigned __int16 **, unsigned __int16 **);
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_NET_TYPE_PARAM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _NET_TYPE_PARAMctor__NET_TYPE_PARAM2_ptr = void (WINAPIV*)(struct _NET_TYPE_PARAM*);
        using _NET_TYPE_PARAMctor__NET_TYPE_PARAM2_clbk = void (WINAPIV*)(struct _NET_TYPE_PARAM*, _NET_TYPE_PARAMctor__NET_TYPE_PARAM2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

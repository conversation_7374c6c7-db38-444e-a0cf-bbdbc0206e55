// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagBinaryParam.hpp>



START_ATF_NAMESPACE
    union $012304FDD17B496AFBE8BAC81FBABAAE
    {
        char *AnsiString;
        wchar_t *UnicodeString;
        int LVal;
        __int16 SVal;
        unsigned __int64 PVal;
        tagBinaryParam BVal;
    };
END_ATF_NAMESPACE

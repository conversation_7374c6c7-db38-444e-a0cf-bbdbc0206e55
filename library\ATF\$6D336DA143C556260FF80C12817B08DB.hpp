// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $6D336DA143C556260FF80C12817B08DB
    {
        BYTE gap0[8];
        int lVal;
    };    
    static_assert(ATF::checkSize<$6D336DA143C556260FF80C12817B08DB, 12>(), "$6D336DA143C556260FF80C12817B08DB");
END_ATF_NAMESPACE

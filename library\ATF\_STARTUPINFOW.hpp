// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _STARTUPINFOW
    {
        unsigned int cb;
        wchar_t *lpReserved;
        wchar_t *lpDesktop;
        wchar_t *lpTitle;
        unsigned int dwX;
        unsigned int dwY;
        unsigned int dwXSize;
        unsigned int dwYSize;
        unsigned int dwXCountChars;
        unsigned int dwYCountChars;
        unsigned int dwFillAttribute;
        unsigned int dwFlags;
        unsigned __int16 wShowWindow;
        unsigned __int16 cbReserved2;
        char *lpReserved2;
        void *hStdInput;
        void *hStdOutput;
        void *hStdError;
    };
END_ATF_NAMESPACE

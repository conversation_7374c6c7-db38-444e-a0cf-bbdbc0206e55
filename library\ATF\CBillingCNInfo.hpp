// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingCN.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CBillingCNctor_CBillingCN2_ptr = void (WINAPIV*)(struct CBillingCN*);
        using CBillingCNctor_CBillingCN2_clbk = void (WINAPIV*)(struct CBillingCN*, CBillingCNctor_CBillingCN2_ptr);
        
        using CBillingCNdtor_CBillingCN7_ptr = void (WINAPIV*)(struct CBillingCN*);
        using CBillingCNdtor_CBillingCN7_clbk = void (WINAPIV*)(struct CBillingCN*, CBillingCNdtor_CBillingCN7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

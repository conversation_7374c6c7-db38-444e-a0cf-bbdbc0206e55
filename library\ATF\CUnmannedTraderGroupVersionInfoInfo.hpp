// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderGroupVersionInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderGroupVersionInfoctor_CUnmannedTraderGroupVersionInfo2_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*);
        using CUnmannedTraderGroupVersionInfoctor_CUnmannedTraderGroupVersionInfo2_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*, CUnmannedTraderGroupVersionInfoctor_CUnmannedTraderGroupVersionInfo2_ptr);
        using CUnmannedTraderGroupVersionInfoGetVersion4_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*, char, char, unsigned int*);
        using CUnmannedTraderGroupVersionInfoGetVersion4_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*, char, char, unsigned int*, CUnmannedTraderGroupVersionInfoGetVersion4_ptr);
        using CUnmannedTraderGroupVersionInfoIncreaseVersion6_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*, char, char);
        using CUnmannedTraderGroupVersionInfoIncreaseVersion6_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*, char, char, CUnmannedTraderGroupVersionInfoIncreaseVersion6_ptr);
        using CUnmannedTraderGroupVersionInfoInit8_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*, std::vector<std::pair<unsigned long,unsigned long>>*);
        using CUnmannedTraderGroupVersionInfoInit8_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*, std::vector<std::pair<unsigned long,unsigned long>>*, CUnmannedTraderGroupVersionInfoInit8_ptr);
        
        using CUnmannedTraderGroupVersionInfodtor_CUnmannedTraderGroupVersionInfo10_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*);
        using CUnmannedTraderGroupVersionInfodtor_CUnmannedTraderGroupVersionInfo10_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupVersionInfo*, CUnmannedTraderGroupVersionInfodtor_CUnmannedTraderGroupVersionInfo10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

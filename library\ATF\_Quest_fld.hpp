// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_action_node.hpp>
#include <_base_fld.hpp>
#include <_quest_fail_condition.hpp>
#include <_quest_reward_item.hpp>
#include <_quest_reward_mastery.hpp>


START_ATF_NAMESPACE
    struct  _Quest_fld : _base_fld
    {
        int m_nLimLv;
        int m_nQuestType;
        int m_bQuestRepeat;
        long double m_dRepeatTime;
        int m_nDifficultyLevel;
        int m_n2;
        int m_bSelectQuestMenual;
        int m_bCompQuestType;
        _action_node m_ActionNode[3];
        int m_nMaxLevel;
         long double m_dConsExp;
        int m_nConsContribution;
        int m_nConsDalant;
        int m_nConspvppoint;
        int m_nConsGold;
        int m_bSelectConsITMenual;
        _quest_reward_item m_RewardItem[6];
        _quest_reward_mastery m_RewardMastery[2];
        char m_strConsSkillCode[64];
        int m_nConsSkillCnt;
        char m_strConsForceCode[64];
        int m_nConsForceCnt;
        char m_strLinkQuest[5][64];
        int m_nLinkQuestGroupID;
        int m_bFailCheck;
        _quest_fail_condition m_QuestFailCond[3];
        char m_strFailBriefCode[64];
        int m_nLinkDummyCond;
        char m_strLinkDummyCode[64];
        char m_strFailLinkQuest[64];
        int m_nViewportType;
        char m_strViewportCode[64];
        int m_nStore_trade;
        char m_txtQTExp[64];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct BASE_HACKSHEILD_PARAMVtbl
    {
        bool (WINAPIV *IsLogPass)(struct BASE_HACKSHEILD_PARAM *_this);
        bool (WINAPIV *OnCheckSession_FirstVerify)(struct BASE_HACKSHEILD_PARAM *_this, int);
        void (WINAPIV *OnConnect)(struct BASE_HACKSHEILD_PARAM *_this, int);
        void (WINAPIV *OnLoop)(struct BASE_HACKSHEILD_PARAM *_this);
        void (WINAPIV *OnDisConnect)(struct BASE_HACKSHEILD_PARAM *_this);
        bool (WINAPIV *OnRecvSession)(struct BASE_HACKSHEILD_PARAM *_this, struct CHackShieldExSystem *, int, char, unsigned __int64, char *);
    };
END_ATF_NAMESPACE

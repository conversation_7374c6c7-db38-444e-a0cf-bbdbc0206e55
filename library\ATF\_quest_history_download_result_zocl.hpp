// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _quest_history_download_result_zocl
    {
        struct __list
        {
            char byIndex;
            char szQuestCode[8];
        };
        char bySlotNum;
        __list SlotInfo[70];
    public:
        _quest_history_download_result_zocl();
        void ctor__quest_history_download_result_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_quest_history_download_result_zocl, 631>(), "_quest_history_download_result_zocl");
END_ATF_NAMESPACE

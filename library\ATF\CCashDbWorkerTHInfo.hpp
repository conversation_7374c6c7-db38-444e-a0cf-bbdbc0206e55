// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerTH.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerTHctor_CCashDbWorkerTH2_ptr = void (WINAPIV*)(struct CCashDbWorkerTH*);
        using CCashDbWorkerTHctor_CCashDbWorkerTH2_clbk = void (WINAPIV*)(struct CCashDbWorkerTH*, CCashDbWorkerTHctor_CCashDbWorkerTH2_ptr);
        using CCashDbWorkerTHGetUseCashQueryStr4_ptr = void (WINAPIV*)(struct CCashDbWorkerTH*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDbWorkerTHGetUseCashQueryStr4_clbk = void (WINAPIV*)(struct CCashDbWorkerTH*, struct _param_cash_update*, int, char*, uint64_t, CCashDbWorkerTHGetUseCashQueryStr4_ptr);
        
        using CCashDbWorkerTHdtor_CCashDbWorkerTH9_ptr = void (WINAPIV*)(struct CCashDbWorkerTH*);
        using CCashDbWorkerTHdtor_CCashDbWorkerTH9_clbk = void (WINAPIV*)(struct CCashDbWorkerTH*, CCashDbWorkerTHdtor_CCashDbWorkerTH9_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

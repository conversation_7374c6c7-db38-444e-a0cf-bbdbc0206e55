// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CEnglandBillingMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CEnglandBillingMgrctor_CEnglandBillingMgr2_ptr = void (WINAPIV*)(struct CEnglandBillingMgr*);
        using CEnglandBillingMgrctor_CEnglandBillingMgr2_clbk = void (WINAPIV*)(struct CEnglandBillingMgr*, CEnglandBillingMgrctor_CEnglandBillingMgr2_ptr);
        using CEnglandBillingMgrCallFunc_Item_Buy4_ptr = int (WINAPIV*)(struct CEnglandBillingMgr*, struct _param_cash_update*, int, int);
        using CEnglandBillingMgrCallFunc_Item_Buy4_clbk = int (WINAPIV*)(struct CEnglandBillingMgr*, struct _param_cash_update*, int, int, CEnglandBillingMgrCallFunc_Item_Buy4_ptr);
        using CEnglandBillingMgrCallFunc_RFOnline_Auth6_ptr = int (WINAPIV*)(struct CEnglandBillingMgr*, struct _param_cash_select*, int);
        using CEnglandBillingMgrCallFunc_RFOnline_Auth6_clbk = int (WINAPIV*)(struct CEnglandBillingMgr*, struct _param_cash_select*, int, CEnglandBillingMgrCallFunc_RFOnline_Auth6_ptr);
        using CEnglandBillingMgrFree8_ptr = bool (WINAPIV*)(struct CEnglandBillingMgr*);
        using CEnglandBillingMgrFree8_clbk = bool (WINAPIV*)(struct CEnglandBillingMgr*, CEnglandBillingMgrFree8_ptr);
        using CEnglandBillingMgrMakeConnectionThread10_ptr = bool (WINAPIV*)(struct CEnglandBillingMgr*);
        using CEnglandBillingMgrMakeConnectionThread10_clbk = bool (WINAPIV*)(struct CEnglandBillingMgr*, CEnglandBillingMgrMakeConnectionThread10_ptr);
        using CEnglandBillingMgrSetPoolPointer12_ptr = void (WINAPIV*)(struct CEnglandBillingMgr*, struct TaskPool*);
        using CEnglandBillingMgrSetPoolPointer12_clbk = void (WINAPIV*)(struct CEnglandBillingMgr*, struct TaskPool*, CEnglandBillingMgrSetPoolPointer12_ptr);
        
        using CEnglandBillingMgrdtor_CEnglandBillingMgr14_ptr = void (WINAPIV*)(struct CEnglandBillingMgr*);
        using CEnglandBillingMgrdtor_CEnglandBillingMgr14_clbk = void (WINAPIV*)(struct CEnglandBillingMgr*, CEnglandBillingMgrdtor_CEnglandBillingMgr14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

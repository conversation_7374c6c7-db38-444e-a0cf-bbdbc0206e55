// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $5CA90CF57237397281FB12BFD52C1905
    {
        BYTE gap0[8];
        int intVal;
    };    
    static_assert(ATF::checkSize<$5CA90CF57237397281FB12BFD52C1905, 12>(), "$5CA90CF57237397281FB12BFD52C1905");
END_ATF_NAMESPACE

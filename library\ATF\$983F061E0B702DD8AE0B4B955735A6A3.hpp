// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $983F061E0B702DD8AE0B4B955735A6A3
    {
        unsigned int Lo;
        int Hi;
    };    
    static_assert(ATF::checkSize<$983F061E0B702DD8AE0B4B955735A6A3, 8>(), "$983F061E0B702DD8AE0B4B955735A6A3");
END_ATF_NAMESPACE

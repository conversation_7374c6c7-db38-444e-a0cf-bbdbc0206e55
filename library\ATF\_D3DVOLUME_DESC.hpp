// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_D3DFORMAT.hpp>


START_ATF_NAMESPACE
    struct _D3DVOLUME_DESC
    {
        _D3DFORMAT Format;
        _D3DRESOURCETYPE Type;
        unsigned int Usage;
        _D3DPOOL Pool;
        unsigned int Size;
        unsigned int Width;
        unsigned int Height;
        unsigned int Depth;
    };
END_ATF_NAMESPACE

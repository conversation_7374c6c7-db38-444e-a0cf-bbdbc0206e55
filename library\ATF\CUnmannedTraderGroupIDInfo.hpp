// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CUnmannedTraderSortType.hpp>
#include <CUnmannedTraderDivisionInfo.hpp>
#include <std__vector.hpp>
#include <std__pair.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderGroupIDInfo
    {
        CLogFile *m_pkLogger;
        std::vector<CUnmannedTraderDivisionInfo *> m_vecDivisionInfo;
    public:
        CUnmannedTraderGroupIDInfo();
        void ctor_CUnmannedTraderGroupIDInfo();
        void CleanUp();
        bool GetGroupID(char byTableCode, uint16_t wItemTableIndex, char* byDivision, char* byClass);
        bool GetGroupID(char byTableCode, uint16_t wItemTableIndex, char* byDivision, char* byClass, char* bySubClass, unsigned int* dwListIndex);
        bool GetIDInfo(std::vector<std::pair<unsigned long,unsigned long>>* vecInfo);
        struct CUnmannedTraderSortType* GetSortType(char byDivision, char bySortType);
        bool IsExistGroupID(char byDivision, char byClass, char bySubClass, char bySortType, unsigned int* dwListIndex);
        bool IsExistID(unsigned int dwID);
        bool LoadXML(char* szFileName);
        void Log(char* fmt);
        void SetLogger(struct CLogFile* pkLogger);
        ~CUnmannedTraderGroupIDInfo();
        void dtor_CUnmannedTraderGroupIDInfo();
    };
END_ATF_NAMESPACE

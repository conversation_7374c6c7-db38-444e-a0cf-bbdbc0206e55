// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct tagCURSORSHAPE
    {
        int xHotSpot;
        int yHotSpot;
        int cx;
        int cy;
        int cbWidth;
        char Planes;
        char BitsPixel;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagEMR.hpp>


START_ATF_NAMESPACE
    struct tagEMRCREATEDIBPATTERNBRUSHPT
    {
        tagEMR emr;
        unsigned int ihBrush;
        unsigned int iUsage;
        unsigned int offBmi;
        unsigned int cbBmi;
        unsigned int offBits;
        unsigned int cbBits;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IDirectDrawSurface.hpp>


START_ATF_NAMESPACE
    union $5FC8BC77704B434305F87B884184E23E
    {
        unsigned int dwFillColor;
        unsigned int dwFillDepth;
        unsigned int dwFillPixel;
        IDirectDrawSurface *lpDDSPattern;
    };
END_ATF_NAMESPACE

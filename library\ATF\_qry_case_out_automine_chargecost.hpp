// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_out_automine_chargecost
    {
        char byRace;
        char byCollisionType;
        unsigned __int16 wIndex;
        unsigned int dwGuildSerial;
        unsigned int in_master;
        int in_charge;
        int in_gold;
        long double out_totaldalant;
        long double out_totalgold;
        char byDate[4];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

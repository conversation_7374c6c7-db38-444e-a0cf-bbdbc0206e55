// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $6C39A09D97B880131B2C5BB4943CBB45
    {
        BYTE gap0[8];
        void *byref;
    };    
    static_assert(ATF::checkSize<$6C39A09D97B880131B2C5BB4943CBB45, 16>(), "$6C39A09D97B880131B2C5BB4943CBB45");
END_ATF_NAMESPACE

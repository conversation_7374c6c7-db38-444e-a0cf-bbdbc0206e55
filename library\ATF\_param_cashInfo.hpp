// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_param_cash.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _param_cashctor__param_cash2_ptr = void (WINAPIV*)(struct _param_cash*, unsigned int, unsigned int, uint16_t);
        using _param_cashctor__param_cash2_clbk = void (WINAPIV*)(struct _param_cash*, unsigned int, unsigned int, uint16_t, _param_cashctor__param_cash2_ptr);
        
        using _param_cashdtor__param_cash4_ptr = void (WINAPIV*)(struct _param_cash*);
        using _param_cashdtor__param_cash4_clbk = void (WINAPIV*)(struct _param_cash*, _param_cashdtor__param_cash4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

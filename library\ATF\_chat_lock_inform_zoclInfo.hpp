// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_chat_lock_inform_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _chat_lock_inform_zoclsize2_ptr = int (WINAPIV*)(struct _chat_lock_inform_zocl*);
        using _chat_lock_inform_zoclsize2_clbk = int (WINAPIV*)(struct _chat_lock_inform_zocl*, _chat_lock_inform_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

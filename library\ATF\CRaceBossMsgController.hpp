// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <CPlayer.hpp>
#include <RACE_BOSS_MSG__CMsg.hpp>
#include <RACE_BOSS_MSG__CMsgListManager.hpp>


START_ATF_NAMESPACE
    struct CRaceBossMsgController
    {
        bool m_bConnectWeb;
        int m_iOldDay;
        CMyTimer *m_pkTimer;
        RACE_BOSS_MSG::CMsgListManager m_kManager;
    public:
        CRaceBossMsgController();
        void ctor_CRaceBossMsgController();
        bool Cancel(char ucRace, unsigned int dwMsgID);
        bool Cancel(char ucRace, unsigned int dwMsgID, struct CPlayer* pkManager);
        void CleanUp();
        static void Destroy();
        int GetCurDay();
        bool Init();
        static struct CRaceBossMsgController* Instance();
        bool IsDayChanged();
        bool LoadCurTime(unsigned int* dwCurTime);
        void OnLoop();
        void SaveCurTime();
        bool Send(struct CPlayer* pkSender, char* pwszMsg);
        bool Send(char ucRace, unsigned int dwSerial, char* wszName, char* pwszMsg, unsigned int dwWebSendDBID);
        void SendCancelWeb(char ucRace, struct RACE_BOSS_MSG::CMsg* pkMsg);
        void SendCancleInfomManager(uint16_t usInx, char ucRet, unsigned int dwMsgID, char* pwszName);
        void SendCancleInfomSender(unsigned int dwSerial);
        void SendComfirmWeb(char ucRace, struct RACE_BOSS_MSG::CMsg* pkMsg);
        void SendConfirmCtrl(char ucRace, struct RACE_BOSS_MSG::CMsg* pkMsg);
        void SendInfomSender(unsigned int dwSerial, char ucRemainCnt);
        void SendMsgRequestResult(uint16_t usInx, char ucRet);
        void SendRequestWeb(char ucRace, struct RACE_BOSS_MSG::CMsg* pkMsg);
        void SendWebRaceBossSMSErrorResult(int iRet, unsigned int dwWebDBID);
        void UpdateSend();
        ~CRaceBossMsgController();
        void dtor_CRaceBossMsgController();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CNetTimer.hpp>
#include <_FILETIME.hpp>
#include <_cash_event_ini.hpp>


START_ATF_NAMESPACE
    struct _cash_event
    {
        char m_event_status;
        CNetTimer m_event_timer;
        _FILETIME m_event_ini_file_time;
        CLogFile m_event_log;
        int m_event_inform_before[2];
        _cash_event_ini m_ini;
    public:
        _cash_event();
        void ctor__cash_event();
        ~_cash_event();
        void dtor__cash_event();
    };
END_ATF_NAMESPACE

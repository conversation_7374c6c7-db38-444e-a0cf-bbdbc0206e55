// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $E7F48E2F4EAA3149E57D87E5D1B2C6B8
    {
        BYTE gap0[8];
        unsigned int uintVal;
    };    
    static_assert(ATF::checkSize<$E7F48E2F4EAA3149E57D87E5D1B2C6B8, 12>(), "$E7F48E2F4EAA3149E57D87E5D1B2C6B8");
END_ATF_NAMESPACE

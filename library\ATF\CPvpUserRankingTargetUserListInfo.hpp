// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPvpUserRankingTargetUserList.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPvpUserRankingTargetUserListAdd2_ptr = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*, unsigned int, char, char);
        using CPvpUserRankingTargetUserListAdd2_clbk = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*, unsigned int, char, char, CPvpUserRankingTargetUserListAdd2_ptr);
        
        using CPvpUserRankingTargetUserListctor_CPvpUserRankingTargetUserList4_ptr = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*);
        using CPvpUserRankingTargetUserListctor_CPvpUserRankingTargetUserList4_clbk = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*, CPvpUserRankingTargetUserListctor_CPvpUserRankingTargetUserList4_ptr);
        using CPvpUserRankingTargetUserListClearRankingStart6_ptr = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*);
        using CPvpUserRankingTargetUserListClearRankingStart6_clbk = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*, CPvpUserRankingTargetUserListClearRankingStart6_ptr);
        using CPvpUserRankingTargetUserListGetAddedTotalCnt8_ptr = unsigned int (WINAPIV*)(struct CPvpUserRankingTargetUserList*);
        using CPvpUserRankingTargetUserListGetAddedTotalCnt8_clbk = unsigned int (WINAPIV*)(struct CPvpUserRankingTargetUserList*, CPvpUserRankingTargetUserListGetAddedTotalCnt8_ptr);
        using CPvpUserRankingTargetUserListUpdateCharGrade10_ptr = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*);
        using CPvpUserRankingTargetUserListUpdateCharGrade10_clbk = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*, CPvpUserRankingTargetUserListUpdateCharGrade10_ptr);
        using CPvpUserRankingTargetUserListUpdateRaceRankStep1112_ptr = char (WINAPIV*)(struct CPvpUserRankingTargetUserList*, char*);
        using CPvpUserRankingTargetUserListUpdateRaceRankStep1112_clbk = char (WINAPIV*)(struct CPvpUserRankingTargetUserList*, char*, CPvpUserRankingTargetUserListUpdateRaceRankStep1112_ptr);
        using CPvpUserRankingTargetUserListassign14_ptr = bool (WINAPIV*)(struct CPvpUserRankingTargetUserList*);
        using CPvpUserRankingTargetUserListassign14_clbk = bool (WINAPIV*)(struct CPvpUserRankingTargetUserList*, CPvpUserRankingTargetUserListassign14_ptr);
        
        using CPvpUserRankingTargetUserListdtor_CPvpUserRankingTargetUserList16_ptr = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*);
        using CPvpUserRankingTargetUserListdtor_CPvpUserRankingTargetUserList16_clbk = void (WINAPIV*)(struct CPvpUserRankingTargetUserList*, CPvpUserRankingTargetUserListdtor_CPvpUserRankingTargetUserList16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FILETIME.hpp>


START_ATF_NAMESPACE
    struct XACTSTATS
    {
        unsigned int cOpen;
        unsigned int cCommitting;
        unsigned int cCommitted;
        unsigned int cAborting;
        unsigned int cAborted;
        unsigned int cInDoubt;
        unsigned int cHeuristicDecision;
        _FILETIME timeTransactionsUp;
    };
END_ATF_NAMESPACE

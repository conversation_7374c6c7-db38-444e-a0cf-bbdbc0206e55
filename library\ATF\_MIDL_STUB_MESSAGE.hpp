// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$3FA64165D15A4602C29B2D9B0DA4A5E9.hpp>
#include <ARRAY_INFO.hpp>
#include <CS_STUB_INFO.hpp>
#include <IRpcChannelBuffer.hpp>
#include <_FULL_PTR_XLAT_TABLES.hpp>
#include <_MIDL_STUB_DESC.hpp>
#include <_RPC_MESSAGE.hpp>


START_ATF_NAMESPACE
    struct _MIDL_STUB_MESSAGE
    {
        _RPC_MESSAGE *RpcMsg;
        char *Buffer;
        char *BufferStart;
        char *BufferEnd;
        char *BufferMark;
        unsigned int BufferLength;
        unsigned int MemorySize;
        char *Memory;
        char IsClient;
        char Pad;
        unsigned __int16 uFlags2;
        int ReuseBuffer;
        struct NDR_ALLOC_ALL_NODES_CONTEXT *pAllocAllNodesContext;
        struct NDR_POINTER_QUEUE_STATE *pPointerQueueState;
        int IgnoreEmbeddedPointers;
        char *PointerBufferMark;
        char fBufferValid;
        char uFlags;
        unsigned __int16 UniquePtrCount;
        unsigned __int64 MaxCount;
        unsigned int Offset;
        unsigned int ActualCount;
        void *(WINAPIV *pfnAllocate)(unsigned __int64);
        void (WINAPIV *pfnFree)(void *);
        char *StackTop;
        char *pPresentedType;
        char *pTransmitType;
        void *SavedHandle;
        _MIDL_STUB_DESC *StubDesc;
        _FULL_PTR_XLAT_TABLES *FullPtrXlatTables;
        unsigned int FullPtrRefId;
        unsigned int PointerLength;
        __int32 fInDontFree : 1;
        __int32 fDontCallFreeInst : 1;
        __int32 fInOnlyParam : 1;
        __int32 fHasReturn : 1;
        __int32 fHasExtensions : 1;
        __int32 fHasNewCorrDesc : 1;
        __int32 fIsOicfServer : 1;
        __int32 fHasMemoryValidateCallback : 1;
        __int32 fUnused : 8;
        __int32 fUnused2 : 16;
        unsigned int dwDestContext;
        void *pvDestContext;
        $3FA64165D15A4602C29B2D9B0DA4A5E9 **SavedContextHandles;
        int ParamNumber;
        IRpcChannelBuffer *pRpcChannelBuffer;
        ARRAY_INFO *pArrayInfo;
        unsigned int *SizePtrCountArray;
        unsigned int *SizePtrOffsetArray;
        unsigned int *SizePtrLengthArray;
        void *pArgQueue;
        unsigned int dwStubPhase;
        void *LowStackMark;
        struct _NDR_ASYNC_MESSAGE *pAsyncMsg;
        _NDR_CORRELATION_INFO *pCorrInfo;
        char *pCorrMemory;
        void *pMemoryList;
        CS_STUB_INFO *pCSInfo;
        char *ConformanceMark;
        char *VarianceMark;
        __int64 Unused;
        struct _NDR_PROC_CONTEXT *pContext;
        void *pUserMarshalList;
        __int64 Reserved51_2;
        __int64 Reserved51_3;
        __int64 Reserved51_4;
        __int64 Reserved51_5;
    };
END_ATF_NAMESPACE

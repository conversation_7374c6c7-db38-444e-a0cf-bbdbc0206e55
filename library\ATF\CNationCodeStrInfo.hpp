// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationCodeStr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationCodeStrctor_CNationCodeStr2_ptr = void (WINAPIV*)(struct CNationCodeStr*, int, char*);
        using CNationCodeStrctor_CNationCodeStr2_clbk = void (WINAPIV*)(struct CNationCodeStr*, int, char*, CNationCodeStrctor_CNationCodeStr2_ptr);
        using CNationCodeStrGetKey4_ptr = int (WINAPIV*)(struct CNationCodeStr*);
        using CNationCodeStrGetKey4_clbk = int (WINAPIV*)(struct CNationCodeStr*, CNationCodeStrGetKey4_ptr);
        using CNationCodeStrGetStr6_ptr = char* (WINAPIV*)(struct CNationCodeStr*);
        using CNationCodeStrGetStr6_clbk = char* (WINAPIV*)(struct CNationCodeStr*, CNationCodeStrGetStr6_ptr);
        using CNationCodeStrIsNULL8_ptr = bool (WINAPIV*)(struct CNationCodeStr*);
        using CNationCodeStrIsNULL8_clbk = bool (WINAPIV*)(struct CNationCodeStr*, CNationCodeStrIsNULL8_ptr);
        
        using CNationCodeStrdtor_CNationCodeStr12_ptr = void (WINAPIV*)(struct CNationCodeStr*);
        using CNationCodeStrdtor_CNationCodeStr12_clbk = void (WINAPIV*)(struct CNationCodeStr*, CNationCodeStrdtor_CNationCodeStr12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

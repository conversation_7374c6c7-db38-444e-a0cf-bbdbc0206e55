// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _COMMPROP
    {
        unsigned __int16 wPacketLength;
        unsigned __int16 wPacketVersion;
        unsigned int dwServiceMask;
        unsigned int dwReserved1;
        unsigned int dwMaxTxQueue;
        unsigned int dwMaxRxQueue;
        unsigned int dwMaxBaud;
        unsigned int dwProvSubType;
        unsigned int dwProvCapabilities;
        unsigned int dwSettableParams;
        unsigned int dwSettableBaud;
        unsigned __int16 wSettableData;
        unsigned __int16 wSettableStopParity;
        unsigned int dwCurrentTxQueue;
        unsigned int dwCurrentRxQueue;
        unsigned int dwProvSpec1;
        unsigned int dwProvSpec2;
        wchar_t wcProvChar[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

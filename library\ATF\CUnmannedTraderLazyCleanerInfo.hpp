// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderLazyCleaner.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderLazyCleanerctor_CUnmannedTraderLazyCleaner2_ptr = void (WINAPIV*)(struct CUnmannedTraderLazyCleaner*);
        using CUnmannedTraderLazyCleanerctor_CUnmannedTraderLazyCleaner2_clbk = void (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, CUnmannedTraderLazyCleanerctor_CUnmannedTraderLazyCleaner2_ptr);
        using CUnmannedTraderLazyCleanerCompleteUpdateClear4_ptr = void (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, char*);
        using CUnmannedTraderLazyCleanerCompleteUpdateClear4_clbk = void (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, char*, CUnmannedTraderLazyCleanerCompleteUpdateClear4_ptr);
        using CUnmannedTraderLazyCleanerInit6_ptr = bool (WINAPIV*)(struct CUnmannedTraderLazyCleaner*);
        using CUnmannedTraderLazyCleanerInit6_clbk = bool (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, CUnmannedTraderLazyCleanerInit6_ptr);
        using CUnmannedTraderLazyCleanerLoop8_ptr = void (WINAPIV*)(struct CUnmannedTraderLazyCleaner*);
        using CUnmannedTraderLazyCleanerLoop8_clbk = void (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, CUnmannedTraderLazyCleanerLoop8_ptr);
        using CUnmannedTraderLazyCleanerProcUpdate10_ptr = char (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, char, struct _SYSTEMTIME*, bool*);
        using CUnmannedTraderLazyCleanerProcUpdate10_clbk = char (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, char, struct _SYSTEMTIME*, bool*, CUnmannedTraderLazyCleanerProcUpdate10_ptr);
        using CUnmannedTraderLazyCleanerUpdateClear12_ptr = char (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, char*);
        using CUnmannedTraderLazyCleanerUpdateClear12_clbk = char (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, char*, CUnmannedTraderLazyCleanerUpdateClear12_ptr);
        
        using CUnmannedTraderLazyCleanerdtor_CUnmannedTraderLazyCleaner14_ptr = void (WINAPIV*)(struct CUnmannedTraderLazyCleaner*);
        using CUnmannedTraderLazyCleanerdtor_CUnmannedTraderLazyCleaner14_clbk = void (WINAPIV*)(struct CUnmannedTraderLazyCleaner*, CUnmannedTraderLazyCleanerdtor_CUnmannedTraderLazyCleaner14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

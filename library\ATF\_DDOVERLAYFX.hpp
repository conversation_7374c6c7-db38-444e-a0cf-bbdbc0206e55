// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$246B53D799D52A4EA437232D85027ECC.hpp>
#include <$67EDD32E34E99E34933362666673E49D.hpp>
#include <_DDCOLORKEY.hpp>


START_ATF_NAMESPACE
    struct _DDOVERLAYFX
    {
        unsigned int dwSize;
        unsigned int dwAlphaEdgeBlendBitDepth;
        unsigned int dwAlphaEdgeBlend;
        unsigned int dwReserved;
        unsigned int dwAlphaDestConstBitDepth;
        $246B53D799D52A4EA437232D85027ECC ___u5;
        unsigned int dwAlphaSrcConstBitDepth;
        $67EDD32E34E99E34933362666673E49D ___u7;
        _DDCOLORKEY dckDestColorkey;
        _DDCOLORKEY dckSrcColorkey;
        unsigned int dwDDFX;
        unsigned int dwFlags;
    };
END_ATF_NAMESPACE

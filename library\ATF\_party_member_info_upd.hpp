// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _party_member_info_upd
    {
        struct  _EFFECT
        {
            unsigned __int16 wEffectCode;
            char byEffectLv;
        };
        unsigned int dwMemSerial;
        unsigned __int16 wHPRate;
        unsigned __int16 wFPRate;
        unsigned __int16 wSPRate;
        char byLv;
        char byMapCode;
        __int16 zPos[2];
        char byContEffectNum;
        _EFFECT Effect[16];
    public:
        _party_member_info_upd();
        void ctor__party_member_info_upd();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_party_member_info_upd, 65>(), "_party_member_info_upd");
END_ATF_NAMESPACE

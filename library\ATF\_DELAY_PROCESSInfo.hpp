// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DELAY_PROCESS.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _DELAY_PROCESSCheckOnLoop2_ptr = void (WINAPIV*)(struct _DELAY_PROCESS*);
        using _DELAY_PROCESSCheckOnLoop2_clbk = void (WINAPIV*)(struct _DELAY_PROCESS*, _DELAY_PROCESSCheckOnLoop2_ptr);
        using _DELAY_PROCESSDelete4_ptr = void (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int);
        using _DELAY_PROCESSDelete4_clbk = void (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int, _DELAY_PROCESSDelete4_ptr);
        using _DELAY_PROCESSInit6_ptr = bool (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int);
        using _DELAY_PROCESSInit6_clbk = bool (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int, _DELAY_PROCESSInit6_ptr);
        using _DELAY_PROCESSProcess8_ptr = void (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int);
        using _DELAY_PROCESSProcess8_clbk = void (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int, _DELAY_PROCESSProcess8_ptr);
        using _DELAY_PROCESSPush10_ptr = bool (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int);
        using _DELAY_PROCESSPush10_clbk = bool (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int, _DELAY_PROCESSPush10_ptr);
        
        using _DELAY_PROCESSctor__DELAY_PROCESS12_ptr = void (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int);
        using _DELAY_PROCESSctor__DELAY_PROCESS12_clbk = void (WINAPIV*)(struct _DELAY_PROCESS*, unsigned int, unsigned int, _DELAY_PROCESSctor__DELAY_PROCESS12_ptr);
        
        using _DELAY_PROCESSctor__DELAY_PROCESS14_ptr = void (WINAPIV*)(struct _DELAY_PROCESS*);
        using _DELAY_PROCESSctor__DELAY_PROCESS14_clbk = void (WINAPIV*)(struct _DELAY_PROCESS*, _DELAY_PROCESSctor__DELAY_PROCESS14_ptr);
        
        using _DELAY_PROCESSdtor__DELAY_PROCESS16_ptr = void (WINAPIV*)(struct _DELAY_PROCESS*);
        using _DELAY_PROCESSdtor__DELAY_PROCESS16_clbk = void (WINAPIV*)(struct _DELAY_PROCESS*, _DELAY_PROCESSdtor__DELAY_PROCESS16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

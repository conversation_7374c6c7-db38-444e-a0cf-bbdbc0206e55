// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _guild_battle_suggest_request_result_zocl
    {
        char byRet;
        char wszDestGuildName[17];
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_guild_battle_suggest_request_result_zocl, 18>(), "_guild_battle_suggest_request_result_zocl");
END_ATF_NAMESPACE

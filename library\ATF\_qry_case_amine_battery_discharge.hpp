// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _qry_case_amine_battery_discharge
    {
        char bySubQryCase;
        char byRace;
        char byCollisionType;
        unsigned int dwGuildSerial;
        unsigned int dwBattery;
    public:
        _qry_case_amine_battery_discharge();
        void ctor__qry_case_amine_battery_discharge();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_amine_battery_discharge, 11>(), "_qry_case_amine_battery_discharge");
END_ATF_NAMESPACE

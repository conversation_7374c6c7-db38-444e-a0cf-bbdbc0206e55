// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $294BB986A46DE43482F34575D09FBC7D
    {
      dliStartProcessing = 0x0,
      dliNoteStartProcessing = 0x0,
      dliNotePreLoadLibrary = 0x1,
      dliNotePreGetProcAddress = 0x2,
      dliFailLoadLib = 0x3,
      dliFailGetProc = 0x4,
      dliNoteEndProcessing = 0x5,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_updatewinloseguildbattlerank
    {
        char byWinRace;
        unsigned int dwWinGuildSerial;
        char byLoseRace;
        unsigned int dwLoseGuildSerial;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_updatewinloseguildbattlerank, 16>(), "_qry_case_updatewinloseguildbattlerank");
END_ATF_NAMESPACE

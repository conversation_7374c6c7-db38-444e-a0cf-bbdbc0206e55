// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_itembox_take_new_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _itembox_take_new_result_zoclsize2_ptr = int (WINAPIV*)(struct _itembox_take_new_result_zocl*);
        using _itembox_take_new_result_zoclsize2_clbk = int (WINAPIV*)(struct _itembox_take_new_result_zocl*, _itembox_take_new_result_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

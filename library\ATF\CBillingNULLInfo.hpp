// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingNULL.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CBillingNULLAlive2_ptr = void (WINAPIV*)(struct CBillingNULL*, struct CUserDB*);
        using CBillingNULLAlive2_clbk = void (WINAPIV*)(struct CBillingNULL*, struct CUserDB*, CBillingNULLAlive2_ptr);
        using CBillingNULLBillingClose4_ptr = void (WINAPIV*)(struct CBillingNULL*, char*);
        using CBillingNULLBillingClose4_clbk = void (WINAPIV*)(struct CBillingNULL*, char*, CBillingNULLBillingClose4_ptr);
        
        using CBillingNULLctor_CBillingNULL6_ptr = void (WINAPIV*)(struct CBillingNULL*);
        using CBillingNULLctor_CBillingNULL6_clbk = void (WINAPIV*)(struct CBillingNULL*, CBillingNULLctor_CBillingNULL6_ptr);
        using CBillingNULLLogin8_ptr = void (WINAPIV*)(struct CBillingNULL*, struct CUserDB*);
        using CBillingNULLLogin8_clbk = void (WINAPIV*)(struct CBillingNULL*, struct CUserDB*, CBillingNULLLogin8_ptr);
        using CBillingNULLLogout10_ptr = void (WINAPIV*)(struct CBillingNULL*, struct CUserDB*);
        using CBillingNULLLogout10_clbk = void (WINAPIV*)(struct CBillingNULL*, struct CUserDB*, CBillingNULLLogout10_ptr);
        using CBillingNULLSendMsg_Login12_ptr = bool (WINAPIV*)(struct CBillingNULL*, char*, char*, char*, int16_t, struct _SYSTEMTIME*, int);
        using CBillingNULLSendMsg_Login12_clbk = bool (WINAPIV*)(struct CBillingNULL*, char*, char*, char*, int16_t, struct _SYSTEMTIME*, int, CBillingNULLSendMsg_Login12_ptr);
        
        using CBillingNULLdtor_CBillingNULL17_ptr = void (WINAPIV*)(struct CBillingNULL*);
        using CBillingNULLdtor_CBillingNULL17_clbk = void (WINAPIV*)(struct CBillingNULL*, CBillingNULLdtor_CBillingNULL17_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

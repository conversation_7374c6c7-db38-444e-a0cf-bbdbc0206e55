// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <US__CriticalSectionInfo.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        namespace Detail
        {
            extern ::std::array<hook_record, 4> CriticalSection_functions;
        }; // end namespace Detail
    }; // end namespace US
END_ATF_NAMESPACE

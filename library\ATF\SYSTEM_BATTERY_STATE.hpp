// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct SYSTEM_BATTERY_STATE
    {
        char AcOnLine;
        char BatteryPresent;
        char Charging;
        char Discharging;
        char Spare1[4];
        unsigned int MaxCapacity;
        unsigned int RemainingCapacity;
        unsigned int Rate;
        unsigned int EstimatedTime;
        unsigned int DefaultAlert1;
        unsigned int DefaultAlert2;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__logic_error.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            
            using std__logic_errorctor_logic_error5_ptr = void (WINAPIV*)(struct std::logic_error*, std::basic_string<char>*);
            using std__logic_errorctor_logic_error5_clbk = void (WINAPIV*)(struct std::logic_error*, std::basic_string<char>*, std__logic_errorctor_logic_error5_ptr);
            
            using std__logic_errorctor_logic_error7_ptr = void (WINAPIV*)(struct std::logic_error*, struct std::logic_error*);
            using std__logic_errorctor_logic_error7_clbk = void (WINAPIV*)(struct std::logic_error*, struct std::logic_error*, std__logic_errorctor_logic_error7_ptr);
            using std__logic_errorwhat9_ptr = char* (WINAPIV*)(struct std::logic_error*);
            using std__logic_errorwhat9_clbk = char* (WINAPIV*)(struct std::logic_error*, std__logic_errorwhat9_ptr);
            
            using std__logic_errordtor_logic_error11_ptr = void (WINAPIV*)(struct std::logic_error*);
            using std__logic_errordtor_logic_error11_clbk = void (WINAPIV*)(struct std::logic_error*, std__logic_errordtor_logic_error11_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

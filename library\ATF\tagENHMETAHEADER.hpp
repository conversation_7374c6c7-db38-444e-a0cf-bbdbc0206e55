// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RECTL.hpp>
#include <tagSIZE.hpp>


START_ATF_NAMESPACE
    struct tagENHMETAHEADER
    {
        unsigned int iType;
        unsigned int nSize;
        _RECTL rclBounds;
        _RECTL rclFrame;
        unsigned int dSignature;
        unsigned int nVersion;
        unsigned int nBytes;
        unsigned int nRecords;
        unsigned __int16 nHandles;
        unsigned __int16 sReserved;
        unsigned int nDescription;
        unsigned int offDescription;
        unsigned int nPalEntries;
        tagSIZE szlDevice;
        tagSIZE szlMillimeters;
        unsigned int cbPixelFormat;
        unsigned int offPixelFormat;
        unsigned int bOpenGL;
        tagSIZE szlMicrometers;
    };
END_ATF_NAMESPACE

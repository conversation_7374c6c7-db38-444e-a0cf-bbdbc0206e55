{"intervals": {"open_world_wait": 1000, "step_delay": 10}, "registry": {"modules": [{"name": "system.crash_dump", "config": {"full_dump": true}}, {"name": "fix.main_thread", "config": {"priority_1": 100, "priority_2": 500, "priority_3": 1000, "priority_4": 2000, "priority_5": 5000}}, {"name": "fix.network_ex", "config": {"accept_ip_check": true}}, {"name": "fix.vote", "config": {"level": 30, "play_time": -1, "pvp_point": -1, "pvp_cash_bag": -1.0, "class_grade": 0, "score_list_show": true, "score_hide": false}}, {"name": "addon.loot_exchange", "config": {"activated": false, "exchange_all": false}}, {"name": "addon.replace_loot_name", "config": {"activated": false, "only_pitboss": true, "replace_item": "iyyyy24"}}, {"name": "addon.radius_drop_loot", "config": {"activated": false, "only_pitboss": false, "range": 100}}, {"name": "addon.enchant_chance", "config": {"activated": false}}, {"name": "addon.advert", "config": {"activated": true, "records": [{"delay": 600, "hide_for_premium": true, "message": "Vote for this server and take bonus"}]}}, {"name": "addon.defence_formula", "config": {"activated": false, "base_resist_coeff": 0.1, "support_resist_coeff": 0.9}}, {"name": "addon.accuracy_effect", "config": {"activated": false, "hit": "standart"}}, {"name": "addon.stone_hp", "config": {"activated": false, "min_lv": 30}}, {"name": "addon.pvp_potion", "config": {"activated": false}}, {"name": "addon.mau_exp", "config": {"activated": false, "mau_exp_coeff": 1.0}}, {"name": "addon.bonus_start", "config": {"activated": false, "start_lvl": 1, "start_dalant": 0, "start_gold": 0}}, {"name": "addon.chat_log", "config": {"activated": false, "folder": "./YorozuyaGS/Logs/Chat/", "circle": true, "far": true, "party": true, "race": true, "race_boss_cry": true, "guild": true, "map": true, "race_boss": true, "guild_est_sen": true, "multi_far": true, "re_resentation": true, "all": true, "trade": true}}, {"name": "addon.server_memory_patch", "config": {"activated": true, "records": [{"address": "0x215ce9", "value": "EB", "description": "enabled gm commands in release mode"}, {"address": "0x02a634", "value": "9090", "description": "enabled button in UI[1/3]"}, {"address": "0x02a734", "value": "9090", "description": "enabled button in UI[2/3]"}, {"address": "0x02a7c4", "value": "9090", "description": "enabled button in UI[3/3]"}, {"address": "0x19ade6", "value": "30750000", "description": "increased count wall in geo data[1/2]"}, {"address": "0x19ae03", "value": "30750000", "description": "increased count wall in geo data[2/2]"}, {"address": "0x0b2a22", "value": "EB", "description": "ignoring bugged loot stack"}]}}, {"name": "addon.various_settings", "config": {"settings": [{"name": "exchange", "config": {"activated": false}}]}}]}}
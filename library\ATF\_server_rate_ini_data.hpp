// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _server_rate_ini_data
    {
        float ItemRootRate;
        float MineSpeedRate;
        float ForceLiverAccumRate;
        float MasteryGetRate;
        float AnimusExpRate;
        float PlayerExpRate;
        float DarkHoleRewardRate;
        float PlayerLostExp;
        float PremiumMiningSpeed;
        float PremiumPlayerExp;
        float PremiumAnimusExp;
        float PremiumBasseMastery;
        float PremiumSkillForceMastery;
        float PremiumItemDrop;
        float PremiumPvpPointRate;
        float PremiumPlayerLostExp;
        char byBindHQ;
        char bySetteMineElanMap;
        char byScrollItem;
        char byCashItem;
        char byAddCharacter;
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_server_rate_ini_data, 72>(), "_server_rate_ini_data");
END_ATF_NAMESPACE

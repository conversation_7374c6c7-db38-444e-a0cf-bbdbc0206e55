#include <_personal_automine_popore_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_popore_zocl::_personal_automine_popore_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_popore_zocl*);
        (org_ptr(0x1402e1b80L))(this);
    };
    void _personal_automine_popore_zocl::ctor__personal_automine_popore_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_popore_zocl*);
        (org_ptr(0x1402e1b80L))(this);
    };
    int _personal_automine_popore_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_popore_zocl*);
        return (org_ptr(0x1402e1bd0L))(this);
    };
END_ATF_NAMESPACE

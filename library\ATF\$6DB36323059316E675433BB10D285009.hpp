// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $6DB36323059316E675433BB10D285009
    {
        BYTE gap0[8];
        __int16 boolVal;
    };    
    static_assert(ATF::checkSize<$6DB36323059316E675433BB10D285009, 10>(), "$6DB36323059316E675433BB10D285009");
END_ATF_NAMESPACE

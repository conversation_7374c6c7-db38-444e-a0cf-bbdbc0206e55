// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CExtPotionBuf
    {
        bool m_bExtPotionBufUse;
        bool m_bDayChange;
        unsigned int m_dwEndPotionTime;
    public:
        CExtPotionBuf();
        void ctor_CExtPotionBuf();
        void CalcRemainTime(uint16_t wInx, bool bUse);
        void CheckPotionTime(struct CPlayer* pOne);
        bool IsExtPotionUse();
        void SednMsg_RemovePotionContEffect(uint16_t wPotionInx, uint16_t wIndex);
        void SendMsg_RemainBufUseTime(bool bUse, uint16_t wIndex, int nEndDay, int nEndHour, int nEndMin);
        void SetExtPotionBufUse(bool bUse);
        void SetExtPotionEndTime(unsigned int dwEndTime);
        void UseBuffPotion(struct CPlayer* pOne);
        ~CExtPotionBuf();
        void dtor_CExtPotionBuf();
    };    
    static_assert(ATF::checkSize<CExtPotionBuf, 8>(), "CExtPotionBuf");
END_ATF_NAMESPACE

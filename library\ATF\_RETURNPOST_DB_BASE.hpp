// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _RETURNPOST_DB_BASE
    {
        bool m_bUpdate;
        int m_nMax;
        int m_nCum;
        unsigned int m_RetSerials[30];
    public:
        void Init();
        _RETURNPOST_DB_BASE();
        void ctor__RETURNPOST_DB_BASE();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_RETURNPOST_DB_BASE, 129>(), "_RETURNPOST_DB_BASE");
END_ATF_NAMESPACE

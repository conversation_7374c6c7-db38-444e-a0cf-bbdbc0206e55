// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ACTRL_ACCESS_ENTRY_LISTA.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _ACTRL_PROPERTY_ENTRYA
    {
        char *lpProperty;
        _ACTRL_ACCESS_ENTRY_LISTA *pAccessEntryList;
        unsigned int fListFlags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

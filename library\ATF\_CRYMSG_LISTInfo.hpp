// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CRYMSG_LIST.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _CRYMSG_LISTInit2_ptr = void (WINAPIV*)(struct _CRYMSG_LIST*);
        using _CRYMSG_LISTInit2_clbk = void (WINAPIV*)(struct _CRYMSG_LIST*, _CRYMSG_LISTInit2_ptr);
        
        using _CRYMSG_LISTctor__CRYMSG_LIST4_ptr = void (WINAPIV*)(struct _CRYMSG_LIST*);
        using _CRYMSG_LISTctor__CRYMSG_LIST4_clbk = void (WINAPIV*)(struct _CRYMSG_LIST*, _CRYMSG_LISTctor__CRYMSG_LIST4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

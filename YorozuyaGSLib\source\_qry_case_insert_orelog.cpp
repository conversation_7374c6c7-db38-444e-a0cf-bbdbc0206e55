#include <_qry_case_insert_orelog.hpp>


START_ATF_NAMESPACE
    _qry_case_insert_orelog::_qry_case_insert_orelog()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_insert_orelog*);
        (org_ptr(0x1403fa190L))(this);
    };
    void _qry_case_insert_orelog::ctor__qry_case_insert_orelog()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_insert_orelog*);
        (org_ptr(0x1403fa190L))(this);
    };
END_ATF_NAMESPACE

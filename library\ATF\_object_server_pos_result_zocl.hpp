// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _object_server_pos_result_zocl
    {
        char byErrCode;
        char byObjKind;
        char byObjID;
        unsigned __int16 wObjIndex;
        unsigned int dwObjSerial;
        float fServerPos[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

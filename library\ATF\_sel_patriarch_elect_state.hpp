// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _sel_patriarch_elect_state
    {
        char szWorldName[33];
        unsigned int dwSerial;
        char byProcType;
        unsigned int dwVoteCnt[3];
        unsigned int dwNonVoteCnt[3];
        unsigned int dwHighGradeNum[3];
        unsigned int dwResetServerToken;
    public:
        _sel_patriarch_elect_state();
        void ctor__sel_patriarch_elect_state();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_sel_patriarch_elect_state, 84>(), "_sel_patriarch_elect_state");
END_ATF_NAMESPACE

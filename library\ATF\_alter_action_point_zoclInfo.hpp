// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_alter_action_point_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _alter_action_point_zoclctor__alter_action_point_zocl2_ptr = void (WINAPIV*)(struct _alter_action_point_zocl*);
        using _alter_action_point_zoclctor__alter_action_point_zocl2_clbk = void (WINAPIV*)(struct _alter_action_point_zocl*, _alter_action_point_zoclctor__alter_action_point_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_equip_up_item_lv_limit_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _equip_up_item_lv_limit_zoclctor__equip_up_item_lv_limit_zocl2_ptr = void (WINAPIV*)(struct _equip_up_item_lv_limit_zocl*);
        using _equip_up_item_lv_limit_zoclctor__equip_up_item_lv_limit_zocl2_clbk = void (WINAPIV*)(struct _equip_up_item_lv_limit_zocl*, _equip_up_item_lv_limit_zoclctor__equip_up_item_lv_limit_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

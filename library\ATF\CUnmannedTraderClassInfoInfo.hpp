// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderClassInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderClassInfoctor_CUnmannedTraderClassInfo2_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfo*, unsigned int);
        using CUnmannedTraderClassInfoctor_CUnmannedTraderClassInfo2_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfo*, unsigned int, CUnmannedTraderClassInfoctor_CUnmannedTraderClassInfo2_ptr);
        using CUnmannedTraderClassInfoCopy4_ptr = struct CUnmannedTraderClassInfo* (WINAPIV*)(struct CUnmannedTraderClassInfo*, struct CUnmannedTraderClassInfo*);
        using CUnmannedTraderClassInfoCopy4_clbk = struct CUnmannedTraderClassInfo* (WINAPIV*)(struct CUnmannedTraderClassInfo*, struct CUnmannedTraderClassInfo*, CUnmannedTraderClassInfoCopy4_ptr);
        using CUnmannedTraderClassInfoGetID6_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderClassInfo*);
        using CUnmannedTraderClassInfoGetID6_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderClassInfo*, CUnmannedTraderClassInfoGetID6_ptr);
        using CUnmannedTraderClassInfoGetTypeName8_ptr = char* (WINAPIV*)(struct CUnmannedTraderClassInfo*);
        using CUnmannedTraderClassInfoGetTypeName8_clbk = char* (WINAPIV*)(struct CUnmannedTraderClassInfo*, CUnmannedTraderClassInfoGetTypeName8_ptr);
        
        using CUnmannedTraderClassInfodtor_CUnmannedTraderClassInfo12_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfo*);
        using CUnmannedTraderClassInfodtor_CUnmannedTraderClassInfo12_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfo*, CUnmannedTraderClassInfodtor_CUnmannedTraderClassInfo12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

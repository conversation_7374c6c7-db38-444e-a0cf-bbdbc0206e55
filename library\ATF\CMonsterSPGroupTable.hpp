// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRecordData.hpp>
#include <_monster_sp_group.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMonsterSPGroupTable
    {
        unsigned int m_dwRecordNum;
        _monster_sp_group *m_pRecordData;
    public:
        CMonsterSPGroupTable();
        void ctor_CMonsterSPGroupTable();
        bool Create(struct CRecordData* pMonsterRecordData, struct CRecordData* pMonsterSPRecordData, struct CRecordData* pSkillRecordData, struct CRecordData* pForceRecordData, struct CRecordData* pClassSkillRecordData);
        struct _monster_sp_group* GetRecord(char* szCode);
        struct _monster_sp_group* GetRecord(unsigned int dwIndex);
        ~CMonsterSPGroupTable();
        void dtor_CMonsterSPGroupTable();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CMonsterSPGroupTable, 16>(), "CMonsterSPGroupTable");
END_ATF_NAMESPACE

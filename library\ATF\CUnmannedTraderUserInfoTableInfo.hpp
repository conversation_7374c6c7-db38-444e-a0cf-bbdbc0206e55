// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderUserInfoTable.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CUnmannedTraderUserInfoTableBuy2_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _unmannedtrader_buy_item_request_clzo*);
        using CUnmannedTraderUserInfoTableBuy2_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _unmannedtrader_buy_item_request_clzo*, CUnmannedTraderUserInfoTableBuy2_ptr);
        
        using CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*);
        using CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_ptr);
        using CUnmannedTraderUserInfoTableCancelRegist6_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _a_trade_clear_item_request_clzo*);
        using CUnmannedTraderUserInfoTableCancelRegist6_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _a_trade_clear_item_request_clzo*, CUnmannedTraderUserInfoTableCancelRegist6_ptr);
        using CUnmannedTraderUserInfoTableCheatCancelRegist8_ptr = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int, char);
        using CUnmannedTraderUserInfoTableCheatCancelRegist8_clbk = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int, char, CUnmannedTraderUserInfoTableCheatCancelRegist8_ptr);
        using CUnmannedTraderUserInfoTableCheckwIndexAndType10_ptr = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, char*);
        using CUnmannedTraderUserInfoTableCheckwIndexAndType10_clbk = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, char*, CUnmannedTraderUserInfoTableCheckwIndexAndType10_ptr);
        using CUnmannedTraderUserInfoTableClearLogLogOutState12_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*, unsigned int, char*, char*, unsigned int, unsigned int, unsigned int, unsigned int);
        using CUnmannedTraderUserInfoTableClearLogLogOutState12_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*, unsigned int, char*, char*, unsigned int, unsigned int, unsigned int, unsigned int, CUnmannedTraderUserInfoTableClearLogLogOutState12_ptr);
        using CUnmannedTraderUserInfoTableClearRequest14_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int);
        using CUnmannedTraderUserInfoTableClearRequest14_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int, CUnmannedTraderUserInfoTableClearRequest14_ptr);
        using CUnmannedTraderUserInfoTableCompleteBuy16_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char*, struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderUserInfoTableCompleteBuy16_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char*, struct CUnmannedTraderTradeInfo*, CUnmannedTraderUserInfoTableCompleteBuy16_ptr);
        using CUnmannedTraderUserInfoTableCompleteCancelRegist18_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char*);
        using CUnmannedTraderUserInfoTableCompleteCancelRegist18_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char*, CUnmannedTraderUserInfoTableCompleteCancelRegist18_ptr);
        using CUnmannedTraderUserInfoTableCompleteCreate20_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t);
        using CUnmannedTraderUserInfoTableCompleteCreate20_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, CUnmannedTraderUserInfoTableCompleteCreate20_ptr);
        using CUnmannedTraderUserInfoTableCompleteReRegist22_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*);
        using CUnmannedTraderUserInfoTableCompleteReRegist22_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*, CUnmannedTraderUserInfoTableCompleteReRegist22_ptr);
        using CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int, char*);
        using CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int, char*, CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_ptr);
        using CUnmannedTraderUserInfoTableCompleteRegist26_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char*);
        using CUnmannedTraderUserInfoTableCompleteRegist26_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char*, CUnmannedTraderUserInfoTableCompleteRegist26_ptr);
        using CUnmannedTraderUserInfoTableCompleteReprice28_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char*);
        using CUnmannedTraderUserInfoTableCompleteReprice28_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char*, CUnmannedTraderUserInfoTableCompleteReprice28_ptr);
        using CUnmannedTraderUserInfoTableCompleteSearch30_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char, char*);
        using CUnmannedTraderUserInfoTableCompleteSearch30_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char, char*, CUnmannedTraderUserInfoTableCompleteSearch30_ptr);
        using CUnmannedTraderUserInfoTableCompleteTimeOutClear32_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*);
        using CUnmannedTraderUserInfoTableCompleteTimeOutClear32_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*, CUnmannedTraderUserInfoTableCompleteTimeOutClear32_ptr);
        using CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*);
        using CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*, CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_ptr);
        using CUnmannedTraderUserInfoTableCompleteUpdateState36_ptr = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, unsigned int, unsigned int, char);
        using CUnmannedTraderUserInfoTableCompleteUpdateState36_clbk = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, unsigned int, unsigned int, char, CUnmannedTraderUserInfoTableCompleteUpdateState36_ptr);
        using CUnmannedTraderUserInfoTableDestroy38_ptr = void (WINAPIV*)();
        using CUnmannedTraderUserInfoTableDestroy38_clbk = void (WINAPIV*)(CUnmannedTraderUserInfoTableDestroy38_ptr);
        using CUnmannedTraderUserInfoTableFind40_ptr = struct CUnmannedTraderUserInfo* (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, unsigned int);
        using CUnmannedTraderUserInfoTableFind40_clbk = struct CUnmannedTraderUserInfo* (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, unsigned int, CUnmannedTraderUserInfoTableFind40_ptr);
        using CUnmannedTraderUserInfoTableFindByIndex42_ptr = struct CUnmannedTraderUserInfo* (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t);
        using CUnmannedTraderUserInfoTableFindByIndex42_clbk = struct CUnmannedTraderUserInfo* (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, CUnmannedTraderUserInfoTableFindByIndex42_ptr);
        using CUnmannedTraderUserInfoTableFindUser44_ptr = struct CUnmannedTraderUserInfo* (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int);
        using CUnmannedTraderUserInfoTableFindUser44_clbk = struct CUnmannedTraderUserInfo* (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int, CUnmannedTraderUserInfoTableFindUser44_ptr);
        using CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_ptr = CUnmannedTraderItemState::STATE (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, unsigned int, unsigned int, struct CPlayer**);
        using CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_clbk = CUnmannedTraderItemState::STATE (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, unsigned int, unsigned int, struct CPlayer**, CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_ptr);
        using CUnmannedTraderUserInfoTableGetMaxRegistCnt48_ptr = char (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int);
        using CUnmannedTraderUserInfoTableGetMaxRegistCnt48_clbk = char (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int, CUnmannedTraderUserInfoTableGetMaxRegistCnt48_ptr);
        using CUnmannedTraderUserInfoTableGetRegItemInfo50_ptr = struct CUnmannedTraderRegistItemInfo* (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int);
        using CUnmannedTraderUserInfoTableGetRegItemInfo50_clbk = struct CUnmannedTraderRegistItemInfo* (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int, CUnmannedTraderUserInfoTableGetRegItemInfo50_ptr);
        using CUnmannedTraderUserInfoTableInit52_ptr = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*);
        using CUnmannedTraderUserInfoTableInit52_clbk = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, CUnmannedTraderUserInfoTableInit52_ptr);
        using CUnmannedTraderUserInfoTableInstance54_ptr = struct CUnmannedTraderUserInfoTable* (WINAPIV*)();
        using CUnmannedTraderUserInfoTableInstance54_clbk = struct CUnmannedTraderUserInfoTable* (WINAPIV*)(CUnmannedTraderUserInfoTableInstance54_ptr);
        using CUnmannedTraderUserInfoTableLoad56_ptr = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, uint16_t, unsigned int, struct _TRADE_DB_BASE*);
        using CUnmannedTraderUserInfoTableLoad56_clbk = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, uint16_t, unsigned int, struct _TRADE_DB_BASE*, CUnmannedTraderUserInfoTableLoad56_ptr);
        using CUnmannedTraderUserInfoTableLog58_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*);
        using CUnmannedTraderUserInfoTableLog58_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*, CUnmannedTraderUserInfoTableLog58_ptr);
        using CUnmannedTraderUserInfoTableLogOut60_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int);
        using CUnmannedTraderUserInfoTableLogOut60_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, unsigned int, CUnmannedTraderUserInfoTableLogOut60_ptr);
        using CUnmannedTraderUserInfoTableModifyPrice62_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _a_trade_adjust_price_request_clzo*);
        using CUnmannedTraderUserInfoTableModifyPrice62_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _a_trade_adjust_price_request_clzo*, CUnmannedTraderUserInfoTableModifyPrice62_ptr);
        using CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, struct _qry_case_unmandtrader_buy_update_wait*);
        using CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, struct _qry_case_unmandtrader_buy_update_wait*, CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_ptr);
        using CUnmannedTraderUserInfoTableReRegist66_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _unmannedtrader_re_regist_request_clzo*);
        using CUnmannedTraderUserInfoTableReRegist66_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _unmannedtrader_re_regist_request_clzo*, CUnmannedTraderUserInfoTableReRegist66_ptr);
        using CUnmannedTraderUserInfoTableRegist68_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _a_trade_reg_item_request_clzo*);
        using CUnmannedTraderUserInfoTableRegist68_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _a_trade_reg_item_request_clzo*, CUnmannedTraderUserInfoTableRegist68_ptr);
        using CUnmannedTraderUserInfoTableSearch70_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _unmannedtrader_search_list_request_clzo*);
        using CUnmannedTraderUserInfoTableSearch70_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, uint16_t, char, struct _unmannedtrader_search_list_request_clzo*, CUnmannedTraderUserInfoTableSearch70_ptr);
        using CUnmannedTraderUserInfoTableServiceLog72_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*);
        using CUnmannedTraderUserInfoTableServiceLog72_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char*, CUnmannedTraderUserInfoTableServiceLog72_ptr);
        using CUnmannedTraderUserInfoTableSetLogger74_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, struct CLogFile*, struct CLogFile*);
        using CUnmannedTraderUserInfoTableSetLogger74_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, struct CLogFile*, struct CLogFile*, CUnmannedTraderUserInfoTableSetLogger74_ptr);
        using CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_ptr = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, struct _qry_case_unmandtrader_buy_update_wait*, struct CUnmannedTraderUserInfo**, struct CPlayer**);
        using CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_clbk = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, struct _qry_case_unmandtrader_buy_update_wait*, struct CUnmannedTraderUserInfo**, struct CPlayer**, CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_ptr);
        using CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char);
        using CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, char, CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_ptr);
        using CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_ptr = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, struct CPlayer*, struct CUnmannedTraderUserInfo*, int64_t, struct _qry_case_unmandtrader_buy_update_wait::__list*, struct _unmannedtrader_buy_item_result_zocl::__list*, struct _qry_case_unmandtrader_buy_update_complete::__list*, char*, unsigned int*, struct CUnmannedTraderTradeInfo*);
        using CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_clbk = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, struct CPlayer*, struct CUnmannedTraderUserInfo*, int64_t, struct _qry_case_unmandtrader_buy_update_wait::__list*, struct _unmannedtrader_buy_item_result_zocl::__list*, struct _qry_case_unmandtrader_buy_update_complete::__list*, char*, unsigned int*, struct CUnmannedTraderTradeInfo*, CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_ptr);
        using CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_ptr = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, struct _qry_case_unmandtrader_buy_update_complete::__list*, char*);
        using CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_clbk = bool (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, char, struct _qry_case_unmandtrader_buy_update_complete::__list*, char*, CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_ptr);
        
        using CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_ptr = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*);
        using CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_clbk = void (WINAPIV*)(struct CUnmannedTraderUserInfoTable*, CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

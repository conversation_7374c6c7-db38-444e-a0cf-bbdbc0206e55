// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _personal_automine_selore_zocl
    {
        unsigned int dwObjSerial;
        char bySelectOre;
    public:
        _personal_automine_selore_zocl();
        void ctor__personal_automine_selore_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_personal_automine_selore_zocl, 5>(), "_personal_automine_selore_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _ASSEMBLY_FILE_DETAILED_INFORMATION
    {
        unsigned int ulFlags;
        unsigned int ulFilenameLength;
        unsigned int ulPathLength;
        const wchar_t *lpFileName;
        const wchar_t *lpFilePath;
    };
END_ATF_NAMESPACE

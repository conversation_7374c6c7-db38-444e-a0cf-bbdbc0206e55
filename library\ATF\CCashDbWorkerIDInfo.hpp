// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerID.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerIDctor_CCashDbWorkerID2_ptr = void (WINAPIV*)(struct CCashDbWorkerID*);
        using CCashDbWorkerIDctor_CCashDbWorkerID2_clbk = void (WINAPIV*)(struct CCashDbWorkerID*, CCashDbWorkerIDctor_CCashDbWorkerID2_ptr);
        using CCashDbWorkerIDGetUseCashQueryStr4_ptr = void (WINAPIV*)(struct CCashDbWorkerID*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDbWorkerIDGetUseCashQueryStr4_clbk = void (WINAPIV*)(struct CCashDbWorkerID*, struct _param_cash_update*, int, char*, uint64_t, CCashDbWorkerIDGetUseCashQueryStr4_ptr);
        
        using CCashDbWorkerIDdtor_CCashDbWorkerID9_ptr = void (WINAPIV*)(struct CCashDbWorkerID*);
        using CCashDbWorkerIDdtor_CCashDbWorkerID9_clbk = void (WINAPIV*)(struct CCashDbWorkerID*, CCashDbWorkerIDdtor_CCashDbWorkerID9_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

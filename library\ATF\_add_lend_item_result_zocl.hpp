// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _add_lend_item_result_zocl
    {
        char byTblCode;
        unsigned __int16 wItemIdx;
        unsigned int dwDur;
        unsigned int dwUp;
        unsigned int dwItemSerial;
        char byCsMethod;
        unsigned int dwT;
    public:
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_add_lend_item_result_zocl, 20>(), "_add_lend_item_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHashMapPtrPool.hpp>
#include <CNationSettingFactory.hpp>


START_ATF_NAMESPACE
    struct CNationSettingFactoryGroup
    {
        CHashMapPtrPool<int,CNationSettingFactory> m_kPool;
    public:
        CNationSettingFactoryGroup();
        void ctor_CNationSettingFactoryGroup();
        struct CNationSettingData* Create(int iNationCode, char* szNationCodeStr, bool bServiceMode);
        int Init();
        ~CNationSettingFactoryGroup();
        void dtor_CNationSettingFactoryGroup();
    };    
    //static_assert(ATF::checkSize<CNationSettingFactoryGroup, 128>(), "CNationSettingFactoryGroup");
END_ATF_NAMESPACE

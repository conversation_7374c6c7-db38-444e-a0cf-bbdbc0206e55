// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBilling.hpp>
#include <CNationSettingData.hpp>
#include <CPlayer.hpp>
#include <CashDbWorker.hpp>
#include <_CashShop_str_fld.hpp>
#include <_NameTxt_fld.hpp>


START_ATF_NAMESPACE
    struct  CNationSettingDataCN : CNationSettingData
    {
    public:
        CNationSettingDataCN();
        void ctor_CNationSettingDataCN();
        struct CBilling* CreateBilling();
        void CreateComplete(struct CPlayer* pOne);
        struct CashDbWorker* CreateWorker();
        int GetCashItemPrice(struct _CashShop_str_fld* pFld);
        char* GetItemName(struct _NameTxt_fld* pFld);
        int Init();
        void Loop();
        void NetClose(struct CPlayer* pOne);
        bool ReadSystemPass();
    };
END_ATF_NAMESPACE

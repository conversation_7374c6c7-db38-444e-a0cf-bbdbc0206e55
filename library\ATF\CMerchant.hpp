// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>
#include <CGameObject.hpp>
#include <CItemStore.hpp>
#include <_npc_create_setdata.hpp>
#include <_object_id.hpp>


START_ATF_NAMESPACE
    struct  CMerchant : CCharacter
    {
        CItemStore *m_pItemStore;
        char m_byRaceCode;
        unsigned int m_dwLastDestroyTime;
        int m_nLeftTicketNum[2];
    public:
        CMerchant();
        void ctor_CMerchant();
        bool Create(struct _npc_create_setdata* pData);
        bool Destroy(struct CGameObject* pAttObj);
        int GetFireTol();
        static unsigned int GetNewMonSerial();
        char* GetObjName();
        int GetObjRace();
        int GetSoilTol();
        char* GetStoreDummyName();
        int GetWaterTol();
        int GetWindTol();
        bool Init(struct _object_id* pID);
        void Loop();
        void OutOfSec();
        void SendMsg_Create();
        void SendMsg_Destroy();
        void SendMsg_FixPosition(int n);
        void SendMsg_Move();
        void SendMsg_RealFixPosition(bool bCircle);
        void SendMsg_RealMovePoint(int n);
        void SendMsg_TransShipTicketNumInform(int n);
        ~CMerchant();
        void dtor_CMerchant();
    };
END_ATF_NAMESPACE

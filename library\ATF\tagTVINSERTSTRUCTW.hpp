// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$59301C59F86822631A537104701ECFD0.hpp>


START_ATF_NAMESPACE
    struct tagTVINSERTSTRUCTW
    {
        struct _TREEITEM *hParent;
        struct _TREEITEM *hInsertAfter;
        $59301C59F86822631A537104701ECFD0 ___u2;
    };
END_ATF_NAMESPACE

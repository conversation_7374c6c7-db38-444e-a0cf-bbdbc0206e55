// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct UNWIND_INFO
    {
        char Ver3_Flags;
        char PrologSize;
        char CntUnwindCodes;
        char <PERSON>Reg_FrRegOff;
    };    
    static_assert(ATF::checkSize<UNWIND_INFO, 4>(), "UNWIND_INFO");
END_ATF_NAMESPACE

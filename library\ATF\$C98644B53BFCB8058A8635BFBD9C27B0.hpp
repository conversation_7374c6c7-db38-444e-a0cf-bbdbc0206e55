// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $C98644B53BFCB8058A8635BFBD9C27B0
    {
        unsigned int dwRuntimeNumber;
        int bRealBug;
        void *pvReturnAddress;
        char *pbDebuggerPresent;
        const wchar_t *pwRuntimeMessage;
    };
END_ATF_NAMESPACE

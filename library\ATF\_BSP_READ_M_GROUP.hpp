// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _BSP_READ_M_GROUP
    {
        unsigned __int16 attr;
        unsigned __int16 face_num;
        unsigned int face_start_id;
        __int16 mtl_id;
        __int16 lgt_id;
        __int16 bb_min[3];
        __int16 bb_max[3];
        float pos[3];
        float scale;
        unsigned __int16 object_id;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

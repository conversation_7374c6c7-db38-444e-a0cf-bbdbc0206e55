// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CLID.hpp>
#include <_GLBID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _enter_world_request_wrac
    {
        _GLBID gidGlobal;
        _CLID idLocal;
        unsigned int ulConnectIP;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_enter_world_request_wrac, 18>(), "_enter_world_request_wrac");
END_ATF_NAMESPACE

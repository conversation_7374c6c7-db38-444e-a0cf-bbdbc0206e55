// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_devicemodeW.hpp>



START_ATF_NAMESPACE
    struct _PRINTER_INFO_2W
    {
        wchar_t *pServerName;
        wchar_t *pPrinterName;
        wchar_t *pShareName;
        wchar_t *pPortName;
        wchar_t *pDriverName;
        wchar_t *pComment;
        wchar_t *pLocation;
        _devicemodeW *pDevMode;
        wchar_t *pSepFile;
        wchar_t *pPrintProcessor;
        wchar_t *pDatatype;
        wchar_t *pParameters;
        void *pSecurityDescriptor;
        unsigned int Attributes;
        unsigned int Priority;
        unsigned int DefaultPriority;
        unsigned int StartTime;
        unsigned int UntilTime;
        unsigned int Status;
        unsigned int cJobs;
        unsigned int AveragePPM;
    };
END_ATF_NAMESPACE

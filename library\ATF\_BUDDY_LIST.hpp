// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetIndexList.hpp>


START_ATF_NAMESPACE
    struct _BUDDY_LIST
    {
        struct __list
        {
            unsigned int dwSerial;
            char wszName[17];
            struct CPlayer *pPtr;
        public:
            void ON(char* pwszName, struct CPlayer* ptr);
            __list();
            void ctor___list();
            bool fill();
            void init();
        };
        __list m_List[50];
        CNetIndexList m_LastApply;
    public:
        int GetBuddyNum();
        struct __list* GetEmptyData();
        void Init();
        bool IsBuddy(unsigned int dwSerial);
        bool IsPushLastApply(unsigned int dwDstSerial);
        int PopBuddy(unsigned int dwSerial, struct CPlayer** ppPoper);
        void PopLastApplyTemp(unsigned int dwDstSerial);
        int PushBuddy(unsigned int dwSerial, char* pwszName, struct CPlayer* pPtr);
        void PushLastApplyTemp(unsigned int dwDstSerial);
        bool SearchBuddyLogin(struct CPlayer* pLoger, unsigned int dwSerial, char* pwszName);
        bool SearchBuddyLogoff(unsigned int dwSerial);
        _BUDDY_LIST();
        void ctor__BUDDY_LIST();
        ~_BUDDY_LIST();
        void dtor__BUDDY_LIST();
    };    
    static_assert(ATF::checkSize<_BUDDY_LIST, 1760>(), "_BUDDY_LIST");
END_ATF_NAMESPACE

#include <_combine_ex_item_result_zocl.hpp>


START_ATF_NAMESPACE
    _combine_ex_item_result_zocl::_combine_ex_item_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _combine_ex_item_result_zocl*);
        (org_ptr(0x1400b82b0L))(this);
    };
    void _combine_ex_item_result_zocl::ctor__combine_ex_item_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _combine_ex_item_result_zocl*);
        (org_ptr(0x1400b82b0L))(this);
    };
    void _combine_ex_item_result_zocl::__item::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _combine_ex_item_result_zocl::__item*);
        (org_ptr(0x1400b83d0L))(this);
    };
    _combine_ex_item_result_zocl::__item::__item()
    {
        using org_ptr = void (WINAPIV*)(struct _combine_ex_item_result_zocl::__item*);
        (org_ptr(0x1400b8380L))(this);
    };
    void _combine_ex_item_result_zocl::__item::ctor___item()
    {
        using org_ptr = void (WINAPIV*)(struct _combine_ex_item_result_zocl::__item*);
        (org_ptr(0x1400b8380L))(this);
    };
    void _combine_ex_item_result_zocl::_Result_ItemList_Buff::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _combine_ex_item_result_zocl::_Result_ItemList_Buff*);
        (org_ptr(0x1400b8430L))(this);
    };
    _combine_ex_item_result_zocl::_Result_ItemList_Buff::_Result_ItemList_Buff()
    {
        using org_ptr = void (WINAPIV*)(struct _combine_ex_item_result_zocl::_Result_ItemList_Buff*);
        (org_ptr(0x1400b8300L))(this);
    };
    void _combine_ex_item_result_zocl::_Result_ItemList_Buff::ctor__Result_ItemList_Buff()
    {
        using org_ptr = void (WINAPIV*)(struct _combine_ex_item_result_zocl::_Result_ItemList_Buff*);
        (org_ptr(0x1400b8300L))(this);
    };
END_ATF_NAMESPACE

[LootExchange]
; Enable the loot exchange addon
Activated=1

; Use custom mappings instead of database money types
; 0 = Use database money types (original behavior)
; 1 = Use custom mappings defined below
ExchangeAll=1

; Enable logging for debugging
EnableLogging=1
LogFilePath=./YorozuyaGS/Logs/LootExchange.log

[AdvancedSettings]
; Enable custom mapping system
UseCustomMappings=1

; Exchange rate modifier (100 = normal, 150 = 50% bonus)
ExchangeRateModifier=120

; Minimum player level to use loot exchange
MinimumPlayerLevel=10

[ItemMappings]
; Specific item mappings - highest priority
; Format: ItemCode=CurrencyType:Value
; CurrencyType: 0=CP, 1=Gold, 2=PvP_Point, 3=PvP_Point_2, 4=Processing_Point, 5=Hunter_Point, 6=Gold_Point

; Specific high-value items exchange for Gold
1:500=1:5000                    ; Weapon table 1, index 500 -> 5000 Gold
1:501=1:7500                    ; Weapon table 1, index 501 -> 7500 Gold
2:300=1:3000                    ; Armor table 2, index 300 -> 3000 Gold

; Rare materials exchange for Processing Points
15:100=4:2000                   ; Resource table 15, index 100 -> 2000 Processing Points
15:101=4:2500                   ; Resource table 15, index 101 -> 2500 Processing Points
15:102=4:3000                   ; Resource table 15, index 102 -> 3000 Processing Points

; PvP-related items exchange for PvP Points
20:50=2:1000                    ; Special table 20, index 50 -> 1000 PvP Points
20:51=2:1500                    ; Special table 20, index 51 -> 1500 PvP Points

; Hunter quest items exchange for Hunter Points
21:10=5:500                     ; Quest table 21, index 10 -> 500 Hunter Points
21:11=5:750                     ; Quest table 21, index 11 -> 750 Hunter Points

; Premium items exchange for Gold Points
25:1=6:10000                    ; Premium table 25, index 1 -> 10000 Gold Points

; Use database values but force currency type (Value=0 means use DB value)
1:600=0:0                       ; Weapon table 1, index 600 -> CP (database value)
2:400=1:0                       ; Armor table 2, index 400 -> Gold (database value)

[TableMappings]
; Map entire item tables to specific currency types
; Format: TableCode=CurrencyType:ValueMultiplier
; ValueMultiplier: 100=normal, 150=50% bonus, 50=50% penalty

; All weapons exchange for CP at 110% rate
1=0:110

; All armor exchanges for Gold at 120% rate
2=1:120
3=1:120
4=1:120
5=1:120
6=1:120
7=1:120
8=1:120

; All consumables exchange for CP at normal rate
9=0:100
10=0:100

; All resources exchange for Processing Points at 150% rate
15=4:150
16=4:150
17=4:150

; All accessories exchange for Gold at 130% rate
18=1:130
19=1:130

[CategoryMappings]
; Map categories of items to currency types
; Format: CategoryName=CurrencyType:ValueMultiplier:TableList

; Equipment category - all equipment exchanges for CP
Equipment=0:105:1,2,3,4,5,6,7,8

; Consumables category - potions and bullets exchange for Gold
Consumables=1:110:9,10

; Resources category - all materials exchange for Processing Points
Resources=4:140:15,16,17

; Accessories category - rings and amulets exchange for Gold
Accessories=1:125:18,19

; Special items category - maps and quest items exchange for Hunter Points
Special=5:100:20,21,22

[ConditionalMappings]
; Advanced conditional mappings (future feature - placeholder)
; Format: Condition=CurrencyType:Value
; Examples:
; MinLevel=50&MaxLevel=60=2:2000
; MinValue=10000=1:0
; PlayerRace=0=0:0

[ExclusionList]
; Items that should NEVER be exchanged
; Format: ItemCode or Table:Index

; Never exchange these specific rare items
1:999                           ; Legendary weapon
2:999                           ; Legendary armor
25:999                          ; Ultra rare premium item

; Never exchange any items from table 30 (example: quest items)
30:*

; Never exchange items matching patterns
rare_*                          ; Any item code starting with "rare_"
quest_*                         ; Any item code starting with "quest_"

[PriorityOverrides]
; Override currency priority for specific items
; Format: ItemCode=Priority1,Priority2,Priority3...
; Use currency type numbers: 0=CP, 1=Gold, 2=PvP, 3=PvP2, 4=Processing, 5=Hunter, 6=GoldPoint

; For weapons, try Gold first, then CP
1:*=1,0,4,5,6,2,3

; For armor, try Gold first, then CP
2:*=1,0,4,5,6,2,3
3:*=1,0,4,5,6,2,3
4:*=1,0,4,5,6,2,3
5:*=1,0,4,5,6,2,3
6:*=1,0,4,5,6,2,3
7:*=1,0,4,5,6,2,3
8:*=1,0,4,5,6,2,3

; For resources, try Processing Points first
15:*=4,5,6,0,1,2,3
16:*=4,5,6,0,1,2,3
17:*=4,5,6,0,1,2,3

; For PvP items, try PvP Points first
20:*=2,3,1,0,4,5,6

; For quest items, try Hunter Points first
21:*=5,4,6,0,1,2,3

[ItemTypeSettings]
; Control which item types can be exchanged
ExchangeEquipment=1
ExchangeConsumables=1
ExchangeResources=1
ExchangeSpecialItems=1
ExchangeForceItems=1
ExchangeAccessories=1

[CurrencySettings]
; Enable/disable specific currency types
EnableCP=1
EnableGold=1
EnablePvPPoint=1
EnablePvPCashBag=1
EnableProcessingPoint=1
EnableHunterPoint=1
EnableGoldPoint=1

[RaceSpecificSettings]
; Race-specific settings for resource items
UseRaceSpecificPricing=1
DefaultRace=0

; CONFIGURATION EXAMPLES AND EXPLANATIONS:
;
; 1. SPECIFIC ITEM MAPPING:
;    1:500=1:5000
;    - Item from table 1, index 500 exchanges for exactly 5000 Gold
;    - Highest priority - overrides all other settings
;
; 2. TABLE MAPPING:
;    1=0:110
;    - All items from table 1 (weapons) exchange for CP at 110% of database value
;    - Medium priority - applies if no specific item mapping exists
;
; 3. CATEGORY MAPPING:
;    Equipment=0:105:1,2,3,4,5,6,7,8
;    - All equipment (tables 1-8) exchanges for CP at 105% rate
;    - Lower priority - applies if no table or item mapping exists
;
; 4. PRIORITY OVERRIDE:
;    1:*=1,0,4,5,6,2,3
;    - For all weapons (table 1), try Gold first, then CP, then other currencies
;    - Affects which currency type is chosen when multiple are available
;
; 5. EXCLUSION:
;    1:999
;    - Item from table 1, index 999 will never be exchanged
;    - Highest priority - completely prevents exchange
;
; PRIORITY ORDER (highest to lowest):
; 1. Exclusion List (prevents exchange entirely)
; 2. Specific Item Mappings
; 3. Table Mappings  
; 4. Category Mappings
; 5. Default database behavior
;
; CURRENCY TYPE REFERENCE:
; 0 = CP (Dalant) - Main currency
; 1 = Gold - Secondary currency
; 2 = PvP Point - PvP currency
; 3 = PvP Point 2 - PvP cash bag
; 4 = Processing Point - Crafting currency
; 5 = Hunter Point - Hunting currency
; 6 = Gold Point - Premium currency

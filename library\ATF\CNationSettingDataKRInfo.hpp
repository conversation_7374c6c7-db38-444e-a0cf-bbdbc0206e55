// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataKR.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataKRctor_CNationSettingDataKR2_ptr = void (WINAPIV*)(struct CNationSettingDataKR*);
        using CNationSettingDataKRctor_CNationSettingDataKR2_clbk = void (WINAPIV*)(struct CNationSettingDataKR*, CNationSettingDataKRctor_CNationSettingDataKR2_ptr);
        using CNationSettingDataKRCreateBilling4_ptr = struct CBilling* (WINAPIV*)(struct CNationSettingDataKR*);
        using CNationSettingDataKRCreateBilling4_clbk = struct CBilling* (WINAPIV*)(struct CNationSettingDataKR*, CNationSettingDataKRCreateBilling4_ptr);
        using CNationSettingDataKRCreateWorker6_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataKR*);
        using CNationSettingDataKRCreateWorker6_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataKR*, CNationSettingDataKRCreateWorker6_ptr);
        using CNationSettingDataKRGetCashItemPrice8_ptr = int (WINAPIV*)(struct CNationSettingDataKR*, struct _CashShop_str_fld*);
        using CNationSettingDataKRGetCashItemPrice8_clbk = int (WINAPIV*)(struct CNationSettingDataKR*, struct _CashShop_str_fld*, CNationSettingDataKRGetCashItemPrice8_ptr);
        using CNationSettingDataKRGetItemName10_ptr = char* (WINAPIV*)(struct CNationSettingDataKR*, struct _NameTxt_fld*);
        using CNationSettingDataKRGetItemName10_clbk = char* (WINAPIV*)(struct CNationSettingDataKR*, struct _NameTxt_fld*, CNationSettingDataKRGetItemName10_ptr);
        using CNationSettingDataKRInit12_ptr = int (WINAPIV*)(struct CNationSettingDataKR*);
        using CNationSettingDataKRInit12_clbk = int (WINAPIV*)(struct CNationSettingDataKR*, CNationSettingDataKRInit12_ptr);
        using CNationSettingDataKRIsNormalChar14_ptr = bool (WINAPIV*)(struct CNationSettingDataKR*, wchar_t);
        using CNationSettingDataKRIsNormalChar14_clbk = bool (WINAPIV*)(struct CNationSettingDataKR*, wchar_t, CNationSettingDataKRIsNormalChar14_ptr);
        using CNationSettingDataKRReadSystemPass16_ptr = bool (WINAPIV*)(struct CNationSettingDataKR*);
        using CNationSettingDataKRReadSystemPass16_clbk = bool (WINAPIV*)(struct CNationSettingDataKR*, CNationSettingDataKRReadSystemPass16_ptr);
        using CNationSettingDataKRSendCashDBDSNRequest18_ptr = void (WINAPIV*)(struct CNationSettingDataKR*);
        using CNationSettingDataKRSendCashDBDSNRequest18_clbk = void (WINAPIV*)(struct CNationSettingDataKR*, CNationSettingDataKRSendCashDBDSNRequest18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

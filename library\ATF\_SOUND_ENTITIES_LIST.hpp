// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _SOUND_ENTITIES_LIST
    {
        unsigned __int16 ID;
        unsigned __int16 EventTime;
        unsigned int Flag;
        float Scale;
        float Attn;
        float Pos[3];
        float BoxScale[3];
        float BoxAttn;
        float BoxRotX;
        float BoxRotY;
        float NextPlayTime;
        float BBMin[3];
        float BBMax[3];
        float mInvMat[4][4];
    public:
        float GetBoxIntensity(float* arg_0);
        float GetPan(float* arg_0);
        float GetVolume(float* arg_0);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CLuaCommand.hpp>
#include <CLuaCommandEx.hpp>
#include <CLuaScript.hpp>
#include <US__CArrayEx.hpp>


START_ATF_NAMESPACE
    struct CLuaScriptMgr
    {
        struct lua_State *m_MasterState;
        US::CArrayEx<CLuaScript,CLuaScript::_State> m_ChildScriptArEx;
        US::CArrayEx<CLuaCommandEx,CLuaCommandEx::_State> m_ExternCommandArEx;
        CLogFile m_LogScriptError;
        CLogFile m_LogScriptState;
    public:
        bool AttachLuaScript(struct CLuaScript* pScript, struct CLuaCommand* pAttachCommand);
        CLuaScriptMgr();
        void ctor_CLuaScriptMgr();
        static void Destroy();
        bool DetackLuaScript(struct CLuaScript* pScript);
        struct CLogFile* GetErrorLogFile();
        struct CLogFile* GetStateLogFile();
        bool InitSDM();
        static struct CLuaScriptMgr* Instance();
        void LogStack(struct CLuaScript* pScript);
        void Loop();
        struct CLuaCommandEx* NewCommandEx();
        struct CLuaScript* NewScript();
        void RemoveCommandEx(struct CLuaCommandEx* pCommand);
        void RemoveScript(struct CLuaScript* pScript);
        struct CLuaScript* SearchScript(char* strFileName);
        struct CLuaScript* SearchScriptFromLuaState(lua_State* state);
        bool _Regist_Novus();
        ~CLuaScriptMgr();
        void dtor_CLuaScriptMgr();
    };
END_ATF_NAMESPACE

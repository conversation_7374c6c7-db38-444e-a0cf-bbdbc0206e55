// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGameStatisticsVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CGameStatistics
    {
        struct _map
        {
            char *pMapName;
            unsigned int dwMaxHourPerMap_Hour;
        public:
            _map();
            void ctor__map();
        };
        struct _DAY
        {
            unsigned int dwEderEnter_Evt;
            unsigned int dwMaxUserHour_Hour;
            unsigned int dwMaxUser_Hour;
            _map MaxHourPerMap_Hour[100];
            unsigned int dwDropStdItem_Evt;
            unsigned int dwDropRareItem_Evt;
            unsigned int dw4MuUpgradeSucc_Evt;
            unsigned int dw4EunUpgradeSucc_Evt;
            unsigned int dw4JaUpgradeSucc_Evt;
            unsigned int dw5MuUpgradeSucc_Evt;
            unsigned int dw5EunUpgradeSucc_Evt;
            unsigned int dw5JaUpgradeSucc_Evt;
            unsigned int dwDaePokUse_Evt;
        public:
            _DAY();
            void ctor__DAY();
            void init();
        };
        CGameStatisticsVtbl *vfptr;
        _DAY m_day;
    public:
        CGameStatistics();
        void ctor_CGameStatistics();
        void ConvertDay(char* pszWorldName);
        struct _DAY* CurWriteData();
        void Init();
        void WriteDayData(char* pszWorldName);
        ~CGameStatistics();
        void dtor_CGameStatistics();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CGameStatistics, 1664>(), "CGameStatistics");
END_ATF_NAMESPACE

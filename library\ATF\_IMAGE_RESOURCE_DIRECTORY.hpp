// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_RESOURCE_DIRECTORY
    {
        unsigned int Characteristics;
        unsigned int TimeDateStamp;
        unsigned __int16 MajorVersion;
        unsigned __int16 MinorVersion;
        unsigned __int16 NumberOfNamedEntries;
        unsigned __int16 NumberOfIdEntries;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTimeSpan.hpp>
#include <_FILETIME.hpp>
#include <_SYSTEMTIME.hpp>
#include <tm.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct CTime
        {
            __int64 m_time;
        public:
            CTime(struct _FILETIME* fileTime, int nDST);
            void ctor_CTime(struct _FILETIME* fileTime, int nDST);
            CTime(struct _SYSTEMTIME* sysTime, int nDST);
            void ctor_CTime(struct _SYSTEMTIME* sysTime, int nDST);
            CTime(int64_t time);
            void ctor_CTime(int64_t time);
            CTime(int nYear, int nMonth, int nDay, int nHour, int nMin, int nSec, int nDST);
            void ctor_CTime(int nYear, int nMonth, int nDay, int nHour, int nMin, int nSec, int nDST);
            CTime(uint16_t wDosDate, uint16_t wDosTime, int nDST);
            void ctor_CTime(uint16_t wDosDate, uint16_t wDosTime, int nDST);
            CTime();
            void ctor_CTime();
            bool GetAsSystemTime(struct _SYSTEMTIME* timeDest);
            int GetDay();
            int GetDayOfWeek();
            struct tm* GetGmtTm(struct tm* ptm);
            int GetHour();
            struct tm* GetLocalTm(struct tm* ptm);
            int GetMinute();
            int GetMonth();
            int GetSecond();
            static struct CTime* GetTickCount(struct CTime* result);
            int64_t GetTime();
            int GetYear();
            static int IsValidFILETIME(struct _FILETIME* fileTime);
        };    
        static_assert(ATF::checkSize<ATL::CTime, 8>(), "ATL::CTime");
    }; // end namespace ATL
END_ATF_NAMESPACE

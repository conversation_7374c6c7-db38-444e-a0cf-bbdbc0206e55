// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_safe_dummy.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _safe_dummySetDummy2_ptr = bool (WINAPIV*)(struct _safe_dummy*, struct _dummy_position*);
        using _safe_dummySetDummy2_clbk = bool (WINAPIV*)(struct _safe_dummy*, struct _dummy_position*, _safe_dummySetDummy2_ptr);
        
        using _safe_dummyctor__safe_dummy4_ptr = void (WINAPIV*)(struct _safe_dummy*);
        using _safe_dummyctor__safe_dummy4_clbk = void (WINAPIV*)(struct _safe_dummy*, _safe_dummyctor__safe_dummy4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__list.hpp>
#include <std__vector.hpp>
#include <stdext___Hmap_traits.hpp>


START_ATF_NAMESPACE
    namespace stdext
    {
        template<typename _Kty, // key type
            typename _Ty,       // mapped type
            typename _Tr,       // comparator predicate type
            typename _Alloc,    // actual allocator type (should be value allocator)
            bool _Mfl>
            struct  _Hash : _Hmap_traits<_Kty, _Ty, _Tr, _Alloc, _Mfl>
        {
            using Myit = typename std::list<std::pair<_Kty, _<PERSON>>, _Alloc>::_Iterator;

            std::list<std::pair<_Kty const, _Ty>, _Alloc> _List;
            std::vector<Myit> _Vec;
            unsigned __int64 _Mask;
            unsigned __int64 _Maxidx;
        };
    }; // end namespace stdext
END_ATF_NAMESPACE

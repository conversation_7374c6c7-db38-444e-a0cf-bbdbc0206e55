// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_AVATOR_DATA.hpp>


START_ATF_NAMESPACE
    struct _qry_case_update_data_for_post_send
    {
        unsigned int dwSerial;
        unsigned int dwGlod;
        _AVATOR_DATA *pNewData;
        _AVATOR_DATA *pOldData;
    public:
        _qry_case_update_data_for_post_send();
        void ctor__qry_case_update_data_for_post_send();
        int size();
    };    
    static_assert(ATF::checkSize<_qry_case_update_data_for_post_send, 24>(), "_qry_case_update_data_for_post_send");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_D3DLIGHTINGELEMENT.hpp>
#include <_D3DTLVERTEX.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _D3DLIGHTDATA
    {
        unsigned int dwSize;
        _D3DLIGHTINGELEMENT *lpIn;
        unsigned int dwInSize;
        _D3DTLVERTEX *lpOut;
        unsigned int dwOutSize;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

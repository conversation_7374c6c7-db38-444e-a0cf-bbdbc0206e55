// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CItemLootTable.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CItemLootTablector_CItemLootTable2_ptr = void (WINAPIV*)(struct CItemLootTable*);
        using CItemLootTablector_CItemLootTable2_clbk = void (WINAPIV*)(struct CItemLootTable*, CItemLootTablector_CItemLootTable2_ptr);
        using CItemLootTableIndexing4_ptr = bool (WINAPIV*)(struct CItemLootTable*, struct CRecordData*, char*);
        using CItemLootTableIndexing4_clbk = bool (WINAPIV*)(struct CItemLootTable*, struct CRecordData*, char*, CItemLootTableIndexing4_ptr);
        using CItemLootTableReadRecord6_ptr = bool (WINAPIV*)(struct CItemLootTable*, char*, struct CRecordData*, char*);
        using CItemLootTableReadRecord6_clbk = bool (WINAPIV*)(struct CItemLootTable*, char*, struct CRecordData*, char*, CItemLootTableReadRecord6_ptr);
        
        using CItemLootTabledtor_CItemLootTable11_ptr = void (WINAPIV*)(struct CItemLootTable*);
        using CItemLootTabledtor_CItemLootTable11_clbk = void (WINAPIV*)(struct CItemLootTable*, CItemLootTabledtor_CItemLootTable11_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

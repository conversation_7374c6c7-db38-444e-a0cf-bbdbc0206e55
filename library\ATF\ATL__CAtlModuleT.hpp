// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CAtlModule.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CAtlModuleT<CComModule> : CAtlModule
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CAtlModule.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CAtlModuleT<CAtlMfcModule> : CAtlModule
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

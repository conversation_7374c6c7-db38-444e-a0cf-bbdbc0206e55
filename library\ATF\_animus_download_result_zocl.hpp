// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _animus_download_result_zocl
    {
        struct  _list
        {
            char sItemIndex;
            unsigned __int64 dwExp;
            unsigned int dwParam;
            char byCsMethod;
            unsigned int dwT;
        };
        char byAnimusNum;
        _list AnimusList[4];
    public:
        _animus_download_result_zocl();
        void ctor__animus_download_result_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_animus_download_result_zocl, 73>(), "_animus_download_result_zocl");
END_ATF_NAMESPACE

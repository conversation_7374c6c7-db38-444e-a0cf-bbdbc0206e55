// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_BILLING_FORCE_CLOSE_DELAY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _BILLING_FORCE_CLOSE_DELAYProcess2_ptr = void (WINAPIV*)(struct _BILLING_FORCE_CLOSE_DELAY*, unsigned int, unsigned int);
        using _BILLING_FORCE_CLOSE_DELAYProcess2_clbk = void (WINAPIV*)(struct _BILLING_FORCE_CLOSE_DELAY*, unsigned int, unsigned int, _BILLING_FORCE_CLOSE_DELAYProcess2_ptr);
        
        using _BILLING_FORCE_CLOSE_DELAYctor__BILLING_FORCE_CLOSE_DELAY4_ptr = void (WINAPIV*)(struct _BILLING_FORCE_CLOSE_DELAY*);
        using _BILLING_FORCE_CLOSE_DELAYctor__BILLING_FORCE_CLOSE_DELAY4_clbk = void (WINAPIV*)(struct _BILLING_FORCE_CLOSE_DELAY*, _BILLING_FORCE_CLOSE_DELAYctor__BILLING_FORCE_CLOSE_DELAY4_ptr);
        
        using _BILLING_FORCE_CLOSE_DELAYdtor__BILLING_FORCE_CLOSE_DELAY6_ptr = void (WINAPIV*)(struct _BILLING_FORCE_CLOSE_DELAY*);
        using _BILLING_FORCE_CLOSE_DELAYdtor__BILLING_FORCE_CLOSE_DELAY6_clbk = void (WINAPIV*)(struct _BILLING_FORCE_CLOSE_DELAY*, _BILLING_FORCE_CLOSE_DELAYdtor__BILLING_FORCE_CLOSE_DELAY6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

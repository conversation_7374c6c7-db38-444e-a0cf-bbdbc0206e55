// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        #pragma pack(push, 8)
        struct CGuildBattleRewardItem
        {
            char m_ucD;
            char m_ucTableCode;
            _base_fld *m_pFld;
        public:
            CGuildBattleRewardItem();
            void ctor_CGuildBattleRewardItem();
            char GetAmount();
            char* GetItemCode();
            struct CGuildBattleRewardItem* Give(struct CPlayer* pkPlayer);
            bool Init(uint16_t usInx);
            bool IsNull();
            bool SetItem(char* szItemCode);
        };    
        #pragma pack(pop)
        static_assert(ATF::checkSize<GUILD_BATTLE::CGuildBattleRewardItem, 16>(), "GUILD_BATTLE::CGuildBattleRewardItem");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

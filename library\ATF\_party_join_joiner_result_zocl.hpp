// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _party_join_joiner_result_zocl
    {
        struct  _list
        {
            unsigned __int16 wIndex;
            unsigned int dwSerial;
            char wszAvator<PERSON>ame[17];
        };
        char byLootShareMode;
        char byListNum;
        _list List[8];
    public:
        _party_join_joiner_result_zocl();
        void ctor__party_join_joiner_result_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_party_join_joiner_result_zocl, 186>(), "_party_join_joiner_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _personal_amine_make_storage_zocl
    {
        unsigned int dwAvatorSerial;
        unsigned int dwTotGold;
    public:
        _personal_amine_make_storage_zocl();
        void ctor__personal_amine_make_storage_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_personal_amine_make_storage_zocl, 8>(), "_personal_amine_make_storage_zocl");
END_ATF_NAMESPACE

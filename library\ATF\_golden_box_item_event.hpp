// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CNetTimer.hpp>
#include <_golden_box_item_ini.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _golden_box_item_event
    {
        char m_event_status;
        CNetTimer m_event_timer;
        CLogFile m_event_log;
        _golden_box_item_ini m_ini;
    public:
        _golden_box_item_event();
        void ctor__golden_box_item_event();
        ~_golden_box_item_event();
        void dtor__golden_box_item_event();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_insert_new_quest_inform_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _insert_new_quest_inform_zoclctor__insert_new_quest_inform_zocl2_ptr = void (WINAPIV*)(struct _insert_new_quest_inform_zocl*);
        using _insert_new_quest_inform_zoclctor__insert_new_quest_inform_zocl2_clbk = void (WINAPIV*)(struct _insert_new_quest_inform_zocl*, _insert_new_quest_inform_zoclctor__insert_new_quest_inform_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

#include <_darkhole_new_mission_inform_zocl.hpp>


START_ATF_NAMESPACE
    _darkhole_new_mission_inform_zocl::_darkhole_new_mission_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _darkhole_new_mission_inform_zocl*);
        (org_ptr(0x14026f880L))(this);
    };
    void _darkhole_new_mission_inform_zocl::ctor__darkhole_new_mission_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _darkhole_new_mission_inform_zocl*);
        (org_ptr(0x14026f880L))(this);
    };
    int _darkhole_new_mission_inform_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _darkhole_new_mission_inform_zocl*);
        return (org_ptr(0x14026f8a0L))(this);
    };
    
END_ATF_NAMESPACE

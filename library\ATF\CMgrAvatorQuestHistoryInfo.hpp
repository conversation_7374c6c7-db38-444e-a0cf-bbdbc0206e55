// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMgrAvatorQuestHistory.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMgrAvatorQuestHistoryctor_CMgrAvatorQuestHistory2_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*);
        using CMgrAvatorQuestHistoryctor_CMgrAvatorQuestHistory2_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, CMgrAvatorQuestHistoryctor_CMgrAvatorQuestHistory2_ptr);
        using CMgrAvatorQuestHistoryGetNewFileName4_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, unsigned int, char*);
        using CMgrAvatorQuestHistoryGetNewFileName4_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, unsigned int, char*, CMgrAvatorQuestHistoryGetNewFileName4_ptr);
        using CMgrAvatorQuestHistoryGetTotalWaitSize6_ptr = int (WINAPIV*)(struct CMgrAvatorQuestHistory*);
        using CMgrAvatorQuestHistoryGetTotalWaitSize6_clbk = int (WINAPIV*)(struct CMgrAvatorQuestHistory*, CMgrAvatorQuestHistoryGetTotalWaitSize6_ptr);
        using CMgrAvatorQuestHistoryIOThread8_ptr = void (WINAPIV*)(void*);
        using CMgrAvatorQuestHistoryIOThread8_clbk = void (WINAPIV*)(void*, CMgrAvatorQuestHistoryIOThread8_ptr);
        using CMgrAvatorQuestHistoryOnLoop10_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*);
        using CMgrAvatorQuestHistoryOnLoop10_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, CMgrAvatorQuestHistoryOnLoop10_ptr);
        using CMgrAvatorQuestHistoryWriteFile12_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, char*, char*);
        using CMgrAvatorQuestHistoryWriteFile12_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, char*, char*, CMgrAvatorQuestHistoryWriteFile12_ptr);
        using CMgrAvatorQuestHistorychar_copy14_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, char*, unsigned int, char*);
        using CMgrAvatorQuestHistorychar_copy14_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, char*, unsigned int, char*, CMgrAvatorQuestHistorychar_copy14_ptr);
        using CMgrAvatorQuestHistorycomplete_quest16_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, int, char*, char*);
        using CMgrAvatorQuestHistorycomplete_quest16_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, int, char*, char*, CMgrAvatorQuestHistorycomplete_quest16_ptr);
        using CMgrAvatorQuestHistoryfail_quest18_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, int, char*, char*, char*);
        using CMgrAvatorQuestHistoryfail_quest18_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, int, char*, char*, char*, CMgrAvatorQuestHistoryfail_quest18_ptr);
        using CMgrAvatorQuestHistoryinit_quest20_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, char*, struct _QUEST_DB_BASE*, char*);
        using CMgrAvatorQuestHistoryinit_quest20_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, char*, struct _QUEST_DB_BASE*, char*, CMgrAvatorQuestHistoryinit_quest20_ptr);
        using CMgrAvatorQuestHistoryinsert_quest22_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, int, char*, char*);
        using CMgrAvatorQuestHistoryinsert_quest22_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, int, char*, char*, CMgrAvatorQuestHistoryinsert_quest22_ptr);
        
        using CMgrAvatorQuestHistorydtor_CMgrAvatorQuestHistory24_ptr = void (WINAPIV*)(struct CMgrAvatorQuestHistory*);
        using CMgrAvatorQuestHistorydtor_CMgrAvatorQuestHistory24_clbk = void (WINAPIV*)(struct CMgrAvatorQuestHistory*, CMgrAvatorQuestHistorydtor_CMgrAvatorQuestHistory24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

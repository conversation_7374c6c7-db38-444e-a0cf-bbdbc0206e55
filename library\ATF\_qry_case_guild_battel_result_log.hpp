// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_guild_battel_result_log
    {
        char szStartTime[17];
        char szEndTime[17];
        unsigned int dwRedSerial;
        char wszRedName[17];
        unsigned int dwBlueSerial;
        char wszBlueName[17];
        unsigned int dwRedScore;
        unsigned int dwBlueScore;
        unsigned int dwRedMaxJoinCnt;
        unsigned int dwBlueMaxJoinCnt;
        unsigned int dwRedGoalCnt;
        unsigned int dwBlueGoalCnt;
        unsigned int dwRedKillCntSum;
        unsigned int dwBlueKillCntSum;
        char byBattleResult;
        unsigned int dwMaxGoalCharacSerial;
        char wszMaxGoalCharacName[17];
        unsigned int dwMaxKillCharacSerial;
        char wszMaxKillCharacName[17];
        char byJoinLimit;
        unsigned int dwGuildBattleCostGold;
        char szBattleMapCode[12];
    };    
    static_assert(ATF::checkSize<_qry_case_guild_battel_result_log, 184>(), "_qry_case_guild_battel_result_log");
END_ATF_NAMESPACE

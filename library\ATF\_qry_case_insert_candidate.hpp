// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_insert_candidate
    {
        char byRace;
        unsigned __int16 wIndex;
        unsigned int dwElectSerial;
        unsigned int dwAvatorSerial;
    public:
        _qry_case_insert_candidate(char byR, uint16_t wIdx, unsigned int dwE, unsigned int dwS);
        void ctor__qry_case_insert_candidate(char byR, uint16_t wIdx, unsigned int dwE, unsigned int dwS);
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_insert_candidate, 12>(), "_qry_case_insert_candidate");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__vector.hpp>
#include <_PVP_RANK_REFRESH_USER.hpp>


START_ATF_NAMESPACE
    struct CPvpUserRankingTargetUserList
    {
        unsigned int m_uiAddTotalCnt;
        unsigned int m_uiRefreshCnt;
        std::vector<_PVP_RANK_REFRESH_USER *> m_PvpRankRefreshUser;
    public:
        void Add(unsigned int dwSerial, char byLv, char byRace);
        CPvpUserRankingTargetUserList();
        void ctor_CPvpUserRankingTargetUserList();
        void ClearRankingStart();
        unsigned int GetAddedTotalCnt();
        void UpdateCharGrade();
        char UpdateRaceRankStep11(char* szData);
        bool assign();
        ~CPvpUserRankingTargetUserList();
        void dtor_CPvpUserRankingTargetUserList();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Container_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct _Iterator_base
        {
            _Container_base *_Mycont;
            _Iterator_base *_Mynextiter;
        public:
            void _Clean_up_iter_debug();
            _Iterator_base(struct _Iterator_base* _Right);
            void ctor__Iterator_base(struct _Iterator_base* _Right);
            _Iterator_base();
            void ctor__Iterator_base();
            ~_Iterator_base();
            void dtor__Iterator_base();
        };    
        static_assert(ATF::checkSize<std::_Iterator_base, 16>(), "std::_Iterator_base");
    }; // end namespace std
END_ATF_NAMESPACE

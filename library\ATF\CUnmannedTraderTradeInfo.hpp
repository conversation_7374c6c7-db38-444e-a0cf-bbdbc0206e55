// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderTradeInfo
    {
        CMyTimer *m_pkTimer;
        int m_iOldDay;
        bool m_bNeedUpdateSave;
        unsigned __int64 m_ui64TotalOldIncome;
        unsigned __int64 m_ui64TotalCurrentIncome;
    public:
        void AddIncome(unsigned int dwOriPrice);
        CUnmannedTraderTradeInfo();
        void ctor_CUnmannedTraderTradeInfo();
        bool Init();
        void LoadINI();
        void Loop();
        void NotifyIncome(char byRace, uint16_t wIndex);
        void NotifyIncome();
        void SaveINI();
        void UpdateIncome();
        ~CUnmannedTraderTradeInfo();
        void dtor_CUnmannedTraderTradeInfo();
    };
END_ATF_NAMESPACE

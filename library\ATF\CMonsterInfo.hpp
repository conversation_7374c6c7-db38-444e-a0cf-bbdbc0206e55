// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonster.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CMonsterAddEventItem2_ptr = bool (WINAPIV*)(struct CMonster*, struct _event_loot_item*);
        using CMonsterAddEventItem2_clbk = bool (WINAPIV*)(struct CMonster*, struct _event_loot_item*, CMonsterAddEventItem2_ptr);
        using CMonsterAssistSF4_ptr = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*);
        using CMonsterAssistSF4_clbk = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*, CMonsterAssistSF4_ptr);
        using CMonsterAttack6_ptr = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*);
        using CMonsterAttack6_clbk = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*, CMonsterAttack6_ptr);
        using CMonsterAttackObject8_ptr = int (WINAPIV*)(struct CMonster*, int, struct CGameObject*);
        using CMonsterAttackObject8_clbk = int (WINAPIV*)(struct CMonster*, int, struct CGameObject*, CMonsterAttackObject8_ptr);
        using CMonsterAttackableHeight10_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterAttackableHeight10_clbk = int (WINAPIV*)(struct CMonster*, CMonsterAttackableHeight10_ptr);
        using CMonsterAutoRecover12_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterAutoRecover12_clbk = void (WINAPIV*)(struct CMonster*, CMonsterAutoRecover12_ptr);
        using CMonsterBeTargeted14_ptr = void (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterBeTargeted14_clbk = void (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterBeTargeted14_ptr);
        
        using CMonsterctor_CMonster16_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterctor_CMonster16_clbk = void (WINAPIV*)(struct CMonster*, CMonsterctor_CMonster16_ptr);
        using CMonsterChangeApparition18_ptr = void (WINAPIV*)(struct CMonster*, bool, unsigned int);
        using CMonsterChangeApparition18_clbk = void (WINAPIV*)(struct CMonster*, bool, unsigned int, CMonsterChangeApparition18_ptr);
        using CMonsterCheckAutoRecoverHP20_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterCheckAutoRecoverHP20_clbk = void (WINAPIV*)(struct CMonster*, CMonsterCheckAutoRecoverHP20_ptr);
        using CMonsterCheckDelayDestroy22_ptr = bool (WINAPIV*)(struct CMonster*);
        using CMonsterCheckDelayDestroy22_clbk = bool (WINAPIV*)(struct CMonster*, CMonsterCheckDelayDestroy22_ptr);
        using CMonsterCheckEmotionPresentation24_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterCheckEmotionPresentation24_clbk = void (WINAPIV*)(struct CMonster*, CMonsterCheckEmotionPresentation24_ptr);
        using CMonsterCheckEventEmotionPresentation26_ptr = bool (WINAPIV*)(struct CMonster*, char, struct CCharacter*);
        using CMonsterCheckEventEmotionPresentation26_clbk = bool (WINAPIV*)(struct CMonster*, char, struct CCharacter*, CMonsterCheckEventEmotionPresentation26_ptr);
        using CMonsterCheckLootItem28_ptr = void (WINAPIV*)(struct CMonster*, struct CPlayer*);
        using CMonsterCheckLootItem28_clbk = void (WINAPIV*)(struct CMonster*, struct CPlayer*, CMonsterCheckLootItem28_ptr);
        using CMonsterCheckMonsterRotate30_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterCheckMonsterRotate30_clbk = void (WINAPIV*)(struct CMonster*, CMonsterCheckMonsterRotate30_ptr);
        using CMonsterCheckMonsterStateData32_ptr = bool (WINAPIV*)(struct CMonster*);
        using CMonsterCheckMonsterStateData32_clbk = bool (WINAPIV*)(struct CMonster*, CMonsterCheckMonsterStateData32_ptr);
        using CMonsterCheckRespawnProcess34_ptr = bool (WINAPIV*)(struct CMonster*);
        using CMonsterCheckRespawnProcess34_clbk = bool (WINAPIV*)(struct CMonster*, CMonsterCheckRespawnProcess34_ptr);
        using CMonsterClearEmotionPresentation36_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterClearEmotionPresentation36_clbk = void (WINAPIV*)(struct CMonster*, CMonsterClearEmotionPresentation36_ptr);
        using CMonsterCommand_ChildMonDestroy38_ptr = void (WINAPIV*)(struct CMonster*, unsigned int);
        using CMonsterCommand_ChildMonDestroy38_clbk = void (WINAPIV*)(struct CMonster*, unsigned int, CMonsterCommand_ChildMonDestroy38_ptr);
        using CMonsterConvertTargetPlayer40_ptr = bool (WINAPIV*)(struct CMonster*, struct CPlayer*);
        using CMonsterConvertTargetPlayer40_clbk = bool (WINAPIV*)(struct CMonster*, struct CPlayer*, CMonsterConvertTargetPlayer40_ptr);
        using CMonsterCreate42_ptr = bool (WINAPIV*)(struct CMonster*, struct _monster_create_setdata*);
        using CMonsterCreate42_clbk = bool (WINAPIV*)(struct CMonster*, struct _monster_create_setdata*, CMonsterCreate42_ptr);
        using CMonsterCreateAI44_ptr = int (WINAPIV*)(struct CMonster*, int);
        using CMonsterCreateAI44_clbk = int (WINAPIV*)(struct CMonster*, int, CMonsterCreateAI44_ptr);
        using CMonsterDestroy46_ptr = bool (WINAPIV*)(struct CMonster*, char, struct CGameObject*);
        using CMonsterDestroy46_clbk = bool (WINAPIV*)(struct CMonster*, char, struct CGameObject*, CMonsterDestroy46_ptr);
        using CMonsterDisableStdItemLoot48_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterDisableStdItemLoot48_clbk = void (WINAPIV*)(struct CMonster*, CMonsterDisableStdItemLoot48_ptr);
        using CMonsterFixTargetWhile50_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, unsigned int);
        using CMonsterFixTargetWhile50_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, unsigned int, CMonsterFixTargetWhile50_ptr);
        using CMonsterGeEmotionImpStdTime52_ptr = float (WINAPIV*)(struct CMonster*);
        using CMonsterGeEmotionImpStdTime52_clbk = float (WINAPIV*)(struct CMonster*, CMonsterGeEmotionImpStdTime52_ptr);
        using CMonsterGetAggroResetTime54_ptr = unsigned int (WINAPIV*)(struct CMonster*);
        using CMonsterGetAggroResetTime54_clbk = unsigned int (WINAPIV*)(struct CMonster*, CMonsterGetAggroResetTime54_ptr);
        using CMonsterGetAggroShortTime56_ptr = unsigned int (WINAPIV*)(struct CMonster*);
        using CMonsterGetAggroShortTime56_clbk = unsigned int (WINAPIV*)(struct CMonster*, CMonsterGetAggroShortTime56_ptr);
        using CMonsterGetAttackDP58_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetAttackDP58_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetAttackDP58_ptr);
        using CMonsterGetAttackPart60_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetAttackPart60_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetAttackPart60_ptr);
        using CMonsterGetAttackRange62_ptr = float (WINAPIV*)(struct CMonster*);
        using CMonsterGetAttackRange62_clbk = float (WINAPIV*)(struct CMonster*, CMonsterGetAttackRange62_ptr);
        using CMonsterGetAttackTarget64_ptr = struct CCharacter* (WINAPIV*)(struct CMonster*);
        using CMonsterGetAttackTarget64_clbk = struct CCharacter* (WINAPIV*)(struct CMonster*, CMonsterGetAttackTarget64_ptr);
        using CMonsterGetBonusInAreaAggro66_ptr = float (WINAPIV*)(struct CMonster*);
        using CMonsterGetBonusInAreaAggro66_clbk = float (WINAPIV*)(struct CMonster*, CMonsterGetBonusInAreaAggro66_ptr);
        using CMonsterGetCombatState68_ptr = char (WINAPIV*)(struct CMonster*);
        using CMonsterGetCombatState68_clbk = char (WINAPIV*)(struct CMonster*, CMonsterGetCombatState68_ptr);
        using CMonsterGetCritical_Exception_Rate70_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetCritical_Exception_Rate70_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetCritical_Exception_Rate70_ptr);
        using CMonsterGetDefFC72_ptr = int (WINAPIV*)(struct CMonster*, int, struct CCharacter*, int*);
        using CMonsterGetDefFC72_clbk = int (WINAPIV*)(struct CMonster*, int, struct CCharacter*, int*, CMonsterGetDefFC72_ptr);
        using CMonsterGetDefFacing74_ptr = float (WINAPIV*)(struct CMonster*, int);
        using CMonsterGetDefFacing74_clbk = float (WINAPIV*)(struct CMonster*, int, CMonsterGetDefFacing74_ptr);
        using CMonsterGetDefGap76_ptr = float (WINAPIV*)(struct CMonster*, int);
        using CMonsterGetDefGap76_clbk = float (WINAPIV*)(struct CMonster*, int, CMonsterGetDefGap76_ptr);
        using CMonsterGetDefSkill78_ptr = int (WINAPIV*)(struct CMonster*, bool);
        using CMonsterGetDefSkill78_clbk = int (WINAPIV*)(struct CMonster*, bool, CMonsterGetDefSkill78_ptr);
        using CMonsterGetEmotionState80_ptr = char (WINAPIV*)(struct CMonster*);
        using CMonsterGetEmotionState80_clbk = char (WINAPIV*)(struct CMonster*, CMonsterGetEmotionState80_ptr);
        using CMonsterGetFireTol82_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetFireTol82_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetFireTol82_ptr);
        using CMonsterGetGenAttackProb84_ptr = int (WINAPIV*)(struct CMonster*, struct CCharacter*, int, bool);
        using CMonsterGetGenAttackProb84_clbk = int (WINAPIV*)(struct CMonster*, struct CCharacter*, int, bool, CMonsterGetGenAttackProb84_ptr);
        using CMonsterGetHP86_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetHP86_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetHP86_ptr);
        using CMonsterGetHelpMeCase88_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetHelpMeCase88_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetHelpMeCase88_ptr);
        using CMonsterGetLevel90_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetLevel90_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetLevel90_ptr);
        using CMonsterGetMaxDMGSFContCount92_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetMaxDMGSFContCount92_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetMaxDMGSFContCount92_ptr);
        using CMonsterGetMaxHP94_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetMaxHP94_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetMaxHP94_ptr);
        using CMonsterGetMob_AsistType96_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetMob_AsistType96_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetMob_AsistType96_ptr);
        using CMonsterGetMob_SubRace98_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetMob_SubRace98_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetMob_SubRace98_ptr);
        using CMonsterGetMonStateInfo100_ptr = uint16_t (WINAPIV*)(struct CMonster*);
        using CMonsterGetMonStateInfo100_clbk = uint16_t (WINAPIV*)(struct CMonster*, CMonsterGetMonStateInfo100_ptr);
        using CMonsterGetMonsterGrade102_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetMonsterGrade102_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetMonsterGrade102_ptr);
        using CMonsterGetMoveSpeed104_ptr = float (WINAPIV*)(struct CMonster*);
        using CMonsterGetMoveSpeed104_clbk = float (WINAPIV*)(struct CMonster*, CMonsterGetMoveSpeed104_ptr);
        using CMonsterGetMoveType106_ptr = char (WINAPIV*)(struct CMonster*);
        using CMonsterGetMoveType106_clbk = char (WINAPIV*)(struct CMonster*, CMonsterGetMoveType106_ptr);
        using CMonsterGetMyDMGSFContCount108_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetMyDMGSFContCount108_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetMyDMGSFContCount108_ptr);
        using CMonsterGetNewMonSerial110_ptr = unsigned int (WINAPIV*)();
        using CMonsterGetNewMonSerial110_clbk = unsigned int (WINAPIV*)(CMonsterGetNewMonSerial110_ptr);
        using CMonsterGetObjName112_ptr = char* (WINAPIV*)(struct CMonster*);
        using CMonsterGetObjName112_clbk = char* (WINAPIV*)(struct CMonster*, CMonsterGetObjName112_ptr);
        using CMonsterGetObjRace114_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetObjRace114_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetObjRace114_ptr);
        using CMonsterGetOffensiveType116_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetOffensiveType116_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetOffensiveType116_ptr);
        using CMonsterGetSignalReActor118_ptr = struct CLuaSignalReActor* (WINAPIV*)(struct CMonster*);
        using CMonsterGetSignalReActor118_clbk = struct CLuaSignalReActor* (WINAPIV*)(struct CMonster*, CMonsterGetSignalReActor118_ptr);
        using CMonsterGetSkillDelayTime120_ptr = float (WINAPIV*)(struct CMonster*, struct CMonsterSkill*);
        using CMonsterGetSkillDelayTime120_clbk = float (WINAPIV*)(struct CMonster*, struct CMonsterSkill*, CMonsterGetSkillDelayTime120_ptr);
        using CMonsterGetSoilTol122_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetSoilTol122_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetSoilTol122_ptr);
        using CMonsterGetViewAngleCap124_ptr = bool (WINAPIV*)(struct CMonster*, int, int*);
        using CMonsterGetViewAngleCap124_clbk = bool (WINAPIV*)(struct CMonster*, int, int*, CMonsterGetViewAngleCap124_ptr);
        using CMonsterGetVisualAngle126_ptr = float (WINAPIV*)(struct CMonster*);
        using CMonsterGetVisualAngle126_clbk = float (WINAPIV*)(struct CMonster*, CMonsterGetVisualAngle126_ptr);
        using CMonsterGetVisualField128_ptr = float (WINAPIV*)(struct CMonster*);
        using CMonsterGetVisualField128_clbk = float (WINAPIV*)(struct CMonster*, CMonsterGetVisualField128_ptr);
        using CMonsterGetWaterTol130_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetWaterTol130_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetWaterTol130_ptr);
        using CMonsterGetWeaponAdjust132_ptr = float (WINAPIV*)(struct CMonster*);
        using CMonsterGetWeaponAdjust132_clbk = float (WINAPIV*)(struct CMonster*, CMonsterGetWeaponAdjust132_ptr);
        using CMonsterGetWeaponClass134_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetWeaponClass134_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetWeaponClass134_ptr);
        using CMonsterGetWidth136_ptr = float (WINAPIV*)(struct CMonster*);
        using CMonsterGetWidth136_clbk = float (WINAPIV*)(struct CMonster*, CMonsterGetWidth136_ptr);
        using CMonsterGetWindTol138_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterGetWindTol138_clbk = int (WINAPIV*)(struct CMonster*, CMonsterGetWindTol138_ptr);
        using CMonsterGetYAngle140_ptr = float (WINAPIV*)(struct CMonster*);
        using CMonsterGetYAngle140_clbk = float (WINAPIV*)(struct CMonster*, CMonsterGetYAngle140_ptr);
        using CMonsterGetYAngleByte142_ptr = char (WINAPIV*)(struct CMonster*);
        using CMonsterGetYAngleByte142_clbk = char (WINAPIV*)(struct CMonster*, CMonsterGetYAngleByte142_ptr);
        using CMonsterInit144_ptr = bool (WINAPIV*)(struct CMonster*, struct _object_id*);
        using CMonsterInit144_clbk = bool (WINAPIV*)(struct CMonster*, struct _object_id*, CMonsterInit144_ptr);
        using CMonsterInsertSFContEffect146_ptr = char (WINAPIV*)(struct CMonster*, char, char, unsigned int, uint16_t, char, bool*, struct CCharacter*);
        using CMonsterInsertSFContEffect146_clbk = char (WINAPIV*)(struct CMonster*, char, char, unsigned int, uint16_t, char, bool*, struct CCharacter*, CMonsterInsertSFContEffect146_ptr);
        using CMonsterIsAttackableInTown148_ptr = bool (WINAPIV*)(struct CMonster*);
        using CMonsterIsAttackableInTown148_clbk = bool (WINAPIV*)(struct CMonster*, CMonsterIsAttackableInTown148_ptr);
        using CMonsterIsBeAttackedAble150_ptr = bool (WINAPIV*)(struct CMonster*, bool);
        using CMonsterIsBeAttackedAble150_clbk = bool (WINAPIV*)(struct CMonster*, bool, CMonsterIsBeAttackedAble150_ptr);
        using CMonsterIsBeDamagedAble152_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterIsBeDamagedAble152_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterIsBeDamagedAble152_ptr);
        using CMonsterIsBossMonster154_ptr = bool (WINAPIV*)(struct CMonster*);
        using CMonsterIsBossMonster154_clbk = bool (WINAPIV*)(struct CMonster*, CMonsterIsBossMonster154_ptr);
        using CMonsterIsMovable156_ptr = bool (WINAPIV*)(struct CMonster*);
        using CMonsterIsMovable156_clbk = bool (WINAPIV*)(struct CMonster*, CMonsterIsMovable156_ptr);
        using CMonsterIsPreAttackAbleMon158_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterIsPreAttackAbleMon158_clbk = int (WINAPIV*)(struct CMonster*, CMonsterIsPreAttackAbleMon158_ptr);
        using CMonsterIsRecvableContEffect160_ptr = bool (WINAPIV*)(struct CMonster*);
        using CMonsterIsRecvableContEffect160_clbk = bool (WINAPIV*)(struct CMonster*, CMonsterIsRecvableContEffect160_ptr);
        using CMonsterIsRewardExp162_ptr = bool (WINAPIV*)(struct CMonster*);
        using CMonsterIsRewardExp162_clbk = bool (WINAPIV*)(struct CMonster*, CMonsterIsRewardExp162_ptr);
        using CMonsterIsRoateMonster164_ptr = bool (WINAPIV*)(struct CMonster*);
        using CMonsterIsRoateMonster164_clbk = bool (WINAPIV*)(struct CMonster*, CMonsterIsRoateMonster164_ptr);
        using CMonsterIsValidPlayer166_ptr = int (WINAPIV*)(struct CMonster*);
        using CMonsterIsValidPlayer166_clbk = int (WINAPIV*)(struct CMonster*, CMonsterIsValidPlayer166_ptr);
        using CMonsterIsViewArea168_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterIsViewArea168_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterIsViewArea168_ptr);
        using CMonsterLinkEventRespawn170_ptr = void (WINAPIV*)(struct CMonster*, struct _event_respawn*);
        using CMonsterLinkEventRespawn170_clbk = void (WINAPIV*)(struct CMonster*, struct _event_respawn*, CMonsterLinkEventRespawn170_ptr);
        using CMonsterLinkEventSet172_ptr = void (WINAPIV*)(struct CMonster*, struct _event_set*);
        using CMonsterLinkEventSet172_clbk = void (WINAPIV*)(struct CMonster*, struct _event_set*, CMonsterLinkEventSet172_ptr);
        using CMonsterLoop174_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterLoop174_clbk = void (WINAPIV*)(struct CMonster*, CMonsterLoop174_ptr);
        using CMonsterOutOfSec176_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterOutOfSec176_clbk = void (WINAPIV*)(struct CMonster*, CMonsterOutOfSec176_ptr);
        using CMonsterRobbedHP178_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, int);
        using CMonsterRobbedHP178_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, int, CMonsterRobbedHP178_ptr);
        using CMonsterSF_AllContHelpForceRemove_Once180_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterSF_AllContHelpForceRemove_Once180_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterSF_AllContHelpForceRemove_Once180_ptr);
        using CMonsterSF_AllContHelpSkillRemove_Once182_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterSF_AllContHelpSkillRemove_Once182_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterSF_AllContHelpSkillRemove_Once182_ptr);
        using CMonsterSF_HPInc_Once184_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, float);
        using CMonsterSF_HPInc_Once184_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, float, CMonsterSF_HPInc_Once184_ptr);
        using CMonsterSF_LateContDamageRemove_Once186_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterSF_LateContDamageRemove_Once186_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterSF_LateContDamageRemove_Once186_ptr);
        using CMonsterSF_LateContHelpForceRemove_Once188_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterSF_LateContHelpForceRemove_Once188_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterSF_LateContHelpForceRemove_Once188_ptr);
        using CMonsterSF_LateContHelpSkillRemove_Once190_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterSF_LateContHelpSkillRemove_Once190_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterSF_LateContHelpSkillRemove_Once190_ptr);
        using CMonsterSearchNearPlayer192_ptr = struct CCharacter* (WINAPIV*)(struct CMonster*);
        using CMonsterSearchNearPlayer192_clbk = struct CCharacter* (WINAPIV*)(struct CMonster*, CMonsterSearchNearPlayer192_ptr);
        using CMonsterSendMsg_Assist_Force194_ptr = void (WINAPIV*)(struct CMonster*, char, struct CCharacter*, struct _force_fld*, int);
        using CMonsterSendMsg_Assist_Force194_clbk = void (WINAPIV*)(struct CMonster*, char, struct CCharacter*, struct _force_fld*, int, CMonsterSendMsg_Assist_Force194_ptr);
        using CMonsterSendMsg_Assist_Skill196_ptr = void (WINAPIV*)(struct CMonster*, char, int, struct CCharacter*, struct _skill_fld*, int);
        using CMonsterSendMsg_Assist_Skill196_clbk = void (WINAPIV*)(struct CMonster*, char, int, struct CCharacter*, struct _skill_fld*, int, CMonsterSendMsg_Assist_Skill196_ptr);
        using CMonsterSendMsg_Attack_Force198_ptr = void (WINAPIV*)(struct CMonster*, struct CMonsterAttack*);
        using CMonsterSendMsg_Attack_Force198_clbk = void (WINAPIV*)(struct CMonster*, struct CMonsterAttack*, CMonsterSendMsg_Attack_Force198_ptr);
        using CMonsterSendMsg_Attack_Gen200_ptr = void (WINAPIV*)(struct CMonster*, struct CMonsterAttack*);
        using CMonsterSendMsg_Attack_Gen200_clbk = void (WINAPIV*)(struct CMonster*, struct CMonsterAttack*, CMonsterSendMsg_Attack_Gen200_ptr);
        using CMonsterSendMsg_Attack_Skill202_ptr = void (WINAPIV*)(struct CMonster*, struct CMonsterAttack*);
        using CMonsterSendMsg_Attack_Skill202_clbk = void (WINAPIV*)(struct CMonster*, struct CMonsterAttack*, CMonsterSendMsg_Attack_Skill202_ptr);
        using CMonsterSendMsg_Change_MonsterRotate204_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterSendMsg_Change_MonsterRotate204_clbk = void (WINAPIV*)(struct CMonster*, CMonsterSendMsg_Change_MonsterRotate204_ptr);
        using CMonsterSendMsg_Change_MonsterState206_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterSendMsg_Change_MonsterState206_clbk = void (WINAPIV*)(struct CMonster*, CMonsterSendMsg_Change_MonsterState206_ptr);
        using CMonsterSendMsg_Change_MonsterTarget208_ptr = void (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterSendMsg_Change_MonsterTarget208_clbk = void (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterSendMsg_Change_MonsterTarget208_ptr);
        using CMonsterSendMsg_Create210_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterSendMsg_Create210_clbk = void (WINAPIV*)(struct CMonster*, CMonsterSendMsg_Create210_ptr);
        using CMonsterSendMsg_Destroy212_ptr = void (WINAPIV*)(struct CMonster*, char);
        using CMonsterSendMsg_Destroy212_clbk = void (WINAPIV*)(struct CMonster*, char, CMonsterSendMsg_Destroy212_ptr);
        using CMonsterSendMsg_Emotion_Presentation214_ptr = void (WINAPIV*)(struct CMonster*, char, uint16_t, uint16_t, int);
        using CMonsterSendMsg_Emotion_Presentation214_clbk = void (WINAPIV*)(struct CMonster*, char, uint16_t, uint16_t, int, CMonsterSendMsg_Emotion_Presentation214_ptr);
        using CMonsterSendMsg_FixPosition216_ptr = void (WINAPIV*)(struct CMonster*, int);
        using CMonsterSendMsg_FixPosition216_clbk = void (WINAPIV*)(struct CMonster*, int, CMonsterSendMsg_FixPosition216_ptr);
        using CMonsterSendMsg_Move218_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterSendMsg_Move218_clbk = void (WINAPIV*)(struct CMonster*, CMonsterSendMsg_Move218_ptr);
        using CMonsterSendMsg_RealMovePoint220_ptr = void (WINAPIV*)(struct CMonster*, int);
        using CMonsterSendMsg_RealMovePoint220_clbk = void (WINAPIV*)(struct CMonster*, int, CMonsterSendMsg_RealMovePoint220_ptr);
        using CMonsterSetAttackTarget222_ptr = void (WINAPIV*)(struct CMonster*, struct CCharacter*);
        using CMonsterSetAttackTarget222_clbk = void (WINAPIV*)(struct CMonster*, struct CCharacter*, CMonsterSetAttackTarget222_ptr);
        using CMonsterSetCombatState224_ptr = void (WINAPIV*)(struct CMonster*, char);
        using CMonsterSetCombatState224_clbk = void (WINAPIV*)(struct CMonster*, char, CMonsterSetCombatState224_ptr);
        using CMonsterSetDamage226_ptr = int (WINAPIV*)(struct CMonster*, int, struct CCharacter*, int, bool, int, unsigned int, bool);
        using CMonsterSetDamage226_clbk = int (WINAPIV*)(struct CMonster*, int, struct CCharacter*, int, bool, int, unsigned int, bool, CMonsterSetDamage226_ptr);
        using CMonsterSetDefPart228_ptr = void (WINAPIV*)(struct CMonster*, struct _monster_fld*);
        using CMonsterSetDefPart228_clbk = void (WINAPIV*)(struct CMonster*, struct _monster_fld*, CMonsterSetDefPart228_ptr);
        using CMonsterSetEmotionState230_ptr = void (WINAPIV*)(struct CMonster*, char);
        using CMonsterSetEmotionState230_clbk = void (WINAPIV*)(struct CMonster*, char, CMonsterSetEmotionState230_ptr);
        using CMonsterSetHP232_ptr = bool (WINAPIV*)(struct CMonster*, int, bool);
        using CMonsterSetHP232_clbk = bool (WINAPIV*)(struct CMonster*, int, bool, CMonsterSetHP232_ptr);
        using CMonsterSetMoveType234_ptr = void (WINAPIV*)(struct CMonster*, char);
        using CMonsterSetMoveType234_clbk = void (WINAPIV*)(struct CMonster*, char, CMonsterSetMoveType234_ptr);
        using CMonsterSetStun236_ptr = void (WINAPIV*)(struct CMonster*, bool);
        using CMonsterSetStun236_clbk = void (WINAPIV*)(struct CMonster*, bool, CMonsterSetStun236_ptr);
        using CMonsterUpdateLookAtPos238_ptr = void (WINAPIV*)(struct CMonster*, float*);
        using CMonsterUpdateLookAtPos238_clbk = void (WINAPIV*)(struct CMonster*, float*, CMonsterUpdateLookAtPos238_ptr);
        using CMonsterUpdateLookAtPos240_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterUpdateLookAtPos240_clbk = void (WINAPIV*)(struct CMonster*, CMonsterUpdateLookAtPos240_ptr);
        using CMonsterUpdateSFCont242_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterUpdateSFCont242_clbk = void (WINAPIV*)(struct CMonster*, CMonsterUpdateSFCont242_ptr);
        using CMonster_AssistSF_Cont_Dmg244_ptr = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*);
        using CMonster_AssistSF_Cont_Dmg244_clbk = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*, CMonster_AssistSF_Cont_Dmg244_ptr);
        using CMonster_AssistSF_Cont_Support246_ptr = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*);
        using CMonster_AssistSF_Cont_Support246_clbk = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*, CMonster_AssistSF_Cont_Support246_ptr);
        using CMonster_AssistSF_Cont_Temp248_ptr = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*);
        using CMonster_AssistSF_Cont_Temp248_clbk = int (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*, CMonster_AssistSF_Cont_Temp248_ptr);
        using CMonster_BossBirthWriteLog250_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonster_BossBirthWriteLog250_clbk = void (WINAPIV*)(struct CMonster*, CMonster_BossBirthWriteLog250_ptr);
        using CMonster_BossDieWriteLog_End252_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonster_BossDieWriteLog_End252_clbk = void (WINAPIV*)(struct CMonster*, CMonster_BossDieWriteLog_End252_ptr);
        using CMonster_BossDieWriteLog_Start254_ptr = void (WINAPIV*)(struct CMonster*, char, struct CGameObject*);
        using CMonster_BossDieWriteLog_Start254_clbk = void (WINAPIV*)(struct CMonster*, char, struct CGameObject*, CMonster_BossDieWriteLog_Start254_ptr);
        using CMonster_DestroySDM256_ptr = void (WINAPIV*)();
        using CMonster_DestroySDM256_clbk = void (WINAPIV*)(CMonster_DestroySDM256_ptr);
        using CMonster_InitSDM258_ptr = void (WINAPIV*)();
        using CMonster_InitSDM258_clbk = void (WINAPIV*)(CMonster_InitSDM258_ptr);
        using CMonster_InitSDM_LootTBL260_ptr = void (WINAPIV*)();
        using CMonster_InitSDM_LootTBL260_clbk = void (WINAPIV*)(CMonster_InitSDM_LootTBL260_ptr);
        using CMonster_LootItem_EventSet262_ptr = bool (WINAPIV*)(struct CMonster*, struct CPlayer*);
        using CMonster_LootItem_EventSet262_clbk = bool (WINAPIV*)(struct CMonster*, struct CPlayer*, CMonster_LootItem_EventSet262_ptr);
        using CMonster_LootItem_Qst264_ptr = bool (WINAPIV*)(struct CMonster*, struct CPlayer*);
        using CMonster_LootItem_Qst264_clbk = bool (WINAPIV*)(struct CMonster*, struct CPlayer*, CMonster_LootItem_Qst264_ptr);
        using CMonster_LootItem_Rwp266_ptr = bool (WINAPIV*)(struct CMonster*, struct CPlayer*);
        using CMonster_LootItem_Rwp266_clbk = bool (WINAPIV*)(struct CMonster*, struct CPlayer*, CMonster_LootItem_Rwp266_ptr);
        using CMonster_LootItem_Std268_ptr = bool (WINAPIV*)(struct CMonster*, struct CPlayer*);
        using CMonster_LootItem_Std268_clbk = bool (WINAPIV*)(struct CMonster*, struct CPlayer*, CMonster_LootItem_Std268_ptr);
        using CMonstermake_force_attack_param274_ptr = void (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*, struct _attack_param*);
        using CMonstermake_force_attack_param274_clbk = void (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*, struct _attack_param*, CMonstermake_force_attack_param274_ptr);
        using CMonstermake_gen_attack_param276_ptr = void (WINAPIV*)(struct CMonster*, struct CCharacter*, struct _attack_param*);
        using CMonstermake_gen_attack_param276_clbk = void (WINAPIV*)(struct CMonster*, struct CCharacter*, struct _attack_param*, CMonstermake_gen_attack_param276_ptr);
        using CMonstermake_skill_attack_param278_ptr = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*, int, struct _attack_param*);
        using CMonstermake_skill_attack_param278_clbk = bool (WINAPIV*)(struct CMonster*, struct CCharacter*, struct CMonsterSkill*, int, struct _attack_param*, CMonstermake_skill_attack_param278_ptr);
        
        using CMonsterdtor_CMonster280_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterdtor_CMonster280_clbk = void (WINAPIV*)(struct CMonster*, CMonsterdtor_CMonster280_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

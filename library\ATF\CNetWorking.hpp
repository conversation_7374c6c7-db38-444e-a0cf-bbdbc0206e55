// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CNetProcess.hpp>
#include <CNetWorkingVtbl.hpp>
#include <_MSG_HEADER.hpp>
#include <_NET_TYPE_PARAM.hpp>
#include <_socket.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CNetWorking
    {
        CNetWorkingVtbl *vfptr;
        char m_szSystemName[128];
        unsigned int m_dwUseProcessNum;
        CLogFile m_LogFile;
        CNetProcess m_Process[4];
        CNetProcess *m_pProcess[4];
        bool m_bUseFG;
        char m_szServerName[33];
        char m_szLogPath[128];
    public:
        void AcceptClientCheck(unsigned int dwProID, unsigned int dwIndex, unsigned int dwSerial);
        void AnsyncConnectComplete(unsigned int dwProID, unsigned int dwIndex, int nResult);
        CNetWorking();
        void ctor_CNetWorking();
        void CloseClientCheck(unsigned int dwProID, unsigned int dwIndex, unsigned int dwSerial);
        void CloseSocket(unsigned int dwProID, unsigned int dwSocketIndex, bool bSlowClose);
        int Connect(unsigned int dwProID, unsigned int dwSocketIndex, unsigned int dwIP, uint16_t wPort);
        bool DataAnalysis(unsigned int dwProID, unsigned int dwClientIndex, struct _MSG_HEADER* pMsgHeader, char* pMsg);
        bool ExpulsionSocket(unsigned int dwProID, unsigned int dwIndex, char byReason, void* pvInfo);
        unsigned int GetCheckRecvTime(unsigned int dwProID, unsigned int dwSocketIndex);
        struct _socket* GetSocket(unsigned int dwProID, unsigned int dwSocketIndex);
        void OnLoop();
        void OnLoop_Receipt();
        void ProcessLogFile(unsigned int dwProID, bool bRecv, bool bSend, bool bSystem);
        void Release();
        bool SetNetSystem(unsigned int dwUseProcessNum, struct _NET_TYPE_PARAM* pType, char* szSystemName, char* pszLogPath);
        void UserLoop();
        ~CNetWorking();
        void dtor_CNetWorking();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CNetWorking, 284528>(), "CNetWorking");
END_ATF_NAMESPACE

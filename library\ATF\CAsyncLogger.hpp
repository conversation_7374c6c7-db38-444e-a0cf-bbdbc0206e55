// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ASYNC_LOG_TYPE.hpp>
#include <CAsyncLogBufferList.hpp>
#include <CAsyncLogInfo.hpp>
#include <CFrameRate.hpp>
#include <CLogFile.hpp>
#include <CMyTimer.hpp>
#include <CNetIndexList.hpp>
#include <std__vector.hpp>
#include <stdext__hash_map.hpp>


START_ATF_NAMESPACE
    struct CAsyncLogger
    {
        enum BUFFER_TYPE
        {
            BT_200B = 0x0,
            BT_1K = 0x1,
            BT_10K = 0x2,
            BT_MAX_TYPE = 0x3,
        };
        enum BUFFER_SIZE_MAX_CNT
        {
            BSMC_200B = 0x9E4,
            BSMC_1K = 0xFE,
            BSMC_10K = 0xFE,
        };
        enum BUFFER_SIZE
        {
            BST_200B = 0xC8,
            BST_1K = 0x400,
            BST_10K = 0x2800,
        };
        CFrameRate m_FrameRate;
        CLogFile m_logLoading;
        CMyTimer m_kCheckUpdateLogFileNameDelay;
        stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > m_mapLogInfo;
        CAsyncLogInfo *m_pSystemLogInfo;
        CNetIndexList m_klistEmpty;
        CNetIndexList m_klistProc;
        std::vector<unsigned long,std::allocator<unsigned long> > m_vecPushList;
        CAsyncLogBufferList *m_kBufferList;
    public:
        CAsyncLogger();
        void ctor_CAsyncLogger();
        static void Destroy();
        bool FormatLog(int iType, char* fmt);
        int GetTotalWaitSize();
        int Init();
        static struct CAsyncLogger* Instance();
        void Log(char* szFileName, char* szLog, int iLenStr);
        bool Log(int iType, char* szLog);
        bool LogFromArg(int iType, char* fmt, char* arg_ptr);
        void Loop();
        static void ProcThread(void* p);
        void ProcWrite();
        bool Regist(ASYNC_LOG_TYPE eType, char* szDirPath, char* szTypeName, bool bAddDateFileName, unsigned int dwUpdateFileNameDelay);
        void SystemLog(char* fmt);
        ~CAsyncLogger();
        void dtor_CAsyncLogger();
    };
END_ATF_NAMESPACE

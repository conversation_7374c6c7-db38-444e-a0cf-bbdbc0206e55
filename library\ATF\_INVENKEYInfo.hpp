// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _INVENKEYCovDBKey2_ptr = int (WINAPIV*)(struct _INVENKEY*);
        using _INVENKEYCovDBKey2_clbk = int (WINAPIV*)(struct _INVENKEY*, _INVENKEYCovDBKey2_ptr);
        using _INVENKEYIsFilled4_ptr = bool (WINAPIV*)(struct _INVENKEY*);
        using _INVENKEYIsFilled4_clbk = bool (WINAPIV*)(struct _INVENKEY*, _INVENKEYIsFilled4_ptr);
        using _INVENKEYIsOverlapItem6_ptr = int (WINAPIV*)(struct _INVENKEY*);
        using _INVENKEYIsOverlapItem6_clbk = int (WINAPIV*)(struct _INVENKEY*, _INVENKEYIsOverlapItem6_ptr);
        using _INVENKEYLoadDBKey8_ptr = void (WINAPIV*)(struct _INVENKEY*, int);
        using _INVENKEYLoadDBKey8_clbk = void (WINAPIV*)(struct _INVENKEY*, int, _INVENKEYLoadDBKey8_ptr);
        using _INVENKEYSetRelease10_ptr = void (WINAPIV*)(struct _INVENKEY*);
        using _INVENKEYSetRelease10_clbk = void (WINAPIV*)(struct _INVENKEY*, _INVENKEYSetRelease10_ptr);
        
        using _INVENKEYctor__INVENKEY12_ptr = void (WINAPIV*)(struct _INVENKEY*, char, char, uint16_t);
        using _INVENKEYctor__INVENKEY12_clbk = void (WINAPIV*)(struct _INVENKEY*, char, char, uint16_t, _INVENKEYctor__INVENKEY12_ptr);
        
        using _INVENKEYctor__INVENKEY14_ptr = void (WINAPIV*)(struct _INVENKEY*);
        using _INVENKEYctor__INVENKEY14_clbk = void (WINAPIV*)(struct _INVENKEY*, _INVENKEYctor__INVENKEY14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_AVATOR_DATA.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_sheet_logout
    {
        unsigned int dwAvatorSerial;
        _AVATOR_DATA NewData;
        _AVATOR_DATA OldData;
        bool bCheckLowHigh;
        bool bUpdateRefineCnt;
        char byRefinedCnt;
        unsigned int dwRefineDate;
    public:
        _qry_sheet_logout();
        void ctor__qry_sheet_logout();
        int size();
        ~_qry_sheet_logout();
        void dtor__qry_sheet_logout();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_sheet_logout, 74444>(), "_qry_sheet_logout");
END_ATF_NAMESPACE

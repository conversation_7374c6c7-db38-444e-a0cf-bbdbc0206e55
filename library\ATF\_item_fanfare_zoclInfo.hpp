// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_item_fanfare_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _item_fanfare_zoclInit2_ptr = void (WINAPIV*)(struct _item_fanfare_zocl*);
        using _item_fanfare_zoclInit2_clbk = void (WINAPIV*)(struct _item_fanfare_zocl*, _item_fanfare_zoclInit2_ptr);
        
        using _item_fanfare_zoclctor__item_fanfare_zocl4_ptr = void (WINAPIV*)(struct _item_fanfare_zocl*);
        using _item_fanfare_zoclctor__item_fanfare_zocl4_clbk = void (WINAPIV*)(struct _item_fanfare_zocl*, _item_fanfare_zoclctor__item_fanfare_zocl4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

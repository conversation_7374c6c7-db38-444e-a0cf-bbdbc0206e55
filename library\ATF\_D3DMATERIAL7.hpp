// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$73A31F5CBED592E4D68563D68CECC2A9.hpp>
#include <$9B5F670D7C9A00CCB4631D748ADA84ED.hpp>
#include <$B17CD2631B3FDDB83281BB2C9951B8BE.hpp>
#include <$C238D5A3E10CB1E6666F856CF21A89A6.hpp>
#include <$FFF3866CF61096AF94481349624DD0C6.hpp>


START_ATF_NAMESPACE
    struct _D3DMATERIAL7
    {
        $C238D5A3E10CB1E6666F856CF21A89A6 ___u0;
        $B17CD2631B3FDDB83281BB2C9951B8BE ___u1;
        $9B5F670D7C9A00CCB4631D748ADA84ED ___u2;
        $FFF3866CF61096AF94481349624DD0C6 ___u3;
        $73A31F5CBED592E4D68563D68CECC2A9 ___u4;
    };
END_ATF_NAMESPACE

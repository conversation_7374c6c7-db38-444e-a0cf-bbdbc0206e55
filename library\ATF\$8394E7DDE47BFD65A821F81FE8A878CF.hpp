// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $8394E7DDE47BFD65A821F81FE8A878CF
    {
      max_event_set_parameter = 0xA,
      max_monster_set = 0xA,
      max_monster_num = 0x64,
      max_event_regen_range = 0x1F4,
      max_eventset_reward_item = 0x32,
    };
END_ATF_NAMESPACE

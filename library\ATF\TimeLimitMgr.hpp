// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <Player_TL_Status.hpp>
#include <_SYSTEMTIME.hpp>
#include <tm.hpp>


START_ATF_NAMESPACE
    struct TimeLimitMgr
    {
        CMyTimer m_tmLoopTime;
        Player_TL_Status m_lstTLStaus[2532];
        unsigned __int16 m_wEnable;
        unsigned __int16 m_wPeriodCnt;
        unsigned int m_dwLogoutTerm;
        unsigned int m_dwNotifyTerm;
        unsigned __int16 *m_pwTime;
        unsigned __int16 *m_pwFatigue;
        long double *m_pdPercent;
        unsigned int m_dwPlayFDegree;
        unsigned int m_dwLogoutFDegree;
    public:
        void Chack_Time();
        bool CheckPlayerStatus(uint16_t wIndex, unsigned int dwLastContSaveTime, char* pbyStatus, unsigned int* pdwFatigue);
        unsigned int ClacLastLogoutTimeSec(unsigned int dwLastConnTime);
        unsigned int ClacLastLogoutTimeToFatigue(unsigned int dwLastConnTime);
        void Delete_All();
        struct Player_TL_Status* Find_Data(unsigned int dwSerial);
        struct Player_TL_Status* Find_Data(uint16_t wIndex);
        uint16_t GetEndPlayTime();
        uint16_t GetPeriodCnt();
        unsigned int GetPlayFDegree();
        uint16_t GetPlayerData(uint16_t wIndex, char* psStatus, long double* pdPercent);
        long double GetPlayerPenalty(uint16_t wIndex);
        char GetPlayerStatus(uint16_t wIndex);
        void InitializeTLMgr();
        void InsertPlayerStatus(uint16_t wIndex, unsigned int dwAccountSerial, char byStatus, unsigned int dwFatigue, unsigned int dwLastLogoutTime, bool bAgeLimit);
        static struct TimeLimitMgr* Instance();
        void LoadTLINIFile();
        void Pop_Data(unsigned int dwAccountSerial, uint16_t wIndex);
        void Push_Data(struct Player_TL_Status* data, uint16_t wIndex);
        void ReInitFatigue();
        void ReSetPercent(uint16_t wIndex);
        bool SetConfig(uint16_t time1, uint16_t time2, uint16_t time3, uint16_t time4, uint16_t time5);
        void SetLogoutFDegree(unsigned int dwDegree);
        void SetPlayFDegree(unsigned int dwDegree);
        void SetTLEnable(uint16_t wState);
        void SetTime(unsigned int dwTime, uint16_t iIndex);
        unsigned int SumMinuteBetweenSec(struct tm* tmLast);
        unsigned int SumMinuteOne(struct _SYSTEMTIME* tm);
        TimeLimitMgr();
        void ctor_TimeLimitMgr();
        bool UpdatePlayerStatus(uint16_t wIndex, unsigned int dwFatigue, char wStatus);
        ~TimeLimitMgr();
        void dtor_TimeLimitMgr();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  tagMETAHEADER
    {
        unsigned __int16 mtType;
        unsigned __int16 mtHeaderSize;
        unsigned __int16 mtVersion;
        unsigned int mtSize;
        unsigned __int16 mtNoObjects;
        unsigned int mtMaxRecord;
        unsigned __int16 mtNoParameters;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

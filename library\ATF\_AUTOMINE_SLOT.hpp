// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    struct _AUTOMINE_SLOT
    {
        int nLumpIndex;
        _INVENKEY item;
        int nOverlapNum;
    public:
        _AUTOMINE_SLOT();
        void ctor__AUTOMINE_SLOT();
    };    
    static_assert(ATF::checkSize<_AUTOMINE_SLOT, 12>(), "_AUTOMINE_SLOT");
END_ATF_NAMESPACE

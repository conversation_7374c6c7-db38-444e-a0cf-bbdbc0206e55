// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pt_appoint_inform_request_zocl
    {
        struct  __body
        {
            char byLevel;
            char byClassType;
            long double dPvpPoint;
            char wszAvatorName[17];
        public:
            __body();
            void ctor___body();
        };
        __body body[4];
    public:
        _pt_appoint_inform_request_zocl();
        void ctor__pt_appoint_inform_request_zocl();
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

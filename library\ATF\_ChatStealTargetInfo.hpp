// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _ChatStealTargetInfo
    {
        char m_byStealType;
        unsigned int m_dwTargetSerial;
        char m_byRaceBoss;
    public:
        _ChatStealTargetInfo();
        void ctor__ChatStealTargetInfo();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

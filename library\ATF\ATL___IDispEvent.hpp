// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL___IDispEventVtbl.hpp>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct _IDispEvent
        {
            _IDispEventVtbl *vfptr;
            _GUID m_libid;
            _GUID m_iid;
            unsigned __int16 m_wMajorVerNum;
            unsigned __int16 m_wMinorVerNum;
            unsigned int m_dwEventCookie;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

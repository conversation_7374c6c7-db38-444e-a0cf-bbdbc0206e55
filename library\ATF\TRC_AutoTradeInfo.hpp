// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <TRC_AutoTrade.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using TRC_AutoTradeAddGDalant2_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, char*);
        using TRC_AutoTradeAddGDalant2_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, char*, TRC_AutoTradeAddGDalant2_ptr);
        using TRC_AutoTradeCalcPrice4_ptr = unsigned int (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, unsigned int);
        using TRC_AutoTradeCalcPrice4_clbk = unsigned int (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, unsigned int, TRC_AutoTradeCalcPrice4_ptr);
        using TRC_AutoTradeChangeOwner6_ptr = int (WINAPIV*)(struct TRC_AutoTrade*, struct CGuild*);
        using TRC_AutoTradeChangeOwner6_clbk = int (WINAPIV*)(struct TRC_AutoTrade*, struct CGuild*, TRC_AutoTradeChangeOwner6_ptr);
        using TRC_AutoTradeChangeTaxRate8_ptr = int (WINAPIV*)(struct TRC_AutoTrade*, float);
        using TRC_AutoTradeChangeTaxRate8_clbk = int (WINAPIV*)(struct TRC_AutoTrade*, float, TRC_AutoTradeChangeTaxRate8_ptr);
        using TRC_AutoTradeChangeTaxRate10_ptr = void (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradeChangeTaxRate10_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradeChangeTaxRate10_ptr);
        using TRC_AutoTradeInitialzie12_ptr = bool (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradeInitialzie12_clbk = bool (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradeInitialzie12_ptr);
        using TRC_AutoTradeIsMaster14_ptr = bool (WINAPIV*)(struct TRC_AutoTrade*, unsigned int);
        using TRC_AutoTradeIsMaster14_clbk = bool (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, TRC_AutoTradeIsMaster14_ptr);
        using TRC_AutoTradeIsOwnerGuild16_ptr = bool (WINAPIV*)(struct TRC_AutoTrade*, unsigned int);
        using TRC_AutoTradeIsOwnerGuild16_clbk = bool (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, TRC_AutoTradeIsOwnerGuild16_ptr);
        using TRC_AutoTradePushDQSData18_ptr = void (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradePushDQSData18_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradePushDQSData18_ptr);
        using TRC_AutoTradePushDQSData_GuildInMoney20_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, unsigned int);
        using TRC_AutoTradePushDQSData_GuildInMoney20_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, unsigned int, TRC_AutoTradePushDQSData_GuildInMoney20_ptr);
        using TRC_AutoTradeSendMsg_PatriarchTaxRate22_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, int);
        using TRC_AutoTradeSendMsg_PatriarchTaxRate22_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, int, TRC_AutoTradeSendMsg_PatriarchTaxRate22_ptr);
        using TRC_AutoTradeSendMsg_UserLogInNotifyTaxRate24_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, int);
        using TRC_AutoTradeSendMsg_UserLogInNotifyTaxRate24_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, int, TRC_AutoTradeSendMsg_UserLogInNotifyTaxRate24_ptr);
        using TRC_AutoTradeSetGuildMaintainMoney26_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, unsigned int);
        using TRC_AutoTradeSetGuildMaintainMoney26_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, unsigned int, TRC_AutoTradeSetGuildMaintainMoney26_ptr);
        using TRC_AutoTradeSetPatriarchTaxMoney28_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, unsigned int);
        using TRC_AutoTradeSetPatriarchTaxMoney28_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, TRC_AutoTradeSetPatriarchTaxMoney28_ptr);
        
        using TRC_AutoTradector_TRC_AutoTrade30_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, char);
        using TRC_AutoTradector_TRC_AutoTrade30_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, char, TRC_AutoTradector_TRC_AutoTrade30_ptr);
        
        using TRC_AutoTradector_TRC_AutoTrade32_ptr = void (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradector_TRC_AutoTrade32_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradector_TRC_AutoTrade32_ptr);
        using TRC_AutoTrade_db_load34_ptr = bool (WINAPIV*)(struct TRC_AutoTrade*, char);
        using TRC_AutoTrade_db_load34_clbk = bool (WINAPIV*)(struct TRC_AutoTrade*, char, TRC_AutoTrade_db_load34_ptr);
        using TRC_AutoTrade_insert_info36_ptr = char (WINAPIV*)(char*);
        using TRC_AutoTrade_insert_info36_clbk = char (WINAPIV*)(char*, TRC_AutoTrade_insert_info36_ptr);
        using TRC_AutoTradecheck40_ptr = int (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, unsigned int);
        using TRC_AutoTradecheck40_clbk = int (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, unsigned int, TRC_AutoTradecheck40_ptr);
        using TRC_AutoTradegetOwnerGuild42_ptr = struct CGuild* (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradegetOwnerGuild42_clbk = struct CGuild* (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradegetOwnerGuild42_ptr);
        using TRC_AutoTradegetSuggestedTime44_ptr = unsigned int (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradegetSuggestedTime44_clbk = unsigned int (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradegetSuggestedTime44_ptr);
        using TRC_AutoTradeget_guidlname46_ptr = char* (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradeget_guidlname46_clbk = char* (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradeget_guidlname46_ptr);
        using TRC_AutoTradeget_next_tax48_ptr = float (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradeget_next_tax48_clbk = float (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradeget_next_tax48_ptr);
        using TRC_AutoTradeget_race50_ptr = char (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradeget_race50_clbk = char (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradeget_race50_ptr);
        using TRC_AutoTradeget_taxrate52_ptr = float (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradeget_taxrate52_clbk = float (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradeget_taxrate52_ptr);
        using TRC_AutoTradehis_income_money54_ptr = void (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradehis_income_money54_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradehis_income_money54_ptr);
        using TRC_AutoTradehistory_used_cheet_changetaxrate56_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, char*);
        using TRC_AutoTradehistory_used_cheet_changetaxrate56_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, unsigned int, char*, TRC_AutoTradehistory_used_cheet_changetaxrate56_ptr);
        using TRC_AutoTradesendmsg_taxrate58_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, int, char);
        using TRC_AutoTradesendmsg_taxrate58_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, int, char, TRC_AutoTradesendmsg_taxrate58_ptr);
        using TRC_AutoTradeset_owner60_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, struct CGuild*);
        using TRC_AutoTradeset_owner60_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, struct CGuild*, TRC_AutoTradeset_owner60_ptr);
        using TRC_AutoTradeset_suggested62_ptr = void (WINAPIV*)(struct TRC_AutoTrade*, char, unsigned int, char*, unsigned int);
        using TRC_AutoTradeset_suggested62_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, char, unsigned int, char*, unsigned int, TRC_AutoTradeset_suggested62_ptr);
        
        using TRC_AutoTradedtor_TRC_AutoTrade64_ptr = void (WINAPIV*)(struct TRC_AutoTrade*);
        using TRC_AutoTradedtor_TRC_AutoTrade64_clbk = void (WINAPIV*)(struct TRC_AutoTrade*, TRC_AutoTradedtor_TRC_AutoTrade64_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

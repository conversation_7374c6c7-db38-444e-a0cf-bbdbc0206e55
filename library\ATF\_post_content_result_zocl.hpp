// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _post_content_result_zocl
    {
        struct  _post_item
        {
            char byTableCode;
            unsigned __int16 wItemIndex;
            unsigned __int64 dwDur;
            unsigned int dwLv;
        };
        char byErrCode;
        unsigned int dwPostSerial;
        char wszContent[201];
        _post_item Item;
        unsigned int dwGold;
    public:
        _post_content_result_zocl();
        void ctor__post_content_result_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_post_content_result_zocl, 225>(), "_post_content_result_zocl");
END_ATF_NAMESPACE

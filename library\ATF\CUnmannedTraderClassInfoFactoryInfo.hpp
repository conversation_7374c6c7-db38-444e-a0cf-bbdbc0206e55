// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderClassInfoFactory.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderClassInfoFactoryctor_CUnmannedTraderClassInfoFactory2_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*);
        using CUnmannedTraderClassInfoFactoryctor_CUnmannedTraderClassInfoFactory2_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*, CUnmannedTraderClassInfoFactoryctor_CUnmannedTraderClassInfoFactory2_ptr);
        using CUnmannedTraderClassInfoFactoryCreate4_ptr = struct CUnmannedTraderClassInfo* (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*, char*, unsigned int);
        using CUnmannedTraderClassInfoFactoryCreate4_clbk = struct CUnmannedTraderClassInfo* (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*, char*, unsigned int, CUnmannedTraderClassInfoFactoryCreate4_ptr);
        using CUnmannedTraderClassInfoFactoryDestroy6_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*);
        using CUnmannedTraderClassInfoFactoryDestroy6_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*, CUnmannedTraderClassInfoFactoryDestroy6_ptr);
        using CUnmannedTraderClassInfoFactoryRegist8_ptr = bool (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*, struct CUnmannedTraderClassInfo*);
        using CUnmannedTraderClassInfoFactoryRegist8_clbk = bool (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*, struct CUnmannedTraderClassInfo*, CUnmannedTraderClassInfoFactoryRegist8_ptr);
        
        using CUnmannedTraderClassInfoFactorydtor_CUnmannedTraderClassInfoFactory10_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*);
        using CUnmannedTraderClassInfoFactorydtor_CUnmannedTraderClassInfoFactory10_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfoFactory*, CUnmannedTraderClassInfoFactorydtor_CUnmannedTraderClassInfoFactory10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

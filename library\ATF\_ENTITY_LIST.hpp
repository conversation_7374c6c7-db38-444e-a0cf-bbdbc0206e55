// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _ENTITY_LIST
    {
        char IsParticle;
        char IsFileExist;
        char Name[62];
        float FadeStart;
        float FadeEnd;
        unsigned __int16 Flag;
        unsigned __int16 ShaderID;
        float Factor[2];
    };
END_ATF_NAMESPACE

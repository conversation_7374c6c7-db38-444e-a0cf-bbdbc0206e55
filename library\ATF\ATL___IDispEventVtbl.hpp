// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct _IDispEventVtbl
        {
            HRESULT (WINAPIV *_LocDEQueryInterface)(_IDispEvent *_this, _GUID *, void **);
            unsigned int (WINAPIV *AddRef)(_IDispEvent *_this);
            unsigned int (WINAPIV *Release)(_IDispEvent *_this);
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

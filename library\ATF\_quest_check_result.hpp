// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _quest_check_result
    {
        struct _node
        {
            char byQuestDBSlot;
            char byActIndex;
            unsigned __int16 wCount;
            bool bORComplete;
        };
        char m_byCheckNum;
        _node m_List[30];
    public:
        void init();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_quest_check_result, 182>(), "_quest_check_result");
END_ATF_NAMESPACE

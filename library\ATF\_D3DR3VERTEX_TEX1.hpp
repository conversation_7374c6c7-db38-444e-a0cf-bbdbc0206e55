// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$25C8602110B22A2EB54BB3EE6752756B.hpp>
#include <$B3185CC329BDC5A6DA4B2C96D7DC3DD4.hpp>


START_ATF_NAMESPACE
    struct _D3DR3VERTEX_TEX1
    {
        $B3185CC329BDC5A6DA4B2C96D7DC3DD4 ___u0;
        float y;
        float z;
        $25C8602110B22A2EB54BB3EE6752756B ___u3;
        float ny;
        float nz;
        unsigned int color;
        float u;
        float v;
    };    
    static_assert(ATF::checkSize<_D3DR3VERTEX_TEX1, 36>(), "_D3DR3VERTEX_TEX1");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IDispatch.hpp>


START_ATF_NAMESPACE
    struct $F7AD3C731BAE9FE9EC401882837B8DB3
    {
        BYTE gap0[8];
        IDispatch **ppdispVal;
    };    
    static_assert(ATF::checkSize<$F7AD3C731BAE9FE9EC401882837B8DB3, 16>(), "$F7AD3C731BAE9FE9EC401882837B8DB3");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _guild_member_info
    {
        unsigned int dwSerial;
        char wszName[17];
        char byClassInGuild;
        char byLv;
        unsigned int dwPvpPoint;
        char byRank;
        char byGrade;
        struct CPlayer *pPlayer;
        bool bVote;
    public:
        bool IsFill();
        _guild_member_info();
        void ctor__guild_member_info();
        void init();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_guild_member_info, 48>(), "_guild_member_info");
END_ATF_NAMESPACE

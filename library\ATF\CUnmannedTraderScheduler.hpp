// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CMyTimer.hpp>
#include <_unmannedtrader_reserved_schedule_info.hpp>
#include <CUnmannedTraderSchedule.hpp>
#include <std___Vector_iterator.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderScheduler
    {
        int m_iOldDay;
        bool m_bLoad;
        CMyTimer *m_pkTimer;
        CLogFile *m_pkLogger;
        std::_Vector_iterator<CUnmannedTraderSchedule> m_iterSchedule;
        std::vector<CUnmannedTraderSchedule> m_veckSchdule;
    public:
        CUnmannedTraderScheduler();
        void ctor_CUnmannedTraderScheduler();
        void CheatPushLoad();
        void ClearAll();
        void CompleteClear(char byDBQueryRet, char byProcRet, char byType, unsigned int dwRegistSerial);
        static void Destroy();
        void DoDayChangedWork();
        std::_Vector_iterator<CUnmannedTraderSchedule>* FindItem(std::_Vector_iterator<CUnmannedTraderSchedule>* result, char byType, unsigned int dwRegistSerial);
        bool FindWaitItem();
        bool Init();
        static struct CUnmannedTraderScheduler* Instance();
        bool Load();
        void Log(char* fmt);
        void Loop();
        void PushLoad();
        void SetLogger(struct CLogFile* pkLogger);
        void Update(struct _unmannedtrader_reserved_schedule_info* pkInfo);
        ~CUnmannedTraderScheduler();
        void dtor_CUnmannedTraderScheduler();
    };
END_ATF_NAMESPACE

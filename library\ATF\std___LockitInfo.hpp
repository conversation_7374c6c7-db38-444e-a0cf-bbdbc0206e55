// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Lockit.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            
            using std___Lockitctor__Lockit1_ptr = int64_t (WINAPIV*)(struct std::_Lockit*, int);
            using std___Lockitctor__Lockit1_clbk = int64_t (WINAPIV*)(struct std::_Lockit*, int, std___Lockitctor__Lockit1_ptr);
            
            using std___Lockitdtor__Lockit2_ptr = int64_t (WINAPIV*)(struct std::_Lockit*);
            using std___Lockitdtor__Lockit2_clbk = int64_t (WINAPIV*)(struct std::_Lockit*, std___Lockitdtor__Lockit2_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

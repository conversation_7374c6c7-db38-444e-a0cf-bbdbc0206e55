// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct EmotionPresentationChecker
    {
        enum _CheckType
        {
            eDectectPlayer = 0x1,
            eSendHelpMsg = 0x2,
            eSupportOtherMon = 0x3,
            eHP_20_Down = 0x4,
            eReceiveDebuff = 0x5,
            eSendPcDebuff = 0x6,
            eIsTargey_N_Change = 0x7,
            eDieMonster = 0x8,
            eFirstDamage = 0x9,
            eIsTopAggroPC = 0xA,
        };
        bool m_bIsSet;
        char m_byType;
        unsigned __int16 m_wIndex;
        unsigned __int16 m_wRandIndex;
        struct CCharacter *m_pTarget;
    public:
        bool CheckEmotionState(struct CMonster* pThis, char byCheckType, struct CCharacter* pTarget);
        EmotionPresentationChecker();
        void ctor_EmotionPresentationChecker();
        void ReSet();
    };
END_ATF_NAMESPACE

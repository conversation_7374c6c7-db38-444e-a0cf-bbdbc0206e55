// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetFrameRate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNetFrameRatector_CNetFrameRate2_ptr = void (WINAPIV*)(struct CNetFrameRate*);
        using CNetFrameRatector_CNetFrameRate2_clbk = void (WINAPIV*)(struct CNetFrameRate*, CNetFrameRatector_CNetFrameRate2_ptr);
        using CNetFrameRateCalcFrameRate4_ptr = void (WINAPIV*)(struct CNetFrameRate*);
        using CNetFrameRateCalcFrameRate4_clbk = void (WINAPIV*)(struct CNetFrameRate*, CNetFrameRateCalcFrameRate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CFileTimeSpan.hpp>
#include <_FILETIME.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct  CFileTime : _FILETIME
        {
        public:
            CFileTime(struct _FILETIME* ft);
            void ctor_CFileTime(struct _FILETIME* ft);
            CFileTime(uint64_t nTime);
            void ctor_CFileTime(uint64_t nTime);
            CFileTime();
            void ctor_CFileTime();
            static struct CFileTime* GetTickCount(struct CFileTime* result);
            uint64_t GetTime();
            struct CFileTime* LocalToUTC(struct CFileTime* result);
            void SetTime(uint64_t nTime);
            struct CFileTime* UTCToLocal(struct CFileTime* result);
        };    
        static_assert(ATF::checkSize<ATL::CFileTime, 8>(), "ATL::CFileTime");
    }; // end namespace ATL
END_ATF_NAMESPACE

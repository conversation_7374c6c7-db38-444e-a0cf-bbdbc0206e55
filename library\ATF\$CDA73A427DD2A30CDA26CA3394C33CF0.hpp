// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $CDA73A427DD2A30CDA26CA3394C33CF0
    {
        BYTE gap0[8];
        int *pintVal;
    };    
    static_assert(ATF::checkSize<$CDA73A427DD2A30CDA26CA3394C33CF0, 16>(), "$CDA73A427DD2A30CDA26CA3394C33CF0");
END_ATF_NAMESPACE

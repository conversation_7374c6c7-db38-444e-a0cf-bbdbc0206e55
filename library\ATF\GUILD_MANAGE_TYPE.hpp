// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum GUILD_MANAGE_TYPE
    {
      guild_manage_type_propose_guild_battle = 0x0,
      guild_manage_type_expulse_member = 0x1,
      guild_manage_type_pop_money = 0x2,
      guild_manage_type_buy_emblem = 0x3,
      guild_manage_type_committee = 0x4,
      guild_manage_type_accept_or_refuse = 0x5,
      guild_manage_type_num = 0x6,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYSTEMTIME.hpp>
#include <tagNMHDR.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagNMDATETIMECHANGE
    {
        tagNMHDR nmhdr;
        unsigned int dwFlags;
        _SYSTEMTIME st;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

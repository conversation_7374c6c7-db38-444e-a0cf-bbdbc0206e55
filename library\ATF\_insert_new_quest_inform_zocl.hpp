// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_QUEST_DB_BASE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _insert_new_quest_inform_zocl
    {
        char byQuestDBSlot;
        _QUEST_DB_BASE::_LIST NewQuestData;
    public:
        _insert_new_quest_inform_zocl();
        void ctor__insert_new_quest_inform_zocl();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_insert_new_quest_inform_zocl, 14>(), "_insert_new_quest_inform_zocl");
END_ATF_NAMESPACE

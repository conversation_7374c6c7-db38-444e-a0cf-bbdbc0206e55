// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _nuclear_bomb_position_inform_zocl
    {
        char byRaceCode;
        char byUseClass;
        float zPos[3];
    public:
        _nuclear_bomb_position_inform_zocl();
        void ctor__nuclear_bomb_position_inform_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_nuclear_bomb_position_inform_zocl, 14>(), "_nuclear_bomb_position_inform_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTakeOut.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CTakeOutTakeOut_Lotto2_ptr = void (WINAPIV*)(int*, unsigned int);
        using CTakeOutTakeOut_Lotto2_clbk = void (WINAPIV*)(int*, unsigned int, CTakeOutTakeOut_Lotto2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

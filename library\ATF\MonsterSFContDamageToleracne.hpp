// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct MonsterSFContDamageToleracne
    {
        struct CMonster *m_pMonster;
        float m_fToleranceProb;
        float m_fToleranceProbMax;
        unsigned int m_dwLastUpdateTime;
    public:
        float GetToleranceProb();
        void Init(float fMaxTolValue);
        bool IsSFContDamage();
        MonsterSFContDamageToleracne();
        void ctor_MonsterSFContDamageToleracne();
        void OnlyOnceInit(struct CMonster* pMonster);
        void SetSFDamageToleracne_Variation(float fAddValue);
        void Update();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonsterHierarchyVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMonsterHierarchy
    {
        CMonsterHierarchyVtbl *vfptr;
        unsigned int m_dwTotalCount;
        struct CMonster *m_pThisMon;
        struct CMonster *m_pParentMon;
        unsigned int m_dwParentSerial;
        struct CMonster *m_pChildMon[3][10];
        unsigned int m_dwMonCount[3];
        char m_byChildMonSetNum;
        unsigned int m_dwChildRecallTime;
    public:
        CMonsterHierarchy();
        void ctor_CMonsterHierarchy();
        char Child<PERSON>ind<PERSON>ount();
        struct CMonster* GetChild(int nKind, int nIndex);
        unsigned int GetChildCount(int nKindIndex);
        struct CMonster* GetParent();
        void Init();
        void OnChildMonsterCreate(struct _monster_create_setdata* pData);
        void OnChildMonsterDestroy();
        void OnChildRegenLoop();
        void OnlyOnceInit(struct CMonster* pThis);
        int PopChildMon(struct CMonster* pMon);
        void PopChildMonAll();
        int PushChildMon(int nKind, struct CMonster* pMon);
        int SearchChildMon(struct CMonster* pMon);
        int SetParent(struct CMonster* pMon);
        ~CMonsterHierarchy();
        void dtor_CMonsterHierarchy();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

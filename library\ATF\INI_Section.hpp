// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <INI_Key.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct INI_Section
    {
        char m_strSection[64];
        std::vector<INI_Key *> m_KeyList;
    public:
        struct INI_Key* GetKey(char* strKey);
        INI_Section();
        void ctor_INI_Section();
        ~INI_Section();
        void dtor_INI_Section();
    };
END_ATF_NAMESPACE

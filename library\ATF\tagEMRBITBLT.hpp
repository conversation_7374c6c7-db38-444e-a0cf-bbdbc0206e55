// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RECTL.hpp>
#include <tagEMR.hpp>
#include <tagXFORM.hpp>


START_ATF_NAMESPACE
    struct tagEMRBITBLT
    {
        tagEMR emr;
        _RECTL rclBounds;
        int xDest;
        int yDest;
        int cxDest;
        int cyDest;
        unsigned int dwRop;
        int xSrc;
        int ySrc;
        tagXFORM xformSrc;
        unsigned int crBkColorSrc;
        unsigned int iUsageSrc;
        unsigned int offBmiSrc;
        unsigned int cbBmiSrc;
        unsigned int offBitsSrc;
        unsigned int cbBitsSrc;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_buyemblem
    {
        unsigned int in_guildserial;
        unsigned int in_emblemback;
        unsigned int in_emblemmark;
        int in_emblemdlant;
        unsigned int in_suggestorSerial;
        char in_date[4];
        unsigned int tmp_guildindex;
        char tmp_w_suggestorname[17];
        long double out_totalgold;
        long double out_totaldalant;
        char byProcRet;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_buyemblem, 72>(), "_qry_case_buyemblem");
END_ATF_NAMESPACE

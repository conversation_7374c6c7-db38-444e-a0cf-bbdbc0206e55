// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum _D3DLIGHTSTATETYPE
    {
      D3DLIGHTSTATE_MATERIAL = 0x1,
      D3DLIGHTSTATE_AMBIENT = 0x2,
      D3DL<PERSON>HTSTATE_COLORMODEL = 0x3,
      D3DLIGHTSTATE_FOGMODE = 0x4,
      D3DLIGHTSTATE_FOGSTART = 0x5,
      D3DLIGHTSTATE_FOGEND = 0x6,
      D3DLIGHTSTATE_FOGDENSITY = 0x7,
      D3DLIGHTSTATE_COLORVERTEX = 0x8,
      D3DL<PERSON>HTSTATE_FORCEDWORD = 0x7FFFFFFF,
    };
END_ATF_NAMESPACE

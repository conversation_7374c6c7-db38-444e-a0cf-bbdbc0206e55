// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuildVtbl.hpp>
#include <_guild_applier_download_zocl.hpp>
#include <_guild_applier_info.hpp>
#include <_guild_battle_suggest_matter.hpp>
#include <_guild_master_info.hpp>
#include <_guild_member_buddy_download_zocl.hpp>
#include <_guild_member_download_zocl.hpp>
#include <_guild_member_info.hpp>
#include <_guild_member_refresh_data.hpp>
#include <_guild_money_io_download_zocl.hpp>
#include <_guild_query_info_result_zocl.hpp>
#include <_io_money_data.hpp>
#include <_suggested_matter.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CGuild
    {
        struct MakeBuddyPacket
        {
            struct __l2
            {
                struct __guild_buddy_list
                {
                    unsigned int dwSerial;
                    unsigned __int16 wMapCode;
                    char byRegionIndex;
                };
            };
        };
        CGuildVtbl *vfptr;
        int m_nIndex;
        unsigned int m_dwSerial;
        char m_wszName[17];
        char m_aszName[17];
        char m_byGrade;
        long double m_dTotalDalant;
        long double m_dTotalGold;
        unsigned int m_dwEmblemBack;
        unsigned int m_dwEmblemMark;
        char m_byRace;
        char m_wszGreetingMsg[256];
        _guild_master_info m_MasterData;
        int m_nMemberNum;
        struct _guild_member_info *m_MemberData;
        struct _guild_member_info *m_pGuildCommittee[3];
        int m_nApplierNum;
        struct _guild_applier_info *m_ApplierData;
        bool m_bNowProcessSgtMter;
        unsigned int m_dwSuggesterSerial;
        _suggested_matter m_SuggestedMatter;
        _guild_battle_suggest_matter m_GuildBattleSugestMatter;
        bool m_bInGuildBattle;
        bool m_bPossibleElectMaster;
        unsigned int m_dwGuildBattleTotWin;
        unsigned int m_dwGuildBattleTotDraw;
        unsigned int m_dwGuildBattleTotLose;
        _guild_member_download_zocl *m_DownPacket_Member;
        _guild_applier_download_zocl *m_DownPacket_Applier;
        _guild_query_info_result_zocl *m_QueryPacket_Info;
        _guild_money_io_download_zocl *m_MoneyIO_List;
        _guild_member_buddy_download_zocl *m_Buddy_List;
        int m_nIOMoneyHistoryNum;
        _io_money_data m_IOMoneyHistory[100];
        bool m_bDBWait;
        bool m_bIOWait;
        bool m_bRankWait;
        char m_byMoneyOutputKind;
        int m_nTempMemberNum;
        unsigned int m_dwLastLoopTime;
        char m_szHistoryFileName[128];
    public:
        bool ActVote(struct _guild_member_info* pMemPtr, char byCode);
        void AddScheduleComplete(char byRet, struct CGuild* pSrcGuild);
        CGuild();
        void ctor_CGuild();
        void CancelSuggestedMatter();
        char CheckGuildBattleSuggestRequestToDestGuild(unsigned int dwSrcGuildSerial, unsigned int dwStartTimeInx, unsigned int dwMemberCntInx, unsigned int dwMapInx);
        void ClearGuildBattle();
        void ClearVote();
        void CompleteOutGuildbattleCost(unsigned int dwSrcGuildSerial, unsigned int dwStartTimeInx, unsigned int dwMemberCntInx, unsigned int dwMapInx);
        void CompleteSelectMasterLastConn(unsigned int dwLastConnTime);
        void Complete_DB_Update_Committee(char* pData);
        bool DB_Update_GuildMaster(struct _guild_member_info* pNewguildMaster);
        void DB_Update_GuildMaster_Complete(unsigned int in_guild_prev_masterSerial, char in_guild_prev_masterPrevGrade, unsigned int in_guild_new_masterSerial, char in_guild_new_masterPrevGrade);
        char DestGuildIsAvailableBattleRequestState();
        void EndRankJob();
        void EstGuild(unsigned int dwSerial, char* pwszName, char byRace, int nMemberNum, struct _guild_member_info* pEstMember);
        void ForceLeave(unsigned int dwMemberSerial);
        struct _guild_applier_info* GetApplierFromSerial(unsigned int dwApplierSerial);
        char GetGrade();
        char* GetGuildMasterName();
        unsigned int GetGuildMasterSerial();
        struct _guild_member_info* GetMemberFromSerial(unsigned int dwMemberSerial);
        int GetMemberNum();
        int GetMemberNumForJoin();
        char GetRace();
        long double GetTotalDalant();
        long double GetTotalGold();
        char GuildBattleSuggestRequestToDestGuild(unsigned int dwSrcGuildSerial, unsigned int dwStartTimeInx, unsigned int dwMemberCntInx, unsigned int dwMapInx);
        void IOMoney(char* pwszIOerName, unsigned int dwIOerSerial, long double dIODalant, long double dIOGold, long double dTotalDalant, long double dTotalGold, char* pbyDate, bool bInPut);
        void Init(int nIndex);
        void InitVote();
        bool IsFill();
        struct _guild_member_info* LoginMember(unsigned int dwMemberSerial, struct CPlayer* pPtr);
        bool LogoffMember(unsigned int dwMemberSerial);
        void Loop(bool bChangeDay);
        void MakeBuddyPacket();
        void MakeDownApplierPacket();
        void MakeDownMemberPacket();
        void MakeMoneyIOPacket();
        void MakeQueryInfoPacket();
        char ManageAcceptORRefuseGuildBattle(bool bAccept);
        char ManageBuyGuildEmblem(unsigned int dwBuyer, unsigned int dwBack, unsigned int dwMark);
        char ManageExpulseMember(unsigned int dwMemberSerial);
        char ManageGuildCommittee(unsigned int dwDestSerial, bool bAppoint);
        char ManagePopGuildMoney(unsigned int dwDest, unsigned int dwDalant, unsigned int dwGold);
        char ManageProposeGuildBattle(unsigned int dwDestGuild, unsigned int dwStartTimeIdx, unsigned int dwMemberCountIdx, unsigned int dwMapIdx);
        bool PopApplier(unsigned int dwApplierSerial, char byDelCode);
        bool PopMember(unsigned int dwMemberSerial);
        bool PushApplier(struct CPlayer* pApplier);
        void PushDQSDestGuildOutputGuildBattleCost();
        void PushDQSGuildMasterLastConnn();
        void PushDQSInGuildBattleCost();
        void PushDQSInGuildBattleRewardMoney();
        void PushDQSSourceGuildOutputGuildBattleCost();
        void PushHistory_IOMoney(bool bInput, char* pwszIOerName, unsigned int dwIOerSerial, long double dIODalant, long double dIOGold, long double dLeftDalant, long double dLeftGold, char* pbyDate);
        struct _guild_member_info* PushMember(struct _guild_member_info* pSheet);
        void RefreshGuildMemberData(struct _guild_member_refresh_data* pRefreshMember);
        bool RegSuggestedMatter(unsigned int dwSuggesterSerial, char byMatterType, unsigned int dwMatterDst, char* pwszComment, unsigned int dwMatterObj1, unsigned int dwMatterObj2, unsigned int dwMatterObj3);
        void Release();
        void ReleaseTemp();
        void SendMsg_AddJoinApplier(struct _guild_applier_info* p);
        void SendMsg_AlterMemberGrade();
        void SendMsg_AlterMemberState();
        void SendMsg_ApplyGuildBattleResultInform(char byRet, char* wszDestGuildName);
        void SendMsg_ChangeTaxRate(char byTax);
        void SendMsg_DelJoinApplier(struct _guild_applier_info* p, char byDelCode);
        void SendMsg_DownPacket(char bDowntype, struct _guild_member_info* pMem);
        int SendMsg_GuildBattleProposed(char* pwszName);
        void SendMsg_GuildBattleRefused(char* pwszName);
        void SendMsg_GuildBattleSuggestResult(char byRet, char* wszDestGuildName);
        void SendMsg_GuildDisjointInform();
        void SendMsg_GuildInfoUpdateInform();
        void SendMsg_GuildJoinAcceptInform(struct _guild_member_info* p, unsigned int dwAcceptSerial);
        void SendMsg_GuildMemberLogin(unsigned int dwSerial, uint16_t wMapCode, uint16_t wRegionIndex);
        void SendMsg_GuildMemberLogoff(unsigned int dwSerial);
        void SendMsg_GuildMemberPosInform(unsigned int dwSerial, uint16_t wMapCode, uint16_t wRegionIndex);
        void SendMsg_GuildOutputMoneyFail(unsigned int dwIOerSerial);
        void SendMsg_GuildRoomRented(char byRoomType);
        void SendMsg_IOMoney(unsigned int dwIOerSerial, long double dIODalant, long double dIOGold, bool bInPut, char* pbyDate);
        void SendMsg_LeaveMember(unsigned int dwMemberSerial, bool bSelf, bool bPunish);
        void SendMsg_ManageGuildCommitteeResult(bool bAppoint, char* pwszCommitteeName);
        void SendMsg_MasterElectPossible(bool bPossible);
        void SendMsg_QueryPacket_Info(int n);
        void SendMsg_VoteCancelInform();
        void SendMsg_VoteComplete(bool bPass);
        void SendMsg_VoteProcessInform_Continue(struct _guild_member_info* pMem);
        void SendMsg_VoteProcessInform_Start();
        void SendMsg_VoteState();
        void SendMsg_VoteStop(unsigned int dwMatterVoteSynKey);
        void SetCopmlteGuildBattleSuggest();
        void SetGreetingmsg_GUILD(char* wszgreetmsg);
        void SetGuild(unsigned int dwSerial, char byGrade, char byRace, char* pwszName, char* pwszGreetingMsg, unsigned int dwEmblemBack, unsigned int dwEmblemMark, int nNum, struct _guild_member_info* pEstMember, long double dTotalDalant, long double dTotalGold, unsigned int dwMasterSerial, char byMasterPrevGrade, int nIOMoneyHisNum, struct _io_money_data* pIOMonHisList, unsigned int dwGuildBattleTotalWinCnt, unsigned int dwGuildBattleTotalDrawCnt, unsigned int dwGuildBattleTotalLoseCnt);
        void SetGuildBattleMatter(unsigned int dwSrcGuildSerial, unsigned int dwStartTime, unsigned int dwNumber, unsigned int dwMapIdx);
        void SetTemp(char* pwszName);
        void SortRankInGuild();
        char SrcGuildIsAvailableBattleRequestState();
        void StartRankJob();
        void UpdateEmblem(unsigned int dwEmblemBack, unsigned int dwEmblemMark);
        void UpdateGrade(char byGrade);
        void UpdateGuildBattleWinCnt(unsigned int dwTotWin, unsigned int dwTotDraw, unsigned int dwTotLose);
        void UpdateUTATax(char byTaxRate);
        ~CGuild();
        void dtor_CGuild();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

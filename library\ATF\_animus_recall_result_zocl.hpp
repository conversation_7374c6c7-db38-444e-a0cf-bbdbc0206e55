// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _animus_recall_result_zocl
    {
        char byResultCode;
        unsigned int dwAnimusSerial;
        unsigned __int16 wAnimusHP;
        unsigned __int16 wAnimusFP;
        unsigned __int64 dwAnimusExp;
        unsigned __int16 wLeftFP;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

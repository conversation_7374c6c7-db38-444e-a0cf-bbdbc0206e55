// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitRight.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMoveMapLimitRightctor_CMoveMapLimitRight2_ptr = void (WINAPIV*)(struct CMoveMapLimitRight*, int);
        using CMoveMapLimitRightctor_CMoveMapLimitRight2_clbk = void (WINAPIV*)(struct CMoveMapLimitRight*, int, CMoveMapLimitRightctor_CMoveMapLimitRight2_ptr);
        using CMoveMapLimitRightCleanUp4_ptr = void (WINAPIV*)(struct CMoveMapLimitRight*);
        using CMoveMapLimitRightCleanUp4_clbk = void (WINAPIV*)(struct CMoveMapLimitRight*, CMoveMapLimitRightCleanUp4_ptr);
        using CMoveMapLimitRightCreate6_ptr = struct CMoveMapLimitRight* (WINAPIV*)(int);
        using CMoveMapLimitRightCreate6_clbk = struct CMoveMapLimitRight* (WINAPIV*)(int, CMoveMapLimitRightCreate6_ptr);
        using CMoveMapLimitRightCreateComplete8_ptr = void (WINAPIV*)(struct CMoveMapLimitRight*, struct CPlayer*);
        using CMoveMapLimitRightCreateComplete8_clbk = void (WINAPIV*)(struct CMoveMapLimitRight*, struct CPlayer*, CMoveMapLimitRightCreateComplete8_ptr);
        using CMoveMapLimitRightGetType10_ptr = int (WINAPIV*)(struct CMoveMapLimitRight*);
        using CMoveMapLimitRightGetType10_clbk = int (WINAPIV*)(struct CMoveMapLimitRight*, CMoveMapLimitRightGetType10_ptr);
        using CMoveMapLimitRightIsHaveRight12_ptr = bool (WINAPIV*)(struct CMoveMapLimitRight*);
        using CMoveMapLimitRightIsHaveRight12_clbk = bool (WINAPIV*)(struct CMoveMapLimitRight*, CMoveMapLimitRightIsHaveRight12_ptr);
        using CMoveMapLimitRightLoad14_ptr = void (WINAPIV*)(struct CMoveMapLimitRight*, struct CPlayer*);
        using CMoveMapLimitRightLoad14_clbk = void (WINAPIV*)(struct CMoveMapLimitRight*, struct CPlayer*, CMoveMapLimitRightLoad14_ptr);
        using CMoveMapLimitRightLogIn16_ptr = void (WINAPIV*)(struct CMoveMapLimitRight*, struct CPlayer*);
        using CMoveMapLimitRightLogIn16_clbk = void (WINAPIV*)(struct CMoveMapLimitRight*, struct CPlayer*, CMoveMapLimitRightLogIn16_ptr);
        using CMoveMapLimitRightLogOut18_ptr = void (WINAPIV*)(struct CMoveMapLimitRight*, struct CPlayer*);
        using CMoveMapLimitRightLogOut18_clbk = void (WINAPIV*)(struct CMoveMapLimitRight*, struct CPlayer*, CMoveMapLimitRightLogOut18_ptr);
        using CMoveMapLimitRightSetFlag20_ptr = void (WINAPIV*)(struct CMoveMapLimitRight*, int, bool);
        using CMoveMapLimitRightSetFlag20_clbk = void (WINAPIV*)(struct CMoveMapLimitRight*, int, bool, CMoveMapLimitRightSetFlag20_ptr);
        
        using CMoveMapLimitRightdtor_CMoveMapLimitRight24_ptr = void (WINAPIV*)(struct CMoveMapLimitRight*);
        using CMoveMapLimitRightdtor_CMoveMapLimitRight24_clbk = void (WINAPIV*)(struct CMoveMapLimitRight*, CMoveMapLimitRightdtor_CMoveMapLimitRight24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
#include <CPlayer.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CMoveMapLimitRightInfo
    {
        std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > m_vecRight;
    public:
        CMoveMapLimitRightInfo(struct CMoveMapLimitRightInfo* __that);
        void ctor_CMoveMapLimitRightInfo(struct CMoveMapLimitRightInfo* __that);
        CMoveMapLimitRightInfo();
        void ctor_CMoveMapLimitRightInfo();
        void CleanUp();
        void CreateComplete(struct CPlayer* pkPlayer);
        bool IsHaveRight(int iType);
        void Load(struct CPlayer* pkPlayer);
        void LogIn(struct CPlayer* pkPlayer);
        void LogOut(struct CPlayer* pkPlayer);
        bool Regist(int iType);
        void SetFlag(int iType, int iSubType, bool bFlag);
        ~CMoveMapLimitRightInfo();
        void dtor_CMoveMapLimitRightInfo();
    };    
    static_assert(ATF::checkSize<CMoveMapLimitRightInfo, 40>(), "CMoveMapLimitRightInfo");
END_ATF_NAMESPACE

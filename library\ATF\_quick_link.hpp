// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _quick_link
    {
        char byLinkIndex;
        unsigned __int16 wSerial;
    public:
        _quick_link();
        void ctor__quick_link();
        void init();
    };    
    static_assert(ATF::checkSize<_quick_link, 4>(), "_quick_link");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagPOINT.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagIMECHARPOSITION
    {
        unsigned int dwSize;
        unsigned int dwCharPos;
        tagPOINT pt;
        unsigned int cLineHeight;
        tagRECT rcDocument;
    };
END_ATF_NAMESPACE

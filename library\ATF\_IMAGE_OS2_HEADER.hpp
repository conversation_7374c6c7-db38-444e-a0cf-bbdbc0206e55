// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_OS2_HEADER
    {
        unsigned __int16 ne_magic;
        char ne_ver;
        char ne_rev;
        unsigned __int16 ne_enttab;
        unsigned __int16 ne_cbenttab;
        int ne_crc;
        unsigned __int16 ne_flags;
        unsigned __int16 ne_autodata;
        unsigned __int16 ne_heap;
        unsigned __int16 ne_stack;
        int ne_csip;
        int ne_sssp;
        unsigned __int16 ne_cseg;
        unsigned __int16 ne_cmod;
        unsigned __int16 ne_cbnrestab;
        unsigned __int16 ne_segtab;
        unsigned __int16 ne_rsrctab;
        unsigned __int16 ne_restab;
        unsigned __int16 ne_modtab;
        unsigned __int16 ne_imptab;
        int ne_nrestab;
        unsigned __int16 ne_cmovent;
        unsigned __int16 ne_align;
        unsigned __int16 ne_cres;
        char ne_exetyp;
        char ne_flagsothers;
        unsigned __int16 ne_pretthunks;
        unsigned __int16 ne_psegrefbytes;
        unsigned __int16 ne_swaparea;
        unsigned __int16 ne_expver;
    };
END_ATF_NAMESPACE

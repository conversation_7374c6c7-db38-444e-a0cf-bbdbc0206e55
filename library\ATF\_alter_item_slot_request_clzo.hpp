// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _alter_item_slot_request_clzo
    {
        struct  __list
        {
            char byStorageIndex;
            unsigned int dwItemSerial;
            char byClientSlotIndex;
        };
        char byNum;
        __list list[100];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_unmandtrader_buy_update_wait
    {
        struct __list
        {
            unsigned int dwRegistSerial;
            unsigned int dwSeller;
            char szAccountID[13];
            unsigned int dwAccountSerial;
            char wszName[17];
            char byProcRet;
            unsigned int dwTax;
            char byOldState;
            char byInveninx;
            unsigned int dwK;
            unsigned __int64 dwD;
            unsigned int dwU;
            unsigned int dwPrice;
            unsigned int dwT;
            unsigned __int64 lnUID;
        };
        unsigned __int16 wInx;
        unsigned int dwBuyer;
        char byRace;
        char byDivision;
        char byClass;
        char bySubClass;
        __int64 tResultTime;
        char byType;
        char byNum;
        __list List[10];
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_unmandtrader_buy_update_wait, 992>(), "_qry_case_unmandtrader_buy_update_wait");
END_ATF_NAMESPACE

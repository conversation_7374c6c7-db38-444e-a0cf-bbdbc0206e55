// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_MASTERY_PARAM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _MASTERY_PARAMAlterCumPerMast2_ptr = bool (WINAPIV*)(struct _MASTERY_PARAM*, char, char, unsigned int, unsigned int*);
        using _MASTERY_PARAMAlterCumPerMast2_clbk = bool (WINAPIV*)(struct _MASTERY_PARAM*, char, char, unsigned int, unsigned int*, _MASTERY_PARAMAlterCumPerMast2_ptr);
        using _MASTERY_PARAMGetAveForceMasteryPerClass4_ptr = float (WINAPIV*)(struct _MASTERY_PARAM*, char);
        using _MASTERY_PARAMGetAveForceMasteryPerClass4_clbk = float (WINAPIV*)(struct _MASTERY_PARAM*, char, _MASTERY_PARAMGetAveForceMasteryPerClass4_ptr);
        using _MASTERY_PARAMGetAveSkillMasteryPerClass6_ptr = float (WINAPIV*)(struct _MASTERY_PARAM*, char);
        using _MASTERY_PARAMGetAveSkillMasteryPerClass6_clbk = float (WINAPIV*)(struct _MASTERY_PARAM*, char, _MASTERY_PARAMGetAveSkillMasteryPerClass6_ptr);
        using _MASTERY_PARAMGetCumPerMast8_ptr = int (WINAPIV*)(struct _MASTERY_PARAM*, char, char);
        using _MASTERY_PARAMGetCumPerMast8_clbk = int (WINAPIV*)(struct _MASTERY_PARAM*, char, char, _MASTERY_PARAMGetCumPerMast8_ptr);
        using _MASTERY_PARAMGetEquipMastery10_ptr = char (WINAPIV*)(struct _MASTERY_PARAM*, int);
        using _MASTERY_PARAMGetEquipMastery10_clbk = char (WINAPIV*)(struct _MASTERY_PARAM*, int, _MASTERY_PARAMGetEquipMastery10_ptr);
        using _MASTERY_PARAMGetMasteryPerMast12_ptr = int (WINAPIV*)(struct _MASTERY_PARAM*, char, char);
        using _MASTERY_PARAMGetMasteryPerMast12_clbk = int (WINAPIV*)(struct _MASTERY_PARAM*, char, char, _MASTERY_PARAMGetMasteryPerMast12_ptr);
        using _MASTERY_PARAMGetSkillLv14_ptr = int (WINAPIV*)(struct _MASTERY_PARAM*, char);
        using _MASTERY_PARAMGetSkillLv14_clbk = int (WINAPIV*)(struct _MASTERY_PARAM*, char, _MASTERY_PARAMGetSkillLv14_ptr);
        using _MASTERY_PARAMInit16_ptr = bool (WINAPIV*)(struct _MASTERY_PARAM*, struct _STAT_DB_BASE*, char);
        using _MASTERY_PARAMInit16_clbk = bool (WINAPIV*)(struct _MASTERY_PARAM*, struct _STAT_DB_BASE*, char, _MASTERY_PARAMInit16_ptr);
        using _MASTERY_PARAMIsValidMasteryCode18_ptr = bool (WINAPIV*)(char, char);
        using _MASTERY_PARAMIsValidMasteryCode18_clbk = bool (WINAPIV*)(char, char, _MASTERY_PARAMIsValidMasteryCode18_ptr);
        using _MASTERY_PARAMSetStaticMember20_ptr = void (WINAPIV*)(struct CRecordData*, struct CRecordData*);
        using _MASTERY_PARAMSetStaticMember20_clbk = void (WINAPIV*)(struct CRecordData*, struct CRecordData*, _MASTERY_PARAMSetStaticMember20_ptr);
        using _MASTERY_PARAMUpdateCumPerMast22_ptr = void (WINAPIV*)(struct _MASTERY_PARAM*, char, char, unsigned int);
        using _MASTERY_PARAMUpdateCumPerMast22_clbk = void (WINAPIV*)(struct _MASTERY_PARAM*, char, char, unsigned int, _MASTERY_PARAMUpdateCumPerMast22_ptr);
        
        using _MASTERY_PARAMctor__MASTERY_PARAM24_ptr = void (WINAPIV*)(struct _MASTERY_PARAM*);
        using _MASTERY_PARAMctor__MASTERY_PARAM24_clbk = void (WINAPIV*)(struct _MASTERY_PARAM*, _MASTERY_PARAMctor__MASTERY_PARAM24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagSIZE.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagPROPPAGEINFO
    {
        unsigned int cb;
        wchar_t *pszTitle;
        tagSIZE size;
        wchar_t *pszDocString;
        wchar_t *pszHelpFile;
        unsigned int dwHelpContext;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

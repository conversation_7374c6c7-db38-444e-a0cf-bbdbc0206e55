// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGameObject.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CGameObjectAlterSec2_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectAlterSec2_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectAlterSec2_ptr);
        using CGameObjectAttackableHeight4_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectAttackableHeight4_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectAttackableHeight4_ptr);
        using CGameObjectBeTargeted6_ptr = void (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectBeTargeted6_clbk = void (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectBeTargeted6_ptr);
        
        using CGameObjectctor_CGameObject8_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectctor_CGameObject8_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectctor_CGameObject8_ptr);
        using CGameObjectCalcAbsPos10_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectCalcAbsPos10_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectCalcAbsPos10_ptr);
        using CGameObjectCalcCirclePlayerNum12_ptr = int (WINAPIV*)(struct CGameObject*, int);
        using CGameObjectCalcCirclePlayerNum12_clbk = int (WINAPIV*)(struct CGameObject*, int, CGameObjectCalcCirclePlayerNum12_ptr);
        using CGameObjectCalcCirclePlayerNum14_ptr = int (WINAPIV*)(struct CGameObject*, int, bool (WINAPIV*)(struct CGameObject*));
        using CGameObjectCalcCirclePlayerNum14_clbk = int (WINAPIV*)(struct CGameObject*, int, bool (WINAPIV*)(struct CGameObject*), CGameObjectCalcCirclePlayerNum14_ptr);
        using CGameObjectCalcCurHPRate16_ptr = uint16_t (WINAPIV*)(struct CGameObject*);
        using CGameObjectCalcCurHPRate16_clbk = uint16_t (WINAPIV*)(struct CGameObject*, CGameObjectCalcCurHPRate16_ptr);
        using CGameObjectCalcScrExtendPoint18_ptr = void (WINAPIV*)(struct CGameObject*, struct CRect*, struct CRect*);
        using CGameObjectCalcScrExtendPoint18_clbk = void (WINAPIV*)(struct CGameObject*, struct CRect*, struct CRect*, CGameObjectCalcScrExtendPoint18_ptr);
        using CGameObjectCalcScrNormalPoint20_ptr = void (WINAPIV*)(struct CGameObject*, struct CRect*);
        using CGameObjectCalcScrNormalPoint20_clbk = void (WINAPIV*)(struct CGameObject*, struct CRect*, CGameObjectCalcScrNormalPoint20_ptr);
        using CGameObjectCalcSecIndex22_ptr = unsigned int (WINAPIV*)(struct CGameObject*);
        using CGameObjectCalcSecIndex22_clbk = unsigned int (WINAPIV*)(struct CGameObject*, CGameObjectCalcSecIndex22_ptr);
        using CGameObjectCircleReport24_ptr = void (WINAPIV*)(struct CGameObject*, char*, char*, int, bool);
        using CGameObjectCircleReport24_clbk = void (WINAPIV*)(struct CGameObject*, char*, char*, int, bool, CGameObjectCircleReport24_ptr);
        using CGameObjectCircleReport26_ptr = void (WINAPIV*)(struct CGameObject*, char*, char*, int, unsigned int, bool);
        using CGameObjectCircleReport26_clbk = void (WINAPIV*)(struct CGameObject*, char*, char*, int, unsigned int, bool, CGameObjectCircleReport26_ptr);
        using CGameObjectCreate28_ptr = bool (WINAPIV*)(struct CGameObject*, struct _object_create_setdata*);
        using CGameObjectCreate28_clbk = bool (WINAPIV*)(struct CGameObject*, struct _object_create_setdata*, CGameObjectCreate28_ptr);
        using CGameObjectDestroy30_ptr = bool (WINAPIV*)(struct CGameObject*);
        using CGameObjectDestroy30_clbk = bool (WINAPIV*)(struct CGameObject*, CGameObjectDestroy30_ptr);
        using CGameObjectFixTargetWhile32_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, unsigned int);
        using CGameObjectFixTargetWhile32_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, unsigned int, CGameObjectFixTargetWhile32_ptr);
        using CGameObjectGetAttackDP34_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetAttackDP34_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetAttackDP34_ptr);
        using CGameObjectGetAttackLevel36_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetAttackLevel36_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetAttackLevel36_ptr);
        using CGameObjectGetAttackRange38_ptr = float (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetAttackRange38_clbk = float (WINAPIV*)(struct CGameObject*, CGameObjectGetAttackRange38_ptr);
        using CGameObjectGetAvoidRate40_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetAvoidRate40_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetAvoidRate40_ptr);
        using CGameObjectGetCurSecNum42_ptr = unsigned int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetCurSecNum42_clbk = unsigned int (WINAPIV*)(struct CGameObject*, CGameObjectGetCurSecNum42_ptr);
        using CGameObjectGetDefFC44_ptr = int (WINAPIV*)(struct CGameObject*, int, struct CCharacter*, int*);
        using CGameObjectGetDefFC44_clbk = int (WINAPIV*)(struct CGameObject*, int, struct CCharacter*, int*, CGameObjectGetDefFC44_ptr);
        using CGameObjectGetDefFacing46_ptr = float (WINAPIV*)(struct CGameObject*, int);
        using CGameObjectGetDefFacing46_clbk = float (WINAPIV*)(struct CGameObject*, int, CGameObjectGetDefFacing46_ptr);
        using CGameObjectGetDefGap48_ptr = float (WINAPIV*)(struct CGameObject*, int);
        using CGameObjectGetDefGap48_clbk = float (WINAPIV*)(struct CGameObject*, int, CGameObjectGetDefGap48_ptr);
        using CGameObjectGetDefSkill50_ptr = int (WINAPIV*)(struct CGameObject*, bool);
        using CGameObjectGetDefSkill50_clbk = int (WINAPIV*)(struct CGameObject*, bool, CGameObjectGetDefSkill50_ptr);
        using CGameObjectGetFireTol52_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetFireTol52_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetFireTol52_ptr);
        using CGameObjectGetGenAttackProb54_ptr = int (WINAPIV*)(struct CGameObject*, struct CCharacter*, int, bool);
        using CGameObjectGetGenAttackProb54_clbk = int (WINAPIV*)(struct CGameObject*, struct CCharacter*, int, bool, CGameObjectGetGenAttackProb54_ptr);
        using CGameObjectGetHP56_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetHP56_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetHP56_ptr);
        using CGameObjectGetLevel58_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetLevel58_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetLevel58_ptr);
        using CGameObjectGetMaxHP60_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetMaxHP60_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetMaxHP60_ptr);
        using CGameObjectGetObjName62_ptr = char* (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetObjName62_clbk = char* (WINAPIV*)(struct CGameObject*, CGameObjectGetObjName62_ptr);
        using CGameObjectGetObjRace64_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetObjRace64_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetObjRace64_ptr);
        using CGameObjectGetSoilTol66_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetSoilTol66_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetSoilTol66_ptr);
        using CGameObjectGetStun68_ptr = bool (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetStun68_clbk = bool (WINAPIV*)(struct CGameObject*, CGameObjectGetStun68_ptr);
        using CGameObjectGetUseSectorRange70_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetUseSectorRange70_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetUseSectorRange70_ptr);
        using CGameObjectGetWaterTol72_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetWaterTol72_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetWaterTol72_ptr);
        using CGameObjectGetWeaponAdjust74_ptr = float (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetWeaponAdjust74_clbk = float (WINAPIV*)(struct CGameObject*, CGameObjectGetWeaponAdjust74_ptr);
        using CGameObjectGetWeaponClass76_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetWeaponClass76_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetWeaponClass76_ptr);
        using CGameObjectGetWidth78_ptr = float (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetWidth78_clbk = float (WINAPIV*)(struct CGameObject*, CGameObjectGetWidth78_ptr);
        using CGameObjectGetWindTol80_ptr = int (WINAPIV*)(struct CGameObject*);
        using CGameObjectGetWindTol80_clbk = int (WINAPIV*)(struct CGameObject*, CGameObjectGetWindTol80_ptr);
        using CGameObjectInit82_ptr = void (WINAPIV*)(struct CGameObject*, struct _object_id*);
        using CGameObjectInit82_clbk = void (WINAPIV*)(struct CGameObject*, struct _object_id*, CGameObjectInit82_ptr);
        using CGameObjectIsAttackableInTown84_ptr = bool (WINAPIV*)(struct CGameObject*);
        using CGameObjectIsAttackableInTown84_clbk = bool (WINAPIV*)(struct CGameObject*, CGameObjectIsAttackableInTown84_ptr);
        using CGameObjectIsBeAttackedAble86_ptr = bool (WINAPIV*)(struct CGameObject*, bool);
        using CGameObjectIsBeAttackedAble86_clbk = bool (WINAPIV*)(struct CGameObject*, bool, CGameObjectIsBeAttackedAble86_ptr);
        using CGameObjectIsBeCirclePlayer88_ptr = bool (WINAPIV*)(struct CGameObject*, int);
        using CGameObjectIsBeCirclePlayer88_clbk = bool (WINAPIV*)(struct CGameObject*, int, CGameObjectIsBeCirclePlayer88_ptr);
        using CGameObjectIsBeDamagedAble90_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectIsBeDamagedAble90_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectIsBeDamagedAble90_ptr);
        using CGameObjectIsCircleObject92_ptr = bool (WINAPIV*)(struct CGameObject*, int, struct CGameObject*);
        using CGameObjectIsCircleObject92_clbk = bool (WINAPIV*)(struct CGameObject*, int, struct CGameObject*, CGameObjectIsCircleObject92_ptr);
        using CGameObjectIsInTown94_ptr = bool (WINAPIV*)(struct CGameObject*);
        using CGameObjectIsInTown94_clbk = bool (WINAPIV*)(struct CGameObject*, CGameObjectIsInTown94_ptr);
        using CGameObjectIsRecvableContEffect96_ptr = bool (WINAPIV*)(struct CGameObject*);
        using CGameObjectIsRecvableContEffect96_clbk = bool (WINAPIV*)(struct CGameObject*, CGameObjectIsRecvableContEffect96_ptr);
        using CGameObjectIsRewardExp98_ptr = bool (WINAPIV*)(struct CGameObject*);
        using CGameObjectIsRewardExp98_clbk = bool (WINAPIV*)(struct CGameObject*, CGameObjectIsRewardExp98_ptr);
        using CGameObjectIs_Battle_Mode100_ptr = bool (WINAPIV*)(struct CGameObject*);
        using CGameObjectIs_Battle_Mode100_clbk = bool (WINAPIV*)(struct CGameObject*, CGameObjectIs_Battle_Mode100_ptr);
        using CGameObjectLoop102_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectLoop102_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectLoop102_ptr);
        using CGameObjectOnLoop104_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectOnLoop104_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectOnLoop104_ptr);
        using CGameObjectOutOfSec106_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectOutOfSec106_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectOutOfSec106_ptr);
        using CGameObjectRecvKillMessage108_ptr = void (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectRecvKillMessage108_clbk = void (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectRecvKillMessage108_ptr);
        using CGameObjectRerangeSecIndex110_ptr = unsigned int (WINAPIV*)(struct CGameObject*, unsigned int, unsigned int);
        using CGameObjectRerangeSecIndex110_clbk = unsigned int (WINAPIV*)(struct CGameObject*, unsigned int, unsigned int, CGameObjectRerangeSecIndex110_ptr);
        using CGameObjectResetSector112_ptr = void (WINAPIV*)(struct CGameObject*, unsigned int, unsigned int);
        using CGameObjectResetSector112_clbk = void (WINAPIV*)(struct CGameObject*, unsigned int, unsigned int, CGameObjectResetSector112_ptr);
        using CGameObjectRobbedHP114_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, int);
        using CGameObjectRobbedHP114_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, int, CGameObjectRobbedHP114_ptr);
        using CGameObjectSFContDelMessage116_ptr = void (WINAPIV*)(struct CGameObject*, char, char, bool, bool);
        using CGameObjectSFContDelMessage116_clbk = void (WINAPIV*)(struct CGameObject*, char, char, bool, bool, CGameObjectSFContDelMessage116_ptr);
        using CGameObjectSFContInsertMessage118_ptr = void (WINAPIV*)(struct CGameObject*, char, char, bool, struct CPlayer*);
        using CGameObjectSFContInsertMessage118_clbk = void (WINAPIV*)(struct CGameObject*, char, char, bool, struct CPlayer*, CGameObjectSFContInsertMessage118_ptr);
        using CGameObjectSFContUpdateTimeMessage120_ptr = void (WINAPIV*)(struct CGameObject*, char, char, int);
        using CGameObjectSFContUpdateTimeMessage120_clbk = void (WINAPIV*)(struct CGameObject*, char, char, int, CGameObjectSFContUpdateTimeMessage120_ptr);
        using CGameObjectSF_AllContDamageForceRemove_Once122_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectSF_AllContDamageForceRemove_Once122_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectSF_AllContDamageForceRemove_Once122_ptr);
        using CGameObjectSF_AllContHelpForceRemove_Once124_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectSF_AllContHelpForceRemove_Once124_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectSF_AllContHelpForceRemove_Once124_ptr);
        using CGameObjectSF_AllContHelpSkillRemove_Once126_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectSF_AllContHelpSkillRemove_Once126_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectSF_AllContHelpSkillRemove_Once126_ptr);
        using CGameObjectSF_AttHPtoDstFP_Once128_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_AttHPtoDstFP_Once128_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_AttHPtoDstFP_Once128_ptr);
        using CGameObjectSF_ContDamageTimeInc_Once130_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_ContDamageTimeInc_Once130_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_ContDamageTimeInc_Once130_ptr);
        using CGameObjectSF_ContHelpTimeInc_Once132_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_ContHelpTimeInc_Once132_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_ContHelpTimeInc_Once132_ptr);
        using CGameObjectSF_ConvertMonsterTarget134_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_ConvertMonsterTarget134_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_ConvertMonsterTarget134_ptr);
        using CGameObjectSF_ConvertTargetDest136_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_ConvertTargetDest136_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_ConvertTargetDest136_ptr);
        using CGameObjectSF_DamageAndStun138_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_DamageAndStun138_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_DamageAndStun138_ptr);
        using CGameObjectSF_FPDec140_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_FPDec140_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_FPDec140_ptr);
        using CGameObjectSF_HPInc_Once142_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_HPInc_Once142_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_HPInc_Once142_ptr);
        using CGameObjectSF_IncHPCircleParty144_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_IncHPCircleParty144_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_IncHPCircleParty144_ptr);
        using CGameObjectSF_IncreaseDP146_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_IncreaseDP146_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_IncreaseDP146_ptr);
        using CGameObjectSF_LateContDamageRemove_Once148_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectSF_LateContDamageRemove_Once148_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectSF_LateContDamageRemove_Once148_ptr);
        using CGameObjectSF_LateContHelpForceRemove_Once150_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectSF_LateContHelpForceRemove_Once150_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectSF_LateContHelpForceRemove_Once150_ptr);
        using CGameObjectSF_LateContHelpSkillRemove_Once152_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectSF_LateContHelpSkillRemove_Once152_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectSF_LateContHelpSkillRemove_Once152_ptr);
        using CGameObjectSF_MakePortalReturnBindPositionPartyMember154_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, char*);
        using CGameObjectSF_MakePortalReturnBindPositionPartyMember154_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, char*, CGameObjectSF_MakePortalReturnBindPositionPartyMember154_ptr);
        using CGameObjectSF_MakeZeroAnimusRecallTimeOnce156_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_MakeZeroAnimusRecallTimeOnce156_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_MakeZeroAnimusRecallTimeOnce156_ptr);
        using CGameObjectSF_OthersContHelpSFRemove_Once158_ptr = bool (WINAPIV*)(struct CGameObject*, float);
        using CGameObjectSF_OthersContHelpSFRemove_Once158_clbk = bool (WINAPIV*)(struct CGameObject*, float, CGameObjectSF_OthersContHelpSFRemove_Once158_ptr);
        using CGameObjectSF_OverHealing_Once160_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_OverHealing_Once160_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_OverHealing_Once160_ptr);
        using CGameObjectSF_RecoverAllReturnStateAnimusHPFull162_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_RecoverAllReturnStateAnimusHPFull162_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_RecoverAllReturnStateAnimusHPFull162_ptr);
        using CGameObjectSF_ReleaseMonsterTarget164_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_ReleaseMonsterTarget164_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_ReleaseMonsterTarget164_ptr);
        using CGameObjectSF_RemoveAllContHelp_Once166_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_RemoveAllContHelp_Once166_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_RemoveAllContHelp_Once166_ptr);
        using CGameObjectSF_Resurrect_Once168_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*);
        using CGameObjectSF_Resurrect_Once168_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, CGameObjectSF_Resurrect_Once168_ptr);
        using CGameObjectSF_ReturnBindPosition170_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_ReturnBindPosition170_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_ReturnBindPosition170_ptr);
        using CGameObjectSF_SPDec172_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_SPDec172_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_SPDec172_ptr);
        using CGameObjectSF_STInc_Once174_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_STInc_Once174_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_STInc_Once174_ptr);
        using CGameObjectSF_SelfDestruction176_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_SelfDestruction176_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_SelfDestruction176_ptr);
        using CGameObjectSF_SkillContHelpTimeInc_Once178_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_SkillContHelpTimeInc_Once178_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_SkillContHelpTimeInc_Once178_ptr);
        using CGameObjectSF_Stun180_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_Stun180_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_Stun180_ptr);
        using CGameObjectSF_TeleportToDestination182_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, bool);
        using CGameObjectSF_TeleportToDestination182_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, bool, CGameObjectSF_TeleportToDestination182_ptr);
        using CGameObjectSF_TransDestHP184_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, char*);
        using CGameObjectSF_TransDestHP184_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, char*, CGameObjectSF_TransDestHP184_ptr);
        using CGameObjectSF_TransMonsterHP186_ptr = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float);
        using CGameObjectSF_TransMonsterHP186_clbk = bool (WINAPIV*)(struct CGameObject*, struct CCharacter*, float, CGameObjectSF_TransMonsterHP186_ptr);
        using CGameObjectSendMsg_BreakStop188_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectSendMsg_BreakStop188_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectSendMsg_BreakStop188_ptr);
        using CGameObjectSendMsg_FixPosition190_ptr = void (WINAPIV*)(struct CGameObject*, int);
        using CGameObjectSendMsg_FixPosition190_clbk = void (WINAPIV*)(struct CGameObject*, int, CGameObjectSendMsg_FixPosition190_ptr);
        using CGameObjectSendMsg_RealFixPosition192_ptr = void (WINAPIV*)(struct CGameObject*, bool);
        using CGameObjectSendMsg_RealFixPosition192_clbk = void (WINAPIV*)(struct CGameObject*, bool, CGameObjectSendMsg_RealFixPosition192_ptr);
        using CGameObjectSendMsg_RealMovePoint194_ptr = void (WINAPIV*)(struct CGameObject*, int);
        using CGameObjectSendMsg_RealMovePoint194_clbk = void (WINAPIV*)(struct CGameObject*, int, CGameObjectSendMsg_RealMovePoint194_ptr);
        using CGameObjectSendMsg_SetHPInform196_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectSendMsg_SetHPInform196_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectSendMsg_SetHPInform196_ptr);
        using CGameObjectSendMsg_StunInform198_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectSendMsg_StunInform198_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectSendMsg_StunInform198_ptr);
        using CGameObjectSetAttackPart200_ptr = void (WINAPIV*)(struct CGameObject*, int);
        using CGameObjectSetAttackPart200_clbk = void (WINAPIV*)(struct CGameObject*, int, CGameObjectSetAttackPart200_ptr);
        using CGameObjectSetBreakTranspar202_ptr = void (WINAPIV*)(struct CGameObject*, bool);
        using CGameObjectSetBreakTranspar202_clbk = void (WINAPIV*)(struct CGameObject*, bool, CGameObjectSetBreakTranspar202_ptr);
        using CGameObjectSetCurBspMap204_ptr = bool (WINAPIV*)(struct CGameObject*, struct CMapData*);
        using CGameObjectSetCurBspMap204_clbk = bool (WINAPIV*)(struct CGameObject*, struct CMapData*, CGameObjectSetCurBspMap204_ptr);
        using CGameObjectSetCurPos206_ptr = bool (WINAPIV*)(struct CGameObject*, float*);
        using CGameObjectSetCurPos206_clbk = bool (WINAPIV*)(struct CGameObject*, float*, CGameObjectSetCurPos206_ptr);
        using CGameObjectSetCurSecNum208_ptr = void (WINAPIV*)(struct CGameObject*, unsigned int);
        using CGameObjectSetCurSecNum208_clbk = void (WINAPIV*)(struct CGameObject*, unsigned int, CGameObjectSetCurSecNum208_ptr);
        using CGameObjectSetDamage210_ptr = int (WINAPIV*)(struct CGameObject*, int, struct CCharacter*, int, bool, int, unsigned int, bool);
        using CGameObjectSetDamage210_clbk = int (WINAPIV*)(struct CGameObject*, int, struct CCharacter*, int, bool, int, unsigned int, bool, CGameObjectSetDamage210_ptr);
        using CGameObjectSetHP212_ptr = bool (WINAPIV*)(struct CGameObject*, int, bool);
        using CGameObjectSetHP212_clbk = bool (WINAPIV*)(struct CGameObject*, int, bool, CGameObjectSetHP212_ptr);
        using CGameObjectSetMaxVersion214_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectSetMaxVersion214_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectSetMaxVersion214_ptr);
        using CGameObjectSetStun216_ptr = void (WINAPIV*)(struct CGameObject*, bool);
        using CGameObjectSetStun216_clbk = void (WINAPIV*)(struct CGameObject*, bool, CGameObjectSetStun216_ptr);
        using CGameObjectUpdateSecList218_ptr = bool (WINAPIV*)(struct CGameObject*);
        using CGameObjectUpdateSecList218_clbk = bool (WINAPIV*)(struct CGameObject*, CGameObjectUpdateSecList218_ptr);
        using CGameObject_ResetCirclePlayer220_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObject_ResetCirclePlayer220_clbk = void (WINAPIV*)(struct CGameObject*, CGameObject_ResetCirclePlayer220_ptr);
        
        using CGameObjectdtor_CGameObject225_ptr = void (WINAPIV*)(struct CGameObject*);
        using CGameObjectdtor_CGameObject225_clbk = void (WINAPIV*)(struct CGameObject*, CGameObjectdtor_CGameObject225_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

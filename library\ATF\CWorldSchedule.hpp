// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <CRecordData.hpp>
#include <_WorldSchedule_fld.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CWorldSchedule
    {
        bool m_bOper;
        CRecordData m_tblSch;
        int m_nMaxSchNum;
        int m_nSchCursor;
        int m_nCurHour;
        int m_nCurMin;
        int m_nCurMilSec;
        unsigned int m_dwLastCheckTime;
        CMyTimer m_tmrCheck;
    public:
        CWorldSchedule();
        void ctor_CWorldSchedule();
        int CalcScheduleCursor(int nHour, int nMin);
        void ChangeSchCursor(struct _WorldSchedule_fld* pFld, int nPassMin);
        void CheckSch();
        bool DataCheck();
        bool Init();
        void Loop();
        void PassOneStep();
        ~CWorldSchedule();
        void dtor_CWorldSchedule();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CWorldSchedule, 232>(), "CWorldSchedule");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $6A799DE577D2E0903028649896E19517
    {
      type_mon_dummy = 0x0,
      type_potal_dummy = 0x1,
      type_item_store_dummy = 0x2,
      type_start_dummy = 0x3,
      type_bind_dummy = 0x4,
      type_res_dummy = 0x5,
      type_stone_dummy = 0x6,
      type_keeper_dummy = 0x7,
      type_quest_dummy = 0x8,
      type_fail_dummy = 0x9,
    };
END_ATF_NAMESPACE

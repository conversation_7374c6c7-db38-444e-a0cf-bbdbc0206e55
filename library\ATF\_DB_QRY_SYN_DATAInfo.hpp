// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DB_QRY_SYN_DATA.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _DB_QRY_SYN_DATActor__DB_QRY_SYN_DATA2_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using _DB_QRY_SYN_DATActor__DB_QRY_SYN_DATA2_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, _DB_QRY_SYN_DATActor__DB_QRY_SYN_DATA2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

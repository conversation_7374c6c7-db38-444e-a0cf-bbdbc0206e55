// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CComBSTR.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            
            using ATL__CComBSTRdtor_CComBSTR1_ptr = void (WINAPIV*)(struct ATL::CComBSTR*);
            using ATL__CComBSTRdtor_CComBSTR1_clbk = void (WINAPIV*)(struct ATL::CComBSTR*, ATL__CComBSTRdtor_CComBSTR1_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleReservedScheduleListManager.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_ptr = void (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_clbk = void (WINAPIV*)(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_ptr = struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_clbk = struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* (WINAPIV*)(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, int, unsigned int, int, int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, int, unsigned int, int, int, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, int, unsigned int, char, char, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, int, unsigned int, char, char, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, char*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, char*, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, char*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, char*, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_ptr);
            
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

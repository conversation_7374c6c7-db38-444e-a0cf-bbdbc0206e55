// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FORCEKEY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _FORCEKEYCovDBKey2_ptr = int (WINAPIV*)(struct _FORCEKEY*);
        using _FORCEKEYCovDBKey2_clbk = int (WINAPIV*)(struct _FORCEKEY*, _FORCEKEYCovDBKey2_ptr);
        using _FORCEKEYGetIndex4_ptr = char (WINAPIV*)(struct _FORCEKEY*);
        using _FORCEKEYGetIndex4_clbk = char (WINAPIV*)(struct _FORCEKEY*, _FORCEKEYGetIndex4_ptr);
        using _FORCEKEYGetStat6_ptr = unsigned int (WINAPIV*)(struct _FORCEKEY*);
        using _FORCEKEYGetStat6_clbk = unsigned int (WINAPIV*)(struct _FORCEKEY*, _FORCEKEYGetStat6_ptr);
        using _FORCEKEYIsFilled8_ptr = bool (WINAPIV*)(struct _FORCEKEY*);
        using _FORCEKEYIsFilled8_clbk = bool (WINAPIV*)(struct _FORCEKEY*, _FORCEKEYIsFilled8_ptr);
        using _FORCEKEYLoadDBKey10_ptr = void (WINAPIV*)(struct _FORCEKEY*, int);
        using _FORCEKEYLoadDBKey10_clbk = void (WINAPIV*)(struct _FORCEKEY*, int, _FORCEKEYLoadDBKey10_ptr);
        using _FORCEKEYSetKey12_ptr = void (WINAPIV*)(struct _FORCEKEY*, char, unsigned int);
        using _FORCEKEYSetKey12_clbk = void (WINAPIV*)(struct _FORCEKEY*, char, unsigned int, _FORCEKEYSetKey12_ptr);
        using _FORCEKEYSetRelease14_ptr = void (WINAPIV*)(struct _FORCEKEY*);
        using _FORCEKEYSetRelease14_clbk = void (WINAPIV*)(struct _FORCEKEY*, _FORCEKEYSetRelease14_ptr);
        using _FORCEKEYSetStat16_ptr = void (WINAPIV*)(struct _FORCEKEY*, unsigned int);
        using _FORCEKEYSetStat16_clbk = void (WINAPIV*)(struct _FORCEKEY*, unsigned int, _FORCEKEYSetStat16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBilling.hpp>
#include <CNationSettingData.hpp>
#include <CPlayer.hpp>
#include <CashDbWorker.hpp>
#include <_CashShop_str_fld.hpp>
#include <_NameTxt_fld.hpp>


START_ATF_NAMESPACE
    struct  CNationSettingDataNULL : CNationSettingData
    {
    public:
        CNationSettingDataNULL();
        void ctor_CNationSettingDataNULL();
        bool CheckEnterWorldRequest(int n, char* pBuf);
        struct CBilling* CreateBilling();
        void CreateComplete(struct CPlayer* pOne);
        struct CashDbWorker* CreateWorker();
        int GetCashItemPrice(struct _CashShop_str_fld* pFld);
        char* GetItemName(struct _NameTxt_fld* pFld);
        int Init();
        bool IsApplyPcbangPrimium(struct CPlayer* pUser);
        bool IsNormal<PERSON>har(wchar_t wcChar);
        bool IsPersonalFreeFixedAmountBillingType(int16_t* pDest1, int16_t* pDest2);
        void Loop();
        void NetClose(struct CPlayer* pOne);
        void SendCashDBDSNRequest();
        void SetUnitPassiveValue(float* fUnitPv_DefFc);
        bool ValidMacAddress();
        ~CNationSettingDataNULL();
        void dtor_CNationSettingDataNULL();
    };    
    static_assert(ATF::checkSize<CNationSettingDataNULL, 448>(), "CNationSettingDataNULL");
END_ATF_NAMESPACE

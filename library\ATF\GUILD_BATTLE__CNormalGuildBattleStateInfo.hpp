// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleState.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStatector_CNormalGuildBattleState2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*);
            using GUILD_BATTLE__CNormalGuildBattleStatector_CNormalGuildBattleState2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, GUILD_BATTLE__CNormalGuildBattleStatector_CNormalGuildBattleState2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateEnter4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateEnter4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateEnter4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateEnter6_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateEnter6_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateEnter6_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateFin8_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateFin8_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateFin8_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateFin10_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateFin10_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateFin10_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateGoal12_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*);
            using GUILD_BATTLE__CNormalGuildBattleStateGoal12_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, GUILD_BATTLE__CNormalGuildBattleStateGoal12_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateLog14_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CNormalGuildBattle*, char*);
            using GUILD_BATTLE__CNormalGuildBattleStateLog14_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CNormalGuildBattle*, char*, GUILD_BATTLE__CNormalGuildBattleStateLog14_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateLoop16_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateLoop16_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateLoop16_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateLoop18_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateLoop18_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateLoop18_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStatedtor_CNormalGuildBattleState20_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*);
            using GUILD_BATTLE__CNormalGuildBattleStatedtor_CNormalGuildBattleState20_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleState*, GUILD_BATTLE__CNormalGuildBattleStatedtor_CNormalGuildBattleState20_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

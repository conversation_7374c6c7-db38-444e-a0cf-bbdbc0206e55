// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleState.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CNormalGuildBattleStateCountDown : CNormalGuildBattleState
        {
        public:
            CNormalGuildBattleStateCountDown();
            void ctor_CNormalGuildBattleStateCountDown();
            int Enter(struct CNormalGuildBattle* pkBattle);
            struct ATL::CTimeSpan* GetTerm(struct ATL::CTimeSpan* result);
            ~CNormalGuildBattleStateCountDown();
            void dtor_CNormalGuildBattleStateCountDown();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CNormalGuildBattleStateCountDown, 8>(), "GUILD_BATTLE::CNormalGuildBattleStateCountDown");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

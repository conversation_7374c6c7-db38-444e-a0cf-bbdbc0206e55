// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _ItemMakeData_fld : _base_fld
    {
        struct _material_list
        {
            char m_itmPdMat[8];
            int m_nPdMatNum;
        };
        struct _output_list
        {
            char m_itmPdOutput[8];
            unsigned int m_dwPdProp;
        };
        int m_nMakeMastery;
        char m_strCivil[64];
        _material_list m_listMaterial[5];
        _output_list m_listOutput[30];
    };
END_ATF_NAMESPACE

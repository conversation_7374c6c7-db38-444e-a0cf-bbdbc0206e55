// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CCmdUIVtbl
    {
        void (WINAPIV *Enable)(CCmdUI *_this, int);
        void (WINAPIV *SetCheck)(CCmdUI *_this, int);
        void (WINAPIV *SetRadio)(CCmdUI *_this, int);
        void (WINAPIV *SetText)(CCmdUI *_this, const char *);
    };
END_ATF_NAMESPACE

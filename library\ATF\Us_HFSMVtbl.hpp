// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct Us_HFSMVtbl
    {
        void *(WINAPIV *__vecDelDtor)(struct Us_HFSM *_this, unsigned int);
        void (WINAPIV *OnProcess)(struct Us_HFSM *_this, unsigned int);
        void (WINAPIV *Init)(struct Us_HFSM *_this);
        int (WINAPIV *SetMyData)(struct Us_HFSM *_this, struct UsStateTBL *, void *);
    };
END_ATF_NAMESPACE

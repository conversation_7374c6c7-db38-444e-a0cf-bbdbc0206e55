// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _sel_char_result_zone
    {
        char byRetCode;
        char bySlotIndex;
        unsigned int dwWorldSerial;
        unsigned int dwDalant;
        unsigned int dwGold;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_sel_char_result_zone, 14>(), "_sel_char_result_zone");
END_ATF_NAMESPACE

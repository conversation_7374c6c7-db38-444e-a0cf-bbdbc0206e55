// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _economy_history_data
    {
        long double dTradeGold[3];
        long double dTradeDalant[3];
        unsigned __int16 wEconomyGuide[3];
        long double dOreMineCount[3][3];
        long double dOreCutCount[3][3];
    public:
        void Init();
        _economy_history_data();
        void ctor__economy_history_data();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_economy_history_data, 200>(), "_economy_history_data");
END_ATF_NAMESPACE

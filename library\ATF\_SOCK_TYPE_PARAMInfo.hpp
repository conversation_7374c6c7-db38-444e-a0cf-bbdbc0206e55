// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SOCK_TYPE_PARAM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _SOCK_TYPE_PARAMctor__SOCK_TYPE_PARAM2_ptr = void (WINAPIV*)(struct _SOCK_TYPE_PARAM*);
        using _SOCK_TYPE_PARAMctor__SOCK_TYPE_PARAM2_clbk = void (WINAPIV*)(struct _SOCK_TYPE_PARAM*, _SOCK_TYPE_PARAMctor__SOCK_TYPE_PARAM2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

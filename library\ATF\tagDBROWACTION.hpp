// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum tagDBROWACTION
    {
      DBROWACTION_IGNORE = 0x0,
      DBROWACTION_UPDATE = 0x1,
      DBROWACTION_DELETE = 0x2,
      DBROWACTION_ADD = 0x3,
      DBROWACTION_LOCK = 0x4,
      DBROWACTION_UNLOCK = 0x5,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitRightInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMoveMapLimitRightInfoctor_CMoveMapLimitRightInfo2_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitRightInfoctor_CMoveMapLimitRightInfo2_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CMoveMapLimitRightInfo*, CMoveMapLimitRightInfoctor_CMoveMapLimitRightInfo2_ptr);
        
        using CMoveMapLimitRightInfoctor_CMoveMapLimitRightInfo4_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitRightInfoctor_CMoveMapLimitRightInfo4_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, CMoveMapLimitRightInfoctor_CMoveMapLimitRightInfo4_ptr);
        using CMoveMapLimitRightInfoCleanUp6_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitRightInfoCleanUp6_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, CMoveMapLimitRightInfoCleanUp6_ptr);
        using CMoveMapLimitRightInfoCreateComplete8_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CPlayer*);
        using CMoveMapLimitRightInfoCreateComplete8_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CPlayer*, CMoveMapLimitRightInfoCreateComplete8_ptr);
        using CMoveMapLimitRightInfoIsHaveRight10_ptr = bool (WINAPIV*)(struct CMoveMapLimitRightInfo*, int);
        using CMoveMapLimitRightInfoIsHaveRight10_clbk = bool (WINAPIV*)(struct CMoveMapLimitRightInfo*, int, CMoveMapLimitRightInfoIsHaveRight10_ptr);
        using CMoveMapLimitRightInfoLoad12_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CPlayer*);
        using CMoveMapLimitRightInfoLoad12_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CPlayer*, CMoveMapLimitRightInfoLoad12_ptr);
        using CMoveMapLimitRightInfoLogIn14_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CPlayer*);
        using CMoveMapLimitRightInfoLogIn14_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CPlayer*, CMoveMapLimitRightInfoLogIn14_ptr);
        using CMoveMapLimitRightInfoLogOut16_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CPlayer*);
        using CMoveMapLimitRightInfoLogOut16_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, struct CPlayer*, CMoveMapLimitRightInfoLogOut16_ptr);
        using CMoveMapLimitRightInfoRegist18_ptr = bool (WINAPIV*)(struct CMoveMapLimitRightInfo*, int);
        using CMoveMapLimitRightInfoRegist18_clbk = bool (WINAPIV*)(struct CMoveMapLimitRightInfo*, int, CMoveMapLimitRightInfoRegist18_ptr);
        using CMoveMapLimitRightInfoSetFlag20_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, int, int, bool);
        using CMoveMapLimitRightInfoSetFlag20_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, int, int, bool, CMoveMapLimitRightInfoSetFlag20_ptr);
        
        using CMoveMapLimitRightInfodtor_CMoveMapLimitRightInfo26_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitRightInfodtor_CMoveMapLimitRightInfo26_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfo*, CMoveMapLimitRightInfodtor_CMoveMapLimitRightInfo26_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

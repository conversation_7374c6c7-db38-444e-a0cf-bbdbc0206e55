// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetCriticalSection.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNetCriticalSectionctor_CNetCriticalSection2_ptr = void (WINAPIV*)(struct CNetCriticalSection*);
        using CNetCriticalSectionctor_CNetCriticalSection2_clbk = void (WINAPIV*)(struct CNetCriticalSection*, CNetCriticalSectionctor_CNetCriticalSection2_ptr);
        using CNetCriticalSectionLock4_ptr = void (WINAPIV*)(struct CNetCriticalSection*);
        using CNetCriticalSectionLock4_clbk = void (WINAPIV*)(struct CNetCriticalSection*, CNetCriticalSectionLock4_ptr);
        using CNetCriticalSectionUnlock6_ptr = void (WINAPIV*)(struct CNetCriticalSection*);
        using CNetCriticalSectionUnlock6_clbk = void (WINAPIV*)(struct CNetCriticalSection*, CNetCriticalSectionUnlock6_ptr);
        
        using CNetCriticalSectiondtor_CNetCriticalSection10_ptr = void (WINAPIV*)(struct CNetCriticalSection*);
        using CNetCriticalSectiondtor_CNetCriticalSection10_clbk = void (WINAPIV*)(struct CNetCriticalSection*, CNetCriticalSectiondtor_CNetCriticalSection10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $703BA223585765AA24C1FD222C2EE9CB
    {
      qry_case_reged = 0x0,
      qry_case_insert = 0x1,
      qry_case_delete = 0x2,
      qry_case_load = 0x3,
      qry_case_save = 0x4,
      qry_case_logout = 0x5,
      qry_case_lobby = 0x6,
      log_case_lv = 0x7,
      log_case_usernum = 0x8,
      log_case_economy = 0x9,
      log_case_charselect = 0xA,
      qry_case_pvpinfosave = 0xB,
      qry_case_contsave = 0xC,
      qry_case_addpvppoint = 0xD,
      qry_case_insertitem = 0xE,
      qry_case_insertguild = 0xF,
      qry_case_joinacguild = 0x10,
      qry_case_selfleave = 0x11,
      qry_case_forceleave = 0x12,
      qry_case_inputgmoney = 0x13,
      qry_case_outputgmoney = 0x14,
      qry_case_buyemblem = 0x15,
      qry_case_disjointguild = 0x16,
      qry_case_alive_char = 0x17,
      log_change_class_after_init_class = 0x18,
      qry_case_sendwebracebosssms = 0x19,
      qry_case_addguildbattleschedule = 0x1E,
      qry_case_updatewinloseguildbattlerank = 0x1F,
      qry_case_updatedrawguildbattlerank = 0x20,
      qry_case_loadguildbattlerank = 0x21,
      qry_case_createguildbattleranktable = 0x22,
      qry_case_clearguildbattlerank = 0x23,
      qry_case_updateclearguildbattleDayInfo = 0x24,
      qry_case_in_guildbattlecost = 0x25,
      qry_case_src_guild_out_guildbattlecost = 0x26,
      qry_case_in_guildbattlerewardmoney = 0x27,
      qry_case_in_guildbattlerankclear = 0x28,
      qry_case_updatereservedschedule = 0x29,
      qry_case_updatetotalguildrank = 0x2B,
      qry_case_updatepvpguildrank = 0x2C,
      qry_case_updateweeklypvpguildrankowner = 0x2D,
      qry_case_updateclearweeklyguildpvppointsum = 0x2E,
      qry_case_updateweeklyguildpvppointsum = 0x2F,
      qry_case_update_guildmaster = 0x31,
      qry_case_macro_dbupdate = 0x32,
      qry_case_in_trc_info = 0x33,
      qry_case_in_atrade_tax = 0x34,
      qry_case_automine = 0x35,
      qry_case_out_automine_chargecost = 0x36,
      qry_case_amine_personal = 0x38,
      qry_case_load_guildbattle_totalrecord = 0x39,
      qry_case_unmandtrader_selectreservedschedule = 0x3A,
      qry_case_unmandtrader_updateitemstate = 0x3B,
      qry_case_unmandtrader_registsingleitem = 0x3C,
      qry_case_unmandtrader_cancelitem = 0x3D,
      qry_case_unmandtrader_time_out_cancelitem = 0x3E,
      qry_case_unmandtrader_update_reprice = 0x3F,
      qry_case_unmandtrader_buy_get_info = 0x40,
      qry_case_unmandtrader_buy_update_wait = 0x41,
      qry_case_unmandtrader_buy_update_rollback = 0x42,
      qry_case_unmannedtrader_lazy_clean = 0x43,
      qry_case_unmandtrader_buy_update_complete = 0x44,
      qry_case_guildroom_insert = 0x47,
      qry_case_guildroom_update = 0x48,
      qry_case_greetingmsg_gm = 0x4A,
      qry_case_greetingmsg_raceboss = 0x4B,
      qry_case_greetingmsg_guild = 0x4C,
      qry_case_post_list_regi = 0x4D,
      qry_case_post_send = 0x4E,
      qry_case_post_storage_list_get = 0x4F,
      qry_case_post_return_list_get = 0x50,
      qry_case_post_content_get = 0x52,
      qry_case_init_classrefinecount = 0x53,
      qry_case_guild_battel_result_log = 0x54,
      qry_case_rank_racerank_step1 = 0x55,
      qry_case_rank_racerank_step2 = 0x56,
      qry_case_rank_racerank_step3 = 0x57,
      qry_case_rank_racerank_step4 = 0x58,
      qry_case_rank_racerank_step5 = 0x59,
      qry_case_rank_racerank_step6 = 0x5A,
      qry_case_rank_racerank_step7 = 0x5B,
      qry_case_rank_racerank_step8 = 0x5C,
      qry_case_rank_racerank_step9 = 0x5D,
      qry_case_rank_racerank_step10 = 0x5E,
      qry_case_rank_racerank_step11 = 0x5F,
      qry_case_rank_check_exist_racerank_table = 0x60,
      qry_case_rank_guildrank_step1 = 0x61,
      qry_case_rank_guildrank_step2 = 0x62,
      qry_case_rank_guildrank_step3 = 0x63,
      qry_case_rank_guildrank_step4 = 0x64,
      qry_case_rank_rankinguild_step1 = 0x65,
      qry_case_rank_rankinguild_step2 = 0x66,
      qry_case_rank_rankinguild_step3 = 0x67,
      qry_case_rank_rankinguild_step4 = 0x68,
      qry_case_rank_rankinguild_step5 = 0x69,
      qry_case_rank_rankinguild_step6 = 0x6A,
      qry_case_rank_update_and_select_garde = 0x6B,
      qry_case_unmandtrader_log_in_proc_update_complete = 0x6F,
      qry_case_store_limit_item_update = 0x70,
      qry_case_store_instance_store_update = 0x71,
      qry_case_regist_candidate_2st = 0x72,
      qry_case_elect_patriarch_score = 0x73,
      qry_case_elect_patriarch_classtype = 0x74,
      qry_case_insert_candidate = 0x75,
      qry_case_dest_guild_out_guildbattlecost = 0x76,
      qry_case_insert_elect = 0x77,
      qry_case_update_elect = 0x78,
      qry_case_update_refund = 0x79,
      qry_case_select_elect = 0x7A,
      qry_case_request_refund = 0x7B,
      qry_case_itemcharge_refund = 0x7C,
      qry_case_insert_patriarch = 0x7D,
      qry_case_discharge_patriarch = 0x7E,
      qry_case_update_punishment = 0x7F,
      qry_case_select_charserial = 0x80,
      qry_case_post_serial_check = 0x81,
      qry_case_select_patriarch_comm = 0x82,
      qry_case_insert_patriarch_comm = 0x83,
      qry_case_update_patriarch_comm = 0x84,
      qry_case_delete_patriarch_comm = 0x85,
      qry_case_update_next_honor_guild = 0x86,
      qry_case_update_change_honor_guild = 0x87,
      qry_case_honor_guild_in_atrade_tax = 0x88,
      qry_case_insert_racebattle_log = 0x89,
      qry_case_is_valid_character = 0x8A,
      qry_case_update_votetime = 0x8B,
      qry_case_unmandtrader_re_registsingleitem = 0x8C,
      qry_case_unmandtrader_cheat_updateregisttime = 0x8D,
      qry_case_raceboss_accumulation_winrate = 0x8E,
      qry_case_update_user_guild_data = 0x8F,
      qry_case_select_guild_master_lastconn = 0x90,
      qry_case_character_rename = 0x91,
      qry_case_update_character_rename = 0x92,
      qry_case_unmandtrader_re_registsingleitem_roll_back = 0x93,
      qry_case_select_pcbang_item = 0x94,
      qry_case_update_vote_available = 0x95,
      qry_case_update_player_vote_info = 0x96,
      qry_case_cheat_player_vote_info = 0x97,
      qry_case_update_server_reset_token = 0x98,
      qry_case_select_timelimit_info = 0x99,
      qry_case_update_timelimit_info = 0x9A,
      qry_case_insert_timelimit_info = 0x9B,
      qry_case_select_cash_limsale = 0x9D,
      qry_case_update_cash_limsale = 0x9E,
      qry_case_manage_client_set_limit_run = 0xA1,
      qry_case_update_goldenboxitem = 0xA5,
      qry_case_lobby_logout = 0xAB,
      qry_case_insert_orelog = 0xAC,
      qry_case_update_data_for_post_send = 0xAF,
      qry_case_update_data_for_trade = 0xB0,
    };
END_ATF_NAMESPACE

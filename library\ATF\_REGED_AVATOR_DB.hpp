// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_EQUIPKEY.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _REGED_AVATOR_DB
    {
        char m_wszAvatorName[17];
        unsigned int m_dwRecordNum;
        char m_byRaceSexCode;
        char m_bySlotIndex;
        char m_szClassCode[5];
        char m_byLevel;
        unsigned int m_dwDalant;
        unsigned int m_dwGold;
        unsigned int m_dwBaseShape;
        _EQUIPKEY m_EquipKey[8];
        char m_byEquipLv[8];
        unsigned int m_dwLastConnTime;
    public:
        void Init();
        _REGED_AVATOR_DB();
        void ctor__REGED_AVATOR_DB();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_REGED_AVATOR_DB, 69>(), "_REGED_AVATOR_DB");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _animus_create_zocl
    {
        unsigned __int16 wIndex;
        unsigned __int16 wRecIndex;
        unsigned int dwSerial;
        __int16 zPos[3];
        char byLv;
        unsigned int dwMasterSerial;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

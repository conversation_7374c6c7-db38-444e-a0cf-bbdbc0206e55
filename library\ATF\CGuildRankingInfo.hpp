// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuildRanking.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CGuildRankingApplyGuildGrade2_ptr = void (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingApplyGuildGrade2_clbk = void (WINAPIV*)(struct CGuildRanking*, CGuildRankingApplyGuildGrade2_ptr);
        using CGuildRankingApplyRankInGuild4_ptr = bool (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingApplyRankInGuild4_clbk = bool (WINAPIV*)(struct CGuildRanking*, CGuildRankingApplyRankInGuild4_ptr);
        
        using CGuildRankingctor_CGuildRanking6_ptr = void (WINAPIV*)(struct CGuildRanking*);
        using CG<PERSON>Rankingctor_CGuildRanking6_clbk = void (WINAPIV*)(struct CGuildRanking*, CGuildRankingctor_CGuildRanking6_ptr);
        using CGuildRankingCheckAndCreateTodayGuildRankTable8_ptr = bool (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingCheckAndCreateTodayGuildRankTable8_clbk = bool (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingCheckAndCreateTodayGuildRankTable8_ptr);
        using CGuildRankingCheckGuildCheckSum10_ptr = bool (WINAPIV*)(struct CGuildRanking*, unsigned int, char*, long double*, long double*);
        using CGuildRankingCheckGuildCheckSum10_clbk = bool (WINAPIV*)(struct CGuildRanking*, unsigned int, char*, long double*, long double*, CGuildRankingCheckGuildCheckSum10_ptr);
        using CGuildRankingCheckMaxGuildMoney12_ptr = void (WINAPIV*)(struct CGuildRanking*, unsigned int, char*, long double*, long double*);
        using CGuildRankingCheckMaxGuildMoney12_clbk = void (WINAPIV*)(struct CGuildRanking*, unsigned int, char*, long double*, long double*, CGuildRankingCheckMaxGuildMoney12_ptr);
        using CGuildRankingClearApplyRankInGuildJobOffset14_ptr = void (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingClearApplyRankInGuildJobOffset14_clbk = void (WINAPIV*)(struct CGuildRanking*, CGuildRankingClearApplyRankInGuildJobOffset14_ptr);
        using CGuildRankingClearGuildGrade16_ptr = void (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingClearGuildGrade16_clbk = void (WINAPIV*)(struct CGuildRanking*, CGuildRankingClearGuildGrade16_ptr);
        using CGuildRankingClearGuildSerial18_ptr = void (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingClearGuildSerial18_clbk = void (WINAPIV*)(struct CGuildRanking*, CGuildRankingClearGuildSerial18_ptr);
        using CGuildRankingClearRefreshData20_ptr = void (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingClearRefreshData20_clbk = void (WINAPIV*)(struct CGuildRanking*, CGuildRankingClearRefreshData20_ptr);
        using CGuildRankingGetNextGuildSerial22_ptr = unsigned int (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingGetNextGuildSerial22_clbk = unsigned int (WINAPIV*)(struct CGuildRanking*, CGuildRankingGetNextGuildSerial22_ptr);
        using CGuildRankingGetRankInGuildJobOffset24_ptr = int (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingGetRankInGuildJobOffset24_clbk = int (WINAPIV*)(struct CGuildRanking*, CGuildRankingGetRankInGuildJobOffset24_ptr);
        using CGuildRankingInit26_ptr = bool (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingInit26_clbk = bool (WINAPIV*)(struct CGuildRanking*, CGuildRankingInit26_ptr);
        using CGuildRankingLoad28_ptr = bool (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingLoad28_clbk = bool (WINAPIV*)(struct CGuildRanking*, CGuildRankingLoad28_ptr);
        using CGuildRankingLoadAllGuildData30_ptr = bool (WINAPIV*)(struct CGuildRanking*, struct _worlddb_guild_info*);
        using CGuildRankingLoadAllGuildData30_clbk = bool (WINAPIV*)(struct CGuildRanking*, struct _worlddb_guild_info*, CGuildRankingLoadAllGuildData30_ptr);
        using CGuildRankingLoadGuildMoneyIOInfo32_ptr = bool (WINAPIV*)(struct CGuildRanking*, unsigned int, struct _io_money_data*, int*);
        using CGuildRankingLoadGuildMoneyIOInfo32_clbk = bool (WINAPIV*)(struct CGuildRanking*, unsigned int, struct _io_money_data*, int*, CGuildRankingLoadGuildMoneyIOInfo32_ptr);
        using CGuildRankingLoadMemberInfo34_ptr = bool (WINAPIV*)(struct CGuildRanking*, unsigned int, unsigned int, struct _guild_member_info*, uint16_t*);
        using CGuildRankingLoadMemberInfo34_clbk = bool (WINAPIV*)(struct CGuildRanking*, unsigned int, unsigned int, struct _guild_member_info*, uint16_t*, CGuildRankingLoadMemberInfo34_ptr);
        using CGuildRankingSetLoadAllGuildInfo36_ptr = bool (WINAPIV*)(struct CGuildRanking*, struct _worlddb_guild_info*);
        using CGuildRankingSetLoadAllGuildInfo36_clbk = bool (WINAPIV*)(struct CGuildRanking*, struct _worlddb_guild_info*, CGuildRankingSetLoadAllGuildInfo36_ptr);
        using CGuildRankingSetLogger38_ptr = void (WINAPIV*)(struct CGuildRanking*, struct CLogFile*);
        using CGuildRankingSetLogger38_clbk = void (WINAPIV*)(struct CGuildRanking*, struct CLogFile*, CGuildRankingSetLogger38_ptr);
        using CGuildRankingUpdateAndSelectGuildGrade40_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateAndSelectGuildGrade40_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateAndSelectGuildGrade40_ptr);
        using CGuildRankingUpdateGuildRankStep142_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateGuildRankStep142_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateGuildRankStep142_ptr);
        using CGuildRankingUpdateGuildRankStep244_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateGuildRankStep244_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateGuildRankStep244_ptr);
        using CGuildRankingUpdateGuildRankStep346_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateGuildRankStep346_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateGuildRankStep346_ptr);
        using CGuildRankingUpdateGuildRankStep448_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateGuildRankStep448_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateGuildRankStep448_ptr);
        using CGuildRankingUpdateRankAndGrade50_ptr = bool (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingUpdateRankAndGrade50_clbk = bool (WINAPIV*)(struct CGuildRanking*, CGuildRankingUpdateRankAndGrade50_ptr);
        using CGuildRankingUpdateRankinGuildStep152_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateRankinGuildStep152_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateRankinGuildStep152_ptr);
        using CGuildRankingUpdateRankinGuildStep254_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateRankinGuildStep254_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateRankinGuildStep254_ptr);
        using CGuildRankingUpdateRankinGuildStep356_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateRankinGuildStep356_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateRankinGuildStep356_ptr);
        using CGuildRankingUpdateRankinGuildStep458_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateRankinGuildStep458_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateRankinGuildStep458_ptr);
        using CGuildRankingUpdateRankinGuildStep560_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateRankinGuildStep560_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateRankinGuildStep560_ptr);
        using CGuildRankingUpdateRankinGuildStep662_ptr = char (WINAPIV*)(struct CGuildRanking*, char*);
        using CGuildRankingUpdateRankinGuildStep662_clbk = char (WINAPIV*)(struct CGuildRanking*, char*, CGuildRankingUpdateRankinGuildStep662_ptr);
        
        using CGuildRankingdtor_CGuildRanking64_ptr = void (WINAPIV*)(struct CGuildRanking*);
        using CGuildRankingdtor_CGuildRanking64_clbk = void (WINAPIV*)(struct CGuildRanking*, CGuildRankingdtor_CGuildRanking64_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

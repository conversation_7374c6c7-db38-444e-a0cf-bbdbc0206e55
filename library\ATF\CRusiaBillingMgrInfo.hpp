// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRusiaBillingMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CRusiaBillingMgrArrangeString2_ptr = void (WINAPIV*)(struct CRusiaBillingMgr*, char*, char*, char);
        using CRusiaBillingMgrArrangeString2_clbk = void (WINAPIV*)(struct CRusiaBillingMgr*, char*, char*, char, CRusiaBillingMgrArrangeString2_ptr);
        
        using CRusiaBillingMgrctor_CRusiaBillingMgr4_ptr = void (WINAPIV*)(struct CRusiaBillingMgr*);
        using CRusiaBillingMgrctor_CRusiaBillingMgr4_clbk = void (WINAPIV*)(struct CRusiaBillingMgr*, CRusiaBillingMgrctor_CRusiaBillingMgr4_ptr);
        using CRusiaBillingMgrCallFunc_Item_Buy6_ptr = int (WINAPIV*)(struct CRusiaBillingMgr*, struct _param_cash_update*, int);
        using CRusiaBillingMgrCallFunc_Item_Buy6_clbk = int (WINAPIV*)(struct CRusiaBillingMgr*, struct _param_cash_update*, int, CRusiaBillingMgrCallFunc_Item_Buy6_ptr);
        using CRusiaBillingMgrCallFunc_Item_Cancel8_ptr = int (WINAPIV*)(struct CRusiaBillingMgr*, struct _param_cash_rollback::__list*, char*);
        using CRusiaBillingMgrCallFunc_Item_Cancel8_clbk = int (WINAPIV*)(struct CRusiaBillingMgr*, struct _param_cash_rollback::__list*, char*, CRusiaBillingMgrCallFunc_Item_Cancel8_ptr);
        using CRusiaBillingMgrCallFunc_RFOnline_Auth10_ptr = int (WINAPIV*)(struct CRusiaBillingMgr*, struct _param_cash_select*);
        using CRusiaBillingMgrCallFunc_RFOnline_Auth10_clbk = int (WINAPIV*)(struct CRusiaBillingMgr*, struct _param_cash_select*, CRusiaBillingMgrCallFunc_RFOnline_Auth10_ptr);
        using CRusiaBillingMgrConfigUserODBC12_ptr = int (WINAPIV*)(struct CRusiaBillingMgr*, char*, char*, char*, uint16_t);
        using CRusiaBillingMgrConfigUserODBC12_clbk = int (WINAPIV*)(struct CRusiaBillingMgr*, char*, char*, char*, uint16_t, CRusiaBillingMgrConfigUserODBC12_ptr);
        using CRusiaBillingMgrDeleteMem14_ptr = void (WINAPIV*)(struct CRusiaBillingMgr*);
        using CRusiaBillingMgrDeleteMem14_clbk = void (WINAPIV*)(struct CRusiaBillingMgr*, CRusiaBillingMgrDeleteMem14_ptr);
        using CRusiaBillingMgrFree16_ptr = int (WINAPIV*)(struct CRusiaBillingMgr*);
        using CRusiaBillingMgrFree16_clbk = int (WINAPIV*)(struct CRusiaBillingMgr*, CRusiaBillingMgrFree16_ptr);
        using CRusiaBillingMgrInit18_ptr = int (WINAPIV*)(struct CRusiaBillingMgr*);
        using CRusiaBillingMgrInit18_clbk = int (WINAPIV*)(struct CRusiaBillingMgr*, CRusiaBillingMgrInit18_ptr);
        using CRusiaBillingMgrInstance20_ptr = struct CRusiaBillingMgr* (WINAPIV*)(struct CRusiaBillingMgr*);
        using CRusiaBillingMgrInstance20_clbk = struct CRusiaBillingMgr* (WINAPIV*)(struct CRusiaBillingMgr*, CRusiaBillingMgrInstance20_ptr);
        using CRusiaBillingMgrLoadINIFile22_ptr = int (WINAPIV*)(struct CRusiaBillingMgr*);
        using CRusiaBillingMgrLoadINIFile22_clbk = int (WINAPIV*)(struct CRusiaBillingMgr*, CRusiaBillingMgrLoadINIFile22_ptr);
        using CRusiaBillingMgrRelease24_ptr = void (WINAPIV*)(struct CRusiaBillingMgr*);
        using CRusiaBillingMgrRelease24_clbk = void (WINAPIV*)(struct CRusiaBillingMgr*, CRusiaBillingMgrRelease24_ptr);
        using CRusiaBillingMgrdhExtractSubString28_ptr = char* (WINAPIV*)(struct CRusiaBillingMgr*, char*, char*, char);
        using CRusiaBillingMgrdhExtractSubString28_clbk = char* (WINAPIV*)(struct CRusiaBillingMgr*, char*, char*, char, CRusiaBillingMgrdhExtractSubString28_ptr);
        using CRusiaBillingMgrdhRExtractSubString30_ptr = void (WINAPIV*)(struct CRusiaBillingMgr*, char*, char*, char);
        using CRusiaBillingMgrdhRExtractSubString30_clbk = void (WINAPIV*)(struct CRusiaBillingMgr*, char*, char*, char, CRusiaBillingMgrdhRExtractSubString30_ptr);
        
        using CRusiaBillingMgrdtor_CRusiaBillingMgr32_ptr = void (WINAPIV*)(struct CRusiaBillingMgr*);
        using CRusiaBillingMgrdtor_CRusiaBillingMgr32_clbk = void (WINAPIV*)(struct CRusiaBillingMgr*, CRusiaBillingMgrdtor_CRusiaBillingMgr32_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

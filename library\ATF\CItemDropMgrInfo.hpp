// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CItemDropMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CItemDropMgrDrop2_ptr = bool (WINAPIV*)(struct CItemDropMgr*, int);
        using CItemDropMgrDrop2_clbk = bool (WINAPIV*)(struct CItemDropMgr*, int, CItemDropMgrDrop2_ptr);
        using CItemDropMgrFrontDrop4_ptr = bool (WINAPIV*)(struct CItemDropMgr*);
        using CItemDropMgrFrontDrop4_clbk = bool (WINAPIV*)(struct CItemDropMgr*, CItemDropMgrFrontDrop4_ptr);
        using CItemDropMgrGetFrontPtr6_ptr = struct _DropItemGroupInfo* (WINAPIV*)(struct CItemDropMgr*);
        using CItemDropMgrGetFrontPtr6_clbk = struct _DropItemGroupInfo* (WINAPIV*)(struct CItemDropMgr*, CItemDropMgrGetFrontPtr6_ptr);
        using CItemDropMgrPopFront8_ptr = bool (WINAPIV*)(struct CItemDropMgr*);
        using CItemDropMgrPopFront8_clbk = bool (WINAPIV*)(struct CItemDropMgr*, CItemDropMgrPopFront8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

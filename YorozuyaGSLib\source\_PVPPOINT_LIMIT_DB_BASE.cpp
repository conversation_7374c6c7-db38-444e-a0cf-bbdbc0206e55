#include <_PVPPOINT_LIMIT_DB_BASE.hpp>


START_ATF_NAMESPACE
    void _PVPPOINT_LIMIT_DB_BASE::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*);
        (org_ptr(0x140077ce0L))(this);
    };
    _PVPPOINT_LIMIT_DB_BASE::_PVPPOINT_LIMIT_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*);
        (org_ptr(0x1400776f0L))(this);
    };
    void _PVPPOINT_LIMIT_DB_BASE::ctor__PVPPOINT_LIMIT_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*);
        (org_ptr(0x1400776f0L))(this);
    };
    _PVPPOINT_LIMIT_DB_BASE::~_PVPPOINT_LIMIT_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*);
        (org_ptr(0x140077750L))(this);
    };
    void _PVPPOINT_LIMIT_DB_BASE::dtor__PVPPOINT_LIMIT_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*);
        (org_ptr(0x140077750L))(this);
    };
END_ATF_NAMESPACE

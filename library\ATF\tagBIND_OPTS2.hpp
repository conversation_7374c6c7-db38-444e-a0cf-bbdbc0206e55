// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_COSERVERINFO.hpp>
#include <tagBIND_OPTS.hpp>


START_ATF_NAMESPACE
    struct  tagBIND_OPTS2 : tagBIND_OPTS
    {
        unsigned int dwTrackFlags;
        unsigned int dwClassContext;
        unsigned int locale;
        _COSERVERINFO *pServerInfo;
    };
END_ATF_NAMESPACE

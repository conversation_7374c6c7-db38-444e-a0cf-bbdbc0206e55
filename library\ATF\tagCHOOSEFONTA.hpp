// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HDC__.hpp>
#include <HINSTANCE__.hpp>
#include <HWND__.hpp>
#include <tagLOGFONTA.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagCHOOSEFONTA
    {
        unsigned int lStructSize;
        HWND__ *hwndOwner;
        HDC__ *hDC;
        tagLOGFONTA *lpLogFont;
        int iPointSize;
        unsigned int Flags;
        unsigned int rgbColors;
        __int64 lCustData;
        unsigned __int64 (WINAPIV *lpfnHook)(HWND__ *, unsigned int, unsigned __int64, __int64);
        const char *lpTemplateName;
        HINSTANCE__ *hInstance;
        char *lpszStyle;
        unsigned __int16 nFontType;
        unsigned __int16 ___MISSING_ALIGNMENT__;
        int nSizeMin;
        int nSizeMax;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

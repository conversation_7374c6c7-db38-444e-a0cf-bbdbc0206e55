// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _refresh_group_target_position_zocl
    {
        char byGroupType;
        char byMapCode;
        char byID;
        unsigned int dwSerial;
        float fPos[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

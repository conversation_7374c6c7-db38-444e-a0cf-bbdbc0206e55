// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _pvp_rank_list_result_data_zocl
    {
        char byRace;
        char byVersion;
        char byPage;
        unsigned __int16 wRankDataLen;
        char szPvpRankData[4000];
    public:
        _pvp_rank_list_result_data_zocl();
        void ctor__pvp_rank_list_result_data_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_pvp_rank_list_result_data_zocl, 4005>(), "_pvp_rank_list_result_data_zocl");
END_ATF_NAMESPACE

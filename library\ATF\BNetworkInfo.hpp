// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <BNetwork.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using BNetworkctor_BNetwork2_ptr = void (WINAPIV*)(struct BNetwork*);
        using BNetworkctor_BNetwork2_clbk = void (WINAPIV*)(struct BNetwork*, BNetworkctor_BNetwork2_ptr);
        using BNetworkFreeDLL4_ptr = void (WINAPIV*)(struct BNetwork*);
        using BNetworkFreeDLL4_clbk = void (WINAPIV*)(struct BNetwork*, BNetworkFreeDLL4_ptr);
        using BNetworkInitNetwork6_ptr = void (WINAPIV*)(struct BNetwork*);
        using BNetworkInitNetwork6_clbk = void (WINAPIV*)(struct BNetwork*, BNetworkInitNetwork6_ptr);
        using BNetworkLoadDll8_ptr = bool (WINAPIV*)(struct BNetwork*, char*);
        using BNetworkLoadDll8_clbk = bool (WINAPIV*)(struct BNetwork*, char*, BNetworkLoadDll8_ptr);
        
        using BNetworkdtor_BNetwork13_ptr = void (WINAPIV*)(struct BNetwork*);
        using BNetworkdtor_BNetwork13_clbk = void (WINAPIV*)(struct BNetwork*, BNetworkdtor_BNetwork13_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

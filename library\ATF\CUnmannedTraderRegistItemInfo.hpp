// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CUnmannedTraderItemState.hpp>
#include <_TRADE_DB_BASE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CUnmannedTraderRegistItemInfo
    {
        unsigned int m_dwRegistSerial;
        unsigned __int16 m_wItemSerial;
        unsigned int m_dwETSerialNumber;
        unsigned int m_dwPrice;
        __int64 m_tStartTime;
        char m_bySellTurm;
        unsigned int m_dwBuyerSerial;
        unsigned int m_dwTax;
        __int64 m_tResultTime;
        char m_wszBuyerName[17];
        char m_szBuyerAccount[13];
        char m_byTableCode;
        unsigned __int16 m_wItemIndex;
        char m_byStorageIndex;
        unsigned __int64 m_dwD;
        unsigned int m_dwU;
        CUnmannedTraderItemState m_kState;
    public:
        CUnmannedTraderRegistItemInfo();
        void ctor_CUnmannedTraderRegistItemInfo();
        void Clear();
        void ClearBuyerInfo();
        void ClearRegist();
        void ClearToWaitState();
        unsigned int GetBuyerSerial();
        uint64_t GetD();
        unsigned int GetETSerial();
        uint16_t GetItemIndex();
        uint16_t GetItemSerial();
        unsigned int GetLeftSec();
        unsigned int GetPrice();
        unsigned int GetRegistSerial();
        int64_t GetResultTime();
        char GetSellTurm();
        int64_t GetStartTime();
        int64_t* GetStartTimePtr();
        CUnmannedTraderItemState::STATE GetState();
        char GetStorageIndex();
        char GetTableCode();
        unsigned int GetTax();
        unsigned int GetU();
        bool IsEmpty();
        bool IsOverRegistTime();
        bool IsRegist();
        bool IsSellUpdateWait();
        bool IsSellWait();
        bool IsWaitNoitfyClose();
        void ReRegistItem(unsigned int dwPrice);
        void RegistItem(unsigned int dwRegistSerial, uint16_t wItemSerial, unsigned int dwETSerialNumber, unsigned int dwPrice, char bySellTurm, char byTableCode, uint16_t wItemIndex, char byStorageIndex, uint64_t dwD, unsigned int dwU, bool bInserted);
        void RepriceItem(unsigned int dwPrice);
        void SellComplete(unsigned int dwPrice, unsigned int dwBuyerSerial, unsigned int dwTax, int64_t tResultTime, char* wszBuyerName, char* szBuyerAccount);
        char SellWaitItem(uint16_t wInx, struct CLogFile* pkLogger, int64_t tResultTime, char* byStorageInx);
        bool Set(uint16_t wInx, char byInvenIndex, unsigned int uiInx, struct _TRADE_DB_BASE* kInfo, struct CLogFile* pkLogger);
        void SetOverRegistTime();
        bool SetState(char byState);
        ~CUnmannedTraderRegistItemInfo();
        void dtor_CUnmannedTraderRegistItemInfo();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CUnmannedTraderRegistItemInfo, 104>(), "CUnmannedTraderRegistItemInfo");
END_ATF_NAMESPACE

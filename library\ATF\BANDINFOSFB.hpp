// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IShellFolder.hpp>
#include <_ITEMIDLIST.hpp>


START_ATF_NAMESPACE
    struct BANDINFOSFB
    {
        unsigned int dwMask;
        unsigned int dwStateMask;
        unsigned int dwState;
        unsigned int crBkgnd;
        unsigned int crBtnLt;
        unsigned int crBtnDk;
        unsigned __int16 wViewMode;
        unsigned __int16 wAlign;
        IShellFolder *psf;
        _ITEMIDLIST *pidl;
    };
END_ATF_NAMESPACE

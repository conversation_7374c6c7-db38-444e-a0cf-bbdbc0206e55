// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_alive_char_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _alive_char_result_zoclctor__alive_char_result_zocl2_ptr = void (WINAPIV*)(struct _alive_char_result_zocl*);
        using _alive_char_result_zoclctor__alive_char_result_zocl2_clbk = void (WINAPIV*)(struct _alive_char_result_zocl*, _alive_char_result_zoclctor__alive_char_result_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _EXIT_ALTER_PARAM
    {
        unsigned int dwHP;
        unsigned int dwFP;
        unsigned int dwSP;
        unsigned int dwDP;
        long double dExp;
        char byMapCode;
        float fStartPos[3];
        unsigned int dwDalant;
        unsigned int dwGold;
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_EXIT_ALTER_PARAM, 48>(), "_EXIT_ALTER_PARAM");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <Worker.hpp>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    struct  CashDbWorker : Worker
    {
        enum LOGTYPE
        {
            log_sys = 0x0,
            log_his = 0x1,
            num_logger = 0x2,
        };
        CLogFile _kLogger[2];
        struct CRFCashItemDatabase *_pkDb;
    public:
        CashDbWorker();
        void ctor_CashDbWorker();
        void CompleteWork();
        int ConvertErrorCode(char state);
        void DoWork();
        bool GetBillingDBConnectionStatus();
        void GetUseCashQueryStr(struct _param_cash_update* rParam, int nIdx, char* wszQuery, uint64_t tBufferSize);
        bool Initialize();
        bool IsNULL();
        bool PushTask(int nTaskCode, char* p, uint64_t size);
        void Release();
        void SendMsgSucceedBuy(uint16_t wSock, struct _param_cash_update* sheet);
        void _all_rollback(struct _param_cash_update* psheet);
        void _complete_tsk_cash_rollback(struct Task* pkTsk);
        void _complete_tsk_cash_select(struct Task* pkTsk);
        void _complete_tsk_cash_total_selling_select(struct Task* pkTsk);
        void _complete_tsk_cash_update(struct Task* pkTsk);
        void _complete_tsk_cashitem_buy_dblog(struct Task* pkTsk);
        void _delete_from_inven(struct CPlayer* pOne, struct _param_cash_update* psheet, int nNum);
        struct CPlayer* _get_player(uint16_t wSock, unsigned int dwAvator);
        bool _init_database();
        bool _init_loggers();
        bool _insert_to_inven(struct CPlayer* pOne, struct _param_cash_update::__item* pGII);
        int _wait_tsk_cash_buy_dblog(struct Task* pkTsk);
        int _wait_tsk_cash_rollback(struct Task* pkTsk);
        int _wait_tsk_cash_select(struct Task* pkTsk);
        int _wait_tsk_cash_update(struct Task* pkTsk);
        int _wait_tst_cash_total_selling_select(struct Task* pkTsk);
        ~CashDbWorker();
        void dtor_CashDbWorker();
    };    
    static_assert(ATF::checkSize<CashDbWorker, 552>(), "CashDbWorker");
END_ATF_NAMESPACE

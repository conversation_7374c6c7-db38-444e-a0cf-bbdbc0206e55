// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _DISPLAY_DEVICEA
    {
        unsigned int cb;
        char DeviceName[32];
        char DeviceString[128];
        unsigned int StateFlags;
        char <PERSON><PERSON><PERSON>[128];
        char <PERSON><PERSON><PERSON><PERSON>[128];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_limitedsale_event_inform_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _limitedsale_event_inform_zoclsize2_ptr = int (WINAPIV*)(struct _limitedsale_event_inform_zocl*);
        using _limitedsale_event_inform_zoclsize2_clbk = int (WINAPIV*)(struct _limitedsale_event_inform_zocl*, _limitedsale_event_inform_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

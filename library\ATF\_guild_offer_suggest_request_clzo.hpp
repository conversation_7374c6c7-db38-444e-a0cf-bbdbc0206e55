// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _guild_offer_suggest_request_clzo
    {
        char byMatterType;
        unsigned int dwMatterDst;
        unsigned int dwMatterObj1;
        unsigned int dwMatterObj2;
        unsigned int dwMatterObj3;
        char byCommentLen;
        char wszComment[65];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

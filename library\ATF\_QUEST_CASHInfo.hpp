// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_QUEST_CASH.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _QUEST_CASHctor__QUEST_CASH2_ptr = void (WINAPIV*)(struct _QUEST_CASH*);
        using _QUEST_CASHctor__QUEST_CASH2_clbk = void (WINAPIV*)(struct _QUEST_CASH*, _QUEST_CASHctor__QUEST_CASH2_ptr);
        using _QUEST_CASHinit4_ptr = void (WINAPIV*)(struct _QUEST_CASH*);
        using _QUEST_CASHinit4_clbk = void (WINAPIV*)(struct _QUEST_CASH*, _QUEST_CASHinit4_ptr);
        using _QUEST_CASHisload6_ptr = bool (WINAPIV*)(struct _QUEST_CASH*);
        using _QUEST_CASHisload6_clbk = bool (WINAPIV*)(struct _QUEST_CASH*, _QUEST_CASHisload6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS_INDIV.hpp>
#include <_param_cash.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _param_cashitem_dblog : _param_cash
    {
        struct __list
        {
            char byRet;
            char byTblCode;
            unsigned __int16 wItemIndex;
            char byOverlapNum;
            unsigned int dwCost;
            int iCashDiscount;
        };
        char in_nCouponCnt;
        _STORAGE_POS_INDIV in_CouponItem[3];
        char byLv;
        int nBuyNum;
        __list data[20];
    public:
        _param_cashitem_dblog(unsigned int dwAv);
        void ctor__param_cashitem_dblog(unsigned int dwAv);
        int size();
        ~_param_cashitem_dblog();
        void dtor__param_cashitem_dblog();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_param_cashitem_dblog, 356>(), "_param_cashitem_dblog");
END_ATF_NAMESPACE

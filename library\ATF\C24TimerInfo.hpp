// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <C24Timer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using C24Timerctor_C24Timer2_ptr = void (WINAPIV*)(struct C24Timer*);
        using C24Timerctor_C24Timer2_clbk = void (WINAPIV*)(struct C24Timer*, C24Timerctor_C24Timer2_ptr);
        using C24TimerGet24TimeFromTickTime4_ptr = unsigned int (WINAPIV*)(struct C24Timer*, unsigned int);
        using C24TimerGet24TimeFromTickTime4_clbk = unsigned int (WINAPIV*)(struct C24Timer*, unsigned int, C24TimerGet24TimeFromTickTime4_ptr);
        using C24TimerInit6_ptr = void (WINAPIV*)(struct C24Timer*);
        using C24TimerInit6_clbk = void (WINAPIV*)(struct C24Timer*, C24TimerInit6_ptr);
        
        using C24Timerdtor_C24Timer8_ptr = void (WINAPIV*)(struct C24Timer*);
        using C24Timerdtor_C24Timer8_clbk = void (WINAPIV*)(struct C24Timer*, C24Timerdtor_C24Timer8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

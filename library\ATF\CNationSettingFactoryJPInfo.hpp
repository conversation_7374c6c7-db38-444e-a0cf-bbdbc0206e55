// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryJP.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryJPctor_CNationSettingFactoryJP2_ptr = void (WINAPIV*)(struct CNationSettingFactoryJP*);
        using CNationSettingFactoryJPctor_CNationSettingFactoryJP2_clbk = void (WINAPIV*)(struct CNationSettingFactoryJP*, CNationSettingFactoryJPctor_CNationSettingFactoryJP2_ptr);
        using CNationSettingFactoryJPCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryJP*, int, char*, bool);
        using CNationSettingFactoryJPCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryJP*, int, char*, bool, CNationSettingFactoryJPCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

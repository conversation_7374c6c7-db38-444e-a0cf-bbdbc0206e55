// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryGroup.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryGroupctor_CNationSettingFactoryGroup2_ptr = void (WINAPIV*)(struct CNationSettingFactoryGroup*);
        using CNationSettingFactoryGroupctor_CNationSettingFactoryGroup2_clbk = void (WINAPIV*)(struct CNationSettingFactoryGroup*, CNationSettingFactoryGroupctor_CNationSettingFactoryGroup2_ptr);
        using CNationSettingFactoryGroupCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryGroup*, int, char*, bool);
        using CNationSettingFactoryGroupCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryGroup*, int, char*, bool, CNationSettingFactoryGroupCreate4_ptr);
        using CNationSettingFactoryGroupInit6_ptr = int (WINAPIV*)(struct CNationSettingFactoryGroup*);
        using CNationSettingFactoryGroupInit6_clbk = int (WINAPIV*)(struct CNationSettingFactoryGroup*, CNationSettingFactoryGroupInit6_ptr);
        
        using CNationSettingFactoryGroupdtor_CNationSettingFactoryGroup8_ptr = void (WINAPIV*)(struct CNationSettingFactoryGroup*);
        using CNationSettingFactoryGroupdtor_CNationSettingFactoryGroup8_clbk = void (WINAPIV*)(struct CNationSettingFactoryGroup*, CNationSettingFactoryGroupdtor_CNationSettingFactoryGroup8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

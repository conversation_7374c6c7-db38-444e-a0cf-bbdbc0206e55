// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Request_Buy_Item.hpp>
#include <Request_Remain_Cash.hpp>
#include <_MSG_HEADER.hpp>
#include <BNetwork.hpp>
#include <CTSingleton.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CEngNetworkBillEX : BNetwork, CTSingleton<CEngNetworkBillEX>
    {
        char m_ip[20];
        unsigned int m_port;
        bool m_bConnect;
    public:
        void ArrangeString(char* szDest, char* szSorc, char cToken)
        {
            using org_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, char);
            (org_ptr(0x14031cbe0L))(this, szDest, szSorc, cToken);
        };
        CEngNetworkBillEX()
        {
            using org_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*);
            (org_ptr(0x14031b5d0L))(this);
        };
        void ctor_CEngNetworkBillEX()
        {
            using org_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*);
            (org_ptr(0x14031b5d0L))(this);
        };
        uint16_t CalcStreamSize(char* pRecvData)
        {
            using org_ptr = uint16_t (WINAPIV*)(struct CEngNetworkBillEX*, char*);
            return (org_ptr(0x14031bba0L))(this, pRecvData);
        };
        int ConnectToNcash()
        {
            using org_ptr = int (WINAPIV*)(struct CEngNetworkBillEX*);
            return (org_ptr(0x14031a420L))(this);
        };
        bool FreeVectorData(unsigned int dwSeq)
        {
            using org_ptr = bool (WINAPIV*)(struct CEngNetworkBillEX*, unsigned int);
            return (org_ptr(0x14031c290L))(this, dwSeq);
        };
        bool Initialize()
        {
            using org_ptr = bool (WINAPIV*)(struct CEngNetworkBillEX*);
            return (org_ptr(0x14031b850L))(this);
        };
        bool IsGetConnected()
        {
            using org_ptr = bool (WINAPIV*)(struct CEngNetworkBillEX*);
            return (org_ptr(0x14022c320L))(this);
        };
        int LoadINIFile()
        {
            using org_ptr = int (WINAPIV*)(struct CEngNetworkBillEX*);
            return (org_ptr(0x14031b740L))(this);
        };
        void ParsingBuyItem(struct Request_Buy_Item* data, char* pRecvData)
        {
            using org_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*, struct Request_Buy_Item*, char*);
            (org_ptr(0x14031c7c0L))(this, data, pRecvData);
        };
        void ParsingRemainCash(struct Request_Remain_Cash* data, char* pRecvData)
        {
            using org_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*, struct Request_Remain_Cash*, char*);
            (org_ptr(0x14031c500L))(this, data, pRecvData);
        };
        unsigned int ParsingSeqNumber(char* pRecvData)
        {
            using org_ptr = unsigned int (WINAPIV*)(struct CEngNetworkBillEX*, char*);
            return (org_ptr(0x14031baa0L))(this, pRecvData);
        };
        bool ReInitialize()
        {
            using org_ptr = bool (WINAPIV*)(struct CEngNetworkBillEX*);
            return (org_ptr(0x14031b9c0L))(this);
        };
        int Send(char* pbyType, char* szMsg, uint16_t nLen)
        {
            using org_ptr = int (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, uint16_t);
            return (org_ptr(0x14031ba40L))(this, pbyType, szMsg, nLen);
        };
        char* dhExtractSubString(char* szSub, char* szFull, char cToken)
        {
            using org_ptr = char* (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, char);
            return (org_ptr(0x14031c9f0L))(this, szSub, szFull, cToken);
        };
        void dhRExtractSubString(char* szSub, char* szFull, char cToken)
        {
            using org_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, char);
            (org_ptr(0x14031cad0L))(this, szSub, szFull, cToken);
        };
        static bool s_DataAnalysis(unsigned int dwProID, unsigned int dwClientIndex, struct _MSG_HEADER* pMsgHeader, char* pMsg)
        {
            using org_ptr = bool (WINAPIV*)(unsigned int, unsigned int, struct _MSG_HEADER*, char*);
            return (org_ptr(0x14031bc20L))(dwProID, dwClientIndex, pMsgHeader, pMsg);
        };
        ~CEngNetworkBillEX()
        {
            using org_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*);
            (org_ptr(0x14031b680L))(this);
        };
        void dtor_CEngNetworkBillEX()
        {
            using org_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*);
            (org_ptr(0x14031b680L))(this);
        };
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRadarItemMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRadarItemMgrctor_CRadarItemMgr2_ptr = void (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrctor_CRadarItemMgr2_clbk = void (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrctor_CRadarItemMgr2_ptr);
        using CRadarItemMgrCalcDelay4_ptr = unsigned int (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrCalcDelay4_clbk = unsigned int (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrCalcDelay4_ptr);
        using CRadarItemMgrGetDelayTime6_ptr = unsigned int (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrGetDelayTime6_clbk = unsigned int (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrGetDelayTime6_ptr);
        using CRadarItemMgrGetStartTime8_ptr = unsigned int (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrGetStartTime8_clbk = unsigned int (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrGetStartTime8_ptr);
        using CRadarItemMgrInit10_ptr = void (WINAPIV*)(struct CRadarItemMgr*, unsigned int);
        using CRadarItemMgrInit10_clbk = void (WINAPIV*)(struct CRadarItemMgr*, unsigned int, CRadarItemMgrInit10_ptr);
        using CRadarItemMgrInit12_ptr = void (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrInit12_clbk = void (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrInit12_ptr);
        using CRadarItemMgrIsRadarUse14_ptr = bool (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrIsRadarUse14_clbk = bool (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrIsRadarUse14_ptr);
        using CRadarItemMgrIsUpdate16_ptr = bool (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrIsUpdate16_clbk = bool (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrIsUpdate16_ptr);
        using CRadarItemMgrIsUse18_ptr = bool (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrIsUse18_clbk = bool (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrIsUse18_ptr);
        using CRadarItemMgrRadarProc20_ptr = bool (WINAPIV*)(struct CRadarItemMgr*, struct _RadarItem_fld*);
        using CRadarItemMgrRadarProc20_clbk = bool (WINAPIV*)(struct CRadarItemMgr*, struct _RadarItem_fld*, CRadarItemMgrRadarProc20_ptr);
        using CRadarItemMgrResetFlags22_ptr = void (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrResetFlags22_clbk = void (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrResetFlags22_ptr);
        using CRadarItemMgrResetUpdate24_ptr = void (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrResetUpdate24_clbk = void (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrResetUpdate24_ptr);
        using CRadarItemMgrSetUseRadar26_ptr = void (WINAPIV*)(struct CRadarItemMgr*, char**, struct CPlayer*, unsigned int, unsigned int);
        using CRadarItemMgrSetUseRadar26_clbk = void (WINAPIV*)(struct CRadarItemMgr*, char**, struct CPlayer*, unsigned int, unsigned int, CRadarItemMgrSetUseRadar26_ptr);
        
        using CRadarItemMgrdtor_CRadarItemMgr28_ptr = void (WINAPIV*)(struct CRadarItemMgr*);
        using CRadarItemMgrdtor_CRadarItemMgr28_clbk = void (WINAPIV*)(struct CRadarItemMgr*, CRadarItemMgrdtor_CRadarItemMgr28_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

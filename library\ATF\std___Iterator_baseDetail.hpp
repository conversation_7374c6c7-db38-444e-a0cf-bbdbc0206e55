// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Iterator_baseInfo.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Detail
        {
            extern ::std::array<hook_record, 4> _Iterator_base_functions;
        }; // end namespace Detail
    }; // end namespace std
END_ATF_NAMESPACE

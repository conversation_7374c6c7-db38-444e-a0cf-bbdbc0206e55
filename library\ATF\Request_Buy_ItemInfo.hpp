// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Request_Buy_Item.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using Request_Buy_Itemctor_Request_Buy_Item2_ptr = void (WINAPIV*)(struct Request_Buy_Item*);
        using Request_Buy_Itemctor_Request_Buy_Item2_clbk = void (WINAPIV*)(struct Request_Buy_Item*, Request_Buy_Itemctor_Request_Buy_Item2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

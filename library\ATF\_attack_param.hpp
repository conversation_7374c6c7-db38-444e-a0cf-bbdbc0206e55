// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _attack_param
    {
        CCharacter *pDst;
        int nPart;
        int nTol;
        int nClass;
        int nMinAF;
        int nMaxAF;
        int nMinSel;
        int nMaxSel;
        int nExtentRange;
        int nShotNum;
        int nAddAttPnt;
        int nWpType;
        char byEffectCode;
        _base_fld *pFld;
        float fArea[3];
        int nLevel;
        int nMastery;
        bool bPassCount;
        int nAttactType;
        bool bMatchless;
        int nMaxAttackPnt;
        bool bBackAttack;
        int nMinAFPlus;
        int nMaxAFPlus;
        int nEffShotNum;
    public:
        _attack_param();
        void ctor__attack_param();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_attack_param, 120>(), "_attack_param");
END_ATF_NAMESPACE

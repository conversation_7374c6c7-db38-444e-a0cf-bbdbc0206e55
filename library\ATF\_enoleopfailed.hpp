// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HRESULT.hpp>
#include <tagNMHDR.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _enoleopfailed
    {
        tagNMHDR nmhdr;
        int iob;
        int lOper;
        HRESULT hr;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

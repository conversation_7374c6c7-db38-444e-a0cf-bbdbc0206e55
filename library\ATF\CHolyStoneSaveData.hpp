// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHolyScheduleData.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CHolyStoneSaveData
    {
        int m_nSceneCode;
        unsigned int m_dwPassTimeInScene;
        int m_nStartStoneHP;
        int m_nHolyMasterRace;
        int m_nDestroyStoneRace;
        char m_byNumOfTime;
        unsigned int m_dwCumPlayerNum;
        unsigned int m_dwCumCount;
        unsigned __int16 m_wStartYear;
        char m_byStartMonth;
        char m_byStartDay;
        char m_byStartHour;
        char m_byStartMin;
        unsigned int m_dwDestroyerSerial;
        int m_eDestroyerState;
        int m_nStoneHP_Buffer[3];
        unsigned int m_dwTerm[2];
        unsigned int m_dwOreRemainAmount;
        unsigned int m_dwOreTotalAmount;
        unsigned int m_dwDestroyerGuildSerial;
        char m_byOreTransferCount;
        unsigned int m_dwOreTransferAmount;
    public:
        void DefaultInit(struct CHolyScheduleData::__HolyScheduleNode* ScheduleNode);
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CHolyStoneSaveData, 88>(), "CHolyStoneSaveData");
END_ATF_NAMESPACE

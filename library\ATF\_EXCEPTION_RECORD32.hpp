// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _EXCEPTION_RECORD32
    {
        unsigned int ExceptionCode;
        unsigned int ExceptionFlags;
        unsigned int ExceptionRecord;
        unsigned int ExceptionAddress;
        unsigned int NumberParameters;
        unsigned int ExceptionInformation[15];
    };
END_ATF_NAMESPACE

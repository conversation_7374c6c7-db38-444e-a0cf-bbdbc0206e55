// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    struct tagOFN_NT4A
    {
        unsigned int lStructSize;
        HWND__ *hwndOwner;
        HINSTANCE__ *hInstance;
        const char *lpstrFilter;
        char *lpstrCustomFilter;
        unsigned int nMaxCustFilter;
        unsigned int nFilterIndex;
        char *lpstrFile;
        unsigned int nMaxFile;
        char *lpstrFileTitle;
        unsigned int nMaxFileTitle;
        const char *lpstrInitialDir;
        const char *lpstrTitle;
        unsigned int Flags;
        unsigned __int16 nFileOffset;
        unsigned __int16 nFileExtension;
        const char *lpstrDefExt;
        __int64 lCustData;
        unsigned __int64 (WINAPIV *lpfnHook)(H<PERSON><PERSON>__ *, unsigned int, unsigned __int64, __int64);
        const char *lpTemplateName;
    };
END_ATF_NAMESPACE

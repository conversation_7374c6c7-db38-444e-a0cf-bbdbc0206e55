// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CSimpleList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CTypedSimpleList<CRuntimeClass *> : CSimpleList
    {
    };
END_ATF_NAMESPACE
#include <CSimpleList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CTypedSimpleList<COleObjectFactory *> : CSimpleList
    {
    };
END_ATF_NAMESPACE
#include <CSimpleList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CTypedSimpleList<CDynLinkLibrary *> : CSimpleList
    {
    };
END_ATF_NAMESPACE
#include <CSimpleList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CTypedSimpleList<COleControlLock *> : CSimpleList
    {
    };
END_ATF_NAMESPACE
#include <CSimpleList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CTypedSimpleList<CThreadData *> : CSimpleList
    {
    };
END_ATF_NAMESPACE
#include <CSimpleList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CTypedSimpleList<CFrameWnd *> : CSimpleList
    {
    };
END_ATF_NAMESPACE

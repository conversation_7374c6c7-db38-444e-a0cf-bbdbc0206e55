// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <RFEventBaseVtbl.hpp>
#include <event_date_range.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct RFEventBase
    {
        RFEventBaseVtbl *vfptr;
        unsigned int _nOldLoopTime;
        event_date_range _kDateRange;
    public:
        int DoEvent(struct CPlayer* pOne);
        char* GetPlayerState(unsigned int nIdx, unsigned int nAvator);
        bool Initialzie();
        bool IsDbUpdate(unsigned int nIdx);
        bool IsEnable();
        void Loop();
        RFEventBase();
        void ctor_RFEventBase();
        bool SetEvent(char* p, int size, bool bInit);
        bool SetPlayerState(void* p, int size);
        ~RFEventBase();
        void dtor_RFEventBase();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayerDB.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPlayerDBAddTrunkDalant2_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBAddTrunkDalant2_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBAddTrunkDalant2_ptr);
        using CPlayerDBAddTrunkGold4_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBAddTrunkGold4_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBAddTrunkGold4_ptr);
        using CPlayerDBAppointSerialStorageItem6_ptr = void (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBAppointSerialStorageItem6_clbk = void (WINAPIV*)(struct CPlayerDB*, CPlayerDBAppointSerialStorageItem6_ptr);
        using CPlayerDBBeHaveBoxOfAMP8_ptr = bool (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBBeHaveBoxOfAMP8_clbk = bool (WINAPIV*)(struct CPlayerDB*, CPlayerDBBeHaveBoxOfAMP8_ptr);
        
        using CPlayerDBctor_CPlayerDB10_ptr = void (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBctor_CPlayerDB10_clbk = void (WINAPIV*)(struct CPlayerDB*, CPlayerDBctor_CPlayerDB10_ptr);
        using CPlayerDBCalcCharGrade12_ptr = char (WINAPIV*)(char, uint16_t);
        using CPlayerDBCalcCharGrade12_clbk = char (WINAPIV*)(char, uint16_t, CPlayerDBCalcCharGrade12_ptr);
        using CPlayerDBConvertAvatorDB14_ptr = bool (WINAPIV*)(struct CPlayerDB*, struct _AVATOR_DATA*);
        using CPlayerDBConvertAvatorDB14_clbk = bool (WINAPIV*)(struct CPlayerDB*, struct _AVATOR_DATA*, CPlayerDBConvertAvatorDB14_ptr);
        using CPlayerDBConvertGeneralDB16_ptr = bool (WINAPIV*)(struct CPlayerDB*, struct _AVATOR_DATA*, struct _AVATOR_DATA*);
        using CPlayerDBConvertGeneralDB16_clbk = bool (WINAPIV*)(struct CPlayerDB*, struct _AVATOR_DATA*, struct _AVATOR_DATA*, CPlayerDBConvertGeneralDB16_ptr);
        using CPlayerDBDeleteItemCountFromCode18_ptr = bool (WINAPIV*)(struct CPlayerDB*, char*, int);
        using CPlayerDBDeleteItemCountFromCode18_clbk = bool (WINAPIV*)(struct CPlayerDB*, char*, int, CPlayerDBDeleteItemCountFromCode18_ptr);
        using CPlayerDBGetBagNum20_ptr = char (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetBagNum20_clbk = char (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetBagNum20_ptr);
        using CPlayerDBGetCharNameA22_ptr = char* (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetCharNameA22_clbk = char* (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetCharNameA22_ptr);
        using CPlayerDBGetCharNameW24_ptr = char* (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetCharNameW24_clbk = char* (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetCharNameW24_ptr);
        using CPlayerDBGetCharSerial26_ptr = unsigned int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetCharSerial26_clbk = unsigned int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetCharSerial26_ptr);
        using CPlayerDBGetClassInGuild28_ptr = char (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetClassInGuild28_clbk = char (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetClassInGuild28_ptr);
        using CPlayerDBGetCurItemSerial30_ptr = uint16_t (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetCurItemSerial30_clbk = uint16_t (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetCurItemSerial30_ptr);
        using CPlayerDBGetCurPos32_ptr = float* (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetCurPos32_clbk = float* (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetCurPos32_ptr);
        using CPlayerDBGetDP34_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetDP34_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetDP34_ptr);
        using CPlayerDBGetDalant36_ptr = unsigned int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetDalant36_clbk = unsigned int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetDalant36_ptr);
        using CPlayerDBGetExp38_ptr = long double (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetExp38_clbk = long double (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetExp38_ptr);
        using CPlayerDBGetExtTrunkSlotNum40_ptr = char (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetExtTrunkSlotNum40_clbk = char (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetExtTrunkSlotNum40_ptr);
        using CPlayerDBGetExtTrunkSlotRace42_ptr = char (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBGetExtTrunkSlotRace42_clbk = char (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBGetExtTrunkSlotRace42_ptr);
        using CPlayerDBGetFP44_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetFP44_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetFP44_ptr);
        using CPlayerDBGetGold46_ptr = unsigned int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetGold46_clbk = unsigned int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetGold46_ptr);
        using CPlayerDBGetGuildSerial48_ptr = unsigned int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetGuildSerial48_clbk = unsigned int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetGuildSerial48_ptr);
        using CPlayerDBGetHP50_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetHP50_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetHP50_ptr);
        using CPlayerDBGetHaveUnitNum52_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetHaveUnitNum52_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetHaveUnitNum52_ptr);
        using CPlayerDBGetInvenItemCountFromCode54_ptr = int (WINAPIV*)(struct CPlayerDB*, char*);
        using CPlayerDBGetInvenItemCountFromCode54_clbk = int (WINAPIV*)(struct CPlayerDB*, char*, CPlayerDBGetInvenItemCountFromCode54_ptr);
        using CPlayerDBGetItem56_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayerDB*, char);
        using CPlayerDBGetItem56_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayerDB*, char, CPlayerDBGetItem56_ptr);
        using CPlayerDBGetLevel58_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetLevel58_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetLevel58_ptr);
        using CPlayerDBGetLossExp60_ptr = long double (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetLossExp60_clbk = long double (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetLossExp60_ptr);
        using CPlayerDBGetMapCode62_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetMapCode62_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetMapCode62_ptr);
        using CPlayerDBGetMaxLevel64_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetMaxLevel64_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetMaxLevel64_ptr);
        using CPlayerDBGetNewItemSerial66_ptr = uint16_t (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetNewItemSerial66_clbk = uint16_t (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetNewItemSerial66_ptr);
        using CPlayerDBGetPtrBaseClass68_ptr = struct _class_fld* (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetPtrBaseClass68_clbk = struct _class_fld* (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetPtrBaseClass68_ptr);
        using CPlayerDBGetPtrCurClass70_ptr = struct _class_fld* (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetPtrCurClass70_clbk = struct _class_fld* (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetPtrCurClass70_ptr);
        using CPlayerDBGetPtrItemStorage72_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayerDB*, uint16_t, char*);
        using CPlayerDBGetPtrItemStorage72_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayerDB*, uint16_t, char*, CPlayerDBGetPtrItemStorage72_ptr);
        using CPlayerDBGetPvPCashBag74_ptr = long double (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetPvPCashBag74_clbk = long double (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetPvPCashBag74_ptr);
        using CPlayerDBGetPvPPoint76_ptr = long double (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetPvPPoint76_clbk = long double (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetPvPPoint76_ptr);
        using CPlayerDBGetPvpRank78_ptr = unsigned int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetPvpRank78_clbk = unsigned int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetPvpRank78_ptr);
        using CPlayerDBGetRaceCode80_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetRaceCode80_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetRaceCode80_ptr);
        using CPlayerDBGetRaceSexCode82_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetRaceSexCode82_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetRaceSexCode82_ptr);
        using CPlayerDBGetResBufferNum84_ptr = char (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetResBufferNum84_clbk = char (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetResBufferNum84_ptr);
        using CPlayerDBGetSP86_ptr = int (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetSP86_clbk = int (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetSP86_ptr);
        using CPlayerDBGetTrunkPasswdW88_ptr = char* (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetTrunkPasswdW88_clbk = char* (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetTrunkPasswdW88_ptr);
        using CPlayerDBGetTrunkSlotNum90_ptr = char (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetTrunkSlotNum90_clbk = char (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetTrunkSlotNum90_ptr);
        using CPlayerDBGetTrunkSlotRace92_ptr = char (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBGetTrunkSlotRace92_clbk = char (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBGetTrunkSlotRace92_ptr);
        using CPlayerDBGetUseSlot94_ptr = char (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBGetUseSlot94_clbk = char (WINAPIV*)(struct CPlayerDB*, CPlayerDBGetUseSlot94_ptr);
        using CPlayerDBInitAlterMastery96_ptr = void (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBInitAlterMastery96_clbk = void (WINAPIV*)(struct CPlayerDB*, CPlayerDBInitAlterMastery96_ptr);
        using CPlayerDBInitClass98_ptr = void (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBInitClass98_clbk = void (WINAPIV*)(struct CPlayerDB*, CPlayerDBInitClass98_ptr);
        using CPlayerDBInitPlayerDB100_ptr = void (WINAPIV*)(struct CPlayerDB*, struct CPlayer*);
        using CPlayerDBInitPlayerDB100_clbk = void (WINAPIV*)(struct CPlayerDB*, struct CPlayer*, CPlayerDBInitPlayerDB100_ptr);
        using CPlayerDBInitResBuffer102_ptr = void (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBInitResBuffer102_clbk = void (WINAPIV*)(struct CPlayerDB*, CPlayerDBInitResBuffer102_ptr);
        using CPlayerDBIsActableClassSkill104_ptr = bool (WINAPIV*)(struct CPlayerDB*, char*, int*);
        using CPlayerDBIsActableClassSkill104_clbk = bool (WINAPIV*)(struct CPlayerDB*, char*, int*, CPlayerDBIsActableClassSkill104_ptr);
        using CPlayerDBIsClassChangeableLv106_ptr = bool (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBIsClassChangeableLv106_clbk = bool (WINAPIV*)(struct CPlayerDB*, CPlayerDBIsClassChangeableLv106_ptr);
        using CPlayerDBPopLink108_ptr = void (WINAPIV*)(struct CPlayerDB*, int);
        using CPlayerDBPopLink108_clbk = void (WINAPIV*)(struct CPlayerDB*, int, CPlayerDBPopLink108_ptr);
        using CPlayerDBPushLink110_ptr = bool (WINAPIV*)(struct CPlayerDB*, int, uint16_t, bool);
        using CPlayerDBPushLink110_clbk = bool (WINAPIV*)(struct CPlayerDB*, int, uint16_t, bool, CPlayerDBPushLink110_ptr);
        using CPlayerDBSelectClass112_ptr = void (WINAPIV*)(struct CPlayerDB*, char, struct _class_fld*);
        using CPlayerDBSelectClass112_clbk = void (WINAPIV*)(struct CPlayerDB*, char, struct _class_fld*, CPlayerDBSelectClass112_ptr);
        using CPlayerDBSetBagNum114_ptr = void (WINAPIV*)(struct CPlayerDB*, char);
        using CPlayerDBSetBagNum114_clbk = void (WINAPIV*)(struct CPlayerDB*, char, CPlayerDBSetBagNum114_ptr);
        using CPlayerDBSetClassInGuild116_ptr = void (WINAPIV*)(struct CPlayerDB*, char);
        using CPlayerDBSetClassInGuild116_clbk = void (WINAPIV*)(struct CPlayerDB*, char, CPlayerDBSetClassInGuild116_ptr);
        using CPlayerDBSetCurPos118_ptr = void (WINAPIV*)(struct CPlayerDB*, float*);
        using CPlayerDBSetCurPos118_clbk = void (WINAPIV*)(struct CPlayerDB*, float*, CPlayerDBSetCurPos118_ptr);
        using CPlayerDBSetDP120_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBSetDP120_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBSetDP120_ptr);
        using CPlayerDBSetDalant122_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBSetDalant122_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBSetDalant122_ptr);
        using CPlayerDBSetExp124_ptr = void (WINAPIV*)(struct CPlayerDB*, long double);
        using CPlayerDBSetExp124_clbk = void (WINAPIV*)(struct CPlayerDB*, long double, CPlayerDBSetExp124_ptr);
        using CPlayerDBSetFP126_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBSetFP126_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBSetFP126_ptr);
        using CPlayerDBSetGold128_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBSetGold128_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBSetGold128_ptr);
        using CPlayerDBSetHP130_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBSetHP130_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBSetHP130_ptr);
        using CPlayerDBSetHaveBoxOfAMP132_ptr = void (WINAPIV*)(struct CPlayerDB*, bool);
        using CPlayerDBSetHaveBoxOfAMP132_clbk = void (WINAPIV*)(struct CPlayerDB*, bool, CPlayerDBSetHaveBoxOfAMP132_ptr);
        using CPlayerDBSetLevel134_ptr = void (WINAPIV*)(struct CPlayerDB*, int);
        using CPlayerDBSetLevel134_clbk = void (WINAPIV*)(struct CPlayerDB*, int, CPlayerDBSetLevel134_ptr);
        using CPlayerDBSetLossExp136_ptr = void (WINAPIV*)(struct CPlayerDB*, long double);
        using CPlayerDBSetLossExp136_clbk = void (WINAPIV*)(struct CPlayerDB*, long double, CPlayerDBSetLossExp136_ptr);
        using CPlayerDBSetMapCode138_ptr = void (WINAPIV*)(struct CPlayerDB*, char);
        using CPlayerDBSetMapCode138_clbk = void (WINAPIV*)(struct CPlayerDB*, char, CPlayerDBSetMapCode138_ptr);
        using CPlayerDBSetMaxLevel140_ptr = void (WINAPIV*)(struct CPlayerDB*, int);
        using CPlayerDBSetMaxLevel140_clbk = void (WINAPIV*)(struct CPlayerDB*, int, CPlayerDBSetMaxLevel140_ptr);
        using CPlayerDBSetPvPPoint142_ptr = void (WINAPIV*)(struct CPlayerDB*, long double);
        using CPlayerDBSetPvPPoint142_clbk = void (WINAPIV*)(struct CPlayerDB*, long double, CPlayerDBSetPvPPoint142_ptr);
        using CPlayerDBSetSP144_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBSetSP144_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBSetSP144_ptr);
        using CPlayerDBSubTrunkDalant146_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBSubTrunkDalant146_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBSubTrunkDalant146_ptr);
        using CPlayerDBSubTrunkGold148_ptr = void (WINAPIV*)(struct CPlayerDB*, unsigned int);
        using CPlayerDBSubTrunkGold148_clbk = void (WINAPIV*)(struct CPlayerDB*, unsigned int, CPlayerDBSubTrunkGold148_ptr);
        
        using CPlayerDBdtor_CPlayerDB150_ptr = void (WINAPIV*)(struct CPlayerDB*);
        using CPlayerDBdtor_CPlayerDB150_clbk = void (WINAPIV*)(struct CPlayerDB*, CPlayerDBdtor_CPlayerDB150_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

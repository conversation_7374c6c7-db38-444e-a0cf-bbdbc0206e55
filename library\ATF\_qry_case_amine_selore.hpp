// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _qry_case_amine_selore
    {
        char bySubQryCase;
        char byCollisionType;
        char byRace;
         unsigned int dwGuildSerial;
        char byOreIdx;
    public:
        _qry_case_amine_selore();
        void ctor__qry_case_amine_selore();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_amine_selore, 8>(), "_qry_case_amine_selore");
END_ATF_NAMESPACE

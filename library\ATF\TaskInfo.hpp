// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Task.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using TaskGetRetCode2_ptr = int (WINAPIV*)(struct Task*);
        using TaskGetRetCode2_clbk = int (WINAPIV*)(struct Task*, TaskGetRetCode2_ptr);
        using TaskGetTaskBuf4_ptr = char* (WINAPIV*)(struct Task*);
        using TaskGetTaskBuf4_clbk = char* (WINAPIV*)(struct Task*, TaskGetTaskBuf4_ptr);
        using TaskGetTaskCode6_ptr = int (WINAPIV*)(struct Task*);
        using TaskGetTaskCode6_clbk = int (WINAPIV*)(struct Task*, TaskGetTaskCode6_ptr);
        using TaskInitialize8_ptr = bool (WINAPIV*)(struct Task*, uint64_t);
        using TaskInitialize8_clbk = bool (WINAPIV*)(struct Task*, uint64_t, TaskInitialize8_ptr);
        using TaskSetRetCode10_ptr = void (WINAPIV*)(struct Task*, int);
        using TaskSetRetCode10_clbk = void (WINAPIV*)(struct Task*, int, TaskSetRetCode10_ptr);
        using TaskSetTask12_ptr = void (WINAPIV*)(struct Task*, int, char*, uint64_t);
        using TaskSetTask12_clbk = void (WINAPIV*)(struct Task*, int, char*, uint64_t, TaskSetTask12_ptr);
        
        using Taskctor_Task14_ptr = void (WINAPIV*)(struct Task*);
        using Taskctor_Task14_clbk = void (WINAPIV*)(struct Task*, Taskctor_Task14_ptr);
        
        using Taskdtor_Task18_ptr = void (WINAPIV*)(struct Task*);
        using Taskdtor_Task18_clbk = void (WINAPIV*)(struct Task*, Taskdtor_Task18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

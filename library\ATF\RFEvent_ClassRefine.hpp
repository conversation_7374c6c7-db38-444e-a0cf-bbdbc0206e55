// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <CPlayer.hpp>
#include <RFEventBase.hpp>
#include <_FILETIME.hpp>
#include <_event_config_classrefine.hpp>
#include <_event_participant_classrefine.hpp>


START_ATF_NAMESPACE
    struct  RFEvent_ClassRefine : RFEventBase
    {
        _event_config_classrefine _kEvent;
        _event_participant_classrefine *_pkParticipant;
        bool m_bUserDataReset;
        bool m_bDateReset;
        _event_config_classrefine _kModifyEvent;
        CMyTimer m_tmDataFileCheckTime;
        _FILETIME m_ftWrite;
    public:
        int CanDoEvent(struct CPlayer* pOne);
        bool CheckRefineEventData();
        int DoEvent(struct CPlayer* pOne);
        char* GetPlayerState(unsigned int nIdx, unsigned int nAvator);
        bool Initialzie();
        bool IsDbUpdate(unsigned int nIdx);
        bool IsEnable();
        void Loop();
        RFEvent_ClassRefine();
        void ctor_RFEvent_ClassRefine();
        void ReadClassRefineEventInfo();
        void ResetRefineData();
        bool SetEvent(char* p, int size, bool bInit);
        bool SetPlayerState(void* p, int size);
        ~RFEvent_ClassRefine();
        void dtor_RFEvent_ClassRefine();
    };
END_ATF_NAMESPACE

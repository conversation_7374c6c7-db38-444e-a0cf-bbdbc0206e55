// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_store_dummy.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _store_dummySetDummy2_ptr = bool (WINAPIV*)(struct _store_dummy*, int, struct _base_fld*, struct _dummy_position*);
        using _store_dummySetDummy2_clbk = bool (WINAPIV*)(struct _store_dummy*, int, struct _base_fld*, struct _dummy_position*, _store_dummySetDummy2_ptr);
        
        using _store_dummyctor__store_dummy4_ptr = void (WINAPIV*)(struct _store_dummy*);
        using _store_dummyctor__store_dummy4_clbk = void (WINAPIV*)(struct _store_dummy*, _store_dummyctor__store_dummy4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

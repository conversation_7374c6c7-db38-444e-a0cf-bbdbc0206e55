// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _player_fixpositon_zocl
    {
        unsigned __int16 wIndex;
        unsigned int dwSerial;
        unsigned __int16 wEquipVer;
        char byRaceCode;
        __int16 zCur[3];
        unsigned __int16 wLastEffectCode;
        unsigned __int64 dwStateFlag;
        char byColor;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

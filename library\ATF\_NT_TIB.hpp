// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$7A727655067EA29DD1B3C3F7D79CBFD1.hpp>


START_ATF_NAMESPACE
    struct _NT_TIB
    {
        struct _EXCEPTION_REGISTRATION_RECORD *ExceptionList;
        void *StackBase;
        void *StackLimit;
        void *SubSystemTib;
        $7A727655067EA29DD1B3C3F7D79CBFD1 ___u4;
        void *ArbitraryUserPointer;
        _NT_TIB *Self;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetIndexList.hpp>
#include <_started_vote_inform_zocl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CVoteSystem
    {
        bool m_bActive;
        char m_byRaceCode;
        int m_nSerial;
        bool m_bHurry;
        unsigned int m_dwPoint[3];
        CNetIndexList m_listVote;
        unsigned int m_dwStartVoteTime;
        unsigned int m_dwLastBroadcastTime;
        char m_byLimGrade;
        _started_vote_inform_zocl m_SendStarted;
        bool m_bPunishment;
        char m_byPunishType;
        unsigned int m_dwAvatorSerial;
        char m_wszCharName[17];
    public:
        bool ActVote(unsigned int dwAvatorSerial, char byPoint);
        CVoteSystem();
        void ctor_CVoteSystem();
        void CompleteSelectCharSerial(char* pData);
        void Loop();
        void ProcessPunishment();
        void SendMsg_StartedVoteInform(int n, unsigned int dwAvatorSerial, bool bPunish);
        bool StartVote(char* pwszContent, char byLimGrade, char byRaceCode);
        bool StartVote(char byRaceCode, char byPunishType, char* pwszContent, char* pwszName, unsigned int dwSerial);
        bool StopVote();
        ~CVoteSystem();
        void dtor_CVoteSystem();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

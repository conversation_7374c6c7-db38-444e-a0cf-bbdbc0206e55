// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum tagDocHostUIFlagDispatch
    {
      docHostUIFlagDIALOG = 0x1,
      docHost<PERSON>FlagDISABLE_HELP_MENU = 0x2,
      docHostUIFlagNO3DBORDER = 0x4,
      docHostUIFlagSCROLL_NO = 0x8,
      docHostUIFlagDISABLE_SCRIPT_INACTIVE = 0x10,
      docHostUIFlagOPENNEWWIN = 0x20,
      docHostUIFlagDISABLE_OFFSCREEN = 0x40,
      docHostUIFlagFLAT_SCROLLBAR = 0x80,
      docHost<PERSON>FlagDIV_BLOCKDEFAULT = 0x100,
      docHostUIFlagACTIVATE_CLIENTHIT_ONLY = 0x200,
    };
END_ATF_NAMESPACE

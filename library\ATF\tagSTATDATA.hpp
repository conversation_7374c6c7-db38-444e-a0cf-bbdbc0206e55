// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IAdviseSink.hpp>
#include <tagFORMATETC.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagSTATDATA
    {
        tagFORMATETC formatetc;
        unsigned int advf;
        IAdviseSink *pAdvSink;
        unsigned int dwConnection;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

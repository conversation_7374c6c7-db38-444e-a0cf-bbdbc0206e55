// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <_IMAGE_THUNK_DATA64.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct InternalImgDelayDescr
    {
        unsigned int grAttrs;
        const char *szName;
        HINSTANCE__ **phmod;
        _IMAGE_THUNK_DATA64 *pIAT;
        _IMAGE_THUNK_DATA64 *pINT;
        _IMAGE_THUNK_DATA64 *pBoundIAT;
        _IMAGE_THUNK_DATA64 *pUnloadIAT;
        unsigned int dwTimeStamp;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$78480DAC57CA142718342B1071C1470C.hpp>
#include <_XMM_SAVE_AREA32.hpp>


START_ATF_NAMESPACE
    union $684CBA0077E04B5E942B3E8824B58094
    {
        _XMM_SAVE_AREA32 FltSave;
        $78480DAC57CA142718342B1071C1470C __s1;
    };    
    static_assert(ATF::checkSize<$684CBA0077E04B5E942B3E8824B58094, 512>(), "$684CBA0077E04B5E942B3E8824B58094");
END_ATF_NAMESPACE

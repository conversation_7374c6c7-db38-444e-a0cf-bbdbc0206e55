// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_rege_char_data.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _rege_char_datactor__rege_char_data2_ptr = void (WINAPIV*)(struct _rege_char_data*);
        using _rege_char_datactor__rege_char_data2_clbk = void (WINAPIV*)(struct _rege_char_data*, _rege_char_datactor__rege_char_data2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

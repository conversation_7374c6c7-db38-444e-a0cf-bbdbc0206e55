// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct Us_FSM_Node
    {
        Us_FSM_Node *m_pParent;
        unsigned int m_dwState;
        int m_bLive;
        unsigned int m_dwLastTime;
        unsigned int m_dwLoopTime;
    public:
        unsigned int GetState();
        void Init();
        void SetLoopTime(unsigned int dwLoopTime);
        void SetParent(struct Us_FSM_Node* pParent);
        void SetState(unsigned int dwState);
        Us_FSM_Node();
        void ctor_Us_FSM_Node();
        ~Us_FSM_Node();
        void dtor_Us_FSM_Node();
    };
END_ATF_NAMESPACE

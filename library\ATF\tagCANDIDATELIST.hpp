// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagCANDIDATELIST
    {
        unsigned int dwSize;
        unsigned int dwStyle;
        unsigned int dwCount;
        unsigned int dwSelection;
        unsigned int dwPageStart;
        unsigned int dwPageSize;
        unsigned int dwOffset[1];
    };
END_ATF_NAMESPACE

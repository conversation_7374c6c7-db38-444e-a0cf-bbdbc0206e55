// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationCodeStrTable.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationCodeStrTablector_CNationCodeStrTable2_ptr = void (WINAPIV*)(struct CNationCodeStrTable*);
        using CNationCodeStrTablector_CNationCodeStrTable2_clbk = void (WINAPIV*)(struct CNationCodeStrTable*, CNationCodeStrTablector_CNationCodeStrTable2_ptr);
        using CNationCodeStrTableGetCode4_ptr = int (WINAPIV*)(struct CNationCodeStrTable*, char*);
        using CNationCodeStrTableGetCode4_clbk = int (WINAPIV*)(struct CNationCodeStrTable*, char*, CNationCodeStrTableGetCode4_ptr);
        using CNationCodeStrTableGetStr6_ptr = char* (WINAPIV*)(struct CNationCodeStrTable*, int);
        using CNationCodeStrTableGetStr6_clbk = char* (WINAPIV*)(struct CNationCodeStrTable*, int, CNationCodeStrTableGetStr6_ptr);
        using CNationCodeStrTableInit8_ptr = bool (WINAPIV*)(struct CNationCodeStrTable*);
        using CNationCodeStrTableInit8_clbk = bool (WINAPIV*)(struct CNationCodeStrTable*, CNationCodeStrTableInit8_ptr);
        using CNationCodeStrTableRegistCode10_ptr = int (WINAPIV*)(struct CNationCodeStrTable*);
        using CNationCodeStrTableRegistCode10_clbk = int (WINAPIV*)(struct CNationCodeStrTable*, CNationCodeStrTableRegistCode10_ptr);
        
        using CNationCodeStrTabledtor_CNationCodeStrTable12_ptr = void (WINAPIV*)(struct CNationCodeStrTable*);
        using CNationCodeStrTabledtor_CNationCodeStrTable12_clbk = void (WINAPIV*)(struct CNationCodeStrTable*, CNationCodeStrTabledtor_CNationCodeStrTable12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

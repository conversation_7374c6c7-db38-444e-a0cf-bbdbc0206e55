// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace std
    {
        struct  _Iosb
        {
            enum _Dummy_enum
            {
                _Dummy_enum_val = 0x1,
            };
            enum _Fmtflags
            {
                _Fmtmask = 0xFFFF,
                _Fmtzero = 0x0,
            };
            enum _Iostate
            {
                _Statmask = 0x17,
            };
            enum _Openmode
            {
                _Openmask = 0xFF,
            };
            enum _Seekdir
            {
                _Seekmask = 0x3,
            };
        };
    }; // end namespace std
END_ATF_NAMESPACE

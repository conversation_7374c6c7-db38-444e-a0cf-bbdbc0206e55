// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBuffHolyQuestResultInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRaceBuffHolyQuestResultInfoctor_CRaceBuffHolyQuestResultInfo2_ptr = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfoctor_CRaceBuffHolyQuestResultInfo2_clbk = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfoctor_CRaceBuffHolyQuestResultInfo2_ptr);
        using CRaceBuffHolyQuestResultInfoClearResult4_ptr = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfoClearResult4_clbk = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfoClearResult4_ptr);
        using CRaceBuffHolyQuestResultInfoFindFailRace6_ptr = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char, char, char*);
        using CRaceBuffHolyQuestResultInfoFindFailRace6_clbk = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char, char, char*, CRaceBuffHolyQuestResultInfoFindFailRace6_ptr);
        using CRaceBuffHolyQuestResultInfoGetContinueCnt8_ptr = unsigned int (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, int);
        using CRaceBuffHolyQuestResultInfoGetContinueCnt8_clbk = unsigned int (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, int, CRaceBuffHolyQuestResultInfoGetContinueCnt8_ptr);
        using CRaceBuffHolyQuestResultInfoGetResultType10_ptr = int (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char, bool);
        using CRaceBuffHolyQuestResultInfoGetResultType10_clbk = int (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char, bool, CRaceBuffHolyQuestResultInfoGetResultType10_ptr);
        using CRaceBuffHolyQuestResultInfoIsChaos12_ptr = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfoIsChaos12_clbk = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfoIsChaos12_ptr);
        using CRaceBuffHolyQuestResultInfoIsValidResult14_ptr = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfoIsValidResult14_clbk = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfoIsValidResult14_ptr);
        using CRaceBuffHolyQuestResultInfoLoad16_ptr = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfoLoad16_clbk = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfoLoad16_ptr);
        using CRaceBuffHolyQuestResultInfoLoadINI18_ptr = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfoLoadINI18_clbk = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfoLoadINI18_ptr);
        using CRaceBuffHolyQuestResultInfoSave20_ptr = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfoSave20_clbk = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfoSave20_ptr);
        using CRaceBuffHolyQuestResultInfoSaveINI22_ptr = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfoSaveINI22_clbk = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfoSaveINI22_ptr);
        using CRaceBuffHolyQuestResultInfoSaveINISubProcSaveNum24_ptr = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char*, char);
        using CRaceBuffHolyQuestResultInfoSaveINISubProcSaveNum24_clbk = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char*, char, CRaceBuffHolyQuestResultInfoSaveINISubProcSaveNum24_ptr);
        using CRaceBuffHolyQuestResultInfoSetBuffFlag26_ptr = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfoSetBuffFlag26_clbk = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfoSetBuffFlag26_ptr);
        using CRaceBuffHolyQuestResultInfoSetResult28_ptr = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char, char, unsigned int);
        using CRaceBuffHolyQuestResultInfoSetResult28_clbk = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char, char, unsigned int, CRaceBuffHolyQuestResultInfoSetResult28_ptr);
        using CRaceBuffHolyQuestResultInfoSetResultSubProcSetRace30_ptr = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char, char, unsigned int);
        using CRaceBuffHolyQuestResultInfoSetResultSubProcSetRace30_clbk = bool (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, char, char, unsigned int, CRaceBuffHolyQuestResultInfoSetResultSubProcSetRace30_ptr);
        
        using CRaceBuffHolyQuestResultInfodtor_CRaceBuffHolyQuestResultInfo32_ptr = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*);
        using CRaceBuffHolyQuestResultInfodtor_CRaceBuffHolyQuestResultInfo32_clbk = void (WINAPIV*)(struct CRaceBuffHolyQuestResultInfo*, CRaceBuffHolyQuestResultInfodtor_CRaceBuffHolyQuestResultInfo32_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

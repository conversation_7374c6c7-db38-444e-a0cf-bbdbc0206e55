// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _CUTTING_DB_BASE
    {
        struct _LIST
        {
            _INVENKEY Key;
            unsigned int dwDur;
        public:
            void Init();
            _LIST();
            void ctor__LIST();
        };
        bool m_bOldDataLoad;
        char m_byLeftNum;
        _LIST m_List[20];
    public:
        void Init();
        void ReSetOldDataLoad();
        _CUTTING_DB_BASE();
        void ctor__CUTTING_DB_BASE();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_CUTTING_DB_BASE, 162>(), "_CUTTING_DB_BASE");
END_ATF_NAMESPACE

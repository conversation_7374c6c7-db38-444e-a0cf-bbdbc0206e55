// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _param_cash_updatector__param_cash_update2_ptr = void (WINAPIV*)(struct _param_cash_update*, unsigned int, unsigned int, uint16_t);
        using _param_cash_updatector__param_cash_update2_clbk = void (WINAPIV*)(struct _param_cash_update*, unsigned int, unsigned int, uint16_t, _param_cash_updatector__param_cash_update2_ptr);
        using _param_cash_updatesize4_ptr = int (WINAPIV*)(struct _param_cash_update*);
        using _param_cash_updatesize4_clbk = int (WINAPIV*)(struct _param_cash_update*, _param_cash_updatesize4_ptr);
        
        using _param_cash_updatedtor__param_cash_update6_ptr = void (WINAPIV*)(struct _param_cash_update*);
        using _param_cash_updatedtor__param_cash_update6_clbk = void (WINAPIV*)(struct _param_cash_update*, _param_cash_updatedtor__param_cash_update6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

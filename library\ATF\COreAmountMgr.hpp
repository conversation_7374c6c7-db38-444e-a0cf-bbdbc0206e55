// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <COreAmountMgrVtbl.hpp>
#include <_FILETIME.hpp>


START_ATF_NAMESPACE
    struct COreAmountMgr
    {
        COreAmountMgrVtbl *vfptr;
        int m_bChangeRemainRate;
        unsigned int m_dwTotalOreSet;
        unsigned int m_dwMinOreSet;
        float m_fMultipleRate[7][3];
        unsigned int m_dwTotalOreAmount;
        unsigned int m_dwRemainOreAmount;
        unsigned int m_uiProccessIndex;
        char m_byDepositRate;
        _FILETIME m_ftWrite;
        unsigned int m_dwOreTransferAmount;
        char m_byOreTransferCount;
        bool m_bCheckExhOreLog;
        CLogFile m_logOreAmount;
    public:
        COreAmountMgr();
        void ctor_COreAmountMgr();
        bool CheatOreAmount(unsigned int dwTot, unsigned int dwRemain);
        void DecreaseOre(unsigned int dwAlt);
        char GetDepositRate();
        float* GetMultipleRate();
        unsigned int GetOreTransferAmount();
        char GetOreTransferCount();
        unsigned int GetRemainOre();
        unsigned int GetTotalOre();
        void IncreaseOreAmount();
        void IncreaseOreCount();
        void InitRemainOreAmount(unsigned int dwRemain, unsigned int dwTotal);
        void InitTransferOre(unsigned int dwTransAmount, char byTransCount);
        void InsertOreLog(char byType);
        static struct COreAmountMgr* Instance();
        bool IsINIFileChanged();
        int IsOreRemain();
        int LoadINI();
        void Loop();
        void LoopSubProcSendInform();
        bool ReLoad();
        void Release();
        void SetOreTransferAmount();
        void UpdateDepositeRate();
        void UpdateForce();
        ~COreAmountMgr();
        void dtor_COreAmountMgr();
    };
END_ATF_NAMESPACE

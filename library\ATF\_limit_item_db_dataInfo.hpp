// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_limit_item_db_data.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _limit_item_db_datactor__limit_item_db_data2_ptr = void (WINAPIV*)(struct _limit_item_db_data*);
        using _limit_item_db_datactor__limit_item_db_data2_clbk = void (WINAPIV*)(struct _limit_item_db_data*, _limit_item_db_datactor__limit_item_db_data2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CEventLootTable.hpp>
#include <CItemLootTableVtbl.hpp>
#include <CRecordData.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CItemLootTable
    {
        struct _linker_code
        {
            char byTableCode;
            unsigned __int16 wItemIndex;
            int bExist;
        };
        CItemLootTableVtbl *vfptr;
        CRecordData m_tblLoot;
        CEventLootTable *m_pTblEvent;
        _linker_code **m_ppLinkCode;
        int m_nLootNum;
    public:
        CItemLootTable();
        void ctor_CItemLootTable();
        bool Indexing(struct CRecordData* pItemRec, char* pszErrMsg);
        bool ReadRecord(char* szFile, struct CRecordData* pItemRec, char* pszErrMsg);
        ~CItemLootTable();
        void dtor_CItemLootTable();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CItemLootTable, 208>(), "CItemLootTable");
END_ATF_NAMESPACE

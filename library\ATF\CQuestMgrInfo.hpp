// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CQuestMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CQuestMgrctor_CQuestMgr2_ptr = void (WINAPIV*)(struct CQuestMgr*);
        using CQuestMgrctor_CQuestMgr2_clbk = void (WINAPIV*)(struct CQuestMgr*, CQuestMgrctor_CQuestMgr2_ptr);
        using CQuestMgrCalcStartNPCQuestCnt4_ptr = bool (WINAPIV*)(unsigned int*);
        using CQuestMgrCalcStartNPCQuestCnt4_clbk = bool (WINAPIV*)(unsigned int*, CQuestMgrCalcStartNPCQuestCnt4_ptr);
        using CQuestMgrCanGiveupQuest6_ptr = bool (WINAPIV*)(struct CQuestMgr*, char);
        using CQuestMgrCanGiveupQuest6_clbk = bool (WINAPIV*)(struct CQuestMgr*, char, CQuestMgrCanGiveupQuest6_ptr);
        using CQuestMgrCheckFailCondition8_ptr = bool (WINAPIV*)(struct CQuestMgr*, char, int, char*);
        using CQuestMgrCheckFailCondition8_clbk = bool (WINAPIV*)(struct CQuestMgr*, char, int, char*, CQuestMgrCheckFailCondition8_ptr);
        using CQuestMgrCheckFailLoop10_ptr = void (WINAPIV*)(struct CQuestMgr*, int, char*);
        using CQuestMgrCheckFailLoop10_clbk = void (WINAPIV*)(struct CQuestMgr*, int, char*, CQuestMgrCheckFailLoop10_ptr);
        using CQuestMgrCheckLimLv12_ptr = struct _quest_fail_result* (WINAPIV*)(struct CQuestMgr*, int);
        using CQuestMgrCheckLimLv12_clbk = struct _quest_fail_result* (WINAPIV*)(struct CQuestMgr*, int, CQuestMgrCheckLimLv12_ptr);
        using CQuestMgrCheckNPCQuestList14_ptr = void (WINAPIV*)(struct CQuestMgr*, char*, char, struct _NPCQuestIndexTempData*);
        using CQuestMgrCheckNPCQuestList14_clbk = void (WINAPIV*)(struct CQuestMgr*, char*, char, struct _NPCQuestIndexTempData*, CQuestMgrCheckNPCQuestList14_ptr);
        using CQuestMgrCheckNPCQuestStartable16_ptr = struct _happen_event_cont* (WINAPIV*)(struct CQuestMgr*, char*, char, unsigned int, unsigned int);
        using CQuestMgrCheckNPCQuestStartable16_clbk = struct _happen_event_cont* (WINAPIV*)(struct CQuestMgr*, char*, char, unsigned int, unsigned int, CQuestMgrCheckNPCQuestStartable16_ptr);
        using CQuestMgrCheckQuestHappenEvent18_ptr = struct _happen_event_cont* (WINAPIV*)(struct CQuestMgr*, QUEST_HAPPEN, char*, char);
        using CQuestMgrCheckQuestHappenEvent18_clbk = struct _happen_event_cont* (WINAPIV*)(struct CQuestMgr*, QUEST_HAPPEN, char*, char, CQuestMgrCheckQuestHappenEvent18_ptr);
        using CQuestMgrCheckReqAct20_ptr = struct _quest_check_result* (WINAPIV*)(struct CQuestMgr*, int, char*, uint16_t, bool);
        using CQuestMgrCheckReqAct20_clbk = struct _quest_check_result* (WINAPIV*)(struct CQuestMgr*, int, char*, uint16_t, bool, CQuestMgrCheckReqAct20_ptr);
        using CQuestMgrCheckRewardMasteryData22_ptr = bool (WINAPIV*)(int, int, struct _quest_reward_mastery*);
        using CQuestMgrCheckRewardMasteryData22_clbk = bool (WINAPIV*)(int, int, struct _quest_reward_mastery*, CQuestMgrCheckRewardMasteryData22_ptr);
        using CQuestMgrDeleteQuestData24_ptr = void (WINAPIV*)(struct CQuestMgr*, char);
        using CQuestMgrDeleteQuestData24_clbk = void (WINAPIV*)(struct CQuestMgr*, char, CQuestMgrDeleteQuestData24_ptr);
        using CQuestMgrDeleteQuestItem26_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*, uint16_t);
        using CQuestMgrDeleteQuestItem26_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, uint16_t, CQuestMgrDeleteQuestItem26_ptr);
        using CQuestMgrGetCountQuestType28_ptr = int (WINAPIV*)(struct CQuestMgr*, int);
        using CQuestMgrGetCountQuestType28_clbk = int (WINAPIV*)(struct CQuestMgr*, int, CQuestMgrGetCountQuestType28_ptr);
        using CQuestMgrGetLastHappenEvent30_ptr = struct _happen_event_cont* (WINAPIV*)(struct CQuestMgr*);
        using CQuestMgrGetLastHappenEvent30_clbk = struct _happen_event_cont* (WINAPIV*)(struct CQuestMgr*, CQuestMgrGetLastHappenEvent30_ptr);
        using CQuestMgrGetQuestFromEvent32_ptr = struct _Quest_fld* (WINAPIV*)(struct CQuestMgr*, char);
        using CQuestMgrGetQuestFromEvent32_clbk = struct _Quest_fld* (WINAPIV*)(struct CQuestMgr*, char, CQuestMgrGetQuestFromEvent32_ptr);
        using CQuestMgrGiveItem34_ptr = bool (WINAPIV*)(struct CQuestMgr*, char, struct _action_node*, bool);
        using CQuestMgrGiveItem34_clbk = bool (WINAPIV*)(struct CQuestMgr*, char, struct _action_node*, bool, CQuestMgrGiveItem34_ptr);
        using CQuestMgrInitMgr36_ptr = void (WINAPIV*)(struct CQuestMgr*, struct CPlayer*, struct _QUEST_DB_BASE*);
        using CQuestMgrInitMgr36_clbk = void (WINAPIV*)(struct CQuestMgr*, struct CPlayer*, struct _QUEST_DB_BASE*, CQuestMgrInitMgr36_ptr);
        using CQuestMgrInsertNpcQuestHistory38_ptr = char (WINAPIV*)(struct CQuestMgr*, char*, char, long double);
        using CQuestMgrInsertNpcQuestHistory38_clbk = char (WINAPIV*)(struct CQuestMgr*, char*, char, long double, CQuestMgrInsertNpcQuestHistory38_ptr);
        using CQuestMgrIsCompleteNpcQuest40_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*, int);
        using CQuestMgrIsCompleteNpcQuest40_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, int, CQuestMgrIsCompleteNpcQuest40_ptr);
        using CQuestMgrIsPossibleRepeatNpcQuest42_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*, int);
        using CQuestMgrIsPossibleRepeatNpcQuest42_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, int, CQuestMgrIsPossibleRepeatNpcQuest42_ptr);
        using CQuestMgrIsProcLinkNpcQuest44_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*, int);
        using CQuestMgrIsProcLinkNpcQuest44_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, int, CQuestMgrIsProcLinkNpcQuest44_ptr);
        using CQuestMgrIsProcNpcQuest46_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*);
        using CQuestMgrIsProcNpcQuest46_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, CQuestMgrIsProcNpcQuest46_ptr);
        using CQuestMgrIsRecvedQuestByNPC48_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*);
        using CQuestMgrIsRecvedQuestByNPC48_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, CQuestMgrIsRecvedQuestByNPC48_ptr);
        using CQuestMgrIsRecvedQuestByNPC50_ptr = bool (WINAPIV*)(struct CQuestMgr*, int);
        using CQuestMgrIsRecvedQuestByNPC50_clbk = bool (WINAPIV*)(struct CQuestMgr*, int, CQuestMgrIsRecvedQuestByNPC50_ptr);
        using CQuestMgrLoadQuestData52_ptr = bool (WINAPIV*)();
        using CQuestMgrLoadQuestData52_clbk = bool (WINAPIV*)(CQuestMgrLoadQuestData52_ptr);
        using CQuestMgrLoop54_ptr = void (WINAPIV*)(struct CQuestMgr*);
        using CQuestMgrLoop54_clbk = void (WINAPIV*)(struct CQuestMgr*, CQuestMgrLoop54_ptr);
        using CQuestMgrReturnItem56_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*, int, char, bool);
        using CQuestMgrReturnItem56_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, int, char, bool, CQuestMgrReturnItem56_ptr);
        using CQuestMgrSendMsgToMaster_NoCompleteQuestFromNPC58_ptr = void (WINAPIV*)(struct CQuestMgr*, char);
        using CQuestMgrSendMsgToMaster_NoCompleteQuestFromNPC58_clbk = void (WINAPIV*)(struct CQuestMgr*, char, CQuestMgrSendMsgToMaster_NoCompleteQuestFromNPC58_ptr);
        using CQuestMgrSendMsgToMaster_NoHaveGiveItem60_ptr = void (WINAPIV*)(struct CQuestMgr*, char);
        using CQuestMgrSendMsgToMaster_NoHaveGiveItem60_clbk = void (WINAPIV*)(struct CQuestMgr*, char, CQuestMgrSendMsgToMaster_NoHaveGiveItem60_ptr);
        using CQuestMgrSendMsgToMaster_NoHaveReturnItem62_ptr = void (WINAPIV*)(struct CQuestMgr*, char);
        using CQuestMgrSendMsgToMaster_NoHaveReturnItem62_clbk = void (WINAPIV*)(struct CQuestMgr*, char, CQuestMgrSendMsgToMaster_NoHaveReturnItem62_ptr);
        using CQuestMgrSendMsgToMaster_ReturnItemAfterQuest64_ptr = void (WINAPIV*)(struct CQuestMgr*, uint16_t, char, char);
        using CQuestMgrSendMsgToMaster_ReturnItemAfterQuest64_clbk = void (WINAPIV*)(struct CQuestMgr*, uint16_t, char, char, CQuestMgrSendMsgToMaster_ReturnItemAfterQuest64_ptr);
        using CQuestMgr_CheckCondition66_ptr = bool (WINAPIV*)(struct CQuestMgr*, struct _happen_event_condition_node*);
        using CQuestMgr_CheckCondition66_clbk = bool (WINAPIV*)(struct CQuestMgr*, struct _happen_event_condition_node*, CQuestMgr_CheckCondition66_ptr);
        using CQuestMgr__CheckCond_Class68_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*);
        using CQuestMgr__CheckCond_Class68_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, CQuestMgr__CheckCond_Class68_ptr);
        using CQuestMgr__CheckCond_Dalant70_ptr = bool (WINAPIV*)(struct CQuestMgr*, char, int);
        using CQuestMgr__CheckCond_Dalant70_clbk = bool (WINAPIV*)(struct CQuestMgr*, char, int, CQuestMgr__CheckCond_Dalant70_ptr);
        using CQuestMgr__CheckCond_Dummy72_ptr = bool (WINAPIV*)(struct CQuestMgr*, int, char*);
        using CQuestMgr__CheckCond_Dummy72_clbk = bool (WINAPIV*)(struct CQuestMgr*, int, char*, CQuestMgr__CheckCond_Dummy72_ptr);
        using CQuestMgr__CheckCond_Equip74_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*);
        using CQuestMgr__CheckCond_Equip74_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, CQuestMgr__CheckCond_Equip74_ptr);
        using CQuestMgr__CheckCond_Gold76_ptr = bool (WINAPIV*)(struct CQuestMgr*, char, int);
        using CQuestMgr__CheckCond_Gold76_clbk = bool (WINAPIV*)(struct CQuestMgr*, char, int, CQuestMgr__CheckCond_Gold76_ptr);
        using CQuestMgr__CheckCond_Grade78_ptr = bool (WINAPIV*)(struct CQuestMgr*, char, int);
        using CQuestMgr__CheckCond_Grade78_clbk = bool (WINAPIV*)(struct CQuestMgr*, char, int, CQuestMgr__CheckCond_Grade78_ptr);
        using CQuestMgr__CheckCond_Guild80_ptr = bool (WINAPIV*)(struct CQuestMgr*, int);
        using CQuestMgr__CheckCond_Guild80_clbk = bool (WINAPIV*)(struct CQuestMgr*, int, CQuestMgr__CheckCond_Guild80_ptr);
        using CQuestMgr__CheckCond_Have82_ptr = bool (WINAPIV*)(struct CQuestMgr*, int, char*);
        using CQuestMgr__CheckCond_Have82_clbk = bool (WINAPIV*)(struct CQuestMgr*, int, char*, CQuestMgr__CheckCond_Have82_ptr);
        using CQuestMgr__CheckCond_LV84_ptr = bool (WINAPIV*)(struct CQuestMgr*, char, int);
        using CQuestMgr__CheckCond_LV84_clbk = bool (WINAPIV*)(struct CQuestMgr*, char, int, CQuestMgr__CheckCond_LV84_ptr);
        using CQuestMgr__CheckCond_Mastery86_ptr = bool (WINAPIV*)(struct CQuestMgr*, char, char*);
        using CQuestMgr__CheckCond_Mastery86_clbk = bool (WINAPIV*)(struct CQuestMgr*, char, char*, CQuestMgr__CheckCond_Mastery86_ptr);
        using CQuestMgr__CheckCond_Nation88_ptr = bool (WINAPIV*)(struct CQuestMgr*, int);
        using CQuestMgr__CheckCond_Nation88_clbk = bool (WINAPIV*)(struct CQuestMgr*, int, CQuestMgr__CheckCond_Nation88_ptr);
        using CQuestMgr__CheckCond_Party90_ptr = bool (WINAPIV*)(struct CQuestMgr*, int);
        using CQuestMgr__CheckCond_Party90_clbk = bool (WINAPIV*)(struct CQuestMgr*, int, CQuestMgr__CheckCond_Party90_ptr);
        using CQuestMgr__CheckCond_Race92_ptr = bool (WINAPIV*)(struct CQuestMgr*, char*);
        using CQuestMgr__CheckCond_Race92_clbk = bool (WINAPIV*)(struct CQuestMgr*, char*, CQuestMgr__CheckCond_Race92_ptr);
        
        using CQuestMgrdtor_CQuestMgr94_ptr = void (WINAPIV*)(struct CQuestMgr*);
        using CQuestMgrdtor_CQuestMgr94_clbk = void (WINAPIV*)(struct CQuestMgr*, CQuestMgrdtor_CQuestMgr94_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

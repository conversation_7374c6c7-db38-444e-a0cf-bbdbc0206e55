// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_log_sheet_usernum.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _log_sheet_usernumsize2_ptr = int (WINAPIV*)(struct _log_sheet_usernum*);
        using _log_sheet_usernumsize2_clbk = int (WINAPIV*)(struct _log_sheet_usernum*, _log_sheet_usernumsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

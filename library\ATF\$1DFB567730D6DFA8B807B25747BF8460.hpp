// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IDispatch.hpp>
#include <IUnknown.hpp>
#include <tagBLOB.hpp>
#include <tagCY.hpp>
#include <tagDBCOLUMNID.hpp>
#include <tagDBVARIANT.hpp>
#include <tagSAFEARRAY.hpp>
#include <tagVARIANT.hpp>



START_ATF_NAMESPACE
    union $1DFB567730D6DFA8B807B25747BF8460
    {
        char bVal;
        __int16 iVal;
        int lVal;
        float fltVal;
        long double dblVal;
        __int16 xbool;
        int scode;
        tagCY cyVal;
        long double date;
        wchar_t *bstrVal;
        IUnknown *punkVal;
        IDispatch *pdispVal;
        tagSAFEARRAY *parray;
        char *pbVal;
        __int16 *piVal;
        int *plVal;
        float *pfltVal;
        long double *pdblVal;
        __int16 *pbool;
        int *pscode;
        tagCY *pcyVal;
        long double *pdate;
        wchar_t **pbstrVal;
        IUnknown **ppunkVal;
        IDispatch **ppdispVal;
        tagSAFEARRAY **pparray;
        tagVARIANT *pvarVal;
        void *byref;
        tagBLOB blob;
        tagDBCOLUMNID *pColumnid;
        char *pszVal;
        wchar_t *pwszVal;
        wchar_t **ppwszVal;
        tagBLOB *pblob;
        tagDBCOLUMNID **ppColumnid;
        tagDBVARIANT *pdbvarVal;
    };
END_ATF_NAMESPACE

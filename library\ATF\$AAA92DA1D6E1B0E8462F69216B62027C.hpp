// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $AAA92DA1D6E1B0E8462F69216B62027C
    {
        BYTE gap0[8];
        __int16 *pboolVal;
    };    
    static_assert(ATF::checkSize<$AAA92DA1D6E1B0E8462F69216B62027C, 16>(), "$AAA92DA1D6E1B0E8462F69216B62027C");
END_ATF_NAMESPACE

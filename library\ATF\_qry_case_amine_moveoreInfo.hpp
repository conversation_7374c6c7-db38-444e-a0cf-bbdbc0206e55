// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_amine_moveore.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_amine_moveorector__qry_case_amine_moveore2_ptr = void (WINAPIV*)(struct _qry_case_amine_moveore*);
        using _qry_case_amine_moveorector__qry_case_amine_moveore2_clbk = void (WINAPIV*)(struct _qry_case_amine_moveore*, _qry_case_amine_moveorector__qry_case_amine_moveore2_ptr);
        using _qry_case_amine_moveoresize4_ptr = int (WINAPIV*)(struct _qry_case_amine_moveore*);
        using _qry_case_amine_moveoresize4_clbk = int (WINAPIV*)(struct _qry_case_amine_moveore*, _qry_case_amine_moveoresize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <INationGameGuardSystem.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using INationGameGuardSystemctor_INationGameGuardSystem2_ptr = void (WINAPIV*)(struct INationGameGuardSystem*);
        using INationGameGuardSystemctor_INationGameGuardSystem2_clbk = void (WINAPIV*)(struct INationGameGuardSystem*, INationGameGuardSystemctor_INationGameGuardSystem2_ptr);
        
        using INationGameGuardSystemdtor_INationGameGuardSystem7_ptr = void (WINAPIV*)(struct INationGameGuardSystem*);
        using INationGameGuardSystemdtor_INationGameGuardSystem7_clbk = void (WINAPIV*)(struct INationGameGuardSystem*, INationGameGuardSystemdtor_INationGameGuardSystem7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

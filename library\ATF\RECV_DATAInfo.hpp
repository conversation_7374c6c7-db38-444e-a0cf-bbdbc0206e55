// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RECV_DATA.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using RECV_DATActor_RECV_DATA2_ptr = void (WINAPIV*)(struct RECV_DATA*);
        using RECV_DATActor_RECV_DATA2_clbk = void (WINAPIV*)(struct RECV_DATA*, RECV_DATActor_RECV_DATA2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

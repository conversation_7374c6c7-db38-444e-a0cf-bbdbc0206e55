// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CWeeklyGuildRankRecord
    {
        unsigned int m_dwInx;
        unsigned __int16 m_wRank;
        unsigned int m_dwSerial;
        char m_wszGuildName[17];
        char m_byGrade;
        long double m_dKillPvpPoint;
        long double m_dGuildBattlePvpPoint;
    public:
        CWeeklyGuildRankRecord();
        void ctor_CWeeklyGuildRankRecord();
        void Clear();
        ~CWeeklyGuildRankRecord();
        void dtor_CWeeklyGuildRankRecord();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_TBBUTTON.hpp>
#include <tagNMHDR.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagNMTOOLBARA
    {
        tagNMHDR hdr;
        int iItem;
        _TBBUTTON tbButton;
        int cchText;
        char *pszText;
        tagRECT rcButton;
    };
END_ATF_NAMESPACE

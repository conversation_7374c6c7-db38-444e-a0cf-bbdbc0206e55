// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace US
    {
        template<typename _Ty, typename _Mtx, bool _B>
        struct CCircularFIFO
        {
            _Mtx m_CS;
            _Ty *m_pPool;
            unsigned __int64 m_StartPos;
            unsigned __int64 m_EndPos;
            unsigned __int64 m_Size;
            unsigned __int64 m_AllocSize;
        };
    }; // end namespace US
END_ATF_NAMESPACE

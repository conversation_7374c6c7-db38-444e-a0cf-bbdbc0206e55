// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CWeeklyGuildRankInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CWeeklyGuildRankInfoctor_CWeeklyGuildRankInfo2_ptr = void (WINAPIV*)(struct CWeeklyGuildRankInfo*);
        using CWeeklyGuildRankInfoctor_CWeeklyGuildRankInfo2_clbk = void (WINAPIV*)(struct CWeeklyGuildRankInfo*, CWeeklyGuildRankInfoctor_CWeeklyGuildRankInfo2_ptr);
        using CWeeklyGuildRankInfoCheckEmpty4_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*);
        using CWeeklyGuildRankInfoCheckEmpty4_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*, CWeeklyGuildRankInfoCheckEmpty4_ptr);
        using CWeeklyGuildRankInfoClear6_ptr = void (WINAPIV*)(struct CWeeklyGuildRankInfo*);
        using CWeeklyGuildRankInfoClear6_clbk = void (WINAPIV*)(struct CWeeklyGuildRankInfo*, CWeeklyGuildRankInfoClear6_ptr);
        using CWeeklyGuildRankInfoClearOwner8_ptr = void (WINAPIV*)(struct CWeeklyGuildRankInfo*);
        using CWeeklyGuildRankInfoClearOwner8_clbk = void (WINAPIV*)(struct CWeeklyGuildRankInfo*, CWeeklyGuildRankInfoClearOwner8_ptr);
        using CWeeklyGuildRankInfoClearRank10_ptr = void (WINAPIV*)(struct CWeeklyGuildRankInfo*);
        using CWeeklyGuildRankInfoClearRank10_clbk = void (WINAPIV*)(struct CWeeklyGuildRankInfo*, CWeeklyGuildRankInfoClearRank10_ptr);
        using CWeeklyGuildRankInfoFind12_ptr = int (WINAPIV*)(struct CWeeklyGuildRankInfo*, char, unsigned int);
        using CWeeklyGuildRankInfoFind12_clbk = int (WINAPIV*)(struct CWeeklyGuildRankInfo*, char, unsigned int, CWeeklyGuildRankInfoFind12_ptr);
        using CWeeklyGuildRankInfoGetCurOwnerGuild14_ptr = struct CGuild* (WINAPIV*)(struct CWeeklyGuildRankInfo*, char, char);
        using CWeeklyGuildRankInfoGetCurOwnerGuild14_clbk = struct CGuild* (WINAPIV*)(struct CWeeklyGuildRankInfo*, char, char, CWeeklyGuildRankInfoGetCurOwnerGuild14_ptr);
        using CWeeklyGuildRankInfoGetOwnerGuild16_ptr = struct CGuild* (WINAPIV*)(struct CWeeklyGuildRankInfo*, char, char);
        using CWeeklyGuildRankInfoGetOwnerGuild16_clbk = struct CGuild* (WINAPIV*)(struct CWeeklyGuildRankInfo*, char, char, CWeeklyGuildRankInfoGetOwnerGuild16_ptr);
        using CWeeklyGuildRankInfoGetPrevOwnerGuild18_ptr = struct CGuild* (WINAPIV*)(struct CWeeklyGuildRankInfo*, char, char);
        using CWeeklyGuildRankInfoGetPrevOwnerGuild18_clbk = struct CGuild* (WINAPIV*)(struct CWeeklyGuildRankInfo*, char, char, CWeeklyGuildRankInfoGetPrevOwnerGuild18_ptr);
        using CWeeklyGuildRankInfoInit20_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*);
        using CWeeklyGuildRankInfoInit20_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, CWeeklyGuildRankInfoInit20_ptr);
        using CWeeklyGuildRankInfoIsNoDataPrev22_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*);
        using CWeeklyGuildRankInfoIsNoDataPrev22_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, CWeeklyGuildRankInfoIsNoDataPrev22_ptr);
        using CWeeklyGuildRankInfoIsNoDataToday24_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*);
        using CWeeklyGuildRankInfoIsNoDataToday24_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, CWeeklyGuildRankInfoIsNoDataToday24_ptr);
        using CWeeklyGuildRankInfoLoad26_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*, bool*);
        using CWeeklyGuildRankInfoLoad26_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*, bool*, CWeeklyGuildRankInfoLoad26_ptr);
        using CWeeklyGuildRankInfoLoadOwner28_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _weeklyguildrank_owner_info*);
        using CWeeklyGuildRankInfoLoadOwner28_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _weeklyguildrank_owner_info*, CWeeklyGuildRankInfoLoadOwner28_ptr);
        using CWeeklyGuildRankInfoLoadPrev30_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*);
        using CWeeklyGuildRankInfoLoadPrev30_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*, CWeeklyGuildRankInfoLoadPrev30_ptr);
        using CWeeklyGuildRankInfoLoadToday32_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*);
        using CWeeklyGuildRankInfoLoadToday32_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*, CWeeklyGuildRankInfoLoadToday32_ptr);
        using CWeeklyGuildRankInfoSend34_ptr = void (WINAPIV*)(struct CWeeklyGuildRankInfo*, unsigned int, int, char, char, unsigned int);
        using CWeeklyGuildRankInfoSend34_clbk = void (WINAPIV*)(struct CWeeklyGuildRankInfo*, unsigned int, int, char, char, unsigned int, CWeeklyGuildRankInfoSend34_ptr);
        using CWeeklyGuildRankInfoSetNoDataFlagToday36_ptr = void (WINAPIV*)(struct CWeeklyGuildRankInfo*);
        using CWeeklyGuildRankInfoSetNoDataFlagToday36_clbk = void (WINAPIV*)(struct CWeeklyGuildRankInfo*, CWeeklyGuildRankInfoSetNoDataFlagToday36_ptr);
        using CWeeklyGuildRankInfoUpdate38_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*);
        using CWeeklyGuildRankInfoUpdate38_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _pvppoint_guild_rank_info*, CWeeklyGuildRankInfoUpdate38_ptr);
        using CWeeklyGuildRankInfoUpdateOwner40_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _weeklyguildrank_owner_info*);
        using CWeeklyGuildRankInfoUpdateOwner40_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankInfo*, struct _weeklyguildrank_owner_info*, CWeeklyGuildRankInfoUpdateOwner40_ptr);
        
        using CWeeklyGuildRankInfodtor_CWeeklyGuildRankInfo42_ptr = void (WINAPIV*)(struct CWeeklyGuildRankInfo*);
        using CWeeklyGuildRankInfodtor_CWeeklyGuildRankInfo42_clbk = void (WINAPIV*)(struct CWeeklyGuildRankInfo*, CWeeklyGuildRankInfodtor_CWeeklyGuildRankInfo42_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

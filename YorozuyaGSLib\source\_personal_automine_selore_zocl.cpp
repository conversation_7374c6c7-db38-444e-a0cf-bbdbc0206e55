#include <_personal_automine_selore_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_selore_zocl::_personal_automine_selore_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_selore_zocl*);
        (org_ptr(0x1402e18e0L))(this);
    };
    void _personal_automine_selore_zocl::ctor__personal_automine_selore_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_selore_zocl*);
        (org_ptr(0x1402e18e0L))(this);
    };
    int _personal_automine_selore_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_selore_zocl*);
        return (org_ptr(0x1402e1930L))(this);
    };
END_ATF_NAMESPACE

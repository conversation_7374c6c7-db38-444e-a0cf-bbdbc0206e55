// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_update_user_guild_data
    {
        unsigned int dwAvatorSerial;
        unsigned int dwGuildIndex;
        unsigned int dwGuildSerial;
        char byGrade;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_update_user_guild_data, 16>(), "_qry_case_update_user_guild_data");
END_ATF_NAMESPACE

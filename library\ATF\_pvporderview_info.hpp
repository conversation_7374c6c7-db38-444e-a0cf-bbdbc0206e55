// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _pvporderview_info
    {
        __int64 tUpdatedate;
        int nDeath;
        int nKill;
        long double dTodayStacked;
        long double dPvpPoint;
        long double dPvpTempCash;
        long double dPvpCash;
        unsigned int dwKillerSerial[10];
        char byContHaveCash;
        char byContLoseCash;
        bool bRaceWarRecvr;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

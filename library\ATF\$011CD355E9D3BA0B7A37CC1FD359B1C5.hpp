// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DDPIXELFORMAT.hpp>


START_ATF_NAMESPACE
    union $011CD355E9D3BA0B7A37CC1FD359B1C5
    {
        _DDPIXELFORMAT ddpfPixelFormat;
        unsigned int dwFVF;
    };    
    static_assert(ATF::checkSize<$011CD355E9D3BA0B7A37CC1FD359B1C5, 32>(), "$011CD355E9D3BA0B7A37CC1FD359B1C5");
END_ATF_NAMESPACE

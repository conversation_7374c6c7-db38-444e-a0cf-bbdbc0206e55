// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryES.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryESctor_CNationSettingFactoryES2_ptr = void (WINAPIV*)(struct CNationSettingFactoryES*);
        using CNationSettingFactoryESctor_CNationSettingFactoryES2_clbk = void (WINAPIV*)(struct CNationSettingFactoryES*, CNationSettingFactoryESctor_CNationSettingFactoryES2_ptr);
        using CNationSettingFactoryESCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryES*, int, char*, bool);
        using CNationSettingFactoryESCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryES*, int, char*, bool, CNationSettingFactoryESCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

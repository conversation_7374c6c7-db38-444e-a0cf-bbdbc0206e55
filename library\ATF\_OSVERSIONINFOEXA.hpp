// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _OSVERSIONINFOEXA
    {
        unsigned int dwOSVersionInfoSize;
        unsigned int dwMajorVersion;
        unsigned int dwMinorVersion;
        unsigned int dwBuildNumber;
        unsigned int dwPlatformId;
        char szCSDVersion[128];
        unsigned __int16 wServicePackMajor;
        unsigned __int16 wServicePackMinor;
        unsigned __int16 wSuiteMask;
        char wProductType;
        char wReserved;
    };
END_ATF_NAMESPACE

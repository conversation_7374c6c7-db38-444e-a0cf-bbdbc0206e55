// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_mon_block.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _mon_blockSelectDummyIndex2_ptr = int (WINAPIV*)(struct _mon_block*);
        using _mon_blockSelectDummyIndex2_clbk = int (WINAPIV*)(struct _mon_block*, _mon_blockSelectDummyIndex2_ptr);
        using _mon_blockSetBlock4_ptr = bool (WINAPIV*)(struct _mon_block*, struct _mon_block_fld*, struct CMapData*, struct _dummy_position**);
        using _mon_blockSetBlock4_clbk = bool (WINAPIV*)(struct _mon_block*, struct _mon_block_fld*, struct CMapData*, struct _dummy_position**, _mon_blockSetBlock4_ptr);
        using _mon_blockSetRotateBlock6_ptr = void (WINAPIV*)(struct _mon_block*, bool);
        using _mon_blockSetRotateBlock6_clbk = void (WINAPIV*)(struct _mon_block*, bool, _mon_blockSetRotateBlock6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateInBattle.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleAdvanceRegenState2_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleAdvanceRegenState2_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, GUILD_BATTLE__CNormalGuildBattleStateInBattleAdvanceRegenState2_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateInBattlector_CNormalGuildBattleStateInBattle4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattlector_CNormalGuildBattleStateInBattle4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, GUILD_BATTLE__CNormalGuildBattleStateInBattlector_CNormalGuildBattleStateInBattle4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleEnter6_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleEnter6_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateInBattleEnter6_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleFin8_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleFin8_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateInBattleFin8_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleGetTerm10_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleGetTerm10_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct ATL::CTimeSpan*, GUILD_BATTLE__CNormalGuildBattleStateInBattleGetTerm10_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleIsInBattleRegenState12_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleIsInBattleRegenState12_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, GUILD_BATTLE__CNormalGuildBattleStateInBattleIsInBattleRegenState12_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleLoop14_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleLoop14_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateInBattleLoop14_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleSetBattleTime16_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct ATL::CTimeSpan);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleSetBattleTime16_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, struct ATL::CTimeSpan, GUILD_BATTLE__CNormalGuildBattleStateInBattleSetBattleTime16_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleSetGotoRegenState18_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattleSetGotoRegenState18_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, GUILD_BATTLE__CNormalGuildBattleStateInBattleSetGotoRegenState18_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateInBattledtor_CNormalGuildBattleStateInBattle20_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateInBattledtor_CNormalGuildBattleStateInBattle20_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateInBattle*, GUILD_BATTLE__CNormalGuildBattleStateInBattledtor_CNormalGuildBattleStateInBattle20_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

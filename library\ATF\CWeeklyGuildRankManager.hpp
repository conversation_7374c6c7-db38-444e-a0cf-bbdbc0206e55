// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuild.hpp>
#include <CPlayer.hpp>
#include <CRFWorldDatabase.hpp>
#include <CWeeklyGuildRankInfo.hpp>
#include <_pvppoint_guild_rank_info.hpp>
#include <_weeklyguildrank_owner_info.hpp>


START_ATF_NAMESPACE
    struct CWeeklyGuildRankManager
    {
        __int64 m_tNextUpdateTime;
        __int64 m_tNextSetOwnerTime;
        CWeeklyGuildRankInfo m_kInfo;
    public:
        CWeeklyGuildRankManager();
        void ctor_CWeeklyGuildRankManager();
        void CompleteLoadeTodayRank(char byRet, char* pLoadData);
        void CompleteUpdateClear(char byRet);
        void CompleteUpdateWeeklyOwner(char byRet, char* pLoadData);
        bool CreatePvpPointGuildRank(char* szDate);
        static void Destroy();
        struct CGuild* GetPrevOwnerGuild(char byRace, char byNth);
        void GetPrevRankDate(char* szDate, int iBuffSize);
        void GetTodayRankDate(char* szDate, int iBuffSize);
        bool Init();
        bool InitNextSetOwnerDate();
        bool InsertDefaultWeeklyPvpPointSumRecord();
        bool InsertSettlementOwner(struct CRFWorldDatabase* pkWorldDB, char* pData);
        static struct CWeeklyGuildRankManager* Instance();
        bool IsEmptyRank(struct _pvppoint_guild_rank_info* pkInfo);
        bool Load();
        int64_t LoadINILastRankTime();
        bool LoadPrevOwner();
        bool LoadPrevTable(char* szDate, struct _pvppoint_guild_rank_info* kInfo);
        void Loop();
        void OrderRank(struct _pvppoint_guild_rank_info* pkInfo);
        bool PushDQSIncWeeklyPvpPointSum(unsigned int dwGuildSerial, long double dPoint);
        void PushSettlementOwnerDBLog(char* pInfo);
        bool SaveINI();
        bool SelectOwnerGuild(char* szDate, struct _weeklyguildrank_owner_info* pkInfo);
        void Send(unsigned int dwVer, char byTabRace, struct CPlayer* pkPlayer);
        void SetNextRankDate();
        void SetSettlementAreaManageOwnerGuild();
        bool UpdateOwnerGuild(char* szDate);
        bool UpdateRankDBRecord(char* szDate, struct _pvppoint_guild_rank_info* pkInfo);
        bool UpdateTodayRank(char* pLoadData);
        int UpdateTodayTable(char* szDate, struct _pvppoint_guild_rank_info* pkInfo);
        bool UpdateWeeklyOwner(char* pLoadData);
        ~CWeeklyGuildRankManager();
        void dtor_CWeeklyGuildRankManager();
    };
END_ATF_NAMESPACE

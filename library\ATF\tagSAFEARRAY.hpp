// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagSAFEARRAYBOUND.hpp>


START_ATF_NAMESPACE
    struct tagSAFEARRAY
    {
        unsigned __int16 cDims;
        unsigned __int16 fFeatures;
        unsigned int cbElements;
        unsigned int cLocks;
        void *pvData;
        tagSAFEARRAYBOUND rgsabound[1];
    };
END_ATF_NAMESPACE

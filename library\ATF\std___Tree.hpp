// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Tree_val.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Tree<_Tmap_traits<basic_string<char,char_traits<char>,allocator<char> >,AreaList,less<basic_string<char,char_traits<char>,allocator<char> > >,allocator<pair<basic_string<char,char_traits<char>,allocator<char> > const ,AreaList> >,0> > : _Tree_val<_Tmap_traits<basic_string<char,char_traits<char>,allocator<char> >,AreaList,less<basic_string<char,char_traits<char>,allocator<char> > >,allocator<pair<basic_string<char,char_traits<char>,allocator<char> > const ,AreaList> >,0> >
        {
            template<>
            enum _Redbl
            {
                _Red = 0x0,
                _Black = 0x1,
            };
            template<>
            struct  const_iterator : _Bidit<pair<basic_string<char,char_traits<char>,allocator<char> > const ,AreaList>,__int64,pair<basic_string<char,char_traits<char>,allocator<char> > const ,AreaList> const *,pair<basic_string<char,char_traits<char>,allocator<char> > const ,AreaList> const &>
            {
                _Tree_nod<_Tmap_traits<basic_string<char,char_traits<char>,allocator<char> >,AreaList,less<basic_string<char,char_traits<char>,allocator<char> > >,allocator<pair<basic_string<char,char_traits<char>,allocator<char> > const ,AreaList> >,0> >::_Node *_Ptr;
            };
            template<>
            struct  iterator : const_iterator
            {
            };
            _Tree_nod<_Tmap_traits<basic_string<char,char_traits<char>,allocator<char> >,AreaList,less<basic_string<char,char_traits<char>,allocator<char> > >,allocator<pair<basic_string<char,char_traits<char>,allocator<char> > const ,AreaList> >,0> >::_Node *_Myhead;
            unsigned __int64 _Mysize;
        };    
        static_assert(ATF::checkSize<std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >, 56>(), "std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >");
    }; // end namespace std
END_ATF_NAMESPACE

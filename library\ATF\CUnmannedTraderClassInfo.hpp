// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderClassInfoVtbl.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderClassInfo
    {
        CUnmannedTraderClassInfoVtbl *vfptr;
        unsigned int m_dwID;
        unsigned int m_dwVer;
        char m_szTypeName[128];
        char m_szClassName[128];
    public:
        CUnmannedTraderClassInfo(unsigned int dwID);
        void ctor_CUnmannedTraderClassInfo(unsigned int dwID);
        struct CUnmannedTraderClassInfo* Copy(struct CUnmannedTraderClassInfo* lhs);
        unsigned int GetID();
        char* GetTypeName();
        ~CUnmannedTraderClassInfo();
        void dtor_CUnmannedTraderClassInfo();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _READ_SOUND_ENTITIES_LIST
    {
        unsigned __int16 id;
        unsigned __int16 event_time;
        unsigned int flag;
        float scale;
        float attn;
        float pos[3];
        float box_scale[3];
        float box_attn;
        float box_rot_x;
        float box_rot_y;
        unsigned int spare;
    };
END_ATF_NAMESPACE

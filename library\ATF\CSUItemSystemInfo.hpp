// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CSUItemSystem.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CSUItemSystemctor_CSUItemSystem2_ptr = void (WINAPIV*)(struct CSUItemSystem*);
        using CSUItemSystemctor_CSUItemSystem2_clbk = void (WINAPIV*)(struct CSUItemSystem*, CSUItemSystemctor_CSUItemSystem2_ptr);
        using CSUItemSystemClass_Init4_ptr = void (WINAPIV*)(struct CSUItemSystem*);
        using CSUItemSystemClass_Init4_clbk = void (WINAPIV*)(struct CSUItemSystem*, CSUItemSystemClass_Init4_ptr);
        using CSUItemSystemGetCRecordData_SetItem6_ptr = struct CRecordData* (WINAPIV*)(struct CSUItemSystem*);
        using CSUItemSystemGetCRecordData_SetItem6_clbk = struct CRecordData* (WINAPIV*)(struct CSUItemSystem*, CSUItemSystemGetCRecordData_SetItem6_ptr);
        using CSUItemSystemGetCSetItemType8_ptr = struct CSetItemType* (WINAPIV*)(struct CSUItemSystem*);
        using CSUItemSystemGetCSetItemType8_clbk = struct CSetItemType* (WINAPIV*)(struct CSUItemSystem*, CSUItemSystemGetCSetItemType8_ptr);
        using CSUItemSystemGetSetItemTableInfo10_ptr = int (WINAPIV*)(struct CSUItemSystem*, unsigned int, char*, int);
        using CSUItemSystemGetSetItemTableInfo10_clbk = int (WINAPIV*)(struct CSUItemSystem*, unsigned int, char*, int, CSUItemSystemGetSetItemTableInfo10_ptr);
        using CSUItemSystemInstance12_ptr = struct CSUItemSystem* (WINAPIV*)();
        using CSUItemSystemInstance12_clbk = struct CSUItemSystem* (WINAPIV*)(CSUItemSystemInstance12_ptr);
        using CSUItemSystemSUItemSystem_CheckData14_ptr = bool (WINAPIV*)(struct CSUItemSystem*);
        using CSUItemSystemSUItemSystem_CheckData14_clbk = bool (WINAPIV*)(struct CSUItemSystem*, CSUItemSystemSUItemSystem_CheckData14_ptr);
        using CSUItemSystemSUItemSystem_Init16_ptr = bool (WINAPIV*)(struct CSUItemSystem*);
        using CSUItemSystemSUItemSystem_Init16_clbk = bool (WINAPIV*)(struct CSUItemSystem*, CSUItemSystemSUItemSystem_Init16_ptr);
        using CSUItemSystemSUItemSystem_UnInit18_ptr = bool (WINAPIV*)(struct CSUItemSystem*);
        using CSUItemSystemSUItemSystem_UnInit18_clbk = bool (WINAPIV*)(struct CSUItemSystem*, CSUItemSystemSUItemSystem_UnInit18_ptr);
        
        using CSUItemSystemdtor_CSUItemSystem20_ptr = void (WINAPIV*)(struct CSUItemSystem*);
        using CSUItemSystemdtor_CSUItemSystem20_clbk = void (WINAPIV*)(struct CSUItemSystem*, CSUItemSystemdtor_CSUItemSystem20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <BNetworkVtbl.hpp>
#include <HINSTANCE__.hpp>


START_ATF_NAMESPACE
    struct BNetwork
    {
        BNetworkVtbl *vfptr;
        HINSTANCE__ *m_hBillingDLL;
        void (WINAPIV *SetDataAnalysisFunc)(bool (WINAPIV *)(unsigned int, unsigned int, struct _MSG_HEADER *, char *));
        void (WINAPIV *Release)();
        int (WINAPIV *LoadSendMsg)(unsigned int, unsigned int, char *, char *, unsigned __int16);
        bool (WINAPIV *SetNetSystem)(unsigned int, struct _NET_TYPE_PARAM *, char *, char *);
        void (WINAPIV *OnLoop)();
        int (WINAPIV *Connect)(unsigned int, unsigned int, unsigned int, unsigned __int16);
        void (WINAPIV *CloseSocket)(unsigned int, unsigned int, bool);
        struct _socket *(WINAPIV *GetSocket)(unsigned int, unsigned int);
        bool (WINAPIV *RecvS)(char *, int, int *);
        bool (WINAPIV *SendS)(char *, int, int *);
    public:
        BNetwork();
        void ctor_BNetwork();
        void FreeDLL();
        void InitNetwork();
        bool LoadDll(char* dll_name);
        ~BNetwork();
        void dtor_BNetwork();
    };
END_ATF_NAMESPACE

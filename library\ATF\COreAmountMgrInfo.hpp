// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <COreAmountMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using COreAmountMgrctor_COreAmountMgr2_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrctor_COreAmountMgr2_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrctor_COreAmountMgr2_ptr);
        using COreAmountMgrCheatOreAmount4_ptr = bool (WINAPIV*)(struct COreAmountMgr*, unsigned int, unsigned int);
        using COreAmountMgrCheatOreAmount4_clbk = bool (WINAPIV*)(struct COreAmountMgr*, unsigned int, unsigned int, COreAmountMgrCheatOreAmount4_ptr);
        using COreAmountMgrDecreaseOre6_ptr = void (WINAPIV*)(struct COreAmountMgr*, unsigned int);
        using COreAmountMgrDecreaseOre6_clbk = void (WINAPIV*)(struct COreAmountMgr*, unsigned int, COreAmountMgrDecreaseOre6_ptr);
        using COreAmountMgrGetDepositRate8_ptr = char (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrGetDepositRate8_clbk = char (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrGetDepositRate8_ptr);
        using COreAmountMgrGetMultipleRate10_ptr = float* (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrGetMultipleRate10_clbk = float* (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrGetMultipleRate10_ptr);
        using COreAmountMgrGetOreTransferAmount12_ptr = unsigned int (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrGetOreTransferAmount12_clbk = unsigned int (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrGetOreTransferAmount12_ptr);
        using COreAmountMgrGetOreTransferCount14_ptr = char (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrGetOreTransferCount14_clbk = char (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrGetOreTransferCount14_ptr);
        using COreAmountMgrGetRemainOre16_ptr = unsigned int (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrGetRemainOre16_clbk = unsigned int (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrGetRemainOre16_ptr);
        using COreAmountMgrGetTotalOre18_ptr = unsigned int (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrGetTotalOre18_clbk = unsigned int (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrGetTotalOre18_ptr);
        using COreAmountMgrIncreaseOreAmount20_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrIncreaseOreAmount20_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrIncreaseOreAmount20_ptr);
        using COreAmountMgrIncreaseOreCount22_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrIncreaseOreCount22_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrIncreaseOreCount22_ptr);
        using COreAmountMgrInitRemainOreAmount24_ptr = void (WINAPIV*)(struct COreAmountMgr*, unsigned int, unsigned int);
        using COreAmountMgrInitRemainOreAmount24_clbk = void (WINAPIV*)(struct COreAmountMgr*, unsigned int, unsigned int, COreAmountMgrInitRemainOreAmount24_ptr);
        using COreAmountMgrInitTransferOre26_ptr = void (WINAPIV*)(struct COreAmountMgr*, unsigned int, char);
        using COreAmountMgrInitTransferOre26_clbk = void (WINAPIV*)(struct COreAmountMgr*, unsigned int, char, COreAmountMgrInitTransferOre26_ptr);
        using COreAmountMgrInsertOreLog28_ptr = void (WINAPIV*)(struct COreAmountMgr*, char);
        using COreAmountMgrInsertOreLog28_clbk = void (WINAPIV*)(struct COreAmountMgr*, char, COreAmountMgrInsertOreLog28_ptr);
        using COreAmountMgrInstance30_ptr = struct COreAmountMgr* (WINAPIV*)();
        using COreAmountMgrInstance30_clbk = struct COreAmountMgr* (WINAPIV*)(COreAmountMgrInstance30_ptr);
        using COreAmountMgrIsINIFileChanged32_ptr = bool (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrIsINIFileChanged32_clbk = bool (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrIsINIFileChanged32_ptr);
        using COreAmountMgrIsOreRemain34_ptr = int (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrIsOreRemain34_clbk = int (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrIsOreRemain34_ptr);
        using COreAmountMgrLoadINI36_ptr = int (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrLoadINI36_clbk = int (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrLoadINI36_ptr);
        using COreAmountMgrLoop38_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrLoop38_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrLoop38_ptr);
        using COreAmountMgrLoopSubProcSendInform40_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrLoopSubProcSendInform40_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrLoopSubProcSendInform40_ptr);
        using COreAmountMgrReLoad42_ptr = bool (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrReLoad42_clbk = bool (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrReLoad42_ptr);
        using COreAmountMgrRelease44_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrRelease44_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrRelease44_ptr);
        using COreAmountMgrSetOreTransferAmount46_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrSetOreTransferAmount46_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrSetOreTransferAmount46_ptr);
        using COreAmountMgrUpdateDepositeRate48_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrUpdateDepositeRate48_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrUpdateDepositeRate48_ptr);
        using COreAmountMgrUpdateForce50_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrUpdateForce50_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrUpdateForce50_ptr);
        
        using COreAmountMgrdtor_COreAmountMgr55_ptr = void (WINAPIV*)(struct COreAmountMgr*);
        using COreAmountMgrdtor_COreAmountMgr55_clbk = void (WINAPIV*)(struct COreAmountMgr*, COreAmountMgrdtor_COreAmountMgr55_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

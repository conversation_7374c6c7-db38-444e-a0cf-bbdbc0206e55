// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$4CFA15F5CE489CDF0BD78DFB308FEF36.hpp>


START_ATF_NAMESPACE
    struct IMPORT_OBJECT_HEADER
    {
        unsigned __int16 Sig1;
        unsigned __int16 Sig2;
        unsigned __int16 Version;
        unsigned __int16 Machine;
        unsigned int TimeDateStamp;
        unsigned int SizeOfData;
        $4CFA15F5CE489CDF0BD78DFB308FEF36 ___u6;
        unsigned __int16 Type : 2;
        unsigned __int16 NameType : 3;
        unsigned __int16 Reserved : 11;
    };
END_ATF_NAMESPACE

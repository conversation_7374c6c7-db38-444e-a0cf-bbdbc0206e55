// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _paraformat
    {
        unsigned int cbSize;
        unsigned int dwMask;
        unsigned __int16 wNumbering;
        unsigned __int16 wEffects;
        int dxStartIndent;
        int dxRightIndent;
        int dxOffset;
        unsigned __int16 wAlignment;
        __int16 cTabCount;
        int rgxTabs[32];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderSortType
    {
        unsigned int m_dwID;
        char m_szTypeName[128];
        char m_szQuery[128];
    public:
        CUnmannedTraderSortType(unsigned int dwID);
        void ctor_CUnmannedTraderSortType(unsigned int dwID);
        unsigned int GetID();
        char* GetQuery();
        bool LoadXML(struct TiXmlElement* pkElemSortType, struct CLogFile* kLogger, unsigned int dwDivisionID);
        ~CUnmannedTraderSortType();
        void dtor_CUnmannedTraderSortType();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _PRINTER_NOTIFY_OPTIONS_TYPE
    {
        unsigned __int16 Type;
        unsigned __int16 Reserved0;
        unsigned int Reserved1;
        unsigned int Reserved2;
        unsigned int Count;
        unsigned __int16 *pFields;
    };
END_ATF_NAMESPACE

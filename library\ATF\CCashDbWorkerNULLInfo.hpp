// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerNULL.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerNULLctor_CCashDbWorkerNULL2_ptr = void (WINAPIV*)(struct CCashDbWorkerNULL*);
        using CCashDbWorkerNULLctor_CCashDbWorkerNULL2_clbk = void (WINAPIV*)(struct CCashDbWorkerNULL*, CCashDbWorkerNULLctor_CCashDbWorkerNULL2_ptr);
        using CCashDbWorkerNULLCompleteWork4_ptr = void (WINAPIV*)(struct CCashDbWorkerNULL*);
        using CCashDbWorkerNULLCompleteWork4_clbk = void (WINAPIV*)(struct CCashDbWorkerNULL*, CCashDbWorkerNULLCompleteWork4_ptr);
        using CCashDbWorkerNULLConvertErrorCode6_ptr = int (WINAPIV*)(struct CCashDbWorkerNULL*, char);
        using CCashDbWorkerNULLConvertErrorCode6_clbk = int (WINAPIV*)(struct CCashDbWorkerNULL*, char, CCashDbWorkerNULLConvertErrorCode6_ptr);
        using CCashDbWorkerNULLDoWork8_ptr = void (WINAPIV*)(struct CCashDbWorkerNULL*);
        using CCashDbWorkerNULLDoWork8_clbk = void (WINAPIV*)(struct CCashDbWorkerNULL*, CCashDbWorkerNULLDoWork8_ptr);
        using CCashDbWorkerNULLGetUseCashQueryStr10_ptr = void (WINAPIV*)(struct CCashDbWorkerNULL*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDbWorkerNULLGetUseCashQueryStr10_clbk = void (WINAPIV*)(struct CCashDbWorkerNULL*, struct _param_cash_update*, int, char*, uint64_t, CCashDbWorkerNULLGetUseCashQueryStr10_ptr);
        using CCashDbWorkerNULLInitialize12_ptr = bool (WINAPIV*)(struct CCashDbWorkerNULL*);
        using CCashDbWorkerNULLInitialize12_clbk = bool (WINAPIV*)(struct CCashDbWorkerNULL*, CCashDbWorkerNULLInitialize12_ptr);
        using CCashDbWorkerNULLRelease14_ptr = void (WINAPIV*)(struct CCashDbWorkerNULL*);
        using CCashDbWorkerNULLRelease14_clbk = void (WINAPIV*)(struct CCashDbWorkerNULL*, CCashDbWorkerNULLRelease14_ptr);
        using CCashDbWorkerNULL_all_rollback16_ptr = void (WINAPIV*)(struct CCashDbWorkerNULL*, struct _param_cash_update*);
        using CCashDbWorkerNULL_all_rollback16_clbk = void (WINAPIV*)(struct CCashDbWorkerNULL*, struct _param_cash_update*, CCashDbWorkerNULL_all_rollback16_ptr);
        using CCashDbWorkerNULL_init_database18_ptr = bool (WINAPIV*)(struct CCashDbWorkerNULL*);
        using CCashDbWorkerNULL_init_database18_clbk = bool (WINAPIV*)(struct CCashDbWorkerNULL*, CCashDbWorkerNULL_init_database18_ptr);
        using CCashDbWorkerNULL_wait_tsk_cash_buy_dblog20_ptr = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*);
        using CCashDbWorkerNULL_wait_tsk_cash_buy_dblog20_clbk = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*, CCashDbWorkerNULL_wait_tsk_cash_buy_dblog20_ptr);
        using CCashDbWorkerNULL_wait_tsk_cash_rollback22_ptr = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*);
        using CCashDbWorkerNULL_wait_tsk_cash_rollback22_clbk = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*, CCashDbWorkerNULL_wait_tsk_cash_rollback22_ptr);
        using CCashDbWorkerNULL_wait_tsk_cash_select24_ptr = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*);
        using CCashDbWorkerNULL_wait_tsk_cash_select24_clbk = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*, CCashDbWorkerNULL_wait_tsk_cash_select24_ptr);
        using CCashDbWorkerNULL_wait_tsk_cash_update26_ptr = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*);
        using CCashDbWorkerNULL_wait_tsk_cash_update26_clbk = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*, CCashDbWorkerNULL_wait_tsk_cash_update26_ptr);
        using CCashDbWorkerNULL_wait_tst_cash_total_selling_select28_ptr = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*);
        using CCashDbWorkerNULL_wait_tst_cash_total_selling_select28_clbk = int (WINAPIV*)(struct CCashDbWorkerNULL*, struct Task*, CCashDbWorkerNULL_wait_tst_cash_total_selling_select28_ptr);
        
        using CCashDbWorkerNULLdtor_CCashDbWorkerNULL33_ptr = void (WINAPIV*)(struct CCashDbWorkerNULL*);
        using CCashDbWorkerNULLdtor_CCashDbWorkerNULL33_clbk = void (WINAPIV*)(struct CCashDbWorkerNULL*, CCashDbWorkerNULLdtor_CCashDbWorkerNULL33_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

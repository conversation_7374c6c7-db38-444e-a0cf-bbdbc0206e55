// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTotalGuildRankRecord.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CTotalGuildRankRecordctor_CTotalGuildRankRecord2_ptr = void (WINAPIV*)(struct CTotalGuildRankRecord*);
        using CTotalGuildRankRecordctor_CTotalGuildRankRecord2_clbk = void (WINAPIV*)(struct CTotalGuildRankRecord*, CTotalGuildRankRecordctor_CTotalGuildRankRecord2_ptr);
        using CTotalGuildRankRecordClear4_ptr = void (WINAPIV*)(struct CTotalGuildRankRecord*);
        using CTotalGuildRankRecordClear4_clbk = void (WINAPIV*)(struct CTotalGuildRankRecord*, CTotalGuildRankRecordClear4_ptr);
        
        using CTotalGuildRankRecorddtor_CTotalGuildRankRecord8_ptr = void (WINAPIV*)(struct CTotalGuildRankRecord*);
        using CTotalGuildRankRecorddtor_CTotalGuildRankRecord8_clbk = void (WINAPIV*)(struct CTotalGuildRankRecord*, CTotalGuildRankRecorddtor_CTotalGuildRankRecord8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

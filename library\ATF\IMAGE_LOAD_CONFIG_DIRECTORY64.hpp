// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct IMAGE_LOAD_CONFIG_DIRECTORY64
    {
        unsigned int Size;
        unsigned int TimeDateStamp;
        unsigned __int16 MajorVersion;
        unsigned __int16 MinorVersion;
        unsigned int GlobalFlagsClear;
        unsigned int GlobalFlagsSet;
        unsigned int CriticalSectionDefaultTimeout;
        unsigned __int64 DeCommitFreeBlockThreshold;
        unsigned __int64 DeCommitTotalFreeThreshold;
        unsigned __int64 LockPrefixTable;
        unsigned __int64 MaximumAllocationSize;
        unsigned __int64 VirtualMemoryThreshold;
        unsigned __int64 ProcessAffinityMask;
        unsigned int ProcessHeapFlags;
        unsigned __int16 CSDVersion;
        unsigned __int16 Reserved1;
        unsigned __int64 EditList;
        unsigned __int64 SecurityCookie;
        unsigned __int64 SEHandlerTable;
        unsigned __int64 SEHandlerCount;
    };
END_ATF_NAMESPACE

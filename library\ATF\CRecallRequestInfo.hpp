// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRecallRequest.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRecallRequestctor_CRecallRequest2_ptr = void (WINAPIV*)(struct CRecallRequest*, uint16_t);
        using CRecallRequestctor_CRecallRequest2_clbk = void (WINAPIV*)(struct CRecallRequest*, uint16_t, CRecallRequestctor_CRecallRequest2_ptr);
        using CRecallRequestClear4_ptr = void (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestClear4_clbk = void (WINAPIV*)(struct CRecallRequest*, CRecallRequestClear4_ptr);
        using CRecallRequestClose6_ptr = void (WINAPIV*)(struct CRecallRequest*, bool);
        using CRecallRequestClose6_clbk = void (WINAPIV*)(struct CRecallRequest*, bool, CRecallRequestClose6_ptr);
        using CRecallRequestGetID8_ptr = uint16_t (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestGetID8_clbk = uint16_t (WINAPIV*)(struct CRecallRequest*, CRecallRequestGetID8_ptr);
        using CRecallRequestGetOwner10_ptr = struct CPlayer* (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestGetOwner10_clbk = struct CPlayer* (WINAPIV*)(struct CRecallRequest*, CRecallRequestGetOwner10_ptr);
        using CRecallRequestIsBattleModeUse12_ptr = bool (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestIsBattleModeUse12_clbk = bool (WINAPIV*)(struct CRecallRequest*, CRecallRequestIsBattleModeUse12_ptr);
        using CRecallRequestIsClose14_ptr = bool (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestIsClose14_clbk = bool (WINAPIV*)(struct CRecallRequest*, CRecallRequestIsClose14_ptr);
        using CRecallRequestIsRecallAfterStoneState16_ptr = bool (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestIsRecallAfterStoneState16_clbk = bool (WINAPIV*)(struct CRecallRequest*, CRecallRequestIsRecallAfterStoneState16_ptr);
        using CRecallRequestIsRecallParty18_ptr = bool (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestIsRecallParty18_clbk = bool (WINAPIV*)(struct CRecallRequest*, CRecallRequestIsRecallParty18_ptr);
        using CRecallRequestIsTimeOut20_ptr = bool (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestIsTimeOut20_clbk = bool (WINAPIV*)(struct CRecallRequest*, CRecallRequestIsTimeOut20_ptr);
        using CRecallRequestIsWait22_ptr = bool (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestIsWait22_clbk = bool (WINAPIV*)(struct CRecallRequest*, CRecallRequestIsWait22_ptr);
        using CRecallRequestRecall24_ptr = char (WINAPIV*)(struct CRecallRequest*, struct CPlayer*, bool);
        using CRecallRequestRecall24_clbk = char (WINAPIV*)(struct CRecallRequest*, struct CPlayer*, bool, CRecallRequestRecall24_ptr);
        using CRecallRequestRegist26_ptr = char (WINAPIV*)(struct CRecallRequest*, struct CPlayer*, struct CCharacter*, bool, bool, bool);
        using CRecallRequestRegist26_clbk = char (WINAPIV*)(struct CRecallRequest*, struct CPlayer*, struct CCharacter*, bool, bool, bool, CRecallRequestRegist26_ptr);
        
        using CRecallRequestdtor_CRecallRequest30_ptr = void (WINAPIV*)(struct CRecallRequest*);
        using CRecallRequestdtor_CRecallRequest30_clbk = void (WINAPIV*)(struct CRecallRequest*, CRecallRequestdtor_CRecallRequest30_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct GSIVtbl
    {
        unsigned int (WINAPIV *QueryInterfaceVersion)(GSI *_this);
        BYTE gap8[8];
        char *(WINAPIV *NextSym)(GSI *_this, char *);
        char *(WINAPIV *HashSym)(GSI *_this, const char *, char *);
        char *(WINAPIV *NearestSym)(GSI *_this, unsigned __int16, int, int *);
        int (WINAPIV *Close)(GSI *_this);
        int (WINAPIV *getEnumThunk)(GSI *_this, unsigned __int16, int, EnumThunk **);
        unsigned int (WINAPIV *OffForSym)(GSI *_this, char *);
        char *(WINAPIV *SymForOff)(GSI *_this, unsigned int);
        char *(WINAPIV *HashSymW)(GSI *_this, const wchar_t *, char *);
        int (WINAPIV *getEnumByAddr)(GSI *_this, EnumSyms **);
    };
END_ATF_NAMESPACE

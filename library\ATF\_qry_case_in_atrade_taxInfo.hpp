// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_in_atrade_tax.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_in_atrade_taxctor__qry_case_in_atrade_tax2_ptr = void (WINAPIV*)(struct _qry_case_in_atrade_tax*);
        using _qry_case_in_atrade_taxctor__qry_case_in_atrade_tax2_clbk = void (WINAPIV*)(struct _qry_case_in_atrade_tax*, _qry_case_in_atrade_taxctor__qry_case_in_atrade_tax2_ptr);
        using _qry_case_in_atrade_taxsize4_ptr = int (WINAPIV*)(struct _qry_case_in_atrade_tax*);
        using _qry_case_in_atrade_taxsize4_clbk = int (WINAPIV*)(struct _qry_case_in_atrade_tax*, _qry_case_in_atrade_taxsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

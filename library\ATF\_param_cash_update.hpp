// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS_INDIV.hpp>
#include <_param_cash.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _param_cash_update : _param_cash
    {
        struct __item
        {
            char byRet;
            char in_strItemCode[8];
            char in_byOverlapNum;
            char in_byTblCode;
            unsigned __int16 in_wItemIdx;
            unsigned __int16 in_nDiscount;
            int in_nPrice;
            char in_nLendType;
            unsigned int in_dwLendTime;
            char in_nEventType;
            bool in_bIsApplyCoupon;
            unsigned __int64 in_lnUID;
            char out_cState;
            int out_nCashAmount;
            int out_nStdPrice;
            int out_nBuyPrice;
            unsigned __int16 out_wItemSerial;
            unsigned int out_dwT;
        public:
            __item();
            void ctor___item();
        };
        char in_szAcc[13];
        char in_szSvrName[33];
        char in_szAvatorName[17];
        unsigned int in_nCashAmount;
        char in_nNum10;
        char in_bySetKind;
        char in_nCouponCnt;
        _STORAGE_POS_INDIV in_CouponItem[3];
        int out_nCashAmount;
        unsigned int in_dwIP;
        unsigned __int16 out_bReturn;
        __item in_item[20];
    public:
        _param_cash_update(unsigned int dwAc, unsigned int dwAv, uint16_t wSock);
        void ctor__param_cash_update(unsigned int dwAc, unsigned int dwAv, uint16_t wSock);
        int size();
        ~_param_cash_update();
        void dtor__param_cash_update();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_param_cash_update, 1392>(), "_param_cash_update");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingVtbl.hpp>
#include <CUserDB.hpp>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CBilling
    {
        CBillingVtbl *vfptr;
        bool m_bOper;
    public:
        void Alive(struct CUserDB* pUserDB);
        void BillingClose(char* szID);
        CBilling();
        void ctor_CBilling();
        void Change_BillingType(char* szID, char* szCMSCode, int16_t iType, int lRemainTime, struct _SYSTEMTIME* pstEndDate, char byReason);
        void Change_Primium(char* szID, bool bResult);
        void Expire_IPOverflow(char* szID);
        void Expire_PCBang(char* szCMS);
        void Expire_Personal(char* szID);
        void Login(struct CUserDB* pUserDB);
        void Logout(struct CUserDB* pUserDB);
        void Remaintime_PCBang(char* szCMSCode, int16_t iType, int lRemaintime, struct _SYSTEMTIME* pstEndDate);
        void Remaintime_Personal(char* szID, int16_t iType, int lRemaintime, struct _SYSTEMTIME* pstEndDate);
        void SendMsg_CurAllUserLogin();
        bool SendMsg_Login(char* szID, char* szIP, char* szCMS, int16_t iType, struct _SYSTEMTIME* pstEndDate, int lRemainTime);
        void SendMsg_StartBilling();
        void SendMsg_ZoneAliveCheck(unsigned int dwData);
        void SetOper(bool bOper);
        void Start();
        ~CBilling();
        void dtor_CBilling();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CBilling, 16>(), "CBilling");
END_ATF_NAMESPACE

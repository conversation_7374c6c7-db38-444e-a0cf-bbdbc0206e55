// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$DF940BDB6441FA477D8F82E238A7A67C.hpp>
#include <CDBVariantVtbl.hpp>


START_ATF_NAMESPACE
    struct CDBVariant
    {
        CDBVariantVtbl *vfptr;
        unsigned int m_dwType;
        $DF940BDB6441FA477D8F82E238A7A67C ___u2;
    };
END_ATF_NAMESPACE

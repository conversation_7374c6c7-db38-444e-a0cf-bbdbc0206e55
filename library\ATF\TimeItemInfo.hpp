// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <TimeItem.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using TimeItemCheckGoods2_ptr = bool (WINAPIV*)(struct TimeItem*);
        using TimeItemCheckGoods2_clbk = bool (WINAPIV*)(struct TimeItem*, TimeItemCheckGoods2_ptr);
        using TimeItemFindTimeRec4_ptr = struct _TimeItem_fld* (WINAPIV*)(int, int);
        using TimeItemFindTimeRec4_clbk = struct _TimeItem_fld* (WINAPIV*)(int, int, TimeItemFindTimeRec4_ptr);
        using TimeItemInit6_ptr = bool (WINAPIV*)(struct TimeItem*);
        using TimeItemInit6_clbk = bool (WINAPIV*)(struct TimeItem*, TimeItemInit6_ptr);
        using TimeItemInstance8_ptr = struct TimeItem* (WINAPIV*)();
        using TimeItemInstance8_clbk = struct TimeItem* (WINAPIV*)(TimeItemInstance8_ptr);
        using TimeItemMakeLinkTable10_ptr = bool (WINAPIV*)(struct TimeItem*, char*, int);
        using TimeItemMakeLinkTable10_clbk = bool (WINAPIV*)(struct TimeItem*, char*, int, TimeItemMakeLinkTable10_ptr);
        using TimeItemReadGoods12_ptr = bool (WINAPIV*)(struct TimeItem*);
        using TimeItemReadGoods12_clbk = bool (WINAPIV*)(struct TimeItem*, TimeItemReadGoods12_ptr);
        
        using TimeItemctor_TimeItem14_ptr = void (WINAPIV*)(struct TimeItem*);
        using TimeItemctor_TimeItem14_clbk = void (WINAPIV*)(struct TimeItem*, TimeItemctor_TimeItem14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

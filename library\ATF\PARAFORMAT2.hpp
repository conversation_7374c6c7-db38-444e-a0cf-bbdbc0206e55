// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_paraformat.hpp>


START_ATF_NAMESPACE
    struct  PARAFORMAT2 : _paraformat
    {
        int dySpaceBefore;
        int dySpaceAfter;
        int dyLineSpacing;
        __int16 sStyle;
        char bLineSpacingRule;
        char bOutlineLevel;
        unsigned __int16 wShadingWeight;
        unsigned __int16 wShadingStyle;
        unsigned __int16 wNumberingStart;
        unsigned __int16 wNumberingStyle;
        unsigned __int16 wNumberingTab;
        unsigned __int16 wBorderSpace;
        unsigned __int16 wBorderWidth;
        unsigned __int16 wBorders;
    };
END_ATF_NAMESPACE

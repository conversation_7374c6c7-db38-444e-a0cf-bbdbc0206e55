// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataJP.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataJPctor_CNationSettingDataJP2_ptr = void (WINAPIV*)(struct CNationSettingDataJP*);
        using CNationSettingDataJPctor_CNationSettingDataJP2_clbk = void (WINAPIV*)(struct CNationSettingDataJP*, CNationSettingDataJPctor_CNationSettingDataJP2_ptr);
        using CNationSettingDataJPCreateBilling4_ptr = struct CBilling* (WINAPIV*)(struct CNationSettingDataJP*);
        using CNationSettingDataJPCreateBilling4_clbk = struct CBilling* (WINAPIV*)(struct CNationSettingDataJP*, CNationSettingDataJPCreateBilling4_ptr);
        using CNationSettingDataJPCreateWorker6_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataJP*);
        using CNationSettingDataJPCreateWorker6_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataJP*, CNationSettingDataJPCreateWorker6_ptr);
        using CNationSettingDataJPGetCashItemPrice8_ptr = int (WINAPIV*)(struct CNationSettingDataJP*, struct _CashShop_str_fld*);
        using CNationSettingDataJPGetCashItemPrice8_clbk = int (WINAPIV*)(struct CNationSettingDataJP*, struct _CashShop_str_fld*, CNationSettingDataJPGetCashItemPrice8_ptr);
        using CNationSettingDataJPGetItemName10_ptr = char* (WINAPIV*)(struct CNationSettingDataJP*, struct _NameTxt_fld*);
        using CNationSettingDataJPGetItemName10_clbk = char* (WINAPIV*)(struct CNationSettingDataJP*, struct _NameTxt_fld*, CNationSettingDataJPGetItemName10_ptr);
        using CNationSettingDataJPInit12_ptr = int (WINAPIV*)(struct CNationSettingDataJP*);
        using CNationSettingDataJPInit12_clbk = int (WINAPIV*)(struct CNationSettingDataJP*, CNationSettingDataJPInit12_ptr);
        using CNationSettingDataJPLoop14_ptr = void (WINAPIV*)(struct CNationSettingDataJP*);
        using CNationSettingDataJPLoop14_clbk = void (WINAPIV*)(struct CNationSettingDataJP*, CNationSettingDataJPLoop14_ptr);
        using CNationSettingDataJPReadSystemPass16_ptr = bool (WINAPIV*)(struct CNationSettingDataJP*);
        using CNationSettingDataJPReadSystemPass16_clbk = bool (WINAPIV*)(struct CNationSettingDataJP*, CNationSettingDataJPReadSystemPass16_ptr);
        using CNationSettingDataJPSetUnitPassiveValue18_ptr = void (WINAPIV*)(struct CNationSettingDataJP*, float*);
        using CNationSettingDataJPSetUnitPassiveValue18_clbk = void (WINAPIV*)(struct CNationSettingDataJP*, float*, CNationSettingDataJPSetUnitPassiveValue18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

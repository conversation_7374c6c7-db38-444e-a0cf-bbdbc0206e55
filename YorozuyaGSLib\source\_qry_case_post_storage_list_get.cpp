#include <_qry_case_post_storage_list_get.hpp>


START_ATF_NAMESPACE
    _qry_case_post_storage_list_get::_qry_case_post_storage_list_get()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_post_storage_list_get*);
        (org_ptr(0x1400ca650L))(this);
    };
    void _qry_case_post_storage_list_get::ctor__qry_case_post_storage_list_get()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_post_storage_list_get*);
        (org_ptr(0x1400ca650L))(this);
    };
    int _qry_case_post_storage_list_get::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_post_storage_list_get*);
        return (org_ptr(0x1400ca6d0L))(this);
    };
    _qry_case_post_storage_list_get::__list::__list()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_post_storage_list_get::__list*);
        (org_ptr(0x1400ca6e0L))(this);
    };
    void _qry_case_post_storage_list_get::__list::ctor___list()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_post_storage_list_get::__list*);
        (org_ptr(0x1400ca6e0L))(this);
    };
END_ATF_NAMESPACE

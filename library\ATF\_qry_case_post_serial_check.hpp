// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_post_serial_check
    {
        struct __list
        {
            unsigned int dwIndex;
            char bySenderDgr;
            char bySenderRace;
            bool bCheckDgr;
            char wszRecvName[17];
            unsigned int dwReceiverSerial;
            char byErr;
        };
        unsigned int dwCount;
        __list List[15];
    public:
        _qry_case_post_serial_check();
        void ctor__qry_case_post_serial_check();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_post_serial_check, 484>(), "_qry_case_post_serial_check");
END_ATF_NAMESPACE

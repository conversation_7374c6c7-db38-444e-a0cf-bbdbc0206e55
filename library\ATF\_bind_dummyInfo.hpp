// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_bind_dummy.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _bind_dummySetDummy2_ptr = bool (WINAPIV*)(struct _bind_dummy*, struct _dummy_position*);
        using _bind_dummySetDummy2_clbk = bool (WINAPIV*)(struct _bind_dummy*, struct _dummy_position*, _bind_dummySetDummy2_ptr);
        
        using _bind_dummyctor__bind_dummy4_ptr = void (WINAPIV*)(struct _bind_dummy*);
        using _bind_dummyctor__bind_dummy4_clbk = void (WINAPIV*)(struct _bind_dummy*, _bind_dummyctor__bind_dummy4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTerm.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CTermctor_CTerm2_ptr = void (WINAPIV*)(struct CTerm*);
        using CTermctor_CTerm2_clbk = void (WINAPIV*)(struct CTerm*, CTermctor_CTerm2_ptr);
        using CTermGetTerm4_ptr = unsigned int (WINAPIV*)(struct CTerm*);
        using CTermGetTerm4_clbk = unsigned int (WINAPIV*)(struct CTerm*, CTermGetTerm4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

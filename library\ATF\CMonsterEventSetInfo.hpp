// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonsterEventSet.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMonsterEventSetctor_CMonsterEventSet2_ptr = void (WINAPIV*)(struct CMonsterEventSet*);
        using CMonsterEventSetctor_CMonsterEventSet2_clbk = void (WINAPIV*)(struct CMonsterEventSet*, CMonsterEventSetctor_CMonsterEventSet2_ptr);
        using CMonsterEventSetCheckEventSetRespawn4_ptr = void (WINAPIV*)(struct CMonsterEventSet*);
        using CMonsterEventSetCheckEventSetRespawn4_clbk = void (WINAPIV*)(struct CMonsterEventSet*, CMonsterEventSetCheckEventSetRespawn4_ptr);
        using CMonsterEventSetGetEmptyEventSet6_ptr = struct _event_set* (WINAPIV*)(struct CMonsterEventSet*);
        using CMonsterEventSetGetEmptyEventSet6_clbk = struct _event_set* (WINAPIV*)(struct CMonsterEventSet*, CMonsterEventSetGetEmptyEventSet6_ptr);
        using CMonsterEventSetGetEvenSetLooting8_ptr = struct _event_set_looting* (WINAPIV*)(struct CMonsterEventSet*, char*);
        using CMonsterEventSetGetEvenSetLooting8_clbk = struct _event_set_looting* (WINAPIV*)(struct CMonsterEventSet*, char*, CMonsterEventSetGetEvenSetLooting8_ptr);
        using CMonsterEventSetGetMonsterSet10_ptr = struct _event_set::_monster_set* (WINAPIV*)(struct CMonsterEventSet*, struct _event_set*);
        using CMonsterEventSetGetMonsterSet10_clbk = struct _event_set::_monster_set* (WINAPIV*)(struct CMonsterEventSet*, struct _event_set*, CMonsterEventSetGetMonsterSet10_ptr);
        using CMonsterEventSetIsINIFileChanged12_ptr = bool (WINAPIV*)(struct CMonsterEventSet*, char*, struct _FILETIME);
        using CMonsterEventSetIsINIFileChanged12_clbk = bool (WINAPIV*)(struct CMonsterEventSet*, char*, struct _FILETIME, CMonsterEventSetIsINIFileChanged12_ptr);
        using CMonsterEventSetLoadEventSet14_ptr = bool (WINAPIV*)(struct CMonsterEventSet*, char*);
        using CMonsterEventSetLoadEventSet14_clbk = bool (WINAPIV*)(struct CMonsterEventSet*, char*, CMonsterEventSetLoadEventSet14_ptr);
        using CMonsterEventSetLoadEventSetLooting16_ptr = bool (WINAPIV*)(struct CMonsterEventSet*);
        using CMonsterEventSetLoadEventSetLooting16_clbk = bool (WINAPIV*)(struct CMonsterEventSet*, CMonsterEventSetLoadEventSetLooting16_ptr);
        using CMonsterEventSetStartEventSet18_ptr = bool (WINAPIV*)(struct CMonsterEventSet*, char*, char*, struct CPlayer*);
        using CMonsterEventSetStartEventSet18_clbk = bool (WINAPIV*)(struct CMonsterEventSet*, char*, char*, struct CPlayer*, CMonsterEventSetStartEventSet18_ptr);
        using CMonsterEventSetStopEventSet20_ptr = bool (WINAPIV*)(struct CMonsterEventSet*, char*, char*);
        using CMonsterEventSetStopEventSet20_clbk = bool (WINAPIV*)(struct CMonsterEventSet*, char*, char*, CMonsterEventSetStopEventSet20_ptr);
        
        using CMonsterEventSetdtor_CMonsterEventSet25_ptr = void (WINAPIV*)(struct CMonsterEventSet*);
        using CMonsterEventSetdtor_CMonsterEventSet25_clbk = void (WINAPIV*)(struct CMonsterEventSet*, CMonsterEventSetdtor_CMonsterEventSet25_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

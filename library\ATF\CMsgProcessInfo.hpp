// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMsgProcess.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMsgProcessctor_CMsgProcess2_ptr = void (WINAPIV*)(struct CMsgProcess*, int);
        using CMsgProcessctor_CMsgProcess2_clbk = void (WINAPIV*)(struct CMsgProcess*, int, CMsgProcessctor_CMsgProcess2_ptr);
        
        using CMsgProcessctor_CMsgProcess4_ptr = void (WINAPIV*)(struct CMsgProcess*);
        using CMsgProcessctor_CMsgProcess4_clbk = void (WINAPIV*)(struct CMsgProcess*, CMsgProcessctor_CMsgProcess4_ptr);
        using CMsgProcessProcessMessage6_ptr = void (WINAPIV*)(struct CMsgProcess*, struct _message*);
        using CMsgProcessProcessMessage6_clbk = void (WINAPIV*)(struct CMsgProcess*, struct _message*, CMsgProcessProcessMessage6_ptr);
        
        using CMsgProcessdtor_CMsgProcess11_ptr = void (WINAPIV*)(struct CMsgProcess*);
        using CMsgProcessdtor_CMsgProcess11_clbk = void (WINAPIV*)(struct CMsgProcess*, CMsgProcessdtor_CMsgProcess11_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaScript.hpp>


START_ATF_NAMESPACE
    struct CLuaEventNode
    {
        struct _State
        {
            bool m_bExist;
            bool m_bAttached;
        public:
            _State();
            void ctor__State();
        };
        char m_strName[64];
        bool m_bCallFunc;
        char m_strCallFuncName[64];
        unsigned int m_dwTimeTerm;
        unsigned int m_dwNextLoopTime;
        CLuaScript *m_pLuaScript;
    public:
        CLuaEventNode();
        void ctor_CLuaEventNode();
        char* GetCallFunName();
        char* GetName();
        unsigned int GetNextLoopTime();
        struct CLuaScript* GetScript();
        unsigned int GetTimeTerm();
        void Init();
        bool IsCallFun();
        void SetCallFun(char* strCallFunName, unsigned int dwTime);
        void SetName(char* strName);
        void SetNextLoopTime(unsigned int dwNextLoopTime);
        void SetScript(struct CLuaScript* pParentScript);
        ~CLuaEventNode();
        void dtor_CLuaEventNode();
    };
END_ATF_NAMESPACE

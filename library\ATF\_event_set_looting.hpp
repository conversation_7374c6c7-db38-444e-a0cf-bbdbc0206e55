// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _event_set_looting
    {
        enum LOOT_AUTH
        {
            AUTH_SAME_PARTY = 0x0,
            AUTH_SAME_RACE = 0x1,
            AUTH_SAME_GUILD = 0x2,
            AUTH_ALL_PLAYER = 0x3,
            AUTH_PATRIARCH = 0x4,
            AUTH_GUILD_MASTER = 0x5,
        };
        struct _event_item
        {
            char strCode[64];
            unsigned __int16 wDropCount;
            unsigned __int16 wDuration;
            char byProb;
        };
        char strCode[64];
        unsigned __int16 wMagnifications;
        unsigned __int16 wRange;
        int bWithHolyScanner;
        char byLootAuth;
        _event_item stEventItemList[50];
        int nItemCount;
    public:
        _event_set_looting();
        void ctor__event_set_looting();
        void init();
    };
END_ATF_NAMESPACE

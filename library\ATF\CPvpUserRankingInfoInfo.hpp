// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPvpUserRankingInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CPvpUserRankingInfoctor_CPvpUserRankingInfo2_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*);
        using CPvpUserRankingInfoctor_CPvpUserRankingInfo2_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, CPvpUserRankingInfoctor_CPvpUserRankingInfo2_ptr);
        using CPvpUserRankingInfoClearTomorrowPvpRankData4_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*);
        using CPvpUserRankingInfoClearTomorrowPvpRankData4_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, CPvpUserRankingInfoClearTomorrowPvpRankData4_ptr);
        using CPvpUserRankingInfoDoDayChangedWork6_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*, struct CLogFile*);
        using CPvpUserRankingInfoDoDayChangedWork6_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, struct CLogFile*, CPvpUserRankingInfoDoDayChangedWork6_ptr);
        using CPvpUserRankingInfoFindRank8_ptr = unsigned int (WINAPIV*)(struct CPvpUserRankingInfo*, char, unsigned int);
        using CPvpUserRankingInfoFindRank8_clbk = unsigned int (WINAPIV*)(struct CPvpUserRankingInfo*, char, unsigned int, CPvpUserRankingInfoFindRank8_ptr);
        using CPvpUserRankingInfoFlipPvPRankTop10_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*);
        using CPvpUserRankingInfoFlipPvPRankTop10_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, CPvpUserRankingInfoFlipPvPRankTop10_ptr);
        using CPvpUserRankingInfoGetBossType12_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char, unsigned int);
        using CPvpUserRankingInfoGetBossType12_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char, unsigned int, CPvpUserRankingInfoGetBossType12_ptr);
        using CPvpUserRankingInfoGetCurrentPvpRankData14_ptr = struct _PVP_RANK_DATA* (WINAPIV*)(struct CPvpUserRankingInfo*, char, char);
        using CPvpUserRankingInfoGetCurrentPvpRankData14_clbk = struct _PVP_RANK_DATA* (WINAPIV*)(struct CPvpUserRankingInfo*, char, char, CPvpUserRankingInfoGetCurrentPvpRankData14_ptr);
        using CPvpUserRankingInfoGetCurrentRaceBossSerial16_ptr = unsigned int (WINAPIV*)(struct CPvpUserRankingInfo*, char, char);
        using CPvpUserRankingInfoGetCurrentRaceBossSerial16_clbk = unsigned int (WINAPIV*)(struct CPvpUserRankingInfo*, char, char, CPvpUserRankingInfoGetCurrentRaceBossSerial16_ptr);
        using CPvpUserRankingInfoGetPvpRankDataVersion18_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*);
        using CPvpUserRankingInfoGetPvpRankDataVersion18_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, CPvpUserRankingInfoGetPvpRankDataVersion18_ptr);
        using CPvpUserRankingInfoGetRankPackedData20_ptr = struct _PVP_RANK_PACKED_DATA* (WINAPIV*)(struct CPvpUserRankingInfo*, char, char);
        using CPvpUserRankingInfoGetRankPackedData20_clbk = struct _PVP_RANK_PACKED_DATA* (WINAPIV*)(struct CPvpUserRankingInfo*, char, char, CPvpUserRankingInfoGetRankPackedData20_ptr);
        using CPvpUserRankingInfoGetTomorrowPvpRankData22_ptr = struct _PVP_RANK_DATA* (WINAPIV*)(struct CPvpUserRankingInfo*, char, char);
        using CPvpUserRankingInfoGetTomorrowPvpRankData22_clbk = struct _PVP_RANK_DATA* (WINAPIV*)(struct CPvpUserRankingInfo*, char, char, CPvpUserRankingInfoGetTomorrowPvpRankData22_ptr);
        using CPvpUserRankingInfoIncreaseVesion24_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*);
        using CPvpUserRankingInfoIncreaseVesion24_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, CPvpUserRankingInfoIncreaseVesion24_ptr);
        using CPvpUserRankingInfoIsCurrentRaceBossGroup26_ptr = bool (WINAPIV*)(struct CPvpUserRankingInfo*, char, unsigned int);
        using CPvpUserRankingInfoIsCurrentRaceBossGroup26_clbk = bool (WINAPIV*)(struct CPvpUserRankingInfo*, char, unsigned int, CPvpUserRankingInfoIsCurrentRaceBossGroup26_ptr);
        using CPvpUserRankingInfoIsRaceViceBoss28_ptr = bool (WINAPIV*)(struct CPvpUserRankingInfo*, char, unsigned int);
        using CPvpUserRankingInfoIsRaceViceBoss28_clbk = bool (WINAPIV*)(struct CPvpUserRankingInfo*, char, unsigned int, CPvpUserRankingInfoIsRaceViceBoss28_ptr);
        using CPvpUserRankingInfoLoadPvpRank30_ptr = bool (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoLoadPvpRank30_clbk = bool (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoLoadPvpRank30_ptr);
        using CPvpUserRankingInfoPvpRankDataPacking32_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*, struct CLogFile*);
        using CPvpUserRankingInfoPvpRankDataPacking32_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, struct CLogFile*, CPvpUserRankingInfoPvpRankDataPacking32_ptr);
        using CPvpUserRankingInfoPvpRankListRequest34_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*, uint16_t, char, char, char);
        using CPvpUserRankingInfoPvpRankListRequest34_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, uint16_t, char, char, char, CPvpUserRankingInfoPvpRankListRequest34_ptr);
        using CPvpUserRankingInfoSendMsg_PvpRankListData36_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*, uint16_t, char, char, char);
        using CPvpUserRankingInfoSendMsg_PvpRankListData36_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, uint16_t, char, char, char, CPvpUserRankingInfoSendMsg_PvpRankListData36_ptr);
        using CPvpUserRankingInfoSendMsg_PvpRankListNodata38_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*, uint16_t, char, char, char);
        using CPvpUserRankingInfoSendMsg_PvpRankListNodata38_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, uint16_t, char, char, char, CPvpUserRankingInfoSendMsg_PvpRankListNodata38_ptr);
        using CPvpUserRankingInfoSetCurrentRaceBossSerial40_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*, char, char, unsigned int);
        using CPvpUserRankingInfoSetCurrentRaceBossSerial40_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, char, char, unsigned int, CPvpUserRankingInfoSetCurrentRaceBossSerial40_ptr);
        using CPvpUserRankingInfoSetUpdateRaceBossSerial42_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*, char, char, unsigned int);
        using CPvpUserRankingInfoSetUpdateRaceBossSerial42_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, char, char, unsigned int, CPvpUserRankingInfoSetUpdateRaceBossSerial42_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep144_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep144_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep144_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep1046_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep1046_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep1046_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep248_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep248_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep248_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep350_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep350_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep350_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep452_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep452_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep452_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep554_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep554_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep554_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep656_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep656_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep656_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep758_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep758_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep758_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep860_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep860_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep860_ptr);
        using CPvpUserRankingInfoUpdateRaceRankStep962_ptr = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*);
        using CPvpUserRankingInfoUpdateRaceRankStep962_clbk = char (WINAPIV*)(struct CPvpUserRankingInfo*, char*, CPvpUserRankingInfoUpdateRaceRankStep962_ptr);
        using CPvpUserRankingInfoassign64_ptr = bool (WINAPIV*)(struct CPvpUserRankingInfo*);
        using CPvpUserRankingInfoassign64_clbk = bool (WINAPIV*)(struct CPvpUserRankingInfo*, CPvpUserRankingInfoassign64_ptr);
        
        using CPvpUserRankingInfodtor_CPvpUserRankingInfo66_ptr = void (WINAPIV*)(struct CPvpUserRankingInfo*);
        using CPvpUserRankingInfodtor_CPvpUserRankingInfo66_clbk = void (WINAPIV*)(struct CPvpUserRankingInfo*, CPvpUserRankingInfodtor_CPvpUserRankingInfo66_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

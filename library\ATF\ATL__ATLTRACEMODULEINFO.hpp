// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__ATLTRACESETTINGS.hpp>



START_ATF_NAMESPACE
    namespace ATL
    {
        #pragma pack(push, 8)
        struct ATLTRACEMODULEINFO
        {
            wchar_t szName[64];
            wchar_t szPath[260];
            ATLTRACESETTINGS settings;
            unsigned __int64 dwModule;
            int nCategories;
        };
        #pragma pack(pop)
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CBillingVtbl
    {
        void *(WINAPIV *__vecDelDtor)(struct CBilling *_this, unsigned int);
        void (WINAPIV *Login)(struct CBilling *_this, struct CUserDB *);
        void (WINAPIV *Alive)(struct CBilling *_this, struct CUserDB *);
        void (WINAPIV *Logout)(struct CBilling *_this, struct CUserDB *);
        void (WINAPIV *BillingClose)(struct CBilling *_this, char *);
        bool (WINAPIV *SendMsg_Login)(struct CBilling *_this, char *, char *, char *, __int16, struct _SYSTEMTIME *, int);
    };
END_ATF_NAMESPACE

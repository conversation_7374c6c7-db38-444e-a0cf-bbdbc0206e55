// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPostData.hpp>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct CPostStorage
    {
        CPostData m_PostData[50];
        int m_nSize;
        bool m_bUpdate;
    public:
        int AddNewPost(unsigned int dwSenderSerial, char* wszSendName, char* wszRecvName, char* wszTitle, char* wszContent, struct _INVENKEY Key, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, unsigned int dwPSSerial, char byState, int* nNumber, uint64_t lnUID);
        int AddPostTitleData(int nIndex, unsigned int dwSerial, char byState, char* wszSendName, char* wszTitle, struct _INVENKEY Key, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, uint64_t lnUID);
        bool AddPostTitleDataByStorageIndex(int nStorageIndex, int nNumber, unsigned int dwSerial, char byState, char* wszSendName, char* wszTitle, struct _INVENKEY Key, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, uint64_t lnUID);
        CPostStorage();
        void ctor_CPostStorage();
        void DelPostData(unsigned int dwIndex);
        struct CPostData* GetPostDataFromInx(int nIndex);
        struct CPostData* GetPostDataFromSerial(unsigned int dwPostSerial);
        int GetSize();
        void Init();
        bool IsContentLoad(unsigned int dwIndex);
        int SetPostContent(unsigned int dwSerial, char* wszContent);
        ~CPostStorage();
        void dtor_CPostStorage();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CPostStorage, 15608>(), "CPostStorage");
END_ATF_NAMESPACE

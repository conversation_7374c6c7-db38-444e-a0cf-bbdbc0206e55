// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_happen_event_cont.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CQuestMgr
    {
        struct CPlayer *m_pMaster;
        struct _QUEST_DB_BASE *m_pQuestData;
        _happen_event_cont m_LastHappenEvent;
        struct _happen_event_cont *m_pTempHappenEvent;
        unsigned int m_dwOldTimeoutChecktime;
    public:
        CQuestMgr();
        void ctor_CQuestMgr();
        static bool CalcStartNPCQuestCnt(unsigned int* pdwCnt);
        bool CanGiveupQuest(char byQuestDBSlot);
        bool CheckFailCondition(char byQuestDBSlot, int nFailCond, char* pszCode);
        void CheckFailLoop(int nFailCond, char* pszCode);
        struct _quest_fail_result* CheckLimLv(int nNewLv);
        void CheckNPCQuestList(char* pszEventCode, char byRaceCode, struct _NPCQuestIndexTempData* pQuestIndexData);
        struct _happen_event_cont* CheckNPCQuestStartable(char* pszEventCode, char byRaceCode, unsigned int dwQuestIndex, unsigned int dwHappenIndex);
        struct _happen_event_cont* CheckQuestHappenEvent(QUEST_HAPPEN HappenType, char* pszEventCode, char byRaceCode);
        struct _quest_check_result* CheckReqAct(int nActCode, char* pszReqCode, uint16_t wActCount, bool bPartyState);
        static bool CheckRewardMasteryData(int iInx, int iNth, struct _quest_reward_mastery* pData);
        void DeleteQuestData(char bySlot);
        bool DeleteQuestItem(char* pszItemCode, uint16_t wCount);
        int GetCountQuestType(int nType);
        struct _happen_event_cont* GetLastHappenEvent();
        struct _Quest_fld* GetQuestFromEvent(char bySelect);
        bool GiveItem(char byQuestDBSlot, struct _action_node* pActionNode, bool bCheckOnly);
        void InitMgr(struct CPlayer* pMaster, struct _QUEST_DB_BASE* pQuestData);
        char InsertNpcQuestHistory(char* pszQuestCode, char byLevel, long double dRepeatTime);
        bool IsCompleteNpcQuest(char* pszCode, int bQuestRepeat);
        bool IsPossibleRepeatNpcQuest(char* pszCode, int nLinkQuestGroupID);
        bool IsProcLinkNpcQuest(char* pszCode, int nLinkQuestGroupID);
        bool IsProcNpcQuest(char* pszCode);
        bool IsRecvedQuestByNPC(char* pszNPCCode);
        bool IsRecvedQuestByNPC(int nEventIndex);
        static bool LoadQuestData();
        void Loop();
        bool ReturnItem(char* pszItemCode, int nEndReturnItemCnt, char byQuestDBSlot, bool bCheckOnly);
        void SendMsgToMaster_NoCompleteQuestFromNPC(char byQuestDBSlot);
        void SendMsgToMaster_NoHaveGiveItem(char byQuestDBSlot);
        void SendMsgToMaster_NoHaveReturnItem(char byQuestDBSlot);
        void SendMsgToMaster_ReturnItemAfterQuest(uint16_t wItemSerial, char byNum, char byQuestDBSlot);
        bool _CheckCondition(struct _happen_event_condition_node* pCond);
        bool __CheckCond_Class(char* pszClassCode);
        bool __CheckCond_Dalant(char byCompare, int nDalant);
        bool __CheckCond_Dummy(int bInclude, char* pszDummyCode);
        bool __CheckCond_Equip(char* pszItemCode);
        bool __CheckCond_Gold(char byCompare, int nGold);
        bool __CheckCond_Grade(char byCompare, int nGrade);
        bool __CheckCond_Guild(int belong);
        bool __CheckCond_Have(int nAmonut, char* pszItemCode);
        bool __CheckCond_LV(char byCompare, int nLv);
        bool __CheckCond_Mastery(char byCompare, char* pszMasteryPack);
        bool __CheckCond_Nation(int belong);
        bool __CheckCond_Party(int belong);
        bool __CheckCond_Race(char* pszRaceSexFlag);
        ~CQuestMgr();
        void dtor_CQuestMgr();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CQuestMgr, 56>(), "CQuestMgr");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_cash_discount_ini_.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _cash_discount_ini_ctor__cash_discount_ini_2_ptr = void (WINAPIV*)(struct _cash_discount_ini_*);
        using _cash_discount_ini_ctor__cash_discount_ini_2_clbk = void (WINAPIV*)(struct _cash_discount_ini_*, _cash_discount_ini_ctor__cash_discount_ini_2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

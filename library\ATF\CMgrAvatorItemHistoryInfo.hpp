// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMgrAvatorItemHistory.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMgrAvatorItemHistoryctor_CMgrAvatorItemHistory2_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*);
        using CMgrAvatorItemHistoryctor_CMgrAvatorItemHistory2_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, CMgrAvatorItemHistoryctor_CMgrAvatorItemHistory2_ptr);
        using CMgrAvatorItemHistoryClassUP4_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, char, char*, char*, int*, int*, char*);
        using CMgrAvatorItemHistoryClassUP4_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, char, char*, char*, int*, int*, char*, CMgrAvatorItemHistoryClassUP4_ptr);
        using CMgrAvatorItemHistoryClearLogBuffer6_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*);
        using CMgrAvatorItemHistoryClearLogBuffer6_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, CMgrAvatorItemHistoryClearLogBuffer6_ptr);
        using CMgrAvatorItemHistoryGetNewFileName8_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, char*);
        using CMgrAvatorItemHistoryGetNewFileName8_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, char*, CMgrAvatorItemHistoryGetNewFileName8_ptr);
        using CMgrAvatorItemHistoryGetTotalWaitSize10_ptr = int (WINAPIV*)(struct CMgrAvatorItemHistory*);
        using CMgrAvatorItemHistoryGetTotalWaitSize10_clbk = int (WINAPIV*)(struct CMgrAvatorItemHistory*, CMgrAvatorItemHistoryGetTotalWaitSize10_ptr);
        using CMgrAvatorItemHistoryIOThread12_ptr = void (WINAPIV*)(void*);
        using CMgrAvatorItemHistoryIOThread12_clbk = void (WINAPIV*)(void*, CMgrAvatorItemHistoryIOThread12_ptr);
        using CMgrAvatorItemHistoryInitClass14_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, char, char*, char*, int*, int*, char*);
        using CMgrAvatorItemHistoryInitClass14_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, char, char*, char*, int*, int*, char*, CMgrAvatorItemHistoryInitClass14_ptr);
        using CMgrAvatorItemHistoryOnLoop16_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*);
        using CMgrAvatorItemHistoryOnLoop16_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, CMgrAvatorItemHistoryOnLoop16_ptr);
        using CMgrAvatorItemHistoryWriteFile18_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, char*);
        using CMgrAvatorItemHistoryWriteFile18_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, char*, CMgrAvatorItemHistoryWriteFile18_ptr);
        using CMgrAvatorItemHistoryWriteLog20_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*);
        using CMgrAvatorItemHistoryWriteLog20_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, CMgrAvatorItemHistoryWriteLog20_ptr);
        using CMgrAvatorItemHistoryadd_storage_fail22_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, char*);
        using CMgrAvatorItemHistoryadd_storage_fail22_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, char*, CMgrAvatorItemHistoryadd_storage_fail22_ptr);
        using CMgrAvatorItemHistoryauto_trade_buy24_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, char*, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryauto_trade_buy24_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, char*, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryauto_trade_buy24_ptr);
        using CMgrAvatorItemHistoryauto_trade_login_sell26_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, char*, unsigned int, struct _STORAGE_LIST::_db_con*, int64_t, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryauto_trade_login_sell26_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, char*, unsigned int, struct _STORAGE_LIST::_db_con*, int64_t, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryauto_trade_login_sell26_ptr);
        using CMgrAvatorItemHistoryauto_trade_sell28_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, char*, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryauto_trade_sell28_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, char*, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryauto_trade_sell28_ptr);
        using CMgrAvatorItemHistoryback_trap_item30_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistoryback_trap_item30_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistoryback_trap_item30_ptr);
        using CMgrAvatorItemHistorybuy_item32_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _buy_offer*, char, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorybuy_item32_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _buy_offer*, char, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistorybuy_item32_ptr);
        using CMgrAvatorItemHistorybuy_to_inven_cashitem34_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, uint16_t, int, int, int, int, int, char*, uint64_t, char);
        using CMgrAvatorItemHistorybuy_to_inven_cashitem34_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, uint16_t, int, int, int, int, int, char*, uint64_t, char, CMgrAvatorItemHistorybuy_to_inven_cashitem34_ptr);
        using CMgrAvatorItemHistorybuy_unit36_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _UNIT_DB_BASE::_LIST*, unsigned int*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorybuy_unit36_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _UNIT_DB_BASE::_LIST*, unsigned int*, unsigned int, unsigned int, char*, CMgrAvatorItemHistorybuy_unit36_ptr);
        using CMgrAvatorItemHistorycash_item_use38_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistorycash_item_use38_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistorycash_item_use38_ptr);
        using CMgrAvatorItemHistorycashitem_del_from_inven40_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, uint16_t, uint64_t, char*);
        using CMgrAvatorItemHistorycashitem_del_from_inven40_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, uint16_t, uint64_t, char*, CMgrAvatorItemHistorycashitem_del_from_inven40_ptr);
        using CMgrAvatorItemHistorychar_copy42_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, char*);
        using CMgrAvatorItemHistorychar_copy42_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, char*, CMgrAvatorItemHistorychar_copy42_ptr);
        using CMgrAvatorItemHistorycheat_add_item44_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char, char*);
        using CMgrAvatorItemHistorycheat_add_item44_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char, char*, CMgrAvatorItemHistorycheat_add_item44_ptr);
        using CMgrAvatorItemHistorycheat_alter_money46_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorycheat_alter_money46_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, unsigned int, char*, CMgrAvatorItemHistorycheat_alter_money46_ptr);
        using CMgrAvatorItemHistorycheat_del_item48_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char, char*);
        using CMgrAvatorItemHistorycheat_del_item48_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char, char*, CMgrAvatorItemHistorycheat_del_item48_ptr);
        using CMgrAvatorItemHistorycheat_make_item_no_material50_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistorycheat_make_item_no_material50_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistorycheat_make_item_no_material50_ptr);
        using CMgrAvatorItemHistoryclose52_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, char*);
        using CMgrAvatorItemHistoryclose52_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, char*, CMgrAvatorItemHistoryclose52_ptr);
        using CMgrAvatorItemHistorycombine_ex_reward_item54_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _ITEMCOMBINE_DB_BASE*, char*, uint64_t*, char*);
        using CMgrAvatorItemHistorycombine_ex_reward_item54_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _ITEMCOMBINE_DB_BASE*, char*, uint64_t*, char*, CMgrAvatorItemHistorycombine_ex_reward_item54_ptr);
        using CMgrAvatorItemHistorycombine_ex_using_material56_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, char, struct _STORAGE_LIST::_db_con**, char*, unsigned int, char*, int, unsigned int);
        using CMgrAvatorItemHistorycombine_ex_using_material56_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, char, struct _STORAGE_LIST::_db_con**, char*, unsigned int, char*, int, unsigned int, CMgrAvatorItemHistorycombine_ex_using_material56_ptr);
        using CMgrAvatorItemHistorycombine_item58_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, char, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorycombine_item58_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, char, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, char*, CMgrAvatorItemHistorycombine_item58_ptr);
        using CMgrAvatorItemHistoryconsume_del_item60_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistoryconsume_del_item60_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistoryconsume_del_item60_ptr);
        using CMgrAvatorItemHistorycoupon_use_buy_item62_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct _STORAGE_LIST::_db_con*, char*, char*);
        using CMgrAvatorItemHistorycoupon_use_buy_item62_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct _STORAGE_LIST::_db_con*, char*, char*, CMgrAvatorItemHistorycoupon_use_buy_item62_ptr);
        using CMgrAvatorItemHistorycut_clear_item64_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, uint16_t*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorycut_clear_item64_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, uint16_t*, unsigned int, unsigned int, char*, CMgrAvatorItemHistorycut_clear_item64_ptr);
        using CMgrAvatorItemHistorycut_item66_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, int, uint16_t*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorycut_item66_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, int, uint16_t*, unsigned int, unsigned int, char*, CMgrAvatorItemHistorycut_item66_ptr);
        using CMgrAvatorItemHistorydelete_npc_quest_item68_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistorydelete_npc_quest_item68_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistorydelete_npc_quest_item68_ptr);
        using CMgrAvatorItemHistorydestroy_unit70_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, char, char*);
        using CMgrAvatorItemHistorydestroy_unit70_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, char, char*, CMgrAvatorItemHistorydestroy_unit70_ptr);
        using CMgrAvatorItemHistoryexchange_item72_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistoryexchange_item72_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistoryexchange_item72_ptr);
        using CMgrAvatorItemHistoryexchange_money74_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryexchange_money74_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryexchange_money74_ptr);
        using CMgrAvatorItemHistoryexchange_pvp_gold76_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryexchange_pvp_gold76_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryexchange_pvp_gold76_ptr);
        using CMgrAvatorItemHistoryexp_prof_log78_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*);
        using CMgrAvatorItemHistoryexp_prof_log78_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, CMgrAvatorItemHistoryexp_prof_log78_ptr);
        using CMgrAvatorItemHistorygrade_down_item80_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, unsigned int, char*);
        using CMgrAvatorItemHistorygrade_down_item80_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, unsigned int, char*, CMgrAvatorItemHistorygrade_down_item80_ptr);
        using CMgrAvatorItemHistorygrade_up_item82_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, char, char, unsigned int, char*);
        using CMgrAvatorItemHistorygrade_up_item82_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, char, char, unsigned int, char*, CMgrAvatorItemHistorygrade_up_item82_ptr);
        using CMgrAvatorItemHistoryguild_est_money84_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryguild_est_money84_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryguild_est_money84_ptr);
        using CMgrAvatorItemHistoryguild_est_money_rollback86_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryguild_est_money_rollback86_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryguild_est_money_rollback86_ptr);
        using CMgrAvatorItemHistoryguild_pop_money88_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryguild_pop_money88_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryguild_pop_money88_ptr);
        using CMgrAvatorItemHistoryguild_pop_money_rollback90_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryguild_pop_money_rollback90_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryguild_pop_money_rollback90_ptr);
        using CMgrAvatorItemHistoryguild_push_money92_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryguild_push_money92_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryguild_push_money92_ptr);
        using CMgrAvatorItemHistoryguild_push_money_rollback94_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryguild_push_money_rollback94_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryguild_push_money_rollback94_ptr);
        using CMgrAvatorItemHistoryguild_suggest_change_taxrate96_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryguild_suggest_change_taxrate96_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryguild_suggest_change_taxrate96_ptr);
        using CMgrAvatorItemHistoryhave_auto_item98_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct CUnmannedTraderRegistItemInfo*, char);
        using CMgrAvatorItemHistoryhave_auto_item98_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct CUnmannedTraderRegistItemInfo*, char, CMgrAvatorItemHistoryhave_auto_item98_ptr);
        using CMgrAvatorItemHistoryhave_item100_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, unsigned int, char, unsigned int, unsigned int, bool, char*);
        using CMgrAvatorItemHistoryhave_item100_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, unsigned int, char, unsigned int, unsigned int, bool, char*, CMgrAvatorItemHistoryhave_item100_ptr);
        using CMgrAvatorItemHistoryhave_item_close102_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, unsigned int, char, unsigned int, unsigned int, struct CUnmannedTraderRegistItemInfo*, char, char*);
        using CMgrAvatorItemHistoryhave_item_close102_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, unsigned int, char, unsigned int, unsigned int, struct CUnmannedTraderRegistItemInfo*, char, char*, CMgrAvatorItemHistoryhave_item_close102_ptr);
        using CMgrAvatorItemHistoryitem_serial_full104_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*);
        using CMgrAvatorItemHistoryitem_serial_full104_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, CMgrAvatorItemHistoryitem_serial_full104_ptr);
        using CMgrAvatorItemHistorylenditem_del_from_inven106_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, uint16_t, uint64_t, char*);
        using CMgrAvatorItemHistorylenditem_del_from_inven106_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, uint16_t, uint64_t, char*, CMgrAvatorItemHistorylenditem_del_from_inven106_ptr);
        using CMgrAvatorItemHistorylogin_cancel_auto_trade108_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, int64_t, char*);
        using CMgrAvatorItemHistorylogin_cancel_auto_trade108_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, int64_t, char*, CMgrAvatorItemHistorylogin_cancel_auto_trade108_ptr);
        using CMgrAvatorItemHistorymake_item110_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, char, char, bool, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistorymake_item110_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, char, char, bool, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistorymake_item110_ptr);
        using CMgrAvatorItemHistorymastery_change_jade112_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, unsigned int, int, float, char*, int);
        using CMgrAvatorItemHistorymastery_change_jade112_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, unsigned int, int, float, char*, int, CMgrAvatorItemHistorymastery_change_jade112_ptr);
        using CMgrAvatorItemHistorypatriarch_push_money114_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorypatriarch_push_money114_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, unsigned int, char*, CMgrAvatorItemHistorypatriarch_push_money114_ptr);
        using CMgrAvatorItemHistorypay_money116_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorypay_money116_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistorypay_money116_ptr);
        using CMgrAvatorItemHistorypersonal_amine_install118_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, uint16_t, struct _personal_amine_inven_db_load*, char*);
        using CMgrAvatorItemHistorypersonal_amine_install118_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, uint16_t, struct _personal_amine_inven_db_load*, char*, CMgrAvatorItemHistorypersonal_amine_install118_ptr);
        using CMgrAvatorItemHistorypersonal_amine_itemlog120_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, char, char, uint16_t, unsigned int, char*);
        using CMgrAvatorItemHistorypersonal_amine_itemlog120_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, char, char, uint16_t, unsigned int, char*, CMgrAvatorItemHistorypersonal_amine_itemlog120_ptr);
        using CMgrAvatorItemHistorypersonal_amine_stop122_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int*, int, char, uint16_t, char*);
        using CMgrAvatorItemHistorypersonal_amine_stop122_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int*, int, char, uint16_t, char*, CMgrAvatorItemHistorypersonal_amine_stop122_ptr);
        using CMgrAvatorItemHistorypersonal_amine_uninstall124_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, unsigned int*, int, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistorypersonal_amine_uninstall124_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char, unsigned int*, int, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistorypersonal_amine_uninstall124_ptr);
        using CMgrAvatorItemHistorypost_delete126_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostData*, char*);
        using CMgrAvatorItemHistorypost_delete126_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostData*, char*, CMgrAvatorItemHistorypost_delete126_ptr);
        using CMgrAvatorItemHistorypost_getpresent128_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, struct _STORAGE_LIST::_db_con*, uint64_t, unsigned int, char*);
        using CMgrAvatorItemHistorypost_getpresent128_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, struct _STORAGE_LIST::_db_con*, uint64_t, unsigned int, char*, CMgrAvatorItemHistorypost_getpresent128_ptr);
        using CMgrAvatorItemHistorypost_receive130_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostData*, char*);
        using CMgrAvatorItemHistorypost_receive130_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostData*, char*, CMgrAvatorItemHistorypost_receive130_ptr);
        using CMgrAvatorItemHistorypost_return132_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, struct _STORAGE_LIST::_db_con*, uint64_t, unsigned int, char*);
        using CMgrAvatorItemHistorypost_return132_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, unsigned int, struct _STORAGE_LIST::_db_con*, uint64_t, unsigned int, char*, CMgrAvatorItemHistorypost_return132_ptr);
        using CMgrAvatorItemHistorypost_returnreceive134_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostData*, char*);
        using CMgrAvatorItemHistorypost_returnreceive134_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostData*, char*, CMgrAvatorItemHistorypost_returnreceive134_ptr);
        using CMgrAvatorItemHistorypost_senditem136_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, struct _STORAGE_LIST::_db_con*, uint64_t, unsigned int, char*);
        using CMgrAvatorItemHistorypost_senditem136_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, struct _STORAGE_LIST::_db_con*, uint64_t, unsigned int, char*, CMgrAvatorItemHistorypost_senditem136_ptr);
        using CMgrAvatorItemHistorypost_storage138_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostStorage*, char*);
        using CMgrAvatorItemHistorypost_storage138_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostStorage*, char*, CMgrAvatorItemHistorypost_storage138_ptr);
        using CMgrAvatorItemHistoryprice_auto_trade140_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryprice_auto_trade140_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryprice_auto_trade140_ptr);
        using CMgrAvatorItemHistoryraceboss_candidate142_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, char*);
        using CMgrAvatorItemHistoryraceboss_candidate142_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, char*, CMgrAvatorItemHistoryraceboss_candidate142_ptr);
        using CMgrAvatorItemHistoryraceboss_giveback144_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryraceboss_giveback144_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryraceboss_giveback144_ptr);
        using CMgrAvatorItemHistoryraceboss_vote146_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, unsigned int, char*, char*);
        using CMgrAvatorItemHistoryraceboss_vote146_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, unsigned int, char*, char*, CMgrAvatorItemHistoryraceboss_vote146_ptr);
        using CMgrAvatorItemHistoryre_reg_auto_trade148_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryre_reg_auto_trade148_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryre_reg_auto_trade148_ptr);
        using CMgrAvatorItemHistoryread_cashamount150_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, unsigned int, int, char*);
        using CMgrAvatorItemHistoryread_cashamount150_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, unsigned int, unsigned int, int, char*, CMgrAvatorItemHistoryread_cashamount150_ptr);
        using CMgrAvatorItemHistoryreg_auto_trade152_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryreg_auto_trade152_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryreg_auto_trade152_ptr);
        using CMgrAvatorItemHistoryreturn_post_storage154_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostReturnStorage*, char*);
        using CMgrAvatorItemHistoryreturn_post_storage154_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct CPostReturnStorage*, char*, CMgrAvatorItemHistoryreturn_post_storage154_ptr);
        using CMgrAvatorItemHistoryreward_add_item156_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistoryreward_add_item156_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistoryreward_add_item156_ptr);
        using CMgrAvatorItemHistoryreward_add_money158_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistoryreward_add_money158_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char*, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistoryreward_add_money158_ptr);
        using CMgrAvatorItemHistoryrollback_cashitem160_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, uint64_t, char*, int, char*);
        using CMgrAvatorItemHistoryrollback_cashitem160_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, uint64_t, char*, int, char*, CMgrAvatorItemHistoryrollback_cashitem160_ptr);
        using CMgrAvatorItemHistoryself_cancel_auto_trade162_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistoryself_cancel_auto_trade162_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistoryself_cancel_auto_trade162_ptr);
        using CMgrAvatorItemHistorysell_item164_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _sell_offer*, char, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorysell_item164_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _sell_offer*, char, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistorysell_item164_ptr);
        using CMgrAvatorItemHistorysell_unit166_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, char, float, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorysell_unit166_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, char, float, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistorysell_unit166_ptr);
        using CMgrAvatorItemHistorytake_ground_item168_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _STORAGE_LIST::_db_con*, char*, unsigned int, char*, uint16_t, char*, float*, char*);
        using CMgrAvatorItemHistorytake_ground_item168_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _STORAGE_LIST::_db_con*, char*, unsigned int, char*, uint16_t, char*, float*, char*, CMgrAvatorItemHistorytake_ground_item168_ptr);
        using CMgrAvatorItemHistorythrow_ground_item170_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, float*, char*);
        using CMgrAvatorItemHistorythrow_ground_item170_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, char*, float*, char*, CMgrAvatorItemHistorythrow_ground_item170_ptr);
        using CMgrAvatorItemHistorytime_jade_effect_log172_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, struct _STORAGE_LIST::_db_con*, bool, char*);
        using CMgrAvatorItemHistorytime_jade_effect_log172_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, char*, struct _STORAGE_LIST::_db_con*, bool, char*, CMgrAvatorItemHistorytime_jade_effect_log172_ptr);
        using CMgrAvatorItemHistorytime_out_cancel_auto_trade174_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, char*);
        using CMgrAvatorItemHistorytime_out_cancel_auto_trade174_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, unsigned int, struct _STORAGE_LIST::_db_con*, char*, CMgrAvatorItemHistorytime_out_cancel_auto_trade174_ptr);
        using CMgrAvatorItemHistorytrade176_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, int, unsigned int, unsigned int, struct _STORAGE_LIST::_db_con*, int, unsigned int, unsigned int, char*, unsigned int, char*, unsigned int, unsigned int, char*, float*, char*);
        using CMgrAvatorItemHistorytrade176_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, int, unsigned int, unsigned int, struct _STORAGE_LIST::_db_con*, int, unsigned int, unsigned int, char*, unsigned int, char*, unsigned int, unsigned int, char*, float*, char*, CMgrAvatorItemHistorytrade176_ptr);
        using CMgrAvatorItemHistorytrans_ground_item178_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct _STORAGE_LIST::_db_con*, char*, unsigned int, char*, char*);
        using CMgrAvatorItemHistorytrans_ground_item178_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, struct _STORAGE_LIST::_db_con*, char*, unsigned int, char*, char*, CMgrAvatorItemHistorytrans_ground_item178_ptr);
        using CMgrAvatorItemHistorytrunk_io_item180_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, bool, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorytrunk_io_item180_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, bool, unsigned int, unsigned int, char*, CMgrAvatorItemHistorytrunk_io_item180_ptr);
        using CMgrAvatorItemHistorytrunk_io_money182_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, bool, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorytrunk_io_money182_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, bool, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, char*, CMgrAvatorItemHistorytrunk_io_money182_ptr);
        using CMgrAvatorItemHistorytrunk_swap_item184_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorytrunk_swap_item184_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, char*, CMgrAvatorItemHistorytrunk_swap_item184_ptr);
        using CMgrAvatorItemHistorytuning_unit186_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _UNIT_DB_BASE::_LIST*, int*, unsigned int, unsigned int, char*);
        using CMgrAvatorItemHistorytuning_unit186_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, char, struct _UNIT_DB_BASE::_LIST*, int*, unsigned int, unsigned int, char*, CMgrAvatorItemHistorytuning_unit186_ptr);
        using CMgrAvatorItemHistoryused_cash188_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, int, char*);
        using CMgrAvatorItemHistoryused_cash188_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, int, int, char*, CMgrAvatorItemHistoryused_cash188_ptr);
        
        using CMgrAvatorItemHistorydtor_CMgrAvatorItemHistory190_ptr = void (WINAPIV*)(struct CMgrAvatorItemHistory*);
        using CMgrAvatorItemHistorydtor_CMgrAvatorItemHistory190_clbk = void (WINAPIV*)(struct CMgrAvatorItemHistory*, CMgrAvatorItemHistorydtor_CMgrAvatorItemHistory190_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagPIXELFORMATDESCRIPTOR
    {
        unsigned __int16 nSize;
        unsigned __int16 nVersion;
        unsigned int dwFlags;
        char iPixelType;
        char cColorBits;
        char cRedBits;
        char cRedShift;
        char cGreenBits;
        char cGreenShift;
        char cBlueBits;
        char cBlueShift;
        char cAlphaBits;
        char cAlphaShift;
        char cAccumBits;
        char cAccumRedBits;
        char cAccumGreenBits;
        char cAccumBlueBits;
        char cAccumAlphaBits;
        char cDepthBits;
        char cStencilBits;
        char cAuxBuffers;
        char iLayerType;
        char bReserved;
        unsigned int dwLayerMask;
        unsigned int dwVisibleMask;
        unsigned int dwDamageMask;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct CEquipItemSFAgent
    {
        struct _requireSlot
        {
            bool m_SlotIndex[8];
        public:
            void Init();
            _requireSlot();
            void ctor__requireSlot();
        };
        struct CPlayer *m_pMaster;
        struct _sf_continous *m_pContSF[8];
    public:
        void AllEndContSF();
        CEquipItemSFAgent();
        void ctor_CEquipItemSFAgent();
        void EndContSF(struct _sf_continous* pSF_Cont);
        float GetBoosterAddSpeed();
        _STORAGE_LIST::_db_con* GetEquip(int nEquipTblIndex);
        struct _sf_continous* GetEquipSFCont(int nEquipTblIndex);
        bool GetRequireSFSlot(struct _requireSlot* pSlot, struct _skill_fld* pSkillFld);
        void Init(struct CPlayer* pMaster);
        char IsEnableSkill(struct _skill_fld* pSkill);
        char IsEnableSkill(int nEquipTblIndex, struct _skill_fld* pSkill);
        bool IsUseBooster();
        void ReleaseSFCont(int nEquipTblIndex);
        float SearchItemAddSpeed(_STORAGE_LIST::_db_con* pItem);
        void SetSFCont(int nEquipTblIndex, struct _sf_continous* pSF);
        void StartContSF(struct _sf_continous* pSF_Cont);
        ~CEquipItemSFAgent();
        void dtor_CEquipItemSFAgent();
    };    
    static_assert(ATF::checkSize<CEquipItemSFAgent, 72>(), "CEquipItemSFAgent");
END_ATF_NAMESPACE

#include <_pt_trans_votepaper_zocl.hpp>


START_ATF_NAMESPACE
    _pt_trans_votepaper_zocl::_pt_trans_votepaper_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _pt_trans_votepaper_zocl*);
        (org_ptr(0x1402bfff0L))(this);
    };
    void _pt_trans_votepaper_zocl::ctor__pt_trans_votepaper_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _pt_trans_votepaper_zocl*);
        (org_ptr(0x1402bfff0L))(this);
    };
    int _pt_trans_votepaper_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _pt_trans_votepaper_zocl*);
        return (org_ptr(0x1402c0490L))(this);
    };
    
END_ATF_NAMESPACE

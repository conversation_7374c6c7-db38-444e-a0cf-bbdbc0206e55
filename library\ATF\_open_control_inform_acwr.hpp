// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _open_control_inform_acwr
    {
        bool bControlOpen;
        unsigned int dwControlIP;
        unsigned __int16 wControlPort;
        unsigned int dwMasterKey[4];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_react_obj.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _react_objctor__react_obj2_ptr = void (WINAPIV*)(struct _react_obj*);
        using _react_objctor__react_obj2_clbk = void (WINAPIV*)(struct _react_obj*, _react_objctor__react_obj2_ptr);
        using _react_objcopy4_ptr = void (WINAPIV*)(struct _react_obj*, struct _react_obj*);
        using _react_objcopy4_clbk = void (WINAPIV*)(struct _react_obj*, struct _react_obj*, _react_objcopy4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_select_guild_master_lastconn
    {
        unsigned int dwSerial;
        unsigned int dwGuildIndex;
        unsigned int dwGuildSerial;
        unsigned int dwLimitConnTime;
        unsigned int dwLastConnTime;
    public:
        _qry_case_select_guild_master_lastconn();
        void ctor__qry_case_select_guild_master_lastconn();
        int size();
    };    
    static_assert(ATF::checkSize<_qry_case_select_guild_master_lastconn, 20>(), "_qry_case_select_guild_master_lastconn");
END_ATF_NAMESPACE

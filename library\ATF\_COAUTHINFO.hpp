// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_COAUTHIDENTITY.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _COAUTHINFO
    {
        unsigned int dwAuthnSvc;
        unsigned int dwAuthzSvc;
        wchar_t *pwszServerPrincName;
        unsigned int dwAuthnLevel;
        unsigned int dwImpersonationLevel;
        _COAUTHIDENTITY *pAuthIdentityData;
        unsigned int dwCapabilities;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

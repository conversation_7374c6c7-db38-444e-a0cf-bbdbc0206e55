// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CFindReplaceDialog.hpp>
#include <CNoTrackObject.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _AFX_EDIT_STATE : CNoTrackObject
    {
        CFindReplaceDialog *pFindReplaceDlg;
        int bFindOnly;
        ATL::CStringT<char> strFind;
        ATL::CStringT<char> strReplace;
        int bCase;
        int bNext;
        int bWord;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

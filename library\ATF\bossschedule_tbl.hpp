// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct BossSchedule_TBL
    {
        int m_nCount;
        struct BossSchedule_Map **m_MapScheduleList;
    public:
        BossSchedule_TBL();
        void ctor_BossSchedule_TBL();
        ~BossSchedule_TBL();
        void dtor_BossSchedule_TBL();
    };
END_ATF_NAMESPACE

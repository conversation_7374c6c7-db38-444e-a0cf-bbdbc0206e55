// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _holy_minetime_extend_inform_zowb
    {
        int nWorldCode;
        char by<PERSON><PERSON>sHour;
        char byChaosMin;
        char byNumOfTime;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

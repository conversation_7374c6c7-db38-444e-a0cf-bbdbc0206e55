// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitInfoPortal.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMoveMapLimitInfoPortalctor_CMoveMapLimitInfoPortal2_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*, unsigned int, int);
        using CMoveMapLimitInfoPortalctor_CMoveMapLimitInfoPortal2_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*, unsigned int, int, CMoveMapLimitInfoPortalctor_CMoveMapLimitInfoPortal2_ptr);
        using CMoveMapLimitInfoPortalInit4_ptr = bool (WINAPIV*)(struct CMoveMapLimitInfoPortal*);
        using CMoveMapLimitInfoPortalInit4_clbk = bool (WINAPIV*)(struct CMoveMapLimitInfoPortal*, CMoveMapLimitInfoPortalInit4_ptr);
        using CMoveMapLimitInfoPortalLoad6_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*, struct CPlayer*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoPortalLoad6_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*, struct CPlayer*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoPortalLoad6_ptr);
        using CMoveMapLimitInfoPortalLoadINI8_ptr = bool (WINAPIV*)(struct CMoveMapLimitInfoPortal*);
        using CMoveMapLimitInfoPortalLoadINI8_clbk = bool (WINAPIV*)(struct CMoveMapLimitInfoPortal*, CMoveMapLimitInfoPortalLoadINI8_ptr);
        using CMoveMapLimitInfoPortalLoop10_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*);
        using CMoveMapLimitInfoPortalLoop10_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*, CMoveMapLimitInfoPortalLoop10_ptr);
        using CMoveMapLimitInfoPortalProcForceMoveHQ12_ptr = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, char*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoPortalProcForceMoveHQ12_clbk = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, char*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoPortalProcForceMoveHQ12_ptr);
        using CMoveMapLimitInfoPortalProcGotoLimitZone14_ptr = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, char*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoPortalProcGotoLimitZone14_clbk = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, char*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoPortalProcGotoLimitZone14_ptr);
        using CMoveMapLimitInfoPortalProcUseMoveScroll16_ptr = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, char*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoPortalProcUseMoveScroll16_clbk = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, char*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoPortalProcUseMoveScroll16_ptr);
        using CMoveMapLimitInfoPortalRequest18_ptr = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, int, char*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoPortalRequest18_clbk = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, int, char*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoPortalRequest18_ptr);
        using CMoveMapLimitInfoPortalSubProcForceMoveHQ20_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*);
        using CMoveMapLimitInfoPortalSubProcForceMoveHQ20_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*, CMoveMapLimitInfoPortalSubProcForceMoveHQ20_ptr);
        using CMoveMapLimitInfoPortalSubProcGotoLimitZone22_ptr = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, char*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoPortalSubProcGotoLimitZone22_clbk = char (WINAPIV*)(struct CMoveMapLimitInfoPortal*, int, char*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoPortalSubProcGotoLimitZone22_ptr);
        using CMoveMapLimitInfoPortalSubProcNotifyForceMoveHQ24_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*);
        using CMoveMapLimitInfoPortalSubProcNotifyForceMoveHQ24_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*, CMoveMapLimitInfoPortalSubProcNotifyForceMoveHQ24_ptr);
        
        using CMoveMapLimitInfoPortaldtor_CMoveMapLimitInfoPortal26_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*);
        using CMoveMapLimitInfoPortaldtor_CMoveMapLimitInfoPortal26_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoPortal*, CMoveMapLimitInfoPortaldtor_CMoveMapLimitInfoPortal26_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

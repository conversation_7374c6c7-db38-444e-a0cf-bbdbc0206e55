// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$AA71CF500467BE6311B9AA077EFAA38D.hpp>
#include <CAlpha.hpp>
#include <CEntity.hpp>
#include <CExtDummy.hpp>
#include <CIndexBuffer.hpp>
#include <CMergeFileManager.hpp>
#include <CParticle.hpp>
#include <CPathFinder.hpp>
#include <CVertexBuffer.hpp>
#include <_ANI_OBJECT.hpp>
#include <_BSP_C_FACE.hpp>
#include <_BSP_FILE_HEADER.hpp>
#include <_BSP_LEAF.hpp>
#include <_BSP_MAT_GROUP.hpp>
#include <_BSP_NODE.hpp>
#include <_ENTITY_LIST.hpp>
#include <_EXT_BSP_FILE_HEADER.hpp>
#include <_LEAF_ENTITIES_LIST_INFO.hpp>
#include <_LEAF_SOUND_ENTITIES_LIST_INFO.hpp>
#include <_MAP_ENTITIES_LIST.hpp>
#include <_SOUND_ENTITIES_LIST.hpp>
#include <_SOUND_ENTITY_LIST.hpp>
#include <_TOOL_COL_LEAF.hpp>
#include <_TOOL_COL_LINE.hpp>


START_ATF_NAMESPACE
    struct CBsp
    {
        float (*mCVertex)[3];
        float (*mCNNormal)[3];
        unsigned int *mCVertexId;
        _BSP_C_FACE *mCFace;
        float (*mCNEdgeNormal)[4];
        unsigned int *mCFaceId;
        unsigned int *mVertexColor;
        __int16 (*mLgtUV)[2];
        int mNowCFaceId;
        unsigned __int16 *MatListInLeafId;
        unsigned int mMatGroupNum;
        _BSP_MAT_GROUP *mMatGroup;
        unsigned int mCVertexNum;
        unsigned int mCFaceNum;
        unsigned int mVertexNum;
        unsigned int mFaceNum;
        int mIsLoaded;
        unsigned int mObjectNum;
        _ANI_OBJECT *mObject;
        unsigned __int16 *mEventObjectID;
        unsigned int mEnvID[2];
        $AA71CF500467BE6311B9AA077EFAA38D ___u21;
        CMergeFileManager mMapEntityMFM;
        int mIsLoadEBP;
        char *mEntityCache;
        unsigned int mEntityCacheSize;
        unsigned int mNowRenderMatGroupNum;
        unsigned int mNowShadowMatGroupNum;
        CEntity *mEntity;
        CParticle *mParticle;
        _ENTITY_LIST *mEntityList;
        unsigned __int16 *mEntityID;
        _LEAF_ENTITIES_LIST_INFO *mLeafEntityList;
        _MAP_ENTITIES_LIST *mMapEntitiesList;
        unsigned int mEntityListNum;
        unsigned int mLeafEntityListNum;
        unsigned int mEntityIDNum;
        unsigned int mMapEntitiesListNum;
        char *mSoundEntityCache;
        unsigned int mSoundEntityCacheSize;
        unsigned int mSoundEntityIDNum;
        unsigned int mLeafSoundEntityListNum;
        unsigned int mSoundEntityListNum;
        unsigned int mSoundEntitiesListNum;
        _SOUND_ENTITY_LIST *mSoundEntityList;
        _SOUND_ENTITIES_LIST *mSoundEntitiesList;
        unsigned __int16 *mSoundEntityID;
        _LEAF_SOUND_ENTITIES_LIST_INFO *mLeafSoundEntityList;
        unsigned int mTotalAllocSize;
        unsigned int mTotalWaveSize;
        unsigned int mVertexBufferSize;
        _BSP_NODE *mNode;
        _BSP_LEAF *mLeaf;
        unsigned int mLeafNum;
        unsigned int mNodeNum;
        unsigned int mCFVertexNum;
        unsigned int mCFLineNum;
        unsigned int mCFLineIdNum;
        float (*mCFVertex)[3];
        _TOOL_COL_LINE *mCFLine;
        unsigned __int16 *mCFLineId;
        _TOOL_COL_LEAF *mCFLeaf;
        float (*mCFVNormal)[3];
        float (*mCFNormal)[4];
        CExtDummy *mDummy;
        CPathFinder mPathFinder;
        int mColFaceId;
        unsigned int mFindPathCnt;
        __int16 mNowPlayerNum;
        unsigned int mStaticVBCnt;
        CVertexBuffer mStaticVertexBuffer[80];
        unsigned int mVBVertexNum[80];
        CIndexBuffer mStaticIndexedBuffer;
        void *mMultiLayerUV;
        void *mMultiLayerST;
        char *mStaticAlloc;
        unsigned int mStaticAllocSize;
        char *mExtBspStaticAlloc;
        unsigned int mExtBspStaticAllocSize;
        int mPickPoly;
        char *mMatGroupCache;
        int mMatGroupCacheSize;
        float mTempCamera[3];
        __int16 mTempSearchOk;
        __int16 mNowLeafNum;
        _BSP_FILE_HEADER mBSPHeader;
        _EXT_BSP_FILE_HEADER mExtBSPHeader;
        CAlpha mAlpha;
    public:
        CBsp();
        int64_t ctor_CBsp();
        void CalcEntitiesMainColor();
        void CalcObjectLoop();
        int64_t CanYouGoThere(float* arg_0, float* arg_1, float** arg_2);
        void ClearVariable();
        void DrawAlphaRender(float* arg_0);
        void DrawBspRender();
        void DrawCollisionPoly();
        void DrawDynamicLightSub(float* arg_0, float* arg_1);
        void DrawDynamicLights();
        void DrawLeafBBox();
        void DrawLeafCollisionPoly(int16_t arg_0);
        void DrawMagicLightSub(float* arg_0, float* arg_1);
        void DrawMapEntitiesRender();
        void DrawMatBBox();
        void DrawShadowRender(float* arg_0, float* arg_1, float* arg_2);
        int EdgeTest(float* arg_0, int arg_1);
        void FastWalkNodeForLeafListFromBBox(int8_t arg_0, int16_t arg_1, int16_t arg_2, float** arg_3);
        void FrameMoveEnvironment();
        void FrameMoveMapEntities();
        float GetBestYposInLeaf(float* arg_0, float* arg_1, float arg_2, float arg_3, int arg_4);
        void GetBspObjectMatrix(float** arg_0, uint16_t arg_1);
        uint32_t GetColorFromPoint(int arg_0, float* arg_1);
        void* GetDynamicVertexBuffer();
        uint32_t GetEventAnimationState(uint16_t arg_0);
        void GetFaceFrontPoint(float** arg_0, int arg_1);
        void GetFastLeafListFromBBox(float* arg_0, float* arg_1, int32_t* arg_2, int16_t* arg_3, uint32_t arg_4);
        int64_t GetFinalPath(void* arg_0, float* arg_1, float** arg_2);
        float GetFirstYpos(float* arg_0, float* arg_1, float* arg_2);
        float GetFirstYpos(float* arg_0, int arg_1);
        float GetFirstYpos(float* arg_0, int16_t* arg_1, int16_t* arg_2);
        void GetHeight(float* arg_0);
        void GetLeafList(float* arg_0, float* arg_1, int32_t* arg_2, int16_t* arg_3, uint32_t arg_4);
        int16_t GetLeafNum(float* arg_0);
        uint32_t GetLightFromPoint(float* arg_0, uint32_t arg_1);
        void GetLightMapUVFromPoint(float* arg_0, int arg_1, float* arg_2);
        struct _BSP_MAT_GROUP* GetMatGroup();
        float GetMatGroupPoint(uint16_t arg_0, float* arg_1);
        void GetPath(float* arg_0, float* arg_1);
        int64_t GetPathCrossPoint(float* arg_0, float* arg_1, float** arg_2, int arg_3, int arg_4);
        uint32_t GetPathFind(float* arg_0, float* arg_1, float** arg_2, uint32_t* arg_3, int arg_4);
        int64_t GetPointFromScreenRay(float arg_0, float arg_1, float* arg_2, float* arg_3);
        int64_t GetPointFromScreenRayFar(float arg_0, float arg_1, float* arg_2, float* arg_3);
        void GetVertexNormal();
        float GetYposInLeaf(float* arg_0, float* arg_1, float arg_2, float arg_3, int arg_4);
        float GetYposInLeafNoAttr(float* arg_0, float* arg_1, float arg_2, float arg_3, int arg_4);
        float GetYposInLeafUseEdgeNormal(float* arg_0, float* arg_1, float arg_2, float arg_3, int arg_4);
        void HearMapSound();
        int64_t IsCollisionFace(float* arg_0, float* arg_1);
        int64_t IsCollisionFace(float* arg_0, float* arg_1, float** arg_2, float** arg_3);
        int64_t IsCollisionFace(float* arg_0, float* arg_1, float** arg_2, float arg_3);
        int64_t IsCollisionFaceForServer(float* arg_0, float* arg_1);
        int64_t IsCollisionFaceForServer(float* arg_0, float* arg_1, float** arg_2, float arg_3);
        int64_t IsCollisionFromPath(float* arg_0, float* arg_1);
        int64_t IsExistSelfPoint(int arg_0, int arg_1);
        int64_t IsInViewFrustum(uint16_t arg_0);
        int64_t IsInWater(float* arg_0, float* arg_1, float** arg_2, float arg_3);
        int64_t IsLoaded();
        void LoadBsp(char* arg_0);
        void LoadEntities(struct _READ_MAP_ENTITIES_LIST* arg_0);
        void LoadEnvironment();
        void LoadExtBsp(char* arg_0);
        void LoadSoundEntities(struct _READ_SOUND_ENTITY_LIST* arg_0, struct _READ_SOUND_ENTITIES_LIST* arg_1);
        void LoopInitRenderedMatGroup();
        void MakeEdgeNormal();
        void OnlyStoreCollisionStructure(struct _BSP_READ_M_GROUP* arg_0, char** arg_1, int16_t** arg_2, float** arg_3, uint32_t* arg_4, struct _BSP_READ_FACE* arg_5, uint32_t* arg_6);
        void PrepareAnimation();
        void PrepareShadowRender(float* arg_0, void* arg_1, float arg_2, uint32_t arg_3, float arg_4, float arg_5);
        void ReadDynamicDataExtBsp(FILE* File);
        void ReadDynamicDataFillVertexBuffer(FILE* File);
        void ReadyBspRender(float* arg_0);
        void ReleaseBsp();
        void ReleaseEntities();
        void ReleaseEnvironment();
        void ReleaseSoundEntities();
        void RenderCollisionLeaf(int16_t arg_0);
        void RenderEnvironment();
        void RenderIndepentMatGroup(uint16_t arg_0);
        void RenderLeaf(int16_t arg_0);
        void RenderMatGroup(uint16_t arg_0);
        void RenderOneEntityRender(uint16_t arg_0);
        void RenderReflectionMatGroup(uint16_t arg_0);
        void RenderShadowMatGroup(uint16_t arg_0);
        void SaveExtBsp(char* arg_0, struct _ADD_BSP_SAVE* arg_1);
        void SearchNode(int16_t arg_0);
        int64_t SetAllAnimationState(uint32_t arg_0);
        void SetCFNormal();
        int64_t SetEventAnimationState(uint16_t arg_0, uint32_t arg_1);
        void SetIsLoaded(int arg_0);
        void SubLeafList(float arg_0, struct _BSP_NODE* arg_1, float* arg_2, float* arg_3, int16_t* arg_4, int* arg_5);
        void SubLeafListFromBBox(float arg_0, struct _BSP_NODE* arg_1, float** arg_2, int16_t* arg_3, int* arg_4);
        void WalkLeaf(int16_t arg_0);
        void WalkNode(int16_t arg_0);
        void WalkNodeForLeafList(int8_t arg_0, int16_t arg_1, int16_t arg_2, float* arg_3, float arg_4);
        ~CBsp();
        int64_t dtor_CBsp();
    };
END_ATF_NAMESPACE

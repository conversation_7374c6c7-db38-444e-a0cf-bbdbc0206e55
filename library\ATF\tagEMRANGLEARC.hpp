// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_POINTL.hpp>
#include <tagEMR.hpp>


START_ATF_NAMESPACE
    struct tagEMRANGLEARC
    {
        tagEMR emr;
        _POINTL ptlCenter;
        unsigned int nRadius;
        float eStartAngle;
        float eSweepAngle;
    };
END_ATF_NAMESPACE

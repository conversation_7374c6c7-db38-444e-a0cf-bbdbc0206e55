// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagVS_FIXEDFILEINFO.hpp>



START_ATF_NAMESPACE
    struct _MINIDUMP_MODULE_CALLBACK
    {
        wchar_t *FullPath;
        unsigned __int64 BaseOfImage;
        unsigned int SizeOfImage;
        unsigned int CheckSum;
        unsigned int TimeDateStamp;
        tagVS_FIXEDFILEINFO VersionInfo;
        void *CvRecord;
        unsigned int SizeOfCvRecord;
         void *MiscRecord;
        unsigned int SizeOfMiscRecord;
    };
END_ATF_NAMESPACE

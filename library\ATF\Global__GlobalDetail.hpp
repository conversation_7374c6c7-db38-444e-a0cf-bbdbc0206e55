// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Global__GlobalInfo.hpp>


START_ATF_NAMESPACE
    namespace Global
    {
        namespace Detail
        {
            extern ::std::array<hook_record, 1277> _functions;
        }; // end namespace Detail
    }; // end namespace Global
END_ATF_NAMESPACE

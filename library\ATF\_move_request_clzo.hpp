// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _move_request_clzo
    {
        char byMoveType;
        float fCur[3];
        unsigned int dwSerial;
        __int16 zTar[2];
        char byDirect;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

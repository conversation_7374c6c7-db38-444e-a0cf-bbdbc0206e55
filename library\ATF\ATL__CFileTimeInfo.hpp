// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CFileTime.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            
            using ATL__CFileTimector_CFileTime1_ptr = void (WINAPIV*)(struct ATL::CFileTime*, struct _FILETIME*);
            using ATL__CFileTimector_CFileTime1_clbk = void (WINAPIV*)(struct ATL::CFileTime*, struct _FILETIME*, ATL__CFileTimector_CFileTime1_ptr);
            
            using ATL__CFileTimector_CFileTime2_ptr = void (WINAPIV*)(struct ATL::CFileTime*, uint64_t);
            using ATL__CFileTimector_CFileTime2_clbk = void (WINAPIV*)(struct ATL::CFileTime*, uint64_t, ATL__CFileTimector_CFileTime2_ptr);
            
            using ATL__CFileTimector_CFileTime3_ptr = void (WINAPIV*)(struct ATL::CFileTime*);
            using ATL__CFileTimector_CFileTime3_clbk = void (WINAPIV*)(struct ATL::CFileTime*, ATL__CFileTimector_CFileTime3_ptr);
            using ATL__CFileTimeGetTickCount4_ptr = struct ATL::CFileTime* (WINAPIV*)(struct ATL::CFileTime*);
            using ATL__CFileTimeGetTickCount4_clbk = struct ATL::CFileTime* (WINAPIV*)(struct ATL::CFileTime*, ATL__CFileTimeGetTickCount4_ptr);
            using ATL__CFileTimeGetTime5_ptr = uint64_t (WINAPIV*)(struct ATL::CFileTime*);
            using ATL__CFileTimeGetTime5_clbk = uint64_t (WINAPIV*)(struct ATL::CFileTime*, ATL__CFileTimeGetTime5_ptr);
            using ATL__CFileTimeLocalToUTC6_ptr = struct ATL::CFileTime* (WINAPIV*)(struct ATL::CFileTime*, struct ATL::CFileTime*);
            using ATL__CFileTimeLocalToUTC6_clbk = struct ATL::CFileTime* (WINAPIV*)(struct ATL::CFileTime*, struct ATL::CFileTime*, ATL__CFileTimeLocalToUTC6_ptr);
            using ATL__CFileTimeSetTime7_ptr = void (WINAPIV*)(struct ATL::CFileTime*, uint64_t);
            using ATL__CFileTimeSetTime7_clbk = void (WINAPIV*)(struct ATL::CFileTime*, uint64_t, ATL__CFileTimeSetTime7_ptr);
            using ATL__CFileTimeUTCToLocal8_ptr = struct ATL::CFileTime* (WINAPIV*)(struct ATL::CFileTime*, struct ATL::CFileTime*);
            using ATL__CFileTimeUTCToLocal8_clbk = struct ATL::CFileTime* (WINAPIV*)(struct ATL::CFileTime*, struct ATL::CFileTime*, ATL__CFileTimeUTCToLocal8_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

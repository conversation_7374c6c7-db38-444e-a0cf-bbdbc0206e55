// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IO_COUNTERS
    {
        unsigned __int64 ReadOperationCount;
        unsigned __int64 WriteOperationCount;
        unsigned __int64 OtherOperationCount;
        unsigned __int64 ReadTransferCount;
        unsigned __int64 WriteTransferCount;
        unsigned __int64 OtherTransferCount;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _NETRESOURCEA
    {
        unsigned int dwScope;
        unsigned int dwType;
        unsigned int dwDisplayType;
        unsigned int dwUsage;
        char *lpLocalName;
        char *lpRemoteName;
        char *lpComment;
        char *lpProvider;
    };
END_ATF_NAMESPACE

#include <_create_holy_master_zocl.hpp>


START_ATF_NAMESPACE
    _create_holy_master_zocl::_create_holy_master_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _create_holy_master_zocl*);
        (org_ptr(0x140284cb0L))(this);
    };
    void _create_holy_master_zocl::ctor__create_holy_master_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _create_holy_master_zocl*);
        (org_ptr(0x140284cb0L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryUS.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryUSctor_CNationSettingFactoryUS2_ptr = void (WINAPIV*)(struct CNationSettingFactoryUS*);
        using CNationSettingFactoryUSctor_CNationSettingFactoryUS2_clbk = void (WINAPIV*)(struct CNationSettingFactoryUS*, CNationSettingFactoryUSctor_CNationSettingFactoryUS2_ptr);
        using CNationSettingFactoryUSCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryUS*, int, char*, bool);
        using CNationSettingFactoryUSCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryUS*, int, char*, bool, CNationSettingFactoryUSCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

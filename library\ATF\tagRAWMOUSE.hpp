// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$6280BC0C15119B35B4A51EECEA781B4F.hpp>


START_ATF_NAMESPACE
    struct tagRAWMOUSE
    {
        unsigned __int16 usFlags;
        $6280BC0C15119B35B4A51EECEA781B4F ___u1;
        unsigned int ulRawButtons;
        int lLastX;
        int lLastY;
        unsigned int ulExtraInformation;
    };
END_ATF_NAMESPACE

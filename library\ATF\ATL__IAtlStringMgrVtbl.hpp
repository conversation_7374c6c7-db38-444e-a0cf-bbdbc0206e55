// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct IAtlStringMgrVtbl
        {
            CStringData *(WINAPIV *Allocate)(IAtlStringMgr *_this, int, int);
            void (WINAPIV *Free)(IAtlStringMgr *_this, CStringData *);
            CStringData *(WINAPIV *Reallocate)(IAtlStringMgr *_this, CStringData *, int, int);
            CStringData *(WINAPIV *GetNilString)(IAtlStringMgr *_this);
            IAtlStringMgr *(WINAPIV *Clone)(IAtlStringMgr *_this);
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

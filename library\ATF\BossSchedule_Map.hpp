// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CIniFile.hpp>


START_ATF_NAMESPACE
    struct BossSchedule_Map
    {
        int m_nIndex;
        char m_strMap[64];
        CIniFile m_INIFile;
        int m_nCount;
        struct BossSchedule **m_ScheduleList;
        struct CBossMonsterScheduleSystem *m_pSystem;
    public:
        BossSchedule_Map();
        void ctor_BossSchedule_Map();
        void Clear();
        bool LoadAll();
        bool SaveAll();
        ~BossSchedule_Map();
        void dtor_BossSchedule_Map();
    };
END_ATF_NAMESPACE

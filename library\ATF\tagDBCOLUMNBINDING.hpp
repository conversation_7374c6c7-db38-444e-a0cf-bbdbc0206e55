// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagDBCOLUMNID.hpp>


START_ATF_NAMESPACE
    struct tagDBCOLUMNBINDING
    {
        tagDBCOLUMNID columnID;
        unsigned int obData;
        unsigned int cbMaxLen;
        unsigned int obVarDataLen;
        unsigned int obInfo;
        unsigned int dwBinding;
        unsigned int dwDataType;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagBITMAP.hpp>
#include <tagBITMAPINFOHEADER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagDIBSECTION
    {
        tagBITMAP dsBm;
        tagBITMAPINFOHEADER dsBmih;
        unsigned int dsBitfields[3];
        void *dshSection;
        unsigned int dsOffset;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

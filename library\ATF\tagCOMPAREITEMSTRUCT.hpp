// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagCOMPAREITEMSTRUCT
    {
        unsigned int CtlType;
        unsigned int CtlID;
        HWND__ *hwndItem;
        unsigned int itemID1;
        unsigned __int64 itemData1;
        unsigned int itemID2;
        unsigned __int64 itemData2;
        unsigned int dwLocaleId;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

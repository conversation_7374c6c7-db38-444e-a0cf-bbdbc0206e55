// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _quest_fail_result
    {
        struct _node
        {
            char byQuestDBSlot;
        };
        char m_byCheckNum;
        _node m_List[30];
    public:
        void init();
    };    
    static_assert(ATF::checkSize<_quest_fail_result, 31>(), "_quest_fail_result");
END_ATF_NAMESPACE

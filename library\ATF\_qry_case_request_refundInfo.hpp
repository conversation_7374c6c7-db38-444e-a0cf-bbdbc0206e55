// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_request_refund.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_request_refundctor__qry_case_request_refund2_ptr = void (WINAPIV*)(struct _qry_case_request_refund*, char, uint16_t, unsigned int, uint64_t);
        using _qry_case_request_refundctor__qry_case_request_refund2_clbk = void (WINAPIV*)(struct _qry_case_request_refund*, char, uint16_t, unsigned int, uint64_t, _qry_case_request_refundctor__qry_case_request_refund2_ptr);
        using _qry_case_request_refundsize4_ptr = int (WINAPIV*)(struct _qry_case_request_refund*);
        using _qry_case_request_refundsize4_clbk = int (WINAPIV*)(struct _qry_case_request_refund*, _qry_case_request_refundsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

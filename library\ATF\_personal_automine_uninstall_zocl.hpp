// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _personal_automine_uninstall_zocl
    {
        struct  __battery
        {
            unsigned int dwDur;
            unsigned __int16 wSerial;
        };
        unsigned int dwObjSerial;
        char byActType;
         unsigned int dwOwnerSerial;
         unsigned __int16 wItemSerial;
        char byCnt;
        __battery battery[2];
    public:
        _personal_automine_uninstall_zocl();
        void ctor__personal_automine_uninstall_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_personal_automine_uninstall_zocl, 24>(), "_personal_automine_uninstall_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPotionParam.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CPotionParamctor_CPotionParam2_ptr = void (WINAPIV*)(struct CPotionParam*);
        using CPotionParamctor_CPotionParam2_clbk = void (WINAPIV*)(struct CPotionParam*, CPotionParamctor_CPotionParam2_ptr);
        using CPotionParamInit4_ptr = void (WINAPIV*)(struct CPotionParam*, struct CPlayer*);
        using CPotionParamInit4_clbk = void (WINAPIV*)(struct CPotionParam*, struct CPlayer*, CPotionParamInit4_ptr);
        using CPotionParamIsUsableActDelay6_ptr = bool (WINAPIV*)(struct CPotionParam*, char, unsigned int);
        using CPotionParamIsUsableActDelay6_clbk = bool (WINAPIV*)(struct CPotionParam*, char, unsigned int, CPotionParamIsUsableActDelay6_ptr);
        using CPotionParamSetPotionActDelay8_ptr = void (WINAPIV*)(struct CPotionParam*, char, unsigned int, unsigned int);
        using CPotionParamSetPotionActDelay8_clbk = void (WINAPIV*)(struct CPotionParam*, char, unsigned int, unsigned int, CPotionParamSetPotionActDelay8_ptr);
        
        using CPotionParamdtor_CPotionParam10_ptr = void (WINAPIV*)(struct CPotionParam*);
        using CPotionParamdtor_CPotionParam10_clbk = void (WINAPIV*)(struct CPotionParam*, CPotionParamdtor_CPotionParam10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

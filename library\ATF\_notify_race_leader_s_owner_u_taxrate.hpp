// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _notify_race_leader_s_owner_u_taxrate
    {
        char wszRaceLeaderName[9][17];
        char wszSettlement1OwnerGuildName[17];
        char wszSettlement1OwnerGuildMasterName[17];
        char wszSettlement2OwnerGuildName[17];
        char wszSettlement2OwnerGuildMasterName[17];
        char byTaxRate;
    };    
    static_assert(ATF::checkSize<_notify_race_leader_s_owner_u_taxrate, 222>(), "_notify_race_leader_s_owner_u_taxrate");
END_ATF_NAMESPACE

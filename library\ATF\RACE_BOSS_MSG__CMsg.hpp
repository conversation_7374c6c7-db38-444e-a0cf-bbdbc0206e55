// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSchedule.hpp>


START_ATF_NAMESPACE
    namespace RACE_BOSS_MSG
    {
        struct CMsg
        {
            typedef CUnmannedTraderSchedule::STATE STATE;
            unsigned int m_uiState;
            unsigned int m_dwID;
            unsigned int m_dwSerial;
            char m_wszName[17];
            char m_wszMsg[49];
            unsigned int m_dwSendTime;
            unsigned int m_dwWebSendDBID;
        public:
            CMsg(char ucRace, unsigned int dwID);
            void ctor_CMsg(char ucRace, unsigned int dwID);
            void Clear();
            char* GetBossName();
            unsigned int GetID();
            char* GetMsg();
            unsigned int GetSerial();
            unsigned int GetWebDBID();
            bool IsDayChanged();
            bool IsSendFromWeb();
            bool IsSendTime();
            bool IsWait();
            bool Load(char ucRace, unsigned int dwCurTime);
            bool Save(char ucRace);
            bool Set(unsigned int dwSerial, char* pwszName, char* pwszMsg, unsigned int dbWebSendDBID);
            void SetDayChanged();
            void SetDone();
            ~CMsg();
            void dtor_CMsg();
        };
    }; // end namespace RACE_BOSS_MSG
END_ATF_NAMESPACE

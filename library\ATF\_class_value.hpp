// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_class_fld.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _class_value
    {
        _class_fld *m_pFld;
        char byClassVal;
    public:
        _class_value();
        void ctor__class_value();
        void init();
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CSocketAddrVtbl.hpp>
#include <addrinfo.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct CSocketAddr
        {
            CSocketAddrVtbl *vfptr;
            addrinfo *m_pAddrs;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

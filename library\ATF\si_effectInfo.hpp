// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <si_effect.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using si_effectGetCountOfEffect2_ptr = char (WINAPIV*)(struct si_effect*);
        using si_effectGetCountOfEffect2_clbk = char (WINAPIV*)(struct si_effect*, si_effectGetCountOfEffect2_ptr);
        using si_effectGetCountOfItem4_ptr = char (WINAPIV*)(struct si_effect*);
        using si_effectGetCountOfItem4_clbk = char (WINAPIV*)(struct si_effect*, si_effectGetCountOfItem4_ptr);
        using si_effectinit6_ptr = void (WINAPIV*)(struct si_effect*);
        using si_effectinit6_clbk = void (WINAPIV*)(struct si_effect*, si_effectinit6_ptr);
        using si_effectset_effect_count_info8_ptr = void (WINAPIV*)(struct si_effect*, char, char);
        using si_effectset_effect_count_info8_clbk = void (WINAPIV*)(struct si_effect*, char, char, si_effectset_effect_count_info8_ptr);
        
        using si_effectctor_si_effect10_ptr = void (WINAPIV*)(struct si_effect*);
        using si_effectctor_si_effect10_clbk = void (WINAPIV*)(struct si_effect*, si_effectctor_si_effect10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

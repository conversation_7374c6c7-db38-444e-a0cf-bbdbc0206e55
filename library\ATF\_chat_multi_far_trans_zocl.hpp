// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _chat_multi_far_trans_zocl
    {
        unsigned __int16 wSize;
        char sData[500];
    public:
        _chat_multi_far_trans_zocl();
        void ctor__chat_multi_far_trans_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_chat_multi_far_trans_zocl, 502>(), "_chat_multi_far_trans_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_make_storage
    {
        char bySubQry;
        int nSockIdx;
        unsigned int dwAvatorSerial;
    public:
        _qry_case_make_storage();
        void ctor__qry_case_make_storage();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_make_storage, 12>(), "_qry_case_make_storage");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSubClassInfo.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderSubClassFactory
    {
        std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > m_vecTable;
    public:
        CUnmannedTraderSubClassFactory();
        void ctor_CUnmannedTraderSubClassFactory();
        struct CUnmannedTraderSubClassInfo* Create(char* szType, unsigned int dwID);
        void Destroy();
        bool Regist(struct CUnmannedTraderSubClassInfo* pkType);
        ~CUnmannedTraderSubClassFactory();
        void dtor_CUnmannedTraderSubClassFactory();
    };    
    static_assert(ATF::checkSize<CUnmannedTraderSubClassFactory, 40>(), "CUnmannedTraderSubClassFactory");
END_ATF_NAMESPACE

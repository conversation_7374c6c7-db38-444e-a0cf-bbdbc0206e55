// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <US__CriticalSection.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        namespace Info
        {
            
            using US__CriticalSectionctor_CriticalSection2_ptr = void (WINAPIV*)(struct US::CriticalSection*);
            using US__CriticalSectionctor_CriticalSection2_clbk = void (WINAPIV*)(struct US::CriticalSection*, US__CriticalSectionctor_CriticalSection2_ptr);
            using US__CriticalSectionLock4_ptr = void (WINAPIV*)(struct US::CriticalSection*);
            using US__CriticalSectionLock4_clbk = void (WINAPIV*)(struct US::CriticalSection*, US__CriticalSectionLock4_ptr);
            using US__CriticalSectionUnLock6_ptr = void (WINAPIV*)(struct US::CriticalSection*);
            using US__CriticalSectionUnLock6_clbk = void (WINAPIV*)(struct US::CriticalSection*, US__CriticalSectionUnLock6_ptr);
            
            using US__CriticalSectiondtor_CriticalSection8_ptr = void (WINAPIV*)(struct US::CriticalSection*);
            using US__CriticalSectiondtor_CriticalSection8_clbk = void (WINAPIV*)(struct US::CriticalSection*, US__CriticalSectiondtor_CriticalSection8_ptr);
        }; // end namespace Info
    }; // end namespace US
END_ATF_NAMESPACE

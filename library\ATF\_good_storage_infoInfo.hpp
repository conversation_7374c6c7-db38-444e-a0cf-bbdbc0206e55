// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_good_storage_info.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _good_storage_infoctor__good_storage_info2_ptr = void (WINAPIV*)(struct _good_storage_info*);
        using _good_storage_infoctor__good_storage_info2_clbk = void (WINAPIV*)(struct _good_storage_info*, _good_storage_infoctor__good_storage_info2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

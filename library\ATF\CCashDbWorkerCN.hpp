// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CashDbWorker.hpp>


START_ATF_NAMESPACE
    struct  CCashDbWorkerCN : CashDbWorker
    {
    public:
        CCashDbWorkerCN();
        void ctor_CCashDbWorkerCN();
        ~CCashDbWorkerCN();
        void dtor_CCashDbWorkerCN();
    };
END_ATF_NAMESPACE

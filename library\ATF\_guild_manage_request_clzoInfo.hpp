// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_guild_manage_request_clzo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _guild_manage_request_clzoctor__guild_manage_request_clzo2_ptr = void (WINAPIV*)(struct _guild_manage_request_clzo*);
        using _guild_manage_request_clzoctor__guild_manage_request_clzo2_clbk = void (WINAPIV*)(struct _guild_manage_request_clzo*, _guild_manage_request_clzoctor__guild_manage_request_clzo2_ptr);
        using _guild_manage_request_clzosize4_ptr = int (WINAPIV*)(struct _guild_manage_request_clzo*);
        using _guild_manage_request_clzosize4_clbk = int (WINAPIV*)(struct _guild_manage_request_clzo*, _guild_manage_request_clzosize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_action_point_system_ini.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct CActionPointSystemMgr
    {
        _action_point_system_ini m_st_ini_list[3];
        char m_bActive[3];
    public:
        CActionPointSystemMgr();
        void ctor_CActionPointSystemMgr();
        void Check_Event_Status();
        void Check_Load_Event_Status(char byActionCode, struct _action_point_system_ini* pIni);
        void Check_Loop();
        char GetEventStatus(char byActionCode);
        bool Initialize();
        static struct CActionPointSystemMgr* Instance();
        bool IsPointReset(char byActionCode);
        bool IsSystemEnable(char byActionCode);
        void Load_Event_INI(struct _action_point_system_ini* pIni, uint16_t wIndex);
        void SetEventStatus(char byActionCode, char byStatus);
        ~CActionPointSystemMgr();
        void dtor_CActionPointSystemMgr();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

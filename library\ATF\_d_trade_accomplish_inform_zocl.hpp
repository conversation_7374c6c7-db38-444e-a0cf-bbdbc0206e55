// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _d_trade_accomplish_inform_zocl
    {
        unsigned int dwDalant;
        unsigned int dwGold;
        unsigned __int16 wStartSerial;
        bool bSucc;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

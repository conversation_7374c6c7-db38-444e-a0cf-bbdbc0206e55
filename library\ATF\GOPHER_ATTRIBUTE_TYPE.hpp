// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$30D40150F5E3EBA45F52C467866E86C3.hpp>


START_ATF_NAMESPACE
    struct GOPHER_ATTRIBUTE_TYPE
    {
        unsigned int CategoryId;
        unsigned int AttributeId;
        $30D40150F5E3EBA45F52C467866E86C3 AttributeType;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _ACTIVATION_CONTEXT_DETAILED_INFORMATION
    {
        unsigned int dwFlags;
        unsigned int ulFormatVersion;
        unsigned int ulAssemblyCount;
        unsigned int ulRootManifestPathType;
        unsigned int ulRootManifestPathChars;
        unsigned int ulRootConfigurationPathType;
        unsigned int ulRootConfigurationPathChars;
        unsigned int ulAppDirPathType;
        unsigned int ulAppDirPathChars;
        const wchar_t *lpRootManifestPath;
        const wchar_t *lpRootConfigurationPath;
        const wchar_t *lpAppDirPath;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CAtlException.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            
            using ATL__CAtlExceptionctor_CAtlException1_ptr = void (WINAPIV*)(struct ATL::CAtlException*, HRESULT);
            using ATL__CAtlExceptionctor_CAtlException1_clbk = void (WINAPIV*)(struct ATL::CAtlException*, HRESULT, ATL__CAtlExceptionctor_CAtlException1_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

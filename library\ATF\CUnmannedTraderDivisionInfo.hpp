// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CUnmannedTraderSortType.hpp>
#include <CUnmannedTraderClassInfo.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderDivisionInfo
    {
        unsigned int m_dwID;
        char m_szName[128];
        std::vector<CUnmannedTraderClassInfo *> m_vecClass;
        std::vector<CUnmannedTraderSortType *> m_vecSortType;
    public:
        CUnmannedTraderDivisionInfo(unsigned int dwID, char* szName);
        void ctor_CUnmannedTraderDivisionInfo(unsigned int dwID, char* szName);
        void CleanUp();
        struct CUnmannedTraderDivisionInfo* Copy(struct CUnmannedTraderDivisionInfo* lhs);
        struct CUnmannedTraderSortType* FindSortType(unsigned int dwID);
        bool GetGroupID(char byTableCode, uint16_t wItemTableIndex, char* byDivision, char* byClass);
        bool GetGroupID(char byTableCode, uint16_t wItemTableIndex, char* byDivision, char* byClass, char* bySubClass, unsigned int* dwListIndex);
        unsigned int GetID();
        unsigned int GetMaxClassCnt();
        unsigned int GetSize();
        struct CUnmannedTraderSortType* GetSortType(char bySortType);
        bool IsExistGroupID(char byDivision, char byClass, char bySubClass, char bySortType, unsigned int* dwListIndex);
        bool IsExistSortTypeID(unsigned int dwID);
        bool IsValidID(unsigned int dwID);
        bool LoadXML(struct TiXmlElement* pkElement, struct CLogFile* kLogger);
        ~CUnmannedTraderDivisionInfo();
        void dtor_CUnmannedTraderDivisionInfo();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_quick_link.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _quick_linkctor__quick_link2_ptr = void (WINAPIV*)(struct _quick_link*);
        using _quick_linkctor__quick_link2_clbk = void (WINAPIV*)(struct _quick_link*, _quick_linkctor__quick_link2_ptr);
        using _quick_linkinit4_ptr = void (WINAPIV*)(struct _quick_link*);
        using _quick_linkinit4_clbk = void (WINAPIV*)(struct _quick_link*, _quick_linkinit4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYSTEMTIME.hpp>
#include <_devicemodeA.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _JOB_INFO_2A
    {
        unsigned int JobId;
        char *pPrinterName;
        char *pMachineName;
        char *pUserName;
        char *pDocument;
        char *pNotifyName;
        char *pDatatype;
        char *pPrintProcessor;
        char *pParameters;
        char *pDriverName;
        _devicemodeA *pDevMode;
        char *pStatus;
        void *pSecurityDescriptor;
        unsigned int Status;
        unsigned int Priority;
        unsigned int Position;
        unsigned int StartTime;
        unsigned int UntilTime;
        unsigned int TotalPages;
        unsigned int Size;
        _SYSTEMTIME Submitted;
        unsigned int Time;
        unsigned int PagesPrinted;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

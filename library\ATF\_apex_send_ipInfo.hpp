// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_apex_send_ip.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _apex_send_ipctor__apex_send_ip2_ptr = void (WINAPIV*)(struct _apex_send_ip*);
        using _apex_send_ipctor__apex_send_ip2_clbk = void (WINAPIV*)(struct _apex_send_ip*, _apex_send_ipctor__apex_send_ip2_ptr);
        using _apex_send_ipsize4_ptr = int (WINAPIV*)(struct _apex_send_ip*);
        using _apex_send_ipsize4_clbk = int (WINAPIV*)(struct _apex_send_ip*, _apex_send_ipsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

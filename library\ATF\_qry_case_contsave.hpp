// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_AVATOR_DATA.hpp>


START_ATF_NAMESPACE
    struct _qry_case_contsave
    {
        unsigned int dwAvatorSerial;
        _AVATOR_DATA NewData;
        _AVATOR_DATA OldData;
        bool bUpdateRefineCnt;
        char byRefinedCnt;
        unsigned int dwRefineDate;
    public:
        _qry_case_contsave();
        void ctor__qry_case_contsave();
        int size();
        ~_qry_case_contsave();
        void dtor__qry_case_contsave();
    };    
    static_assert(ATF::checkSize<_qry_case_contsave, 74440>(), "_qry_case_contsave");
END_ATF_NAMESPACE

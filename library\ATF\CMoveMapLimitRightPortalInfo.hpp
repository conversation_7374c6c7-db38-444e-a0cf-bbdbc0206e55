// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitRightPortal.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMoveMapLimitRightPortalctor_CMoveMapLimitRightPortal2_ptr = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, int);
        using CMoveMapLimitRightPortalctor_CMoveMapLimitRightPortal2_clbk = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, int, CMoveMapLimitRightPortalctor_CMoveMapLimitRightPortal2_ptr);
        using CMoveMapLimitRightPortalCreateComplete4_ptr = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, struct CPlayer*);
        using CMoveMapLimitRightPortalCreateComplete4_clbk = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, struct CPlayer*, CMoveMapLimitRightPortalCreateComplete4_ptr);
        using CMoveMapLimitRightPortalIsHaveRight6_ptr = bool (WINAPIV*)(struct CMoveMapLimitRightPortal*);
        using CMoveMapLimitRightPortalIsHaveRight6_clbk = bool (WINAPIV*)(struct CMoveMapLimitRightPortal*, CMoveMapLimitRightPortalIsHaveRight6_ptr);
        using CMoveMapLimitRightPortalLoad8_ptr = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, struct CPlayer*);
        using CMoveMapLimitRightPortalLoad8_clbk = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, struct CPlayer*, CMoveMapLimitRightPortalLoad8_ptr);
        using CMoveMapLimitRightPortalLogOut10_ptr = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, struct CPlayer*);
        using CMoveMapLimitRightPortalLogOut10_clbk = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, struct CPlayer*, CMoveMapLimitRightPortalLogOut10_ptr);
        using CMoveMapLimitRightPortalSetFlag12_ptr = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, int, bool);
        using CMoveMapLimitRightPortalSetFlag12_clbk = void (WINAPIV*)(struct CMoveMapLimitRightPortal*, int, bool, CMoveMapLimitRightPortalSetFlag12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

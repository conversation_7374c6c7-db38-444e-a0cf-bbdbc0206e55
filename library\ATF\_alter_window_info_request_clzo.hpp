// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _alter_window_info_request_clzo
    {
        unsigned int dwSkill[2];
        unsigned int dwForce[2];
        unsigned int dwCharacter[2];
        unsigned int dwAnimus[2];
        unsigned int dwInven;
        unsigned int dwInvenBag[5];
    };
END_ATF_NAMESPACE

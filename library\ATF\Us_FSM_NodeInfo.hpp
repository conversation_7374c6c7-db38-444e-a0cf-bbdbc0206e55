// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Us_FSM_Node.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using Us_FSM_NodeGetState2_ptr = unsigned int (WINAPIV*)(struct Us_FSM_Node*);
        using Us_FSM_NodeGetState2_clbk = unsigned int (WINAPIV*)(struct Us_FSM_Node*, Us_FSM_NodeGetState2_ptr);
        using Us_FSM_NodeInit4_ptr = void (WINAPIV*)(struct Us_FSM_Node*);
        using Us_FSM_NodeInit4_clbk = void (WINAPIV*)(struct Us_FSM_Node*, Us_FSM_NodeInit4_ptr);
        using Us_FSM_NodeSetLoopTime6_ptr = void (WINAPIV*)(struct Us_FSM_Node*, unsigned int);
        using Us_FSM_NodeSetLoopTime6_clbk = void (WINAPIV*)(struct Us_FSM_Node*, unsigned int, Us_FSM_NodeSetLoopTime6_ptr);
        using Us_FSM_NodeSetParent8_ptr = void (WINAPIV*)(struct Us_FSM_Node*, struct Us_FSM_Node*);
        using Us_FSM_NodeSetParent8_clbk = void (WINAPIV*)(struct Us_FSM_Node*, struct Us_FSM_Node*, Us_FSM_NodeSetParent8_ptr);
        using Us_FSM_NodeSetState10_ptr = void (WINAPIV*)(struct Us_FSM_Node*, unsigned int);
        using Us_FSM_NodeSetState10_clbk = void (WINAPIV*)(struct Us_FSM_Node*, unsigned int, Us_FSM_NodeSetState10_ptr);
        
        using Us_FSM_Nodector_Us_FSM_Node12_ptr = void (WINAPIV*)(struct Us_FSM_Node*);
        using Us_FSM_Nodector_Us_FSM_Node12_clbk = void (WINAPIV*)(struct Us_FSM_Node*, Us_FSM_Nodector_Us_FSM_Node12_ptr);
        
        using Us_FSM_Nodedtor_Us_FSM_Node14_ptr = void (WINAPIV*)(struct Us_FSM_Node*);
        using Us_FSM_Nodedtor_Us_FSM_Node14_clbk = void (WINAPIV*)(struct Us_FSM_Node*, Us_FSM_Nodedtor_Us_FSM_Node14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _event_participant_classrefine
    {
        unsigned __int16 nSock;
        unsigned int nAvatorSerial;
        bool bChange;
        unsigned int dwRefineDate;
        char nCurRefineCnt;
    public:
        bool IsChanged();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_event_participant_classrefine, 20>(), "_event_participant_classrefine");
END_ATF_NAMESPACE

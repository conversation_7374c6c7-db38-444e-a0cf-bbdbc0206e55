// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBattleTournamentInfo.hpp>
#include <CCharacter.hpp>
#include <CCheckSum.hpp>
#include <CConnNumPHMgr.hpp>
#include <CFrameRate.hpp>
#include <CGameObject.hpp>
#include <CItemLootTable.hpp>
#include <CItemUpgradeTable.hpp>
#include <CLogFile.hpp>
#include <CMainThreadVtbl.hpp>
#include <CMapData.hpp>
#include <CMonsterSPGroupTable.hpp>
#include <CMsgProcess.hpp>
#include <CMyTimer.hpp>
#include <CNetIndexList.hpp>
#include <CNotifyNotifyRaceLeaderSownerUTaxrate.hpp>
#include <COreCuttingTable.hpp>
#include <CRFWorldDatabase.hpp>
#include <CRecordData.hpp>
#include <GuildCreateEventInfo.hpp>
#include <RFEventBase.hpp>
#include <TimeLimitMgr.hpp>
#include <_AIOC_A_MACRODATA.hpp>
#include <_AVATOR_DATA.hpp>
#include <_BUDDY_DB_BASE.hpp>
#include <_CLID.hpp>
#include <_CRYMSG_DB_BASE.hpp>
#include <_CUTTING_DB_BASE.hpp>
#include <_DB_QRY_SYN_DATA.hpp>
#include <_GLBID.hpp>
#include <_INVEN_DB_BASE.hpp>
#include <_ITEMCOMBINE_DB_BASE.hpp>
#include <_LINK_DB_BASE.hpp>
#include <_NOT_ARRANGED_AVATOR_DB.hpp>
#include <_PCBANG_FAVOR_ITEM_DB_BASE.hpp>
#include <_PCBANG_PLAY_TIME.hpp>
#include <_POTION_NEXT_USE_TIME_DB_BASE.hpp>
#include <_PVPPOINT_LIMIT_DB_BASE.hpp>
#include <_PVP_ORDER_VIEW_DB_BASE.hpp>
#include <_QUEST_DB_BASE.hpp>
#include <_REGED.hpp>
#include <_REGED_AVATOR_DB.hpp>
#include <_SFCONT_DB_BASE.hpp>
#include <_SRAND.hpp>
#include <_SUPPLEMENT_DB_BASE.hpp>
#include <_SYSTEMTIME.hpp>
#include <_TIMELIMITINFO_DB_BASE.hpp>
#include <_TRADE_DB_BASE.hpp>
#include <_TRUNK_DB_BASE.hpp>
#include <_UNIT_DB_BASE.hpp>
#include <_WAIT_ENTER_ACCOUNT.hpp>
#include <_db_cash_limited_sale.hpp>
#include <_db_golden_box_item.hpp>
#include <_economy_history_data.hpp>
#include <_mob_message.hpp>
#include <_object_id.hpp>
#include <_qry_case_gm_greetingmsg.hpp>
#include <_qry_case_guild_greetingmsg.hpp>
#include <_qry_case_guildroom_insert.hpp>
#include <_qry_case_guildroom_update.hpp>
#include <_qry_case_race_greetingmsg.hpp>
#include <_qry_case_sendwebracebosssms.hpp>
#include <_qry_case_update_guildmaster.hpp>
#include <_server_rate_realtime_load.hpp>
#include <_worlddb_economy_history_info_array.hpp>
#include <_worlddb_sf_delay_info.hpp>
#include <qry_case_cash_limsale.hpp>
#include <qry_case_select_golden_box_item.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMainThread
    {
        CMainThreadVtbl *vfptr;
        _SRAND m_Rand;
        _WAIT_ENTER_ACCOUNT m_WaitEnterAccount[2532];
        CRFWorldDatabase *m_pWorldDB;
        CFrameRate m_MainFrameRate;
        CFrameRate m_DBFrameRate;
        CMsgProcess m_GameMsg;
        CConnNumPHMgr m_MgrConnNum;
        CConnNumPHMgr m_HisMainFPS;
        CConnNumPHMgr m_HisSendFPS;
        CConnNumPHMgr m_HisDataFPS;
        CMyTimer m_tmServerState;
        bool m_bVerCheck;
        CMyTimer m_tmEconomyState;
        int m_tmDbUpdate;
        _DB_QRY_SYN_DATA m_DBQrySynData[12660];
        CNetIndexList m_listDQSData;
        CNetIndexList m_listDQSDataComplete;
        CNetIndexList m_listDQSDataEmpty;
        CCheckSum m_CheckSum;
        int m_nLimUserNum;
        char m_szWorldName[33];
        char m_wszWorldName[33];
        char m_wszMainGreetingMsg[256];
        char m_wszRaceGreetingMsg[3][256];
        char m_wszGMName[17];
        char m_wszBossName[3][17];
        bool m_bAwayPartyConsumeItem;
        char m_strAwayPartyItemCode[64];
        bool m_bAwayPartyConsumeMoney;
        unsigned int m_dwAwayPartyMoney;
        char m_strAllRaceChatItemCode[64];
        bool m_bAllRaceChatItemConsume;
        bool m_bAllRaceChatMoneyConsume;
        unsigned int m_dwAllRaceChatMoney;
        char m_byWorldCode;
        bool m_bWorldOpen;
        bool m_bWorldService;
        char m_szWorldDBName[64];
        unsigned int m_dwMessengerIP;
        unsigned int m_dwAccountIP;
        unsigned int m_dwCheckAccountOldTick;
        CMyTimer m_tmrCheckAvator;
        CMyTimer m_tmrCheckLoop;
        CMyTimer m_tmrAccountPing;
        CMyTimer m_tmrStateMsgGotoWeb;
        CMyTimer m_tmrCheckRadarDelay;
        int m_bFreeServer;
        bool m_bRuleThread;
        bool m_bDQSThread;
        CRecordData m_tblPlayer;
        CRecordData m_tblMonster;
        CRecordData m_tblNPC;
        CRecordData m_tblAnimus;
        CRecordData m_tblClass;
        CRecordData m_tblExp;
        CRecordData m_tblGrade;
        CItemLootTable m_tblItemLoot;
        COreCuttingTable m_tblOreCutting;
        CRecordData m_tblItemMakeData;
        CRecordData m_tblItemCombineData;
        CRecordData m_tblItemExchangeData;
        CItemUpgradeTable m_tblItemUpgrade;
        CRecordData m_tblItemData[37];
        CRecordData m_tblEffectData[4];
        CRecordData m_tblUnitPart[6];
        CRecordData m_tblUnitBullet;
        CRecordData m_tblUnitFrame;
        CRecordData m_tblEditData;
        CRecordData m_MonsterBaseSPData;
        CMonsterSPGroupTable m_MonsterSPGroupTable;
        CLogFile m_logBillCheck;
        CLogFile m_logSystemError;
        CLogFile m_logLoadingError;
        CLogFile m_logDungeon;
        CLogFile m_logKillMon;
        CLogFile m_logServerState;
        CLogFile m_logDTrade;
        CLogFile m_logGuild;
        CLogFile m_logDQS;
        CLogFile m_logRename;
        CLogFile m_logAutoTrade;
        CLogFile m_logEvent;
        CLogFile m_logMove;
        CLogFile m_logSave;
        CLogFile m_logReturnGate;
        CLogFile m_logHack;
        CLogFile m_logPvP;
        CLogFile m_logMonNum;
        CMyTimer m_tmForceUserExit;
        int m_nForceExitSocketIndexOffset;
        bool m_bServerClosing;
        bool m_bCheckOverTickCount;
        int m_nSleepTerm;
        int m_nSleepValue;
        int m_nSleepIgnore;
        bool m_bCheckSumActive;
        char m_byWebAgentServerNetInx;
        bool m_bConnectedWebAgentServer;
        char m_byControllServerNetInx;
        bool m_bConnectedControllServer;
        int m_iOldDay;
        bool m_bServiceKeyPass;
        char m_byWorldType;
        bool m_bReleaseServiceMode;
        bool m_bExcuteService;
        RFEventBase *m_pRFEvent_ClassRefine;
        CNotifyNotifyRaceLeaderSownerUTaxrate m_kEtcNotifyInfo;
        CBattleTournamentInfo m_BattleTournamentInfo;
        GuildCreateEventInfo m_GuildCreateEventInfo;
        _server_rate_realtime_load m_ServerRateLoad;
        TimeLimitMgr *m_pTimeLimitMgr;
        CMyTimer m_tmCheckForceClose;
        unsigned int m_dwStartNPCQuestCnt[3];
        _mob_message *m_MobMessage;
        unsigned int m_dwServerResetToken;
        unsigned int m_dwCheatSetPlayTime;
        unsigned __int16 m_dwCheatSetScanerCnt;
        unsigned __int16 m_dwCheatSetLevel;
    public:
        void AccountServerLogin();
        void AddGuildBattleSchdule(struct _DB_QRY_SYN_DATA* pData);
        void AddPassablePacket();
        void Alive_Char_Complete(struct _DB_QRY_SYN_DATA* pData);
        CMainThread();
        void ctor_CMainThread();
        bool CashDBInit(char* szIP, char* szDBName, char* szAccount, char* szPassword, unsigned int dwPort);
        void CheckAccountLineState();
        void CheckAvatorState();
        void CheckConnNumLog();
        void CheckDayChangedPvpPointClear();
        bool CheckDefine();
        void CheckForceClose();
        void CheckRadarItemDelay();
        void CheckServerRateINIFile();
        void CheckServiceableTime();
        void CompleteLoadGuildBattleTotalRecord(char byRet, char* pLoadData);
        void CompleteUpdatePlayerVoteInfo(char* pData);
        void CompleteUpdateServerToken(char* pData);
        void CompleteUpdateSetLimitRun(char byRet, char* pData);
        void CompleteUpdateVoteAvailable(char* pData);
        void Complete_Select_RegeAvator_For_Lobby_Logout(char* pSheet);
        void Complete_db_Update_Data_For_Post_Send(char* pSheet);
        void Complete_db_Update_Data_For_Trade(char* pSheet);
        void ContUserSaveJobCheck();
        void Cont_UserSave_Complete(struct _DB_QRY_SYN_DATA* pData);
        unsigned int CreateDataResetToken(struct _SYSTEMTIME* tm);
        void CreateSelectCharacterLogTable(char byMonth);
        void DQSCompleteProcess();
        static void DQSThread(void* pv);
        bool DataFileInit();
        bool DatabaseInit(char* pszDBName, char* pszDBIP);
        void Delete_Avator_Complete(struct _DB_QRY_SYN_DATA* pData);
        void EndServer();
        void ForceCloseUserInTiming();
        struct CGameObject* GetChar(char* pszCharName);
        struct CGameObject* GetCharW(char* wpszCharName);
        struct CGameObject* GetObjectA(struct _object_id* pObjID);
        struct CGameObject* GetObjectA(int kind, int id, int index);
        struct CGameObject* GetObjectExpand(struct _object_id* pObjID, char* szCharName, uint16_t wSearchIndex);
        void GetTommorrowStr(char* szTommorrow);
        void InAtradTaxMoney(struct _DB_QRY_SYN_DATA* p);
        void InGuildbattleCost(struct _DB_QRY_SYN_DATA* pData);
        void InGuildbattleRewardMoney(struct _DB_QRY_SYN_DATA* pData);
        bool Init();
        void Insert_Avator_Complete(struct _DB_QRY_SYN_DATA* pData);
        bool IsExcuteService();
        bool IsReleaseServiceMode();
        bool IsTestServer();
        int LoadINI();
        void LoadItemConsumeINI();
        bool LoadLimitInfo();
        bool LoadServerRateINIFile();
        int LoadWorldInfoINI();
        int LoadWorldSystemINI();
        void Load_Content_Complete(char* pData);
        void Load_PostStorage_Complete(char* pData);
        void Load_ReturnPost_Complete(char* pData);
        void Lobby_Account_Complete(struct _DB_QRY_SYN_DATA* pData);
        void Logout_Account_Complete(struct _DB_QRY_SYN_DATA* pData);
        void MakeSystemTower();
        void ManageClientLimitRunRequest(char* pBuf);
        bool NetworkInit();
        bool ObjectInit();
        void OnDQSRun();
        void OnRun();
        void OutDestGuildbattleCost(struct _DB_QRY_SYN_DATA* pData);
        void OutSrcGuildbattleCost(struct _DB_QRY_SYN_DATA* pData);
        void PingToAccount();
        struct _DB_QRY_SYN_DATA* PushDQSData(unsigned int dwAccountSerial, struct _CLID* pidWorld, char byQryCase, char* pQryData, int nSize);
        void PushResetServerToken();
        bool Push_ChargeItem(unsigned int dwSerial, unsigned int dwK, unsigned int dwD, unsigned int dwU, char byType);
        void QryCaseAddpvppoint(struct _DB_QRY_SYN_DATA* pData);
        void Reged_Avator_Complete(struct _DB_QRY_SYN_DATA* pData);
        void Release();
        static void RuleThread(void* pv);
        void Select_Avator_Complete(struct _DB_QRY_SYN_DATA* pData);
        void SendWebRaceBossSMS(struct _DB_QRY_SYN_DATA* pData);
        void SerivceForceSet(bool bService);
        void SerivceSelfStart();
        void SerivceSelfStop();
        void ServerStateMsgGotoWebAgent();
        bool SetGlobalDataName();
        void SetServerRate();
        void UpdateGuildBattleDrawRankInfo(struct _DB_QRY_SYN_DATA* pData);
        void UpdateGuildBattleWinLoseRankInfo(struct _DB_QRY_SYN_DATA* pData);
        void UpdateLoadGuildBattleRank(struct _DB_QRY_SYN_DATA* pData);
        void UpdateReservedGuildBattleSchedule(struct _DB_QRY_SYN_DATA* pData);
        bool ValidMacAddress();
        bool _CheckGuildCheckSum(unsigned int dwSerial, char* wszGuildName, long double* dDalant, long double* dGold);
        bool _CheckTotalSales();
        bool _GameDataBaseInit();
        char _db_Check_NpcData(unsigned int dwSerial, struct _AVATOR_DATA* pAvatorData);
        char _db_GuildRoom_Insert(struct _qry_case_guildroom_insert* pSheet);
        bool _db_GuildRoom_Update(struct _qry_case_guildroom_update* pSheet);
        char _db_Load_Base(unsigned int dwSerial, struct _AVATOR_DATA* pCon);
        void _db_Load_BattleTournamentInfo();
        char _db_Load_Buddy(unsigned int dwSerial, struct _BUDDY_DB_BASE* pBuddy);
        char _db_Load_Cash_LimSale(struct qry_case_cash_limsale* pDbLimitedSale);
        char _db_Load_CryMsg(unsigned int dwSerial, struct _CRYMSG_DB_BASE* pBossCry);
        char _db_Load_General(unsigned int dwSerial, char byRaceCode, struct _AVATOR_DATA* pCon);
        char _db_Load_GoldBoxItem(struct qry_case_select_golden_box_item* pDbGoldenboxitem, int* pnDBSerial);
        char _db_Load_Inven(unsigned int dwSerial, int nBagNum, struct _INVEN_DB_BASE* pCon);
        char _db_Load_ItemCombineEx(unsigned int dwSerial, struct _ITEMCOMBINE_DB_BASE* pCombineEx);
        char _db_Load_MacroData(unsigned int dwSerial, struct _AIOC_A_MACRODATA* pMacro);
        char _db_Load_NpcQuest_History(unsigned int dwSerial, struct _QUEST_DB_BASE* pCon);
        char _db_Load_OreCutting(unsigned int dwSerial, struct _CUTTING_DB_BASE* pDbCutting);
        int _db_Load_PatriarchComm(char* pData);
        char _db_Load_PcBangFavor(unsigned int dwSerial, struct _PCBANG_FAVOR_ITEM_DB_BASE* pDbPcBangFavor);
        char _db_Load_PotionDelay(unsigned int dwSerial, struct _POTION_NEXT_USE_TIME_DB_BASE* pDbPotionDelay);
        char _db_Load_PrimiumPlayTime(unsigned int dwAccSerial, struct _PCBANG_PLAY_TIME* kData);
        char _db_Load_PvpOrderView(unsigned int dwSerial, struct _PVP_ORDER_VIEW_DB_BASE* kData);
        char _db_Load_PvpPointLimitData(unsigned int dwSerial, struct _PVPPOINT_LIMIT_DB_BASE* kData);
        char _db_Load_Quest(unsigned int dwSerial, struct _QUEST_DB_BASE* pCon);
        char _db_Load_SFDelayData(unsigned int dwSerial, struct _worlddb_sf_delay_info* pDbSFDelayInfo);
        char _db_Load_Start_NpcQuest_History(unsigned int dwSerial, char byRaceCode, struct _QUEST_DB_BASE* pCon);
        char _db_Load_Supplement(unsigned int dwSerial, struct _SUPPLEMENT_DB_BASE* pDbSupplement);
        char _db_Load_TimeLimitInfo(unsigned int dwAccSerial, struct _TIMELIMITINFO_DB_BASE* pDbTimeLimitInfo);
        char _db_Load_Trade(char byRace, unsigned int dwSerial, struct _TRADE_DB_BASE* pTrade);
        char _db_Load_Trunk(unsigned int dwSerial, unsigned int dwAccountSerial, char byRace, struct _TRUNK_DB_BASE* pTrunk);
        char _db_Load_UI(unsigned int dwSerial, struct _LINK_DB_BASE* pLink, struct _SFCONT_DB_BASE* pSfcont);
        char _db_Load_Unit(unsigned int dwSerial, struct _UNIT_DB_BASE* pCon);
        char _db_Select_RegeAvator_For_Lobby_Logout(char* pSheet);
        bool _db_Update_Base(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pSzQuery, bool bCheckLowHigh);
        bool _db_Update_Buddy(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pwszQuery);
        char _db_Update_Cash_LimSale(struct _db_cash_limited_sale* pNewData, struct _db_cash_limited_sale* pOldData);
        bool _db_Update_CryMsg(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pwszQuery);
        char _db_Update_Data_For_Post_Send(char* pSheet);
        char _db_Update_Data_For_Trade(char* pSheet);
        bool _db_Update_General(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pSzQuery, bool bCheckLowHigh);
        char _db_Update_GoldBoxItem(int nDBSerial, struct _db_golden_box_item* pNewData, struct _db_golden_box_item* pOldData);
        bool _db_Update_Inven(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pSzQuery);
        bool _db_Update_ItemCombineEx(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pSzQuery);
        bool _db_Update_MacroData(unsigned int dwSerial, struct _AIOC_A_MACRODATA* pMacro, struct _AIOC_A_MACRODATA* pOldMacro);
        bool _db_Update_NpcData(unsigned int dwSerial, struct _AVATOR_DATA* pAvatorData, char* pSzNpcQuery);
        bool _db_Update_NpcQuest_History(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pSzQuery);
        bool _db_Update_OreCutting(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* szOreCuttingQuery, int nSize);
        bool _db_Update_PcBangFavor(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* szPcBangFavorQuery, int nSize);
        bool _db_Update_PotionDelay(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* szPotionDelayQuery, int nSize);
        bool _db_Update_PrimiumPlayTime(unsigned int dwAccSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* szQuery, char* szError);
        bool _db_Update_PvpOrderView(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* szQuery, char* szError);
        bool _db_Update_PvpPointLimit(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* szQuery, char* szError);
        bool _db_Update_Quest(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pSzQuery);
        bool _db_Update_SFDelayData(unsigned int dwSerial, struct _AVATOR_DATA* pNewData);
        char _db_Update_Set_Limit_Run();
        bool _db_Update_Start_NpcQuest_History(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData);
        bool _db_Update_Supplement(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* szSupplementQuery, int nSize);
        char _db_Update_TimeLimitInfo(unsigned int dwAccSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* szTimeLimitInfoQuery, int nSize);
        bool _db_Update_Trunk(unsigned int dwAccountSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pwszQuery);
        bool _db_Update_Trunk_Extend(unsigned int dwAccountSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pwszQuery);
        bool _db_Update_UI(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pSzQuery);
        bool _db_Update_Unit(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pSzQuery);
        void _db_complete_event_classrefine(uint16_t wSock, unsigned int dwAvatorSerial, char byRefinedCnt, unsigned int dwRefineDate);
        void _db_complete_update_event_classrefine(uint16_t wSock, unsigned int dwAvatorSerial);
        int _db_init_classrefine_count();
        char _db_load_event_classrefine(unsigned int dwAvatorSerial, char* byRefinedCnt, unsigned int* dwRefineDate);
        char _db_load_losebattlecount(unsigned int dwSerial, struct _AVATOR_DATA* pCon);
        char _db_load_punishment(unsigned int dwSerial, struct _AVATOR_DATA* pCon);
        char _db_load_raceboss(unsigned int dwSerial, struct _AVATOR_DATA* pCon);
        char _db_update_event_classrefine(uint16_t wSock, unsigned int dwAvatorSerial, char byRefinedCnt, unsigned int dwRefineDate);
        bool _db_update_inven_AMP(unsigned int dwAvatorSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, char* pszQuery);
        bool check_dbsyn_data_size();
        bool check_item_code_index();
        bool check_loaded_data();
        char check_min_max_guild_money(unsigned int dwGuildSerial, long double* pdDalant, long double* pdGold);
        char db_Add_PvpPoint(unsigned int dwSerial, unsigned int dwPoint, unsigned int dwCashBag);
        char db_Delete_Avator(unsigned int dwSerial, char byRaceCode);
        char db_GM_GreetingMsg(struct _qry_case_gm_greetingmsg* pSheet);
        char db_GUILD_GreetingMsg(struct _qry_case_guild_greetingmsg* pSheet);
        char db_Insert_Avator(unsigned int dwAccountSerial, char* pszAccount, struct _REGED_AVATOR_DB* pCharDB, unsigned int* pdwAvatorSerial);
        char db_Insert_ChangeClass_AfterInitClass(unsigned int dwCharacSerial, char byType, char* szPrevClassCode, char* szNextClassCode, int nClassInitCnt, char byLastClassGrade, uint16_t dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec);
        char db_Insert_CharacSelect_Log(unsigned int dwAccountSerial, char* szAccount, unsigned int dwCharacSerial, char* pwszCharacName, uint16_t dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec);
        char db_Insert_Economy_History(unsigned int dwDate, struct _worlddb_economy_history_info_array::_worlddb_economy_history_info* pEconomyData);
        char db_Insert_Item(unsigned int dwSerial, unsigned int dwItemCodeK, unsigned int dwItemCodeD, unsigned int dwItemCodeU, char byType);
        char db_Insert_guild(unsigned int* dwSerial, char* pwszGuildName, char byRace, unsigned int* dwGuildSerial);
        bool db_LoadGreetingMsg();
        char db_Load_Avator(unsigned int dwSerial, unsigned int dwAccountSerial, struct _AVATOR_DATA* pData, bool* pbAddItem, unsigned int* pdwAddDalant, unsigned int* pdwAddGold, bool* pbTrunkAddItem, char* pbyTrunkOldSlot, long double* pdTrunkOldDalant, long double* pdTrunkOldGold, bool* pbCreateTrunkFree, bool* pbExtTrunkAddItem, char* pbyExtTrunkOldSlot, bool bAll, unsigned int* pdwCheckSum);
        char db_Load_Content(char* pData);
        char db_Load_PostStorage(char* pData);
        char db_Load_ReturnPost(char* pData);
        char db_Log_AvatorLevel(unsigned int dwTotalPlayMin, unsigned int dwSerial, char byLv);
        char db_Log_UserNum(int nAveragePerHour, int nMaxPerHour);
        char db_RACE_GreetingMsg(struct _qry_case_race_greetingmsg* pSheet);
        char db_Reged_Avator(unsigned int dwAccountSerial, struct _REGED* pRegedList, struct _NOT_ARRANGED_AVATOR_DB* pArrangedList, char* pszIP);
        char db_Select_Economy_History(struct _economy_history_data* pCurData, int* pnCurMgrValue, int* pnNextMgrValue, struct _economy_history_data* pHisData, int* pHistoryNum, unsigned int dwDate);
        char db_Update_Avator(unsigned int dwSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData, bool bCheckLowHigh);
        char db_Update_PostStorage(unsigned int dwAvatorSerial, struct _AVATOR_DATA* pNewData, struct _AVATOR_DATA* pOldData);
        char db_Update_PvpInfo(unsigned int dwSerial, char byLevel, int16_t* pzClassHistory, long double dPvpPoint);
        char db_buy_emblem(unsigned int dwGuildSerial, int nEmblemDalant, unsigned int dwEmblemBack, unsigned int dwEmblemMark, unsigned int dwSuggestorSerial, long double* dTotalDalant, long double* dTotalGold, char* byDate, char* pwszName, char* pbyProcRet);
        char db_char_set_alive(unsigned int dwAccountSerial, char byCase, unsigned int dwSerial, char* pwszName, char bySlot, struct _REGED* pAliveAvator);
        char db_disjoint_guild(unsigned int dwGuildSerial);
        char db_input_guild_money(unsigned int dwPusherSerial, unsigned int dwGuildSerial, unsigned int dwAddDalant, unsigned int dwAddGold, long double* dTotalDalant, long double* dTotalGold, char* byDate, char* pwszName);
        char db_input_guild_money_atradetax(unsigned int dwPusherSerial, unsigned int dwGuildSerial, unsigned int dwAddDalant, long double* dTotalDalant, long double* dTotalGold, char* byDate);
        char db_output_guild_money(unsigned int dwPusherSerial, unsigned int dwGuildSerial, unsigned int dwSubDalant, unsigned int dwSubGold, long double* dTotalDalant, long double* dTotalGold, char* byDate, char* pwszName, char* pbyProcRet);
        char db_sendwebracebosssms(struct _qry_case_sendwebracebosssms* pSheet);
        char db_update_guildmaster(struct _qry_case_update_guildmaster* pSheet);
        char db_update_guildmember_add(unsigned int dwAvatorSerial, unsigned int dwGuildSerial, char byGrade, int nMemberNum);
        char db_update_guildmember_del(unsigned int dwAvatorSerial, unsigned int dwGuildSerial, int nMemberNum);
        void gm_DisplayAll();
        void gm_DisplaymodeChange();
        void gm_DungeonLoad();
        void gm_MainThreadControl();
        void gm_MapChange(struct CMapData* pMap);
        bool gm_MonsterInit(struct CCharacter* pExt);
        void gm_ObjectSelect();
        void gm_PreCloseAnn();
        void gm_ServerClose();
        void gm_UpdateMap();
        void gm_UpdateObject();
        void gm_UpdateServer();
        void gm_UserExit();
        void pc_AllUserGMNoticeInform(char* pwszMsg);
        void pc_AllUserKickInform();
        void pc_AllUserMsgInform(char* pwszMsg);
        void pc_AlterWorldService(bool bSerivce);
        void pc_CashDBInfoRecvResult(char* szIP, char* szDBName, char* szAccount, char* szPassword, unsigned int dwPort);
        void pc_ChatLockCommand(struct _CLID* pidLocal, uint16_t wBlockTimeH);
        void pc_EnterWorldResult(char byRetCode, struct _CLID* pidWorld);
        void pc_ForceCloseCommand(struct _CLID* pidWorld, bool bDirectly, char byKickType, unsigned int dwPushIP);
        void pc_OpenWorldFailureResult(char* szMsg);
        void pc_OpenWorldSuccessResult(char byWorldCode, char* pszDBName, char* pszDBIP);
        void pc_SetMainGreetingMsg(char* pwszGMName, char* pwszMsg);
        void pc_SetRaceGreetingMsg(int racenum, char* pwszBossName, char* pwszMsg);
        void pc_TaiwanBillingUserCertify(char* szAccount, char byCertify);
        void pc_TransIPKeyInform(unsigned int dwAccountSerial, char* pszAccountID, char byUserDgr, char bySubDgr, unsigned int* pdwKey, struct _GLBID* pgidGlobal, unsigned int dwClientIP, bool bChatLock, int16_t iType, char* szCMS, struct _SYSTEMTIME* pstEndDate, int lRemainTime, char byUILock, char* szUILockPW, char byUILockFailCnt, char* szAccountPW, char byUILock_HintIndex, char* uszUILock_HintAnswer, char byUILockFindPassFailCount, bool bIsPcBang, int nTrans, bool bAgeLimit, unsigned int* pdwRequestMoveCharacterSerialList, unsigned int* pdwTournamentCharacterSerialList);
        void pc_UILockInitResult(char* pMsg);
        void pc_UILockUpdateResult(char* pMsg);
        void pc_UserChatBlockResult(char byBlockResult, struct _CLID* pcidTarget, struct _CLID* pcidGM, int bLogin);
        ~CMainThread();
        void dtor_CMainThread();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CMainThread, *********>(), "CMainThread");
END_ATF_NAMESPACE

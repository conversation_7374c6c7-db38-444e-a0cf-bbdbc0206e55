// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _alter_cont_effect_time_zocl
    {
        struct __list
        {
            unsigned __int16 wEffectCode;
            __int16 zLeftSec;
        };
        char byEffectNum;
        __list List[8];
    public:
        _alter_cont_effect_time_zocl();
        void ctor__alter_cont_effect_time_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_alter_cont_effect_time_zocl, 33>(), "_alter_cont_effect_time_zocl");
END_ATF_NAMESPACE

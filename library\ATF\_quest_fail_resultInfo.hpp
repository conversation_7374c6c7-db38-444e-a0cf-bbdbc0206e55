// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_quest_fail_result.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _quest_fail_resultinit2_ptr = void (WINAPIV*)(struct _quest_fail_result*);
        using _quest_fail_resultinit2_clbk = void (WINAPIV*)(struct _quest_fail_result*, _quest_fail_resultinit2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _enter_world_result_zone
    {
        char byResult;
        char byUserGrade;
        char bySvrType;
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_enter_world_result_zone, 3>(), "_enter_world_result_zone");
END_ATF_NAMESPACE

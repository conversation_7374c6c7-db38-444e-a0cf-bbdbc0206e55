// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Iosb.hpp>
#include <std__ios_baseVtbl.hpp>
#include <std__locale.hpp>
#include <std__runtime_error.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct  ios_base : _Iosb
        {
            enum event
            {
                erase_event = 0x0,
                imbue_event = 0x1,
                copyfmt_event = 0x2,
            };
            struct _Iosarray
            {
                _Iosarray *_Next;
                int _Index;
                int _Lo;
                void *_Vp;
            };
            struct _Fnarray
            {
                _Fnarray *_Next;
                int _Index;
                void (WINAPIV *_Pfn)(event, ios_base *, int);
            };
            struct  failure : runtime_error
            {
            };
            struct  Init
            {
            };
            ios_baseVtbl *vfptr;
            unsigned __int64 _Stdstr;
            int _Mystate;
            int _Except;
            int _Fmtfl;
            __int64 _Prec;
            __int64 _Wide;
            _Iosarray *_Arr;
            _Fnarray *_Calls;
            locale *_Ploc;
        public:
            bool eof()
            {
                using org_ptr = bool (WINAPIV*)(struct ios_base*);
                return (org_ptr(0x140676356L))(this);
            };
            int64_t flags()
            {
                using org_ptr = int64_t (WINAPIV*)(struct ios_base*);
                return (org_ptr(0x1406762c6L))(this);
            };
            bool good()
            {
                using org_ptr = bool (WINAPIV*)(struct ios_base*);
                return (org_ptr(0x140676308L))(this);
            };
            int64_t width(int64_t arg_0)
            {
                using org_ptr = int64_t (WINAPIV*)(struct ios_base*, int64_t);
                return (org_ptr(0x1406762d2L))(this, arg_0);
            };
            int64_t width()
            {
                using org_ptr = int64_t (WINAPIV*)(struct ios_base*);
                return (org_ptr(0x1406762f6L))(this);
            };
        };
    }; // end namespace std
END_ATF_NAMESPACE

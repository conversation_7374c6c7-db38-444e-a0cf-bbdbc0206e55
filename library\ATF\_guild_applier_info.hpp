// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _guild_applier_info
    {
        struct CPlayer *pPlayer;
        unsigned int dwApplyTime;
    public:
        bool IsFill();
        _guild_applier_info();
        void ctor__guild_applier_info();
        void init();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

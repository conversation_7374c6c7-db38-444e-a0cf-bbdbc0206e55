// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _OBJECTS_AND_NAME_W
    {
        unsigned int ObjectsPresent;
        _SE_OBJECT_TYPE ObjectType;
        wchar_t *ObjectTypeName;
        wchar_t *InheritedObjectTypeName;
        wchar_t *ptstrName;
    };
END_ATF_NAMESPACE

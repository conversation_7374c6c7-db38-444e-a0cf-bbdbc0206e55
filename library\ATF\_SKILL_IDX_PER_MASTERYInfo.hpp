// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SKILL_IDX_PER_MASTERY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _SKILL_IDX_PER_MASTERYctor__SKILL_IDX_PER_MASTERY2_ptr = void (WINAPIV*)(struct _SKILL_IDX_PER_MASTERY*);
        using _SKILL_IDX_PER_MASTERYctor__SKILL_IDX_PER_MASTERY2_clbk = void (WINAPIV*)(struct _SKILL_IDX_PER_MASTERY*, _SKILL_IDX_PER_MASTERYctor__SKILL_IDX_PER_MASTERY2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

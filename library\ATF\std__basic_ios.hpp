// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__basic_ostream.hpp>
#include <std__basic_streambuf.hpp>
#include <std__ios_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 8)
        template<>
        struct  basic_ios<char,char_traits<char> > : ios_base
        {
            basic_streambuf<char,char_traits<char> > *_Mystrbuf;
            basic_ostream<char,char_traits<char> > *_Tiestr;
            char _Fillch;
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE
#include <std__basic_ostream.hpp>
#include <std__basic_streambuf.hpp>
#include <std__ios_base.hpp>



START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 8)
        template<>
        struct  basic_ios<wchar_t,char_traits<wchar_t> > : ios_base
        {
            basic_streambuf<wchar_t,char_traits<wchar_t> > *_Mystrbuf;
            basic_ostream<wchar_t,char_traits<wchar_t> > *_Tiestr;
            wchar_t _Fillch;
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE

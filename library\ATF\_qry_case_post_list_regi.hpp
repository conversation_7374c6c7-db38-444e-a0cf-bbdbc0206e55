// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_post_list_regi
    {
        struct __list
        {
            unsigned int dwIndex;
            bool bRet;
            char bySendRace;
            char bySenderDgr;
            char byState;
            unsigned int dwSenderSerial;
            char wszSendName[17];
            char wszRecvName[17];
            char wszTitle[21];
            char wszContent[201];
            _INVENKEY key;
            unsigned __int64 dwDur;
            unsigned int dwUpt;
            unsigned __int64 lnUID;
            unsigned int dwGold;
        public:
            __list();
            void ctor___list();
        };
        unsigned int dwCount;
        __list List[20];
    public:
        _qry_case_post_list_regi();
        void ctor__qry_case_post_list_regi();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_post_list_regi, 6088>(), "_qry_case_post_list_regi");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace _ATL_SAFE_ALLOCA_IMPL
        {
            template<>
            struct CAtlSafeAllocBufferManager<ATL::CCRTAllocator>
            {
                template<>
                struct CAtlSafeAllocBufferNode
                {
                    CAtlSafeAllocBufferNode *m_pNext;
                    char _pad[8];
                public:
                    void* GetData()
                    {
                        using org_ptr = void* (WINAPIV*)(struct CAtlSafeAllocBufferNode*);
                        return (org_ptr(0x1400273d0L))(this);
                    };
                };
                CAtlSafeAllocBufferNode *m_pHead;
            public:
                void* Allocate(uint64_t nRequestedSize)
                {
                    using org_ptr = void* (WINAPIV*)(struct CAtlSafeAllocBufferManager<ATL::CCRTAllocator>*, uint64_t);
                    return (org_ptr(0x140026c30L))(this, nRequestedSize);
                };
                CAtlSafeAllocBufferManager()
                {
                    using org_ptr = void (WINAPIV*)(struct CAtlSafeAllocBufferManager<ATL::CCRTAllocator>*);
                    (org_ptr(0x140026c10L))(this);
                };
                void ctor_CAtlSafeAllocBufferManager()
                {
                    using org_ptr = void (WINAPIV*)(struct CAtlSafeAllocBufferManager<ATL::CCRTAllocator>*);
                    (org_ptr(0x140026c10L))(this);
                };
                ~CAtlSafeAllocBufferManager()
                {
                    using org_ptr = void (WINAPIV*)(struct CAtlSafeAllocBufferManager<ATL::CCRTAllocator>*);
                    (org_ptr(0x140026d20L))(this);
                };
                void dtor_CAtlSafeAllocBufferManager()
                {
                    using org_ptr = void (WINAPIV*)(struct CAtlSafeAllocBufferManager<ATL::CCRTAllocator>*);
                    (org_ptr(0x140026d20L))(this);
                };
            };    
            static_assert(ATF::checkSize<ATL::_ATL_SAFE_ALLOCA_IMPL::CAtlSafeAllocBufferManager<ATL::CCRTAllocator>, 8>(), "ATL::_ATL_SAFE_ALLOCA_IMPL::CAtlSafeAllocBufferManager<ATL::CCRTAllocator>");
        }; // end namespace _ATL_SAFE_ALLOCA_IMPL
    }; // end namespace ATL
END_ATF_NAMESPACE

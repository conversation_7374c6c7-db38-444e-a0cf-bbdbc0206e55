// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGameObjectVtbl.hpp>
#include <CMapData.hpp>
#include <CRect.hpp>
#include <_100_per_random_table.hpp>
#include <_base_fld.hpp>
#include <_object_create_setdata.hpp>
#include <_object_id.hpp>
#include <_object_list_point.hpp>


START_ATF_NAMESPACE
    struct CGameObject
    {
        CGameObjectVtbl *vfptr;
        struct _base_fld *m_pRecordSet;
        _object_id m_ObjID;
        unsigned int m_dwObjSerial;
        bool m_bLive;
        int m_nTotalObjIndex;
        bool m_bCorpse;
        bool m_bMove;
        bool m_bStun;
        bool m_bMapLoading;
        unsigned int m_dwLastSendTime;
        float m_fCurPos[3];
        float m_fAbsPos[3];
        int m_nScreenPos[2];
        float m_fOldPos[3];
        struct CMapData *m_pCurMap;
        _100_per_random_table m_rtPer100;
        int m_nCirclePlayerNum;
        unsigned __int16 m_wMapLayerIndex;
        _object_list_point m_SectorPoint;
        _object_list_point m_SectorNetPoint;
        unsigned int m_dwNextFreeStunTime;
        unsigned int m_dwOldTickBreakTranspar;
        bool m_bBreakTranspar;
        bool m_bMaxVision;
        bool *m_bPlayerCircleList;
        bool m_bObserver;
        unsigned int m_dwCurSec;
    public:
        void AlterSec();
        int AttackableHeight();
        void BeTargeted(struct CCharacter* pSeacher);
        CGameObject();
        void ctor_CGameObject();
        void CalcAbsPos();
        int CalcCirclePlayerNum(int nRange);
        int CalcCirclePlayerNum(int nRange, bool (WINAPIV* fnComp)(struct CGameObject*));
        uint16_t CalcCurHPRate();
        void CalcScrExtendPoint(struct CRect* prcWnd, struct CRect* prcExtend);
        void CalcScrNormalPoint(struct CRect* prcWnd);
        unsigned int CalcSecIndex();
        void CircleReport(char* pbyType, char* szMsg, int nMsgSize, bool bToOne);
        void CircleReport(char* pbyType, char* szMsg, int nMsgSize, unsigned int dwPassObjSerial, bool bToOne);
        bool Create(struct _object_create_setdata* pData);
        bool Destroy();
        bool FixTargetWhile(struct CCharacter* pkTarget, unsigned int dwMiliSecond);
        int GetAttackDP();
        int GetAttackLevel();
        float GetAttackRange();
        int GetAvoidRate();
        unsigned int GetCurSecNum();
        int GetDefFC(int nAttactPart, struct CCharacter* pAttChar, int* pnConvertPart);
        float GetDefFacing(int nPart);
        float GetDefGap(int nPart);
        int GetDefSkill(bool bBackAttackDamage);
        int GetFireTol();
        int GetGenAttackProb(struct CCharacter* pDst, int nPart, bool bBackAttack);
        int GetHP();
        int GetLevel();
        int GetMaxHP();
        char* GetObjName();
        int GetObjRace();
        int GetSoilTol();
        bool GetStun();
        int GetUseSectorRange();
        int GetWaterTol();
        float GetWeaponAdjust();
        int GetWeaponClass();
        float GetWidth();
        int GetWindTol();
        void Init(struct _object_id* pID);
        bool IsAttackableInTown();
        bool IsBeAttackedAble(bool bFirst);
        bool IsBeCirclePlayer(int nRange);
        bool IsBeDamagedAble(struct CCharacter* pAtter);
        bool IsCircleObject(int nRange, struct CGameObject* pObject);
        bool IsInTown();
        bool IsRecvableContEffect();
        bool IsRewardExp();
        bool Is_Battle_Mode();
        void Loop();
        void OnLoop();
        void OutOfSec();
        void RecvKillMessage(struct CCharacter* pDier);
        unsigned int RerangeSecIndex(unsigned int dwOld, unsigned int dwNew);
        void ResetSector(unsigned int dwOldSec, unsigned int dwNewSec);
        bool RobbedHP(struct CCharacter* pDst, int nDecHP);
        void SFContDelMessage(char byContCode, char byListIndex, bool bSend, bool bAura);
        void SFContInsertMessage(char byContCode, char byListIndex, bool bAura, struct CPlayer* pPlayerAct);
        void SFContUpdateTimeMessage(char byContCode, char byListIndex, int nLeftTime);
        bool SF_AllContDamageForceRemove_Once(struct CCharacter* pDstObj);
        bool SF_AllContHelpForceRemove_Once(struct CCharacter* pDstObj);
        bool SF_AllContHelpSkillRemove_Once(struct CCharacter* pDstObj);
        bool SF_AttHPtoDstFP_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ContDamageTimeInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ContHelpTimeInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ConvertMonsterTarget(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ConvertTargetDest(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_DamageAndStun(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_FPDec(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_HPInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_IncHPCircleParty(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_IncreaseDP(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_LateContDamageRemove_Once(struct CCharacter* pDstObj);
        bool SF_LateContHelpForceRemove_Once(struct CCharacter* pDstObj);
        bool SF_LateContHelpSkillRemove_Once(struct CCharacter* pDstObj);
        bool SF_MakePortalReturnBindPositionPartyMember(struct CCharacter* pDstObj, float fEffectValue, char* byRet);
        bool SF_MakeZeroAnimusRecallTimeOnce(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_OthersContHelpSFRemove_Once(float fEffectValue);
        bool SF_OverHealing_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_RecoverAllReturnStateAnimusHPFull(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ReleaseMonsterTarget(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_RemoveAllContHelp_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_Resurrect_Once(struct CCharacter* pDstObj);
        bool SF_ReturnBindPosition(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_SPDec(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_STInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_SelfDestruction(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_SkillContHelpTimeInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_Stun(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_TeleportToDestination(struct CCharacter* pDstObj, bool bStone);
        bool SF_TransDestHP(struct CCharacter* pDstObj, float fEffectValue, char* byRet);
        bool SF_TransMonsterHP(struct CCharacter* pDstObj, float fEffectValue);
        void SendMsg_BreakStop();
        void SendMsg_FixPosition(int n);
        void SendMsg_RealFixPosition(bool bCircle);
        void SendMsg_RealMovePoint(int n);
        void SendMsg_SetHPInform();
        void SendMsg_StunInform();
        void SetAttackPart(int nAttactPart);
        void SetBreakTranspar(bool bBreak);
        bool SetCurBspMap(struct CMapData* pMap);
        bool SetCurPos(float* pPos);
        void SetCurSecNum(unsigned int dwNewSecNum);
        int SetDamage(int nDam, struct CCharacter* pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn);
        bool SetHP(int nHP, bool bOver);
        void SetMaxVersion();
        void SetStun(bool bStun);
        bool UpdateSecList();
        void _ResetCirclePlayer();
        ~CGameObject();
        void dtor_CGameObject();
    };    
    static_assert(ATF::checkSize<CGameObject, 192>(), "CGameObject");
END_ATF_NAMESPACE

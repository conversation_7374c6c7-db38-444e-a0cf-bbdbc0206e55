// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CUnmannedTraderSubClassInfoVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CUnmannedTraderSubClassInfo
    {
        CUnmannedTraderSubClassInfoVtbl *vfptr;
        char m_szName[24];
        unsigned int m_dwID;
    public:
        CUnmannedTraderSubClassInfo(struct CUnmannedTraderSubClassInfo* lhs);
        void ctor_CUnmannedTraderSubClassInfo(struct CUnmannedTraderSubClassInfo* lhs);
        CUnmannedTraderSubClassInfo(unsigned int dwID);
        void ctor_CUnmannedTraderSubClassInfo(unsigned int dwID);
        struct CUnmannedTraderSubClassInfo* Copy(struct CUnmannedTraderSubClassInfo* lhs);
        bool GetGroupID(char byTableCode, uint16_t wItemTableIndex, char* bySubClass);
        unsigned int GetID();
        char* GetTypeName();
        bool LoadXML(struct TiXmlElement* pkElement, struct CLogFile* kLogger, unsigned int dwDivisionID, unsigned int dwClassID);
        ~CUnmannedTraderSubClassInfo();
        void dtor_CUnmannedTraderSubClassInfo();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

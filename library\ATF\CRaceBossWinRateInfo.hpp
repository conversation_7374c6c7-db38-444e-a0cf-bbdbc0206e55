// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBossWinRate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRaceBossWinRatector_CRaceBossWinRate2_ptr = void (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRatector_CRaceBossWinRate2_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRatector_CRaceBossWinRate2_ptr);
        using CRaceBossWinRateCompleteBossAccmulationWinRate4_ptr = void (WINAPIV*)(struct CRaceBossWinRate*, struct _qry_case_raceboss_accumulation_winrate*);
        using CRaceBossWinRateCompleteBossAccmulationWinRate4_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, struct _qry_case_raceboss_accumulation_winrate*, CRaceBossWinRateCompleteBossAccmulationWinRate4_ptr);
        using CRaceBossWinRateDestroy6_ptr = void (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRateDestroy6_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRateDestroy6_ptr);
        using CRaceBossWinRateInstance8_ptr = struct CRaceBossWinRate* (WINAPIV*)();
        using CRaceBossWinRateInstance8_clbk = struct CRaceBossWinRate* (WINAPIV*)(CRaceBossWinRateInstance8_ptr);
        using CRaceBossWinRateLoadBossAccmulationWinRate10_ptr = char (WINAPIV*)(struct CRaceBossWinRate*, struct _qry_case_raceboss_accumulation_winrate*);
        using CRaceBossWinRateLoadBossAccmulationWinRate10_clbk = char (WINAPIV*)(struct CRaceBossWinRate*, struct _qry_case_raceboss_accumulation_winrate*, CRaceBossWinRateLoadBossAccmulationWinRate10_ptr);
        using CRaceBossWinRateLoadBossCurrentWinRate12_ptr = bool (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRateLoadBossCurrentWinRate12_clbk = bool (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRateLoadBossCurrentWinRate12_ptr);
        using CRaceBossWinRateLoadDB14_ptr = bool (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRateLoadDB14_clbk = bool (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRateLoadDB14_ptr);
        using CRaceBossWinRateLoadINI16_ptr = bool (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRateLoadINI16_clbk = bool (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRateLoadINI16_ptr);
        using CRaceBossWinRateNotify18_ptr = void (WINAPIV*)(struct CRaceBossWinRate*, char);
        using CRaceBossWinRateNotify18_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, char, CRaceBossWinRateNotify18_ptr);
        using CRaceBossWinRateNotify20_ptr = void (WINAPIV*)(struct CRaceBossWinRate*, char, uint16_t);
        using CRaceBossWinRateNotify20_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, char, uint16_t, CRaceBossWinRateNotify20_ptr);
        using CRaceBossWinRateNotify22_ptr = void (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRateNotify22_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRateNotify22_ptr);
        using CRaceBossWinRateSaveINI24_ptr = bool (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRateSaveINI24_clbk = bool (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRateSaveINI24_ptr);
        using CRaceBossWinRateUpdateRaceBossWinRate26_ptr = void (WINAPIV*)(struct CRaceBossWinRate*, char);
        using CRaceBossWinRateUpdateRaceBossWinRate26_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, char, CRaceBossWinRateUpdateRaceBossWinRate26_ptr);
        using CRaceBossWinRateUpdateRaceBossWinRate28_ptr = void (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRateUpdateRaceBossWinRate28_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRateUpdateRaceBossWinRate28_ptr);
        using CRaceBossWinRateUpdateTotalCnt30_ptr = void (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRateUpdateTotalCnt30_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRateUpdateTotalCnt30_ptr);
        using CRaceBossWinRateUpdateWinCnt32_ptr = void (WINAPIV*)(struct CRaceBossWinRate*, char);
        using CRaceBossWinRateUpdateWinCnt32_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, char, CRaceBossWinRateUpdateWinCnt32_ptr);
        
        using CRaceBossWinRatedtor_CRaceBossWinRate36_ptr = void (WINAPIV*)(struct CRaceBossWinRate*);
        using CRaceBossWinRatedtor_CRaceBossWinRate36_clbk = void (WINAPIV*)(struct CRaceBossWinRate*, CRaceBossWinRatedtor_CRaceBossWinRate36_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

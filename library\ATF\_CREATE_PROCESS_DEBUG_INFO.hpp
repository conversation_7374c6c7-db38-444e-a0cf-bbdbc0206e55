// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _CREATE_PROCESS_DEBUG_INFO
    {
        void *hFile;
        void *hProcess;
        void *hThread;
        void *lpBaseOfImage;
        unsigned int dwDebugInfoFileOffset;
        unsigned int nDebugInfoSize;
        void *lpThreadLocalBase;
        unsigned int (WINAPIV *lpStartAddress)(void *);
        void *lpImageName;
        unsigned __int16 fUnicode;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

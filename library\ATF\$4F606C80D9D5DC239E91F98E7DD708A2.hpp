// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$028B7756A2EE9D0FC794C4C2CEAC0939.hpp>
#include <$0720ADDE9364F7ED5D0B2BDD67D39F24.hpp>
#include <$166B1F81F6EA96F97683A65F38FB1A59.hpp>
#include <$17BA733FE6B8BEB3CC80B954432F0727.hpp>
#include <$2516E53E690D8CC5659AAB7EDC49E664.hpp>
#include <$2CABE618CF1C2625A4479554E4967E0A.hpp>
#include <$31126B8528A05AF3606C6D495FD178E8.hpp>
#include <$3196835341B7747CEBAC56274C56725F.hpp>
#include <$31DBDBB5C8522AA951CC08B5BCDF769C.hpp>
#include <$3461A5A573CDCCB6C60C7D2847145B25.hpp>
#include <$425275FB7B5434BC85720BC50BA05A27.hpp>
#include <$4535B7E0DA0A2FB8E2AB12031406377E.hpp>
#include <$48802A31D3D0701BC13CEF9CEA041E7B.hpp>
#include <$4A435249DA9BA58B45910B7F754430A8.hpp>
#include <$4A630C69B4B1F82AF58B533C567D2FDB.hpp>
#include <$4C0EC2258454B893CE739DAE89D8DB7B.hpp>
#include <$50230A970D9734D4E9774CFC619DF0F6.hpp>
#include <$531E1B27497FB057F019E48D524479A8.hpp>
#include <$5669F5A4AD19EF2CB42A5602080CB0A6.hpp>
#include <$5C7199480B872EF5D95AF85BAAD1BB21.hpp>
#include <$5CA90CF57237397281FB12BFD52C1905.hpp>
#include <$5DF3780F58367B9F2537DF425A8A812E.hpp>
#include <$60747C4A2D2EBEBDFA25E6FDE38FFB70.hpp>
#include <$60E4D37D1F84587CCAA4F5EA0F9453FD.hpp>
#include <$6367D5097EC8A7504B1ED28E022F6230.hpp>
#include <$6690E87BFA1F41C7DB8500018D99BD62.hpp>
#include <$6810C0431CE7B8C44277107BC1E4A02A.hpp>
#include <$6857AE69DB7C952EEFD665431A7B503A.hpp>
#include <$6D336DA143C556260FF80C12817B08DB.hpp>
#include <$6DB36323059316E675433BB10D285009.hpp>
#include <$719B1B3FFBDBE2695D157A8478E90609.hpp>
#include <$745F72B1BB712DA8B7C4A54B320F0BA5.hpp>
#include <$76732A64405C0E287FCB302687644550.hpp>
#include <$78812D144EBA90DE31B13A991FB7A5BE.hpp>
#include <$7B1C2AA558A72DB3909F7F0B6C8C78B2.hpp>
#include <$7BBC4720490BEC0135F79C24FD110E35.hpp>
#include <$7E3CD2A3F28A1D98589E1ABB097DD9A4.hpp>
#include <$7F63C69334116FBAAE72975283C5BD43.hpp>
#include <$886F8AE9D7C8B1B861B6CA58D67B682A.hpp>
#include <$8E3C8731874D1B3BC66617C4DD3163A6.hpp>
#include <$8FB3C68451F3C0E120BBDF52173BA3E1.hpp>
#include <$97F5F5BA23CFC74B08C416B61972487A.hpp>
#include <$980D029DCC99044F2463A5C9CAF1F597.hpp>
#include <$9CA05AD428C5CE11A605E30716B6C83D.hpp>
#include <$A123DDE5155BABE8656CED1ACF96B10E.hpp>
#include <$A24CCB9C650A9CC3453F37B8C46067B8.hpp>
#include <$AAA92DA1D6E1B0E8462F69216B62027C.hpp>
#include <$AD8630CBC7A55BCFB532806489E2D4B2.hpp>
#include <$B0093AC2C62CD9A191152A2F079F59F8.hpp>
#include <$B35A59728DFC936764D4DA5E7F795482.hpp>
#include <$B644145F46FF1F932B106BFA6CC3F6D9.hpp>
#include <$B915257600B336FC783C14BECAF8BDCD.hpp>
#include <$BE56ADCB97A5F80C4E840DC3FCEE04D4.hpp>
#include <$BF8CC88CDF129BD6E4FA4ABA13F521AD.hpp>
#include <$C8872C9E496A5F106B62833CDD5C9B19.hpp>
#include <$CDA73A427DD2A30CDA26CA3394C33CF0.hpp>
#include <$D504B478025DE79B9E2E1DF6DD5C41FB.hpp>
#include <$D6BD858AB7A2318D79D1BE61F5BAB601.hpp>
#include <$DD1A186763504C5A4B16B82835CE3F33.hpp>
#include <$DF102B31B2A9296EC5F201FD7C6F7BC9.hpp>
#include <$E14ADF507523F7163DD0A80DFEB039A7.hpp>
#include <$E1D71D20C3D01115E20D79FAE9629CE8.hpp>
#include <$E5FC128E66C26C95254032BA69D36E74.hpp>
#include <$E7F48E2F4EAA3149E57D87E5D1B2C6B8.hpp>
#include <$E94952165EEA9EEA990FEB33603E1B19.hpp>
#include <$F077B4D3B321AFDA9989B898104BF92D.hpp>
#include <$F28A51B4B40B0D33C72915D739D42B4B.hpp>
#include <$F46D9090672738781A752404F4626ABF.hpp>
#include <$F7167AE7A8ABA03C094C204FB1564A28.hpp>
#include <$F7AD3C731BAE9FE9EC401882837B8DB3.hpp>
#include <$F843BCFCB8B642AED9B77EEE39B126D8.hpp>
#include <$F984BB6A5754EE500E2E7944DD7C75AC.hpp>
#include <tagDEC.hpp>


START_ATF_NAMESPACE
    union $4F606C80D9D5DC239E91F98E7DD708A2
    {
        $5DF3780F58367B9F2537DF425A8A812E __s0;
        tagDEC decVal;
        $F28A51B4B40B0D33C72915D739D42B4B __s2;
        $31126B8528A05AF3606C6D495FD178E8 __s3;
        $48802A31D3D0701BC13CEF9CEA041E7B __s4;
        $6D336DA143C556260FF80C12817B08DB __s5;
        $B644145F46FF1F932B106BFA6CC3F6D9 __s6;
        $5CA90CF57237397281FB12BFD52C1905 __s7;
        $E7F48E2F4EAA3149E57D87E5D1B2C6B8 __s8;
        $31DBDBB5C8522AA951CC08B5BCDF769C __s9;
        $6690E87BFA1F41C7DB8500018D99BD62 __s10;
        $50230A970D9734D4E9774CFC619DF0F6 __s11;
        $4C0EC2258454B893CE739DAE89D8DB7B __s12;
        $6DB36323059316E675433BB10D285009 __s13;
        $8E3C8731874D1B3BC66617C4DD3163A6 __s14;
        $A24CCB9C650A9CC3453F37B8C46067B8 __s15;
        $E94952165EEA9EEA990FEB33603E1B19 __s16;
        $8FB3C68451F3C0E120BBDF52173BA3E1 __s17;
        $DD1A186763504C5A4B16B82835CE3F33 __s18;
        $4A435249DA9BA58B45910B7F754430A8 __s19;
        $7B1C2AA558A72DB3909F7F0B6C8C78B2 __s20;
        $B915257600B336FC783C14BECAF8BDCD __s21;
        $3196835341B7747CEBAC56274C56725F __s22;
        $17BA733FE6B8BEB3CC80B954432F0727 __s23;
        $A123DDE5155BABE8656CED1ACF96B10E __s24;
        $60E4D37D1F84587CCAA4F5EA0F9453FD __s25;
        $C8872C9E496A5F106B62833CDD5C9B19 __s26;
        $028B7756A2EE9D0FC794C4C2CEAC0939 __s27;
        $5C7199480B872EF5D95AF85BAAD1BB21 __s28;
        $D6BD858AB7A2318D79D1BE61F5BAB601 __s29;
        $6810C0431CE7B8C44277107BC1E4A02A __s30;
        $3461A5A573CDCCB6C60C7D2847145B25 __s31;
        $0720ADDE9364F7ED5D0B2BDD67D39F24 __s32;
        $7F63C69334116FBAAE72975283C5BD43 __s33;
        $425275FB7B5434BC85720BC50BA05A27 __s34;
        $4535B7E0DA0A2FB8E2AB12031406377E __s35;
        $7E3CD2A3F28A1D98589E1ABB097DD9A4 __s36;
        $F984BB6A5754EE500E2E7944DD7C75AC __s37;
        $9CA05AD428C5CE11A605E30716B6C83D __s38;
        $B0093AC2C62CD9A191152A2F079F59F8 __s39;
        $719B1B3FFBDBE2695D157A8478E90609 __s40;
        $B35A59728DFC936764D4DA5E7F795482 __s41;
        $E14ADF507523F7163DD0A80DFEB039A7 __s42;
        $980D029DCC99044F2463A5C9CAF1F597 __s43;
        $97F5F5BA23CFC74B08C416B61972487A __s44;
        $60747C4A2D2EBEBDFA25E6FDE38FFB70 __s45;
        $7BBC4720490BEC0135F79C24FD110E35 __s46;
        $4A630C69B4B1F82AF58B533C567D2FDB __s47;
        $531E1B27497FB057F019E48D524479A8 __s48;
        $D504B478025DE79B9E2E1DF6DD5C41FB __s49;
        $6367D5097EC8A7504B1ED28E022F6230 __s50;
        $F077B4D3B321AFDA9989B898104BF92D __s51;
        $745F72B1BB712DA8B7C4A54B320F0BA5 __s52;
        $E1D71D20C3D01115E20D79FAE9629CE8 __s53;
        $166B1F81F6EA96F97683A65F38FB1A59 __s54;
        $76732A64405C0E287FCB302687644550 __s55;
        $5669F5A4AD19EF2CB42A5602080CB0A6 __s56;
        $E5FC128E66C26C95254032BA69D36E74 __s57;
        $886F8AE9D7C8B1B861B6CA58D67B682A __s58;
        $CDA73A427DD2A30CDA26CA3394C33CF0 __s59;
        $2516E53E690D8CC5659AAB7EDC49E664 __s60;
        $BF8CC88CDF129BD6E4FA4ABA13F521AD __s61;
        $2CABE618CF1C2625A4479554E4967E0A __s62;
        $AAA92DA1D6E1B0E8462F69216B62027C __s63;
        $F46D9090672738781A752404F4626ABF __s64;
        $F7167AE7A8ABA03C094C204FB1564A28 __s65;
        $AD8630CBC7A55BCFB532806489E2D4B2 __s66;
        $6857AE69DB7C952EEFD665431A7B503A __s67;
        $BE56ADCB97A5F80C4E840DC3FCEE04D4 __s68;
        $DF102B31B2A9296EC5F201FD7C6F7BC9 __s69;
        $F7AD3C731BAE9FE9EC401882837B8DB3 __s70;
        $F843BCFCB8B642AED9B77EEE39B126D8 __s71;
        $78812D144EBA90DE31B13A991FB7A5BE __s72;
    };
END_ATF_NAMESPACE

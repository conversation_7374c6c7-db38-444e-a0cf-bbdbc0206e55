// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    struct _QUOTA_LIMITS
    {
        unsigned __int64 PagedPoolLimit;
        unsigned __int64 NonPagedPoolLimit;
        unsigned __int64 MinimumWorkingSetSize;
        unsigned __int64 MaximumWorkingSetSize;
        unsigned __int64 PagefileLimit;
        _LARGE_INTEGER TimeLimit;
    };
END_ATF_NAMESPACE

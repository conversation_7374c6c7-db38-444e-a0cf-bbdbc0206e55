// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_log_change_class_after_init_class.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _log_change_class_after_init_classsize2_ptr = int (WINAPIV*)(struct _log_change_class_after_init_class*);
        using _log_change_class_after_init_classsize2_clbk = int (WINAPIV*)(struct _log_change_class_after_init_class*, _log_change_class_after_init_classsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

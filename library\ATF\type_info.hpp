// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <type_infoVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    const struct type_info
    {
        type_infoVtbl *vfptr;
        void *_m_data;
        char _m_d_name[1];
    public:
        char* _name_internal_method(struct __type_info_node* arg_0);
        type_info(struct type_info* rhs);
        void ctor_type_info(struct type_info* rhs);
        ~type_info();
        void dtor_type_info();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<type_info, 24>(), "type_info");
END_ATF_NAMESPACE

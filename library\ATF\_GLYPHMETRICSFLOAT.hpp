// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_POINTFLOAT.hpp>


START_ATF_NAMESPACE
    struct _GLYPHMETRICSFLOAT
    {
        float gmfBlackBoxX;
        float gmfBlackBoxY;
        _POINTFLOAT gmfptGlyphOrigin;
        float gmfCellIncX;
        float gmfCellIncY;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryPH.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryPHctor_CNationSettingFactoryPH2_ptr = void (WINAPIV*)(struct CNationSettingFactoryPH*);
        using CNationSettingFactoryPHctor_CNationSettingFactoryPH2_clbk = void (WINAPIV*)(struct CNationSettingFactoryPH*, CNationSettingFactoryPHctor_CNationSettingFactoryPH2_ptr);
        using CNationSettingFactoryPHCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryPH*, int, char*, bool);
        using CNationSettingFactoryPHCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryPH*, int, char*, bool, CNationSettingFactoryPHCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

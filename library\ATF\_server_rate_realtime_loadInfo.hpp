// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_server_rate_realtime_load.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _server_rate_realtime_loadInit2_ptr = void (WINAPIV*)(struct _server_rate_realtime_load*, unsigned int);
        using _server_rate_realtime_loadInit2_clbk = void (WINAPIV*)(struct _server_rate_realtime_load*, unsigned int, _server_rate_realtime_loadInit2_ptr);
        
        using _server_rate_realtime_loadctor__server_rate_realtime_load4_ptr = void (WINAPIV*)(struct _server_rate_realtime_load*);
        using _server_rate_realtime_loadctor__server_rate_realtime_load4_clbk = void (WINAPIV*)(struct _server_rate_realtime_load*, _server_rate_realtime_loadctor__server_rate_realtime_load4_ptr);
        
        using _server_rate_realtime_loaddtor__server_rate_realtime_load6_ptr = void (WINAPIV*)(struct _server_rate_realtime_load*);
        using _server_rate_realtime_loaddtor__server_rate_realtime_load6_clbk = void (WINAPIV*)(struct _server_rate_realtime_load*, _server_rate_realtime_loaddtor__server_rate_realtime_load6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

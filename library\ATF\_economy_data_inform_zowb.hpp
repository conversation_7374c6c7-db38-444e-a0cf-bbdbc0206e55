// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _economy_data_inform_zowb
    {
        float fPayExgRate[3];
        float fTexRate[3];
        unsigned __int16 wEconomyGuide[3];
        unsigned __int16 wYear;
        char byMonth;
        char byDay;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

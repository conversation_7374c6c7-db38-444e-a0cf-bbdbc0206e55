// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IUnknown.hpp>


START_ATF_NAMESPACE
    struct $DF102B31B2A9296EC5F201FD7C6F7BC9
    {
        BYTE gap0[8];
        IUnknown **ppunkVal;
    };    
    static_assert(ATF::checkSize<$DF102B31B2A9296EC5F201FD7C6F7BC9, 16>(), "$DF102B31B2A9296EC5F201FD7C6F7BC9");
END_ATF_NAMESPACE

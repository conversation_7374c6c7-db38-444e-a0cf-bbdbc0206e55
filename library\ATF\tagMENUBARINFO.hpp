// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HMENU__.hpp>
#include <HWND__.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagMENUBARINFO
    {
        unsigned int cbSize;
        tagRECT rcBar;
        HMENU__ *hMenu;
        HWND__ *hwndMenu;
        __int32 fBarFocused : 1;
        __int32 fFocused : 1;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

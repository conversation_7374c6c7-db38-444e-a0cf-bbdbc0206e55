// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _DOCINFOA
    {
        int cbSize;
        const char *lpszDocName;
        const char *lpszOutput;
        const char *lpszDatatype;
        unsigned int fwType;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

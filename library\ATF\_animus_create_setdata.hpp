// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <_character_create_setdata.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _animus_create_setdata : _character_create_setdata
    {
        int nHP;
        int nFP;
        unsigned __int64 dwExp;
        CPlayer *pMaster;
        int nMaxAttackPnt;
    public:
        _animus_create_setdata();
        void ctor__animus_create_setdata();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_animus_create_setdata, 64>(), "_animus_create_setdata");
END_ATF_NAMESPACE

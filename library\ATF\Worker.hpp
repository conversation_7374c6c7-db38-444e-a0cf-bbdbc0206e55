// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <TaskPool.hpp>
#include <WorkerVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct Worker
    {
        WorkerVtbl *vfptr;
        bool _bInit;
        bool _bRun;
        int _nLoop;
        int _nTerm;
        int _nMaxTskNum;
        int _nMaxTskBufSize;
        char _szWorkerName[128];
        void *_hRunEvent;
        TaskPool *_pkPool;
    public:
        bool Initialize(int nLoop, int nTerm);
        void Start();
        void Stop();
        Worker(char* pWorkerName, int nMaxTskNum, int nMaxTskBufSize);
        void ctor_Worker(char* pWorkerName, int nMaxTskNum, int nMaxTskBufSize);
        static void s_loop(void* pArg);
        ~Worker();
        void dtor_Worker();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<Worker, 176>(), "Worker");
END_ATF_NAMESPACE

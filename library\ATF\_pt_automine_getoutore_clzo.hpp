// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _pt_automine_getoutore_clzo
    {
        char bySrcPage;
        unsigned int dwSrcK;
        char byDstBag;
        unsigned int dwDstK;
        char byOverlapNum;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

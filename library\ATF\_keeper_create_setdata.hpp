// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_character_create_setdata.hpp>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _keeper_create_setdata : _character_create_setdata
    {
        int nMasterRace;
        _dummy_position *pPosCreate;
        _dummy_position *pPosActive;
        _dummy_position *pPosCenter;
    public:
        _keeper_create_setdata();
        void ctor__keeper_create_setdata();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_keeper_create_setdata, 64>(), "_keeper_create_setdata");
END_ATF_NAMESPACE

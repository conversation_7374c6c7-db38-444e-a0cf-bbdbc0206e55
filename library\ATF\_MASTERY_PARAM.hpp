// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRecordData.hpp>
#include <_STAT_DB_BASE.hpp>
#include <_mastery_up_data.hpp>
#include <_skill_lv_up_data.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _MASTERY_PARAM
    {
        char m_byRaceCode;
         _STAT_DB_BASE m_BaseCum;
        unsigned int m_dwSkillMasteryCum[8];
        unsigned int m_dwForceLvCum[4];
        char m_mtyWp[2];
        char m_mtySuffer;
        char m_mtyShield;
        char m_lvSkill[48];
        char m_mtySkill[8];
        char m_mtyForce[24];
        char m_mtyStaff;
        char m_mtyMakeItem[3];
        char m_mtySpecial;
        unsigned int *m_ppdwMasteryCumPtr[7];
        char *m_ppbyMasteryPtr[7];
        char *m_ppbyEquipMasteryPrt[6];
        _mastery_up_data m_MastUpData;
        _skill_lv_up_data m_SkillUpData;
        bool m_bUpdateEquipMast;
    public:
        bool AlterCumPerMast(char byClass, char byIndex, unsigned int dwAlterCum, unsigned int* pdwAfterCum);
        float GetAveForceMasteryPerClass(char byClass);
        float GetAveSkillMasteryPerClass(char byClass);
        int GetCumPerMast(char byCode, char byMast);
        char GetEquipMastery(int nEquipMasteryCode);
        int GetMasteryPerMast(char byCode, char byMast);
        int GetSkillLv(char bySkillIndex);
        bool Init(struct _STAT_DB_BASE* pStatBase, char byRaceCode);
        static bool IsValidMasteryCode(char byCode, char byIndex);
        static void SetStaticMember(struct CRecordData* pSkillData, struct CRecordData* pForceData);
        void UpdateCumPerMast(char byClass, char byIndex, unsigned int dwNewCum);
        _MASTERY_PARAM();
        void ctor__MASTERY_PARAM();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_MASTERY_PARAM, 640>(), "_MASTERY_PARAM");
END_ATF_NAMESPACE

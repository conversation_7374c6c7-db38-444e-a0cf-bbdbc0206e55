// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_object_id.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _object_idctor__object_id2_ptr = void (WINAPIV*)(struct _object_id*, char, char, uint16_t);
        using _object_idctor__object_id2_clbk = void (WINAPIV*)(struct _object_id*, char, char, uint16_t, _object_idctor__object_id2_ptr);
        
        using _object_idctor__object_id4_ptr = void (WINAPIV*)(struct _object_id*);
        using _object_idctor__object_id4_clbk = void (WINAPIV*)(struct _object_id*, _object_idctor__object_id4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

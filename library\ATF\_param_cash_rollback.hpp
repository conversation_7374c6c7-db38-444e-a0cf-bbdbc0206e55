// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_param_cash.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _param_cash_rollback : _param_cash
    {
        struct __list
        {
            unsigned __int64 in_lnUID;
            char in_strItemCode[8];
            char in_byOverlapNum;
            int in_nPrice;
            int in_nDiscount;
            int out_nRet;
            char out_cStatus;
            int out_nCashAmount;
            int out_nStatus;
        };
        char in_szAcc[13];
        int in_byNum;
        int out_nCashAmount;
        char in_szWorldName[33];
        char in_UserIP[15];
        char in_szAvatorName[17];
        __list data[20];
    public:
        _param_cash_rollback(unsigned int dwAc, unsigned int dwAv, uint16_t wSock);
        void ctor__param_cash_rollback(unsigned int dwAc, unsigned int dwAv, uint16_t wSock);
        int size();
        ~_param_cash_rollback();
        void dtor__param_cash_rollback();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_param_cash_rollback, 1072>(), "_param_cash_rollback");
END_ATF_NAMESPACE

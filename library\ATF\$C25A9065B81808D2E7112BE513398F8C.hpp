// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $C25A9065B81808D2E7112BE513398F8C
    {
        unsigned __int16 wFlipMSTypes;
        unsigned __int16 wBltMSTypes;
    };    
    static_assert(ATF::checkSize<$C25A9065B81808D2E7112BE513398F8C, 4>(), "$C25A9065B81808D2E7112BE513398F8C");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBuffInfoByHolyQuestfGroup.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CRaceBuffInfoByHolyQuestfGroupApply2_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, int, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestfGroupApply2_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, int, struct CPlayer*, CRaceBuffInfoByHolyQuestfGroupApply2_ptr);
        
        using CRaceBuffInfoByHolyQuestfGroupctor_CRaceBuffInfoByHolyQuestfGroup4_ptr = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, unsigned int);
        using CRaceBuffInfoByHolyQuestfGroupctor_CRaceBuffInfoByHolyQuestfGroup4_clbk = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, unsigned int, CRaceBuffInfoByHolyQuestfGroupctor_CRaceBuffInfoByHolyQuestfGroup4_ptr);
        using CRaceBuffInfoByHolyQuestfGroupCreateComplete6_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, int, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestfGroupCreateComplete6_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, int, struct CPlayer*, CRaceBuffInfoByHolyQuestfGroupCreateComplete6_ptr);
        using CRaceBuffInfoByHolyQuestfGroupInit8_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*);
        using CRaceBuffInfoByHolyQuestfGroupInit8_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, CRaceBuffInfoByHolyQuestfGroupInit8_ptr);
        using CRaceBuffInfoByHolyQuestfGroupRelease10_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, int, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestfGroupRelease10_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, int, struct CPlayer*, CRaceBuffInfoByHolyQuestfGroupRelease10_ptr);
        
        using CRaceBuffInfoByHolyQuestfGroupdtor_CRaceBuffInfoByHolyQuestfGroup14_ptr = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*);
        using CRaceBuffInfoByHolyQuestfGroupdtor_CRaceBuffInfoByHolyQuestfGroup14_clbk = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuestfGroup*, CRaceBuffInfoByHolyQuestfGroupdtor_CRaceBuffInfoByHolyQuestfGroup14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

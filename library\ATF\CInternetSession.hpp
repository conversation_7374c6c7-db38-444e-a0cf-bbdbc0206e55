// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CObject.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CInternetSession : CObject
    {
        unsigned __int64 m_dwContext;
        void *m_hSession;
        void (WINAPIV *m_pOldCallback)(void *, unsigned __int64, unsigned int, void *, unsigned int);
        int m_bCallbackEnabled;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

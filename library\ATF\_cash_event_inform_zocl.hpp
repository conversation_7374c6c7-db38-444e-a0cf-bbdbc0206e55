// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _cash_event_inform_zocl
    {
        struct __lim_sale
        {
            struct  __Item
            {
                char byTableCode;
                unsigned int dwIndex;
                unsigned __int16 wNum;
            };
            char byCount;
            char byDiscount;
            __Item item[20];
        };
        char byEventType;
        char byEventStatus;
        unsigned __int16 wYear[2];
        char byMonth[2];
        char byDay[2];
        char byHour[2];
        char byMinute[2];
        __lim_sale LimitedSale;
    public:
        _cash_event_inform_zocl();
        void ctor__cash_event_inform_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_cash_event_inform_zocl, 156>(), "_cash_event_inform_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_Ctypevec.hpp>
#include <std__ctype_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  ctype<char> : ctype_base
        {
            _Ctypevec _Ctype;
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <_Ctypevec.hpp>
#include <_Cvtvec.hpp>
#include <std__ctype_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  ctype<wchar_t> : ctype_base
        {
            _Ctypevec _Ctype;
            _Cvtvec _Cvt;
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <_Ctypevec.hpp>
#include <_Cvtvec.hpp>
#include <std__ctype_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  ctype<unsigned short> : ctype_base
        {
            _Ctypevec _Ctype;
            _Cvtvec _Cvt;
        };
    }; // end namespace std
END_ATF_NAMESPACE

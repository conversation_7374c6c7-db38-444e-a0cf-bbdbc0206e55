// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _mon_active_fld : _base_fld
    {
        unsigned int m_dwRegenTime;
        unsigned int m_dwRegenLimNum;
        unsigned int m_dwRegenProp;
        unsigned int m_dwRegenMinNum;
        unsigned int m_dwStdKill;
        unsigned int m_dwRegenMaxNum;
    };
END_ATF_NAMESPACE

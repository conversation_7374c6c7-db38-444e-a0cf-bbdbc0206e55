// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DTRADE_ITEM.hpp>


START_ATF_NAMESPACE
    struct _DTRADE_PARAM
    {
        bool bDTradeMode;
        bool bDTradeLock;
        bool bDTradeOK;
        unsigned __int16 wDTradeDstIndex;
        unsigned int dwDTradeDstSerial;
        _DTRADE_ITEM DItemNode[15];
        unsigned int dwDTrade_Dalant;
        unsigned int dwDTrade_Gold;
        char byEmptyInvenNum;
        char bySellItemNum;
        unsigned int dwKey[4];
    public:
        void Init();
        void SetDTradeStart(uint16_t pl_dwDstIndex, unsigned int pl_dwDstSerial, int pl_mEmptyInvenNum, unsigned int* pl_dwKey);
        _DTRADE_PARAM();
        void ctor__DTRADE_PARAM();
    };    
    static_assert(ATF::checkSize<_DTRADE_PARAM, 220>(), "_DTRADE_PARAM");
END_ATF_NAMESPACE

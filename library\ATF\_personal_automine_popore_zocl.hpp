// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _personal_automine_popore_zocl
    {
        unsigned __int16 wItemSerial;
        char byNum;
    public:
        _personal_automine_popore_zocl();
        void ctor__personal_automine_popore_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_personal_automine_popore_zocl, 3>(), "_personal_automine_popore_zocl");
END_ATF_NAMESPACE

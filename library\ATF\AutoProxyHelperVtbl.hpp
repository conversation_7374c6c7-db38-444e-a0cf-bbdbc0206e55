// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    const struct AutoProxyHelperVtbl
    {
        int (WINAPIV *IsResolvable)(char *);
        unsigned int (WINAPIV *GetIPAddress)(char *, unsigned int *);
        unsigned int (WINAPIV *ResolveHostName)(char *, char *, unsigned int *);
        int (WINAPIV *IsInNet)(char *, char *, char *);
    };
END_ATF_NAMESPACE

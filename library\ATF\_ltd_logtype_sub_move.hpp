// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum _ltd_logtype_sub_move
    {
      atrade_buy = 0x0,
      atrade_sell = 0xA,
      dtrade = 0x14,
      drop = 0x1E,
      pickup = 0x28,
      exchange = 0x32,
      trunk_in_money = 0x3C,
      trunk_out_money = 0x46,
      trunk_in_item = 0x50,
      trunk_out_item = 0x5A,
      trunk_exchange_item = 0x64,
    };
END_ATF_NAMESPACE

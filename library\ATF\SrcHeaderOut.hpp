// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$3AD4F131CA8543D9E88BC26A1B5980C5.hpp>
#include <$5F12993BB80B062326247C4784E48DC0.hpp>


START_ATF_NAMESPACE
    struct SrcHeaderOut
    {
        unsigned int cb;
        unsigned int ver;
        unsigned int sig;
        unsigned int cbSource;
        unsigned int niFile;
        unsigned int niObj;
        unsigned int niVirt;
        char srccompress;
        $5F12993BB80B062326247C4784E48DC0 ___u8;
        __int16 sPad;
        $3AD4F131CA8543D9E88BC26A1B5980C5 ___u10;
    };
END_ATF_NAMESPACE

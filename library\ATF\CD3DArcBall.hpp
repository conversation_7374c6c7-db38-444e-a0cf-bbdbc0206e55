// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND.hpp>


START_ATF_NAMESPACE
    struct CD3DArcBall
    {
    public:
        CD3DArcBall();
        int64_t ctor_CD3DArcBall();
        int64_t HandleMouseMessages(HWND arg_0, unsigned int arg_1, uint64_t arg_2, int64_t arg_3);
        struct D3DXVECTOR3* ScreenToVector(short retstr, struct D3DXVECTOR3* arg_0, int arg_1);
        void SetRadius(float arg_0);
        void SetWindow(int arg_0, int arg_1, float arg_2);
    };
END_ATF_NAMESPACE

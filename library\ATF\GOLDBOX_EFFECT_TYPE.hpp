// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum GOLDBOX_EFFECT_TYPE
    {
      Get_Hero_Gold_Box = 0x1,
      Get_Starter_Gold_Box = 0x2,
      Get_HolyStone_Gold_Box = 0x3,
      Get_Mine_Gold_Box = 0x4,
      Get_Pure_Gold_Box = 0x5,
      Get_Gold_Bar = 0x6,
      Get_Imitation_Gold_Bar = 0x7,
      Get_Event_Coupon = 0x8,
      Use_Hero_Gold_Box = 0x9,
      Use_Starter_Gold_Box = 0xA,
      Use_HolyStone_Gold_Box = 0xB,
      Use_Mine_Gold_Box = 0xC,
      Use_Coupon_Gold_Box = 0xD,
    };
END_ATF_NAMESPACE

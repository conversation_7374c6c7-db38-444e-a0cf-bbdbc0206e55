// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<typename _Ty, bool _b>
        struct CSimpleStringT
        {
            _Ty *m_pszData;
        };
        static_assert(ATF::checkSize<ATL::CSimpleStringT<char, true>, 8>(), "ATL::CSimpleStringT<char,1>");
    }; // end namespace ATL
END_ATF_NAMESPACE

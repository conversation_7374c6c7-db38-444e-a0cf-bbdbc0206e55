// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagSAFEARRAY.hpp>


START_ATF_NAMESPACE
    struct $F843BCFCB8B642AED9B77EEE39B126D8
    {
        BYTE gap0[8];
        tagSAFEARRAY **pparray;
    };    
    static_assert(ATF::checkSize<$F843BCFCB8B642AED9B77EEE39B126D8, 16>(), "$F843BCFCB8B642AED9B77EEE39B126D8");
END_ATF_NAMESPACE

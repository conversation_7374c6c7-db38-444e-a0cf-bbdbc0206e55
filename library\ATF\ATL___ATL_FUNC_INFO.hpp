// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct _ATL_FUNC_INFO
        {
            tagCALLCONV cc;
            unsigned __int16 vtReturn;
            __int16 nParams;
            unsigned __int16 pVarTypes[8];
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

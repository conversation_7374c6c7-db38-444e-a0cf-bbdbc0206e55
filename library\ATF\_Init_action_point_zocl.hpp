// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _Init_action_point_zocl
    {
        unsigned int dwActionPoint[3];
    public:
        _Init_action_point_zocl();
        void ctor__Init_action_point_zocl();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_Init_action_point_zocl, 12>(), "_Init_action_point_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagACTCTX_SECTION_KEYED_DATA_ASSEMBLY_METADATA.hpp>


START_ATF_NAMESPACE
    struct tagACTCTX_SECTION_KEYED_DATA
    {
        unsigned int cbSize;
        unsigned int ulDataFormatVersion;
        void *lpData;
        unsigned int ulLength;
        void *lpSectionGlobalData;
        unsigned int ulSectionGlobalDataLength;
        void *lpSectionBase;
        unsigned int ulSectionTotalLength;
        void *hActCtx;
        unsigned int ulAssemblyRosterIndex;
        unsigned int ulFlags;
        tagACTCTX_SECTION_KEYED_DATA_ASSEMBLY_METADATA AssemblyMetadata;
    };
END_ATF_NAMESPACE

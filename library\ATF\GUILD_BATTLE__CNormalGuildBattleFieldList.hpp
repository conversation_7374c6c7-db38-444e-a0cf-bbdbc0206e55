// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCircleZone.hpp>
#include <CGravityStone.hpp>
#include <CGravityStoneRegener.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleField.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CNormalGuildBattleFieldList
        {
            unsigned int m_dwCnt;
            CNormalGuildBattleField *m_pkField;
            char m_byUseFieldCnt[3];
            CNormalGuildBattleField **m_ppkUseFieldByRace[3];
        public:
            CNormalGuildBattleFieldList();
            void ctor_CNormalGuildBattleFieldList();
            static void Destroy();
            struct CCircleZone* GetCircleZone(int iInx);
            struct CNormalGuildBattleField* GetField(char byRace, unsigned int dwMapCode);
            struct CNormalGuildBattleField* GetField(unsigned int dwMapID);
            struct CNormalGuildBattleField* GetFirstMapFieldByRace(char byRace);
            bool GetFirstMapInxByRace(char byRace, char* byInx);
            unsigned int GetMapCnt();
            bool GetMapInx(char byRace, unsigned int dwMapCode, unsigned int* dwMapInx);
            unsigned int GetMapInxList(char byRace, char* pbyInxList);
            struct CGravityStoneRegener* GetRegener(int iInx);
            struct CGravityStone* GetStone(int iInx);
            bool Init();
            bool InitUseField(char byRace, char* szKeyName, char* szStrBuff, char** szParseBuff);
            static struct CNormalGuildBattleFieldList* Instance();
            int IsRegistedMapInx(char byRace, char** szParseBuff);
            ~CNormalGuildBattleFieldList();
            void dtor_CNormalGuildBattleFieldList();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

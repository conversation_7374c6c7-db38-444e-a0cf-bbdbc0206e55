// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$E5EC784184F61E2D1DB730C15D65FB8F.hpp>
#include <HINSTANCE__.hpp>
#include <HKEY__.hpp>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    struct _SHELLEXECUTEINFOA
    {
        unsigned int cbSize;
        unsigned int fMask;
        HWND__ *hwnd;
        const char *lpVerb;
        const char *lpFile;
        const char *lpParameters;
        const char *lpDirectory;
        int nShow;
        HINSTANCE__ *hInstApp;
        void *lpIDList;
        const char *lpClass;
        HKEY__ *hkeyClass;
        unsigned int dwHotKey;
        $E5EC784184F61E2D1DB730C15D65FB8F ___u13;
        void *hProcess;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FULL_PTR_TO_REFID_ELEMENT.hpp>


START_ATF_NAMESPACE
    struct $14BF9AA2EF21CC35F0ACBBD33CBBC311
    {
        _FULL_PTR_TO_REFID_ELEMENT **XlatTable;
        unsigned int NumberOfBuckets;
        unsigned int HashMask;
    };
END_ATF_NAMESPACE

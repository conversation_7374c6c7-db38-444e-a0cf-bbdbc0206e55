// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _party_member_max_hfsp_upd
    {
        unsigned int dwMemSerial;
        unsigned __int16 wMaxHP;
        unsigned __int16 wMaxFP;
        unsigned __int16 wMaxSP;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

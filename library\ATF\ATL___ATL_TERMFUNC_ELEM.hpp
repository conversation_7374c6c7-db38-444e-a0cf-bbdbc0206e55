// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct _ATL_TERMFUNC_ELEM
        {
            void (WINAPIV *pFunc)(unsigned __int64);
            unsigned __int64 dw;
            _ATL_TERMFUNC_ELEM *pNext;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

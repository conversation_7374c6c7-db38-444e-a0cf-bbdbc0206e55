// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _DTRADE_ITEM
    {
        bool bLoad;
        char byStorageCode;
        unsigned int dwSerial;
        char byAmount;
    public:
        void ReleaseData();
        void SetData(char p_byStorageCode, unsigned int p_dwSerial, char p_byAmount);
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_DTRADE_ITEM, 12>(), "_DTRADE_ITEM");
END_ATF_NAMESPACE

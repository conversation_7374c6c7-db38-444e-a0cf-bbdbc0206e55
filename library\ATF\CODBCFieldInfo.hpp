// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CODBCFieldInfo
    {
        ATL::CStringT<char> m_strName;
        __int16 m_nSQLType;
        unsigned __int64 m_nPrecision;
        __int16 m_nScale;
        __int16 m_nNullability;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTime.hpp>
#include <BossSchedule_Map.hpp>
#include <_mon_active.hpp>
#include <_mon_block.hpp>


START_ATF_NAMESPACE
    struct BossSchedule
    {
        enum SAVE_KEY
        {
            KEY_MOB_CODE = 0x0,
            KEY_MOB_LASTTIME = 0x1,
            KEY_MOB_LIVE_COUNT = 0x2,
            KEY_MAX = 0x3,
        };
        char m_strSection[64];
        char m_strMonCode[64];
        _mon_block *m_pBlock;
        _mon_active *m_pMonAct;
        int m_nBlockIndex;
        int m_nActIndex;
        ATL::CTime m_LastRespawnSystemTime;
        unsigned __int16 m_LiveCount;
        BossSchedule_Map *m_pParent;
    public:
        BossSchedule();
        void ctor_BossSchedule();
        static struct ATL::CTime* Make_LastTimeRespawnSystemTime(struct ATL::CTime* result, char* strTimeValue);
        bool Make_LastTimeRespawnSystemTimeString(char* strBuff, int nBuffSize);
        static uint16_t Make_LiveCount(char* strTimeValue);
        bool Make_LiveCountString(char* strBuff, int nBuffSize);
        void Save_LastRespawnSystemTime(struct ATL::CTime* systime);
        void Save_LiveCount(uint16_t wCount);
        ~BossSchedule();
        void dtor_BossSchedule();
    };
END_ATF_NAMESPACE

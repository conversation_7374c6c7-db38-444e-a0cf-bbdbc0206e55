// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $50230A970D9734D4E9774CFC619DF0F6
    {
        BYTE gap0[8];
        float fltVal;
    };    
    static_assert(ATF::checkSize<$50230A970D9734D4E9774CFC619DF0F6, 12>(), "$50230A970D9734D4E9774CFC619DF0F6");
END_ATF_NAMESPACE

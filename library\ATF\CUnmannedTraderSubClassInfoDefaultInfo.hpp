// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSubClassInfoDefault.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderSubClassInfoDefaultctor_CUnmannedTraderSubClassInfoDefault2_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoDefault*, unsigned int);
        using CUnmannedTraderSubClassInfoDefaultctor_CUnmannedTraderSubClassInfoDefault2_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoDefault*, unsigned int, CUnmannedTraderSubClassInfoDefaultctor_CUnmannedTraderSubClassInfoDefault2_ptr);
        using CUnmannedTraderSubClassInfoDefaultCreate4_ptr = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfoDefault*, unsigned int);
        using CUnmannedTraderSubClassInfoDefaultCreate4_clbk = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfoDefault*, unsigned int, CUnmannedTraderSubClassInfoDefaultCreate4_ptr);
        using CUnmannedTraderSubClassInfoDefaultGetGroupID6_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoDefault*, char, uint16_t, char*);
        using CUnmannedTraderSubClassInfoDefaultGetGroupID6_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoDefault*, char, uint16_t, char*, CUnmannedTraderSubClassInfoDefaultGetGroupID6_ptr);
        using CUnmannedTraderSubClassInfoDefaultLoadXML8_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoDefault*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int);
        using CUnmannedTraderSubClassInfoDefaultLoadXML8_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoDefault*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int, CUnmannedTraderSubClassInfoDefaultLoadXML8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

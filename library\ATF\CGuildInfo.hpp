// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuild.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CGuildActVote2_ptr = bool (WINAPIV*)(struct CGuild*, struct _guild_member_info*, char);
        using CGuildActVote2_clbk = bool (WINAPIV*)(struct CGuild*, struct _guild_member_info*, char, CGuildActVote2_ptr);
        using CGuildAddScheduleComplete4_ptr = void (WINAPIV*)(struct CGuild*, char, struct CGuild*);
        using CGuildAddScheduleComplete4_clbk = void (WINAPIV*)(struct CGuild*, char, struct CGuild*, CGuildAddScheduleComplete4_ptr);
        
        using CGuildctor_CGuild6_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildctor_CGuild6_clbk = void (WINAPIV*)(struct CGuild*, CGuildctor_CGuild6_ptr);
        using CGuildCancelSuggestedMatter8_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildCancelSuggestedMatter8_clbk = void (WINAPIV*)(struct CGuild*, CGuildCancelSuggestedMatter8_ptr);
        using CGuildCheckGuildBattleSuggestRequestToDestGuild10_ptr = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int);
        using CGuildCheckGuildBattleSuggestRequestToDestGuild10_clbk = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int, CGuildCheckGuildBattleSuggestRequestToDestGuild10_ptr);
        using CGuildClearGuildBattle12_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildClearGuildBattle12_clbk = void (WINAPIV*)(struct CGuild*, CGuildClearGuildBattle12_ptr);
        using CGuildClearVote14_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildClearVote14_clbk = void (WINAPIV*)(struct CGuild*, CGuildClearVote14_ptr);
        using CGuildCompleteOutGuildbattleCost16_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int);
        using CGuildCompleteOutGuildbattleCost16_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int, CGuildCompleteOutGuildbattleCost16_ptr);
        using CGuildCompleteSelectMasterLastConn18_ptr = void (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildCompleteSelectMasterLastConn18_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, CGuildCompleteSelectMasterLastConn18_ptr);
        using CGuildComplete_DB_Update_Committee20_ptr = void (WINAPIV*)(struct CGuild*, char*);
        using CGuildComplete_DB_Update_Committee20_clbk = void (WINAPIV*)(struct CGuild*, char*, CGuildComplete_DB_Update_Committee20_ptr);
        using CGuildDB_Update_GuildMaster22_ptr = bool (WINAPIV*)(struct CGuild*, struct _guild_member_info*);
        using CGuildDB_Update_GuildMaster22_clbk = bool (WINAPIV*)(struct CGuild*, struct _guild_member_info*, CGuildDB_Update_GuildMaster22_ptr);
        using CGuildDB_Update_GuildMaster_Complete24_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, char, unsigned int, char);
        using CGuildDB_Update_GuildMaster_Complete24_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, char, unsigned int, char, CGuildDB_Update_GuildMaster_Complete24_ptr);
        using CGuildDestGuildIsAvailableBattleRequestState26_ptr = char (WINAPIV*)(struct CGuild*);
        using CGuildDestGuildIsAvailableBattleRequestState26_clbk = char (WINAPIV*)(struct CGuild*, CGuildDestGuildIsAvailableBattleRequestState26_ptr);
        using CGuildEndRankJob28_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildEndRankJob28_clbk = void (WINAPIV*)(struct CGuild*, CGuildEndRankJob28_ptr);
        using CGuildEstGuild30_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, char*, char, int, struct _guild_member_info*);
        using CGuildEstGuild30_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, char*, char, int, struct _guild_member_info*, CGuildEstGuild30_ptr);
        using CGuildForceLeave32_ptr = void (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildForceLeave32_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, CGuildForceLeave32_ptr);
        using CGuildGetApplierFromSerial34_ptr = struct _guild_applier_info* (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildGetApplierFromSerial34_clbk = struct _guild_applier_info* (WINAPIV*)(struct CGuild*, unsigned int, CGuildGetApplierFromSerial34_ptr);
        using CGuildGetGrade36_ptr = char (WINAPIV*)(struct CGuild*);
        using CGuildGetGrade36_clbk = char (WINAPIV*)(struct CGuild*, CGuildGetGrade36_ptr);
        using CGuildGetGuildMasterName38_ptr = char* (WINAPIV*)(struct CGuild*);
        using CGuildGetGuildMasterName38_clbk = char* (WINAPIV*)(struct CGuild*, CGuildGetGuildMasterName38_ptr);
        using CGuildGetGuildMasterSerial40_ptr = unsigned int (WINAPIV*)(struct CGuild*);
        using CGuildGetGuildMasterSerial40_clbk = unsigned int (WINAPIV*)(struct CGuild*, CGuildGetGuildMasterSerial40_ptr);
        using CGuildGetMemberFromSerial42_ptr = struct _guild_member_info* (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildGetMemberFromSerial42_clbk = struct _guild_member_info* (WINAPIV*)(struct CGuild*, unsigned int, CGuildGetMemberFromSerial42_ptr);
        using CGuildGetMemberNum44_ptr = int (WINAPIV*)(struct CGuild*);
        using CGuildGetMemberNum44_clbk = int (WINAPIV*)(struct CGuild*, CGuildGetMemberNum44_ptr);
        using CGuildGetMemberNumForJoin46_ptr = int (WINAPIV*)(struct CGuild*);
        using CGuildGetMemberNumForJoin46_clbk = int (WINAPIV*)(struct CGuild*, CGuildGetMemberNumForJoin46_ptr);
        using CGuildGetRace48_ptr = char (WINAPIV*)(struct CGuild*);
        using CGuildGetRace48_clbk = char (WINAPIV*)(struct CGuild*, CGuildGetRace48_ptr);
        using CGuildGetTotalDalant50_ptr = long double (WINAPIV*)(struct CGuild*);
        using CGuildGetTotalDalant50_clbk = long double (WINAPIV*)(struct CGuild*, CGuildGetTotalDalant50_ptr);
        using CGuildGetTotalGold52_ptr = long double (WINAPIV*)(struct CGuild*);
        using CGuildGetTotalGold52_clbk = long double (WINAPIV*)(struct CGuild*, CGuildGetTotalGold52_ptr);
        using CGuildGuildBattleSuggestRequestToDestGuild54_ptr = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int);
        using CGuildGuildBattleSuggestRequestToDestGuild54_clbk = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int, CGuildGuildBattleSuggestRequestToDestGuild54_ptr);
        using CGuildIOMoney56_ptr = void (WINAPIV*)(struct CGuild*, char*, unsigned int, long double, long double, long double, long double, char*, bool);
        using CGuildIOMoney56_clbk = void (WINAPIV*)(struct CGuild*, char*, unsigned int, long double, long double, long double, long double, char*, bool, CGuildIOMoney56_ptr);
        using CGuildInit58_ptr = void (WINAPIV*)(struct CGuild*, int);
        using CGuildInit58_clbk = void (WINAPIV*)(struct CGuild*, int, CGuildInit58_ptr);
        using CGuildInitVote60_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildInitVote60_clbk = void (WINAPIV*)(struct CGuild*, CGuildInitVote60_ptr);
        using CGuildIsFill62_ptr = bool (WINAPIV*)(struct CGuild*);
        using CGuildIsFill62_clbk = bool (WINAPIV*)(struct CGuild*, CGuildIsFill62_ptr);
        using CGuildLoginMember64_ptr = struct _guild_member_info* (WINAPIV*)(struct CGuild*, unsigned int, struct CPlayer*);
        using CGuildLoginMember64_clbk = struct _guild_member_info* (WINAPIV*)(struct CGuild*, unsigned int, struct CPlayer*, CGuildLoginMember64_ptr);
        using CGuildLogoffMember66_ptr = bool (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildLogoffMember66_clbk = bool (WINAPIV*)(struct CGuild*, unsigned int, CGuildLogoffMember66_ptr);
        using CGuildLoop68_ptr = void (WINAPIV*)(struct CGuild*, bool);
        using CGuildLoop68_clbk = void (WINAPIV*)(struct CGuild*, bool, CGuildLoop68_ptr);
        using CGuildMakeBuddyPacket70_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildMakeBuddyPacket70_clbk = void (WINAPIV*)(struct CGuild*, CGuildMakeBuddyPacket70_ptr);
        using CGuildMakeDownApplierPacket72_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildMakeDownApplierPacket72_clbk = void (WINAPIV*)(struct CGuild*, CGuildMakeDownApplierPacket72_ptr);
        using CGuildMakeDownMemberPacket74_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildMakeDownMemberPacket74_clbk = void (WINAPIV*)(struct CGuild*, CGuildMakeDownMemberPacket74_ptr);
        using CGuildMakeMoneyIOPacket76_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildMakeMoneyIOPacket76_clbk = void (WINAPIV*)(struct CGuild*, CGuildMakeMoneyIOPacket76_ptr);
        using CGuildMakeQueryInfoPacket78_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildMakeQueryInfoPacket78_clbk = void (WINAPIV*)(struct CGuild*, CGuildMakeQueryInfoPacket78_ptr);
        using CGuildManageAcceptORRefuseGuildBattle80_ptr = char (WINAPIV*)(struct CGuild*, bool);
        using CGuildManageAcceptORRefuseGuildBattle80_clbk = char (WINAPIV*)(struct CGuild*, bool, CGuildManageAcceptORRefuseGuildBattle80_ptr);
        using CGuildManageBuyGuildEmblem82_ptr = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int);
        using CGuildManageBuyGuildEmblem82_clbk = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, CGuildManageBuyGuildEmblem82_ptr);
        using CGuildManageExpulseMember84_ptr = char (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildManageExpulseMember84_clbk = char (WINAPIV*)(struct CGuild*, unsigned int, CGuildManageExpulseMember84_ptr);
        using CGuildManageGuildCommittee86_ptr = char (WINAPIV*)(struct CGuild*, unsigned int, bool);
        using CGuildManageGuildCommittee86_clbk = char (WINAPIV*)(struct CGuild*, unsigned int, bool, CGuildManageGuildCommittee86_ptr);
        using CGuildManagePopGuildMoney88_ptr = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int);
        using CGuildManagePopGuildMoney88_clbk = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, CGuildManagePopGuildMoney88_ptr);
        using CGuildManageProposeGuildBattle90_ptr = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int);
        using CGuildManageProposeGuildBattle90_clbk = char (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int, CGuildManageProposeGuildBattle90_ptr);
        using CGuildPopApplier92_ptr = bool (WINAPIV*)(struct CGuild*, unsigned int, char);
        using CGuildPopApplier92_clbk = bool (WINAPIV*)(struct CGuild*, unsigned int, char, CGuildPopApplier92_ptr);
        using CGuildPopMember94_ptr = bool (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildPopMember94_clbk = bool (WINAPIV*)(struct CGuild*, unsigned int, CGuildPopMember94_ptr);
        using CGuildPushApplier96_ptr = bool (WINAPIV*)(struct CGuild*, struct CPlayer*);
        using CGuildPushApplier96_clbk = bool (WINAPIV*)(struct CGuild*, struct CPlayer*, CGuildPushApplier96_ptr);
        using CGuildPushDQSDestGuildOutputGuildBattleCost98_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildPushDQSDestGuildOutputGuildBattleCost98_clbk = void (WINAPIV*)(struct CGuild*, CGuildPushDQSDestGuildOutputGuildBattleCost98_ptr);
        using CGuildPushDQSGuildMasterLastConnn100_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildPushDQSGuildMasterLastConnn100_clbk = void (WINAPIV*)(struct CGuild*, CGuildPushDQSGuildMasterLastConnn100_ptr);
        using CGuildPushDQSInGuildBattleCost102_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildPushDQSInGuildBattleCost102_clbk = void (WINAPIV*)(struct CGuild*, CGuildPushDQSInGuildBattleCost102_ptr);
        using CGuildPushDQSInGuildBattleRewardMoney104_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildPushDQSInGuildBattleRewardMoney104_clbk = void (WINAPIV*)(struct CGuild*, CGuildPushDQSInGuildBattleRewardMoney104_ptr);
        using CGuildPushDQSSourceGuildOutputGuildBattleCost106_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildPushDQSSourceGuildOutputGuildBattleCost106_clbk = void (WINAPIV*)(struct CGuild*, CGuildPushDQSSourceGuildOutputGuildBattleCost106_ptr);
        using CGuildPushHistory_IOMoney108_ptr = void (WINAPIV*)(struct CGuild*, bool, char*, unsigned int, long double, long double, long double, long double, char*);
        using CGuildPushHistory_IOMoney108_clbk = void (WINAPIV*)(struct CGuild*, bool, char*, unsigned int, long double, long double, long double, long double, char*, CGuildPushHistory_IOMoney108_ptr);
        using CGuildPushMember110_ptr = struct _guild_member_info* (WINAPIV*)(struct CGuild*, struct _guild_member_info*);
        using CGuildPushMember110_clbk = struct _guild_member_info* (WINAPIV*)(struct CGuild*, struct _guild_member_info*, CGuildPushMember110_ptr);
        using CGuildRefreshGuildMemberData112_ptr = void (WINAPIV*)(struct CGuild*, struct _guild_member_refresh_data*);
        using CGuildRefreshGuildMemberData112_clbk = void (WINAPIV*)(struct CGuild*, struct _guild_member_refresh_data*, CGuildRefreshGuildMemberData112_ptr);
        using CGuildRegSuggestedMatter114_ptr = bool (WINAPIV*)(struct CGuild*, unsigned int, char, unsigned int, char*, unsigned int, unsigned int, unsigned int);
        using CGuildRegSuggestedMatter114_clbk = bool (WINAPIV*)(struct CGuild*, unsigned int, char, unsigned int, char*, unsigned int, unsigned int, unsigned int, CGuildRegSuggestedMatter114_ptr);
        using CGuildRelease116_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildRelease116_clbk = void (WINAPIV*)(struct CGuild*, CGuildRelease116_ptr);
        using CGuildReleaseTemp118_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildReleaseTemp118_clbk = void (WINAPIV*)(struct CGuild*, CGuildReleaseTemp118_ptr);
        using CGuildSendMsg_AddJoinApplier120_ptr = void (WINAPIV*)(struct CGuild*, struct _guild_applier_info*);
        using CGuildSendMsg_AddJoinApplier120_clbk = void (WINAPIV*)(struct CGuild*, struct _guild_applier_info*, CGuildSendMsg_AddJoinApplier120_ptr);
        using CGuildSendMsg_AlterMemberGrade122_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildSendMsg_AlterMemberGrade122_clbk = void (WINAPIV*)(struct CGuild*, CGuildSendMsg_AlterMemberGrade122_ptr);
        using CGuildSendMsg_AlterMemberState124_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildSendMsg_AlterMemberState124_clbk = void (WINAPIV*)(struct CGuild*, CGuildSendMsg_AlterMemberState124_ptr);
        using CGuildSendMsg_ApplyGuildBattleResultInform126_ptr = void (WINAPIV*)(struct CGuild*, char, char*);
        using CGuildSendMsg_ApplyGuildBattleResultInform126_clbk = void (WINAPIV*)(struct CGuild*, char, char*, CGuildSendMsg_ApplyGuildBattleResultInform126_ptr);
        using CGuildSendMsg_ChangeTaxRate128_ptr = void (WINAPIV*)(struct CGuild*, char);
        using CGuildSendMsg_ChangeTaxRate128_clbk = void (WINAPIV*)(struct CGuild*, char, CGuildSendMsg_ChangeTaxRate128_ptr);
        using CGuildSendMsg_DelJoinApplier130_ptr = void (WINAPIV*)(struct CGuild*, struct _guild_applier_info*, char);
        using CGuildSendMsg_DelJoinApplier130_clbk = void (WINAPIV*)(struct CGuild*, struct _guild_applier_info*, char, CGuildSendMsg_DelJoinApplier130_ptr);
        using CGuildSendMsg_DownPacket132_ptr = void (WINAPIV*)(struct CGuild*, char, struct _guild_member_info*);
        using CGuildSendMsg_DownPacket132_clbk = void (WINAPIV*)(struct CGuild*, char, struct _guild_member_info*, CGuildSendMsg_DownPacket132_ptr);
        using CGuildSendMsg_GuildBattleProposed134_ptr = int (WINAPIV*)(struct CGuild*, char*);
        using CGuildSendMsg_GuildBattleProposed134_clbk = int (WINAPIV*)(struct CGuild*, char*, CGuildSendMsg_GuildBattleProposed134_ptr);
        using CGuildSendMsg_GuildBattleRefused136_ptr = void (WINAPIV*)(struct CGuild*, char*);
        using CGuildSendMsg_GuildBattleRefused136_clbk = void (WINAPIV*)(struct CGuild*, char*, CGuildSendMsg_GuildBattleRefused136_ptr);
        using CGuildSendMsg_GuildBattleSuggestResult138_ptr = void (WINAPIV*)(struct CGuild*, char, char*);
        using CGuildSendMsg_GuildBattleSuggestResult138_clbk = void (WINAPIV*)(struct CGuild*, char, char*, CGuildSendMsg_GuildBattleSuggestResult138_ptr);
        using CGuildSendMsg_GuildDisjointInform140_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildSendMsg_GuildDisjointInform140_clbk = void (WINAPIV*)(struct CGuild*, CGuildSendMsg_GuildDisjointInform140_ptr);
        using CGuildSendMsg_GuildInfoUpdateInform142_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildSendMsg_GuildInfoUpdateInform142_clbk = void (WINAPIV*)(struct CGuild*, CGuildSendMsg_GuildInfoUpdateInform142_ptr);
        using CGuildSendMsg_GuildJoinAcceptInform144_ptr = void (WINAPIV*)(struct CGuild*, struct _guild_member_info*, unsigned int);
        using CGuildSendMsg_GuildJoinAcceptInform144_clbk = void (WINAPIV*)(struct CGuild*, struct _guild_member_info*, unsigned int, CGuildSendMsg_GuildJoinAcceptInform144_ptr);
        using CGuildSendMsg_GuildMemberLogin146_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, uint16_t, uint16_t);
        using CGuildSendMsg_GuildMemberLogin146_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, uint16_t, uint16_t, CGuildSendMsg_GuildMemberLogin146_ptr);
        using CGuildSendMsg_GuildMemberLogoff148_ptr = void (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildSendMsg_GuildMemberLogoff148_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, CGuildSendMsg_GuildMemberLogoff148_ptr);
        using CGuildSendMsg_GuildMemberPosInform150_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, uint16_t, uint16_t);
        using CGuildSendMsg_GuildMemberPosInform150_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, uint16_t, uint16_t, CGuildSendMsg_GuildMemberPosInform150_ptr);
        using CGuildSendMsg_GuildOutputMoneyFail152_ptr = void (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildSendMsg_GuildOutputMoneyFail152_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, CGuildSendMsg_GuildOutputMoneyFail152_ptr);
        using CGuildSendMsg_GuildRoomRented154_ptr = void (WINAPIV*)(struct CGuild*, char);
        using CGuildSendMsg_GuildRoomRented154_clbk = void (WINAPIV*)(struct CGuild*, char, CGuildSendMsg_GuildRoomRented154_ptr);
        using CGuildSendMsg_IOMoney156_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, long double, long double, bool, char*);
        using CGuildSendMsg_IOMoney156_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, long double, long double, bool, char*, CGuildSendMsg_IOMoney156_ptr);
        using CGuildSendMsg_LeaveMember158_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, bool, bool);
        using CGuildSendMsg_LeaveMember158_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, bool, bool, CGuildSendMsg_LeaveMember158_ptr);
        using CGuildSendMsg_ManageGuildCommitteeResult160_ptr = void (WINAPIV*)(struct CGuild*, bool, char*);
        using CGuildSendMsg_ManageGuildCommitteeResult160_clbk = void (WINAPIV*)(struct CGuild*, bool, char*, CGuildSendMsg_ManageGuildCommitteeResult160_ptr);
        using CGuildSendMsg_MasterElectPossible162_ptr = void (WINAPIV*)(struct CGuild*, bool);
        using CGuildSendMsg_MasterElectPossible162_clbk = void (WINAPIV*)(struct CGuild*, bool, CGuildSendMsg_MasterElectPossible162_ptr);
        using CGuildSendMsg_QueryPacket_Info164_ptr = void (WINAPIV*)(struct CGuild*, int);
        using CGuildSendMsg_QueryPacket_Info164_clbk = void (WINAPIV*)(struct CGuild*, int, CGuildSendMsg_QueryPacket_Info164_ptr);
        using CGuildSendMsg_VoteCancelInform166_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildSendMsg_VoteCancelInform166_clbk = void (WINAPIV*)(struct CGuild*, CGuildSendMsg_VoteCancelInform166_ptr);
        using CGuildSendMsg_VoteComplete168_ptr = void (WINAPIV*)(struct CGuild*, bool);
        using CGuildSendMsg_VoteComplete168_clbk = void (WINAPIV*)(struct CGuild*, bool, CGuildSendMsg_VoteComplete168_ptr);
        using CGuildSendMsg_VoteProcessInform_Continue170_ptr = void (WINAPIV*)(struct CGuild*, struct _guild_member_info*);
        using CGuildSendMsg_VoteProcessInform_Continue170_clbk = void (WINAPIV*)(struct CGuild*, struct _guild_member_info*, CGuildSendMsg_VoteProcessInform_Continue170_ptr);
        using CGuildSendMsg_VoteProcessInform_Start172_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildSendMsg_VoteProcessInform_Start172_clbk = void (WINAPIV*)(struct CGuild*, CGuildSendMsg_VoteProcessInform_Start172_ptr);
        using CGuildSendMsg_VoteState174_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildSendMsg_VoteState174_clbk = void (WINAPIV*)(struct CGuild*, CGuildSendMsg_VoteState174_ptr);
        using CGuildSendMsg_VoteStop176_ptr = void (WINAPIV*)(struct CGuild*, unsigned int);
        using CGuildSendMsg_VoteStop176_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, CGuildSendMsg_VoteStop176_ptr);
        using CGuildSetCopmlteGuildBattleSuggest178_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildSetCopmlteGuildBattleSuggest178_clbk = void (WINAPIV*)(struct CGuild*, CGuildSetCopmlteGuildBattleSuggest178_ptr);
        using CGuildSetGreetingmsg_GUILD180_ptr = void (WINAPIV*)(struct CGuild*, char*);
        using CGuildSetGreetingmsg_GUILD180_clbk = void (WINAPIV*)(struct CGuild*, char*, CGuildSetGreetingmsg_GUILD180_ptr);
        using CGuildSetGuild182_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, char, char, char*, char*, unsigned int, unsigned int, int, struct _guild_member_info*, long double, long double, unsigned int, char, int, struct _io_money_data*, unsigned int, unsigned int, unsigned int);
        using CGuildSetGuild182_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, char, char, char*, char*, unsigned int, unsigned int, int, struct _guild_member_info*, long double, long double, unsigned int, char, int, struct _io_money_data*, unsigned int, unsigned int, unsigned int, CGuildSetGuild182_ptr);
        using CGuildSetGuildBattleMatter184_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int);
        using CGuildSetGuildBattleMatter184_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int, CGuildSetGuildBattleMatter184_ptr);
        using CGuildSetTemp186_ptr = void (WINAPIV*)(struct CGuild*, char*);
        using CGuildSetTemp186_clbk = void (WINAPIV*)(struct CGuild*, char*, CGuildSetTemp186_ptr);
        using CGuildSortRankInGuild188_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildSortRankInGuild188_clbk = void (WINAPIV*)(struct CGuild*, CGuildSortRankInGuild188_ptr);
        using CGuildSrcGuildIsAvailableBattleRequestState190_ptr = char (WINAPIV*)(struct CGuild*);
        using CGuildSrcGuildIsAvailableBattleRequestState190_clbk = char (WINAPIV*)(struct CGuild*, CGuildSrcGuildIsAvailableBattleRequestState190_ptr);
        using CGuildStartRankJob192_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuildStartRankJob192_clbk = void (WINAPIV*)(struct CGuild*, CGuildStartRankJob192_ptr);
        using CGuildUpdateEmblem194_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, unsigned int);
        using CGuildUpdateEmblem194_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, CGuildUpdateEmblem194_ptr);
        using CGuildUpdateGrade196_ptr = void (WINAPIV*)(struct CGuild*, char);
        using CGuildUpdateGrade196_clbk = void (WINAPIV*)(struct CGuild*, char, CGuildUpdateGrade196_ptr);
        using CGuildUpdateGuildBattleWinCnt198_ptr = void (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int);
        using CGuildUpdateGuildBattleWinCnt198_clbk = void (WINAPIV*)(struct CGuild*, unsigned int, unsigned int, unsigned int, CGuildUpdateGuildBattleWinCnt198_ptr);
        using CGuildUpdateUTATax200_ptr = void (WINAPIV*)(struct CGuild*, char);
        using CGuildUpdateUTATax200_clbk = void (WINAPIV*)(struct CGuild*, char, CGuildUpdateUTATax200_ptr);
        
        using CGuilddtor_CGuild206_ptr = void (WINAPIV*)(struct CGuild*);
        using CGuilddtor_CGuild206_clbk = void (WINAPIV*)(struct CGuild*, CGuilddtor_CGuild206_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

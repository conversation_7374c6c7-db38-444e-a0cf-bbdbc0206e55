// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct UsPoint
    {
        struct UsStateTBL *m_pObject;
    public:
        UsPoint(struct UsPoint* ptr)
        {
            using org_ptr = void (WINAPIV*)(struct UsPoint*, struct UsPoint*);
            (org_ptr(0x14014cbc0L))(this, ptr);
        };
        void ctor_UsPoint(struct UsPoint* ptr)
        {
            using org_ptr = void (WINAPIV*)(struct UsPoint*, struct UsPoint*);
            (org_ptr(0x14014cbc0L))(this, ptr);
        };
        UsPoint()
            : UsPoint((struct UsStateTBL*)nullptr)
        {
        };

        UsPoint(struct UsStateTBL* pObject)
        {
            using org_ptr = void (WINAPIV*)(struct UsPoint*, struct UsStateTBL*);
            (org_ptr(0x14014cc70L))(this, pObject);
        };
        void ctor_UsPoint(struct UsStateTBL* pObject)
        {
            using org_ptr = void (WINAPIV*)(struct UsPoint*, struct UsStateTBL*);
            (org_ptr(0x14014cc70L))(this, pObject);
        };
        ~UsPoint()
        {
            using org_ptr = void (WINAPIV*)(struct UsPoint*);
            (org_ptr(0x14014cce0L))(this);
        };
        void dtor_UsPoint()
        {
            using org_ptr = void (WINAPIV*)(UsPoint*);
            (org_ptr(0x14014cce0L))(this);
        };
    };
    static_assert(ATF::checkSize<UsPoint, 8>(), "UsPoint");
END_ATF_NAMESPACE

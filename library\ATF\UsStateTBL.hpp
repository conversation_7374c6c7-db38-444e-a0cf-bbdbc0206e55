// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <UsRefObject.hpp>
#include <Us_HFSM.hpp>


START_ATF_NAMESPACE
    struct  UsStateTBL : UsRefObject
    {
        struct _TBLData
        {
            char byKey;
            void (WINAPIV *pEvnetFun)(Us_HFSM *, unsigned int, void *);
        public:
            void Init();
            _TBLData();
            void ctor__TBLData();
        };
        struct _HFSM_Node_Info
        {
            int m_ParentData;
            unsigned int m_dwStartState;
            int m_bUsed;
            unsigned int m_dwLoopTime;
        public:
            void Init();
            _HFSM_Node_Info();
            void ctor__HFSM_Node_Info();
        };
        unsigned int m_IdentityKey;
        _TBLData **m_ppTBL;
        _HFSM_Node_Info *m_pNodeInfo;
        int m_bAlloc;
        char m_byHFSMSize;
        char m_byStateSize;
        char m_byMessageSize;
        void (WINAPIV *m_pFun)(Us_HFSM *, unsigned int, unsigned int, void *);
        int (WINAPIV *m_pInitFun)(UsStateTBL *, Us_HFSM *);
        void (WINAPIV *m_pExternFun)(Us_HFSM *, unsigned int, void *, int);
    public:
        int Add(char byHFSMIndex, char byCurrState, char byEvent_IN, char byNextState_OUT, void (WINAPIV* pEvnetFun)(struct Us_HFSM*, unsigned int, void*));
        void Alloc(char byHFSMSize, char byStateSize, char byMessageSize);
        void CleanUp();
        char GetHSFMSize();
        struct _TBLData* GetTransState(char byState, char byMessage);
        static void OnMsgProc(struct Us_HFSM* pHFS, unsigned int dwFSMIndex, unsigned int dwMSG, void* lpParam);
        void SetCallFunction(void (WINAPIV* pFun)(struct Us_HFSM*, unsigned int, unsigned int, void*));
        void SetExternCallFunction(void (WINAPIV* pExternFun)(struct Us_HFSM*, unsigned int, void*, int));
        int SetHFSM(struct Us_HFSM* pHFSM, void* pObject);
        void SetHFSMNode(int nNodeIndex, unsigned int dwStartState, unsigned int dwLoopTime, int ParentData, int bUsed);
        void SetInitFunction(int (WINAPIV* pInitFun)(struct UsStateTBL*, struct Us_HFSM*));
        UsStateTBL();
        void ctor_UsStateTBL();
        ~UsStateTBL();
        void dtor_UsStateTBL();
    };
END_ATF_NAMESPACE

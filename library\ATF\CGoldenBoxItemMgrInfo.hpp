// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGoldenBoxItemMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CGoldenBoxItemMgrBoxItemDataCopy2_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrBoxItemDataCopy2_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrBoxItemDataCopy2_ptr);
        using CGoldenBoxItemMgrBoxItemOpen4_ptr = struct _ItemExchange_fld::_output* (WINAPIV*)(struct CGoldenBoxItemMgr*, char);
        using CGoldenBoxItemMgrBoxItemOpen4_clbk = struct _ItemExchange_fld::_output* (WINAPIV*)(struct CGoldenBoxItemMgr*, char, CGoldenBoxItemMgrBoxItemOpen4_ptr);
        using CGoldenBoxItemMgrBoxItemOpenEffectType6_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char*, char*, char*, bool*);
        using CGoldenBoxItemMgrBoxItemOpenEffectType6_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char*, char*, char*, bool*, CGoldenBoxItemMgrBoxItemOpenEffectType6_ptr);
        
        using CGoldenBoxItemMgrctor_CGoldenBoxItemMgr8_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrctor_CGoldenBoxItemMgr8_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrctor_CGoldenBoxItemMgr8_ptr);
        using CGoldenBoxItemMgrCheck_Event_Status10_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrCheck_Event_Status10_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrCheck_Event_Status10_ptr);
        using CGoldenBoxItemMgrCheck_Loaded_Event_Status12_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrCheck_Loaded_Event_Status12_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrCheck_Loaded_Event_Status12_ptr);
        using CGoldenBoxItemMgrGetGodBoxItemInfoPtr14_ptr = struct _db_golden_box_item* (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrGetGodBoxItemInfoPtr14_clbk = struct _db_golden_box_item* (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrGetGodBoxItemInfoPtr14_ptr);
        using CGoldenBoxItemMgrGetGoldBoxItemIndex16_ptr = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*, uint16_t);
        using CGoldenBoxItemMgrGetGoldBoxItemIndex16_clbk = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*, uint16_t, CGoldenBoxItemMgrGetGoldBoxItemIndex16_ptr);
        using CGoldenBoxItemMgrGetGoldBoxItemPtr18_ptr = char* (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrGetGoldBoxItemPtr18_clbk = char* (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrGetGoldBoxItemPtr18_ptr);
        using CGoldenBoxItemMgrGetLoopCount20_ptr = char (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrGetLoopCount20_clbk = char (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrGetLoopCount20_ptr);
        using CGoldenBoxItemMgrGetOreItemTotalCnt22_ptr = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrGetOreItemTotalCnt22_clbk = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrGetOreItemTotalCnt22_ptr);
        using CGoldenBoxItemMgrGetStarterBoxCode24_ptr = char* (WINAPIV*)(struct CGoldenBoxItemMgr*, uint16_t);
        using CGoldenBoxItemMgrGetStarterBoxCode24_clbk = char* (WINAPIV*)(struct CGoldenBoxItemMgr*, uint16_t, CGoldenBoxItemMgrGetStarterBoxCode24_ptr);
        using CGoldenBoxItemMgrGet_BoxItem_Count26_ptr = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*, char, unsigned int);
        using CGoldenBoxItemMgrGet_BoxItem_Count26_clbk = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*, char, unsigned int, CGoldenBoxItemMgrGet_BoxItem_Count26_ptr);
        using CGoldenBoxItemMgrGet_Box_Count28_ptr = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*, char);
        using CGoldenBoxItemMgrGet_Box_Count28_clbk = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*, char, CGoldenBoxItemMgrGet_Box_Count28_ptr);
        using CGoldenBoxItemMgrGet_Event_Status30_ptr = char (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrGet_Event_Status30_clbk = char (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrGet_Event_Status30_ptr);
        using CGoldenBoxItemMgrGet_StarterBox_Count32_ptr = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrGet_StarterBox_Count32_clbk = uint16_t (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrGet_StarterBox_Count32_ptr);
        using CGoldenBoxItemMgrInitialize34_ptr = bool (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrInitialize34_clbk = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrInitialize34_ptr);
        using CGoldenBoxItemMgrInstance36_ptr = struct CGoldenBoxItemMgr* (WINAPIV*)();
        using CGoldenBoxItemMgrInstance36_clbk = struct CGoldenBoxItemMgr* (WINAPIV*)(CGoldenBoxItemMgrInstance36_ptr);
        using CGoldenBoxItemMgrIsBuyRaceBossGoldBox38_ptr = char (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*);
        using CGoldenBoxItemMgrIsBuyRaceBossGoldBox38_clbk = char (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, CGoldenBoxItemMgrIsBuyRaceBossGoldBox38_ptr);
        using CGoldenBoxItemMgrLoad_Event_INI40_ptr = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, struct _golden_box_item_ini*);
        using CGoldenBoxItemMgrLoad_Event_INI40_clbk = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, struct _golden_box_item_ini*, CGoldenBoxItemMgrLoad_Event_INI40_ptr);
        using CGoldenBoxItemMgrLoad_Golden_Box_Item_Event42_ptr = bool (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrLoad_Golden_Box_Item_Event42_clbk = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrLoad_Golden_Box_Item_Event42_ptr);
        using CGoldenBoxItemMgrLoop_Event44_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrLoop_Event44_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrLoop_Event44_ptr);
        using CGoldenBoxItemMgrPushDQSUpdate46_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrPushDQSUpdate46_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrPushDQSUpdate46_ptr);
        using CGoldenBoxItemMgrRateCheck48_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char);
        using CGoldenBoxItemMgrRateCheck48_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char, CGoldenBoxItemMgrRateCheck48_ptr);
        using CGoldenBoxItemMgrSendMsg_RaceChat50_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, char*);
        using CGoldenBoxItemMgrSendMsg_RaceChat50_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, char*, CGoldenBoxItemMgrSendMsg_RaceChat50_ptr);
        using CGoldenBoxItemMgrSetDBSerial52_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, int);
        using CGoldenBoxItemMgrSetDBSerial52_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, int, CGoldenBoxItemMgrSetDBSerial52_ptr);
        using CGoldenBoxItemMgrSetGoldBoxItemIndex54_ptr = bool (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrSetGoldBoxItemIndex54_clbk = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrSetGoldBoxItemIndex54_ptr);
        using CGoldenBoxItemMgrSet_BoxItem_Count56_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char, unsigned int);
        using CGoldenBoxItemMgrSet_BoxItem_Count56_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char, unsigned int, CGoldenBoxItemMgrSet_BoxItem_Count56_ptr);
        using CGoldenBoxItemMgrSet_Box_Count58_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char);
        using CGoldenBoxItemMgrSet_Box_Count58_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char, CGoldenBoxItemMgrSet_Box_Count58_ptr);
        using CGoldenBoxItemMgrSet_DCK60_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char);
        using CGoldenBoxItemMgrSet_DCK60_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char, CGoldenBoxItemMgrSet_DCK60_ptr);
        using CGoldenBoxItemMgrSet_Event_Status62_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char);
        using CGoldenBoxItemMgrSet_Event_Status62_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, char, CGoldenBoxItemMgrSet_Event_Status62_ptr);
        using CGoldenBoxItemMgrSet_FromINIToStruct64_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, struct _golden_box_item_ini*);
        using CGoldenBoxItemMgrSet_FromINIToStruct64_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, struct _golden_box_item_ini*, CGoldenBoxItemMgrSet_FromINIToStruct64_ptr);
        using CGoldenBoxItemMgrSet_FromStruct66_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrSet_FromStruct66_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrSet_FromStruct66_ptr);
        using CGoldenBoxItemMgrSet_StarterBox_Count68_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, unsigned int, bool);
        using CGoldenBoxItemMgrSet_StarterBox_Count68_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, unsigned int, bool, CGoldenBoxItemMgrSet_StarterBox_Count68_ptr);
        using CGoldenBoxItemMgrSet_ToStruct70_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrSet_ToStruct70_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrSet_ToStruct70_ptr);
        using CGoldenBoxItemMgrStarterBox_InsertToInven72_ptr = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, char*);
        using CGoldenBoxItemMgrStarterBox_InsertToInven72_clbk = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, char*, CGoldenBoxItemMgrStarterBox_InsertToInven72_ptr);
        using CGoldenBoxItemMgrSynchINIANDDB74_ptr = bool (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrSynchINIANDDB74_clbk = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrSynchINIANDDB74_ptr);
        using CGoldenBoxItemMgrWriteEventCouponLog76_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, struct _STORAGE_LIST::_db_con*);
        using CGoldenBoxItemMgrWriteEventCouponLog76_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, struct _STORAGE_LIST::_db_con*, CGoldenBoxItemMgrWriteEventCouponLog76_ptr);
        using CGoldenBoxItemMgrWriteGetGoldBarLog78_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, struct _STORAGE_LIST::_db_con*);
        using CGoldenBoxItemMgrWriteGetGoldBarLog78_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, struct _STORAGE_LIST::_db_con*, CGoldenBoxItemMgrWriteGetGoldBarLog78_ptr);
        using CGoldenBoxItemMgr_init_loggers80_ptr = bool (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgr_init_loggers80_clbk = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgr_init_loggers80_ptr);
        using CGoldenBoxItemMgr_insert_to_inven82_ptr = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, char, uint16_t);
        using CGoldenBoxItemMgr_insert_to_inven82_clbk = bool (WINAPIV*)(struct CGoldenBoxItemMgr*, struct CPlayer*, char, uint16_t, CGoldenBoxItemMgr_insert_to_inven82_ptr);
        
        using CGoldenBoxItemMgrdtor_CGoldenBoxItemMgr84_ptr = void (WINAPIV*)(struct CGoldenBoxItemMgr*);
        using CGoldenBoxItemMgrdtor_CGoldenBoxItemMgr84_clbk = void (WINAPIV*)(struct CGoldenBoxItemMgr*, CGoldenBoxItemMgrdtor_CGoldenBoxItemMgr84_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

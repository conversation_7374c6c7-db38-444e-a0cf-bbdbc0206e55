#include <_personal_automine_alter_dur_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_alter_dur_zocl::_personal_automine_alter_dur_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_alter_dur_zocl*);
        (org_ptr(0x1402de290L))(this);
    };
    void _personal_automine_alter_dur_zocl::ctor__personal_automine_alter_dur_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_alter_dur_zocl*);
        (org_ptr(0x1402de290L))(this);
    };
    int _personal_automine_alter_dur_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_alter_dur_zocl*);
        return (org_ptr(0x1402de2e0L))(this);
    };
END_ATF_NAMESPACE

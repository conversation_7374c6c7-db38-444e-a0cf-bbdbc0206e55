// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SEC_WINNT_AUTH_IDENTITY_W.hpp>


START_ATF_NAMESPACE
    struct _RPC_HTTP_TRANSPORT_CREDENTIALS_W
    {
        _SEC_WINNT_AUTH_IDENTITY_W *TransportCredentials;
        unsigned int Flags;
        unsigned int AuthenticationTarget;
        unsigned int NumberOfAuthnSchemes;
        unsigned int *AuthnSchemes;
        unsigned __int16 *ServerCertificateSubject;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 8)
        template<typename _Ty1, typename _Ty2>
        struct pair
        {
            _Ty1 first;
            _Ty2 second;
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum enumDMUS_SEGF_FLAGS
    {
      DMUS_SEGF_REFTIME = 0x40,
      DMUS_SEGF_SECONDARY = 0x80,
      DMUS_SEGF_QUEUE = 0x100,
      DMUS_SEGF_CONTROL = 0x200,
      DMUS_SEGF_AFTERPREPARETIME = 0x400,
      DMUS_SEGF_GRID = 0x800,
      DMUS_SEGF_BEAT = 0x1000,
      DMUS_SEGF_MEASURE = 0x2000,
      DMUS_SEGF_DEFAULT = 0x4000,
      DMUS_SEGF_NOINVALIDATE = 0x8000,
      DMUS_SEGF_ALIGN = 0x10000,
      DMUS_SEGF_VALID_START_BEAT = 0x20000,
      DMUS_SEGF_VALID_START_GRID = 0x40000,
      DMUS_SEGF_VALID_START_TICK = 0x80000,
      DMUS_SEGF_AUTOTRANSITION = 0x100000,
      DMUS_SEGF_AFTERQUEUETIME = 0x200000,
      DMUS_SEGF_AFTERLATENCYTIME = 0x400000,
      DMUS_SEGF_SEGMENTEND = 0x800000,
      DMUS_SEGF_MARKER = 0x1000000,
      DMUS_SEGF_TIMESIG_ALWAYS = 0x2000000,
      DMUS_SEGF_USE_AUDIOPATH = 0x4000000,
      DMUS_SEGF_VALID_START_MEASURE = 0x8000000,
      DMUS_SEGF_INVALIDATE_PRI = 0x10000000,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct C24Timer
    {
        unsigned int m_dwBaseTickTime;
        unsigned int m_dwBase24Time;
    public:
        C24Timer();
        void ctor_C24Timer();
        unsigned int Get24TimeFromTickTime(unsigned int dwTickTime);
        void Init();
        ~C24Timer();
        void dtor_C24Timer();
    };    
    static_assert(ATF::checkSize<C24Timer, 8>(), "C24Timer");
END_ATF_NAMESPACE

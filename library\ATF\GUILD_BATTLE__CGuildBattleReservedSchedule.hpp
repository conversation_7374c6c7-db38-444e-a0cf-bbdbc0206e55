// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleSchedule.hpp>
#include <_worlddb_guild_battle_schedule_list.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleReservedSchedule
        {
            unsigned int m_uiScheduleListID;
            bool m_bDone;
            unsigned int m_uiCurScheduleInx;
            bool m_bUseField[23];
            CGuildBattleSchedule *m_pkSchedule[23];
        public:
            char Add(unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, struct CGuildBattleSchedule** ppkSchedule);
            CGuildBattleReservedSchedule(unsigned int uiScheduleListID);
            void ctor_CGuildBattleReservedSchedule(unsigned int uiScheduleListID);
            bool CheckNextEvent(int iRet);
            bool CleanUpDanglingReservedSchedule();
            bool Clear(unsigned int dwID);
            void Clear();
            void ClearElapsedSchedule();
            bool CopyUseTimeField(bool* pbField);
            void Flip();
            unsigned int GetCurScheduleID();
            unsigned int GetID();
            bool IsDone();
            char IsEmptyTime(unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt);
            bool Load(bool bToday, struct _worlddb_guild_battle_schedule_list* pkInfo);
            bool Loop();
            bool Next();
            void UpdateUseField(unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt);
            struct CGuildBattleSchedule* UpdateUseFlag(unsigned int dwID);
            ~CGuildBattleReservedSchedule();
            void dtor_CGuildBattleReservedSchedule();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

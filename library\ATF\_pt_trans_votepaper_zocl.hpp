// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pt_trans_votepaper_zocl
    {
        struct  __body
        {
            char byRank;
            char wszAvator<PERSON>ame[17];
            char wszGuildName[17];
            unsigned int dwWinCnt;
        };
        char byCnt;
        __body body[8];
    public:
        _pt_trans_votepaper_zocl();
        void ctor__pt_trans_votepaper_zocl();
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

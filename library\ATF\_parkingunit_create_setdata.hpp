// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <_object_create_setdata.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _parkingunit_create_setdata : _object_create_setdata
    {
        CPlayer *pOwner;
        char byCreateType;
        char byFrame;
        char byPartCode[6];
        char byTransDistCode;
        unsigned __int16 wHPRate;
    public:
        _parkingunit_create_setdata();
        void ctor__parkingunit_create_setdata();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_parkingunit_create_setdata, 56>(), "_parkingunit_create_setdata");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct CR3Font
    {
    public:
        int CalcStrIndexPitInWidthA(char* arg_0, int arg_1, int arg_2);
        int CalcStrIndexPitInWidthW(wchar_t* arg_0, int arg_1, int arg_2);
        void CalcStrPixelSizeA(char* arg_0, struct tagSIZE* arg_1, int arg_2);
        void CalcStrPixelSizeW(wchar_t* arg_0, struct tagSIZE* arg_1, int arg_2);
        void ClearCache();
        int32_t DeleteDeviceObjects();
        void DrawFullText(struct FONT2DVERTEX* arg_0);
        int32_t DrawTextA(float* arg_0, uint32_t arg_1, char* arg_2, uint32_t arg_3, float arg_4);
        int32_t DrawTextA(float arg_0, float arg_1, uint32_t arg_2, char* arg_3, float** arg_4, uint32_t arg_5, float arg_6);
        int32_t DrawTextA(float arg_0, float arg_1, uint32_t arg_2, char* arg_3, uint32_t arg_4, float arg_5);
        int32_t DrawTextW(float* arg_0, uint32_t arg_1, wchar_t* arg_2, uint32_t arg_3, float arg_4);
        int32_t DrawTextW(float arg_0, float arg_1, uint32_t arg_2, wchar_t* arg_3, float** arg_4, uint32_t arg_5, float arg_6);
        int32_t DrawTextW(float arg_0, float arg_1, uint32_t arg_2, wchar_t* arg_3, uint32_t arg_4, float arg_5);
        int32_t FillItA(struct FONT2DVERTEX* arg_0, float* arg_1, uint32_t arg_2, char* arg_3, uint32_t arg_4, float arg_5);
        int32_t FillItW(wchar_t* arg_0, float* arg_1, uint32_t arg_2, wchar_t* arg_3, uint32_t arg_4, float arg_5);
        void GetBestPosCacheA(uint32_t arg_0, uint32_t* arg_1, uint32_t* arg_2, uint32_t* arg_3);
        void GetBestPosCacheW(wchar_t* arg_0, uint32_t arg_1, int* arg_2, int* arg_3, int* arg_4, int* arg_5);
        uint32_t GetOutLineColor();
        int32_t InitDeviceObjects(struct IDirect3DDevice8* arg_0, uint32_t arg_1, uint32_t arg_2, uint32_t arg_3);
        int32_t InvalidateDeviceObjects();
        int64_t IsExistCacheA(char* arg_0, uint32_t arg_1, uint32_t* arg_2, uint32_t* arg_3);
        int64_t IsExistCacheW(wchar_t* arg_0, uint32_t arg_1, int* arg_2, int* arg_3);
        void MemAllocate();
        void MemFree();
        void PrepareDrawText();
        void PrivateInit();
        void PrivateRelease();
        int32_t RestoreDeviceObjects();
        void SetCacheA(char* arg_0, uint32_t arg_1, uint32_t arg_2, uint32_t arg_3, uint32_t arg_4);
        void SetCacheW(wchar_t* arg_0, uint32_t arg_1, int arg_2, int arg_3, int arg_4, int arg_5);
        void SetCharSet(uint32_t arg_0);
        void SetFont(char* arg_0);
        void SetOutLineColor(uint32_t arg_0);
        ~CR3Font();
        int64_t dtor_CR3Font();
    }
    ;
END_ATF_NAMESPACE

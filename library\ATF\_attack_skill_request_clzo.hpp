// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _attack_skill_request_clzo
    {
        char byID;
         unsigned __int16 wIndex;
        char byEffectCode;
        unsigned __int16 wSkillIndex;
        unsigned __int16 wBulletSerial;
        __int16 zAreaPos[2];
        unsigned __int16 wConsumeItemSerial[3];
        unsigned __int16 wEffBulletSerial;
    };
END_ATF_NAMESPACE

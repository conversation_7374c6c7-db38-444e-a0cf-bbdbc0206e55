// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_alive_char.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_alive_charctor__qry_case_alive_char2_ptr = void (WINAPIV*)(struct _qry_case_alive_char*);
        using _qry_case_alive_charctor__qry_case_alive_char2_clbk = void (WINAPIV*)(struct _qry_case_alive_char*, _qry_case_alive_charctor__qry_case_alive_char2_ptr);
        using _qry_case_alive_charsize4_ptr = int (WINAPIV*)(struct _qry_case_alive_char*);
        using _qry_case_alive_charsize4_clbk = int (WINAPIV*)(struct _qry_case_alive_char*, _qry_case_alive_charsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

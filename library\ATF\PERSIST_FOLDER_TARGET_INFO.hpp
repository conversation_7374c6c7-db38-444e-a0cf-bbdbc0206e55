// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ITEMIDLIST.hpp>



START_ATF_NAMESPACE
    struct PERSIST_FOLDER_TARGET_INFO
    {
        _ITEMIDLIST *pidlTargetFolder;
        wchar_t szTargetParsingName[260];
        wchar_t szNetworkProvider[260];
        unsigned int dwAttributes;
        int csidl;
    };
END_ATF_NAMESPACE

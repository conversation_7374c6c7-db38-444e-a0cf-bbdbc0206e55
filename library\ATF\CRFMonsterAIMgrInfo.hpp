// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRFMonsterAIMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRFMonsterAIMgrctor_CRFMonsterAIMgr2_ptr = void (WINAPIV*)(struct CRFMonsterAIMgr*);
        using CRFMonsterAIMgrctor_CRFMonsterAIMgr2_clbk = void (WINAPIV*)(struct CRFMonsterAIMgr*, CRFMonsterAIMgrctor_CRFMonsterAIMgr2_ptr);
        using CRFMonsterAIMgrDestory4_ptr = void (WINAPIV*)();
        using CRFMonsterAIMgrDestory4_clbk = void (WINAPIV*)(CRFMonsterAIMgrDestory4_ptr);
        using CRFMonsterAIMgrGetStateTBL6_ptr = struct UsPoint* (WINAPIV*)(struct CRFMonsterAIMgr*, struct UsPoint*, int);
        using CRFMonsterAIMgrGetStateTBL6_clbk = struct UsPoint* (WINAPIV*)(struct CRFMonsterAIMgr*, struct UsPoint*, int, CRFMonsterAIMgrGetStateTBL6_ptr);
        using CRFMonsterAIMgrInstance8_ptr = struct CRFMonsterAIMgr* (WINAPIV*)();
        using CRFMonsterAIMgrInstance8_clbk = struct CRFMonsterAIMgr* (WINAPIV*)(CRFMonsterAIMgrInstance8_ptr);
        
        using CRFMonsterAIMgrdtor_CRFMonsterAIMgr12_ptr = void (WINAPIV*)(struct CRFMonsterAIMgr*);
        using CRFMonsterAIMgrdtor_CRFMonsterAIMgr12_clbk = void (WINAPIV*)(struct CRFMonsterAIMgr*, CRFMonsterAIMgrdtor_CRFMonsterAIMgr12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

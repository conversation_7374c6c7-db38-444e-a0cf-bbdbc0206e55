// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CAtlModuleVtbl.hpp>
#include <ATL___ATL_MODULE70.hpp>
#include <IGlobalInterfaceTable.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct  CAtlModule : _ATL_MODULE70
        {
            CAtlModuleVtbl *vfptr;
            IGlobalInterfaceTable *m_pGIT;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

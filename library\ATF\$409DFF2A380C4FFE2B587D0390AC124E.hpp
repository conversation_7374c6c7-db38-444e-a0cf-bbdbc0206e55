// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $409DFF2A380C4FFE2B587D0390AC124E
    {
        unsigned __int16 wProcessorArchitecture;
        unsigned __int16 wReserved;
    };    
    static_assert(ATF::checkSize<$409DFF2A380C4FFE2B587D0390AC124E, 4>(), "$409DFF2A380C4FFE2B587D0390AC124E");
END_ATF_NAMESPACE

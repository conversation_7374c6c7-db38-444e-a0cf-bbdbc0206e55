// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned __int64 *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned __int64>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<char>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned int>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<IUnknown * *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<int>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<double *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<tagVARIANT>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned int *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<short *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<double>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned char *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned short>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<float>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<__int64>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<wchar_t *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<long *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned short *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<int *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<wchar_t * *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<float *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<IDispatch * *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<short>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<char *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<IUnknown *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned long *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<tagCY>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<__int64 *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<long>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned char>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<tagCY *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<IDispatch *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CVarTypeInfo<unsigned long>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

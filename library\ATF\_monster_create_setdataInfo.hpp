// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_monster_create_setdata.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _monster_create_setdatactor__monster_create_setdata2_ptr = void (WINAPIV*)(struct _monster_create_setdata*);
        using _monster_create_setdatactor__monster_create_setdata2_clbk = void (WINAPIV*)(struct _monster_create_setdata*, _monster_create_setdatactor__monster_create_setdata2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

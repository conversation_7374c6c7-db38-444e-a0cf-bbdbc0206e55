// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCheckSumCharacAccountTrunkData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCheckSumCharacAccountTrunkDatactor_CCheckSumCharacAccountTrunkData2_ptr = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, unsigned int, unsigned int, char);
        using CCheckSumCharacAccountTrunkDatactor_CCheckSumCharacAccountTrunkData2_clbk = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, unsigned int, unsigned int, char, CCheckSumCharacAccountTrunkDatactor_CCheckSumCharacAccountTrunkData2_ptr);
        using CCheckSumCharacAccountTrunkDataCheckDiff4_ptr = int (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*, char*, struct CCheckSumCharacAccountTrunkData*);
        using CCheckSumCharacAccountTrunkDataCheckDiff4_clbk = int (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*, char*, struct CCheckSumCharacAccountTrunkData*, CCheckSumCharacAccountTrunkDataCheckDiff4_ptr);
        using CCheckSumCharacAccountTrunkDataDecode6_ptr = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct _AVATOR_DATA*);
        using CCheckSumCharacAccountTrunkDataDecode6_clbk = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct _AVATOR_DATA*, CCheckSumCharacAccountTrunkDataDecode6_ptr);
        using CCheckSumCharacAccountTrunkDataEncode8_ptr = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct _AVATOR_DATA*);
        using CCheckSumCharacAccountTrunkDataEncode8_clbk = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct _AVATOR_DATA*, CCheckSumCharacAccountTrunkDataEncode8_ptr);
        using CCheckSumCharacAccountTrunkDataInsertCharacData10_ptr = bool (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*);
        using CCheckSumCharacAccountTrunkDataInsertCharacData10_clbk = bool (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*, CCheckSumCharacAccountTrunkDataInsertCharacData10_ptr);
        using CCheckSumCharacAccountTrunkDataInsertTrunkData12_ptr = bool (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*);
        using CCheckSumCharacAccountTrunkDataInsertTrunkData12_clbk = bool (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*, CCheckSumCharacAccountTrunkDataInsertTrunkData12_ptr);
        using CCheckSumCharacAccountTrunkDataLoad14_ptr = int (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*, struct CCheckSumCharacAccountTrunkData*);
        using CCheckSumCharacAccountTrunkDataLoad14_clbk = int (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*, struct CCheckSumCharacAccountTrunkData*, CCheckSumCharacAccountTrunkDataLoad14_ptr);
        using CCheckSumCharacAccountTrunkDataSetValue16_ptr = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, CCheckSumCharacAccountTrunkData::COLUMN_DW_TYPE, unsigned int);
        using CCheckSumCharacAccountTrunkDataSetValue16_clbk = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, CCheckSumCharacAccountTrunkData::COLUMN_DW_TYPE, unsigned int, CCheckSumCharacAccountTrunkDataSetValue16_ptr);
        using CCheckSumCharacAccountTrunkDataSetValue18_ptr = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, CCheckSumCharacAccountTrunkData::COLUMN_D_TYPE, long double);
        using CCheckSumCharacAccountTrunkDataSetValue18_clbk = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, CCheckSumCharacAccountTrunkData::COLUMN_D_TYPE, long double, CCheckSumCharacAccountTrunkDataSetValue18_ptr);
        using CCheckSumCharacAccountTrunkDataUpdate20_ptr = bool (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*);
        using CCheckSumCharacAccountTrunkDataUpdate20_clbk = bool (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, struct CRFWorldDatabase*, CCheckSumCharacAccountTrunkDataUpdate20_ptr);
        
        using CCheckSumCharacAccountTrunkDatadtor_CCheckSumCharacAccountTrunkData22_ptr = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*);
        using CCheckSumCharacAccountTrunkDatadtor_CCheckSumCharacAccountTrunkData22_clbk = void (WINAPIV*)(struct CCheckSumCharacAccountTrunkData*, CCheckSumCharacAccountTrunkDatadtor_CCheckSumCharacAccountTrunkData22_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

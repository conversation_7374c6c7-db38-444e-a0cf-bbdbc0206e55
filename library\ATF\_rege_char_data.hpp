// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _rege_char_data
    {
        struct _reged_list
        {
            char bySlotIndex;
            unsigned int dwCharSerial;
            char szCharName[17];
            int nLevel;
            unsigned int dwDalant;
            unsigned int dwGold;
        };
        int nCharNum;
        _reged_list RegeList[3];
    public:
        _rege_char_data();
        void ctor__rege_char_data();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_rege_char_data, 124>(), "_rege_char_data");
END_ATF_NAMESPACE

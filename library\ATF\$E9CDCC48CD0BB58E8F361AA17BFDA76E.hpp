// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $E9CDCC48CD0BB58E8F361AA17BFDA76E
    {
        BYTE gap0[8];
        __int64 *pllVal;
    };    
    static_assert(ATF::checkSize<$E9CDCC48CD0BB58E8F361AA17BFDA76E, 16>(), "$E9CDCC48CD0BB58E8F361AA17BFDA76E");
END_ATF_NAMESPACE

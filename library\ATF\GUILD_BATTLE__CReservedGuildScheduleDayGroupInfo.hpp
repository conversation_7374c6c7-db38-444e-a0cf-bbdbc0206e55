// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CReservedGuildScheduleDayGroup.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupctor_CReservedGuildScheduleDayGroup2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupctor_CReservedGuildScheduleDayGroup2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, GUILD_BATTLE__CReservedGuildScheduleDayGroupctor_CReservedGuildScheduleDayGroup2_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupClear4_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupClear4_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, GUILD_BATTLE__CReservedGuildScheduleDayGroupClear4_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupFind6_ptr = struct GUILD_BATTLE::CReservedGuildSchedulePage* (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, unsigned int);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupFind6_clbk = struct GUILD_BATTLE::CReservedGuildSchedulePage* (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, unsigned int, GUILD_BATTLE__CReservedGuildScheduleDayGroupFind6_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupFlip8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupFlip8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, GUILD_BATTLE__CReservedGuildScheduleDayGroupFlip8_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupInit10_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, unsigned int);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupInit10_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, unsigned int, GUILD_BATTLE__CReservedGuildScheduleDayGroupInit10_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupLoad12_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, char, unsigned int, struct _worlddb_guild_battle_reserved_schedule_info*);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupLoad12_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, char, unsigned int, struct _worlddb_guild_battle_reserved_schedule_info*, GUILD_BATTLE__CReservedGuildScheduleDayGroupLoad12_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupSend14_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, char, unsigned int, int, unsigned int, char, unsigned int);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupSend14_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, char, unsigned int, int, unsigned int, char, unsigned int, GUILD_BATTLE__CReservedGuildScheduleDayGroupSend14_ptr);
            
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupdtor_CReservedGuildScheduleDayGroup16_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*);
            using GUILD_BATTLE__CReservedGuildScheduleDayGroupdtor_CReservedGuildScheduleDayGroup16_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleDayGroup*, GUILD_BATTLE__CReservedGuildScheduleDayGroupdtor_CReservedGuildScheduleDayGroup16_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

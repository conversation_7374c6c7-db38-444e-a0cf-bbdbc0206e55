// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $E1D71D20C3D01115E20D79FAE9629CE8
    {
        BYTE gap0[8];
        char *pcVal;
    };    
    static_assert(ATF::checkSize<$E1D71D20C3D01115E20D79FAE9629CE8, 16>(), "$E1D71D20C3D01115E20D79FAE9629CE8");
END_ATF_NAMESPACE

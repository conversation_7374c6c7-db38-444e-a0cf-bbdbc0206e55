// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagDBID.hpp>
#include <tagVARIANT.hpp>


START_ATF_NAMESPACE
    struct tagDBPROP
    {
        unsigned int dwPropertyID;
        unsigned int dwOptions;
        unsigned int dwStatus;
        tagDBID colid;
        tagVARIANT vValue;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$5F12993BB80B062326247C4784E48DC0.hpp>



START_ATF_NAMESPACE
    struct SrcHeaderW
    {
        unsigned int cb;
        unsigned int ver;
        unsigned int sig;
        unsigned int cbSource;
        char srccompress;
        $5F12993BB80B062326247C4784E48DC0 ___u5;
        wchar_t szNames[1];
    };
END_ATF_NAMESPACE

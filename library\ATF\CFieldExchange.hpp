// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CDumpContext.hpp>
#include <CRecordset.hpp>


START_ATF_NAMESPACE
    struct CFieldExchange
    {
        enum RFX_Operation
        {
            BindParam = 0x0,
            RebindParam = 0x1,
            BindFieldToColumn = 0x2,
            BindFieldForUpdate = 0x3,
            UnbindFieldForUpdate = 0x4,
            Fixup = 0x5,
            MarkForAddNew = 0x6,
            MarkForUpdate = 0x7,
            Name = 0x8,
            NameValue = 0x9,
            Value = 0xA,
            SetFieldNull = 0xB,
            StoreField = 0xC,
            LoadField = 0xD,
            AllocCache = 0xE,
            AllocMultiRowBuffer = 0xF,
            DeleteMultiRowBuffer = 0x10,
            DumpField = 0x11,
        };
        enum FieldType
        {
            noFieldType = 0xFFFFFFFF,
            outputColumn = 0x0,
            param = 0x1,
            inputParam = 0x1,
            outputParam = 0x4,
            inoutParam = 0x2,
        };
        unsigned int m_nOperation;
        CRecordset *m_prs;
        unsigned int m_nFieldType;
        unsigned int m_nFieldFound;
        ATL::CStringT<char> *m_pstr;
        int m_bField;
        void *m_pvField;
        const char *m_lpszSeparator;
        unsigned int m_nFields;
        unsigned int m_nParams;
        unsigned int m_nParamFields;
        void *m_hstmt;
        __int64 m_lDefaultLBFetchSize;
        __int64 m_lDefaultLBReallocSize;
        CDumpContext *m_pdcDump;
    };
END_ATF_NAMESPACE

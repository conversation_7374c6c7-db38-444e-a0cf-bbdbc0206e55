// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _keeper_create_zocl
    {
        unsigned __int16 wRecIndex;
        unsigned __int16 wIndex;
        unsigned int dwSerial;
        __int16 zPos[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

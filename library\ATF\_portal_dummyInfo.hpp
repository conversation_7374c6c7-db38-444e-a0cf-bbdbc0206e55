// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_portal_dummy.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _portal_dummySetDummy2_ptr = bool (WINAPIV*)(struct _portal_dummy*, struct _portal_fld*, struct _dummy_position*);
        using _portal_dummySetDummy2_clbk = bool (WINAPIV*)(struct _portal_dummy*, struct _portal_fld*, struct _dummy_position*, _portal_dummySetDummy2_ptr);
        
        using _portal_dummyctor__portal_dummy4_ptr = void (WINAPIV*)(struct _portal_dummy*);
        using _portal_dummyctor__portal_dummy4_clbk = void (WINAPIV*)(struct _portal_dummy*, _portal_dummyctor__portal_dummy4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

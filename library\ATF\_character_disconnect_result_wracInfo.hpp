// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_character_disconnect_result_wrac.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _character_disconnect_result_wracsize2_ptr = int (WINAPIV*)(struct _character_disconnect_result_wrac*);
        using _character_disconnect_result_wracsize2_clbk = int (WINAPIV*)(struct _character_disconnect_result_wrac*, _character_disconnect_result_wracsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

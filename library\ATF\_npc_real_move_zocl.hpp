// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _npc_real_move_zocl
    {
        unsigned __int16 wRecIndex;
        unsigned __int16 wIndex;
        unsigned int dwSerial;
        unsigned __int16 wLastEffectCode;
        __int16 zCur[3];
        __int16 zTar[2];
    };
END_ATF_NAMESPACE

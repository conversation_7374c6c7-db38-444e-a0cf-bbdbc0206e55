// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $3A4D00B72E001E6A2E13ED3F4EBA8378
    {
      eLevel_ContSF_Time_Skill = 0x0,
      eLevel_ContSF_Time_Force = 0x1,
      eLevel_ContSF_Time_Class = 0x2,
      eLevel_ContSF_Time_Throw = 0x3,
      eCommontMax = 0x4,
    };
END_ATF_NAMESPACE

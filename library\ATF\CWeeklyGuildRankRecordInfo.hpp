// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CWeeklyGuildRankRecord.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CWeeklyGuildRankRecordctor_CWeeklyGuildRankRecord2_ptr = void (WINAPIV*)(struct CWeeklyGuildRankRecord*);
        using CWeeklyGuildRankRecordctor_CWeeklyGuildRankRecord2_clbk = void (WINAPIV*)(struct CWeeklyGuildRankRecord*, CWeeklyGuildRankRecordctor_CWeeklyGuildRankRecord2_ptr);
        using CWeeklyGuildRankRecordClear4_ptr = void (WINAPIV*)(struct CWeeklyGuildRankRecord*);
        using CWeeklyGuildRankRecordClear4_clbk = void (WINAPIV*)(struct CWeeklyGuildRankRecord*, CWeeklyGuildRankRecordClear4_ptr);
        
        using CWeeklyGuildRankRecorddtor_CWeeklyGuildRankRecord8_ptr = void (WINAPIV*)(struct CWeeklyGuildRankRecord*);
        using CWeeklyGuildRankRecorddtor_CWeeklyGuildRankRecord8_clbk = void (WINAPIV*)(struct CWeeklyGuildRankRecord*, CWeeklyGuildRankRecorddtor_CWeeklyGuildRankRecord8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

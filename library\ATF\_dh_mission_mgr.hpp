// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _dh_mission_mgr
    {
        enum E_POTAL_NUM
        {
            max_portal_quest = 0x80,
        };
        struct _count
        {
            int nCount;
            bool bPass;
        public:
            void Init();
        };
        struct _if_change
        {
            struct _dh_mission_setup *pMissionPtr;
            char *pszDespt;
            char *pszComMsg;
        public:
            void Init();
            bool IsFill();
            _if_change();
            void ctor__if_change();
        };
        struct _respawn_monster_act
        {
            struct _monster_data
            {
                struct CMonster *pMon;
                unsigned int dwSerial;
            };
            struct __respawn_monster *pData;
            int nCum;
            unsigned int dwLastRespawnTime;
            _monster_data NowMonster[128];
            bool bStart;
        public:
            _respawn_monster_act();
            void ctor__respawn_monster_act();
            void init();
            void set(struct __respawn_monster* data);
        };
        _dh_mission_setup *pCurMssionPtr;
        _count Count[8];
        bool bOpenPortal[128];
        unsigned int dwMissionStartTime;
        unsigned int dwMissionEndTime;
        int nAddLimMSecTime;
        bool bInnerCheck[64];
        _if_change IfCont[100];
        int nRespawnActNum;
        _respawn_monster_act RespawnMonsterAct[32];
    public:
        unsigned int GetLimMSecTime();
        struct _if_change* GetMissionCont(struct _dh_mission_setup* pMsSetup);
        void Init();
        bool IsOpenPortal(int nIndex);
        void NextMission(struct _dh_mission_setup* pNextMssionPtr);
        void OpenPortal(int nIndex);
        struct _if_change* SearchCurMissionCont();
        _dh_mission_mgr();
        void ctor__dh_mission_mgr();
    };    
    static_assert(ATF::checkSize<_dh_mission_mgr, 68992>(), "_dh_mission_mgr");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderRequestLimiter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderRequestLimiterctor_CUnmannedTraderRequestLimiter2_ptr = void (WINAPIV*)(struct CUnmannedTraderRequestLimiter*);
        using CUnmannedTraderRequestLimiterctor_CUnmannedTraderRequestLimiter2_clbk = void (WINAPIV*)(struct CUnmannedTraderRequestLimiter*, CUnmannedTraderRequestLimiterctor_CUnmannedTraderRequestLimiter2_ptr);
        using CUnmannedTraderRequestLimiterClearRequset4_ptr = void (WINAPIV*)(struct CUnmannedTraderRequestLimiter*);
        using CUnmannedTraderRequestLimiterClearRequset4_clbk = void (WINAPIV*)(struct CUnmannedTraderRequestLimiter*, CUnmannedTraderRequestLimiterClearRequset4_ptr);
        using CUnmannedTraderRequestLimiterIsEmpty6_ptr = bool (WINAPIV*)(struct CUnmannedTraderRequestLimiter*);
        using CUnmannedTraderRequestLimiterIsEmpty6_clbk = bool (WINAPIV*)(struct CUnmannedTraderRequestLimiter*, CUnmannedTraderRequestLimiterIsEmpty6_ptr);
        using CUnmannedTraderRequestLimiterSetRequest8_ptr = void (WINAPIV*)(struct CUnmannedTraderRequestLimiter*, int);
        using CUnmannedTraderRequestLimiterSetRequest8_clbk = void (WINAPIV*)(struct CUnmannedTraderRequestLimiter*, int, CUnmannedTraderRequestLimiterSetRequest8_ptr);
        
        using CUnmannedTraderRequestLimiterdtor_CUnmannedTraderRequestLimiter10_ptr = void (WINAPIV*)(struct CUnmannedTraderRequestLimiter*);
        using CUnmannedTraderRequestLimiterdtor_CUnmannedTraderRequestLimiter10_clbk = void (WINAPIV*)(struct CUnmannedTraderRequestLimiter*, CUnmannedTraderRequestLimiterdtor_CUnmannedTraderRequestLimiter10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHolyStoneSystemDataMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CHolyStoneSystemDataMgrLoadIni2_ptr = bool (WINAPIV*)(struct CHolyStoneSystem*);
        using CHolyStoneSystemDataMgrLoadIni2_clbk = bool (WINAPIV*)(struct CHolyStoneSystem*, CHolyStoneSystemDataMgrLoadIni2_ptr);
        using CHolyStoneSystemDataMgrLoadSceduleData4_ptr = bool (WINAPIV*)(struct CHolyScheduleData*);
        using CHolyStoneSystemDataMgrLoadSceduleData4_clbk = bool (WINAPIV*)(struct CHolyScheduleData*, CHolyStoneSystemDataMgrLoadSceduleData4_ptr);
        using CHolyStoneSystemDataMgrLoadStateData6_ptr = bool (WINAPIV*)(struct CHolyStoneSaveData*);
        using CHolyStoneSystemDataMgrLoadStateData6_clbk = bool (WINAPIV*)(struct CHolyStoneSaveData*, CHolyStoneSystemDataMgrLoadStateData6_ptr);
        using CHolyStoneSystemDataMgrSaveStateData8_ptr = bool (WINAPIV*)(struct CHolyStoneSaveData*);
        using CHolyStoneSystemDataMgrSaveStateData8_clbk = bool (WINAPIV*)(struct CHolyStoneSaveData*, CHolyStoneSystemDataMgrSaveStateData8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

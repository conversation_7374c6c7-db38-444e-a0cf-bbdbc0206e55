// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagCOMBOBOXINFO
    {
        unsigned int cbSize;
        tagRECT rcItem;
        tagRECT rcButton;
        unsigned int stateButton;
        HWND__ *hwndCombo;
        HWND__ *hwndItem;
        HWND__ *hwndList;
    };
END_ATF_NAMESPACE

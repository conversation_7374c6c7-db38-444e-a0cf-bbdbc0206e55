// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CUnmannedTraderGroupIDInfo.hpp>
#include <CUnmannedTraderGroupVersionInfo.hpp>
#include <CUnmannedTraderSortType.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderGroupItemInfoTable
    {
        CLogFile *m_pkLogger;
        CUnmannedTraderGroupVersionInfo m_kVerInfo;
        CUnmannedTraderGroupIDInfo m_kGroupIDInfo;
    public:
        CUnmannedTraderGroupItemInfoTable();
        void ctor_CUnmannedTraderGroupItemInfoTable();
        static void Destroy();
        bool GetGroupID(char byTableCode, uint16_t wItemTableIndex, char* byDivision, char* byClass, char* bySubClass, unsigned int* dwListIndex);
        struct CUnmannedTraderSortType* GetSortType(char byDivision, char bySortType);
        bool GetVersion(char byDivision, char byClass, unsigned int* dwVer);
        bool IncreaseVersion(char byDivision, char byClass);
        bool IncreaseVersion(char byTableCode, uint16_t wItemTableIndex);
        bool IncreaseVersion(char byTableCode, uint16_t wItemTableIndex, char byRegistDivision, char byRegistClass);
        bool Init();
        static struct CUnmannedTraderGroupItemInfoTable* Instance();
        bool IsExistGroupID(char byDivision, char byClass, char bySubClass, char bySortType, unsigned int* dwListIndex);
        void Log(char* fmt);
        void SetLogger(struct CLogFile* pkLogger);
        ~CUnmannedTraderGroupItemInfoTable();
        void dtor_CUnmannedTraderGroupItemInfoTable();
    };
END_ATF_NAMESPACE

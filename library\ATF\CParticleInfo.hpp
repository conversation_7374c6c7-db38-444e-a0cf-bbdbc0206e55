// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CParticle.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CParticleCheckCollision1_ptr = void (WINAPIV*)(struct CParticle*, int, float);
        using CParticleCheckCollision1_clbk = void (WINAPIV*)(struct CParticle*, int, float, CParticleCheckCollision1_ptr);
        using CParticleCopyParticleToSaveParticle2_ptr = void (WINAPIV*)(struct CParticle*, struct _SAVE_PARTICLE*);
        using CParticleCopyParticleToSaveParticle2_clbk = void (WINAPIV*)(struct CParticle*, struct _SAVE_PARTICLE*, CParticleCopyParticleToSaveParticle2_ptr);
        using CParticleCopySaveParticleToParticle3_ptr = void (WINAPIV*)(struct CParticle*, struct _SAVE_PARTICLE*);
        using CParticleCopySaveParticleToParticle3_clbk = void (WINAPIV*)(struct CParticle*, struct _SAVE_PARTICLE*, CParticleCopySaveParticleToParticle3_ptr);
        using CParticleGetBBox4_ptr = void (WINAPIV*)(struct CParticle*, float*, float*);
        using CParticleGetBBox4_clbk = void (WINAPIV*)(struct CParticle*, float*, float*, CParticleGetBBox4_ptr);
        using CParticleGetFlickerARGB5_ptr = void (WINAPIV*)(struct CParticle*, int, uint32_t*);
        using CParticleGetFlickerARGB5_clbk = void (WINAPIV*)(struct CParticle*, int, uint32_t*, CParticleGetFlickerARGB5_ptr);
        using CParticleGetPartcleStep6_ptr = void (WINAPIV*)(struct CParticle*, int, float);
        using CParticleGetPartcleStep6_clbk = void (WINAPIV*)(struct CParticle*, int, float, CParticleGetPartcleStep6_ptr);
        using CParticleGetParticleState7_ptr = uint32_t (WINAPIV*)(struct CParticle*);
        using CParticleGetParticleState7_clbk = uint32_t (WINAPIV*)(struct CParticle*, CParticleGetParticleState7_ptr);
        using CParticleInitElement8_ptr = void (WINAPIV*)(struct CParticle*, int, float);
        using CParticleInitElement8_clbk = void (WINAPIV*)(struct CParticle*, int, float, CParticleInitElement8_ptr);
        using CParticleInitParticle9_ptr = void (WINAPIV*)(struct CParticle*);
        using CParticleInitParticle9_clbk = void (WINAPIV*)(struct CParticle*, CParticleInitParticle9_ptr);
        using CParticleLoadParticleSPT10_ptr = int64_t (WINAPIV*)(struct CParticle*, char*, uint32_t);
        using CParticleLoadParticleSPT10_clbk = int64_t (WINAPIV*)(struct CParticle*, char*, uint32_t, CParticleLoadParticleSPT10_ptr);
        using CParticleLoop11_ptr = int32_t (WINAPIV*)(struct CParticle*);
        using CParticleLoop11_clbk = int32_t (WINAPIV*)(struct CParticle*, CParticleLoop11_ptr);
        using CParticleReInitParticle12_ptr = void (WINAPIV*)(struct CParticle*, int);
        using CParticleReInitParticle12_clbk = void (WINAPIV*)(struct CParticle*, int, CParticleReInitParticle12_ptr);
        using CParticleRealLoop13_ptr = int32_t (WINAPIV*)(struct CParticle*);
        using CParticleRealLoop13_clbk = int32_t (WINAPIV*)(struct CParticle*, CParticleRealLoop13_ptr);
        using CParticleReleaseEntity14_ptr = void (WINAPIV*)(struct CParticle*);
        using CParticleReleaseEntity14_clbk = void (WINAPIV*)(struct CParticle*, CParticleReleaseEntity14_ptr);
        using CParticleReleaseParticle15_ptr = void (WINAPIV*)(struct CParticle*);
        using CParticleReleaseParticle15_clbk = void (WINAPIV*)(struct CParticle*, CParticleReleaseParticle15_ptr);
        using CParticleResetOnePerTime16_ptr = void (WINAPIV*)(struct CParticle*);
        using CParticleResetOnePerTime16_clbk = void (WINAPIV*)(struct CParticle*, CParticleResetOnePerTime16_ptr);
        using CParticleSetCreatePos17_ptr = void (WINAPIV*)(struct CParticle*, float*);
        using CParticleSetCreatePos17_clbk = void (WINAPIV*)(struct CParticle*, float*, CParticleSetCreatePos17_ptr);
        using CParticleSetParticleState18_ptr = void (WINAPIV*)(struct CParticle*, uint32_t);
        using CParticleSetParticleState18_clbk = void (WINAPIV*)(struct CParticle*, uint32_t, CParticleSetParticleState18_ptr);
        using CParticleSetPreCalcParticle19_ptr = void (WINAPIV*)(struct CParticle*, uint32_t);
        using CParticleSetPreCalcParticle19_clbk = void (WINAPIV*)(struct CParticle*, uint32_t, CParticleSetPreCalcParticle19_ptr);
        using CParticleSetStartBoxArea20_ptr = void (WINAPIV*)(struct CParticle*);
        using CParticleSetStartBoxArea20_clbk = void (WINAPIV*)(struct CParticle*, CParticleSetStartBoxArea20_ptr);
        using CParticleSpecialLoop21_ptr = int32_t (WINAPIV*)(struct CParticle*);
        using CParticleSpecialLoop21_clbk = int32_t (WINAPIV*)(struct CParticle*, CParticleSpecialLoop21_ptr);
        using CParticleSpecialLoop222_ptr = int32_t (WINAPIV*)(struct CParticle*);
        using CParticleSpecialLoop222_clbk = int32_t (WINAPIV*)(struct CParticle*, CParticleSpecialLoop222_ptr);
        
        using CParticledtor_CParticle24_ptr = int64_t (WINAPIV*)(struct CParticle*);
        using CParticledtor_CParticle24_clbk = int64_t (WINAPIV*)(struct CParticle*, CParticledtor_CParticle24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

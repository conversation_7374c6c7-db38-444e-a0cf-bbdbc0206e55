// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__bad_alloc.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            
            using std__bad_allocctor_bad_alloc5_ptr = void (WINAPIV*)(struct std::bad_alloc*, char*);
            using std__bad_allocctor_bad_alloc5_clbk = void (WINAPIV*)(struct std::bad_alloc*, char*, std__bad_allocctor_bad_alloc5_ptr);
            
            using std__bad_allocctor_bad_alloc7_ptr = void (WINAPIV*)(struct std::bad_alloc*, struct std::bad_alloc*);
            using std__bad_allocctor_bad_alloc7_clbk = void (WINAPIV*)(struct std::bad_alloc*, struct std::bad_alloc*, std__bad_allocctor_bad_alloc7_ptr);
            
            using std__bad_allocctor_bad_alloc8_ptr = int64_t (WINAPIV*)(struct std::bad_alloc*);
            using std__bad_allocctor_bad_alloc8_clbk = int64_t (WINAPIV*)(struct std::bad_alloc*, std__bad_allocctor_bad_alloc8_ptr);
            
            using std__bad_allocdtor_bad_alloc10_ptr = void (WINAPIV*)(struct std::bad_alloc*);
            using std__bad_allocdtor_bad_alloc10_clbk = void (WINAPIV*)(struct std::bad_alloc*, std__bad_allocdtor_bad_alloc10_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

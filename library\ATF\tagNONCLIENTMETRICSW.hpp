// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagLOGFONTW.hpp>


START_ATF_NAMESPACE
    struct tagNONCLIENTMETRICSW
    {
        unsigned int cbSize;
        int iBorderWidth;
        int iScrollWidth;
        int iScrollHeight;
        int iCaptionWidth;
        int iCaptionHeight;
        tagLOGFONTW lfCaptionFont;
        int iSmCaptionWidth;
        int iSmCaptionHeight;
        tagLOGFONTW lfSmCaptionFont;
        int iMenuWidth;
        int iMenuHeight;
        tagLOGFONTW lfMenuFont;
        tagLOGFONTW lfStatusFont;
        tagLOGFONTW lfMessageFont;
    };
END_ATF_NAMESPACE

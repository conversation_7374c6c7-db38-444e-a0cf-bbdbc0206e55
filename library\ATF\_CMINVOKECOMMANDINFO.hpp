// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    struct _CMINVOKECOMMANDINFO
    {
        unsigned int cbSize;
        unsigned int fMask;
        HWND__ *hwnd;
        const char *lpVerb;
        const char *lpParameters;
        const char *lpDirectory;
        int nShow;
        unsigned int dwHotKey;
        void *hIcon;
    };
END_ATF_NAMESPACE

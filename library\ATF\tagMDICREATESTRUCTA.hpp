// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagMDICREATESTRUCTA
    {
        const char *szClass;
        const char *szTitle;
        void *hOwner;
        int x;
        int y;
        int cx;
        int cy;
        unsigned int style;
        __int64 lParam;
    };
END_ATF_NAMESPACE

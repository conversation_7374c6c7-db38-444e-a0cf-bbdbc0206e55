// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHackShieldExSystem.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CHackShieldExSystemctor_CHackShieldExSystem2_ptr = void (WINAPIV*)(struct CHackShieldExSystem*);
        using CHackShieldExSystemctor_CHackShieldExSystem2_clbk = void (WINAPIV*)(struct CHackShieldExSystem*, CHackShieldExSystemctor_CHackShieldExSystem2_ptr);
        using CHackShieldExSystemGetParam4_ptr = struct BASE_HACKSHEILD_PARAM* (WINAPIV*)(struct CHackShieldExSystem*, unsigned int);
        using CHackShieldExSystemGetParam4_clbk = struct BASE_HACKSHEILD_PARAM* (WINAPIV*)(struct CHackShieldExSystem*, unsigned int, CHackShieldExSystemGetParam4_ptr);
        using CHackShieldExSystemIsActive6_ptr = bool (WINAPIV*)(struct CHackShieldExSystem*);
        using CHackShieldExSystemIsActive6_clbk = bool (WINAPIV*)(struct CHackShieldExSystem*, CHackShieldExSystemIsActive6_ptr);
        using CHackShieldExSystemIsInit8_ptr = bool (WINAPIV*)(struct CHackShieldExSystem*);
        using CHackShieldExSystemIsInit8_clbk = bool (WINAPIV*)(struct CHackShieldExSystem*, CHackShieldExSystemIsInit8_ptr);
        using CHackShieldExSystemOnCheckSession_FirstVerify10_ptr = bool (WINAPIV*)(struct CHackShieldExSystem*, int);
        using CHackShieldExSystemOnCheckSession_FirstVerify10_clbk = bool (WINAPIV*)(struct CHackShieldExSystem*, int, CHackShieldExSystemOnCheckSession_FirstVerify10_ptr);
        using CHackShieldExSystemOnConnectSession12_ptr = void (WINAPIV*)(struct CHackShieldExSystem*, int);
        using CHackShieldExSystemOnConnectSession12_clbk = void (WINAPIV*)(struct CHackShieldExSystem*, int, CHackShieldExSystemOnConnectSession12_ptr);
        using CHackShieldExSystemOnDisConnectSession14_ptr = void (WINAPIV*)(struct CHackShieldExSystem*, int);
        using CHackShieldExSystemOnDisConnectSession14_clbk = void (WINAPIV*)(struct CHackShieldExSystem*, int, CHackShieldExSystemOnDisConnectSession14_ptr);
        using CHackShieldExSystemOnLoop16_ptr = void (WINAPIV*)(struct CHackShieldExSystem*);
        using CHackShieldExSystemOnLoop16_clbk = void (WINAPIV*)(struct CHackShieldExSystem*, CHackShieldExSystemOnLoop16_ptr);
        using CHackShieldExSystemOnLoopSession18_ptr = void (WINAPIV*)(struct CHackShieldExSystem*, int);
        using CHackShieldExSystemOnLoopSession18_clbk = void (WINAPIV*)(struct CHackShieldExSystem*, int, CHackShieldExSystemOnLoopSession18_ptr);
        using CHackShieldExSystemRecvClientLine20_ptr = bool (WINAPIV*)(struct CHackShieldExSystem*, int, struct _MSG_HEADER*, char*);
        using CHackShieldExSystemRecvClientLine20_clbk = bool (WINAPIV*)(struct CHackShieldExSystem*, int, struct _MSG_HEADER*, char*, CHackShieldExSystemRecvClientLine20_ptr);
        
        using CHackShieldExSystemdtor_CHackShieldExSystem25_ptr = void (WINAPIV*)(struct CHackShieldExSystem*);
        using CHackShieldExSystemdtor_CHackShieldExSystem25_clbk = void (WINAPIV*)(struct CHackShieldExSystem*, CHackShieldExSystemdtor_CHackShieldExSystem25_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGameObject.hpp>
#include <CPlayer.hpp>
#include <_STORAGE_LIST.hpp>
#include <_itembox_create_setdata.hpp>
#include <_object_id.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CItemBox : CGameObject
    {
        unsigned int m_dwOwnerSerial;
        unsigned __int16 m_wOwnerIndex;
        unsigned int m_dwThrowerSerial;
        char m_byThrowerID;
        unsigned __int16 m_wThrowerIndex;
        unsigned __int16 m_wMonRecIndex;
        bool m_bBossMob;
        char m_wszThrowerName[17];
        char m_aszThrowerName[17];
        unsigned int m_dwThrowerCharSerial;
        char m_szThrowerID[13];
        char m_byThrowerRaceCode;
        char m_byThrowerDegree;
        char *m_szThrowerItemHistoryFileName;
        unsigned int m_dwLootStartTime;
        int m_nStateCode;
        unsigned int m_dwLastDestroyTime;
        char m_byCreateCode;
        unsigned int m_dwPartyBossSerial;
        bool m_bPartyShare;
        bool m_bCompDgr;
        unsigned int m_dwEventPartyBoss;
        unsigned int m_dwEventGuildSerial;
        char m_byEventRaceCode;
        char m_byEventLootAuth;
        int m_bHolyScanner;
        _STORAGE_LIST::_db_con m_Item;
        bool m_bHide;
    public:
        CItemBox();
        void ctor_CItemBox();
        bool Create(struct _itembox_create_setdata* pParam, bool bHide);
        bool Destroy();
        void Init(struct _object_id* pID);
        bool IsTakeRight(struct CPlayer* pOne);
        void Loop();
        void SendMsg_Create();
        void SendMsg_Destroy();
        void SendMsg_FixPosition(int n);
        void SendMsg_StateChange();
        ~CItemBox();
        void dtor_CItemBox();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<CItemBox, 0x178>(), "CItemBox");
END_ATF_NAMESPACE

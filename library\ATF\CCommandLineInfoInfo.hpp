// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCommandLineInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCommandLineInfodtor_CCommandLineInfo1_ptr = int64_t (WINAPIV*)(struct CCommandLineInfo*);
        using CCommandLineInfodtor_CCommandLineInfo1_clbk = int64_t (WINAPIV*)(struct CCommandLineInfo*, CCommandLineInfodtor_CCommandLineInfo1_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

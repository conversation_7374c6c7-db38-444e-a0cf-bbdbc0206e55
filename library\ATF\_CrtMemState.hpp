// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _CrtMemState
    {
        _CrtMemBlockHeader *pBlockHeader;
        unsigned __int64 lCounts[5];
        unsigned __int64 lSizes[5];
        unsigned __int64 lHighWaterCount;
        unsigned __int64 lTotalCount;
    };
END_ATF_NAMESPACE

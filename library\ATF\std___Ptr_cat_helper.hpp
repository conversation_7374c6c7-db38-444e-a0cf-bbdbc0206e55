// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<double const *,double *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<__int64 *,__int64 *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned int const *,unsigned int *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<double *,double *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<int *,int *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<long double const *,long double *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<wchar_t *,wchar_t *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<signed char const *,signed char *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned char *,unsigned char *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<char const *,char *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<long double *,long double *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<float const *,float *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<_Undefined_inner_type_tag,_Undefined_inner_type_tag>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<short const *,short *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned int *,unsigned int *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<signed char *,signed char *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<__int64 const *,__int64 *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<bool *,bool *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<wchar_t const *,wchar_t *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<short *,short *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned short *,unsigned short *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned short const *,unsigned short *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<long const *,long *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned __int64 *,unsigned __int64 *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned __int64 const *,unsigned __int64 *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<long *,long *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned long *,unsigned long *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<char *,char *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<bool const *,bool *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<int const *,int *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned char const *,unsigned char *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<unsigned long const *,unsigned long *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Ptr_cat_helper<float *,float *>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE

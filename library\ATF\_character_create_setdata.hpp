// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_object_create_setdata.hpp>


START_ATF_NAMESPACE
    struct  _character_create_setdata : _object_create_setdata
    {
    public:
        _character_create_setdata();
        void ctor__character_create_setdata();
    };    
    static_assert(ATF::checkSize<_character_create_setdata, 32>(), "_character_create_setdata");
END_ATF_NAMESPACE

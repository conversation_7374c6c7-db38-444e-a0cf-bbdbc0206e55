// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $F28A51B4B40B0D33C72915D739D42B4B
    {
        BYTE gap0[8];
        char bVal;
    };    
    static_assert(ATF::checkSize<$F28A51B4B40B0D33C72915D739D42B4B, 9>(), "$F28A51B4B40B0D33C72915D739D42B4B");
END_ATF_NAMESPACE

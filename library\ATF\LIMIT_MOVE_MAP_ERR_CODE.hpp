// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum LIMIT_MOVE_MAP_ERR_CODE
    {
      ERROR_MOVEMAP_SUCCESS = 0x0,
      ERROR_MOVEMAP_INVALID_REQUEST = 0x1,
      ERROR_MOVEMAP_NOT_EXIST_RIGHT_INFO = 0x2,
      ERROR_MOVEMAP_NOT_EXIST_LIMIT_INFO = 0x3,
      ERROR_MOVEMAP_GOTO_INTERNAL_ERROR = 0x5,
      ERROR_MOVEMAP_USE_SCROLL_HAVE_NOT_RIGHT = 0xA,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_INVALID_USER = 0x14,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_INVALID_MAP = 0x15,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_INVALID_NPC = 0x16,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_MAP_LOADING = 0x17,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_CORPSE = 0x18,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_INVALID_MAP_TYPE = 0x19,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_SIT_STATE = 0x1A,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_SIEGE_MODE = 0x1B,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_RIDING_UNIT = 0x1C,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_STONE_STATE = 0x1D,
      ERROR_MOVEMAP_GOTO_LIMIT_ZONE_NPC_LONG_DISTANCE = 0x1E,
      ERROR_MOVEMAP_GOTO_LIMIT_HAVE_NOT_RIGHT = 0x1F,
      ERROR_MOVEMAP_GOTO_LIMIT_INVALID_DEST_DUMMY = 0x20,
    };
END_ATF_NAMESPACE

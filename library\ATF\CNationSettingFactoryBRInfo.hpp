// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryBR.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryBRctor_CNationSettingFactoryBR2_ptr = void (WINAPIV*)(struct CNationSettingFactoryBR*);
        using CNationSettingFactoryBRctor_CNationSettingFactoryBR2_clbk = void (WINAPIV*)(struct CNationSettingFactoryBR*, CNationSettingFactoryBRctor_CNationSettingFactoryBR2_ptr);
        using CNationSettingFactoryBRCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryBR*, int, char*, bool);
        using CNationSettingFactoryBRCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryBR*, int, char*, bool, CNationSettingFactoryBRCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

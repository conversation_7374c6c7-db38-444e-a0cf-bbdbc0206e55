// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_D3DCOLORVALUE.hpp>


START_ATF_NAMESPACE
    struct _D3DMATERIAL8
    {
        _D3DCOLORVALUE Diffuse;
        _D3DCOLORVALUE Ambient;
        _D3DCOLORVALUE Specular;
        _D3DCOLORVALUE Emissive;
        float Power;
    };    
    static_assert(ATF::checkSize<_D3DMATERIAL8, 68>(), "_D3DMATERIAL8");
END_ATF_NAMESPACE

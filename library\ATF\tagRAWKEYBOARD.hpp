// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagRAWKEYBOARD
    {
        unsigned __int16 MakeCode;
        unsigned __int16 Flags;
        unsigned __int16 Reserved;
        unsigned __int16 VKey;
        unsigned int Message;
        unsigned int ExtraInformation;
    };
END_ATF_NAMESPACE

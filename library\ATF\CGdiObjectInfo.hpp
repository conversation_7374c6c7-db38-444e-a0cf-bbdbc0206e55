// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGdiObject.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CGdiObjectDeleteObject1_ptr = int64_t (WINAPIV*)(struct CGdiObject*);
        using CGdiObjectDeleteObject1_clbk = int64_t (WINAPIV*)(struct CGdiObject*, CGdiObjectDeleteObject1_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _DISPLAY_DEVICEW
    {
        unsigned int cb;
        wchar_t DeviceName[32];
        wchar_t DeviceString[128];
        unsigned int StateFlags;
        wchar_t DeviceID[128];
        wchar_t Device<PERSON>ey[128];
    };
END_ATF_NAMESPACE

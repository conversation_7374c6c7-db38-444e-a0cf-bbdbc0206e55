// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <US__CNoneCopyAble.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        namespace Info
        {
            
            using US__CNoneCopyAblector_CNoneCopyAble2_ptr = void (WINAPIV*)(struct US::CNoneCopyAble*);
            using US__CNoneCopyAblector_CNoneCopyAble2_clbk = void (WINAPIV*)(struct US::CNoneCopyAble*, US__CNoneCopyAblector_CNoneCopyAble2_ptr);
            
            using US__CNoneCopyAbledtor_CNoneCopyAble4_ptr = void (WINAPIV*)(struct US::CNoneCopyAble*);
            using US__CNoneCopyAbledtor_CNoneCopyAble4_clbk = void (WINAPIV*)(struct US::CNoneCopyAble*, US__CNoneCopyAbledtor_CNoneCopyAble4_ptr);
        }; // end namespace Info
    }; // end namespace US
END_ATF_NAMESPACE

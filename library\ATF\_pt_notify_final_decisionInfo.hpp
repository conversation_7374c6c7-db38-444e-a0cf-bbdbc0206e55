// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_pt_notify_final_decision.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _pt_notify_final_decisionctor__pt_notify_final_decision2_ptr = void (WINAPIV*)(struct _pt_notify_final_decision*);
        using _pt_notify_final_decisionctor__pt_notify_final_decision2_clbk = void (WINAPIV*)(struct _pt_notify_final_decision*, _pt_notify_final_decisionctor__pt_notify_final_decision2_ptr);
        using _pt_notify_final_decisionsize4_ptr = int (WINAPIV*)(struct _pt_notify_final_decision*);
        using _pt_notify_final_decisionsize4_clbk = int (WINAPIV*)(struct _pt_notify_final_decision*, _pt_notify_final_decisionsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>
#include <_fix_supply.hpp>
#include <_select_supply.hpp>


START_ATF_NAMESPACE
    struct  _PcRoom_fld : _base_fld
    {
        int m_bUse;
        char m_strEventName[64];
        _select_supply m_SelectSupply[5];
        _fix_supply m_FixSupply[10];
    };
END_ATF_NAMESPACE

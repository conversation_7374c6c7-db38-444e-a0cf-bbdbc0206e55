// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateReady.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateReadyctor_CNormalGuildBattleStateReady2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReady*);
            using GUILD_BATTLE__CNormalGuildBattleStateReadyctor_CNormalGuildBattleStateReady2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReady*, GUILD_BATTLE__CNormalGuildBattleStateReadyctor_CNormalGuildBattleStateReady2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateReadyEnter4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReady*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateReadyEnter4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReady*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateReadyEnter4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateReadyGetTerm6_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReady*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CNormalGuildBattleStateReadyGetTerm6_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReady*, struct ATL::CTimeSpan*, GUILD_BATTLE__CNormalGuildBattleStateReadyGetTerm6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateReadydtor_CNormalGuildBattleStateReady8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReady*);
            using GUILD_BATTLE__CNormalGuildBattleStateReadydtor_CNormalGuildBattleStateReady8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReady*, GUILD_BATTLE__CNormalGuildBattleStateReadydtor_CNormalGuildBattleStateReady8_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

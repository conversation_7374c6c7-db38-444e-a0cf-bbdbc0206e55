// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuildRanking.hpp>
#include <CLogFile.hpp>
#include <CPvpUserRankingInfo.hpp>
#include <CPvpUserRankingTargetUserList.hpp>
#include <_PVP_RANK_DATA.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CUserRankingProcess
    {
        enum PROC_STATE
        {
            PS_NONE = 0xFFFFFFFF,
            PS_INVALID = 0xFFFFFFFE,
            PS_WAIT = 0x0,
            PS_SAVE_TARGET_LIST = 0x1,
            PS_RANK_START = 0x2,
            PS_RANK_COMPLETE = 0x3,
            PS_WAIT_DAY_CHANGED = 0x4,
            PS_NOTIFY_VERSIONUP = 0x5,
            PS_APPLY_GUILD_GRADE = 0x6,
            PS_APPLY_RANK_IN_GUILD = 0x7,
            PS_FAILED_WAIT = 0x8,
            PS_RANK_SUCCESS = 0x9,
            PS_VALID_MAX_STATE_CNT = 0xA,
            PS_USER_RANK_PROCESS = 0x14,
            PS_GUILD_RANK_PROCESS = 0x15,
            PS_RANK_IN_GUILD_PROCESS = 0x16,
            PS_UPDATE_AND_LOAD_GRADE = 0x17,
        };
        PROC_STATE m_eState;
        CLogFile *m_pkLogger;
        int m_iOldDay;
        __int64 m_tStartTime;
        __int64 m_tFailStartTime;
        int m_iPvpSaveJobIndexOffset;
        int m_iPvpRankDataVersionUpSendOffset;
        char m_szPvpRankDate[32];
        std::vector<void (WINAPIV *)(void),std::allocator<void (WINAPIV *)(void)> > m_vecProc;
        CPvpUserRankingInfo m_kPvpRankingInfo;
        CPvpUserRankingTargetUserList m_kTargetUserList;
        CGuildRanking m_kGuildRanking;
    public:
        bool AllocObject();
        CUserRankingProcess();
        void ctor_CUserRankingProcess();
        bool CheckAndCreateTodayPvpRankTable(char* szDate);
        void CheckTomorrowPvpRankDate();
        void CompleteGuildRankStep1(char byRet, char* szData);
        void CompleteGuildRankStep2(char byRet, char* szData);
        void CompleteGuildRankStep3(char byRet, char* szData);
        void CompleteGuildRankStep4(char byRet, char* szData);
        void CompleteRaceRankStep1(char byRet, char* szData);
        void CompleteRaceRankStep10(char byRet, char* szData);
        void CompleteRaceRankStep11(char byRet, char* szData);
        void CompleteRaceRankStep2(char byRet, char* szData);
        void CompleteRaceRankStep3(char byRet, char* szData);
        void CompleteRaceRankStep4(char byRet, char* szData);
        void CompleteRaceRankStep5(char byRet, char* szData);
        void CompleteRaceRankStep6(char byRet, char* szData);
        void CompleteRaceRankStep7(char byRet, char* szData);
        void CompleteRaceRankStep8(char byRet, char* szData);
        void CompleteRaceRankStep9(char byRet, char* szData);
        void CompleteRankInGuildStep1(char byRet, char* szData);
        void CompleteRankInGuildStep2(char byRet, char* szData);
        void CompleteRankInGuildStep3(char byRet, char* szData);
        void CompleteRankInGuildStep4(char byRet, char* szData);
        void CompleteRankInGuildStep5(char byRet, char* szData);
        void CompleteRankInGuildStep6(char byRet, char* szData);
        void CompleteRankUpdateAndSelectGarde(char byRet, char* szData);
        unsigned int FindRank(char byRaceCode, unsigned int dwAvatorSerial);
        void FlipPvPRankTop();
        char GetBossType(char byRace, unsigned int dwSerial);
        struct _PVP_RANK_DATA* GetCurrentPvpRankData(char byRace, char byNth);
        unsigned int GetCurrentRaceBossSerial(char byRace, char byNth);
        void GetRankDateStr(char* szDate, uint64_t tDateStrSize);
        void GetTommorrowStr(char* szTommorrow);
        void IncreaseVesion();
        bool Init();
        bool InitProcFunc();
        bool IsCurrentRaceBossGroup(char byRace, unsigned int dwSerial);
        bool IsRaceViceBoss(char byRace, unsigned int dwSerial);
        bool Load();
        void LoadINI(int* piHour, int* piMin);
        void Loop();
        void ProcApplyGuildGrade();
        void ProcApplyRankInGuild();
        void ProcFailedWait();
        void ProcNotifyVersionUp();
        void ProcRankComplete();
        void ProcRankStart();
        void ProcRankSuccess();
        void ProcSaveTargetList();
        void ProcWait();
        void ProcWaitDayChanged();
        void PvpRankDataPacking();
        void PvpRankListRequest(uint16_t wIndex, char byRace, char byVersion, char byPage);
        void SetCurrentRaceBossSerial(char byRace, char byNth, unsigned int dwSerial);
        void SetLogger(struct CLogFile* pkLogger);
        bool SetRankingStartTime(int iHour, int iMin);
        void SetUpdateRaceBossSerial(char byRace, char byNth, unsigned int dwSerial);
        char UpdateAndSelectGuildGrade(char* szData);
        char UpdateGuildRankStep1(char* szData);
        char UpdateGuildRankStep2(char* szData);
        char UpdateGuildRankStep3(char* szData);
        char UpdateGuildRankStep4(char* szData);
        void UpdateNextRankingStartTime();
        char UpdateRaceRankStep1(char* szData);
        char UpdateRaceRankStep10(char* szData);
        char UpdateRaceRankStep11(char* szData);
        char UpdateRaceRankStep2(char* szData);
        char UpdateRaceRankStep3(char* szData);
        char UpdateRaceRankStep4(char* szData);
        char UpdateRaceRankStep5(char* szData);
        char UpdateRaceRankStep6(char* szData);
        char UpdateRaceRankStep7(char* szData);
        char UpdateRaceRankStep8(char* szData);
        char UpdateRaceRankStep9(char* szData);
        char UpdateRankinGuildStep1(char* szData);
        char UpdateRankinGuildStep2(char* szData);
        char UpdateRankinGuildStep3(char* szData);
        char UpdateRankinGuildStep4(char* szData);
        char UpdateRankinGuildStep5(char* szData);
        char UpdateRankinGuildStep6(char* szData);
        ~CUserRankingProcess();
        void dtor_CUserRankingProcess();
    };
END_ATF_NAMESPACE

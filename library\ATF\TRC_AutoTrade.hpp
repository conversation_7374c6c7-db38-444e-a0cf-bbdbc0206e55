// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <ControllerTaxRate.hpp>
#include <_suggested_matter_change_taxrate.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct TRC_AutoTrade
    {
        bool m_bInit;
        char m_byCurDay;
        unsigned __int16 m_wCurMonth;
        unsigned __int16 m_wCurYear;
        bool m_bChangeTaxRate;
        float m_fCommonTaxRate;
        struct CGuild *m_pOwnerGuild;
        ControllerTaxRate m_Controller;
        CLogFile m_sysLog;
        CLogFile m_serviceLog;
        unsigned int m_dwTrade;
        long double m_dIncomeMoney;
        char m_byRace;
        _suggested_matter_change_taxrate m_suggested;
    public:
        void AddGDalant(char* pdata);
        unsigned int CalcPrice(unsigned int nGuildSerial, unsigned int nPrice);
        int ChangeOwner(struct CGuild* pGuild);
        int ChangeTaxRate(float fNewTaxRate);
        void ChangeTaxRate();
        bool Initialzie();
        bool IsMaster(unsigned int dwSerial);
        bool IsOwnerGuild(unsigned int nGuildSerial);
        void PushDQSData();
        void PushDQSData_GuildInMoney(unsigned int dwRetPrice, unsigned int dwSeller);
        void SendMsg_PatriarchTaxRate(int n);
        void SendMsg_UserLogInNotifyTaxRate(int n);
        void SetGuildMaintainMoney(unsigned int dwTax, unsigned int dwSeller);
        void SetPatriarchTaxMoney(unsigned int dwTax);
        TRC_AutoTrade(char byRace);
        void ctor_TRC_AutoTrade(char byRace);
        TRC_AutoTrade();
        void ctor_TRC_AutoTrade();
        bool _db_load(char byRace);
        static char _insert_info(char* pdata);
        int check(unsigned int dwAvatorSerial, unsigned int dwGuildSerial);
        struct CGuild* getOwnerGuild();
        unsigned int getSuggestedTime();
        char* get_guidlname();
        float get_next_tax();
        char get_race();
        float get_taxrate();
        void his_income_money();
        void history_used_cheet_changetaxrate(unsigned int dwProb, char* pName);
        void sendmsg_taxrate(int n, char byRet);
        void set_owner(struct CGuild* pGuild);
        void set_suggested(char byMatterType, unsigned int dwMatterDst, char* wszMatterDst, unsigned int dwNext);
        ~TRC_AutoTrade();
        void dtor_TRC_AutoTrade();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

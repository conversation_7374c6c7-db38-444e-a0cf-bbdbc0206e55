// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$381CBF3893FFC823238DCA24986D6E61.hpp>
#include <$EEC2CF0CE9A8D2E77412C49A5289D564.hpp>


START_ATF_NAMESPACE
    struct tagDEC
    {
        unsigned __int16 wReserved;
        $EEC2CF0CE9A8D2E77412C49A5289D564 ___u1;
        unsigned int Hi32;
        $381CBF3893FFC823238DCA24986D6E61 ___u3;
    };    
    static_assert(ATF::checkSize<tagDEC, 16>(), "tagDEC");
END_ATF_NAMESPACE

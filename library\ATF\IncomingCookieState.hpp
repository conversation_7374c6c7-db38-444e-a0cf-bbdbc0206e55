// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct IncomingCookieState
    {
        int cSession;
        int cPersistent;
        int cAccepted;
        int cLeashed;
        int cDowngraded;
        int cBlocked;
        const char *pszLocation;
    };
END_ATF_NAMESPACE

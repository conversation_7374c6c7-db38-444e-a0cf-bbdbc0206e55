// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaEventNode.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CLuaEventNodector_CLuaEventNode2_ptr = void (WINAPIV*)(struct CLuaEventNode*);
        using CLuaEventNodector_CLuaEventNode2_clbk = void (WINAPIV*)(struct CLuaEventNode*, CLuaEventNodector_CLuaEventNode2_ptr);
        using CLuaEventNodeGetCallFunName4_ptr = char* (WINAPIV*)(struct CLuaEventNode*);
        using CLuaEventNodeGetCallFunName4_clbk = char* (WINAPIV*)(struct CLuaEventNode*, CLuaEventNodeGetCallFunName4_ptr);
        using CLuaEventNodeGetName6_ptr = char* (WINAPIV*)(struct CLuaEventNode*);
        using CLuaEventNodeGetName6_clbk = char* (WINAPIV*)(struct CLuaEventNode*, CLuaEventNodeGetName6_ptr);
        using CLuaEventNodeGetNextLoopTime8_ptr = unsigned int (WINAPIV*)(struct CLuaEventNode*);
        using CLuaEventNodeGetNextLoopTime8_clbk = unsigned int (WINAPIV*)(struct CLuaEventNode*, CLuaEventNodeGetNextLoopTime8_ptr);
        using CLuaEventNodeGetScript10_ptr = struct CLuaScript* (WINAPIV*)(struct CLuaEventNode*);
        using CLuaEventNodeGetScript10_clbk = struct CLuaScript* (WINAPIV*)(struct CLuaEventNode*, CLuaEventNodeGetScript10_ptr);
        using CLuaEventNodeGetTimeTerm12_ptr = unsigned int (WINAPIV*)(struct CLuaEventNode*);
        using CLuaEventNodeGetTimeTerm12_clbk = unsigned int (WINAPIV*)(struct CLuaEventNode*, CLuaEventNodeGetTimeTerm12_ptr);
        using CLuaEventNodeInit14_ptr = void (WINAPIV*)(struct CLuaEventNode*);
        using CLuaEventNodeInit14_clbk = void (WINAPIV*)(struct CLuaEventNode*, CLuaEventNodeInit14_ptr);
        using CLuaEventNodeIsCallFun16_ptr = bool (WINAPIV*)(struct CLuaEventNode*);
        using CLuaEventNodeIsCallFun16_clbk = bool (WINAPIV*)(struct CLuaEventNode*, CLuaEventNodeIsCallFun16_ptr);
        using CLuaEventNodeSetCallFun18_ptr = void (WINAPIV*)(struct CLuaEventNode*, char*, unsigned int);
        using CLuaEventNodeSetCallFun18_clbk = void (WINAPIV*)(struct CLuaEventNode*, char*, unsigned int, CLuaEventNodeSetCallFun18_ptr);
        using CLuaEventNodeSetName20_ptr = void (WINAPIV*)(struct CLuaEventNode*, char*);
        using CLuaEventNodeSetName20_clbk = void (WINAPIV*)(struct CLuaEventNode*, char*, CLuaEventNodeSetName20_ptr);
        using CLuaEventNodeSetNextLoopTime22_ptr = void (WINAPIV*)(struct CLuaEventNode*, unsigned int);
        using CLuaEventNodeSetNextLoopTime22_clbk = void (WINAPIV*)(struct CLuaEventNode*, unsigned int, CLuaEventNodeSetNextLoopTime22_ptr);
        using CLuaEventNodeSetScript24_ptr = void (WINAPIV*)(struct CLuaEventNode*, struct CLuaScript*);
        using CLuaEventNodeSetScript24_clbk = void (WINAPIV*)(struct CLuaEventNode*, struct CLuaScript*, CLuaEventNodeSetScript24_ptr);
        
        using CLuaEventNodedtor_CLuaEventNode28_ptr = void (WINAPIV*)(struct CLuaEventNode*);
        using CLuaEventNodedtor_CLuaEventNode28_clbk = void (WINAPIV*)(struct CLuaEventNode*, CLuaEventNodedtor_CLuaEventNode28_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

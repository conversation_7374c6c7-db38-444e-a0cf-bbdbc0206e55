// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBuffInfoByHolyQuest.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CRaceBuffInfoByHolyQuestApply2_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestApply2_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*, CRaceBuffInfoByHolyQuestApply2_ptr);
        using CRaceBuffInfoByHolyQuestApplyEffect4_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*, bool);
        using CRaceBuffInfoByHolyQuestApplyEffect4_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*, bool, CRaceBuffInfoByHolyQuestApplyEffect4_ptr);
        
        using CRaceBuffInfoByHolyQuestctor_CRaceBuffInfoByHolyQuest6_ptr = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct _skill_fld*, char);
        using CRaceBuffInfoByHolyQuestctor_CRaceBuffInfoByHolyQuest6_clbk = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct _skill_fld*, char, CRaceBuffInfoByHolyQuestctor_CRaceBuffInfoByHolyQuest6_ptr);
        using CRaceBuffInfoByHolyQuestCreate8_ptr = struct CRaceBuffInfoByHolyQuest* (WINAPIV*)(unsigned int, char*);
        using CRaceBuffInfoByHolyQuestCreate8_clbk = struct CRaceBuffInfoByHolyQuest* (WINAPIV*)(unsigned int, char*, CRaceBuffInfoByHolyQuestCreate8_ptr);
        using CRaceBuffInfoByHolyQuestCreateComplete10_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestCreateComplete10_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*, CRaceBuffInfoByHolyQuestCreateComplete10_ptr);
        using CRaceBuffInfoByHolyQuestLoadINISubProcLoadCode12_ptr = bool (WINAPIV*)(unsigned int, char*, struct _skill_fld**, char*);
        using CRaceBuffInfoByHolyQuestLoadINISubProcLoadCode12_clbk = bool (WINAPIV*)(unsigned int, char*, struct _skill_fld**, char*, CRaceBuffInfoByHolyQuestLoadINISubProcLoadCode12_ptr);
        using CRaceBuffInfoByHolyQuestNotifyLogInSetBuff14_ptr = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, uint16_t);
        using CRaceBuffInfoByHolyQuestNotifyLogInSetBuff14_clbk = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, uint16_t, CRaceBuffInfoByHolyQuestNotifyLogInSetBuff14_ptr);
        using CRaceBuffInfoByHolyQuestNotifyReleaseBuff16_ptr = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, uint16_t);
        using CRaceBuffInfoByHolyQuestNotifyReleaseBuff16_clbk = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, uint16_t, CRaceBuffInfoByHolyQuestNotifyReleaseBuff16_ptr);
        using CRaceBuffInfoByHolyQuestNotifySetBuff18_ptr = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestNotifySetBuff18_clbk = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*, CRaceBuffInfoByHolyQuestNotifySetBuff18_ptr);
        using CRaceBuffInfoByHolyQuestRelease20_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestRelease20_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, struct CPlayer*, CRaceBuffInfoByHolyQuestRelease20_ptr);
        
        using CRaceBuffInfoByHolyQuestdtor_CRaceBuffInfoByHolyQuest24_ptr = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*);
        using CRaceBuffInfoByHolyQuestdtor_CRaceBuffInfoByHolyQuest24_clbk = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuest*, CRaceBuffInfoByHolyQuestdtor_CRaceBuffInfoByHolyQuest24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

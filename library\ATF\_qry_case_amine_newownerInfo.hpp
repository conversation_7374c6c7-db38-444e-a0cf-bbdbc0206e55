// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_amine_newowner.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_amine_newownerctor__qry_case_amine_newowner2_ptr = void (WINAPIV*)(struct _qry_case_amine_newowner*);
        using _qry_case_amine_newownerctor__qry_case_amine_newowner2_clbk = void (WINAPIV*)(struct _qry_case_amine_newowner*, _qry_case_amine_newownerctor__qry_case_amine_newowner2_ptr);
        using _qry_case_amine_newownersize4_ptr = int (WINAPIV*)(struct _qry_case_amine_newowner*);
        using _qry_case_amine_newownersize4_clbk = int (WINAPIV*)(struct _qry_case_amine_newowner*, _qry_case_amine_newownersize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

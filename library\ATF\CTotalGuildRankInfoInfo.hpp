// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTotalGuildRankInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CTotalGuildRankInfoctor_CTotalGuildRankInfo2_ptr = void (WINAPIV*)(struct CTotalGuildRankInfo*);
        using CTotalGuildRankInfoctor_CTotalGuildRankInfo2_clbk = void (WINAPIV*)(struct CTotalGuildRankInfo*, CTotalGuildRankInfoctor_CTotalGuildRankInfo2_ptr);
        using CTotalGuildRankInfoClear4_ptr = void (WINAPIV*)(struct CTotalGuildRankInfo*);
        using CTotalGuildRankInfoClear4_clbk = void (WINAPIV*)(struct CTotalGuildRankInfo*, CTotalGuildRankInfoClear4_ptr);
        using CTotalGuildRankInfoFind6_ptr = int (WINAPIV*)(struct CTotalGuildRankInfo*, char, unsigned int);
        using CTotalGuildRankInfoFind6_clbk = int (WINAPIV*)(struct CTotalGuildRankInfo*, char, unsigned int, CTotalGuildRankInfoFind6_ptr);
        using CTotalGuildRankInfoInit8_ptr = bool (WINAPIV*)(struct CTotalGuildRankInfo*);
        using CTotalGuildRankInfoInit8_clbk = bool (WINAPIV*)(struct CTotalGuildRankInfo*, CTotalGuildRankInfoInit8_ptr);
        using CTotalGuildRankInfoLoad10_ptr = bool (WINAPIV*)(struct CTotalGuildRankInfo*, struct _total_guild_rank_info*);
        using CTotalGuildRankInfoLoad10_clbk = bool (WINAPIV*)(struct CTotalGuildRankInfo*, struct _total_guild_rank_info*, CTotalGuildRankInfoLoad10_ptr);
        using CTotalGuildRankInfoSend12_ptr = void (WINAPIV*)(struct CTotalGuildRankInfo*, unsigned int, int, char, char, unsigned int);
        using CTotalGuildRankInfoSend12_clbk = void (WINAPIV*)(struct CTotalGuildRankInfo*, unsigned int, int, char, char, unsigned int, CTotalGuildRankInfoSend12_ptr);
        using CTotalGuildRankInfoSetNoDataFlag14_ptr = void (WINAPIV*)(struct CTotalGuildRankInfo*);
        using CTotalGuildRankInfoSetNoDataFlag14_clbk = void (WINAPIV*)(struct CTotalGuildRankInfo*, CTotalGuildRankInfoSetNoDataFlag14_ptr);
        using CTotalGuildRankInfoUpdate16_ptr = bool (WINAPIV*)(struct CTotalGuildRankInfo*, struct _total_guild_rank_info*);
        using CTotalGuildRankInfoUpdate16_clbk = bool (WINAPIV*)(struct CTotalGuildRankInfo*, struct _total_guild_rank_info*, CTotalGuildRankInfoUpdate16_ptr);
        
        using CTotalGuildRankInfodtor_CTotalGuildRankInfo18_ptr = void (WINAPIV*)(struct CTotalGuildRankInfo*);
        using CTotalGuildRankInfodtor_CTotalGuildRankInfo18_clbk = void (WINAPIV*)(struct CTotalGuildRankInfo*, CTotalGuildRankInfodtor_CTotalGuildRankInfo18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

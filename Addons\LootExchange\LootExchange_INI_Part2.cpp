        // Template function for getting values from item records
        template<typename _Ty>
        int GetValue(_Ty* pRec, uint32_t nMoneyType)
        {
            switch ((e_money_type)nMoneyType)
            {
            case e_money_type::cp:
            case e_money_type::gold:
                return pRec->m_nStdPrice;
            case e_money_type::pvp_point:
            case e_money_type::pvp_point_2:
                return pRec->m_nStdPoint;
            case e_money_type::processing_point:
                return pRec->m_nProcPoint;
            case e_money_type::hunter_point:
                return pRec->m_nKillPoint;
            case e_money_type::gold_point:
                return pRec->m_nGoldPoint;
            }
            return 0;
        }

        // Special function for resource items (race-specific pricing)
        int GetValueResource(ATF::_ResourceItem_fld* pRec, uint32_t nMoneyType, int nRace)
        {
            switch ((e_money_type)nMoneyType)
            {
            case e_money_type::cp:
            case e_money_type::gold:
                if (CLootExchange::m_bUseRaceSpecificPricing)
                {
                    if (nRace == 0) return pRec->m_nAncStdPrice;
                    if (nRace == 1) return pRec->m_nExStdPrice;
                    if (nRace == 2) return pRec->m_nMecaStdPrice;
                }
                else
                {
                    // Use default race pricing
                    if (CLootExchange::m_nDefaultRace == 0) return pRec->m_nAncStdPrice;
                    if (CLootExchange::m_nDefaultRace == 1) return pRec->m_nExStdPrice;
                    if (CLootExchange::m_nDefaultRace == 2) return pRec->m_nMecaStdPrice;
                }
                break;
            case e_money_type::pvp_point:
            case e_money_type::pvp_point_2:
                return pRec->m_nStdPoint;
            case e_money_type::processing_point:
                return pRec->m_nProcPoint;
            case e_money_type::hunter_point:
                return pRec->m_nKillPoint;
            case e_money_type::gold_point:
                return pRec->m_nGoldPoint;
            }
            return 0;
        }

        int CLootExchange::GetMoneyValue(
            ATF::_base_fld* pRec,
            char byTableCode,
            uint32_t nMoneyType,
            int nRace)
        {
            int nMoneyValue = 0;
            switch ((e_code_item_table)byTableCode)
            {
            case e_code_item_table::tbl_code_upper:
            case e_code_item_table::tbl_code_lower:
            case e_code_item_table::tbl_code_gauntlet:
            case e_code_item_table::tbl_code_shoe:
            case e_code_item_table::tbl_code_shield:
            case e_code_item_table::tbl_code_helmet:
            case e_code_item_table::tbl_code_cloak:
            {
                ATF::_DfnEquipItem_fld* pItemRec = (ATF::_DfnEquipItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_weapon: {
                ATF::_WeaponItem_fld* pItemRec = (ATF::_WeaponItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_maketool: {
                ATF::_MakeToolItem_fld* pItemRec = (ATF::_MakeToolItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_bag: {
                ATF::_BagItem_fld* pItemRec = (ATF::_BagItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_potion: {
                ATF::_PotionItem_fld* pItemRec = (ATF::_PotionItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_bullet: {
                ATF::_BulletItem_fld* pItemRec = (ATF::_BulletItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_res: {
                ATF::_ResourceItem_fld* pItemRec = (ATF::_ResourceItem_fld*)pRec;
                nMoneyValue = GetValueResource(pItemRec, nMoneyType, nRace);
            } break;
            case e_code_item_table::tbl_code_booty: {
                ATF::_BootyItem_fld* pItemRec = (ATF::_BootyItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_battery: {
                ATF::_BatteryItem_fld* pItemRec = (ATF::_BatteryItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_fcitem: {
                ATF::_ForceItem_fld* pItemRec = (ATF::_ForceItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_ring: {
                ATF::_RingItem_fld* pItemRec = (ATF::_RingItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_amulet: {
                ATF::_AmuletItem_fld* pItemRec = (ATF::_AmuletItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_map: {
                ATF::_MapItem_fld* pItemRec = (ATF::_MapItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_town: {
                ATF::_TOWNItem_fld* pItemRec = (ATF::_TOWNItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_bdungeon: {
                ATF::_BattleDungeonItem_fld* pItemRec = (ATF::_BattleDungeonItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_animus: {
                ATF::_AnimusItem_fld* pItemRec = (ATF::_AnimusItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            case e_code_item_table::tbl_code_tower: {
                ATF::_GuardTowerItem_fld* pItemRec = (ATF::_GuardTowerItem_fld*)pRec;
                nMoneyValue = GetValue(pItemRec, nMoneyType);
            } break;
            default:
                break;
            }
            return nMoneyValue;
        }

        bool CLootExchange::AddMoney(
            ATF::CPlayer* pObj,
            uint32_t nMoneyType,
            int nMoneyValue)
        {
            auto fnAddActPoint = [&](char byActionCode) -> bool
            {
                bool result = ATF::CActionPointSystemMgr::Instance()->GetEventStatus(byActionCode) == 2;
                if (!result)
                    return result;

                auto dwPoint = pObj->m_pUserDB->GetActPoint(byActionCode);
                dwPoint += nMoneyValue;
                pObj->m_pUserDB->Update_User_Action_Point(byActionCode, dwPoint);
                pObj->SendMsg_Alter_Action_Point(byActionCode, dwPoint);

                return result;
            };

            bool result = false;
            switch ((e_money_type)nMoneyType)
            {
            case e_money_type::cp:
                pObj->AlterDalant(nMoneyValue);
                pObj->SendMsg_AlterMoneyInform(0);
                result = true;
                break;
            case e_money_type::gold:
                pObj->AlterGold(nMoneyValue);
                pObj->SendMsg_AlterMoneyInform(0);
                result = true;
                break;
            case e_money_type::pvp_point:
                pObj->AlterPvPPoint(nMoneyValue, ATF::PVP_ALTER_TYPE::cheat, -1);
                result = true;
                break;
            case e_money_type::pvp_point_2:
                pObj->AlterPvPCashBag(nMoneyValue, ATF::PVP_MONEY_ALTER_TYPE::pm_kill);
                result = true;
                break;
            case e_money_type::processing_point:
                result = fnAddActPoint(0);
                break;
            case e_money_type::hunter_point:
                result = fnAddActPoint(1);
                break;
            case e_money_type::gold_point:
                result = fnAddActPoint(2);
                break;
            }

            return result;
        }

    }
}

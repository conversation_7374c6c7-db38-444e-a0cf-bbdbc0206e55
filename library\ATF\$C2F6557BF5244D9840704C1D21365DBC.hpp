// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$42A3CB4E031CC824EFC3D0F4D1894CCF.hpp>


START_ATF_NAMESPACE
    union $C2F6557BF5244D9840704C1D21365DBC
    {
        $42A3CB4E031CC824EFC3D0F4D1894CCF __s0;
        unsigned int uiData;
    };    
    static_assert(ATF::checkSize<$C2F6557BF5244D9840704C1D21365DBC, 4>(), "$C2F6557BF5244D9840704C1D21365DBC");
END_ATF_NAMESPACE

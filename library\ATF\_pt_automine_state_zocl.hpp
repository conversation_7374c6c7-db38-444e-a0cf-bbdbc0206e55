// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _pt_automine_state_zocl
    {
        char bySelectOre;
        unsigned int dwGage;
        char byPage;
        _INVENKEY item;
    public:
        _pt_automine_state_zocl();
        void ctor__pt_automine_state_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_pt_automine_state_zocl, 10>(), "_pt_automine_state_zocl");
END_ATF_NAMESPACE

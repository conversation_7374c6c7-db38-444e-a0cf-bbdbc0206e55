// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_rank_racerank_guildrank.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _qry_case_rank_racerank_guildrankClearRetParam2_ptr = void (WINAPIV*)(struct _qry_case_rank_racerank_guildrank*);
        using _qry_case_rank_racerank_guildrankClearRetParam2_clbk = void (WINAPIV*)(struct _qry_case_rank_racerank_guildrank*, _qry_case_rank_racerank_guildrankClearRetParam2_ptr);
        
        using _qry_case_rank_racerank_guildrankctor__qry_case_rank_racerank_guildrank4_ptr = void (WINAPIV*)(struct _qry_case_rank_racerank_guildrank*);
        using _qry_case_rank_racerank_guildrankctor__qry_case_rank_racerank_guildrank4_clbk = void (WINAPIV*)(struct _qry_case_rank_racerank_guildrank*, _qry_case_rank_racerank_guildrankctor__qry_case_rank_racerank_guildrank4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

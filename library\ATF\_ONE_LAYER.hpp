// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _ONE_LAYER
    {
        __int16 m_iTileAniTexNum;
        int m_iSurface;
        unsigned int m_dwAlphaType;
        unsigned int m_ARGB;
        unsigned int m_dwFlag;
        __int16 m_sUVLavaWave;
        __int16 m_sUVLavaSpeed;
        __int16 m_sUVScrollU;
        __int16 m_sUVScrollV;
        __int16 m_sUVRotate;
        __int16 m_sUVScaleStart;
        __int16 m_sUVScaleEnd;
        __int16 m_sUVScaleSpeed;
        __int16 m_sUVMetal;
        __int16 m_sANIAlphaFlicker;
        unsigned __int16 m_sANIAlphaFlickerAni;
        __int16 m_sANITexFrame;
        __int16 m_sANITexSpeed;
        __int16 m_sGradientAlpha;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_EMBELLKEY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _EMBELLKEYCovDBKey2_ptr = int (WINAPIV*)(struct _EMBELLKEY*);
        using _EMBELLKEYCovDBKey2_clbk = int (WINAPIV*)(struct _EMBELLKEY*, _EMBELLKEYCovDBKey2_ptr);
        using _EMBELLKEYIsFilled4_ptr = bool (WINAPIV*)(struct _EMBELLKEY*);
        using _EMBELLKEYIsFilled4_clbk = bool (WINAPIV*)(struct _EMBELLKEY*, _EMBELLKEYIsFilled4_ptr);
        using _EMBELLKEYLoadDBKey6_ptr = void (WINAPIV*)(struct _EMBELLKEY*, int);
        using _<PERSON>MBELLKEYLoadDBKey6_clbk = void (WINAPIV*)(struct _EMBELLKEY*, int, _EMBELLKEYLoadDBKey6_ptr);
        using _EMBELLKEYSetRelease8_ptr = void (WINAPIV*)(struct _EMBELLKEY*);
        using _EMBELLKEYSetRelease8_clbk = void (WINAPIV*)(struct _EMBELLKEY*, _EMBELLKEYSetRelease8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

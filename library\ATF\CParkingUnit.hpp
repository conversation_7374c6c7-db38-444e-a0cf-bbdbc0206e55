// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGameObject.hpp>
#include <CPlayer.hpp>
#include <_object_id.hpp>
#include <_parkingunit_create_setdata.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CParkingUnit : CGameObject
    {
        CPlayer *m_pOwner;
        unsigned int m_dwOwnerSerial;
        char m_byFrame;
        char m_byPartCode[6];
        char m_byCreateType;
        char m_byTransDistCode;
        unsigned int m_dwParkingStartTime;
        unsigned __int16 m_wHPRate;
        unsigned int m_dwLastDestroyTime;
    public:
        CParkingUnit();
        void ctor_CParkingUnit();
        uint16_t CalcCurHPRate();
        void ChangeOwner(struct CPlayer* pNewOwner, char byUnitSlotIndex);
        bool Create(struct _parkingunit_create_setdata* pParam);
        bool Destroy(char byDestoryType);
        void Init(struct _object_id* pID);
        bool IsRideRight(struct CPlayer* pOne);
        void Loop();
        void SendMsg_ChangeOwner(char byUnitSlotIndex, struct CPlayer* pOldOwner);
        void SendMsg_Create();
        void SendMsg_Destroy(char byDestoryType);
        void SendMsg_FixPosition(int n);
        ~CParkingUnit();
        void dtor_CParkingUnit();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

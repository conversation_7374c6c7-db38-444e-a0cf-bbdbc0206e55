// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GMCallMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using GMCallMgrctor_GMCallMgr2_ptr = void (WINAPIV*)(struct GMCallMgr*);
        using GMCallMgrctor_GMCallMgr2_clbk = void (WINAPIV*)(struct GMCallMgr*, GMCallMgrctor_GMCallMgr2_ptr);
        using GMCallMgrGetGMRequestDataPtr4_ptr = struct GMRequestData* (WINAPIV*)(struct GMCallMgr*, struct CPlayer*);
        using GMCallMgrGetGMRequestDataPtr4_clbk = struct GMRequestData* (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, GMCallMgrGetGMRequestDataPtr4_ptr);
        using GMCallMgrInit6_ptr = void (WINAPIV*)(struct GMCallMgr*);
        using GMCallMgrInit6_clbk = void (WINAPIV*)(struct GMCallMgr*, GMCallMgrInit6_ptr);
        using GMCallMgrInitReqBuff8_ptr = void (WINAPIV*)(struct GMCallMgr*);
        using GMCallMgrInitReqBuff8_clbk = void (WINAPIV*)(struct GMCallMgr*, GMCallMgrInitReqBuff8_ptr);
        using GMCallMgrPopReqEmptNode10_ptr = struct GMRequestData* (WINAPIV*)(struct GMCallMgr*);
        using GMCallMgrPopReqEmptNode10_clbk = struct GMRequestData* (WINAPIV*)(struct GMCallMgr*, GMCallMgrPopReqEmptNode10_ptr);
        using GMCallMgrPushReqNode12_ptr = void (WINAPIV*)(struct GMCallMgr*, struct GMRequestData*);
        using GMCallMgrPushReqNode12_clbk = void (WINAPIV*)(struct GMCallMgr*, struct GMRequestData*, GMCallMgrPushReqNode12_ptr);
        using GMCallMgrRequestAcceptGMCall14_ptr = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, unsigned int);
        using GMCallMgrRequestAcceptGMCall14_clbk = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, unsigned int, GMCallMgrRequestAcceptGMCall14_ptr);
        using GMCallMgrRequestGMCall16_ptr = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, int);
        using GMCallMgrRequestGMCall16_clbk = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, int, GMCallMgrRequestGMCall16_ptr);
        using GMCallMgrRequestGMList18_ptr = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, int);
        using GMCallMgrRequestGMList18_clbk = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, int, GMCallMgrRequestGMList18_ptr);
        using GMCallMgrSendResponseAcceptResult20_ptr = void (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, struct CPlayer*, int);
        using GMCallMgrSendResponseAcceptResult20_clbk = void (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, struct CPlayer*, int, GMCallMgrSendResponseAcceptResult20_ptr);
        using GMCallMgrSendResponseGMCall22_ptr = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, int);
        using GMCallMgrSendResponseGMCall22_clbk = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, int, GMCallMgrSendResponseGMCall22_ptr);
        using GMCallMgrSendResponseGMList24_ptr = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, int);
        using GMCallMgrSendResponseGMList24_clbk = bool (WINAPIV*)(struct GMCallMgr*, struct CPlayer*, int, GMCallMgrSendResponseGMList24_ptr);
        
        using GMCallMgrdtor_GMCallMgr29_ptr = void (WINAPIV*)(struct GMCallMgr*);
        using GMCallMgrdtor_GMCallMgr29_clbk = void (WINAPIV*)(struct GMCallMgr*, GMCallMgrdtor_GMCallMgr29_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

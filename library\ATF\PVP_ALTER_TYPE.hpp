// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum PVP_ALTER_TYPE
    {
      kill_s_inc = 0x0,
      kill_p_inc = 0x1,
      die_dec = 0x2,
      quest_inc = 0x3,
      holy_dec = 0x4,
      cheat = 0x5,
      logoff_inc = 0x6,
      guildbattle = 0x7,
      logoff_dec = 0x8,
      holy_award = 0x9,
    };
END_ATF_NAMESPACE

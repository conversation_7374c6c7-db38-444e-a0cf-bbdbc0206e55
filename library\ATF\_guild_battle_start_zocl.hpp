// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _guild_battle_start_zocl
    {
        char byColorInx;
         unsigned int dwLeftRedScore;
         unsigned int dwRightBlueScore;
         unsigned int dwLeftRedGoalCnt;
         unsigned int dwRightBlueGoalCnt;
        char byLeftHour;
        char byLeftMin;
        char byLeftSec;
        int iRedPortalInx;
        int iBluePortalInx;
        int iRegenPortalInx[3];
    };
END_ATF_NAMESPACE

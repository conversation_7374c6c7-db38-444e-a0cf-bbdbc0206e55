// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_updateweeklyguildpvppointsum
    {
        unsigned int dwGuildSerial;
        long double dPvpPoint;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_updateweeklyguildpvppointsum, 16>(), "_qry_case_updateweeklyguildpvppointsum");
END_ATF_NAMESPACE

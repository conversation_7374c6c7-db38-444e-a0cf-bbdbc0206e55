// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_POINTL.hpp>
#include <_RECTL.hpp>
#include <tagEMR.hpp>


START_ATF_NAMESPACE
    struct tagEMRPOLYPOLYLINE
    {
        tagEMR emr;
        _RECTL rclBounds;
        unsigned int nPolys;
        unsigned int cptl;
        unsigned int aPolyCounts[1];
        _POINTL aptl[1];
    };
END_ATF_NAMESPACE

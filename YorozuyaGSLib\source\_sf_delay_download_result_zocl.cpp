#include <_sf_delay_download_result_zocl.hpp>


START_ATF_NAMESPACE
    _sf_delay_download_result_zocl::_sf_delay_download_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _sf_delay_download_result_zocl*);
        (org_ptr(0x1400f05d0L))(this);
    };
    void _sf_delay_download_result_zocl::ctor__sf_delay_download_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _sf_delay_download_result_zocl*);
        (org_ptr(0x1400f05d0L))(this);
    };
    _sf_delay_download_result_zocl::_eff_list::_eff_list()
    {
        using org_ptr = void (WINAPIV*)(struct _sf_delay_download_result_zocl::_eff_list*);
        (org_ptr(0x1400f0660L))(this);
    };
    void _sf_delay_download_result_zocl::_eff_list::ctor__eff_list()
    {
        using org_ptr = void (WINAPIV*)(struct _sf_delay_download_result_zocl::_eff_list*);
        (org_ptr(0x1400f0660L))(this);
    };
    _sf_delay_download_result_zocl::_mas_list::_mas_list()
    {
        using org_ptr = void (WINAPIV*)(struct _sf_delay_download_result_zocl::_mas_list*);
        (org_ptr(0x1400f0680L))(this);
    };
    void _sf_delay_download_result_zocl::_mas_list::ctor__mas_list()
    {
        using org_ptr = void (WINAPIV*)(struct _sf_delay_download_result_zocl::_mas_list*);
        (org_ptr(0x1400f0680L))(this);
    };
END_ATF_NAMESPACE

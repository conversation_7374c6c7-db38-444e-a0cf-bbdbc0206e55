#include <_nuclear_bomb_position_inform_zocl.hpp>


START_ATF_NAMESPACE
    _nuclear_bomb_position_inform_zocl::_nuclear_bomb_position_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_bomb_position_inform_zocl*);
        (org_ptr(0x14013e5e0L))(this);
    };
    void _nuclear_bomb_position_inform_zocl::ctor__nuclear_bomb_position_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_bomb_position_inform_zocl*);
        (org_ptr(0x14013e5e0L))(this);
    };
END_ATF_NAMESPACE

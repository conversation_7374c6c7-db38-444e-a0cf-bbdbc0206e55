// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CObject.hpp>
#include <CPtrList.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CDatabase : CObject
    {
        enum DbOpenOptions
        {
            openExclusive = 0x1,
            openReadOnly = 0x2,
            useCursorLib = 0x4,
            noOdbcDialog = 0x8,
            forceOdbcDialog = 0x10,
        };
        void *m_hdbc;
        int m_bTransactionPending;
        int m_bStripTrailingSpaces;
        int m_bIncRecordCountOnAdd;
        int m_bAddForUpdate;
        char m_chIDQuoteChar;
        ATL::CStringT<char> m_strConnect;
        CPtrList m_listRecordsets;
        int nRefCount;
        int m_bUpdatable;
        int m_bTransactions;
        __int16 m_nTransactionCapable;
        __int16 m_nCursorCommitBehavior;
        __int16 m_nCursorRollbackBehavior;
        unsigned int m_dwUpdateOptions;
        unsigned int m_dwBookmarkAttributes;
        unsigned int m_dwLoginTimeout;
        void *m_hstmt;
        unsigned int m_dwQueryTimeout;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

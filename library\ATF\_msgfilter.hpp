// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMHDR.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _msgfilter
    {
        tagNMHDR nmhdr;
        unsigned int msg;
        unsigned __int64 wParam;
        __int64 lParam;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

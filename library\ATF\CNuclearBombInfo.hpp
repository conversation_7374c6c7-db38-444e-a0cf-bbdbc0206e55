// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNuclearBomb.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CNuclearBombAttack2_ptr = void (WINAPIV*)(struct CNuclearBomb*, int, int);
        using CNuclearBombAttack2_clbk = void (WINAPIV*)(struct CNuclearBomb*, int, int, CNuclearBombAttack2_ptr);
        
        using CNuclearBombctor_CNuclearBomb4_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombctor_CNuclearBomb4_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombctor_CNuclearBomb4_ptr);
        using CNuclearBombCreate6_ptr = bool (WINAPIV*)(struct CNuclearBomb*, struct _nuclear_create_setdata*);
        using CNuclearBomb<PERSON>reate6_clbk = bool (WINAPIV*)(struct CNuclearBomb*, struct _nuclear_create_setdata*, CNuclearBombCreate6_ptr);
        using CNuclearBombDestroy8_ptr = bool (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombDestroy8_clbk = bool (WINAPIV*)(struct CNuclearBomb*, CNuclearBombDestroy8_ptr);
        using CNuclearBombGetBombStatus10_ptr = char (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombGetBombStatus10_clbk = char (WINAPIV*)(struct CNuclearBomb*, CNuclearBombGetBombStatus10_ptr);
        using CNuclearBombGetControlSerial12_ptr = uint16_t (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombGetControlSerial12_clbk = uint16_t (WINAPIV*)(struct CNuclearBomb*, CNuclearBombGetControlSerial12_ptr);
        using CNuclearBombGetDamagedObjNum14_ptr = int (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombGetDamagedObjNum14_clbk = int (WINAPIV*)(struct CNuclearBomb*, CNuclearBombGetDamagedObjNum14_ptr);
        using CNuclearBombGetGenAttackProb16_ptr = int (WINAPIV*)(struct CNuclearBomb*, struct CCharacter*, int);
        using CNuclearBombGetGenAttackProb16_clbk = int (WINAPIV*)(struct CNuclearBomb*, struct CCharacter*, int, CNuclearBombGetGenAttackProb16_ptr);
        using CNuclearBombGetItemIndex18_ptr = uint16_t (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombGetItemIndex18_clbk = uint16_t (WINAPIV*)(struct CNuclearBomb*, CNuclearBombGetItemIndex18_ptr);
        using CNuclearBombGetMasterClass20_ptr = char (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombGetMasterClass20_clbk = char (WINAPIV*)(struct CNuclearBomb*, CNuclearBombGetMasterClass20_ptr);
        using CNuclearBombGetMasterRace22_ptr = char (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombGetMasterRace22_clbk = char (WINAPIV*)(struct CNuclearBomb*, CNuclearBombGetMasterRace22_ptr);
        using CNuclearBombGetMissilePos24_ptr = float* (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombGetMissilePos24_clbk = float* (WINAPIV*)(struct CNuclearBomb*, CNuclearBombGetMissilePos24_ptr);
        using CNuclearBombGetNewSerial26_ptr = unsigned int (WINAPIV*)();
        using CNuclearBombGetNewSerial26_clbk = unsigned int (WINAPIV*)(CNuclearBombGetNewSerial26_ptr);
        using CNuclearBombGetShowEffectList28_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombGetShowEffectList28_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombGetShowEffectList28_ptr);
        using CNuclearBombGetUse30_ptr = bool (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombGetUse30_clbk = bool (WINAPIV*)(struct CNuclearBomb*, CNuclearBombGetUse30_ptr);
        using CNuclearBombInit32_ptr = bool (WINAPIV*)(struct CNuclearBomb*, struct _object_id*);
        using CNuclearBombInit32_clbk = bool (WINAPIV*)(struct CNuclearBomb*, struct _object_id*, CNuclearBombInit32_ptr);
        using CNuclearBombLoop34_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombLoop34_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombLoop34_ptr);
        using CNuclearBombNuclearDamege36_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombNuclearDamege36_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombNuclearDamege36_ptr);
        using CNuclearBombRecvKillMessage38_ptr = void (WINAPIV*)(struct CNuclearBomb*, struct CCharacter*);
        using CNuclearBombRecvKillMessage38_clbk = void (WINAPIV*)(struct CNuclearBomb*, struct CCharacter*, CNuclearBombRecvKillMessage38_ptr);
        using CNuclearBombSendMsg_AddEffect40_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombSendMsg_AddEffect40_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombSendMsg_AddEffect40_ptr);
        using CNuclearBombSendMsg_Attack42_ptr = void (WINAPIV*)(struct CNuclearBomb*, int, int);
        using CNuclearBombSendMsg_Attack42_clbk = void (WINAPIV*)(struct CNuclearBomb*, int, int, CNuclearBombSendMsg_Attack42_ptr);
        using CNuclearBombSendMsg_DropMissile44_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombSendMsg_DropMissile44_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombSendMsg_DropMissile44_ptr);
        using CNuclearBombSendMsg_InformAttack46_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombSendMsg_InformAttack46_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombSendMsg_InformAttack46_ptr);
        using CNuclearBombSendMsg_InformDropPos48_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombSendMsg_InformDropPos48_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombSendMsg_InformDropPos48_ptr);
        using CNuclearBombSendMsg_MasterDie50_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombSendMsg_MasterDie50_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombSendMsg_MasterDie50_ptr);
        using CNuclearBombSendMsg_NuclearFind52_ptr = void (WINAPIV*)(struct CNuclearBomb*, int, char);
        using CNuclearBombSendMsg_NuclearFind52_clbk = void (WINAPIV*)(struct CNuclearBomb*, int, char, CNuclearBombSendMsg_NuclearFind52_ptr);
        using CNuclearBombSendMsg_Result54_ptr = void (WINAPIV*)(struct CNuclearBomb*, int, char);
        using CNuclearBombSendMsg_Result54_clbk = void (WINAPIV*)(struct CNuclearBomb*, int, char, CNuclearBombSendMsg_Result54_ptr);
        using CNuclearBombSetBombStatus56_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombSetBombStatus56_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombSetBombStatus56_ptr);
        using CNuclearBombSetControlSerial58_ptr = void (WINAPIV*)(struct CNuclearBomb*, uint16_t);
        using CNuclearBombSetControlSerial58_clbk = void (WINAPIV*)(struct CNuclearBomb*, uint16_t, CNuclearBombSetControlSerial58_ptr);
        using CNuclearBombSetNuclearIndex60_ptr = void (WINAPIV*)(struct CNuclearBomb*, uint16_t);
        using CNuclearBombSetNuclearIndex60_clbk = void (WINAPIV*)(struct CNuclearBomb*, uint16_t, CNuclearBombSetNuclearIndex60_ptr);
        using CNuclearBombWarningToAll62_ptr = void (WINAPIV*)(struct CNuclearBomb*, char);
        using CNuclearBombWarningToAll62_clbk = void (WINAPIV*)(struct CNuclearBomb*, char, CNuclearBombWarningToAll62_ptr);
        
        using CNuclearBombdtor_CNuclearBomb67_ptr = void (WINAPIV*)(struct CNuclearBomb*);
        using CNuclearBombdtor_CNuclearBomb67_clbk = void (WINAPIV*)(struct CNuclearBomb*, CNuclearBombdtor_CNuclearBomb67_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

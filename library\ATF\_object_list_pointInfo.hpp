// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_object_list_point.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _object_list_pointInitLink2_ptr = void (WINAPIV*)(struct _object_list_point*);
        using _object_list_pointInitLink2_clbk = void (WINAPIV*)(struct _object_list_point*, _object_list_pointInitLink2_ptr);
        using _object_list_pointSetPoint4_ptr = void (WINAPIV*)(struct _object_list_point*, struct CGameObject*);
        using _object_list_pointSetPoint4_clbk = void (WINAPIV*)(struct _object_list_point*, struct CGameObject*, _object_list_pointSetPoint4_ptr);
        
        using _object_list_pointctor__object_list_point6_ptr = void (WINAPIV*)(struct _object_list_point*);
        using _object_list_pointctor__object_list_point6_clbk = void (WINAPIV*)(struct _object_list_point*, _object_list_pointctor__object_list_point6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CReturnGateCreateParam.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CReturnGateCreateParamctor_CReturnGateCreateParam2_ptr = void (WINAPIV*)(struct CReturnGateCreateParam*, struct CPlayer*);
        using CReturnGateCreateParamctor_CReturnGateCreateParam2_clbk = void (WINAPIV*)(struct CReturnGateCreateParam*, struct CPlayer*, CReturnGateCreateParamctor_CReturnGateCreateParam2_ptr);
        using CReturnGateCreateParamGetOwner4_ptr = struct CPlayer* (WINAPIV*)(struct CReturnGateCreateParam*);
        using CReturnGateCreateParamGetOwner4_clbk = struct CPlayer* (WINAPIV*)(struct CReturnGateCreateParam*, CReturnGateCreateParamGetOwner4_ptr);
        
        using CReturnGateCreateParamdtor_CReturnGateCreateParam6_ptr = void (WINAPIV*)(struct CReturnGateCreateParam*);
        using CReturnGateCreateParamdtor_CReturnGateCreateParam6_clbk = void (WINAPIV*)(struct CReturnGateCreateParam*, CReturnGateCreateParamdtor_CReturnGateCreateParam6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

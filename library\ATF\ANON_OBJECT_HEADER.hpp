// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    struct ANON_OBJECT_HEADER
    {
        unsigned __int16 Sig1;
        unsigned __int16 Sig2;
        unsigned __int16 Version;
        unsigned __int16 Machine;
        unsigned int TimeDateStamp;
        _GUID ClassID;
        unsigned int SizeOfData;
    };
END_ATF_NAMESPACE

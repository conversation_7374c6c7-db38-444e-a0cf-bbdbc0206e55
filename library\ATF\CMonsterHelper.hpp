// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>
#include <CGameObject.hpp>
#include <CMonster.hpp>
#include <CPlayer.hpp>
#include <_NEAR_DATA.hpp>


START_ATF_NAMESPACE
    struct  CMonsterHelper
    {
    public:
        static int CheckPreAttackRangeTargetAbleCharacter(struct CMonster* pMon, struct CGameObject* pTarget);
        static float GetAngle(float* mon, float* plr);
        static void GetDirection(float** cur, float** tar, float** out, float deg);
        static void HierarcyHelpCast(struct CMonster* pMon);
        static int IsInSector(float* chkpos, float* src, float* dest, float angle, float radius, float* pfDist);
        static unsigned int SearchNearMonster(struct CMonster* pMon, struct _NEAR_DATA* NearChar, unsigned int dwArSize, int bTargetIgnore);
        static struct CMonster* SearchNearMonsterByDistance(struct CMonster* pMon, unsigned int dwDist);
        static struct CPlayer* SearchNearPlayer(struct CMonster* pMon, int nType);
        static int SearchPatrolMovePos(struct CMonster* mon, float** NewTar);
        static int SearchTargetMovePos_MovingTarget(struct CMonster* pMon, struct CCharacter* pTargetCharacter, float** tarPos);
        static int SearchTargetMovePos_StopTarget(struct CMonster* pMon, struct CCharacter* pTargetCharacter, float** tarPos);
        static void TransPort(struct CMonster* mon, float* tarPos);
    };
END_ATF_NAMESPACE

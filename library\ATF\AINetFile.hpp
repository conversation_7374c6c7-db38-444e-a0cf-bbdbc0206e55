// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AINetFileVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct AINetFile
    {
        AINetFileVtbl *vfptr;
        void *m_hFile;
        unsigned int m_dwAccess;
    public:
        AINetFile(void* hFile, unsigned int dwAccess);
        void ctor_AINetFile(void* hFile, unsigned int dwAccess);
        void Close();
        unsigned int Read(void* lpBuf, unsigned int nCount);
        unsigned int Write(void* lpBuf, unsigned int nCount);
        int WriteString(char* str);
        ~AINetFile();
        void dtor_AINetFile();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

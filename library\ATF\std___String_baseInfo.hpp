// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___String_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            
            using std___String_basector__String_base1_ptr = int64_t (WINAPIV*)(struct std::_String_base*, struct std::_String_base*);
            using std___String_basector__String_base1_clbk = int64_t (WINAPIV*)(struct std::_String_base*, struct std::_String_base*, std___String_basector__String_base1_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

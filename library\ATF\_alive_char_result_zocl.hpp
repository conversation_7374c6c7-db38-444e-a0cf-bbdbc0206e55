// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_REGED_AVATOR_DB.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _alive_char_result_zocl
    {
        char byRetCode;
        unsigned int dwSerial;
        _REGED_AVATOR_DB AliveChar;
    public:
        _alive_char_result_zocl();
        void ctor__alive_char_result_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_alive_char_result_zocl, 74>(), "_alive_char_result_zocl");
END_ATF_NAMESPACE

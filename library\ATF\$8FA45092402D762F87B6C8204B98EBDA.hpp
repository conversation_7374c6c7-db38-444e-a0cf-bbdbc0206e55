// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IDispatch.hpp>
#include <IUnknown.hpp>
#include <_FLAGGED_WORD_BLOB.hpp>
#include <_wireBRECORD.hpp>
#include <_wireSAFEARRAY.hpp>
#include <_wireVARIANT.hpp>
#include <tagCY.hpp>
#include <tagDEC.hpp>


START_ATF_NAMESPACE
    union $8FA45092402D762F87B6C8204B98EBDA
    {
        __int64 llVal;
        int lVal;
        char bVal;
        __int16 iVal;
        float fltVal;
        long double dblVal;
        __int16 boolVal;
        int scode;
        tagCY cyVal;
        long double date;
        _FLAGGED_WORD_BLOB *bstrVal;
        IUnknown *punkVal;
        IDispatch *pdispVal;
        _wireSAFEARRAY **parray;
        _wireBRECORD *brecVal;
        char *pbVal;
        __int16 *piVal;
        int *plVal;
        __int64 *pllVal;
        float *pfltVal;
        long double *pdblVal;
        __int16 *pboolVal;
        int *pscode;
        tagCY *pcyVal;
        long double *pdate;
        _FLAGGED_WORD_BLOB **pbstrVal;
        IUnknown **ppunkVal;
        IDispatch **ppdispVal;
        _wireSAFEARRAY ***pparray;
        _wireVARIANT **pvarVal;
        char cVal;
        unsigned __int16 uiVal;
        unsigned int ulVal;
        unsigned __int64 ullVal;
        int intVal;
        unsigned int uintVal;
        tagDEC decVal;
        tagDEC *pdecVal;
        char *pcVal;
        unsigned __int16 *puiVal;
        unsigned int *pulVal;
        unsigned __int64 *pullVal;
        int *pintVal;
        unsigned int *puintVal;
    };
END_ATF_NAMESPACE

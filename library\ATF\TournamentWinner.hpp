// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct TournamentWinner
    {
        unsigned int dwSerial;
        char wsz<PERSON>har<PERSON><PERSON>[17];
        char byGrade;
    public:
        TournamentWinner();
        void ctor_TournamentWinner();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<TournamentWinner, 24>(), "TournamentWinner");
END_ATF_NAMESPACE

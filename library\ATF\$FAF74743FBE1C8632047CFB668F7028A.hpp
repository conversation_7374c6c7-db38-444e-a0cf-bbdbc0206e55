// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $FAF74743FBE1C8632047CFB668F7028A
    {
        unsigned int LowPart;
        int HighPart;
    };    
    static_assert(ATF::checkSize<$FAF74743FBE1C8632047CFB668F7028A, 8>(), "$FAF74743FBE1C8632047CFB668F7028A");
END_ATF_NAMESPACE

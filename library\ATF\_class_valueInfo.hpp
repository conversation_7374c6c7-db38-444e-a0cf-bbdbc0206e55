// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_class_value.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _class_valuector__class_value2_ptr = void (WINAPIV*)(struct _class_value*);
        using _class_valuector__class_value2_clbk = void (WINAPIV*)(struct _class_value*, _class_valuector__class_value2_ptr);
        using _class_valueinit4_ptr = void (WINAPIV*)(struct _class_value*);
        using _class_valueinit4_clbk = void (WINAPIV*)(struct _class_value*, _class_valueinit4_ptr);
        using _class_valuesize6_ptr = int (WINAPIV*)(struct _class_value*);
        using _class_valuesize6_clbk = int (WINAPIV*)(struct _class_value*, _class_valuesize6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

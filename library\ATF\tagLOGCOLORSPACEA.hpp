// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagICEXYZTRIPLE.hpp>


START_ATF_NAMESPACE
    struct tagLOGCOLORSPACEA
    {
        unsigned int lcsSignature;
        unsigned int lcsVersion;
        unsigned int lcsSize;
        int lcsCSType;
        int lcsIntent;
        tagICEXYZTRIPLE lcsEndpoints;
        unsigned int lcsGammaRed;
        unsigned int lcsGammaGreen;
        unsigned int lcsGammaBlue;
        char lcsFilename[260];
    };
END_ATF_NAMESPACE

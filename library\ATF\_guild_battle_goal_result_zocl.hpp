// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _guild_battle_goal_result_zocl
    {
        char byRet;
        char wszGuildName[17];
        unsigned int dwObjSerial;
        char wszCharName[17];
        unsigned int dwLeftRedScore;
        unsigned int dwRightBlueScore;
        unsigned int dwLeftRedGoalCnt;
        unsigned int dwRightBlueGoalCnt;
        char byLeftHour;
        char byLeftMin;
        char byLeftSec;
    public:
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

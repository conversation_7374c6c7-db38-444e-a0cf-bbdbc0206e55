// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RTL_VERIFIER_THUNK_DESCRIPTOR.hpp>



START_ATF_NAMESPACE
    struct _RTL_VERIFIER_DLL_DESCRIPTOR
    {
        wchar_t *DllName;
        unsigned int DllFlags;
        void *DllAddress;
        _RTL_VERIFIER_THUNK_DESCRIPTOR *DllThunks;
    };
END_ATF_NAMESPACE

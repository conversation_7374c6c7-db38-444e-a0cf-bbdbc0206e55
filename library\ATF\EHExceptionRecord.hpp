// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct EHExceptionRecord
    {
        struct EHParameters
        {
            unsigned int magicNumber;
            void *pExceptionObject;
            struct _s_ThrowInfo *pThrowInfo;
            void *pThrowImageBase;
        };
        unsigned int ExceptionCode;
        unsigned int ExceptionFlags;
        struct _EXCEPTION_RECORD *ExceptionRecord;
        void *ExceptionAddress;
        unsigned int NumberParameters;
        EHParameters params;
    };
END_ATF_NAMESPACE

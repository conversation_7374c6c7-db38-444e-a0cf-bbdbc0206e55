// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CRecordData.hpp>
#include <_TimeItem_fld.hpp>


START_ATF_NAMESPACE
    struct TimeItem
    {
        CLogFile _kLogger;
        CRecordData _kRecTimeItem;
    public:
        bool CheckGoods();
        static struct _TimeItem_fld* FindTimeRec(int nTbl, int nIdx);
        bool Init();
        static struct TimeItem* Instance();
        bool MakeLinkTable(char* szMsg, int nSize);
        bool ReadGoods();
        TimeItem();
        void ctor_TimeItem();
    };
END_ATF_NAMESPACE

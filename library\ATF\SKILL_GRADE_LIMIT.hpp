// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum SKILL_GRADE_LIMIT
    {
      LIMIT_ALL = 0x0,
      LIMIT_PATRIARCH = 0x1,
      LIMIT_SUB_PATRIARCH_A = 0x2,
      LIMIT_SUB_PATRIARCH_B = 0x3,
      LIMIT_ATTACK_A = 0x4,
      LIMIT_ATTACK_B = 0x5,
      LIMIT_DEFENSE_A = 0x6,
      LIMIT_DEFENSE_B = 0x7,
      LIMIT_SUPPORT_A = 0x8,
      LIMIT_SUPPORT_B = 0x9,
      LIMIT_GUILD_MASTER = 0xA,
      LIMIT_PARTY_LEADER = 0xB,
      LIMIT_GRADE_NUM = 0xC,
    };
END_ATF_NAMESPACE

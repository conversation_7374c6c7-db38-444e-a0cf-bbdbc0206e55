// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _itembox_fixpositon_zocl
    {
        char byItemTableCode;
        unsigned __int16 wItemRecIndex;
        char byAmount;
        unsigned __int16 wItemBoxIndex;
        unsigned int dwOwerSerial;
        __int16 zPos[3];
        char byState;
        char byThrowerRace;
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_itembox_fixpositon_zocl, 0x12>(), "_itembox_fixpositon_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CashDbWorker.hpp>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    struct  CCashDbWorkerES : CashDbWorker
    {
    public:
        CCashDbWorkerES();
        void ctor_CCashDbWorkerES();
        void GetUseCashQueryStr(struct _param_cash_update* rParam, int nIdx, char* wszQuery, uint64_t tBufferSize);
        ~CCashDbWorkerES();
        void dtor_CCashDbWorkerES();
    };
END_ATF_NAMESPACE

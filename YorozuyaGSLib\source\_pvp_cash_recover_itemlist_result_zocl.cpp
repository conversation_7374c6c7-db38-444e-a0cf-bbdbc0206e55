#include <_pvp_cash_recover_itemlist_result_zocl.hpp>


START_ATF_NAMESPACE
    _pvp_cash_recover_itemlist_result_zocl::_pvp_cash_recover_itemlist_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _pvp_cash_recover_itemlist_result_zocl*);
        (org_ptr(0x1403f6d70L))(this);
    };
    void _pvp_cash_recover_itemlist_result_zocl::ctor__pvp_cash_recover_itemlist_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _pvp_cash_recover_itemlist_result_zocl*);
        (org_ptr(0x1403f6d70L))(this);
    };
    int _pvp_cash_recover_itemlist_result_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _pvp_cash_recover_itemlist_result_zocl*);
        return (org_ptr(0x1403f6dc0L))(this);
    };
END_ATF_NAMESPACE

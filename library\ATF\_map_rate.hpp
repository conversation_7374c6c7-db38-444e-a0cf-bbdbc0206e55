// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _map_rate
    {
        float fSide[2];
        int nStandard;
        int nPartner;
    public:
        void Setting(int x, int y);
    };    
    static_assert(ATF::checkSize<_map_rate, 16>(), "_map_rate");
END_ATF_NAMESPACE

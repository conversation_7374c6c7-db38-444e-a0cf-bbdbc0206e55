// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_NEAR_DATA.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _NEAR_DATAInit2_ptr = void (WINAPIV*)(struct _NEAR_DATA*);
        using _NEAR_DATAInit2_clbk = void (WINAPIV*)(struct _NEAR_DATA*, _NEAR_DATAInit2_ptr);
        
        using _NEAR_DATActor__NEAR_DATA4_ptr = void (WINAPIV*)(struct _NEAR_DATA*);
        using _NEAR_DATActor__NEAR_DATA4_clbk = void (WINAPIV*)(struct _NEAR_DATA*, _NEAR_DATActor__NEAR_DATA4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

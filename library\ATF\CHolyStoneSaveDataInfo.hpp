// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHolyStoneSaveData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CHolyStoneSaveDataDefaultInit2_ptr = void (WINAPIV*)(struct CHolyStoneSaveData*, struct CHolyScheduleData::__HolyScheduleNode*);
        using CHolyStoneSaveDataDefaultInit2_clbk = void (WINAPIV*)(struct CHolyStoneSaveData*, struct CHolyScheduleData::__HolyScheduleNode*, CHolyStoneSaveDataDefaultInit2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleState.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CNormalGuildBattleStateFin : CNormalGuildBattleState
        {
        public:
            CNormalGuildBattleStateFin();
            void ctor_CNormalGuildBattleStateFin();
            int Fin(struct CNormalGuildBattle* pkBattle);
            int ctor_Fin(struct CNormalGuildBattle* pkBattle);
            struct ATL::CTimeSpan* GetTerm(struct ATL::CTimeSpan* result);
            ~CNormalGuildBattleStateFin();
            void dtor_CNormalGuildBattleStateFin();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CNormalGuildBattleStateFin, 8>(), "GUILD_BATTLE::CNormalGuildBattleStateFin");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

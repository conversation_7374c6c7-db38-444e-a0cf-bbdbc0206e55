// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPostData.hpp>
#include <CRFNewDatabase.hpp>
#include <TournamentWinner.hpp>
#include <_AIOC_A_MACRODATA.hpp>
#include <_DB_LOAD_AUTOMINE_MACHINE.hpp>
#include <_PCBANG_PLAY_TIME.hpp>
#include <_PVP_RANK_DATA.hpp>
#include <_SYSTEMTIME.hpp>
#include <_candidate_info.hpp>
#include <_guild_honor_list_result_zocl.hpp>
#include <_guildbattle_totalrecord.hpp>
#include <_guildroom_info.hpp>
#include <_patriarch_comm_list.hpp>
#include <_personal_amine_inven.hpp>
#include <_post_storage_list.hpp>
#include <_pvporderview_info.hpp>
#include <_pvppoint_guild_rank_info.hpp>
#include <_pvppointlimit_info.hpp>
#include <_qry_case_all_store_limit_item.hpp>
#include <_qry_case_gm_greetingmsg.hpp>
#include <_qry_case_guild_greetingmsg.hpp>
#include <_qry_case_race_greetingmsg.hpp>
#include <_race_battle_log_info.hpp>
#include <_raceboss_acc_winrate.hpp>
#include <_rege_char_data.hpp>
#include <_return_post_list.hpp>
#include <_sel_patriarch_elect_state.hpp>
#include <_total_guild_rank_info.hpp>
#include <_unmannedtrader_buy_item_info.hpp>
#include <_unmannedtrader_page_info.hpp>
#include <_unmannedtrader_registsingleitem.hpp>
#include <_unmannedtrader_reserved_schedule_info.hpp>
#include <_unmannedtrader_seller_info.hpp>
#include <_unmannedtrader_stade_id_info.hpp>
#include <_weeklyguildrank_owner_info.hpp>
#include <_worlddb_arrange_char_info.hpp>
#include <_worlddb_buddy_info.hpp>
#include <_worlddb_cash_limited_sale.hpp>
#include <_worlddb_character_array_info.hpp>
#include <_worlddb_character_base_info.hpp>
#include <_worlddb_character_base_info_array.hpp>
#include <_worlddb_character_general_info.hpp>
#include <_worlddb_character_supplement_info.hpp>
#include <_worlddb_crymsg_info.hpp>
#include <_worlddb_economy_history_info_array.hpp>
#include <_worlddb_golden_box_item.hpp>
#include <_worlddb_guild_battle_info.hpp>
#include <_worlddb_guild_battle_rank_list.hpp>
#include <_worlddb_guild_battle_reserved_schedule_info.hpp>
#include <_worlddb_guild_battle_schedule_list.hpp>
#include <_worlddb_guild_info.hpp>
#include <_worlddb_guild_member_info.hpp>
#include <_worlddb_guild_money_io_info.hpp>
#include <_worlddb_inven_info.hpp>
#include <_worlddb_item_list.hpp>
#include <_worlddb_itemcombineex_info.hpp>
#include <_worlddb_npc_quest_complete_history.hpp>
#include <_worlddb_ore_cutting.hpp>
#include <_worlddb_pcbang_favor_item.hpp>
#include <_worlddb_potion_delay_info.hpp>
#include <_worlddb_quest_array.hpp>
#include <_worlddb_rankinguild_info.hpp>
#include <_worlddb_sf_delay_info.hpp>
#include <_worlddb_start_npc_quest_complete_history.hpp>
#include <_worlddb_time_limit_info.hpp>
#include <_worlddb_trade_info.hpp>
#include <_worlddb_trunk_info.hpp>
#include <_worlddb_unit_info_array.hpp>
#include <_worlddb_update_char_query.hpp>
#include <_worlddb_user_count_info.hpp>
#include <_worlddb_userinterface_info.hpp>


START_ATF_NAMESPACE
    struct  CRFWorldDatabase : CRFNewDatabase
    {
    public:
        bool Add_PvpPoint(unsigned int dwSerial, unsigned int dwPoint, unsigned int dwCashBag);
        CRFWorldDatabase();
        void ctor_CRFWorldDatabase();
        bool Check_GuildMemberCount(unsigned int dwGuildSerial);
        bool CreateCharacterSelectLogTable(char* szTableName);
        bool CreateGuildBattleRankTable(char* szDate);
        bool Create_PvpPointGuildRankTable(char* szDate);
        bool DeleteGuildBattleInfo();
        bool DeleteGuildBattleScheduleInfo();
        bool Delete_CharacterData(unsigned int dwCharacterSerial);
        bool Delete_Guild(unsigned int dwGuildSerial);
        bool Delete_ItemCharge(unsigned int dwItemChargeIndex);
        char Delete_PatriarchComm(unsigned int dwSerial, char* pszDepDate);
        bool Delete_TrunkItemCharge(unsigned int dwDBID);
        bool Delete_TrunkItemCharge_Extend(unsigned int dwDBID);
        bool InsertChangeClassLogAfterInitClass(unsigned int dwCharacSerial, char byType, char* szPrevClass, char* szNextClass, int nClassInitCnt, char byLastClassGrade, uint16_t dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec);
        bool InsertCharacterSelectLog(unsigned int dwAccountSerial, char* wszAccount, unsigned int dwCharacSerial, char* pwszCharacName, uint16_t dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec);
        bool InsertGuildBattleDefaultRecord(unsigned int dwRowCnt);
        bool InsertGuildBattleRankRecord(unsigned int dwGuildSerial);
        bool InsertGuildBattleScheduleDefaultRecord(unsigned int uiDayCnt, unsigned int uiMapCnt, char byMaxHour, char byUnitTimeCntPerTime);
        bool Insert_AccountTrunk(unsigned int dwAccountSerial);
        bool Insert_AccountTrunkExtend(unsigned int dwAccountSerial);
        bool Insert_AnimusData(unsigned int dwSerial, long double* pVal);
        bool Insert_AnimusLog(unsigned int dwSerial, char* wszName, char byDID, long double dOrgVal, long double dChgVal);
        bool Insert_BossCryRecord(unsigned int dwSerial);
        bool Insert_Buddy(unsigned int dwSerial);
        bool Insert_CashLimSale();
        bool Insert_CharacterData(char* pwszCharacterName, char* wszClassCode, unsigned int dwAccountSerial, char* wszAccount, char bySlotIndex, char byRaceSexCode, unsigned int dwBaseShape, int nMapIndex, unsigned int* pDwSerial);
        bool Insert_DefaultWeeklyGuildPvpPointSumRecord();
        bool Insert_Economy_History(unsigned int dwDate, struct _worlddb_economy_history_info_array::_worlddb_economy_history_info* pEconomyData);
        bool Insert_GoldenBoxItem();
        bool Insert_GreetingRecord(int nUseType, char* wszName, char* wszMessage);
        bool Insert_GuidRoom(unsigned int dwGuildSerial, char byRoomType, char byRace);
        bool Insert_Guild(char* pwszGuildName, char byRace);
        bool Insert_GuildBatlleResultLog(char* szStartTime, char* szEndTime, unsigned int dwRedSerial, char* wszRedName, unsigned int dwBlueSerial, char* wszBlueName, unsigned int dwRedScore, unsigned int dwBlueScore, unsigned int dwRedMaxJoinCnt, unsigned int dwBlueMaxJoinCnt, unsigned int dwRedGoalCntSum, unsigned int dwBlueGoalCntSum, unsigned int dwRedKillCntSum, unsigned int dwBlueKillCntSum, char byBattleResult, unsigned int dwMaxGoalCharacSerial, char* wszMaxGoalCharacName, unsigned int dwMaxKillCharacSerial, char* wszMaxKillCharacName, char byJoinLimit, unsigned int dwGuildBattleCostGold, char* szBattleMapCode);
        bool Insert_GuildBatlleResultLogBattelInfo(char* szStartTime, char* szEndTime, unsigned int dwRedSerial, char* wszRedName, unsigned int dwBlueSerial, char* wszBlueName, unsigned int dwRedScore, unsigned int dwBlueScore, unsigned int dwRedMaxJoinCnt, unsigned int dwBlueMaxJoinCnt, unsigned int dwRedGoalCntSum, unsigned int dwBlueGoalCntSum, unsigned int dwRedKillCntSum, unsigned int dwBlueKillCntSum, char byBattleResult, unsigned int dwMaxGoalCharacSerial, char* wszMaxGoalCharacName, unsigned int dwMaxKillCharacSerial, char* wszMaxKillCharacName, char byJoinLimit, unsigned int dwGuildBattleCostGold, char* szBattleMapCode);
        bool Insert_GuildMoneyHistory(unsigned int dwGuildSerial, long double dInoutDalant, long double dInoutGold, long double dResultDalant, long double dResultGold, char* wszDate, unsigned int dwAvatorSerial, char* pwszName);
        bool Insert_ItemChargeInGame(unsigned int dwAvatorSerial, unsigned int dwItemCode_K, uint64_t dwItemCode_D, unsigned int dwItemCode_U, char byType);
        bool Insert_ItemCombineEx(unsigned int dwCharacterSerial);
        bool Insert_Level_Log(unsigned int dwCharacterSerial, char byLevel, unsigned int dwTotalPlayMin);
        bool Insert_LimitItemRecord(unsigned int* pdwSerial);
        bool Insert_MacroData(unsigned int dwSerial);
        bool Insert_NpcData(unsigned int dwSerial);
        bool Insert_NpcData(unsigned int dwSerial, unsigned int* pNpcData);
        bool Insert_NpcLog(unsigned int dwSerial, char* pwszName, char byIndex, unsigned int dwOrgValue, unsigned int dwChgValue);
        bool Insert_NpcQuest_History(unsigned int dwSerial);
        bool Insert_OreCutting(unsigned int dwSerial);
        bool Insert_OreReset_Log(char byType, int nLiveUsercnt, unsigned int dwOreRemain, unsigned int dwTAmount);
        bool Insert_PSDefaultRecord(unsigned int dwCum);
        char Insert_PatriarchComm(unsigned int dwSerial, unsigned int dwDalant, char* pszDepDate);
        bool Insert_PatrirchItemChargeRefund(char* szData);
        bool Insert_PcBangFavorItem(unsigned int dwSerial);
        bool Insert_PlayerTimeLimitInfo(unsigned int dwAccountSerial);
        bool Insert_PostStorageRecord();
        bool Insert_PotionDelay(unsigned int dwSerial);
        bool Insert_PrimiumPlayTime(unsigned int dwAccSerial);
        bool Insert_PvpOrderViewInfo(unsigned int dwSerial);
        bool Insert_PvpPointGuildRankData(char* szDate);
        bool Insert_PvpPointLimitInfoRecord(unsigned int dwSerial);
        bool Insert_Quest(unsigned int dwSerial);
        bool Insert_RFEvent_ClassRefine(unsigned int dwAvatorSerial);
        bool Insert_RaceBattleLog(struct _race_battle_log_info* pInfo);
        bool Insert_RenamePotionLog(unsigned int dwSerial, char* wszOldName, char* wszNewName);
        bool Insert_SFDelayInfo(unsigned int dwSerial, struct _worlddb_sf_delay_info* pSFDelay);
        bool Insert_Set_Limit_Run(char* pData, int iSize);
        bool Insert_SettlementOwnerLog(char byNth, char byRace, unsigned int dwGuildSerial, char* wszGuildName, uint16_t wRank, char byGrade, long double dKillPvpPoint, long double dGuildBattlePvpPoint, unsigned int dwSumLv);
        bool Insert_Start_NpcQuest_History(unsigned int dwSerial, char* szQuestCode, char byLevel, char* szTime, int64_t nEndTime);
        bool Insert_Supplement(unsigned int dwSerial);
        bool Insert_Unit(unsigned int dwCharacterSerial);
        bool Insert_UnitData(unsigned int dwSerial, long double* pUnitData);
        bool Insert_UnitLog(unsigned int dwSerial, char* wszName, char byDID, long double dOrgVal, long double dChgVal);
        bool Insert_UnmannedTraderItemStateRecord(unsigned int dwRowCnt, wchar_t** ppwszStr);
        bool Insert_UnmannedTraderSingleDefaultRecord(unsigned int dwRowCnt);
        bool Insert_UserInterface(unsigned int dwSerial);
        bool Insert_UserNum_Log(int nAvgUserNum, int nMaxUserNum);
        bool Insert_WeeklyGuildPvpPointSum(unsigned int dwSerial);
        bool LoadGreetingMsg(char* pwszGMGreetingmsg, char* pwszRaceGreetingmsgA, char* pwszRaceGreetingmsgB, char* pwszRaceGreetingmsgC, char* pwszGMName, char* pwszNameA, char* pwszNameB, char* pwszNameC);
        bool LoadGuildBattleInfo(unsigned int dwStartID, unsigned int dwRowCnt, struct _worlddb_guild_battle_info* pkInfo);
        char LoadGuildBattleScheduleInfo(unsigned int uiStartListID, unsigned int uiScheduleUnitCnt, struct _worlddb_guild_battle_schedule_list* pkInfo);
        bool Rebirth_Base(unsigned int dwCharacterSerial, char* pwszName);
        bool Regist_UnmannedTraderSingleItem(unsigned int dwRegSerial, struct _unmannedtrader_registsingleitem* kInfo, bool bInsertRecord);
        bool SelectAllGuildSerial(unsigned int* pdwCount, unsigned int* pdwSerial);
        bool SelectAllGuildSerialGrade(unsigned int* pdwCount, unsigned int* pdwSerial, char* pbyGrade);
        bool SelectGuildBattleRankList(char byRace, struct _worlddb_guild_battle_rank_list* pkInfo);
        bool SelectGuildBattleRankRecord(unsigned int dwGuildSerial);
        bool SelectGuildBattleRerservedList(unsigned int uiStartSLID, unsigned int uiEndSLID, struct _worlddb_guild_battle_reserved_schedule_info* pkInfo);
        char SelectGuildBattleScheduleInfoID(unsigned int dwID);
        int SelectRowCountGuildBattleInfo();
        int SelectRowCountGuildBattleScheduleInfo();
        bool Select_AccountByAvatorName(char* pwszAvatorName, char* szAccount);
        bool Select_AccountItemCharge(unsigned int dwAccountSerial, char* pbyType, long double* pdMoney, unsigned int* pdwItemCode_K, uint64_t* pdwItemCode_D, unsigned int* pdwItemCode_U, char* pbyRace, unsigned int* pdwDBID, int* piTime);
        bool Select_AccountItemCharge_Extend(unsigned int dwAccountSerial, char* pbyType, unsigned int* pdwItemCode_K, uint64_t* pdwItemCode_D, unsigned int* pdwItemCode_U, char* pbyRace, unsigned int* pdwDBID, int* piTime);
        bool Select_AccountSerial(char* pwszCharacterName, char* szAccount, unsigned int* pSerial);
        char Select_AccountTrunk(unsigned int dwAccountSerial, char byRace, struct _worlddb_trunk_info* pTrunkData);
        char Select_AccountTrunkExtend(unsigned int dwAccountSerial, struct _worlddb_trunk_info* pTrunkData);
        bool Select_AllGuildData(struct _worlddb_guild_info* pGuildInfo);
        uint16_t Select_AllGuildNum();
        char Select_AnimusData(unsigned int dwSerial, char byRace, long double* pAnimusData);
        bool Select_ArrangeInfo(unsigned int dwSerial);
        char Select_BattleResultLogLatest(unsigned int* pkLogSerial);
        bool Select_BattleTournamentInfo(struct TournamentWinner* pWinnerInfo, int nMax);
        char Select_BossCryMsg(unsigned int dwSerial, struct _worlddb_crymsg_info* pCryMsg);
        char Select_Buddy(unsigned int dwSerial, struct _worlddb_buddy_info* pBuddyData);
        int Select_CashLimSale(struct _worlddb_cash_limited_sale* pcashlimitedsale);
        char Select_CharNumInWorld(unsigned int dwAccountSerial, char* byCharNum);
        char Select_CharacterBaseInfo(unsigned int dwCharacterSerial, struct _worlddb_character_base_info* pCharacterData);
        char Select_CharacterBaseInfoByName(char* pwszCharacterName, struct _worlddb_character_base_info* pCharacterData);
        char Select_CharacterBaseInfoBySerial(unsigned int dwAccountSerial, struct _worlddb_character_base_info_array* pCharacterDataArray);
        char Select_CharacterGeneralInfo(unsigned int dwCharacterSerial, struct _worlddb_character_general_info* pCharacterData);
        bool Select_CharacterName(unsigned int dwSerial, char* pwszCharacterName, char* szAccount);
        bool Select_CharacterReName(char* pwszName, unsigned int* pSerial);
        bool Select_CharacterSerial(char* pwszCharacterName, unsigned int* pSerial);
        bool Select_CharactersInfo(unsigned int dwAccountSerial, struct _worlddb_character_array_info* pCharacterData);
        char Select_CheckGreetRecord(int nUseType);
        bool Select_CheckSumValue(unsigned int dwSerial, unsigned int* pdwCheckSum);
        bool Select_ChracterSerialRace(char* pwszCharacterName, unsigned int* pSerial, char* pbyRace);
        int Select_ClearHonorGuild(char byRace, unsigned int* dwSerial);
        char Select_Economy_History(struct _worlddb_economy_history_info_array* pEconomyData, unsigned int dwDate);
        bool Select_Equal_DeleteName_NoArranged(char* pwszCharacterName);
        bool Select_Equal_Name(char* pwszCharacterName);
        char Select_Exist_Economy(unsigned int dwDate, struct _worlddb_economy_history_info_array::_worlddb_economy_history_info* pEconomyData);
        int Select_FailBattleCount(char byRace, unsigned int dwSerial, unsigned int* dwCount);
        int Select_GetCharSerialByNameRace(char* pwszName, char byRace, unsigned int* pSerial);
        int Select_GodenBoxItem(struct _worlddb_golden_box_item* goldenboxitem, int* pnSerial);
        bool Select_GuildBattleRecord(unsigned int dwSerial, struct _guildbattle_totalrecord* pkInfo);
        bool Select_GuildData(unsigned int dwGuildSerial, struct _worlddb_guild_info::__guild_info* pGuildData);
        char Select_GuildMasterLastConn(unsigned int dwSerial, unsigned int dwLimitConnTime, unsigned int* pdwLastConnTime);
        bool Select_GuildMemberData(uint16_t wMaxMember, unsigned int dwGuildSerial, struct _worlddb_guild_member_info* pGuildMemberInfo);
        bool Select_GuildMoneyIOData(unsigned int dwGuildSerial, struct _worlddb_guild_money_io_info* pGuildIOData);
        bool Select_GuildRoomInfo(struct _guildroom_info* pInfo);
        bool Select_GuildSerial(char* pwszGuildName, unsigned int* pdwGuildSerial);
        int Select_HonorGuild(char byRace, struct _guild_honor_list_result_zocl* pOutList, bool bNext);
        char Select_Inven(unsigned int dwCharacterSerial, uint16_t wBagCount, struct _worlddb_inven_info* pInvenData);
        int Select_IsValidChar(unsigned int dwSerial, unsigned int* dwDbSerial);
        bool Select_ItemCharge(unsigned int dwAvatorSerial, char* pbyType, unsigned int* pDwItemCode_K, uint64_t* pDwItemCode_D, unsigned int* pDwItemCode_U, unsigned int* pDwItemChargeIndex, int* piTime);
        char Select_ItemCombineEx(unsigned int dwSerial, struct _worlddb_itemcombineex_info* pdbItemCombineExInfo);
        char Select_LimitInfo(char* pData, uint64_t tDataSize);
        char Select_LimitItemEmptyRecord(unsigned int* pdwSerial);
        char Select_LimitItemUsedRecord(char byType, unsigned int dwTypeSerial, unsigned int dwStoreInx, unsigned int* pdwSerial);
        char Select_Limit_Run_Record();
        int Select_LoseBattleCount(char byRace, unsigned int dwSerial, unsigned int* dwCount);
        char Select_MacroData(unsigned int dwSerial, struct _AIOC_A_MACRODATA* pMacro);
        char Select_NotArrangeCharacter(unsigned int dwAccountSerial, struct _worlddb_arrange_char_info* pCharData);
        char Select_NpcData(unsigned int dwSerial, unsigned int* pNpcData);
        char Select_NpcQuest_History(unsigned int dwSerial, struct _worlddb_npc_quest_complete_history* pNpcQHis);
        int Select_OldVerPatriarchGroup(char byRace, struct _candidate_info* p);
        int Select_OreCutting(unsigned int dwSerial, struct _worlddb_ore_cutting* pOreCutting);
        char Select_PatriarchCandidate(unsigned int dwSerial, char byRace, struct _candidate_info* p);
        int Select_PatriarchComm(unsigned int dwSerial, struct _patriarch_comm_list* pOutList);
        int Select_PatriarchCommCount(unsigned int dwSerial, char* pszDate, unsigned int* dwCnt);
        int Select_PatriarchElectState(struct _sel_patriarch_elect_state* pSheet);
        char Select_PatriarchGroup(char byRace, struct _candidate_info* p);
        int Select_PatriarchRefundCount(char byRaceCode, unsigned int dwAvatorSerial, unsigned int* dwCnt);
        int Select_PatriarchVoted(char byRace, unsigned int dwSerial, bool* bOverlapVote);
        int Select_PatriarchWinCnt(char byRace, unsigned int dwAvatorSerial, unsigned int* dwWinCnt);
        int Select_PcBangFavorItem(unsigned int dwSerial, struct _worlddb_pcbang_favor_item* pPcBangFavorItem);
        int Select_PlayerTimeLimitInfo(unsigned int dwAccountSerial, struct _worlddb_time_limit_info* pTimeLimiInfo);
        int Select_PlayerTimeLimitInfo(unsigned int dwAccountSerial, unsigned int* pdwFatigue, char* pbyStatus);
        int Select_Player_Last_LogoutTime(unsigned int dwAccSerial, unsigned int* pdwLastLogoutTime);
        char Select_PostContent(unsigned int dwPostSerial, char* wszContent, int nSize);
        char Select_PostRecvSerialFromName(char* wszRecvName, unsigned int* pdwOutSerial, unsigned int* pdwAccSerial, unsigned int* pdwRace);
        int Select_PostRecvStorageCheck(unsigned int dwSerial);
        char Select_PostRegistryData(unsigned int dwMax, struct CPostData* pPostData);
        int Select_PostStorageEmptyRecord();
        bool Select_PostStorageEmptyRecordSerial(unsigned int* pdwStorageSerial);
        char Select_PostStorageList(unsigned int dwOwner, struct _post_storage_list* pListData);
        bool Select_PostStorageRecordCheck();
        int Select_PotionDelay(unsigned int dwSerial, struct _worlddb_potion_delay_info* pPotionDelayInfo);
        int Select_PrimiumPlayTime(unsigned int dwAccSerial, struct _PCBANG_PLAY_TIME* kInfo);
        int Select_Punishment(unsigned int dwSerial, unsigned int* dwESerial, unsigned int* dwValue);
        int Select_PunishmentCount(char byType, unsigned int dwAvatorSerial, unsigned int* pdwCnt);
        int Select_PvpOrderViewInfo(unsigned int dwSerial, struct _pvporderview_info* kInfo);
        char Select_PvpPointGuildRank(char* szDate, struct _pvppoint_guild_rank_info* pkInfo);
        char Select_PvpPointLimitInfo(unsigned int dwSerial, struct _pvppointlimit_info* kInfo);
        char Select_PvpRankInfo(char byRace, char* szDate, struct _PVP_RANK_DATA* rankData);
        char Select_PvpRate(unsigned int dwSerial, char* szDate, unsigned int* pdwRank, uint16_t* pwRankRate);
        char Select_Quest(unsigned int dwSerial, struct _worlddb_quest_array* questData);
        int Select_RFEvent_ClassRefine(unsigned int dwAvatorSerial, char* byRefinedCnt, unsigned int* dwRefineDate);
        char Select_RaceBossAccumulationWinRate(char byRace, unsigned int dwBossSerial, struct _raceboss_acc_winrate* windata);
        char Select_RaceBossCurrentWinRate(char byRace, char* szDate, unsigned int* dwTotalCnt, unsigned int* dwWinCnt);
        char Select_RegeAvator_For_Lobby_Logout(unsigned int dwAccountSerial, struct _rege_char_data* pRegeCharData);
        char Select_ReturnPost(unsigned int dwOwner, unsigned int dwMax, struct _return_post_list* pRetData);
        char Select_SFDelayInfo(unsigned int dwSerial, struct _worlddb_sf_delay_info* pSFDelay);
        char Select_Start_NpcQuest_History(unsigned int dwSerial, struct _worlddb_start_npc_quest_complete_history* pNpcQHis, unsigned int dwCount);
        char Select_Start_NpcQuest_History_Count(unsigned int dwSerial, unsigned int* pdwCount);
        char Select_StoreLimitItem(struct _qry_case_all_store_limit_item* pData);
        int Select_Supplement(unsigned int dwSerial, struct _worlddb_character_supplement_info* pSupplement);
        int Select_Supplement_ActPoint(unsigned int dwSerial, struct _worlddb_character_supplement_info* pSupplement);
        int Select_Supplement_Ex(unsigned int dwSerial, struct _worlddb_character_supplement_info* pSupplement);
        char Select_TakeItem(unsigned int dwAvatorSerial, struct _worlddb_item_list* itemList);
        char Select_TotalGuildRank(char* szDate, struct _total_guild_rank_info* pkInfo);
        char Select_TotalRecordNum(unsigned int* pdwTotalNum);
        char Select_Trade(char byType, unsigned int dwSerial, char byRace, struct _worlddb_trade_info* pTradeData);
        bool Select_TrunkMoney(unsigned int dwSerial, long double* pVal);
        char Select_Unit(unsigned int dwCharacterSerial, struct _worlddb_unit_info_array* pUnitInfo);
        char Select_UnitData(unsigned int dwSerial, long double* pUnitData);
        char Select_UnmannedTraderBuySingleItemInfo(char byType, unsigned int dwRegistSerial, struct _unmannedtrader_buy_item_info* kData);
        char Select_UnmannedTraderItemRecordCntByState(char byType, char byState, unsigned int* pdwSerial, uint16_t wMaxCnt, uint16_t* pwRecordCnt);
        char Select_UnmannedTraderItemState(char byType, unsigned int dwRegistSerial, char* byState);
        int Select_UnmannedTraderItemStateInfo(struct _unmannedtrader_stade_id_info* pkInfo, unsigned int dwMaxCnt);
        int Select_UnmannedTraderItemStateInfoCnt(unsigned int* pdwCnt);
        char Select_UnmannedTraderRegister(char byType, unsigned int dwRegistSerial, unsigned int* pdwRegister);
        char Select_UnmannedTraderReservedSchedule(unsigned int dwMaxCnt, struct _unmannedtrader_reserved_schedule_info* pkInfo);
        char Select_UnmannedTraderSearchGroupTotalRowCount(char byType, char byRace, char byClass1, char byClass2, char byClass3, unsigned int* dwCount);
        char Select_UnmannedTraderSearchPageInfo(char byType, char byRace, char byClass1, char byClass2, char byClass3, unsigned int dwMaxRowCount, unsigned int dwExcludeRowCount, char* szSortQuery, struct _unmannedtrader_page_info* pkInfo);
        char Select_UnmannedTraderSellInfo(char byType, unsigned int dwRegistSerial, char byRace, struct _unmannedtrader_seller_info* kData);
        bool Select_UnmannedTraderSingleItemBottomSerial(unsigned int* dwSerial);
        int Select_UnmannedTraderSingleItemEmptyRecordCnt();
        int Select_UnmannedTraderSingleItemEmptyRecordSerial(unsigned int* dwSerial);
        char Select_UsedLimitItemRecordNum(unsigned int* pdwUsedNum);
        bool Select_UserCountInfo(char* szStartDate, char* szEndDate, struct _worlddb_user_count_info* pUserCountData);
        char Select_UserInterface(unsigned int dwSerial, struct _worlddb_userinterface_info* pUserinterfaceInfo);
        char Select_WaitItem(unsigned int dwAvatorSerial, struct _worlddb_item_list* itemList);
        char Select_WeeklyGuildRankOwnerGuild(char* szDate, char byRace, char byLimitCnt, struct _weeklyguildrank_owner_info* pkInfo);
        int Select_WinBattleCount(char byRace, unsigned int dwSerial, unsigned int* dwCount);
        char Select_utSellWaitItems_SalesTotals(char byType, unsigned int dwOwnor, unsigned int* pSalesTotals);
        bool Truncate_UnmannedTraderItemStateRecord();
        bool UpdateClearGuildBattleInfo(unsigned int dwStartID, unsigned int dwEndID);
        bool UpdateClearGuildBattleRank();
        bool UpdateClearGuildBattleScheduleInfo(unsigned int uiStartListID, unsigned int uiEndListID);
        bool UpdateClearGuildBattleScheduleInfo(unsigned int dwID);
        bool UpdateDrawGuildBattleResult(unsigned int dwGuildSerial, unsigned int dwScore);
        bool UpdateGuildBattleInfo(unsigned int dwID, unsigned int dwP1GuildSerial, unsigned int dwP2GuildSerial, unsigned int dwMapID, char byNumber);
        bool UpdateGuildBattleScheduleInfo(unsigned int dwID, unsigned int dwSLID, char byState, int64_t tStartTime, uint16_t wTurmMin);
        bool UpdateGuildMoney(unsigned int dwSerial, long double dDalant, long double dGold);
        bool UpdateLoseGuildBattleResult(unsigned int dwGuildSerial, unsigned int dwScore);
        bool UpdateServerResetToken(unsigned int dwToken, uint16_t wProcType, unsigned int dwESerial);
        bool UpdateVotedReset_Cheat(unsigned int dwSerial);
        bool UpdateVotedReset_General(unsigned int dwSerial);
        bool UpdateVotedReset_Supplement(unsigned int dwSerial);
        bool UpdateWinGuildBattleResult(unsigned int dwGuildSerial, unsigned int dwScore);
        bool Update_AnimusData(unsigned int dwSerial, char byRace, long double* pAnimusData);
        bool Update_BattleResultLogBattleResultAndPvpPoint(unsigned int kLogSerial, unsigned int dwRedSerial, unsigned int dwBlueSerial);
        bool Update_CharSlot(unsigned int dwAvatorSerial);
        bool Update_CharacterData(unsigned int dwSerial, struct _worlddb_update_char_query* pUpdateQuery);
        bool Update_CharacterReName(char* pwszName, unsigned int dwSerial);
        bool Update_ClearWeeklyPvpPointSum();
        bool Update_CristalBattleCharInfo(unsigned int dwSerial, char byHSKTime, char byPvpGrade, int iPvpPoint, uint16_t wKillPoint, uint16_t wDiePoint);
        bool Update_Dalant(unsigned int dwSerial, unsigned int dwDalant);
        bool Update_DisableInstanceStore(unsigned int dwSerial);
        bool Update_DisappearOwnerRecord();
        bool Update_GmGreet(struct _qry_case_gm_greetingmsg* pSheet);
        bool Update_Gold(unsigned int dwSerial, unsigned int dwGold);
        bool Update_GuildEmblem(unsigned int dwGuildSerial, long double dCurDalant, unsigned int dwEmblemBack, unsigned int dwEmblemMark);
        bool Update_GuildGrade();
        bool Update_GuildGreet(struct _qry_case_guild_greetingmsg* pSheet);
        bool Update_GuildMaster(unsigned int dwGuild_Serial, unsigned int dwGuildMaster_Serial, char byGuildMaster_PrevGrade);
        bool Update_GuildMemberCount(unsigned int dwGuildSerial, uint16_t wMemberNum);
        bool Update_GuildRank(char* szDate);
        bool Update_GuildRank_Step1(char* szDate);
        bool Update_GuildRank_Step2(char* szDate);
        bool Update_GuildRank_Step3(char* szDate);
        bool Update_GuildRoom(unsigned int dwGuildSerial);
        bool Update_IncreaseWeeklyGuildGuildBattlePvpPointSum(unsigned int dwSerial, long double dPvpPoint);
        bool Update_IncreaseWeeklyGuildKillPvpPointSum(unsigned int dwSerial, long double dPvpPoint);
        bool Update_InputGuildMoney(unsigned int dwGuildSerial, unsigned int dwDalant, unsigned int dwGold);
        bool Update_Level(unsigned int dwSerial, char byLv);
        bool Update_LimitItemNum(char* pszQuery);
        bool Update_MacroData(unsigned int dwSerial, struct _AIOC_A_MACRODATA* pMacro);
        bool Update_NpcData(unsigned int dwSerial, unsigned int* pNpcData);
        bool Update_OutputGuildMoney(unsigned int dwGuildSerial, unsigned int dwDalant, unsigned int dwGold);
        char Update_PatriarchComm(unsigned int dwSerial, unsigned int dwDalant, char* pszDepDate);
        bool Update_Player_TimeLimit_Info(unsigned int dwAccSerial, unsigned int dwFatigue, char wStatus);
        bool Update_Player_Vote_Info(unsigned int dwSerial, unsigned int dwAccPlayTime, char IsVote, char VoteEnable, unsigned int dwScanerData);
        bool Update_Post(char* szPostQuery);
        bool Update_PostRegistry(unsigned int dwIndex, unsigned int dwSenderSerial, char* wszSendName, char* wszRecvName, char* wszTitle, char* wszContent, int nK, uint64_t dwD, unsigned int dwU, unsigned int dwGold, char bySendRace, char bySenderDgr, uint64_t lnUID);
        bool Update_PostRegistryDisable(unsigned int dwIndex);
        bool Update_PostStorageSendToRecver(unsigned int dwOwner, unsigned int dwPostSerial, char byPostState, char* wszSendName, char* wszRecvName, char* wszTitle, char* wszContent, int nK, uint64_t dwD, unsigned int dwU, unsigned int dwGold, char byErr, uint16_t wStorageIndex, char* pbyNumber, bool bGetNumber, uint64_t lnUID);
        bool Update_Punishment(char* szData);
        bool Update_PvpPointGuildRankRecord(char* szDate, unsigned int dwSerial, uint16_t wRank);
        bool Update_PvpPointGuildRankSumLv(char* szDate, char byRace, char byLimitCnt, char byLimitGrade);
        bool Update_PvpPointInfo(unsigned int dwSerial, int16_t* zClass, long double dPvpPoint);
        bool Update_RFEvent_ClassRefine(unsigned int dwAvatorSerial, char byRefineCnt, unsigned int dwRefineDate);
        bool Update_RaceGreet(struct _qry_case_race_greetingmsg* pSheet);
        bool Update_RaceRank(char* szDate);
        bool Update_RaceRank_Step1(char* szDate);
        bool Update_RaceRank_Step2(char* szDate);
        bool Update_RaceRank_Step3(char* szDate);
        bool Update_RaceRank_Step4(char* szDate);
        bool Update_RaceRank_Step5(char* szDate);
        bool Update_RaceRank_Step6(char* szDate);
        bool Update_RaceRank_Step7(char* szDate);
        bool Update_RaceRank_Step8(char* szDate);
        bool Update_RaceRank_Step9(char* szDate);
        bool Update_RaceRank_Step_6_1(char* szDate);
        bool Update_RaceRank_Step_6_2(char* szDate);
        bool Update_RaceRank_Step_6_3(char* szDate);
        bool Update_RankInGuild(unsigned int dwGuildSerial, struct _worlddb_rankinguild_info* pGuildMemberRankData);
        char Update_RankInGuild_Step1(unsigned int dwGuildSerial);
        bool Update_RankInGuild_Step2(unsigned int dwGuildSerial);
        bool Update_RankInGuild_Step3(unsigned int dwGuildSerial);
        bool Update_RankInGuild_Step4(unsigned int dwGuildSerial);
        bool Update_RankInGuild_Step5(unsigned int dwGuildSerial, struct _worlddb_rankinguild_info* pGuildMemberRankData);
        bool Update_RankInGuild_Step6();
        bool Update_RankInGuild_Step7();
        bool Update_RankInGuild_Step8();
        bool Update_RankInGuild_Step9();
        bool Update_SFDelayInfo(unsigned int dwSerial, struct _worlddb_sf_delay_info* pSFDelay);
        bool Update_SetActive(unsigned int dwSerial, char* pwszActiveName, char bySlot);
        bool Update_SetGuildMoney(unsigned int dwGuildSerial, long double dDalant, long double dGold);
        bool Update_Set_Limit_Run(char* pData, int iSize);
        bool Update_Start_NpcQuest_History(unsigned int dwSerial, char* szQuestCode, char byLevel, char* szTime, int64_t nEndTime);
        bool Update_UnitData(unsigned int dwSerial, long double* pUnitData);
        bool Update_UnmannedTraderCheatUpdateRegistDate(char byType, unsigned int dwRegistSerial);
        bool Update_UnmannedTraderClearDanglingOwnerRecord();
        bool Update_UnmannedTraderItemState(char byType, unsigned int dwItemSerial, char byState, struct _SYSTEMTIME* kCurTime);
        char Update_UnmannedTraderReRegist(char byType, unsigned int dwRegistSerial, char byState, unsigned int dwPrice, unsigned int dwTax, struct _SYSTEMTIME* kCurTime);
        bool Update_UnmannedTraderResutlInfo(char byType, unsigned int dwRegistSerial, char byState, unsigned int dwBuyer, unsigned int dwTax, struct _SYSTEMTIME* kCurTime);
        bool Update_UnmannedTraderSellInfo(unsigned int dwRegSerial, struct _unmannedtrader_registsingleitem* kInfo, struct _SYSTEMTIME* kCurTime);
        bool Update_UnmannedTraderSellInfoPrice(char byType, unsigned int dwRegistSerial, unsigned int dwOwner, unsigned int dwNewPrice);
        bool Update_UnmannedTraderSingleItemInfo(unsigned int dwRegSerial, struct _unmannedtrader_registsingleitem* kInfo);
        bool Update_UnmannedTraderSingleTypeClearUseCompleteRecord(unsigned int dwSerial, struct _SYSTEMTIME* pkCurTime);
        bool Update_UserGuildData(unsigned int dwAvatorSerial, unsigned int dwGuildSerial, char byGrade);
        int Updatet_Account_Vote_Available(unsigned int dwSerial, char* byVoteEnable);
        bool create_amine_personal();
        bool create_automine_table();
        bool create_sumtotal_dungeon(int nRecodeNum, char** ppKey);
        bool create_table_atrade_taxrate();
        int exist_aminpersonal_inven(unsigned int dwSerial);
        int exist_automine(char byCollisionType, char byRace);
        bool insert_amine_newowner(char byType, char byRace, unsigned int dwSerial);
        bool insert_amine_personal(unsigned int dwSerial);
        bool insert_atrade_taxrate(char byRace, unsigned int dwSerial, char* wszName, unsigned int dwMatterDst, char* wszMatterDst, char byCurrTax, unsigned int dwNext);
        int select_amine_personal(unsigned int dwSerial);
        int select_amine_personal(unsigned int dwSerial, struct _personal_amine_inven* pInven);
        int select_atrade_taxrate(char byRace, char* pwszName, char* byCurrTax, char* byNextTax);
        int select_automine(struct _DB_LOAD_AUTOMINE_MACHINE* pdata);
        bool update_amine_battery(char byType, char byRace, unsigned int dwSerial, unsigned int dwBattery);
        bool update_amine_dck(char byType, char byRace, unsigned int dwSerial);
        bool update_amine_mineore(char byType, char byRace, unsigned int dwSerial, char bySlot, unsigned int dwK, char byNum, unsigned int dwGage);
        bool update_amine_moveore(char byType, char byRace, unsigned int dwSerial, char bySSlot, unsigned int dwSK, char bySNum, char byDSlot, unsigned int dwDK, char byDNum);
        bool update_amine_personal(char* pQry);
        bool update_amine_selore(char byType, char byRace, unsigned int dwSerial, char byOreIdx);
        bool update_amine_workstate(char byType, char byRace, unsigned int dwSerial, bool bWorking);
        bool update_cristalbattle_date(unsigned int dwCharSerial, char bHSKTime);
        ~CRFWorldDatabase();
        void dtor_CRFWorldDatabase();
    };
END_ATF_NAMESPACE

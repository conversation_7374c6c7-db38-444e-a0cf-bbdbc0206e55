// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagSCROLLBARINFO
    {
        unsigned int cbSize;
        tagRECT rcScrollBar;
        int dxyLineButton;
        int xyThumbTop;
        int xyThumbBottom;
        int reserved;
        unsigned int rgstate[6];
    };
END_ATF_NAMESPACE

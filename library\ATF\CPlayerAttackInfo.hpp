// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayerAttack.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPlayerAttackAttackSkill2_ptr = void (WINAPIV*)(struct CPlayerAttack*, struct _attack_param*, bool);
        using CPlayerAttackAttackSkill2_clbk = void (WINAPIV*)(struct CPlayerAttack*, struct _attack_param*, bool, CPlayerAttackAttackSkill2_ptr);
        using CPlayerAttackAttackUnit4_ptr = void (WINAPIV*)(struct CPlayerAttack*, struct _attack_param*);
        using CPlayerAttackAttackUnit4_clbk = void (WINAPIV*)(struct CPlayerAttack*, struct _attack_param*, CPlayerAttackAttackUnit4_ptr);
        
        using CPlayerAttackctor_CPlayerAttack6_ptr = void (WINAPIV*)(struct CPlayerAttack*, struct CCharacter*);
        using CPlayerAttackctor_CPlayerAttack6_clbk = void (WINAPIV*)(struct CPlayerAttack*, struct CCharacter*, CPlayerAttackctor_CPlayerAttack6_ptr);
        using CPlayerAttackWPActiveAttackForce8_ptr = void (WINAPIV*)(struct CPlayerAttack*, struct _attack_param*);
        using CPlayerAttackWPActiveAttackForce8_clbk = void (WINAPIV*)(struct CPlayerAttack*, struct _attack_param*, CPlayerAttackWPActiveAttackForce8_ptr);
        using CPlayerAttackWPActiveAttackSkill10_ptr = void (WINAPIV*)(struct CPlayerAttack*, struct _attack_param*);
        using CPlayerAttackWPActiveAttackSkill10_clbk = void (WINAPIV*)(struct CPlayerAttack*, struct _attack_param*, CPlayerAttackWPActiveAttackSkill10_ptr);
        using CPlayerAttack_CalcSkillAttPnt12_ptr = int (WINAPIV*)(struct CPlayerAttack*, bool);
        using CPlayerAttack_CalcSkillAttPnt12_clbk = int (WINAPIV*)(struct CPlayerAttack*, bool, CPlayerAttack_CalcSkillAttPnt12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

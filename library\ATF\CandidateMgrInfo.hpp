// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CandidateMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CandidateMgrAddScore2_ptr = void (WINAPIV*)(struct CandidateMgr*, char, char*, char);
        using CandidateMgrAddScore2_clbk = void (WINAPIV*)(struct CandidateMgr*, char, char*, char, CandidateMgrAddScore2_ptr);
        using CandidateMgrApplyPatriarchGroup4_ptr = void (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrApplyPatriarchGroup4_clbk = void (WINAPIV*)(struct CandidateMgr*, CandidateMgrApplyPatriarchGroup4_ptr);
        using CandidateMgrAppointPatriarchGroup6_ptr = bool (WINAPIV*)(struct CandidateMgr*, struct CPlayer*, _candidate_info::ClassType);
        using CandidateMgrAppointPatriarchGroup6_clbk = bool (WINAPIV*)(struct CandidateMgr*, struct CPlayer*, _candidate_info::ClassType, CandidateMgrAppointPatriarchGroup6_ptr);
        
        using CandidateMgrctor_CandidateMgr8_ptr = void (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrctor_CandidateMgr8_clbk = void (WINAPIV*)(struct CandidateMgr*, CandidateMgrctor_CandidateMgr8_ptr);
        using CandidateMgrChangeState_1to210_ptr = void (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrChangeState_1to210_clbk = void (WINAPIV*)(struct CandidateMgr*, CandidateMgrChangeState_1to210_ptr);
        using CandidateMgrCheckDBValidCharacter12_ptr = int (WINAPIV*)(struct CandidateMgr*, char);
        using CandidateMgrCheckDBValidCharacter12_clbk = int (WINAPIV*)(struct CandidateMgr*, char, CandidateMgrCheckDBValidCharacter12_ptr);
        using CandidateMgrCompleteInsertCandidate14_ptr = void (WINAPIV*)(struct CandidateMgr*, char, char*);
        using CandidateMgrCompleteInsertCandidate14_clbk = void (WINAPIV*)(struct CandidateMgr*, char, char*, CandidateMgrCompleteInsertCandidate14_ptr);
        using CandidateMgrDischargePatriarchGroup16_ptr = bool (WINAPIV*)(struct CandidateMgr*, char, _candidate_info::ClassType);
        using CandidateMgrDischargePatriarchGroup16_clbk = bool (WINAPIV*)(struct CandidateMgr*, char, _candidate_info::ClassType, CandidateMgrDischargePatriarchGroup16_ptr);
        using CandidateMgrFinalDecision18_ptr = void (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrFinalDecision18_clbk = void (WINAPIV*)(struct CandidateMgr*, CandidateMgrFinalDecision18_ptr);
        using CandidateMgrGetCandidate20_ptr = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, unsigned int);
        using CandidateMgrGetCandidate20_clbk = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, unsigned int, CandidateMgrGetCandidate20_ptr);
        using CandidateMgrGetCandidateBySerial22_ptr = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, unsigned int);
        using CandidateMgrGetCandidateBySerial22_clbk = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, unsigned int, CandidateMgrGetCandidateBySerial22_ptr);
        using CandidateMgrGetCandidateCnt_1st24_ptr = int (WINAPIV*)(struct CandidateMgr*, char);
        using CandidateMgrGetCandidateCnt_1st24_clbk = int (WINAPIV*)(struct CandidateMgr*, char, CandidateMgrGetCandidateCnt_1st24_ptr);
        using CandidateMgrGetCandidateCnt_2st26_ptr = int (WINAPIV*)(struct CandidateMgr*, char);
        using CandidateMgrGetCandidateCnt_2st26_clbk = int (WINAPIV*)(struct CandidateMgr*, char, CandidateMgrGetCandidateCnt_2st26_ptr);
        using CandidateMgrGetCandidate_2st28_ptr = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, int);
        using CandidateMgrGetCandidate_2st28_clbk = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, int, CandidateMgrGetCandidate_2st28_ptr);
        using CandidateMgrGetEmpty30_ptr = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char);
        using CandidateMgrGetEmpty30_clbk = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, CandidateMgrGetEmpty30_ptr);
        using CandidateMgrGetEmptyPatriarchGroup32_ptr = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char);
        using CandidateMgrGetEmptyPatriarchGroup32_clbk = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, CandidateMgrGetEmptyPatriarchGroup32_ptr);
        using CandidateMgrGetLeader34_ptr = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, int);
        using CandidateMgrGetLeader34_clbk = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, int, CandidateMgrGetLeader34_ptr);
        using CandidateMgrGetMaxNum36_ptr = int (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrGetMaxNum36_clbk = int (WINAPIV*)(struct CandidateMgr*, CandidateMgrGetMaxNum36_ptr);
        using CandidateMgrGetPatriarchGroup38_ptr = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, _candidate_info::ClassType);
        using CandidateMgrGetPatriarchGroup38_clbk = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, _candidate_info::ClassType, CandidateMgrGetPatriarchGroup38_ptr);
        using CandidateMgrGetPatriarchGroupBySerial40_ptr = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, unsigned int);
        using CandidateMgrGetPatriarchGroupBySerial40_clbk = struct _candidate_info* (WINAPIV*)(struct CandidateMgr*, char, unsigned int, CandidateMgrGetPatriarchGroupBySerial40_ptr);
        using CandidateMgrGetWinCnt42_ptr = unsigned int (WINAPIV*)(struct CandidateMgr*, char, unsigned int);
        using CandidateMgrGetWinCnt42_clbk = unsigned int (WINAPIV*)(struct CandidateMgr*, char, unsigned int, CandidateMgrGetWinCnt42_ptr);
        using CandidateMgrInitCandidate44_ptr = void (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrInitCandidate44_clbk = void (WINAPIV*)(struct CandidateMgr*, CandidateMgrInitCandidate44_ptr);
        using CandidateMgrInitialize46_ptr = bool (WINAPIV*)(struct CandidateMgr*, int);
        using CandidateMgrInitialize46_clbk = bool (WINAPIV*)(struct CandidateMgr*, int, CandidateMgrInitialize46_ptr);
        using CandidateMgrInsert_Candidate48_ptr = int (WINAPIV*)(struct CandidateMgr*, char*);
        using CandidateMgrInsert_Candidate48_clbk = int (WINAPIV*)(struct CandidateMgr*, char*, CandidateMgrInsert_Candidate48_ptr);
        using CandidateMgrInstance50_ptr = struct CandidateMgr* (WINAPIV*)();
        using CandidateMgrInstance50_clbk = struct CandidateMgr* (WINAPIV*)(CandidateMgrInstance50_ptr);
        using CandidateMgrIsRegistedAvator_152_ptr = bool (WINAPIV*)(struct CandidateMgr*, char, unsigned int);
        using CandidateMgrIsRegistedAvator_152_clbk = bool (WINAPIV*)(struct CandidateMgr*, char, unsigned int, CandidateMgrIsRegistedAvator_152_ptr);
        using CandidateMgrIsRegistedAvator_254_ptr = bool (WINAPIV*)(struct CandidateMgr*, char, char*);
        using CandidateMgrIsRegistedAvator_254_clbk = bool (WINAPIV*)(struct CandidateMgr*, char, char*, CandidateMgrIsRegistedAvator_254_ptr);
        using CandidateMgrIsRegistedAvator_256_ptr = bool (WINAPIV*)(struct CandidateMgr*, char, unsigned int);
        using CandidateMgrIsRegistedAvator_256_clbk = bool (WINAPIV*)(struct CandidateMgr*, char, unsigned int, CandidateMgrIsRegistedAvator_256_ptr);
        using CandidateMgrLoadDatabase58_ptr = bool (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrLoadDatabase58_clbk = bool (WINAPIV*)(struct CandidateMgr*, CandidateMgrLoadDatabase58_ptr);
        using CandidateMgrLoadLeaderPreVersion60_ptr = bool (WINAPIV*)(struct CandidateMgr*, char);
        using CandidateMgrLoadLeaderPreVersion60_clbk = bool (WINAPIV*)(struct CandidateMgr*, char, CandidateMgrLoadLeaderPreVersion60_ptr);
        using CandidateMgrLoadPatriarchGroup62_ptr = bool (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrLoadPatriarchGroup62_clbk = bool (WINAPIV*)(struct CandidateMgr*, CandidateMgrLoadPatriarchGroup62_ptr);
        using CandidateMgrRegist64_ptr = bool (WINAPIV*)(struct CandidateMgr*, struct CPlayer*);
        using CandidateMgrRegist64_clbk = bool (WINAPIV*)(struct CandidateMgr*, struct CPlayer*, CandidateMgrRegist64_ptr);
        using CandidateMgrRegist66_ptr = bool (WINAPIV*)(struct CandidateMgr*, char, struct _PVP_RANK_DATA*);
        using CandidateMgrRegist66_clbk = bool (WINAPIV*)(struct CandidateMgr*, char, struct _PVP_RANK_DATA*, CandidateMgrRegist66_ptr);
        using CandidateMgrRelease68_ptr = void (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrRelease68_clbk = void (WINAPIV*)(struct CandidateMgr*, CandidateMgrRelease68_ptr);
        using CandidateMgrUpdate_ClassType70_ptr = int (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrUpdate_ClassType70_clbk = int (WINAPIV*)(struct CandidateMgr*, CandidateMgrUpdate_ClassType70_ptr);
        using CandidateMgrUpdate_DischargePatriarch72_ptr = int (WINAPIV*)(struct CandidateMgr*, char*);
        using CandidateMgrUpdate_DischargePatriarch72_clbk = int (WINAPIV*)(struct CandidateMgr*, char*, CandidateMgrUpdate_DischargePatriarch72_ptr);
        using CandidateMgrUpdate_Refund74_ptr = int (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrUpdate_Refund74_clbk = int (WINAPIV*)(struct CandidateMgr*, CandidateMgrUpdate_Refund74_ptr);
        using CandidateMgrUpdate_RegistCandidate_2st76_ptr = int (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrUpdate_RegistCandidate_2st76_clbk = int (WINAPIV*)(struct CandidateMgr*, CandidateMgrUpdate_RegistCandidate_2st76_ptr);
        using CandidateMgrUpdate_Score78_ptr = int (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrUpdate_Score78_clbk = int (WINAPIV*)(struct CandidateMgr*, CandidateMgrUpdate_Score78_ptr);
        using CandidateMgrUpdate_VoteTime80_ptr = int (WINAPIV*)(struct CandidateMgr*, unsigned int);
        using CandidateMgrUpdate_VoteTime80_clbk = int (WINAPIV*)(struct CandidateMgr*, unsigned int, CandidateMgrUpdate_VoteTime80_ptr);
        using CandidateMgr__AddWinner82_ptr = void (WINAPIV*)(struct CandidateMgr*, char, char);
        using CandidateMgr__AddWinner82_clbk = void (WINAPIV*)(struct CandidateMgr*, char, char, CandidateMgr__AddWinner82_ptr);
        using CandidateMgr__SortByPvpPoint84_ptr = int (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgr__SortByPvpPoint84_clbk = int (WINAPIV*)(struct CandidateMgr*, CandidateMgr__SortByPvpPoint84_ptr);
        using CandidateMgr__SortByRank86_ptr = int (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgr__SortByRank86_clbk = int (WINAPIV*)(struct CandidateMgr*, CandidateMgr__SortByRank86_ptr);
        using CandidateMgr__SortByScore88_ptr = int (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgr__SortByScore88_clbk = int (WINAPIV*)(struct CandidateMgr*, CandidateMgr__SortByScore88_ptr);
        
        using CandidateMgrdtor_CandidateMgr90_ptr = void (WINAPIV*)(struct CandidateMgr*);
        using CandidateMgrdtor_CandidateMgr90_clbk = void (WINAPIV*)(struct CandidateMgr*, CandidateMgrdtor_CandidateMgr90_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

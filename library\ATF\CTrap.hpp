// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAttack.hpp>
#include <CCharacter.hpp>
#include <CPlayer.hpp>
#include <_object_id.hpp>
#include <_trap_create_setdata.hpp>


START_ATF_NAMESPACE
    struct  CTrap : CCharacter
    {
        int m_nHP;
        CPlayer *m_pMaster;
        char m_byRaceCode;
        unsigned int m_dwMasterSerial;
        char m_wszMasterName[17];
        char m_aszMasterName[17];
        long double m_dMasterPvPPoint;
        unsigned int m_dwStartMakeTime;
        bool m_bComplete;
        bool m_bBreakTransparBuffer;
        unsigned int m_dwLastDestroyTime;
        int m_nTrapMaxAttackPnt;
    public:
        void Attack(struct CCharacter* pTarget);
        int AttackableHeight();
        CTrap();
        void ctor_CTrap();
        void CheckTranspar();
        bool Create(struct _trap_create_setdata* pData);
        bool Destroy(char byDesType);
        int GetAttackDP();
        float GetAttackRange();
        int GetDefFC(int nAttactPart, struct CCharacter* pAttChar, int* pnConvertPart);
        float GetDefFacing(int nPart);
        float GetDefGap(int nPart);
        int GetDefSkill(bool bBackAttack);
        int GetFireTol();
        int GetGenAttackProb(struct CCharacter* pDst, int nPart, bool bBackAttack);
        int GetHP();
        int GetLevel();
        int GetMaxHP();
        static unsigned int GetNewSerial();
        char* GetObjName();
        int GetObjRace();
        int GetSoilTol();
        int GetWaterTol();
        float GetWeaponAdjust();
        int GetWeaponClass();
        float GetWidth();
        int GetWindTol();
        bool Init(struct _object_id* pID);
        bool IsBeAttackedAble(bool bFirst);
        static bool IsHaveEmpty();
        bool IsInTown();
        void Loop();
        void MasterNetClose(long double dPvPPoint);
        void MasterReStart(struct CPlayer* pMaster);
        void OutOfSec();
        void RecvKillMessage(struct CCharacter* pDier);
        struct CCharacter* SearchNearEnemy();
        void SendMsg_AlterTranspar(bool bTranspar);
        void SendMsg_Attack(struct CAttack* pAt);
        void SendMsg_Create();
        void SendMsg_Destroy(char byDesType);
        void SendMsg_FixPosition(int n);
        void SendMsg_TrapCompleteInform();
        int SetDamage(int nDam, struct CCharacter* pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn);
        ~CTrap();
        void dtor_CTrap();
    };
END_ATF_NAMESPACE

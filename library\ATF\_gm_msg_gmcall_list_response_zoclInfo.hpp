// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_gm_msg_gmcall_list_response_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _gm_msg_gmcall_list_response_zoclInit2_ptr = void (WINAPIV*)(struct _gm_msg_gmcall_list_response_zocl*);
        using _gm_msg_gmcall_list_response_zoclInit2_clbk = void (WINAPIV*)(struct _gm_msg_gmcall_list_response_zocl*, _gm_msg_gmcall_list_response_zoclInit2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DB_LOAD_AUTOMINE_MACHINE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _DB_LOAD_AUTOMINE_MACHINEctor__DB_LOAD_AUTOMINE_MACHINE2_ptr = void (WINAPIV*)(struct _DB_LOAD_AUTOMINE_MACHINE*);
        using _DB_LOAD_AUTOMINE_MACHINEctor__DB_LOAD_AUTOMINE_MACHINE2_clbk = void (WINAPIV*)(struct _DB_LOAD_AUTOMINE_MACHINE*, _DB_LOAD_AUTOMINE_MACHINEctor__DB_LOAD_AUTOMINE_MACHINE2_ptr);
        using _DB_LOAD_AUTOMINE_MACHINEsize4_ptr = int (WINAPIV*)(struct _DB_LOAD_AUTOMINE_MACHINE*);
        using _DB_LOAD_AUTOMINE_MACHINEsize4_clbk = int (WINAPIV*)(struct _DB_LOAD_AUTOMINE_MACHINE*, _DB_LOAD_AUTOMINE_MACHINEsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

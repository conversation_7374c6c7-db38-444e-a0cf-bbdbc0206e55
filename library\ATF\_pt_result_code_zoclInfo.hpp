// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_pt_result_code_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _pt_result_code_zoclctor__pt_result_code_zocl2_ptr = void (WINAPIV*)(struct _pt_result_code_zocl*);
        using _pt_result_code_zoclctor__pt_result_code_zocl2_clbk = void (WINAPIV*)(struct _pt_result_code_zocl*, _pt_result_code_zoclctor__pt_result_code_zocl2_ptr);
        using _pt_result_code_zoclsize4_ptr = int (WINAPIV*)(struct _pt_result_code_zocl*);
        using _pt_result_code_zoclsize4_clbk = int (WINAPIV*)(struct _pt_result_code_zocl*, _pt_result_code_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

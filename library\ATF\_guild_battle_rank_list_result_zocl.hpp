// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _guild_battle_rank_list_result_zocl
    {
        struct  __list
        {
            int nRank;
            char byGrade;
            char wszName[17];
            unsigned int dwWin;
            unsigned int dwLose;
            unsigned int dwDraw;
            unsigned int dwScore;
        };
        unsigned int dwCurVer;
        char byRace;
        char byCurPage;
        char byMaxPage;
        char byExistSelfGuildInfo;
        char byCnt;
        __list list[11];
    public:
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct ScheduleMSG
    {
        char m_strSection[64];
        char m_byKey;
        char m_strValue[64];
        unsigned __int16 m_wIniFileIndex;
    public:
        void Init();
        ScheduleMSG();
        void ctor_ScheduleMSG();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _ItemUpgrade_fld : _base_fld
    {
        char m_strName[64];
        int m_nJewelType;
        float m_fJewelFieldValue;
        int m_nUpperUp;
        int m_nLowerUp;
        int m_nGauntletUp;
        int m_nShoeUp;
        int m_nHelmetUp;
        int m_nShieldUp;
        int m_nMeleeUp;
        int m_nMissileUp;
        int m_nUnitUp;
        int m_nClockUp;
        int m_nDataEffect;
        int m_nEffectUnit;
        float m_fUp[7];
    };
END_ATF_NAMESPACE

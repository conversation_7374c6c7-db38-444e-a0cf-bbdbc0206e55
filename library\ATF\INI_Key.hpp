// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct INI_Key
    {
        struct INI_Section *m_pParentSection;
        char m_str<PERSON><PERSON>[65];
        char m_strValue[65];
    public:
        INI_Key();
        void ctor_INI_Key();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

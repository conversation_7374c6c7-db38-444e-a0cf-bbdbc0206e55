// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _other_shape_change_zocl
    {
        struct  _model
        {
            unsigned __int16 wPartIndex;
            char byLv;
        };
        unsigned __int16 wIndex;
         unsigned int dwSerial;
        unsigned __int16 wEquipVer;
        char byPartIndex;
        _model ModelPerPart;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

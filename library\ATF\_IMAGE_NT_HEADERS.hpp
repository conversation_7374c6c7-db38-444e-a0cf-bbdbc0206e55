// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_IMAGE_FILE_HEADER.hpp>
#include <_IMAGE_OPTIONAL_HEADER.hpp>


START_ATF_NAMESPACE
    struct _IMAGE_NT_HEADERS
    {
        unsigned int Signature;
        _IMAGE_FILE_HEADER FileHeader;
        _IMAGE_OPTIONAL_HEADER OptionalHeader;
    };
END_ATF_NAMESPACE

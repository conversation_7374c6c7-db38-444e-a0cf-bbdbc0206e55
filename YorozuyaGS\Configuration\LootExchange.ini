[LootExchange]
; Enable or disable the loot exchange addon
; 0 = Disabled, 1 = Enabled
Activated=0

; Exchange mode configuration
; 0 = Only exchange items with processing points (original behavior)
; 1 = Exchange items for all available money types
ExchangeAll=0

; Priority order for money types when ExchangeAll=1
; The addon will try to exchange items in this order:
; 0=CP(Dalant), 1=Gold, 2=PvP_Point, 3=PvP_Point_2, 4=Processing_Point, 5=Hunter_Point, 6=Gold_Point
; You can change the priority by reordering these values (comma-separated)
MoneyTypePriority=0,1,2,3,4,5,6

; Minimum item value required for exchange
; Items with value below this threshold will not be exchanged
; Set to 0 to exchange all items regardless of value
MinimumValue=0

; Maximum pickup distance for loot exchange
; Distance in game units (default: 100)
MaxPickupDistance=100

; Enable logging for debugging purposes
; 0 = Disabled, 1 = Enabled
EnableLogging=0

; Log file path (relative to server root)
LogFilePath=./YorozuyaGS/Logs/LootExchange.log

[ItemTypeSettings]
; Configure which item types can be exchanged
; 0 = Disabled, 1 = Enabled for each item type

; Equipment items (armor, weapons, etc.)
ExchangeEquipment=1

; Consumable items (potions, bullets, etc.)
ExchangeConsumables=1

; Resource items (ores, materials, etc.)
ExchangeResources=1

; Special items (maps, town items, etc.)
ExchangeSpecialItems=1

; Force items
ExchangeForceItems=1

; Accessories (rings, amulets, etc.)
ExchangeAccessories=1

[CurrencySettings]
; Enable/disable specific currency types for exchange
; 0 = Disabled, 1 = Enabled

; Main currencies
EnableCP=1
EnableGold=1

; PvP currencies
EnablePvPPoint=1
EnablePvPCashBag=1

; Special point currencies
EnableProcessingPoint=1
EnableHunterPoint=1
EnableGoldPoint=1

[RaceSpecificSettings]
; Race-specific settings for resource items
; 0=Accretia, 1=Bellato, 2=Cora

; Enable race-specific pricing for resources
UseRaceSpecificPricing=1

; Default race to use when race-specific pricing is disabled
; 0=Accretia, 1=Bellato, 2=Cora
DefaultRace=0

[AdvancedSettings]
; Advanced configuration options

; Multiply exchange value by item durability/quantity
; 0 = Use base value only, 1 = Multiply by durability
MultiplyByDurability=1

; Exchange rate modifier (percentage)
; 100 = Normal rate, 150 = 50% bonus, 50 = 50% penalty
ExchangeRateModifier=100

; Enable exchange for premium players only
; 0 = All players, 1 = Premium players only
PremiumPlayersOnly=0

; Minimum player level required to use loot exchange
; 0 = No level requirement
MinimumPlayerLevel=0

; Maximum exchanges per player per day
; 0 = No limit
DailyExchangeLimit=0

; Reset time for daily limits (24-hour format: HH:MM)
DailyResetTime=00:00

[LootExchange]
; Enable or disable the loot exchange addon
; 0 = Disabled, 1 = Enabled
Activated=0

; Exchange mode configuration
; 0 = Only exchange items with processing points (original behavior)
; 1 = Exchange items for all available money types
ExchangeAll=0

; Priority order for money types when ExchangeAll=1
; The addon will try to exchange items in this order:
; 0=CP(Dalant), 1=Gold, 2=PvP_Point, 3=PvP_Point_2, 4=Processing_Point, 5=Hunter_Point, 6=Gold_Point
; You can change the priority by reordering these values (comma-separated)
MoneyTypePriority=0,1,2,3,4,5,6

; Minimum item value required for exchange
; Items with value below this threshold will not be exchanged
; Set to 0 to exchange all items regardless of value
MinimumValue=0

; Maximum pickup distance for loot exchange
; Distance in game units (default: 100)
MaxPickupDistance=100

; Enable logging for debugging purposes
; 0 = Disabled, 1 = Enabled
EnableLogging=0

; Log file path (relative to server root)
LogFilePath=./YorozuyaGS/Logs/LootExchange.log

[ItemTypeSettings]
; Configure which item types can be exchanged
; 0 = Disabled, 1 = Enabled for each item type

; Equipment items (armor, weapons, etc.)
ExchangeEquipment=1

; Consumable items (potions, bullets, etc.)
ExchangeConsumables=1

; Resource items (ores, materials, etc.)
ExchangeResources=1

; Special items (maps, town items, etc.)
ExchangeSpecialItems=1

; Force items
ExchangeForceItems=1

; Accessories (rings, amulets, etc.)
ExchangeAccessories=1

[CurrencySettings]
; Enable/disable specific currency types for exchange
; 0 = Disabled, 1 = Enabled

; Main currencies
EnableCP=1
EnableGold=1

; PvP currencies
EnablePvPPoint=1
EnablePvPCashBag=1

; Special point currencies
EnableProcessingPoint=1
EnableHunterPoint=1
EnableGoldPoint=1

[RaceSpecificSettings]
; Race-specific settings for resource items
; 0=Accretia, 1=Bellato, 2=Cora

; Enable race-specific pricing for resources
UseRaceSpecificPricing=1

; Default race to use when race-specific pricing is disabled
; 0=Accretia, 1=Bellato, 2=Cora
DefaultRace=0

[AdvancedSettings]
; Advanced configuration options

; Multiply exchange value by item durability/quantity
; 0 = Use base value only, 1 = Multiply by durability
MultiplyByDurability=1

; Exchange rate modifier (percentage)
; 100 = Normal rate, 150 = 50% bonus, 50 = 50% penalty
ExchangeRateModifier=100

; Enable exchange for premium players only
; 0 = All players, 1 = Premium players only
PremiumPlayersOnly=0

; Minimum player level required to use loot exchange
; 0 = No level requirement
MinimumPlayerLevel=0

; Maximum exchanges per player per day
; 0 = No limit
DailyExchangeLimit=0

; Reset time for daily limits (24-hour format: HH:MM)
DailyResetTime=00:00

; Use custom item mappings instead of database money types
; 0 = Use database money types (default behavior)
; 1 = Use custom mappings defined below
UseCustomMappings=0

[ItemMappings]
; Custom item-to-currency mappings
; Format: ItemCode=CurrencyType:Value
; ItemCode can be:
;   - Specific item code (e.g., iyyy001)
;   - Table:Index format (e.g., 1:100 for table 1, index 100)
;   - Wildcard patterns (e.g., iyyy* for all items starting with iyyy)
;
; CurrencyType values:
;   0=CP(Dalant), 1=Gold, 2=PvP_Point, 3=PvP_Point_2, 4=Processing_Point, 5=Hunter_Point, 6=Gold_Point
;
; Value: Amount of currency to give (0 = use database value)
;
; Examples:
; iyyy001=0:500          ; Item iyyy001 exchanges for 500 CP
; iyyy002=1:250          ; Item iyyy002 exchanges for 250 Gold
; 1:100=2:1000           ; Table 1, Index 100 exchanges for 1000 PvP Points
; iyyy*=4:0              ; All items starting with iyyy exchange for Processing Points (database value)
; weapon_*=1:0           ; All weapon items exchange for Gold (database value)

[TableMappings]
; Map entire item tables to specific currency types
; Format: TableCode=CurrencyType:ValueMultiplier
; TableCode: Item table number (0-255)
; CurrencyType: 0-6 (see above)
; ValueMultiplier: Multiply database value by this amount (100 = normal, 200 = double, 50 = half)
;
; Examples:
; 1=0:100               ; All weapons (table 1) exchange for CP at normal rate
; 2=1:150               ; All armor (table 2) exchange for Gold at 150% rate
; 15=4:200              ; All resources (table 15) exchange for Processing Points at 200% rate

[CategoryMappings]
; Map item categories to currency types
; Categories are groups of related tables
; Format: CategoryName=CurrencyType:ValueMultiplier:TableList
; TableList: Comma-separated list of table codes
;
; Examples:
Equipment=0:100:1,2,3,4,5,6,7,8        ; All equipment exchanges for CP
Consumables=1:120:9,10                 ; Consumables exchange for Gold at 120% rate
Resources=4:150:15,16,17               ; Resources exchange for Processing Points at 150% rate
Special=5:100:20,21,22                 ; Special items exchange for Hunter Points

[ConditionalMappings]
; Advanced conditional mappings based on item properties
; Format: Condition=CurrencyType:Value
; Conditions can include:
;   - MinLevel=X (minimum item level)
;   - MaxLevel=X (maximum item level)
;   - MinValue=X (minimum database value)
;   - MaxValue=X (maximum database value)
;   - Rarity=X (item rarity level)
;   - PlayerLevel=X (minimum player level)
;   - PlayerRace=X (player race: 0=Accretia, 1=Bellato, 2=Cora)
;
; Examples:
; MinLevel=50&MaxLevel=60=2:2000        ; Items level 50-60 exchange for 2000 PvP Points
; MinValue=10000=1:0                    ; High-value items exchange for Gold
; Rarity=5=6:5000                       ; Rare items exchange for 5000 Gold Points
; PlayerRace=0&MinLevel=40=0:0          ; Accretia players with level 40+ items get CP

[ExclusionList]
; Items that should NEVER be exchanged (blacklist)
; Format: ItemCode or Table:Index
; One item per line
;
; Examples:
; iyyy999                               ; Never exchange item iyyy999
; 1:500                                 ; Never exchange table 1, index 500
; rare_*                                ; Never exchange items starting with rare_

[PriorityOverrides]
; Override the global money type priority for specific items
; Format: ItemCode=Priority1,Priority2,Priority3...
; Use currency type numbers (0-6)
;
; Examples:
; iyyy001=1,0,2                         ; For iyyy001, try Gold first, then CP, then PvP Points
; weapon_*=0,1                          ; For weapons, try CP first, then Gold
; 15:*=4,5,6                            ; For all table 15 items, try Processing, Hunter, Gold Points

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuildBattleController.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CGuildBattleControllerAdd2_ptr = char (WINAPIV*)(struct CGuildBattleController*, struct CGuild*, struct CGuild*, unsigned int, char, unsigned int);
        using CGuildBattleControllerAdd2_clbk = char (WINAPIV*)(struct CGuildBattleController*, struct CGuild*, struct CGuild*, unsigned int, char, unsigned int, CGuildBattleControllerAdd2_ptr);
        using CGuildBattleControllerAdd4_ptr = char (WINAPIV*)(struct CGuildBattleController*, struct CGuild*, struct CGuild*, unsigned int, unsigned int, char, unsigned int);
        using CGuildBattleControllerAdd4_clbk = char (WINAPIV*)(struct CGuildBattleController*, struct CGuild*, struct CGuild*, unsigned int, unsigned int, char, unsigned int, CGuildBattleControllerAdd4_ptr);
        using CGuildBattleControllerAddComplete6_ptr = void (WINAPIV*)(struct CGuildBattleController*, char, unsigned int, unsigned int, unsigned int);
        using CGuildBattleControllerAddComplete6_clbk = void (WINAPIV*)(struct CGuildBattleController*, char, unsigned int, unsigned int, unsigned int, CGuildBattleControllerAddComplete6_ptr);
        using CGuildBattleControllerAddSchedule8_ptr = char (WINAPIV*)(struct CGuildBattleController*, char*);
        using CGuildBattleControllerAddSchedule8_clbk = char (WINAPIV*)(struct CGuildBattleController*, char*, CGuildBattleControllerAddSchedule8_ptr);
        
        using CGuildBattleControllerctor_CGuildBattleController10_ptr = void (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerctor_CGuildBattleController10_clbk = void (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerctor_CGuildBattleController10_ptr);
        using CGuildBattleControllerCheatCreateFieldObject12_ptr = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerCheatCreateFieldObject12_clbk = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerCheatCreateFieldObject12_ptr);
        using CGuildBattleControllerCheatDestroyFieldObject14_ptr = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerCheatDestroyFieldObject14_clbk = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerCheatDestroyFieldObject14_ptr);
        using CGuildBattleControllerCheatDestroyStone16_ptr = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerCheatDestroyStone16_clbk = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerCheatDestroyStone16_ptr);
        using CGuildBattleControllerCheatDropStone18_ptr = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerCheatDropStone18_clbk = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerCheatDropStone18_ptr);
        using CGuildBattleControllerCheatForceTakeStone20_ptr = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerCheatForceTakeStone20_clbk = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerCheatForceTakeStone20_ptr);
        using CGuildBattleControllerCheatGetStone22_ptr = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerCheatGetStone22_clbk = bool (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerCheatGetStone22_ptr);
        using CGuildBattleControllerCheatRegenStone24_ptr = int (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, int);
        using CGuildBattleControllerCheatRegenStone24_clbk = int (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, int, CGuildBattleControllerCheatRegenStone24_ptr);
        using CGuildBattleControllerCheatTakeStone26_ptr = bool (WINAPIV*)(struct CGuildBattleController*, int, struct CPlayer*);
        using CGuildBattleControllerCheatTakeStone26_clbk = bool (WINAPIV*)(struct CGuildBattleController*, int, struct CPlayer*, CGuildBattleControllerCheatTakeStone26_ptr);
        using CGuildBattleControllerCheckGetGravityStone28_ptr = void (WINAPIV*)(struct CGuildBattleController*, uint16_t, unsigned int, struct CPlayer*);
        using CGuildBattleControllerCheckGetGravityStone28_clbk = void (WINAPIV*)(struct CGuildBattleController*, uint16_t, unsigned int, struct CPlayer*, CGuildBattleControllerCheckGetGravityStone28_ptr);
        using CGuildBattleControllerCheckGoal30_ptr = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, int);
        using CGuildBattleControllerCheckGoal30_clbk = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, int, CGuildBattleControllerCheckGoal30_ptr);
        using CGuildBattleControllerCheckTakeGravityStone32_ptr = void (WINAPIV*)(struct CGuildBattleController*, int, struct CPlayer*);
        using CGuildBattleControllerCheckTakeGravityStone32_clbk = void (WINAPIV*)(struct CGuildBattleController*, int, struct CPlayer*, CGuildBattleControllerCheckTakeGravityStone32_ptr);
        using CGuildBattleControllerCleanUp34_ptr = void (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerCleanUp34_clbk = void (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerCleanUp34_ptr);
        using CGuildBattleControllerClear36_ptr = void (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerClear36_clbk = void (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerClear36_ptr);
        using CGuildBattleControllerCompleteClearGuildBattleRank38_ptr = void (WINAPIV*)(struct CGuildBattleController*, char);
        using CGuildBattleControllerCompleteClearGuildBattleRank38_clbk = void (WINAPIV*)(struct CGuildBattleController*, char, CGuildBattleControllerCompleteClearGuildBattleRank38_ptr);
        using CGuildBattleControllerCompleteCreateGuildBattleRankTable40_ptr = void (WINAPIV*)(struct CGuildBattleController*, char);
        using CGuildBattleControllerCompleteCreateGuildBattleRankTable40_clbk = void (WINAPIV*)(struct CGuildBattleController*, char, CGuildBattleControllerCompleteCreateGuildBattleRankTable40_ptr);
        using CGuildBattleControllerCompleteUpdateRank42_ptr = void (WINAPIV*)(struct CGuildBattleController*, char, char, char*);
        using CGuildBattleControllerCompleteUpdateRank42_clbk = void (WINAPIV*)(struct CGuildBattleController*, char, char, char*, CGuildBattleControllerCompleteUpdateRank42_ptr);
        using CGuildBattleControllerCompleteUpdateReservedSchedule44_ptr = void (WINAPIV*)(struct CGuildBattleController*, unsigned int, char*);
        using CGuildBattleControllerCompleteUpdateReservedSchedule44_clbk = void (WINAPIV*)(struct CGuildBattleController*, unsigned int, char*, CGuildBattleControllerCompleteUpdateReservedSchedule44_ptr);
        using CGuildBattleControllerDestroy46_ptr = void (WINAPIV*)();
        using CGuildBattleControllerDestroy46_clbk = void (WINAPIV*)(CGuildBattleControllerDestroy46_ptr);
        using CGuildBattleControllerDropGravityStone48_ptr = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerDropGravityStone48_clbk = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerDropGravityStone48_ptr);
        using CGuildBattleControllerFlip50_ptr = void (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerFlip50_clbk = void (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerFlip50_ptr);
        using CGuildBattleControllerGetCircleZone52_ptr = struct CGameObject* (WINAPIV*)(struct CGuildBattleController*, int);
        using CGuildBattleControllerGetCircleZone52_clbk = struct CGameObject* (WINAPIV*)(struct CGuildBattleController*, int, CGuildBattleControllerGetCircleZone52_ptr);
        using CGuildBattleControllerGetRegener54_ptr = struct CGameObject* (WINAPIV*)(struct CGuildBattleController*, int);
        using CGuildBattleControllerGetRegener54_clbk = struct CGameObject* (WINAPIV*)(struct CGuildBattleController*, int, CGuildBattleControllerGetRegener54_ptr);
        using CGuildBattleControllerGetStone56_ptr = struct CGameObject* (WINAPIV*)(struct CGuildBattleController*, int);
        using CGuildBattleControllerGetStone56_clbk = struct CGameObject* (WINAPIV*)(struct CGuildBattleController*, int, CGuildBattleControllerGetStone56_ptr);
        using CGuildBattleControllerInit58_ptr = bool (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerInit58_clbk = bool (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerInit58_ptr);
        using CGuildBattleControllerInstance60_ptr = struct CGuildBattleController* (WINAPIV*)();
        using CGuildBattleControllerInstance60_clbk = struct CGuildBattleController* (WINAPIV*)(CGuildBattleControllerInstance60_ptr);
        using CGuildBattleControllerIsAvailableSuggest62_ptr = char (WINAPIV*)(struct CGuildBattleController*, struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int);
        using CGuildBattleControllerIsAvailableSuggest62_clbk = char (WINAPIV*)(struct CGuildBattleController*, struct CGuild*, unsigned int, unsigned int, unsigned int, unsigned int, CGuildBattleControllerIsAvailableSuggest62_ptr);
        using CGuildBattleControllerJoin64_ptr = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerJoin64_clbk = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerJoin64_ptr);
        using CGuildBattleControllerJoinGuild66_ptr = void (WINAPIV*)(struct CGuildBattleController*, int, unsigned int, unsigned int);
        using CGuildBattleControllerJoinGuild66_clbk = void (WINAPIV*)(struct CGuildBattleController*, int, unsigned int, unsigned int, CGuildBattleControllerJoinGuild66_ptr);
        using CGuildBattleControllerKill68_ptr = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, struct CPlayer*);
        using CGuildBattleControllerKill68_clbk = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, struct CPlayer*, CGuildBattleControllerKill68_ptr);
        using CGuildBattleControllerLeaveGuild70_ptr = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerLeaveGuild70_clbk = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerLeaveGuild70_ptr);
        using CGuildBattleControllerLoad72_ptr = bool (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerLoad72_clbk = bool (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerLoad72_ptr);
        using CGuildBattleControllerLoadINI74_ptr = bool (WINAPIV*)(struct CGuildBattleController*, unsigned int*, int*, int*, int*, int*);
        using CGuildBattleControllerLoadINI74_clbk = bool (WINAPIV*)(struct CGuildBattleController*, unsigned int*, int*, int*, int*, int*, CGuildBattleControllerLoadINI74_ptr);
        using CGuildBattleControllerLogIn76_ptr = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerLogIn76_clbk = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerLogIn76_ptr);
        using CGuildBattleControllerLoop78_ptr = void (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerLoop78_clbk = void (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerLoop78_ptr);
        using CGuildBattleControllerNetClose80_ptr = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerNetClose80_clbk = void (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerNetClose80_ptr);
        using CGuildBattleControllerPushClearGuildBattleRank82_ptr = void (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerPushClearGuildBattleRank82_clbk = void (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerPushClearGuildBattleRank82_ptr);
        using CGuildBattleControllerPushCreateGuildBattleRankTable84_ptr = void (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerPushCreateGuildBattleRankTable84_clbk = void (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerPushCreateGuildBattleRankTable84_ptr);
        using CGuildBattleControllerSaveINI86_ptr = bool (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerSaveINI86_clbk = bool (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerSaveINI86_ptr);
        using CGuildBattleControllerSendCurrentBattleInfoRequest88_ptr = void (WINAPIV*)(struct CGuildBattleController*, int, unsigned int);
        using CGuildBattleControllerSendCurrentBattleInfoRequest88_clbk = void (WINAPIV*)(struct CGuildBattleController*, int, unsigned int, CGuildBattleControllerSendCurrentBattleInfoRequest88_ptr);
        using CGuildBattleControllerSendPossibleBattleGuildList90_ptr = void (WINAPIV*)(struct CGuildBattleController*, int, char, char, unsigned int);
        using CGuildBattleControllerSendPossibleBattleGuildList90_clbk = void (WINAPIV*)(struct CGuildBattleController*, int, char, char, unsigned int, CGuildBattleControllerSendPossibleBattleGuildList90_ptr);
        using CGuildBattleControllerSendPossibleBattleGuildListFirst92_ptr = void (WINAPIV*)(struct CGuildBattleController*, int, char);
        using CGuildBattleControllerSendPossibleBattleGuildListFirst92_clbk = void (WINAPIV*)(struct CGuildBattleController*, int, char, CGuildBattleControllerSendPossibleBattleGuildListFirst92_ptr);
        using CGuildBattleControllerSendRankList94_ptr = void (WINAPIV*)(struct CGuildBattleController*, int, char, unsigned int, unsigned int, char, unsigned int);
        using CGuildBattleControllerSendRankList94_clbk = void (WINAPIV*)(struct CGuildBattleController*, int, char, unsigned int, unsigned int, char, unsigned int, CGuildBattleControllerSendRankList94_ptr);
        using CGuildBattleControllerSendReservedScheduleList96_ptr = void (WINAPIV*)(struct CGuildBattleController*, int, unsigned int, unsigned int, char, char, unsigned int);
        using CGuildBattleControllerSendReservedScheduleList96_clbk = void (WINAPIV*)(struct CGuildBattleController*, int, unsigned int, unsigned int, char, char, unsigned int, CGuildBattleControllerSendReservedScheduleList96_ptr);
        using CGuildBattleControllerStart98_ptr = char (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*);
        using CGuildBattleControllerStart98_clbk = char (WINAPIV*)(struct CGuildBattleController*, struct CPlayer*, CGuildBattleControllerStart98_ptr);
        using CGuildBattleControllerUpdateClearRerservedDayInfo100_ptr = bool (WINAPIV*)(struct CGuildBattleController*, unsigned int, unsigned int, unsigned int, unsigned int);
        using CGuildBattleControllerUpdateClearRerservedDayInfo100_clbk = bool (WINAPIV*)(struct CGuildBattleController*, unsigned int, unsigned int, unsigned int, unsigned int, CGuildBattleControllerUpdateClearRerservedDayInfo100_ptr);
        using CGuildBattleControllerUpdateDraw102_ptr = bool (WINAPIV*)(struct CGuildBattleController*, char, unsigned int, char, unsigned int);
        using CGuildBattleControllerUpdateDraw102_clbk = bool (WINAPIV*)(struct CGuildBattleController*, char, unsigned int, char, unsigned int, CGuildBattleControllerUpdateDraw102_ptr);
        using CGuildBattleControllerUpdatePossibleBattleGuildList104_ptr = void (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerUpdatePossibleBattleGuildList104_clbk = void (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerUpdatePossibleBattleGuildList104_ptr);
        using CGuildBattleControllerUpdateRank106_ptr = bool (WINAPIV*)(struct CGuildBattleController*, char, char*);
        using CGuildBattleControllerUpdateRank106_clbk = bool (WINAPIV*)(struct CGuildBattleController*, char, char*, CGuildBattleControllerUpdateRank106_ptr);
        using CGuildBattleControllerUpdateReservedGuildBattleSchedule108_ptr = bool (WINAPIV*)(struct CGuildBattleController*, unsigned int, char*);
        using CGuildBattleControllerUpdateReservedGuildBattleSchedule108_clbk = bool (WINAPIV*)(struct CGuildBattleController*, unsigned int, char*, CGuildBattleControllerUpdateReservedGuildBattleSchedule108_ptr);
        using CGuildBattleControllerUpdateWinLose110_ptr = bool (WINAPIV*)(struct CGuildBattleController*, char, unsigned int, char, unsigned int);
        using CGuildBattleControllerUpdateWinLose110_clbk = bool (WINAPIV*)(struct CGuildBattleController*, char, unsigned int, char, unsigned int, CGuildBattleControllerUpdateWinLose110_ptr);
        
        using CGuildBattleControllerdtor_CGuildBattleController114_ptr = void (WINAPIV*)(struct CGuildBattleController*);
        using CGuildBattleControllerdtor_CGuildBattleController114_clbk = void (WINAPIV*)(struct CGuildBattleController*, CGuildBattleControllerdtor_CGuildBattleController114_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

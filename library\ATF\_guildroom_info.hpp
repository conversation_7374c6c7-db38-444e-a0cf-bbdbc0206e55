// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagTIMESTAMP_STRUCT.hpp>


START_ATF_NAMESPACE
    struct _guildroom_info
    {
        struct gr_info
        {
            unsigned int dwGuildSerial;
            char uszGuildName[17];
            char byRoomType;
            char byRace;
            tagTIMESTAMP_STRUCT ts;
        };
        char byCount;
        gr_info info[90];
    };
END_ATF_NAMESPACE

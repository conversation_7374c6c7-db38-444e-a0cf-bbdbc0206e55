// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _attack_siege_request_clzo
    {
        char byID;
         unsigned __int16 wIndex;
         __int16 zAttackPos[2];
        char byAttPart;
        unsigned __int16 wBulletSerial;
        unsigned __int16 wEffBulletSerial;
    };
END_ATF_NAMESPACE

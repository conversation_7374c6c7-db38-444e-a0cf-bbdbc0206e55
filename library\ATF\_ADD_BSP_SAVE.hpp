// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ENTITY_LIST.hpp>
#include <_LEAF_ENTITIES_LIST_INFO.hpp>
#include <_LEAF_SOUND_ENTITIES_LIST_INFO.hpp>
#include <_READ_MAP_ENTITIES_LIST.hpp>
#include <_READ_SOUND_ENTITIES_LIST.hpp>
#include <_READ_SOUND_ENTITY_LIST.hpp>
#include <_TOOL_COL_LEAF.hpp>
#include <_TOOL_COL_LINE.hpp>
#include <_TOOL_COL_POINT.hpp>


START_ATF_NAMESPACE
    struct _ADD_BSP_SAVE
    {
        _TOOL_COL_POINT *ColPoint;
        _TOOL_COL_LINE *ColLine;
        unsigned __int16 *ColLineID;
        _TOOL_COL_LEAF *ColLeaf;
        _ENTITY_LIST *EntityList;
        _LEAF_ENTITIES_LIST_INFO *LeafEntityList;
        _READ_MAP_ENTITIES_LIST *MapEntitiesList;
        unsigned __int16 *EntityID;
        _READ_SOUND_ENTITY_LIST *SoundEntityList;
        _LEAF_SOUND_ENTITIES_LIST_INFO *LeafSoundEntityList;
        _READ_SOUND_ENTITIES_LIST *SoundEntitiesList;
        unsigned __int16 *SoundEntityID;
    };
END_ATF_NAMESPACE

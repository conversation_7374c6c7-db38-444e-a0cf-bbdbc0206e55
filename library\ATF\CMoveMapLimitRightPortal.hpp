// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitRight.hpp>
#include <CPlayer.hpp>
#include <MiningTicket.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CMoveMapLimitRightPortal : CMoveMapLimitRight
    {
        MiningTicket *m_pkRight;
        bool m_bNotifyForceMoveStartPosition;
    public:
        CMoveMapLimitRightPortal(int iType);
        void ctor_CMoveMapLimitRightPortal(int iType);
        void CreateComplete(struct CPlayer* pkPlayer);
        bool IsHaveRight();
        void Load(struct CPlayer* pkPlayer);
        void LogOut(struct CPlayer* pkPlayer);
        void SetFlag(int iType, bool bFlag);
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

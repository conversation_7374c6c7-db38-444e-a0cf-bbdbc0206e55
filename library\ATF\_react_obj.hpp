// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <OBJ_DEF_TYPE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _react_obj
    {
        struct ITEM
        {
            char byItemTableCode;
            struct _base_fld *pItemFld;
        };
        struct MONSTER
        {
            struct _monster_fld *pMonsterFld;
        };
        struct MON_GRP
        {
            struct __monster_group *pMonGrp;
        };
        union OBJ
        {
            ITEM item;
            MONSTER monster;
            MON_GRP mon_grp;
        };
        OBJ_DEF_TYPE ObjDefType;
        OBJ obj;
        unsigned __int16 wNum;
    public:
        _react_obj();
        void ctor__react_obj();
        void copy(struct _react_obj* pObj);
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_react_obj, 32>(), "_react_obj");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$40942CBCB8F0A1CDBC81269929B28324.hpp>


START_ATF_NAMESPACE
    union $06F42084E633F086ECEA3694A507987C
    {
        $40942CBCB8F0A1CDBC81269929B28324 __s0;
        float m[4][4];
    };    
    static_assert(ATF::checkSize<$06F42084E633F086ECEA3694A507987C, 64>(), "$06F42084E633F086ECEA3694A507987C");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Sky.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using SkyCreateVertexBuffer1_ptr = int32_t (WINAPIV*)(struct Sky*);
        using SkyCreateVertexBuffer1_clbk = int32_t (WINAPIV*)(struct Sky*, SkyCreateVertexBuffer1_ptr);
        using SkyFillupVertexBuffer2_ptr = int32_t (WINAPIV*)(struct Sky*);
        using SkyFillupVertexBuffer2_clbk = int32_t (WINAPIV*)(struct Sky*, SkyFillupVertexBuffer2_ptr);
        using SkyInvalidateSky3_ptr = void (WINAPIV*)(struct Sky*);
        using SkyInvalidateSky3_clbk = void (WINAPIV*)(struct Sky*, SkyInvalidateSky3_ptr);
        using SkyRender4_ptr = int32_t (WINAPIV*)(struct Sky*);
        using SkyRender4_clbk = int32_t (WINAPIV*)(struct Sky*, SkyRender4_ptr);
        using SkyRestoreSky5_ptr = void (WINAPIV*)(struct Sky*);
        using SkyRestoreSky5_clbk = void (WINAPIV*)(struct Sky*, SkyRestoreSky5_ptr);
        
        using Skyctor_Sky6_ptr = int64_t (WINAPIV*)(struct Sky*);
        using Skyctor_Sky6_clbk = int64_t (WINAPIV*)(struct Sky*, Skyctor_Sky6_ptr);
        
        using Skydtor_Sky7_ptr = int64_t (WINAPIV*)(struct Sky*);
        using Skydtor_Sky7_clbk = int64_t (WINAPIV*)(struct Sky*, Skydtor_Sky7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

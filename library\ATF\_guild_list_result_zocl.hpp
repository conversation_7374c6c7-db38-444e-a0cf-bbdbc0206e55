// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _guild_list_result_zocl
    {
        struct __list
        {
            char byGrade;
            char wszGuild<PERSON>ame[17];
            char wszMaster<PERSON>ame[17];
        };
        char byPage;
        char byMaxPage;
        char byListCnt;
        __list GuildList[4];
    public:
        _guild_list_result_zocl();
        void ctor__guild_list_result_zocl();
        int size();
    };    
    static_assert(ATF::checkSize<_guild_list_result_zocl, 143>(), "_guild_list_result_zocl");
END_ATF_NAMESPACE

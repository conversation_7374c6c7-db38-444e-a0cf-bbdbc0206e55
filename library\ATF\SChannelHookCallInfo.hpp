// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    struct SChannelHookCallInfo
    {
        _GUID iid;
        unsigned int cbSize;
        _GUID uCausality;
        unsigned int dwServerPid;
        unsigned int iMethod;
        void *pObject;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct DbgVtbl
    {
        int (WINAPIV *Close)(Dbg *_this);
        BYTE gap8[8];
        void (WINAPIV *Reset)(Dbg *_this);
        int (WINAPIV *Skip)(Dbg *_this, unsigned int);
        int (WINAPIV *QueryNext)(Dbg *_this, unsigned int, void *);
        int (WINAPIV *Find)(Dbg *_this, void *);
        int (WINAPIV *Clear)(Dbg *_this);
        int (WINAPIV *Append)(Dbg *_this, unsigned int, const void *);
        int (WINAPIV *ReplaceNext)(Dbg *_this, unsigned int, const void *);
        int (WINAPIV *Clone)(Dbg *_this, Dbg **);
        int (WINAPIV *QueryElementSize)(Dbg *_this);
    };
END_ATF_NAMESPACE

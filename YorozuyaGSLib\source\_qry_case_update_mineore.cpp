#include <_qry_case_update_mineore.hpp>


START_ATF_NAMESPACE
    _qry_case_update_mineore::_qry_case_update_mineore()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_update_mineore*);
        (org_ptr(0x1402ddbb0L))(this);
    };
    void _qry_case_update_mineore::ctor__qry_case_update_mineore()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_update_mineore*);
        (org_ptr(0x1402ddbb0L))(this);
    };
    
END_ATF_NAMESPACE

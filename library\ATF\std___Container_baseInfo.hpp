// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Container_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            
            using std___Container_basector__Container_base1_ptr = int64_t (WINAPIV*)(struct std::_Container_base*, struct std::_Container_base*);
            using std___Container_basector__Container_base1_clbk = int64_t (WINAPIV*)(struct std::_Container_base*, struct std::_Container_base*, std___Container_basector__Container_base1_ptr);
            
            using std___Container_basector__Container_base2_ptr = int64_t (WINAPIV*)(struct std::_Container_base*);
            using std___Container_basector__Container_base2_clbk = int64_t (WINAPIV*)(struct std::_Container_base*, std___Container_basector__Container_base2_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

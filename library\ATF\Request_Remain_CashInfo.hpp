// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Request_Remain_Cash.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using Request_Remain_Cashctor_Request_Remain_Cash2_ptr = void (WINAPIV*)(struct Request_Remain_Cash*);
        using Request_Remain_Cashctor_Request_Remain_Cash2_clbk = void (WINAPIV*)(struct Request_Remain_Cash*, Request_Remain_Cashctor_Request_Remain_Cash2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

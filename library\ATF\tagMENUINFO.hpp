// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HBRUSH__.hpp>


START_ATF_NAMESPACE
    struct tagMENUINFO
    {
        unsigned int cbSize;
        unsigned int fMask;
        unsigned int dwStyle;
        unsigned int cyMax;
        HBRUSH__ *hbrBack;
        unsigned int dwContextHelpID;
        unsigned __int64 dwMenuData;
    };
END_ATF_NAMESPACE

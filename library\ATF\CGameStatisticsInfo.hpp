// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGameStatistics.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CGameStatisticsctor_CGameStatistics2_ptr = void (WINAPIV*)(struct CGameStatistics*);
        using CGameStatisticsctor_CGameStatistics2_clbk = void (WINAPIV*)(struct CGameStatistics*, CGameStatisticsctor_CGameStatistics2_ptr);
        using CGameStatisticsConvertDay4_ptr = void (WINAPIV*)(struct CGameStatistics*, char*);
        using CGameStatisticsConvertDay4_clbk = void (WINAPIV*)(struct CGameStatistics*, char*, CGameStatisticsConvertDay4_ptr);
        using CGameStatisticsCurWriteData6_ptr = struct CGameStatistics::_DAY* (WINAPIV*)(struct CGameStatistics*);
        using CGameStatisticsCurWriteData6_clbk = struct CGameStatistics::_DAY* (WINAPIV*)(struct CGameStatistics*, CGameStatisticsCurWriteData6_ptr);
        using CGameStatisticsInit8_ptr = void (WINAPIV*)(struct CGameStatistics*);
        using CGameStatisticsInit8_clbk = void (WINAPIV*)(struct CGameStatistics*, CGameStatisticsInit8_ptr);
        using CGameStatisticsWriteDayData10_ptr = void (WINAPIV*)(struct CGameStatistics*, char*);
        using CGameStatisticsWriteDayData10_clbk = void (WINAPIV*)(struct CGameStatistics*, char*, CGameStatisticsWriteDayData10_ptr);
        
        using CGameStatisticsdtor_CGameStatistics15_ptr = void (WINAPIV*)(struct CGameStatistics*);
        using CGameStatisticsdtor_CGameStatistics15_clbk = void (WINAPIV*)(struct CGameStatistics*, CGameStatisticsdtor_CGameStatistics15_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

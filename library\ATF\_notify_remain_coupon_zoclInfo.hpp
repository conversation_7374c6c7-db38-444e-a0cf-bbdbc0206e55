// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_notify_remain_coupon_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _notify_remain_coupon_zoclsize2_ptr = int (WINAPIV*)(struct _notify_remain_coupon_zocl*);
        using _notify_remain_coupon_zoclsize2_clbk = int (WINAPIV*)(struct _notify_remain_coupon_zocl*, _notify_remain_coupon_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CIndexList.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CIndexListctor_CIndexList2_ptr = void (WINAPIV*)(struct CIndexList*);
        using CIndexListctor_CIndexList2_clbk = void (WINAPIV*)(struct CIndexList*, CIndexListctor_CIndexList2_ptr);
        using CIndexListCopyFront4_ptr = bool (WINAPIV*)(struct CIndexList*, unsigned int*, char*);
        using CIndexListCopyFront4_clbk = bool (WINAPIV*)(struct CIndexList*, unsigned int*, char*, CIndexListCopyFront4_ptr);
        using CIndexListFindNode6_ptr = bool (WINAPIV*)(struct CIndexList*, unsigned int, char*);
        using CIndexListFindNode6_clbk = bool (WINAPIV*)(struct CIndexList*, unsigned int, char*, CIndexListFindNode6_ptr);
        using CIndexListGetAllNode8_ptr = struct CIndexList::_index_node* (WINAPIV*)(struct CIndexList*, unsigned int*);
        using CIndexListGetAllNode8_clbk = struct CIndexList::_index_node* (WINAPIV*)(struct CIndexList*, unsigned int*, CIndexListGetAllNode8_ptr);
        using CIndexListGetSize10_ptr = int (WINAPIV*)(struct CIndexList*);
        using CIndexListGetSize10_clbk = int (WINAPIV*)(struct CIndexList*, CIndexListGetSize10_ptr);
        using CIndexListIsInList12_ptr = bool (WINAPIV*)(struct CIndexList*, unsigned int, char*);
        using CIndexListIsInList12_clbk = bool (WINAPIV*)(struct CIndexList*, unsigned int, char*, CIndexListIsInList12_ptr);
        using CIndexListIsSetting14_ptr = bool (WINAPIV*)(struct CIndexList*);
        using CIndexListIsSetting14_clbk = bool (WINAPIV*)(struct CIndexList*, CIndexListIsSetting14_ptr);
        using CIndexListPopNode_Front16_ptr = bool (WINAPIV*)(struct CIndexList*, unsigned int*, char*);
        using CIndexListPopNode_Front16_clbk = bool (WINAPIV*)(struct CIndexList*, unsigned int*, char*, CIndexListPopNode_Front16_ptr);
        using CIndexListPushNode_Back18_ptr = bool (WINAPIV*)(struct CIndexList*, unsigned int, char*);
        using CIndexListPushNode_Back18_clbk = bool (WINAPIV*)(struct CIndexList*, unsigned int, char*, CIndexListPushNode_Back18_ptr);
        using CIndexListResetList20_ptr = void (WINAPIV*)(struct CIndexList*);
        using CIndexListResetList20_clbk = void (WINAPIV*)(struct CIndexList*, CIndexListResetList20_ptr);
        using CIndexListSetList22_ptr = bool (WINAPIV*)(struct CIndexList*, unsigned int, unsigned int, bool);
        using CIndexListSetList22_clbk = bool (WINAPIV*)(struct CIndexList*, unsigned int, unsigned int, bool, CIndexListSetList22_ptr);
        
        using CIndexListdtor_CIndexList27_ptr = void (WINAPIV*)(struct CIndexList*);
        using CIndexListdtor_CIndexList27_clbk = void (WINAPIV*)(struct CIndexList*, CIndexListdtor_CIndexList27_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct TBMETRICS
    {
        unsigned int cbSize;
        unsigned int dwMask;
        int cxPad;
        int cyPad;
        int cxBarPad;
        int cyBarPad;
        int cxButtonSpacing;
        int cyButtonSpacing;
    };
END_ATF_NAMESPACE

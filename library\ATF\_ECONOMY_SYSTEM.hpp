// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _ECONOMY_SYSTEM
    {
        struct _CUR_RATE
        {
            float fPayExgRate;
            float fTexRate;
            float fOreRate;
            unsigned __int16 wEconomyGuide;
            long double dOldTradeGold;
            long double dOldTradeDalant;
            long double dOldOreMineCount[3];
            long double dOldOreCutCount[3];
            unsigned int dwTexRate;
        };
        bool m_bLoad;
        long double m_dCurTradeGold[3];
        long double m_dCurTradeDalant[3];
        long double m_dBufTradeGold[3];
        long double m_dBufTradeDalant[3];
        long double m_dCurOreMineCount[3][3];
        long double m_dCurOreCutCount[3][3];
        long double m_dBufOreMineCount[3][3];
        long double m_dBufOreCutCount[3][3];
        _CUR_RATE m_CurRate[3];
        char m_byCurHour;
        unsigned int m_dwLastUpdateTime;
        unsigned int m_dwSystemOperStartTime;
        unsigned int m_dwLastDate;
    public:
        void CurTradeMoneyInit();
        void Init();
        _ECONOMY_SYSTEM();
        void ctor__ECONOMY_SYSTEM();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_ECONOMY_SYSTEM, 672>(), "_ECONOMY_SYSTEM");
END_ATF_NAMESPACE

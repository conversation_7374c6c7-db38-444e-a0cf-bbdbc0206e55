// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum NuclearRet
    {
      retcode_nuclear_success = 0x0,
      retcode__this_is_nuclear = 0x1,
      retcode_nuclear_drop_success = 0x2,
      retcode_nuclear_obtain_nodate = 0x3,
      retcode_nuclear_obtain_unqualified = 0x4,
      retcode_nuclear_use_unqualified = 0x5,
      retcode_nuclear_noready_time = 0x6,
      retcode_nulcear_noready_stick = 0x7,
      retcode_inven_full = 0x8,
      retcode_nuclear_drop_unusable_area = 0x9,
      retcode_not_use_in_stealth = 0xA,
      retcode_nuclear_data_fail = 0xB,
      retcode_nuclear_master_died = 0xC,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_rank_racerank_guildrank
    {
        enum STATE
        {
            RET_SUCCESS = 0x0,
            RET_UPDATERACERANKSTEP13_FAIL = 0x1,
            RET_UPDATERACERANKSTEP14_FAIL = 0x2,
            RET_UPDATEGUILDRANKSTEP4_FAIL = 0x3,
            RET_UPDATERANKINGUILDSTEP1_GUILD_MEBER_NODATA = 0x4,
            RET_UPDATERANKINGUILDSTEP5_FAIL_INVALID_CURINX = 0x5,
            RET_UPDATERANKINGUILDSTEP5_FAIL_INVALID_SERIAL = 0x6,
            RET_UPDATE_AND_SELECT_GARDE_FAIL_UPDATE_GUILDGRADE = 0x7,
            RET_UPDATE_AND_SELECT_GARDE_FAIL_SELECTALLGUILDSERIALGRADE = 0x8,
        };
        char szDate[9];
        char scProcRet;
        unsigned int dwParam1;
        unsigned int dwParam2;
    public:
        void ClearRetParam();
        _qry_case_rank_racerank_guildrank();
        void ctor__qry_case_rank_racerank_guildrank();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_rank_racerank_guildrank, 20>(), "_qry_case_rank_racerank_guildrank");
END_ATF_NAMESPACE

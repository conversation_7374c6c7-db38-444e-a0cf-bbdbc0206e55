// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <LendItemSheet.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct LendItemMng
    {
        LendItemSheet **_ppkLendItem;
        CLogFile _kLogSys;
    public:
        bool DeleteLink(uint16_t wIdx, char byStorageCode, struct _STORAGE_LIST::_db_con* pkItem);
        struct LendItemSheet* GetSheet(uint16_t wIdx);
        bool Initialize();
        bool InsertLink(uint16_t wIdx, char byStorageCode, struct _STORAGE_LIST::_db_con* pkItem);
        static struct LendItemMng* Instance();
        LendItemMng();
        void ctor_LendItemMng();
        void Release(uint16_t wIdx);
        void ReleaseAll();
        ~LendItemMng();
        void dtor_LendItemMng();
    };
END_ATF_NAMESPACE

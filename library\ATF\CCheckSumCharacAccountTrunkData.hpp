// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRFWorldDatabase.hpp>
#include <_AVATOR_DATA.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CCheckSumCharacAccountTrunkData
    {
        enum COLUMN_DW_TYPE
        {
            CDWT_BASE = 0xFFFFFFFF,
            CDWT_DALANT = 0x0,
            CDWT_GOLD = 0x1,
            CDWT_LV = 0x2,
            CDWT_NEAR_MASTERY = 0x3,
            CDWT_DIS_MASTERY = 0x4,
            CDWT_DEF_MASTERY = 0x5,
            CDWT_DW_MAX_TYPE = 0x6,
        };
        enum COLUMN_D_TYPE
        {
            CDT_BASE = 0xFFFFFFFF,
            CDT_TRUNK_DALANT = 0x0,
            CDT_TRUNK_GOLD = 0x1,
            CDT_MAX_TYPE = 0x2,
        };
        unsigned int m_dwSerial;
        unsigned int m_dwAccountSerial;
        char m_byRace;
        unsigned int m_dwValues[6];
        long double m_dValues[2];
    public:
        CCheckSumCharacAccountTrunkData(unsigned int dwSerial, unsigned int dwAccountSerial, char byRace);
        void ctor_CCheckSumCharacAccountTrunkData(unsigned int dwSerial, unsigned int dwAccountSerial, char byRace);
        int CheckDiff(struct CRFWorldDatabase* pkDB, char* wszName, struct CCheckSumCharacAccountTrunkData* kSrcValue);
        void Decode(struct _AVATOR_DATA* pAvator);
        void Encode(struct _AVATOR_DATA* pAvator);
        bool InsertCharacData(struct CRFWorldDatabase* pkDB);
        bool InsertTrunkData(struct CRFWorldDatabase* pkDB);
        int Load(struct CRFWorldDatabase* pkDB, struct CCheckSumCharacAccountTrunkData* kSrcValue);
        void SetValue(COLUMN_DW_TYPE eType, unsigned int dwValue);
        void SetValue(COLUMN_D_TYPE eType, long double dValue);
        bool Update(struct CRFWorldDatabase* pkDB);
        ~CCheckSumCharacAccountTrunkData();
        void dtor_CCheckSumCharacAccountTrunkData();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CCheckSumCharacAccountTrunkData, 56>(), "CCheckSumCharacAccountTrunkData");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__locale.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  numpunct<char> : locale::facet
        {
            const char *_Grouping;
            char _Dp;
            char _Kseparator;
            const char *_Falsename;
            const char *_Truename;
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std__locale.hpp>



START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  numpunct<wchar_t> : locale::facet
        {
            const char *_Grouping;
            wchar_t _Dp;
            wchar_t _Kseparator;
            const wchar_t *_Falsename;
            const wchar_t *_Truename;
        };
    }; // end namespace std
END_ATF_NAMESPACE

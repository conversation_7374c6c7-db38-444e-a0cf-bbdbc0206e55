// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_joinacguild
    {
        unsigned int in_guildindex;
        unsigned int in_guildserial;
        unsigned int in_applierindex;
        unsigned int in_applierserial;
        unsigned int in_accepterserial;
        char in_Grade;
        int in_MemberNum;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_joinacguild, 28>(), "_qry_case_joinacguild");
END_ATF_NAMESPACE

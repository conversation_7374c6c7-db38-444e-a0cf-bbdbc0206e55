// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderLazyCleaner
    {
        CMyTimer *m_pkTimer;
        bool m_bClearProcess;
        unsigned int m_uiRetryCnt;
    public:
        CUnmannedTraderLazyCleaner();
        void ctor_CUnmannedTraderLazyCleaner();
        void CompleteUpdateClear(char* p);
        bool Init();
        void Loop();
        char ProcUpdate(char byState, struct _SYSTEMTIME* pCurTime, bool* pbRemain);
        char UpdateClear(char* p);
        ~CUnmannedTraderLazyCleaner();
        void dtor_CUnmannedTraderLazyCleaner();
    };
END_ATF_NAMESPACE

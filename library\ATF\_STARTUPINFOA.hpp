// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _STARTUPINFOA
    {
        unsigned int cb;
        char *lpReserved;
        char *lpDesktop;
        char *lpTitle;
        unsigned int dwX;
        unsigned int dwY;
        unsigned int dwXSize;
        unsigned int dwYSize;
        unsigned int dwXCountChars;
        unsigned int dwYCountChars;
        unsigned int dwFillAttribute;
        unsigned int dwFlags;
        unsigned __int16 wShowWindow;
        unsigned __int16 cbReserved2;
        char *lpReserved2;
        void *hStdInput;
        void *hStdOutput;
        void *hStdError;
    };    
    static_assert(ATF::checkSize<_STARTUPINFOA, 104>(), "_STARTUPINFOA");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AINet.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using AINetctor_AINet2_ptr = void (WINAPIV*)(struct AINet*, char*, unsigned int, char*, char*, unsigned int);
        using AINetctor_AINet2_clbk = void (WINAPIV*)(struct AINet*, char*, unsigned int, char*, char*, unsigned int, AINetctor_AINet2_ptr);
        
        using AINetdtor_AINet7_ptr = void (WINAPIV*)(struct AINet*);
        using AINetdtor_AINet7_clbk = void (WINAPIV*)(struct AINet*, AINetdtor_AINet7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

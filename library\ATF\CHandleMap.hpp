// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CFixedAllocNoSync.hpp>
#include <CMapPtrToPtr.hpp>
#include <CRuntimeClass.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CHandleMap
    {
        CFixedAllocNoSync m_alloc;
        void (WINAPIV *m_pfnConstructObject)(CObject *);
        void (WINAPIV *m_pfnDestructObject)(CObject *);
        CMapPtrToPtr m_permanentMap;
        CMapPtrToPtr m_temporaryMap;
        CRuntimeClass *m_pClass;
        unsigned __int64 m_nOffset;
        int m_nHandles;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

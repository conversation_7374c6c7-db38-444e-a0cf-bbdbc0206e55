// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingJP.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CBillingJPAlive2_ptr = void (WINAPIV*)(struct CBillingJP*, struct CUserDB*);
        using CBillingJPAlive2_clbk = void (WINAPIV*)(struct CBillingJP*, struct CUserDB*, CBillingJPAlive2_ptr);
        
        using CBillingJPctor_CBillingJP4_ptr = void (WINAPIV*)(struct CBillingJP*);
        using CBillingJPctor_CBillingJP4_clbk = void (WINAPIV*)(struct CBillingJP*, CBillingJPctor_CBillingJP4_ptr);
        using CBillingJPLogin6_ptr = void (WINAPIV*)(struct CBillingJP*, struct CUserDB*);
        using CBillingJPLogin6_clbk = void (WINAPIV*)(struct CBillingJP*, struct CUserDB*, CBillingJPLogin6_ptr);
        using CBillingJPLogout8_ptr = void (WINAPIV*)(struct CBillingJP*, struct CUserDB*);
        using CBillingJPLogout8_clbk = void (WINAPIV*)(struct CBillingJP*, struct CUserDB*, CBillingJPLogout8_ptr);
        using CBillingJPSendMsg_Login10_ptr = bool (WINAPIV*)(struct CBillingJP*, char*, char*, char*, int16_t, struct _SYSTEMTIME*, int);
        using CBillingJPSendMsg_Login10_clbk = bool (WINAPIV*)(struct CBillingJP*, char*, char*, char*, int16_t, struct _SYSTEMTIME*, int, CBillingJPSendMsg_Login10_ptr);
        
        using CBillingJPdtor_CBillingJP15_ptr = void (WINAPIV*)(struct CBillingJP*);
        using CBillingJPdtor_CBillingJP15_clbk = void (WINAPIV*)(struct CBillingJP*, CBillingJPdtor_CBillingJP15_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

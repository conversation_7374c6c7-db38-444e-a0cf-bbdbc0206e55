// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleGuildMember.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_ptr = long double (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, struct GUILD_BATTLE::CNormalGuildBattleLogger*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_clbk = long double (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, struct GUILD_BATTLE::CNormalGuildBattleLogger*, GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_ptr = uint16_t (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_clbk = uint16_t (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_ptr = uint16_t (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_clbk = uint16_t (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_ptr = uint16_t (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_clbk = uint16_t (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_ptr = struct CPlayer* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_clbk = struct CPlayer* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_ptr = long double (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, long double, struct GUILD_BATTLE::CNormalGuildBattleLogger*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_clbk = long double (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, long double, struct GUILD_BATTLE::CNormalGuildBattleLogger*, GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, struct _guild_member_info*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, struct _guild_member_info*, GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, unsigned int, GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, char*, char*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, char*, char*, unsigned int, GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, bool, char);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, bool, char, GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_ptr);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*);
            using GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Global.hpp>


START_ATF_NAMESPACE
    namespace Global
    {
        namespace Info
        {
            using ARGBToJpegFile1_ptr = int64_t (WINAPIV*)(char*, uint8_t*, unsigned int, unsigned int, unsigned int, int, int);
            using ARGBToJpegFile1_clbk = int64_t (WINAPIV*)(char*, uint8_t*, unsigned int, unsigned int, unsigned int, int, int, ARGBToJpegFile1_ptr);
            using AddEnvVariable3_ptr = int (WINAPIV*)(char*, char*, int);
            using AddEnvVariable3_clbk = int (WINAPIV*)(char*, char*, int, AddEnvVariable3_ptr);
            using AdjustIndependenceR3M4_ptr = void (WINAPIV*)(struct _R3MATERIAL*, int32_t, int32_t);
            using AdjustIndependenceR3M4_clbk = void (WINAPIV*)(struct _R3MATERIAL*, int32_t, int32_t, AdjustIndependenceR3M4_ptr);
            using AfterRenderOneLayer5_ptr = void (WINAPIV*)(struct CVertexBuffer*, struct _BSP_MAT_GROUP*);
            using AfterRenderOneLayer5_clbk = void (WINAPIV*)(struct CVertexBuffer*, struct _BSP_MAT_GROUP*, AfterRenderOneLayer5_ptr);
            using AfterRenderSetting6_ptr = void (WINAPIV*)(int, struct CVertexBuffer*, struct _BSP_MAT_GROUP*);
            using AfterRenderSetting6_clbk = void (WINAPIV*)(int, struct CVertexBuffer*, struct _BSP_MAT_GROUP*, AfterRenderSetting6_ptr);
            using AfxAssertFailedLine7_ptr = int64_t (WINAPIV*)(char*, int);
            using AfxAssertFailedLine7_clbk = int64_t (WINAPIV*)(char*, int, AfxAssertFailedLine7_ptr);
            using AfxCrtErrorCheck9_ptr = int (WINAPIV*)(int);
            using AfxCrtErrorCheck9_clbk = int (WINAPIV*)(int, AfxCrtErrorCheck9_ptr);
            using AfxEnableControlContainer10_ptr = void (WINAPIV*)(struct COccManager*);
            using AfxEnableControlContainer10_clbk = void (WINAPIV*)(struct COccManager*, AfxEnableControlContainer10_ptr);
            using AfxGetInstanceHandle11_ptr = HINSTANCE (WINAPIV*)();
            using AfxGetInstanceHandle11_clbk = HINSTANCE (WINAPIV*)(AfxGetInstanceHandle11_ptr);
            using AfxGetModuleState12_ptr = struct AFX_MODULE_STATE* (WINAPIV*)();
            using AfxGetModuleState12_clbk = struct AFX_MODULE_STATE* (WINAPIV*)(AfxGetModuleState12_ptr);
            using AfxInitialize13_ptr = int (WINAPIV*)(int, unsigned int);
            using AfxInitialize13_clbk = int (WINAPIV*)(int, unsigned int, AfxInitialize13_ptr);
            using AfxThrowInvalidArgException14_ptr = void (WINAPIV*)();
            using AfxThrowInvalidArgException14_clbk = void (WINAPIV*)(AfxThrowInvalidArgException14_ptr);
            using AfxThrowMemoryException15_ptr = void (WINAPIV*)();
            using AfxThrowMemoryException15_clbk = void (WINAPIV*)(AfxThrowMemoryException15_ptr);
            using AfxThrowOleException16_ptr = void (WINAPIV*)(int32_t);
            using AfxThrowOleException16_clbk = void (WINAPIV*)(int32_t, AfxThrowOleException16_ptr);
            using AfxWinMain17_ptr = int64_t (WINAPIV*)(HINSTANCE, HINSTANCE, char*, int);
            using AfxWinMain17_clbk = int64_t (WINAPIV*)(HINSTANCE, HINSTANCE, char*, int, AfxWinMain17_ptr);
            using AtlA2WHelper19_ptr = wchar_t* (WINAPIV*)(wchar_t*, char*, int, unsigned int);
            using AtlA2WHelper19_clbk = wchar_t* (WINAPIV*)(wchar_t*, char*, int, unsigned int, AtlA2WHelper19_ptr);
            using AtlW2AHelper21_ptr = char* (WINAPIV*)(char*, wchar_t*, int, unsigned int);
            using AtlW2AHelper21_clbk = char* (WINAPIV*)(char*, wchar_t*, int, unsigned int, AtlW2AHelper21_ptr);
            using AtoH23_ptr = void (WINAPIV*)(char*, char*, int);
            using AtoH23_clbk = void (WINAPIV*)(char*, char*, int, AtoH23_ptr);
            using AuthorityFilter25_ptr = bool (WINAPIV*)(struct CHEAT_COMMAND*, struct CPlayer*);
            using AuthorityFilter25_clbk = bool (WINAPIV*)(struct CHEAT_COMMAND*, struct CPlayer*, AuthorityFilter25_ptr);
            using BGRFromRGB26_ptr = int64_t (WINAPIV*)(uint8_t*, unsigned int, unsigned int);
            using BGRFromRGB26_clbk = int64_t (WINAPIV*)(uint8_t*, unsigned int, unsigned int, BGRFromRGB26_ptr);
            using BlendOff27_ptr = void (WINAPIV*)();
            using BlendOff27_clbk = void (WINAPIV*)(BlendOff27_ptr);
            using BlendOn28_ptr = void (WINAPIV*)(int);
            using BlendOn28_clbk = void (WINAPIV*)(int, BlendOn28_ptr);
            using BlurFilterShader29_ptr = void (WINAPIV*)(uint32_t, struct CTextureRender*, struct CTextureRender*);
            using BlurFilterShader29_clbk = void (WINAPIV*)(uint32_t, struct CTextureRender*, struct CTextureRender*, BlurFilterShader29_ptr);
            using BlurFilterSprite30_ptr = void (WINAPIV*)(struct CTextureRender*, uint32_t, void*);
            using BlurFilterSprite30_clbk = void (WINAPIV*)(struct CTextureRender*, uint32_t, void*, BlurFilterSprite30_ptr);
            using BlurShaderVSPS31_ptr = int32_t (WINAPIV*)(uint32_t, struct CTextureRender*, struct CTextureRender*);
            using BlurShaderVSPS31_clbk = int32_t (WINAPIV*)(uint32_t, struct CTextureRender*, struct CTextureRender*, BlurShaderVSPS31_ptr);
            using BtoH33_ptr = char (WINAPIV*)(char);
            using BtoH33_clbk = char (WINAPIV*)(char, BtoH33_ptr);
            using ByteSortForShort34_ptr = void (WINAPIV*)(uint32_t, uint32_t*, int16_t*, uint32_t*, int16_t*);
            using ByteSortForShort34_clbk = void (WINAPIV*)(uint32_t, uint32_t*, int16_t*, uint32_t*, int16_t*, ByteSortForShort34_ptr);
            using CN_CalculateSunAndFieldColor35_ptr = void (WINAPIV*)(float);
            using CN_CalculateSunAndFieldColor35_clbk = void (WINAPIV*)(float, CN_CalculateSunAndFieldColor35_ptr);
            using CN_GetAccselateTime36_ptr = float (WINAPIV*)();
            using CN_GetAccselateTime36_clbk = float (WINAPIV*)(CN_GetAccselateTime36_ptr);
            using CN_GetAtmosphere37_ptr = struct Atmosphere* (WINAPIV*)();
            using CN_GetAtmosphere37_clbk = struct Atmosphere* (WINAPIV*)(CN_GetAtmosphere37_ptr);
            using CN_GetDayTime38_ptr = void (WINAPIV*)(uint32_t*, uint32_t*, uint32_t*);
            using CN_GetDayTime38_clbk = void (WINAPIV*)(uint32_t*, uint32_t*, uint32_t*, CN_GetDayTime38_ptr);
            using CN_GetDayTime39_ptr = float (WINAPIV*)();
            using CN_GetDayTime39_clbk = float (WINAPIV*)(CN_GetDayTime39_ptr);
            using CN_GetFieldColor40_ptr = uint32_t (WINAPIV*)();
            using CN_GetFieldColor40_clbk = uint32_t (WINAPIV*)(CN_GetFieldColor40_ptr);
            using CN_GetFogColor41_ptr = uint32_t (WINAPIV*)(float*);
            using CN_GetFogColor41_clbk = uint32_t (WINAPIV*)(float*, CN_GetFogColor41_ptr);
            using CN_GetRealDayTime42_ptr = void (WINAPIV*)(uint32_t*, uint32_t*, uint32_t*);
            using CN_GetRealDayTime42_clbk = void (WINAPIV*)(uint32_t*, uint32_t*, uint32_t*, CN_GetRealDayTime42_ptr);
            using CN_GetRealDayTime43_ptr = float (WINAPIV*)();
            using CN_GetRealDayTime43_clbk = float (WINAPIV*)(CN_GetRealDayTime43_ptr);
            using CN_GetSky44_ptr = Sky* (WINAPIV*)();
            using CN_GetSky44_clbk = Sky* (WINAPIV*)(CN_GetSky44_ptr);
            using CN_GetSun45_ptr = struct Sun* (WINAPIV*)();
            using CN_GetSun45_clbk = struct Sun* (WINAPIV*)(CN_GetSun45_ptr);
            using CN_GetSunColor46_ptr = uint32_t (WINAPIV*)();
            using CN_GetSunColor46_clbk = uint32_t (WINAPIV*)(CN_GetSunColor46_ptr);
            using CN_GetSunDirection47_ptr = void (WINAPIV*)(float*);
            using CN_GetSunDirection47_clbk = void (WINAPIV*)(float*, CN_GetSunDirection47_ptr);
            using CN_GetWeather48_ptr = float (WINAPIV*)();
            using CN_GetWeather48_clbk = float (WINAPIV*)(CN_GetWeather48_ptr);
            using CN_InvalidateNature49_ptr = void (WINAPIV*)();
            using CN_InvalidateNature49_clbk = void (WINAPIV*)(CN_InvalidateNature49_ptr);
            using CN_IsEnableSky50_ptr = int64_t (WINAPIV*)();
            using CN_IsEnableSky50_clbk = int64_t (WINAPIV*)(CN_IsEnableSky50_ptr);
            using CN_MixDayColor51_ptr = uint32_t (WINAPIV*)(uint32_t);
            using CN_MixDayColor51_clbk = uint32_t (WINAPIV*)(uint32_t, CN_MixDayColor51_ptr);
            using CN_NatureFrameMove52_ptr = void (WINAPIV*)();
            using CN_NatureFrameMove52_clbk = void (WINAPIV*)(CN_NatureFrameMove52_ptr);
            using CN_RenderSky53_ptr = void (WINAPIV*)();
            using CN_RenderSky53_clbk = void (WINAPIV*)(CN_RenderSky53_ptr);
            using CN_RestoreNature54_ptr = void (WINAPIV*)();
            using CN_RestoreNature54_clbk = void (WINAPIV*)(CN_RestoreNature54_ptr);
            using CN_SetAccselateTime55_ptr = void (WINAPIV*)(float);
            using CN_SetAccselateTime55_clbk = void (WINAPIV*)(float, CN_SetAccselateTime55_ptr);
            using CN_SetDayTime56_ptr = void (WINAPIV*)(float);
            using CN_SetDayTime56_clbk = void (WINAPIV*)(float, CN_SetDayTime56_ptr);
            using CN_SetDayTime57_ptr = void (WINAPIV*)(uint32_t, uint32_t, uint32_t);
            using CN_SetDayTime57_clbk = void (WINAPIV*)(uint32_t, uint32_t, uint32_t, CN_SetDayTime57_ptr);
            using CN_SetEnableSky58_ptr = void (WINAPIV*)(int);
            using CN_SetEnableSky58_clbk = void (WINAPIV*)(int, CN_SetEnableSky58_ptr);
            using CN_SetRealDayTime59_ptr = void (WINAPIV*)(float);
            using CN_SetRealDayTime59_clbk = void (WINAPIV*)(float, CN_SetRealDayTime59_ptr);
            using CN_SetRealDayTime60_ptr = void (WINAPIV*)(uint32_t, uint32_t, uint32_t);
            using CN_SetRealDayTime60_clbk = void (WINAPIV*)(uint32_t, uint32_t, uint32_t, CN_SetRealDayTime60_ptr);
            using CN_SetWeather61_ptr = void (WINAPIV*)(float);
            using CN_SetWeather61_clbk = void (WINAPIV*)(float, CN_SetWeather61_ptr);
            using CN_SkyVertexShaderConstants62_ptr = void (WINAPIV*)();
            using CN_SkyVertexShaderConstants62_clbk = void (WINAPIV*)(CN_SkyVertexShaderConstants62_ptr);
            using CalcBi_n63_ptr = float (WINAPIV*)(int, int, double);
            using CalcBi_n63_clbk = float (WINAPIV*)(int, int, double, CalcBi_n63_ptr);
            using CalcCodeKey65_ptr = unsigned int* (WINAPIV*)(unsigned int*);
            using CalcCodeKey65_clbk = unsigned int* (WINAPIV*)(unsigned int*, CalcCodeKey65_ptr);
            using CalcCubicCurve66_ptr = void (WINAPIV*)(float**, int, float*);
            using CalcCubicCurve66_clbk = void (WINAPIV*)(float**, int, float*, CalcCubicCurve66_ptr);
            using CalcEvalCubicCurve67_ptr = float (WINAPIV*)(float*, float);
            using CalcEvalCubicCurve67_clbk = float (WINAPIV*)(float*, float, CalcEvalCubicCurve67_ptr);
            using CalcFileSize69_ptr = int (WINAPIV*)(char*);
            using CalcFileSize69_clbk = int (WINAPIV*)(char*, CalcFileSize69_ptr);
            using CalcMastery71_ptr = int (WINAPIV*)(int, int, unsigned int, int);
            using CalcMastery71_clbk = int (WINAPIV*)(int, int, unsigned int, int, CalcMastery71_ptr);
            using CalcRoundUp73_ptr = int (WINAPIV*)(float);
            using CalcRoundUp73_clbk = int (WINAPIV*)(float, CalcRoundUp73_ptr);
            using CalcSnakeVertexList74_ptr = uint32_t (WINAPIV*)(struct _D3DR3VERTEX_TEX1*, float**, uint32_t, float, uint32_t);
            using CalcSnakeVertexList74_clbk = uint32_t (WINAPIV*)(struct _D3DR3VERTEX_TEX1*, float**, uint32_t, float, uint32_t, CalcSnakeVertexList74_ptr);
            using CalcSquare76_ptr = int (WINAPIV*)(int, int);
            using CalcSquare76_clbk = int (WINAPIV*)(int, int, CalcSquare76_ptr);
            using CalculateMoveCamera77_ptr = void (WINAPIV*)(_MOVE_CAMERA*);
            using CalculateMoveCamera77_clbk = void (WINAPIV*)(_MOVE_CAMERA*, CalculateMoveCamera77_ptr);
            using CanAddMoneyForMaxLimGold79_ptr = bool (WINAPIV*)(uint64_t, uint64_t);
            using CanAddMoneyForMaxLimGold79_clbk = bool (WINAPIV*)(uint64_t, uint64_t, CanAddMoneyForMaxLimGold79_ptr);
            using CanAddMoneyForMaxLimMoney81_ptr = bool (WINAPIV*)(uint64_t, uint64_t);
            using CanAddMoneyForMaxLimMoney81_clbk = bool (WINAPIV*)(uint64_t, uint64_t, CanAddMoneyForMaxLimMoney81_ptr);
            using CcrFgCallback83_ptr = int (WINAPIV*)(int, void*, void*, int, void*);
            using CcrFgCallback83_clbk = int (WINAPIV*)(int, void*, void*, int, void*, CcrFgCallback83_ptr);
            using CheckEdge84_ptr = int64_t (WINAPIV*)(float*, float*, float*, float*, float);
            using CheckEdge84_clbk = int64_t (WINAPIV*)(float*, float*, float*, float*, float, CheckEdge84_ptr);
            using CheckEdgeEpsilon85_ptr = int64_t (WINAPIV*)(float*, float*, float*, float*, float);
            using CheckEdgeEpsilon85_clbk = int64_t (WINAPIV*)(float*, float*, float*, float*, float, CheckEdgeEpsilon85_ptr);
            using CheckSameItemFromString_CodeIndex87_ptr = bool (WINAPIV*)(char*, char, uint16_t);
            using CheckSameItemFromString_CodeIndex87_clbk = bool (WINAPIV*)(char*, char, uint16_t, CheckSameItemFromString_CodeIndex87_ptr);
            using Clean2DRectangleZbuffer88_ptr = void (WINAPIV*)(int32_t, int32_t, int32_t, int32_t);
            using Clean2DRectangleZbuffer88_clbk = void (WINAPIV*)(int32_t, int32_t, int32_t, int32_t, Clean2DRectangleZbuffer88_ptr);
            using CleanViewPortStack89_ptr = void (WINAPIV*)();
            using CleanViewPortStack89_clbk = void (WINAPIV*)(CleanViewPortStack89_ptr);
            using CleanZbuffer90_ptr = void (WINAPIV*)(float, float, float, float);
            using CleanZbuffer90_clbk = void (WINAPIV*)(float, float, float, float, CleanZbuffer90_ptr);
            using ClearDynamicLight91_ptr = void (WINAPIV*)();
            using ClearDynamicLight91_clbk = void (WINAPIV*)(ClearDynamicLight91_ptr);
            using CombineExCheckKeyGen93_ptr = unsigned int (WINAPIV*)(unsigned int, unsigned int);
            using CombineExCheckKeyGen93_clbk = unsigned int (WINAPIV*)(unsigned int, unsigned int, CombineExCheckKeyGen93_ptr);
            using CompareGradeAndPvpPoint95_ptr = int (WINAPIV*)(void*, void*);
            using CompareGradeAndPvpPoint95_clbk = int (WINAPIV*)(void*, void*, CompareGradeAndPvpPoint95_ptr);
            using ConvAniObject96_ptr = void (WINAPIV*)(int, uint8_t*, _READ_ANI_OBJECT*, struct _ANI_OBJECT*);
            using ConvAniObject96_clbk = void (WINAPIV*)(int, uint8_t*, _READ_ANI_OBJECT*, struct _ANI_OBJECT*, ConvAniObject96_ptr);
            using ConvertCodeIntoItem98_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(char*, char, char);
            using ConvertCodeIntoItem98_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(char*, char, char, ConvertCodeIntoItem98_ptr);
            using ConvertErrorCode_Jap100_ptr = int (WINAPIV*)(char);
            using ConvertErrorCode_Jap100_clbk = int (WINAPIV*)(char, ConvertErrorCode_Jap100_ptr);
            using ConvertHexa101_ptr = uint32_t (WINAPIV*)(char*);
            using ConvertHexa101_clbk = uint32_t (WINAPIV*)(char*, ConvertHexa101_ptr);
            using CosineInterpolate102_ptr = float (WINAPIV*)(float, float, float);
            using CosineInterpolate102_clbk = float (WINAPIV*)(float, float, float, CosineInterpolate102_ptr);
            using CountOfImports103_ptr = unsigned int (WINAPIV*)(struct _IMAGE_THUNK_DATA64*);
            using CountOfImports103_clbk = unsigned int (WINAPIV*)(struct _IMAGE_THUNK_DATA64*, CountOfImports103_ptr);
            using CreateAndWriteUVOffsets104_ptr = void (WINAPIV*)(int, int);
            using CreateAndWriteUVOffsets104_clbk = void (WINAPIV*)(int, int, CreateAndWriteUVOffsets104_ptr);
            using CreateAnimus106_ptr = bool (WINAPIV*)(struct CMapData*, uint16_t, float*, char, int, int, unsigned int, struct CPlayer*);
            using CreateAnimus106_clbk = bool (WINAPIV*)(struct CMapData*, uint16_t, float*, char, int, int, unsigned int, struct CPlayer*, CreateAnimus106_ptr);
            using CreateBlurVBuffer107_ptr = void (WINAPIV*)(uint32_t, uint32_t);
            using CreateBlurVBuffer107_clbk = void (WINAPIV*)(uint32_t, uint32_t, CreateBlurVBuffer107_ptr);
            using CreateGuardTower109_ptr = struct CGuardTower* (WINAPIV*)(struct CMapData*, uint16_t, float*, struct _STORAGE_LIST::_db_con*, struct CPlayer*, char, bool);
            using CreateGuardTower109_clbk = struct CGuardTower* (WINAPIV*)(struct CMapData*, uint16_t, float*, struct _STORAGE_LIST::_db_con*, struct CPlayer*, char, bool, CreateGuardTower109_ptr);
            using CreateItemBox111_ptr = struct CItemBox* (WINAPIV*)(struct _STORAGE_LIST::_db_con*, struct CPlayer*, unsigned int, bool, struct CCharacter*, char, struct CMapData*, uint16_t, float*, bool);
            using CreateItemBox111_clbk = struct CItemBox* (WINAPIV*)(struct _STORAGE_LIST::_db_con*, struct CPlayer*, unsigned int, bool, struct CCharacter*, char, struct CMapData*, uint16_t, float*, bool, CreateItemBox111_ptr);
            using CreateItemBox113_ptr = struct CItemBox* (WINAPIV*)(struct _STORAGE_LIST::_db_con*, char, struct CMapData*, uint16_t, float*, bool, struct CPlayer*, int, char);
            using CreateItemBox113_clbk = struct CItemBox* (WINAPIV*)(struct _STORAGE_LIST::_db_con*, char, struct CMapData*, uint16_t, float*, bool, struct CPlayer*, int, char, CreateItemBox113_ptr);
            using CreateRepMonster115_ptr = struct CMonster* (WINAPIV*)(struct CMapData*, uint16_t, float*, char*, struct CMonster*, bool, bool, bool, bool, bool);
            using CreateRepMonster115_clbk = struct CMonster* (WINAPIV*)(struct CMapData*, uint16_t, float*, char*, struct CMonster*, bool, bool, bool, bool, bool, CreateRepMonster115_ptr);
            using CreateRespawnMonster117_ptr = struct CMonster* (WINAPIV*)(struct CMapData*, uint16_t, int, struct _mon_active*, struct _dummy_position*, bool, bool, bool, bool, bool);
            using CreateRespawnMonster117_clbk = struct CMonster* (WINAPIV*)(struct CMapData*, uint16_t, int, struct _mon_active*, struct _dummy_position*, bool, bool, bool, bool, bool, CreateRespawnMonster117_ptr);
            using CreateSystemTower119_ptr = struct CGuardTower* (WINAPIV*)(struct CMapData*, uint16_t, float*, int, char, int);
            using CreateSystemTower119_clbk = struct CGuardTower* (WINAPIV*)(struct CMapData*, uint16_t, float*, int, char, int, CreateSystemTower119_ptr);
            using CreateTrap121_ptr = struct CTrap* (WINAPIV*)(struct CMapData*, uint16_t, float*, struct CPlayer*, int);
            using CreateTrap121_clbk = struct CTrap* (WINAPIV*)(struct CMapData*, uint16_t, float*, struct CPlayer*, int, CreateTrap121_ptr);
            using CrossVector122_ptr = void (WINAPIV*)(float*, float*, float*);
            using CrossVector122_clbk = void (WINAPIV*)(float*, float*, float*, CrossVector122_ptr);
            using CubicInterpolate123_ptr = float (WINAPIV*)(float, float, float, float, float);
            using CubicInterpolate123_clbk = float (WINAPIV*)(float, float, float, float, float, CubicInterpolate123_ptr);
            using DDX_Control148_ptr = void (WINAPIV*)(struct CDataExchange*, int, struct CWnd*);
            using DDX_Control148_clbk = void (WINAPIV*)(struct CDataExchange*, int, struct CWnd*, DDX_Control148_ptr);
            using DDX_Text149_ptr = void (WINAPIV*)(struct CDataExchange*, int, int*);
            using DDX_Text149_clbk = void (WINAPIV*)(struct CDataExchange*, int, int*, DDX_Text149_ptr);
            using DDX_Text150_ptr = void (WINAPIV*)(struct CDataExchange*, int, unsigned int*);
            using DDX_Text150_clbk = void (WINAPIV*)(struct CDataExchange*, int, unsigned int*, DDX_Text150_ptr);
            using DE_AllContDamageForceRemove152_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_AllContDamageForceRemove152_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_AllContDamageForceRemove152_ptr);
            using DE_AllContHelpForceRemove154_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_AllContHelpForceRemove154_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_AllContHelpForceRemove154_ptr);
            using DE_AllContHelpSkillRemove156_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_AllContHelpSkillRemove156_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_AllContHelpSkillRemove156_ptr);
            using DE_AttHPtoDstFP158_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_AttHPtoDstFP158_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_AttHPtoDstFP158_ptr);
            using DE_BattleMode_RecallCommonPlayer160_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_BattleMode_RecallCommonPlayer160_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_BattleMode_RecallCommonPlayer160_ptr);
            using DE_ContDamageTimeInc162_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_ContDamageTimeInc162_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_ContDamageTimeInc162_ptr);
            using DE_ContHelpTimeInc164_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_ContHelpTimeInc164_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_ContHelpTimeInc164_ptr);
            using DE_ConvertMonsterTarget166_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_ConvertMonsterTarget166_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_ConvertMonsterTarget166_ptr);
            using DE_ConvertTargetDest168_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_ConvertTargetDest168_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_ConvertTargetDest168_ptr);
            using DE_DamStun170_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_DamStun170_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_DamStun170_ptr);
            using DE_DetectTrap172_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_DetectTrap172_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_DetectTrap172_ptr);
            using DE_FPDec174_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_FPDec174_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_FPDec174_ptr);
            using DE_HPInc176_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_HPInc176_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_HPInc176_ptr);
            using DE_IncHPCircleParty178_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_IncHPCircleParty178_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_IncHPCircleParty178_ptr);
            using DE_IncreaseDP180_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_IncreaseDP180_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_IncreaseDP180_ptr);
            using DE_LateContDamageRemove182_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_LateContDamageRemove182_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_LateContDamageRemove182_ptr);
            using DE_LateContHelpForceRemove184_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_LateContHelpForceRemove184_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_LateContHelpForceRemove184_ptr);
            using DE_LateContHelpSkillRemove186_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_LateContHelpSkillRemove186_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_LateContHelpSkillRemove186_ptr);
            using DE_LayTrap188_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_LayTrap188_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_LayTrap188_ptr);
            using DE_MakeGuardTower190_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_MakeGuardTower190_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_MakeGuardTower190_ptr);
            using DE_MakePortalReturnBindPositionPartyMember192_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_MakePortalReturnBindPositionPartyMember192_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_MakePortalReturnBindPositionPartyMember192_ptr);
            using DE_MakeZeroAnimusRecallTimeOnce194_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_MakeZeroAnimusRecallTimeOnce194_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_MakeZeroAnimusRecallTimeOnce194_ptr);
            using DE_OthersContHelpSFRemove196_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_OthersContHelpSFRemove196_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_OthersContHelpSFRemove196_ptr);
            using DE_OverHealing198_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_OverHealing198_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_OverHealing198_ptr);
            using DE_Potion_AllContHelpSkillRemove_Once200_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_AllContHelpSkillRemove_Once200_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_AllContHelpSkillRemove_Once200_ptr);
            using DE_Potion_Buf_Extend202_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Buf_Extend202_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Buf_Extend202_ptr);
            using DE_Potion_Chaos_Dec_Time204_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Chaos_Dec_Time204_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Chaos_Dec_Time204_ptr);
            using DE_Potion_Chaos_Inc_Time206_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Chaos_Inc_Time206_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Chaos_Inc_Time206_ptr);
            using DE_Potion_CharReName208_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_CharReName208_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_CharReName208_ptr);
            using DE_Potion_Class_Refine210_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Class_Refine210_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Class_Refine210_ptr);
            using DE_Potion_Cont_Damage_Remove212_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Cont_Damage_Remove212_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Cont_Damage_Remove212_ptr);
            using DE_Potion_DecHalfSFContDam214_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_DecHalfSFContDam214_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_DecHalfSFContDam214_ptr);
            using DE_Potion_Exp_Increase_Absolute216_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Exp_Increase_Absolute216_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Exp_Increase_Absolute216_ptr);
            using DE_Potion_Exp_Increase_Percentage218_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Exp_Increase_Percentage218_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Exp_Increase_Percentage218_ptr);
            using DE_Potion_FP_In_Value220_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_FP_In_Value220_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_FP_In_Value220_ptr);
            using DE_Potion_Gold_Point222_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Gold_Point222_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Gold_Point222_ptr);
            using DE_Potion_HFP_Full_Recover224_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_HFP_Full_Recover224_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_HFP_Full_Recover224_ptr);
            using DE_Potion_HP_In_Value226_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_HP_In_Value226_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_HP_In_Value226_ptr);
            using DE_Potion_Race_Debuff_Clear_One228_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Race_Debuff_Clear_One228_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Race_Debuff_Clear_One228_ptr);
            using DE_Potion_Race_Debuff_Clear_Two230_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Race_Debuff_Clear_Two230_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Race_Debuff_Clear_Two230_ptr);
            using DE_Potion_RemoveAfterEffect232_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_RemoveAfterEffect232_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_RemoveAfterEffect232_ptr);
            using DE_Potion_RemoveAllContinousEffect234_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_RemoveAllContinousEffect234_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_RemoveAllContinousEffect234_ptr);
            using DE_Potion_Revival_Die_Position236_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Revival_Die_Position236_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Revival_Die_Position236_ptr);
            using DE_Potion_SP_In_Value238_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_SP_In_Value238_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_SP_In_Value238_ptr);
            using DE_Potion_Trunk_Extend240_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Potion_Trunk_Extend240_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Potion_Trunk_Extend240_ptr);
            using DE_Quick_Revival_Die_Position242_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Quick_Revival_Die_Position242_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Quick_Revival_Die_Position242_ptr);
            using DE_RecallCommonPlayer244_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_RecallCommonPlayer244_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_RecallCommonPlayer244_ptr);
            using DE_RecallPartyMember246_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_RecallPartyMember246_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_RecallPartyMember246_ptr);
            using DE_Recall_After_Stone248_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Recall_After_Stone248_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Recall_After_Stone248_ptr);
            using DE_RecoverAllReturnStateAnimusHPFull250_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_RecoverAllReturnStateAnimusHPFull250_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_RecoverAllReturnStateAnimusHPFull250_ptr);
            using DE_Recovery252_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Recovery252_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Recovery252_ptr);
            using DE_ReleaseMonsterTarget254_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_ReleaseMonsterTarget254_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_ReleaseMonsterTarget254_ptr);
            using DE_RemoveAllContHelp256_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_RemoveAllContHelp256_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_RemoveAllContHelp256_ptr);
            using DE_ReturnBindPosition258_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_ReturnBindPosition258_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_ReturnBindPosition258_ptr);
            using DE_SPDec260_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_SPDec260_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_SPDec260_ptr);
            using DE_STInc262_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_STInc262_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_STInc262_ptr);
            using DE_SelfDestruction264_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_SelfDestruction264_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_SelfDestruction264_ptr);
            using DE_SkillContHelpTimeInc266_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_SkillContHelpTimeInc266_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_SkillContHelpTimeInc266_ptr);
            using DE_Stun268_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Stun268_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Stun268_ptr);
            using DE_TeleportCommonPlayer270_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_TeleportCommonPlayer270_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_TeleportCommonPlayer270_ptr);
            using DE_Teleport_After_Stone272_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_Teleport_After_Stone272_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_Teleport_After_Stone272_ptr);
            using DE_TransDestHP274_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_TransDestHP274_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_TransDestHP274_ptr);
            using DE_TransMonsterHP276_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_TransMonsterHP276_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_TransMonsterHP276_ptr);
            using DE_ViewWeakPoint278_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*);
            using DE_ViewWeakPoint278_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, float, char*, DE_ViewWeakPoint278_ptr);
            using DTradeEqualPerson280_ptr = bool (WINAPIV*)(struct CPlayer*, struct CPlayer**);
            using DTradeEqualPerson280_clbk = bool (WINAPIV*)(struct CPlayer*, struct CPlayer**, DTradeEqualPerson280_ptr);
            using DXUtil_ConvertAnsiStringToGeneric282_ptr = void (WINAPIV*)(char*, char*, int);
            using DXUtil_ConvertAnsiStringToGeneric282_clbk = void (WINAPIV*)(char*, char*, int, DXUtil_ConvertAnsiStringToGeneric282_ptr);
            using DXUtil_ConvertAnsiStringToWide284_ptr = void (WINAPIV*)(wchar_t*, char*, int);
            using DXUtil_ConvertAnsiStringToWide284_clbk = void (WINAPIV*)(wchar_t*, char*, int, DXUtil_ConvertAnsiStringToWide284_ptr);
            using DXUtil_ConvertGenericStringToAnsi286_ptr = void (WINAPIV*)(char*, char*, int);
            using DXUtil_ConvertGenericStringToAnsi286_clbk = void (WINAPIV*)(char*, char*, int, DXUtil_ConvertGenericStringToAnsi286_ptr);
            using DXUtil_ConvertGenericStringToWide288_ptr = void (WINAPIV*)(wchar_t*, char*, int);
            using DXUtil_ConvertGenericStringToWide288_clbk = void (WINAPIV*)(wchar_t*, char*, int, DXUtil_ConvertGenericStringToWide288_ptr);
            using DXUtil_ConvertWideStringToAnsi290_ptr = void (WINAPIV*)(char*, wchar_t*, int);
            using DXUtil_ConvertWideStringToAnsi290_clbk = void (WINAPIV*)(char*, wchar_t*, int, DXUtil_ConvertWideStringToAnsi290_ptr);
            using DXUtil_ConvertWideStringToGeneric292_ptr = void (WINAPIV*)(char*, wchar_t*, int);
            using DXUtil_ConvertWideStringToGeneric292_clbk = void (WINAPIV*)(char*, wchar_t*, int, DXUtil_ConvertWideStringToGeneric292_ptr);
            using DXUtil_FindMediaFile294_ptr = HRESULT (WINAPIV*)(char*, char*);
            using DXUtil_FindMediaFile294_clbk = HRESULT (WINAPIV*)(char*, char*, DXUtil_FindMediaFile294_ptr);
            using DXUtil_GetDXSDKMediaPath296_ptr = char* (WINAPIV*)();
            using DXUtil_GetDXSDKMediaPath296_clbk = char* (WINAPIV*)(DXUtil_GetDXSDKMediaPath296_ptr);
            using DXUtil_ReadBoolRegKey298_ptr = HRESULT (WINAPIV*)(struct HKEY__*, char*, int*, int);
            using DXUtil_ReadBoolRegKey298_clbk = HRESULT (WINAPIV*)(struct HKEY__*, char*, int*, int, DXUtil_ReadBoolRegKey298_ptr);
            using DXUtil_ReadGuidRegKey300_ptr = HRESULT (WINAPIV*)(struct HKEY__*, char*, struct _GUID*, struct _GUID*);
            using DXUtil_ReadGuidRegKey300_clbk = HRESULT (WINAPIV*)(struct HKEY__*, char*, struct _GUID*, struct _GUID*, DXUtil_ReadGuidRegKey300_ptr);
            using DXUtil_ReadIntRegKey302_ptr = HRESULT (WINAPIV*)(struct HKEY__*, char*, unsigned int*, unsigned int);
            using DXUtil_ReadIntRegKey302_clbk = HRESULT (WINAPIV*)(struct HKEY__*, char*, unsigned int*, unsigned int, DXUtil_ReadIntRegKey302_ptr);
            using DXUtil_ReadStringRegKey304_ptr = HRESULT (WINAPIV*)(struct HKEY__*, char*, char*, unsigned int, char*);
            using DXUtil_ReadStringRegKey304_clbk = HRESULT (WINAPIV*)(struct HKEY__*, char*, char*, unsigned int, char*, DXUtil_ReadStringRegKey304_ptr);
            using DXUtil_Timer306_ptr = float (WINAPIV*)(TIMER_COMMAND);
            using DXUtil_Timer306_clbk = float (WINAPIV*)(TIMER_COMMAND, DXUtil_Timer306_ptr);
            using DXUtil_Trace308_ptr = void (WINAPIV*)(char*);
            using DXUtil_Trace308_clbk = void (WINAPIV*)(char*, DXUtil_Trace308_ptr);
            using DXUtil_WriteBoolRegKey310_ptr = HRESULT (WINAPIV*)(struct HKEY__*, char*, int);
            using DXUtil_WriteBoolRegKey310_clbk = HRESULT (WINAPIV*)(struct HKEY__*, char*, int, DXUtil_WriteBoolRegKey310_ptr);
            using DXUtil_WriteGuidRegKey312_ptr = HRESULT (WINAPIV*)(struct HKEY__*, char*, struct _GUID);
            using DXUtil_WriteGuidRegKey312_clbk = HRESULT (WINAPIV*)(struct HKEY__*, char*, struct _GUID, DXUtil_WriteGuidRegKey312_ptr);
            using DXUtil_WriteIntRegKey314_ptr = HRESULT (WINAPIV*)(struct HKEY__*, char*, unsigned int);
            using DXUtil_WriteIntRegKey314_clbk = HRESULT (WINAPIV*)(struct HKEY__*, char*, unsigned int, DXUtil_WriteIntRegKey314_ptr);
            using DXUtil_WriteStringRegKey316_ptr = HRESULT (WINAPIV*)(struct HKEY__*, char*, char*);
            using DXUtil_WriteStringRegKey316_clbk = HRESULT (WINAPIV*)(struct HKEY__*, char*, char*, DXUtil_WriteStringRegKey316_ptr);
            using DeCryptString318_ptr = void (WINAPIV*)(char*, int, char, uint16_t);
            using DeCryptString318_clbk = void (WINAPIV*)(char*, int, char, uint16_t, DeCryptString318_ptr);
            using DeCrypt_Move320_ptr = void (WINAPIV*)(char*, int, char, uint16_t);
            using DeCrypt_Move320_clbk = void (WINAPIV*)(char*, int, char, uint16_t, DeCrypt_Move320_ptr);
            using DebugDrawIndexedPrimitiveTLTex1321_ptr = void (WINAPIV*)(int, int, uint16_t*, void*);
            using DebugDrawIndexedPrimitiveTLTex1321_clbk = void (WINAPIV*)(int, int, uint16_t*, void*, DebugDrawIndexedPrimitiveTLTex1321_ptr);
            using DebugDrawIndexedPrimitiveUPTex1322_ptr = void (WINAPIV*)(int, int, uint16_t*, void*);
            using DebugDrawIndexedPrimitiveUPTex1322_clbk = void (WINAPIV*)(int, int, uint16_t*, void*, DebugDrawIndexedPrimitiveUPTex1322_ptr);
            using DebugPushEntityNum323_ptr = void (WINAPIV*)(uint32_t);
            using DebugPushEntityNum323_clbk = void (WINAPIV*)(uint32_t, DebugPushEntityNum323_ptr);
            using DebugPushMagicNum324_ptr = void (WINAPIV*)(uint32_t);
            using DebugPushMagicNum324_clbk = void (WINAPIV*)(uint32_t, DebugPushMagicNum324_ptr);
            using DetailTextureOffStage2325_ptr = void (WINAPIV*)();
            using DetailTextureOffStage2325_clbk = void (WINAPIV*)(DetailTextureOffStage2325_ptr);
            using DetailTextureOnStage2326_ptr = void (WINAPIV*)(struct _R3MATERIAL*);
            using DetailTextureOnStage2326_clbk = void (WINAPIV*)(struct _R3MATERIAL*, DetailTextureOnStage2326_ptr);
            using Dfree327_ptr = void (WINAPIV*)(void*);
            using Dfree327_clbk = void (WINAPIV*)(void*, Dfree327_ptr);
            using DisplayANSICodePageStrOutputDebug329_ptr = void (WINAPIV*)();
            using DisplayANSICodePageStrOutputDebug329_clbk = void (WINAPIV*)(DisplayANSICodePageStrOutputDebug329_ptr);
            using DisplayItemUpgInfo331_ptr = char* (WINAPIV*)(int, unsigned int);
            using DisplayItemUpgInfo331_clbk = char* (WINAPIV*)(int, unsigned int, DisplayItemUpgInfo331_ptr);
            using Dmalloc332_ptr = void* (WINAPIV*)(int);
            using Dmalloc332_clbk = void* (WINAPIV*)(int, Dmalloc332_ptr);
            using DotProduct333_ptr = float (WINAPIV*)(float*, float*);
            using DotProduct333_clbk = float (WINAPIV*)(float*, float*, DotProduct333_ptr);
            using EnCryptString395_ptr = void (WINAPIV*)(char*, int, char, uint16_t);
            using EnCryptString395_clbk = void (WINAPIV*)(char*, int, char, uint16_t, EnCryptString395_ptr);
            using EnCrypt_Move397_ptr = void (WINAPIV*)(char*, int, char, uint16_t);
            using EnCrypt_Move397_clbk = void (WINAPIV*)(char*, int, char, uint16_t, EnCrypt_Move397_ptr);
            using Error398_ptr = void (WINAPIV*)(char*, char*);
            using Error398_clbk = void (WINAPIV*)(char*, char*, Error398_ptr);
            using ErrorButRun399_ptr = void (WINAPIV*)(char*, char*);
            using ErrorButRun399_clbk = void (WINAPIV*)(char*, char*, ErrorButRun399_ptr);
            using ExtractVertex400_ptr = void (WINAPIV*)(uint16_t, int, float**, void*, float*, float);
            using ExtractVertex400_clbk = void (WINAPIV*)(uint16_t, int, float**, void*, float*, float, ExtractVertex400_ptr);
            using F402_ptr = unsigned int (WINAPIV*)(unsigned int, unsigned int, unsigned int);
            using F402_clbk = unsigned int (WINAPIV*)(unsigned int, unsigned int, unsigned int, F402_ptr);
            using F2DW403_ptr = uint32_t (WINAPIV*)(float);
            using F2DW403_clbk = uint32_t (WINAPIV*)(float, F2DW403_ptr);
            using FastBBoShasiToFrameBuffer404_ptr = void (WINAPIV*)(struct CLevel*, uint32_t);
            using FastBBoShasiToFrameBuffer404_clbk = void (WINAPIV*)(struct CLevel*, uint32_t, FastBBoShasiToFrameBuffer404_ptr);
            using FastCmp405_ptr = int64_t (WINAPIV*)(uint32_t*, uint32_t*, uint32_t);
            using FastCmp405_clbk = int64_t (WINAPIV*)(uint32_t*, uint32_t*, uint32_t, FastCmp405_ptr);
            using FatalError406_ptr = void (WINAPIV*)(char*, char*);
            using FatalError406_clbk = void (WINAPIV*)(char*, char*, FatalError406_ptr);
            using FindAllFile408_ptr = int (WINAPIV*)(char*, char**, int);
            using FindAllFile408_clbk = int (WINAPIV*)(char*, char**, int, FindAllFile408_ptr);
            using FindEmptyAnimus410_ptr = struct CAnimus* (WINAPIV*)(struct CAnimus*, int);
            using FindEmptyAnimus410_clbk = struct CAnimus* (WINAPIV*)(struct CAnimus*, int, FindEmptyAnimus410_ptr);
            using FindEmptyNPC412_ptr = struct CMerchant* (WINAPIV*)(struct CMerchant*, int);
            using FindEmptyNPC412_clbk = struct CMerchant* (WINAPIV*)(struct CMerchant*, int, FindEmptyNPC412_ptr);
            using FindEmptyParkingUnit414_ptr = struct CParkingUnit* (WINAPIV*)(struct CParkingUnit*, int);
            using FindEmptyParkingUnit414_clbk = struct CParkingUnit* (WINAPIV*)(struct CParkingUnit*, int, FindEmptyParkingUnit414_ptr);
            using FixTalikItemIndex416_ptr = int (WINAPIV*)(char);
            using FixTalikItemIndex416_clbk = int (WINAPIV*)(char, FixTalikItemIndex416_ptr);
            using FloatToShort418_ptr = void (WINAPIV*)(float*, int16_t*, int);
            using FloatToShort418_clbk = void (WINAPIV*)(float*, int16_t*, int, FloatToShort418_ptr);
            using Force32BitRendering419_ptr = void (WINAPIV*)();
            using Force32BitRendering419_clbk = void (WINAPIV*)(Force32BitRendering419_ptr);
            using ForceFullScreen420_ptr = void (WINAPIV*)();
            using ForceFullScreen420_clbk = void (WINAPIV*)(ForceFullScreen420_ptr);
            using FramebufferToBMP421_ptr = void (WINAPIV*)(char*);
            using FramebufferToBMP421_clbk = void (WINAPIV*)(char*, FramebufferToBMP421_ptr);
            using FramebufferToJPG422_ptr = void (WINAPIV*)(char*);
            using FramebufferToJPG422_clbk = void (WINAPIV*)(char*, FramebufferToJPG422_ptr);
            using FreePointer423_ptr = void (WINAPIV*)(void*);
            using FreePointer423_clbk = void (WINAPIV*)(void*, FreePointer423_ptr);
            using FtoDW424_ptr = uint32_t (WINAPIV*)(float);
            using FtoDW424_clbk = uint32_t (WINAPIV*)(float, FtoDW424_ptr);
            using FullScreenEffect425_ptr = void (WINAPIV*)();
            using FullScreenEffect425_clbk = void (WINAPIV*)(FullScreenEffect425_ptr);
            using G427_ptr = unsigned int (WINAPIV*)(unsigned int, unsigned int, unsigned int);
            using G427_clbk = unsigned int (WINAPIV*)(unsigned int, unsigned int, unsigned int, G427_ptr);
            using Get1st1024x1024TexRender428_ptr = struct CTextureRender* (WINAPIV*)();
            using Get1st1024x1024TexRender428_clbk = struct CTextureRender* (WINAPIV*)(Get1st1024x1024TexRender428_ptr);
            using Get1st256x256TexRender429_ptr = struct CTextureRender* (WINAPIV*)();
            using Get1st256x256TexRender429_clbk = struct CTextureRender* (WINAPIV*)(Get1st256x256TexRender429_ptr);
            using Get1st512x512TexRender430_ptr = struct CTextureRender* (WINAPIV*)();
            using Get1st512x512TexRender430_clbk = struct CTextureRender* (WINAPIV*)(Get1st512x512TexRender430_ptr);
            using Get2DTo3DTranslation431_ptr = void (WINAPIV*)(float**, float*, float*, float, float, float, float, float);
            using Get2DTo3DTranslation431_clbk = void (WINAPIV*)(float**, float*, float*, float, float, float, float, float, Get2DTo3DTranslation431_ptr);
            using Get2nd256x256TexRender432_ptr = struct CTextureRender* (WINAPIV*)();
            using Get2nd256x256TexRender432_clbk = struct CTextureRender* (WINAPIV*)(Get2nd256x256TexRender432_ptr);
            using Get2nd512x512TexRender433_ptr = struct CTextureRender* (WINAPIV*)();
            using Get2nd512x512TexRender433_clbk = struct CTextureRender* (WINAPIV*)(Get2nd512x512TexRender433_ptr);
            using Get3DSqrt435_ptr = float (WINAPIV*)(float*, float*);
            using Get3DSqrt435_clbk = float (WINAPIV*)(float*, float*, Get3DSqrt435_ptr);
            using GetAddrString437_ptr = void (WINAPIV*)(struct sockaddr_ipx*, char*);
            using GetAddrString437_clbk = void (WINAPIV*)(struct sockaddr_ipx*, char*, GetAddrString437_ptr);
            using GetAllPlayingWaves438_ptr = uint32_t (WINAPIV*)();
            using GetAllPlayingWaves438_clbk = uint32_t (WINAPIV*)(GetAllPlayingWaves438_ptr);
            using GetAngle440_ptr = float (WINAPIV*)(float*, float*);
            using GetAngle440_clbk = float (WINAPIV*)(float*, float*, GetAngle440_ptr);
            using GetAniMatrix441_ptr = void (WINAPIV*)(float**, struct _ANI_OBJECT*, float);
            using GetAniMatrix441_clbk = void (WINAPIV*)(float**, struct _ANI_OBJECT*, float, GetAniMatrix441_ptr);
            using GetAnimusFldFromExp443_ptr = struct _animus_fld* (WINAPIV*)(int, uint64_t);
            using GetAnimusFldFromExp443_clbk = struct _animus_fld* (WINAPIV*)(int, uint64_t, GetAnimusFldFromExp443_ptr);
            using GetAnimusFldFromLv445_ptr = struct _animus_fld* (WINAPIV*)(int, unsigned int);
            using GetAnimusFldFromLv445_clbk = struct _animus_fld* (WINAPIV*)(int, unsigned int, GetAnimusFldFromLv445_ptr);
            using GetAvailableVidMem446_ptr = uint32_t (WINAPIV*)();
            using GetAvailableVidMem446_clbk = uint32_t (WINAPIV*)(GetAvailableVidMem446_ptr);
            using GetBBoxRotate447_ptr = void (WINAPIV*)(float**, float**, float*, float*, float);
            using GetBBoxRotate447_clbk = void (WINAPIV*)(float**, float**, float*, float*, float, GetBBoxRotate447_ptr);
            using GetBezierPoint448_ptr = void (WINAPIV*)(float**, float**, uint32_t, float);
            using GetBezierPoint448_clbk = void (WINAPIV*)(float**, float**, uint32_t, float, GetBezierPoint448_ptr);
            using GetBillboardMatrix449_ptr = void (WINAPIV*)(struct D3DXMATRIX*, float*, float*);
            using GetBillboardMatrix449_clbk = void (WINAPIV*)(struct D3DXMATRIX*, float*, float*, GetBillboardMatrix449_ptr);
            using GetBitAfterDowngrade451_ptr = unsigned int (WINAPIV*)(unsigned int, char);
            using GetBitAfterDowngrade451_clbk = unsigned int (WINAPIV*)(unsigned int, char, GetBitAfterDowngrade451_ptr);
            using GetBitAfterSetLimSocket453_ptr = unsigned int (WINAPIV*)(char);
            using GetBitAfterSetLimSocket453_clbk = unsigned int (WINAPIV*)(char, GetBitAfterSetLimSocket453_ptr);
            using GetBitAfterUpgrade455_ptr = unsigned int (WINAPIV*)(unsigned int, unsigned int, char);
            using GetBitAfterUpgrade455_clbk = unsigned int (WINAPIV*)(unsigned int, unsigned int, char, GetBitAfterUpgrade455_ptr);
            using GetBoldSubLeng456_ptr = int32_t (WINAPIV*)(char*);
            using GetBoldSubLeng456_clbk = int32_t (WINAPIV*)(char*, GetBoldSubLeng456_ptr);
            using GetBumpFactor457_ptr = void (WINAPIV*)(float*);
            using GetBumpFactor457_clbk = void (WINAPIV*)(float*, GetBumpFactor457_ptr);
            using GetCameraPos458_ptr = void (WINAPIV*)(float**);
            using GetCameraPos458_clbk = void (WINAPIV*)(float**, GetCameraPos458_ptr);
            using GetCharFromKey459_ptr = int64_t (WINAPIV*)();
            using GetCharFromKey459_clbk = int64_t (WINAPIV*)(GetCharFromKey459_ptr);
            using GetCheckTimeFromCombineExCheckKey461_ptr = unsigned int (WINAPIV*)(unsigned int);
            using GetCheckTimeFromCombineExCheckKey461_clbk = unsigned int (WINAPIV*)(unsigned int, GetCheckTimeFromCombineExCheckKey461_ptr);
            using GetColorToColorAlpha462_ptr = uint32_t (WINAPIV*)(uint32_t, uint32_t, float);
            using GetColorToColorAlpha462_clbk = uint32_t (WINAPIV*)(uint32_t, uint32_t, float, GetColorToColorAlpha462_ptr);
            using GetConnectTime_AddBySec464_ptr = unsigned int (WINAPIV*)(int);
            using GetConnectTime_AddBySec464_clbk = unsigned int (WINAPIV*)(int, GetConnectTime_AddBySec464_ptr);
            using GetConsoleStateB465_ptr = int64_t (WINAPIV*)(int);
            using GetConsoleStateB465_clbk = int64_t (WINAPIV*)(int, GetConsoleStateB465_ptr);
            using GetContrast466_ptr = void (WINAPIV*)(uint8_t*);
            using GetContrast466_clbk = void (WINAPIV*)(uint8_t*, GetContrast466_ptr);
            using GetCurDay468_ptr = int (WINAPIV*)();
            using GetCurDay468_clbk = int (WINAPIV*)(GetCurDay468_ptr);
            using GetCurHour470_ptr = int (WINAPIV*)();
            using GetCurHour470_clbk = int (WINAPIV*)(GetCurHour470_ptr);
            using GetCurrentDay472_ptr = int (WINAPIV*)();
            using GetCurrentDay472_clbk = int (WINAPIV*)(GetCurrentDay472_ptr);
            using GetCurrentHour474_ptr = int (WINAPIV*)();
            using GetCurrentHour474_clbk = int (WINAPIV*)(GetCurrentHour474_ptr);
            using GetCurrentMin476_ptr = int (WINAPIV*)();
            using GetCurrentMin476_clbk = int (WINAPIV*)(GetCurrentMin476_ptr);
            using GetCurrentMonth478_ptr = int (WINAPIV*)();
            using GetCurrentMonth478_clbk = int (WINAPIV*)(GetCurrentMonth478_ptr);
            using GetCurrentSec480_ptr = int (WINAPIV*)();
            using GetCurrentSec480_clbk = int (WINAPIV*)(GetCurrentSec480_ptr);
            using GetCurrentYear482_ptr = int (WINAPIV*)();
            using GetCurrentYear482_clbk = int (WINAPIV*)(GetCurrentYear482_ptr);
            using GetCurwDay484_ptr = int (WINAPIV*)();
            using GetCurwDay484_clbk = int (WINAPIV*)(GetCurwDay484_ptr);
            using GetD3DTexture485_ptr = struct IDirect3DTexture8* (WINAPIV*)(uint16_t, uint16_t, _D3DFORMAT, uint16_t, uint16_t, _D3DFORMAT, uint8_t*, uint32_t);
            using GetD3DTexture485_clbk = struct IDirect3DTexture8* (WINAPIV*)(uint16_t, uint16_t, _D3DFORMAT, uint16_t, uint16_t, _D3DFORMAT, uint8_t*, uint32_t, GetD3DTexture485_ptr);
            using GetD3DTextureFromBuffer486_ptr = struct IDirect3DTexture8* (WINAPIV*)(uint8_t*, uint32_t, uint32_t);
            using GetD3DTextureFromBuffer486_clbk = struct IDirect3DTexture8* (WINAPIV*)(uint8_t*, uint32_t, uint32_t, GetD3DTextureFromBuffer486_ptr);
            using GetD3dDevice487_ptr = struct IDirect3DDevice8* (WINAPIV*)();
            using GetD3dDevice487_clbk = struct IDirect3DDevice8* (WINAPIV*)(GetD3dDevice487_ptr);
            using GetDDSTexFromBuffer488_ptr = void* (WINAPIV*)(uint16_t, uint16_t, uint32_t, uint8_t*);
            using GetDDSTexFromBuffer488_clbk = void* (WINAPIV*)(uint16_t, uint16_t, uint32_t, uint8_t*, GetDDSTexFromBuffer488_ptr);
            using GetDateStrAfterDay490_ptr = bool (WINAPIV*)(char*, int, uint16_t);
            using GetDateStrAfterDay490_clbk = bool (WINAPIV*)(char*, int, uint16_t, GetDateStrAfterDay490_ptr);
            using GetDateTimeStr492_ptr = bool (WINAPIV*)(char*);
            using GetDateTimeStr492_clbk = bool (WINAPIV*)(char*, GetDateTimeStr492_ptr);
            using GetDefItemUpgSocketNum494_ptr = char (WINAPIV*)(int, int);
            using GetDefItemUpgSocketNum494_clbk = char (WINAPIV*)(int, int, GetDefItemUpgSocketNum494_ptr);
            using GetDensityFromPos495_ptr = float (WINAPIV*)(float*);
            using GetDensityFromPos495_clbk = float (WINAPIV*)(float*, GetDensityFromPos495_ptr);
            using GetDirection497_ptr = void (WINAPIV*)(float**, float**, float**, float);
            using GetDirection497_clbk = void (WINAPIV*)(float**, float**, float**, float, GetDirection497_ptr);
            using GetDist498_ptr = float (WINAPIV*)(float*, float*);
            using GetDist498_clbk = float (WINAPIV*)(float*, float*, GetDist498_ptr);
            using GetDmallocCnt499_ptr = uint32_t (WINAPIV*)();
            using GetDmallocCnt499_clbk = uint32_t (WINAPIV*)(GetDmallocCnt499_ptr);
            using GetDmallocSize500_ptr = int64_t (WINAPIV*)();
            using GetDmallocSize500_clbk = int64_t (WINAPIV*)(GetDmallocSize500_ptr);
            using GetDuration501_ptr = float (WINAPIV*)();
            using GetDuration501_clbk = float (WINAPIV*)(GetDuration501_ptr);
            using GetDynamicLight502_ptr = void (WINAPIV*)(uint32_t, float*, float*, uint32_t*, void**, uint32_t*);
            using GetDynamicLight502_clbk = void (WINAPIV*)(uint32_t, float*, float*, uint32_t*, void**, uint32_t*, GetDynamicLight502_ptr);
            using GetDynamicLightBBox503_ptr = void (WINAPIV*)(uint32_t, float**, float**);
            using GetDynamicLightBBox503_clbk = void (WINAPIV*)(uint32_t, float**, float**, GetDynamicLightBBox503_ptr);
            using GetDynamicLightNum504_ptr = uint32_t (WINAPIV*)();
            using GetDynamicLightNum504_clbk = uint32_t (WINAPIV*)(GetDynamicLightNum504_ptr);
            using GetEXT505_ptr = void (WINAPIV*)(char*, char*);
            using GetEXT505_clbk = void (WINAPIV*)(char*, char*, GetEXT505_ptr);
            using GetEmptyGuildData507_ptr = struct CGuild* (WINAPIV*)(struct CGuild*, int);
            using GetEmptyGuildData507_clbk = struct CGuild* (WINAPIV*)(struct CGuild*, int, GetEmptyGuildData507_ptr);
            using GetEntityAnimationPos508_ptr = void (WINAPIV*)(float*, struct CParticle*);
            using GetEntityAnimationPos508_clbk = void (WINAPIV*)(float*, struct CParticle*, GetEntityAnimationPos508_ptr);
            using GetExcelIndexFromCombineExCheckKey510_ptr = uint16_t (WINAPIV*)(unsigned int);
            using GetExcelIndexFromCombineExCheckKey510_clbk = uint16_t (WINAPIV*)(unsigned int, GetExcelIndexFromCombineExCheckKey510_ptr);
            using GetFPS511_ptr = float (WINAPIV*)();
            using GetFPS511_clbk = float (WINAPIV*)(GetFPS511_ptr);
            using GetFileSize512_ptr = int64_t (WINAPIV*)(char*);
            using GetFileSize512_clbk = int64_t (WINAPIV*)(char*, GetFileSize512_ptr);
            using GetFileSizeAndMergeFile513_ptr = uint32_t (WINAPIV*)(char*);
            using GetFileSizeAndMergeFile513_clbk = uint32_t (WINAPIV*)(char*, GetFileSizeAndMergeFile513_ptr);
            using GetFinalBilloardMatrix514_ptr = void (WINAPIV*)(float**, float**, struct CParticle*, uint32_t);
            using GetFinalBilloardMatrix514_clbk = void (WINAPIV*)(float**, float**, struct CParticle*, uint32_t, GetFinalBilloardMatrix514_ptr);
            using GetFinalPath515_ptr = void (WINAPIV*)(char*, char*);
            using GetFinalPath515_clbk = void (WINAPIV*)(char*, char*, GetFinalPath515_ptr);
            using GetFloatMod516_ptr = float (WINAPIV*)(float, float);
            using GetFloatMod516_clbk = float (WINAPIV*)(float, float, GetFloatMod516_ptr);
            using GetFogColor517_ptr = uint32_t (WINAPIV*)();
            using GetFogColor517_clbk = uint32_t (WINAPIV*)(GetFogColor517_ptr);
            using GetFrustumNormalPlane518_ptr = void (WINAPIV*)(float**);
            using GetFrustumNormalPlane518_clbk = void (WINAPIV*)(float**, GetFrustumNormalPlane518_ptr);
            using GetGlobalMusicVolume519_ptr = float (WINAPIV*)();
            using GetGlobalMusicVolume519_clbk = float (WINAPIV*)(GetGlobalMusicVolume519_ptr);
            using GetGlobalWavVolume520_ptr = float (WINAPIV*)();
            using GetGlobalWavVolume520_clbk = float (WINAPIV*)(GetGlobalWavVolume520_ptr);
            using GetGravity521_ptr = float (WINAPIV*)(float);
            using GetGravity521_clbk = float (WINAPIV*)(float, GetGravity521_ptr);
            using GetGuildDataFromSerial523_ptr = struct CGuild* (WINAPIV*)(struct CGuild*, int, unsigned int);
            using GetGuildDataFromSerial523_clbk = struct CGuild* (WINAPIV*)(struct CGuild*, int, unsigned int, GetGuildDataFromSerial523_ptr);
            using GetGuildPtrFromName525_ptr = struct CGuild* (WINAPIV*)(struct CGuild*, int, char*);
            using GetGuildPtrFromName525_clbk = struct CGuild* (WINAPIV*)(struct CGuild*, int, char*, GetGuildPtrFromName525_ptr);
            using GetHeadMatrix526_ptr = void (WINAPIV*)(struct D3DXMATRIX*, struct R3Camera*, float, int32_t, int32_t);
            using GetHeadMatrix526_clbk = void (WINAPIV*)(struct D3DXMATRIX*, struct R3Camera*, float, int32_t, int32_t, GetHeadMatrix526_ptr);
            using GetIPAddress528_ptr = int (WINAPIV*)(char*);
            using GetIPAddress528_clbk = int (WINAPIV*)(char*, GetIPAddress528_ptr);
            using GetIPAddress530_ptr = int (WINAPIV*)(struct sockaddr_in*);
            using GetIPAddress530_clbk = int (WINAPIV*)(struct sockaddr_in*, GetIPAddress530_ptr);
            using GetIPAddress532_ptr = unsigned int (WINAPIV*)();
            using GetIPAddress532_clbk = unsigned int (WINAPIV*)(GetIPAddress532_ptr);
            using GetInverseTransformVertex533_ptr = void (WINAPIV*)(float*, float*);
            using GetInverseTransformVertex533_clbk = void (WINAPIV*)(float*, float*, GetInverseTransformVertex533_ptr);
            using GetItemDurPoint535_ptr = int (WINAPIV*)(int, int);
            using GetItemDurPoint535_clbk = int (WINAPIV*)(int, int, GetItemDurPoint535_ptr);
            using GetItemEquipCivil537_ptr = char* (WINAPIV*)(int, int);
            using GetItemEquipCivil537_clbk = char* (WINAPIV*)(int, int, GetItemEquipCivil537_ptr);
            using GetItemEquipGrade539_ptr = int (WINAPIV*)(int, char*);
            using GetItemEquipGrade539_clbk = int (WINAPIV*)(int, char*, GetItemEquipGrade539_ptr);
            using GetItemEquipGrade541_ptr = int (WINAPIV*)(int, int);
            using GetItemEquipGrade541_clbk = int (WINAPIV*)(int, int, GetItemEquipGrade541_ptr);
            using GetItemEquipLevel543_ptr = int (WINAPIV*)(int, int);
            using GetItemEquipLevel543_clbk = int (WINAPIV*)(int, int, GetItemEquipLevel543_ptr);
            using GetItemEquipMastery545_ptr = struct _EQUIP_MASTERY_LIM* (WINAPIV*)(int, int, int*);
            using GetItemEquipMastery545_clbk = struct _EQUIP_MASTERY_LIM* (WINAPIV*)(int, int, int*, GetItemEquipMastery545_ptr);
            using GetItemEquipUpLevel547_ptr = int (WINAPIV*)(int, int);
            using GetItemEquipUpLevel547_clbk = int (WINAPIV*)(int, int, GetItemEquipUpLevel547_ptr);
            using GetItemGoldPoint549_ptr = int (WINAPIV*)(int, int, int, char*);
            using GetItemGoldPoint549_clbk = int (WINAPIV*)(int, int, int, char*, GetItemGoldPoint549_ptr);
            using GetItemGrade551_ptr = char (WINAPIV*)(int, int);
            using GetItemGrade551_clbk = char (WINAPIV*)(int, int, GetItemGrade551_ptr);
            using GetItemKillPoint553_ptr = int (WINAPIV*)(int, int, int, char*);
            using GetItemKillPoint553_clbk = int (WINAPIV*)(int, int, int, char*, GetItemKillPoint553_ptr);
            using GetItemKindCode555_ptr = char (WINAPIV*)(int);
            using GetItemKindCode555_clbk = char (WINAPIV*)(int, GetItemKindCode555_ptr);
            using GetItemKorName557_ptr = char* (WINAPIV*)(int, int);
            using GetItemKorName557_clbk = char* (WINAPIV*)(int, int, GetItemKorName557_ptr);
            using GetItemProcPoint559_ptr = int (WINAPIV*)(int, int, int, char*);
            using GetItemProcPoint559_clbk = int (WINAPIV*)(int, int, int, char*, GetItemProcPoint559_ptr);
            using GetItemStdPoint561_ptr = int (WINAPIV*)(int, int, int, char*);
            using GetItemStdPoint561_clbk = int (WINAPIV*)(int, int, int, char*, GetItemStdPoint561_ptr);
            using GetItemStdPrice563_ptr = int (WINAPIV*)(int, int, int, char*);
            using GetItemStdPrice563_clbk = int (WINAPIV*)(int, int, int, char*, GetItemStdPrice563_ptr);
            using GetItemStoragePrice565_ptr = int (WINAPIV*)(int, int, int);
            using GetItemStoragePrice565_clbk = int (WINAPIV*)(int, int, int, GetItemStoragePrice565_ptr);
            using GetItemTableCode567_ptr = int (WINAPIV*)(char*);
            using GetItemTableCode567_clbk = int (WINAPIV*)(char*, GetItemTableCode567_ptr);
            using GetItemUpgLimSocket569_ptr = char (WINAPIV*)(unsigned int);
            using GetItemUpgLimSocket569_clbk = char (WINAPIV*)(unsigned int, GetItemUpgLimSocket569_ptr);
            using GetItemUpgedLv571_ptr = char (WINAPIV*)(unsigned int);
            using GetItemUpgedLv571_clbk = char (WINAPIV*)(unsigned int, GetItemUpgedLv571_ptr);
            using GetJPGDimensions572_ptr = int64_t (WINAPIV*)(char*, unsigned int*, unsigned int*);
            using GetJPGDimensions572_clbk = int64_t (WINAPIV*)(char*, unsigned int*, unsigned int*, GetJPGDimensions572_ptr);
            using GetKorLocalTime574_ptr = unsigned int (WINAPIV*)();
            using GetKorLocalTime574_clbk = unsigned int (WINAPIV*)(GetKorLocalTime574_ptr);
            using GetLastWriteFileTime576_ptr = bool (WINAPIV*)(char*, struct _FILETIME*);
            using GetLastWriteFileTime576_clbk = bool (WINAPIV*)(char*, struct _FILETIME*, GetLastWriteFileTime576_ptr);
            using GetLightMapColor577_ptr = uint32_t (WINAPIV*)(float*, int);
            using GetLightMapColor577_clbk = uint32_t (WINAPIV*)(float*, int, GetLightMapColor577_ptr);
            using GetLightMapSurface578_ptr = void* (WINAPIV*)(int);
            using GetLightMapSurface578_clbk = void* (WINAPIV*)(int, GetLightMapSurface578_ptr);
            using GetLightMapTexSize579_ptr = uint32_t (WINAPIV*)();
            using GetLightMapTexSize579_clbk = uint32_t (WINAPIV*)(GetLightMapTexSize579_ptr);
            using GetLocalDate581_ptr = unsigned int (WINAPIV*)();
            using GetLocalDate581_clbk = unsigned int (WINAPIV*)(GetLocalDate581_ptr);
            using GetLoopTime583_ptr = unsigned int (WINAPIV*)();
            using GetLoopTime583_clbk = unsigned int (WINAPIV*)(GetLoopTime583_ptr);
            using GetMacAddrString585_ptr = void (WINAPIV*)(char*, uint64_t);
            using GetMacAddrString585_clbk = void (WINAPIV*)(char*, uint64_t, GetMacAddrString585_ptr);
            using GetMainMaterial586_ptr = struct _R3MATERIAL* (WINAPIV*)();
            using GetMainMaterial586_clbk = struct _R3MATERIAL* (WINAPIV*)(GetMainMaterial586_ptr);
            using GetMainMaterialNum587_ptr = uint32_t (WINAPIV*)();
            using GetMainMaterialNum587_clbk = uint32_t (WINAPIV*)(GetMainMaterialNum587_ptr);
            using GetMatLightFromColor588_ptr = void (WINAPIV*)(struct _D3DLIGHT8*, struct _D3DMATERIAL8*, uint32_t, float);
            using GetMatLightFromColor588_clbk = void (WINAPIV*)(struct _D3DLIGHT8*, struct _D3DMATERIAL8*, uint32_t, float, GetMatLightFromColor588_ptr);
            using GetMaterialNameNum589_ptr = int64_t (WINAPIV*)(struct _R3MATERIAL*);
            using GetMaterialNameNum589_clbk = int64_t (WINAPIV*)(struct _R3MATERIAL*, GetMaterialNameNum589_ptr);
            using GetMatrixFrom3DSMAXMatrix590_ptr = void (WINAPIV*)(float**);
            using GetMatrixFrom3DSMAXMatrix590_clbk = void (WINAPIV*)(float**, GetMatrixFrom3DSMAXMatrix590_ptr);
            using GetMatrixFromAtoB591_ptr = void (WINAPIV*)(float**, float*, float*);
            using GetMatrixFromAtoB591_clbk = void (WINAPIV*)(float**, float*, float*, GetMatrixFromAtoB591_ptr);
            using GetMatrixFromAtoB2592_ptr = void (WINAPIV*)(float**, float*, float*);
            using GetMatrixFromAtoB2592_clbk = void (WINAPIV*)(float**, float*, float*, GetMatrixFromAtoB2592_ptr);
            using GetMatrixFromVector593_ptr = void (WINAPIV*)(float**, float*, float*);
            using GetMatrixFromVector593_clbk = void (WINAPIV*)(float**, float*, float*, GetMatrixFromVector593_ptr);
            using GetMaxParamFromExp595_ptr = unsigned int (WINAPIV*)(int, uint64_t);
            using GetMaxParamFromExp595_clbk = unsigned int (WINAPIV*)(int, uint64_t, GetMaxParamFromExp595_ptr);
            using GetMaxResKind597_ptr = int (WINAPIV*)();
            using GetMaxResKind597_clbk = int (WINAPIV*)(GetMaxResKind597_ptr);
            using GetMergeFileManager598_ptr = struct CMergeFileManager* (WINAPIV*)();
            using GetMergeFileManager598_clbk = struct CMergeFileManager* (WINAPIV*)(GetMergeFileManager598_ptr);
            using GetMipMapSkipSize599_ptr = int64_t (WINAPIV*)(struct _DDSURFACEDESC2*, uint32_t, uint32_t, uint32_t);
            using GetMipMapSkipSize599_clbk = int64_t (WINAPIV*)(struct _DDSURFACEDESC2*, uint32_t, uint32_t, uint32_t, GetMipMapSkipSize599_ptr);
            using GetMotionBlurLength600_ptr = float (WINAPIV*)();
            using GetMotionBlurLength600_clbk = float (WINAPIV*)(GetMotionBlurLength600_ptr);
            using GetMultiLayerTime601_ptr = float (WINAPIV*)();
            using GetMultiLayerTime601_clbk = float (WINAPIV*)(GetMultiLayerTime601_ptr);
            using GetNextDay603_ptr = int (WINAPIV*)();
            using GetNextDay603_clbk = int (WINAPIV*)(GetNextDay603_ptr);
            using GetNormal604_ptr = void (WINAPIV*)(float*, float*, float*, float*);
            using GetNormal604_clbk = void (WINAPIV*)(float*, float*, float*, float*, GetNormal604_ptr);
            using GetNowDateTime606_ptr = void (WINAPIV*)(char*);
            using GetNowDateTime606_clbk = void (WINAPIV*)(char*, GetNowDateTime606_ptr);
            using GetNowFrame607_ptr = float (WINAPIV*)();
            using GetNowFrame607_clbk = float (WINAPIV*)(GetNowFrame607_ptr);
            using GetNowFreeJmallocSize608_ptr = int64_t (WINAPIV*)();
            using GetNowFreeJmallocSize608_clbk = int64_t (WINAPIV*)(GetNowFreeJmallocSize608_ptr);
            using GetNowHWnd609_ptr = HWND (WINAPIV*)();
            using GetNowHWnd609_clbk = HWND (WINAPIV*)(GetNowHWnd609_ptr);
            using GetNowJmallocSize610_ptr = int64_t (WINAPIV*)();
            using GetNowJmallocSize610_clbk = int64_t (WINAPIV*)(GetNowJmallocSize610_ptr);
            using GetNowPath611_ptr = int64_t (WINAPIV*)(char*);
            using GetNowPath611_clbk = int64_t (WINAPIV*)(char*, GetNowPath611_ptr);
            using GetNowR3D3DTexCnt612_ptr = int64_t (WINAPIV*)();
            using GetNowR3D3DTexCnt612_clbk = int64_t (WINAPIV*)(GetNowR3D3DTexCnt612_ptr);
            using GetNowR3TexCnt613_ptr = int64_t (WINAPIV*)();
            using GetNowR3TexCnt613_clbk = int64_t (WINAPIV*)(GetNowR3TexCnt613_ptr);
            using GetNowTexMemSize614_ptr = uint32_t (WINAPIV*)();
            using GetNowTexMemSize614_clbk = uint32_t (WINAPIV*)(GetNowTexMemSize614_ptr);
            using GetNumAllFile616_ptr = int (WINAPIV*)(char*);
            using GetNumAllFile616_clbk = int (WINAPIV*)(char*, GetNumAllFile616_ptr);
            using GetObjectMatrix617_ptr = void (WINAPIV*)(float**, uint16_t, struct _ANI_OBJECT*, float);
            using GetObjectMatrix617_clbk = void (WINAPIV*)(float**, uint16_t, struct _ANI_OBJECT*, float, GetObjectMatrix617_ptr);
            using GetOldRenderTarget618_ptr = struct IDirect3DSurface8* (WINAPIV*)();
            using GetOldRenderTarget618_clbk = struct IDirect3DSurface8* (WINAPIV*)(GetOldRenderTarget618_ptr);
            using GetOldStencilZ619_ptr = struct IDirect3DSurface8* (WINAPIV*)();
            using GetOldStencilZ619_clbk = struct IDirect3DSurface8* (WINAPIV*)(GetOldStencilZ619_ptr);
            using GetOneNameFromPath620_ptr = uint32_t (WINAPIV*)(char*, char*, uint32_t*);
            using GetOneNameFromPath620_clbk = uint32_t (WINAPIV*)(char*, char*, uint32_t*, GetOneNameFromPath620_ptr);
            using GetOutLineColor621_ptr = uint32_t (WINAPIV*)();
            using GetOutLineColor621_clbk = uint32_t (WINAPIV*)(GetOutLineColor621_ptr);
            using GetOutLineColorFont16622_ptr = uint32_t (WINAPIV*)();
            using GetOutLineColorFont16622_clbk = uint32_t (WINAPIV*)(GetOutLineColorFont16622_ptr);
            using GetOutLineColorFont24623_ptr = uint32_t (WINAPIV*)();
            using GetOutLineColorFont24623_clbk = uint32_t (WINAPIV*)(GetOutLineColorFont24623_ptr);
            using GetPlaneCrossPoint624_ptr = uint32_t (WINAPIV*)(float*, float*, float*, float*, float);
            using GetPlaneCrossPoint624_clbk = uint32_t (WINAPIV*)(float*, float*, float*, float*, float, GetPlaneCrossPoint624_ptr);
            using GetPlaneCrossPoint625_ptr = int64_t (WINAPIV*)(float*, float*, float*, float*, float, float);
            using GetPlaneCrossPoint625_clbk = int64_t (WINAPIV*)(float*, float*, float*, float*, float, float, GetPlaneCrossPoint625_ptr);
            using GetPointCamera626_ptr = void (WINAPIV*)(float**);
            using GetPointCamera626_clbk = void (WINAPIV*)(float**, GetPointCamera626_ptr);
            using GetPosByDistFromATOB627_ptr = void (WINAPIV*)(float**, float*, float*, float);
            using GetPosByDistFromATOB627_clbk = void (WINAPIV*)(float**, float*, float*, float, GetPosByDistFromATOB627_ptr);
            using GetPreParticleList628_ptr = _PRE_PARTICLE_LIST* (WINAPIV*)(int);
            using GetPreParticleList628_clbk = _PRE_PARTICLE_LIST* (WINAPIV*)(int, GetPreParticleList628_ptr);
            using GetPreParticleListNum629_ptr = int64_t (WINAPIV*)();
            using GetPreParticleListNum629_clbk = int64_t (WINAPIV*)(GetPreParticleListNum629_ptr);
            using GetPrevDay631_ptr = int (WINAPIV*)();
            using GetPrevDay631_clbk = int (WINAPIV*)(GetPrevDay631_ptr);
            using GetProjectMatrix632_ptr = void (WINAPIV*)(struct D3DXMATRIX*);
            using GetProjectMatrix632_clbk = void (WINAPIV*)(struct D3DXMATRIX*, GetProjectMatrix632_ptr);
            using GetPtrPlayerFromAccount634_ptr = struct CPlayer* (WINAPIV*)(struct CPlayer*, int, char*);
            using GetPtrPlayerFromAccount634_clbk = struct CPlayer* (WINAPIV*)(struct CPlayer*, int, char*, GetPtrPlayerFromAccount634_ptr);
            using GetPtrPlayerFromAccountSerial636_ptr = struct CPlayer* (WINAPIV*)(struct CPlayer*, int, unsigned int);
            using GetPtrPlayerFromAccountSerial636_clbk = struct CPlayer* (WINAPIV*)(struct CPlayer*, int, unsigned int, GetPtrPlayerFromAccountSerial636_ptr);
            using GetPtrPlayerFromCharSerial638_ptr = struct CPlayer* (WINAPIV*)(int, unsigned int);
            using GetPtrPlayerFromCharSerial638_clbk = struct CPlayer* (WINAPIV*)(int, unsigned int, GetPtrPlayerFromCharSerial638_ptr);
            using GetPtrPlayerFromName640_ptr = struct CPlayer* (WINAPIV*)(struct CPlayer*, int, char*);
            using GetPtrPlayerFromName640_clbk = struct CPlayer* (WINAPIV*)(struct CPlayer*, int, char*, GetPtrPlayerFromName640_ptr);
            using GetPtrPlayerFromSerial642_ptr = struct CPlayer* (WINAPIV*)(struct CPlayer*, int, unsigned int);
            using GetPtrPlayerFromSerial642_clbk = struct CPlayer* (WINAPIV*)(struct CPlayer*, int, unsigned int, GetPtrPlayerFromSerial642_ptr);
            using GetQLen644_ptr = int (WINAPIV*)(float*, float*);
            using GetQLen644_clbk = int (WINAPIV*)(float*, float*, GetQLen644_ptr);
            using GetQuaternionFromVector645_ptr = void (WINAPIV*)(float*, float*);
            using GetQuaternionFromVector645_clbk = void (WINAPIV*)(float*, float*, GetQuaternionFromVector645_ptr);
            using GetQuaternionFromVector646_ptr = void (WINAPIV*)(float*, float*, float*);
            using GetQuaternionFromVector646_clbk = void (WINAPIV*)(float*, float*, float*, GetQuaternionFromVector646_ptr);
            using GetR3TexManageFlag647_ptr = uint32_t (WINAPIV*)();
            using GetR3TexManageFlag647_clbk = uint32_t (WINAPIV*)(GetR3TexManageFlag647_ptr);
            using GetRandOrNum648_ptr = int64_t (WINAPIV*)(FILE*, float*, float*);
            using GetRandOrNum648_clbk = int64_t (WINAPIV*)(FILE*, float*, float*, GetRandOrNum648_ptr);
            using GetReactArea650_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, struct _react_area*, unsigned int, char*, char*);
            using GetReactArea650_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, struct _react_area*, unsigned int, char*, char*, GetReactArea650_ptr);
            using GetReactObject652_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, struct _react_obj*, bool, unsigned int, char*, char*);
            using GetReactObject652_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, struct _react_obj*, bool, unsigned int, char*, char*, GetReactObject652_ptr);
            using GetReflectionState653_ptr = int64_t (WINAPIV*)();
            using GetReflectionState653_clbk = int64_t (WINAPIV*)(GetReflectionState653_ptr);
            using GetRegionIndex655_ptr = int (WINAPIV*)(int, unsigned int, unsigned int, unsigned int, unsigned int);
            using GetRegionIndex655_clbk = int (WINAPIV*)(int, unsigned int, unsigned int, unsigned int, unsigned int, GetRegionIndex655_ptr);
            using GetRegionName657_ptr = char* (WINAPIV*)(int, unsigned int, unsigned int, unsigned int, unsigned int);
            using GetRegionName657_clbk = char* (WINAPIV*)(int, unsigned int, unsigned int, unsigned int, unsigned int, GetRegionName657_ptr);
            using GetRewardItemNumChangeClass659_ptr = int (WINAPIV*)(struct _class_fld*);
            using GetRewardItemNumChangeClass659_clbk = int (WINAPIV*)(struct _class_fld*, GetRewardItemNumChangeClass659_ptr);
            using GetSFLevel661_ptr = int (WINAPIV*)(int, unsigned int);
            using GetSFLevel661_clbk = int (WINAPIV*)(int, unsigned int, GetSFLevel661_ptr);
            using GetShadowRenderTexture662_ptr = struct CTextureRender* (WINAPIV*)();
            using GetShadowRenderTexture662_clbk = struct CTextureRender* (WINAPIV*)(GetShadowRenderTexture662_ptr);
            using GetShadowTexture663_ptr = struct IDirect3DTexture8* (WINAPIV*)();
            using GetShadowTexture663_clbk = struct IDirect3DTexture8* (WINAPIV*)(GetShadowTexture663_ptr);
            using GetSocketName665_ptr = bool (WINAPIV*)(uint64_t, char*);
            using GetSocketName665_clbk = bool (WINAPIV*)(uint64_t, char*, GetSocketName665_ptr);
            using GetSqrt667_ptr = float (WINAPIV*)(float*, float*);
            using GetSqrt667_clbk = float (WINAPIV*)(float*, float*, GetSqrt667_ptr);
            using GetStaffMastery669_ptr = int (WINAPIV*)(unsigned int*);
            using GetStaffMastery669_clbk = int (WINAPIV*)(unsigned int*, GetStaffMastery669_ptr);
            using GetStateFullScreenEffect670_ptr = uint32_t (WINAPIV*)();
            using GetStateFullScreenEffect670_clbk = uint32_t (WINAPIV*)(GetStateFullScreenEffect670_ptr);
            using GetSubDayStr672_ptr = void (WINAPIV*)(int, char*);
            using GetSubDayStr672_clbk = void (WINAPIV*)(int, char*, GetSubDayStr672_ptr);
            using GetSwimU673_ptr = float (WINAPIV*)(float);
            using GetSwimU673_clbk = float (WINAPIV*)(float, GetSwimU673_ptr);
            using GetSwimV674_ptr = float (WINAPIV*)(float);
            using GetSwimV674_clbk = float (WINAPIV*)(float, GetSwimV674_ptr);
            using GetTalikFromSocket676_ptr = char (WINAPIV*)(unsigned int, char);
            using GetTalikFromSocket676_clbk = char (WINAPIV*)(unsigned int, char, GetTalikFromSocket676_ptr);
            using GetTempBuffer677_ptr = void* (WINAPIV*)();
            using GetTempBuffer677_clbk = void* (WINAPIV*)(GetTempBuffer677_ptr);
            using GetTempBufferSize678_ptr = uint32_t (WINAPIV*)();
            using GetTempBufferSize678_clbk = uint32_t (WINAPIV*)(GetTempBufferSize678_ptr);
            using GetTextureMatrix679_ptr = int (WINAPIV*)(struct _R3MATERIAL*, int, struct D3DXMATRIX*, float);
            using GetTextureMatrix679_clbk = int (WINAPIV*)(struct _R3MATERIAL*, int, struct D3DXMATRIX*, float, GetTextureMatrix679_ptr);
            using GetTileAniTextureAddId680_ptr = int64_t (WINAPIV*)(struct _R3MATERIAL*, int, float);
            using GetTileAniTextureAddId680_clbk = int64_t (WINAPIV*)(struct _R3MATERIAL*, int, float, GetTileAniTextureAddId680_ptr);
            using GetTodayStr682_ptr = void (WINAPIV*)(char*);
            using GetTodayStr682_clbk = void (WINAPIV*)(char*, GetTodayStr682_ptr);
            using GetTokenFloat683_ptr = uint32_t (WINAPIV*)(char*, float*);
            using GetTokenFloat683_clbk = uint32_t (WINAPIV*)(char*, float*, GetTokenFloat683_ptr);
            using GetTotalFrame684_ptr = uint32_t (WINAPIV*)();
            using GetTotalFrame684_clbk = uint32_t (WINAPIV*)(GetTotalFrame684_ptr);
            using GetTotalFreeJmallocSize685_ptr = int64_t (WINAPIV*)();
            using GetTotalFreeJmallocSize685_clbk = int64_t (WINAPIV*)(GetTotalFreeJmallocSize685_ptr);
            using GetTotalVertexBufferCnt686_ptr = uint32_t (WINAPIV*)();
            using GetTotalVertexBufferCnt686_clbk = uint32_t (WINAPIV*)(GetTotalVertexBufferCnt686_ptr);
            using GetTotalVertexBufferSize687_ptr = uint32_t (WINAPIV*)();
            using GetTotalVertexBufferSize687_clbk = uint32_t (WINAPIV*)(GetTotalVertexBufferSize687_ptr);
            using GetTransformVertex688_ptr = int64_t (WINAPIV*)(float**, float*);
            using GetTransformVertex688_clbk = int64_t (WINAPIV*)(float**, float*, GetTransformVertex688_ptr);
            using GetUsePcCashType690_ptr = int (WINAPIV*)(char, int);
            using GetUsePcCashType690_clbk = int (WINAPIV*)(char, int, GetUsePcCashType690_ptr);
            using GetVertexFromBVertex691_ptr = void (WINAPIV*)(float*, void*, struct _BSP_READ_M_GROUP*);
            using GetVertexFromBVertex691_clbk = void (WINAPIV*)(float*, void*, struct _BSP_READ_M_GROUP*, GetVertexFromBVertex691_ptr);
            using GetVertexFromFVertex692_ptr = void (WINAPIV*)(float*, void*, struct _BSP_READ_M_GROUP*);
            using GetVertexFromFVertex692_clbk = void (WINAPIV*)(float*, void*, struct _BSP_READ_M_GROUP*, GetVertexFromFVertex692_ptr);
            using GetVertexFromWVertex693_ptr = void (WINAPIV*)(float*, void*, struct _BSP_READ_M_GROUP*);
            using GetVertexFromWVertex693_clbk = void (WINAPIV*)(float*, void*, struct _BSP_READ_M_GROUP*, GetVertexFromWVertex693_ptr);
            using GetViewMatrix694_ptr = void (WINAPIV*)(struct D3DXMATRIX*);
            using GetViewMatrix694_clbk = void (WINAPIV*)(struct D3DXMATRIX*, GetViewMatrix694_ptr);
            using GetViewVector695_ptr = void (WINAPIV*)(float*);
            using GetViewVector695_clbk = void (WINAPIV*)(float*, GetViewVector695_ptr);
            using GetWeaponClass697_ptr = char (WINAPIV*)(int);
            using GetWeaponClass697_clbk = char (WINAPIV*)(int, GetWeaponClass697_ptr);
            using GetWebBrowserRect698_ptr = void (WINAPIV*)(struct tagRECT*);
            using GetWebBrowserRect698_clbk = void (WINAPIV*)(struct tagRECT*, GetWebBrowserRect698_ptr);
            using GetXAngle699_ptr = float (WINAPIV*)(float*, float*);
            using GetXAngle699_clbk = float (WINAPIV*)(float*, float*, GetXAngle699_ptr);
            using GetYAngle700_ptr = float (WINAPIV*)(float*, float*);
            using GetYAngle700_clbk = float (WINAPIV*)(float*, float*, GetYAngle700_ptr);
            using GetYAngle701_ptr = int (WINAPIV*)(float*, float*, float*);
            using GetYAngle701_clbk = int (WINAPIV*)(float*, float*, float*, GetYAngle701_ptr);
            using GetYAngle703_ptr = float (WINAPIV*)(float*, float*);
            using GetYAngle703_clbk = float (WINAPIV*)(float*, float*, GetYAngle703_ptr);
            using GetYBillboardMatrix704_ptr = void (WINAPIV*)(struct D3DXMATRIX*, int32_t, struct D3DXVECTOR3*);
            using GetYBillboardMatrix704_clbk = void (WINAPIV*)(struct D3DXMATRIX*, int32_t, struct D3DXVECTOR3*, GetYBillboardMatrix704_ptr);
            using GetYBillboardMatrix705_ptr = void (WINAPIV*)(struct D3DXMATRIX*, float*, float*);
            using GetYBillboardMatrix705_clbk = void (WINAPIV*)(struct D3DXMATRIX*, float*, float*, GetYBillboardMatrix705_ptr);
            using GetZBillboardMatrix706_ptr = void (WINAPIV*)(struct D3DXMATRIX*, float*, float*);
            using GetZBillboardMatrix706_clbk = void (WINAPIV*)(struct D3DXMATRIX*, float*, float*, GetZBillboardMatrix706_ptr);
            using Get_CashEvent_Name708_ptr = void (WINAPIV*)(char, char*);
            using Get_CashEvent_Name708_clbk = void (WINAPIV*)(char, char*, Get_CashEvent_Name708_ptr);
            using H710_ptr = unsigned int (WINAPIV*)(unsigned int, unsigned int, unsigned int);
            using H710_clbk = unsigned int (WINAPIV*)(unsigned int, unsigned int, unsigned int, H710_ptr);
            using HtoA712_ptr = void (WINAPIV*)(char*, char*, int);
            using HtoA712_clbk = void (WINAPIV*)(char*, char*, int, HtoA712_ptr);
            using HtoB714_ptr = char (WINAPIV*)(char);
            using HtoB714_clbk = char (WINAPIV*)(char, HtoB714_ptr);
            using I716_ptr = unsigned int (WINAPIV*)(unsigned int, unsigned int, unsigned int);
            using I716_clbk = unsigned int (WINAPIV*)(unsigned int, unsigned int, unsigned int, I716_ptr);
            using IM_LoadWave717_ptr = uint32_t (WINAPIV*)(char*, uint32_t);
            using IM_LoadWave717_clbk = uint32_t (WINAPIV*)(char*, uint32_t, IM_LoadWave717_ptr);
            using IM_PlayWave718_ptr = void (WINAPIV*)(uint32_t, float, float);
            using IM_PlayWave718_clbk = void (WINAPIV*)(uint32_t, float, float, IM_PlayWave718_ptr);
            using IM_ReleaseAllWaves719_ptr = void (WINAPIV*)();
            using IM_ReleaseAllWaves719_clbk = void (WINAPIV*)(IM_ReleaseAllWaves719_ptr);
            using IM_ReleaseWave720_ptr = void (WINAPIV*)(uint32_t);
            using IM_ReleaseWave720_clbk = void (WINAPIV*)(uint32_t, IM_ReleaseWave720_ptr);
            using IM_SetLoopCntWave721_ptr = void (WINAPIV*)(uint32_t, uint32_t);
            using IM_SetLoopCntWave721_clbk = void (WINAPIV*)(uint32_t, uint32_t, IM_SetLoopCntWave721_ptr);
            using IM_SetWaveVolumeAndPan722_ptr = void (WINAPIV*)(uint32_t, float, float);
            using IM_SetWaveVolumeAndPan722_clbk = void (WINAPIV*)(uint32_t, float, float, IM_SetWaveVolumeAndPan722_ptr);
            using IM_StopWave723_ptr = void (WINAPIV*)(uint32_t);
            using IM_StopWave723_clbk = void (WINAPIV*)(uint32_t, IM_StopWave723_ptr);
            using IPConvertingStringToByte725_ptr = bool (WINAPIV*)(char*, char*);
            using IPConvertingStringToByte725_clbk = bool (WINAPIV*)(char*, char*, IPConvertingStringToByte725_ptr);
            using IndexFromPImgThunkData726_ptr = unsigned int (WINAPIV*)(struct _IMAGE_THUNK_DATA64*, struct _IMAGE_THUNK_DATA64*);
            using IndexFromPImgThunkData726_clbk = unsigned int (WINAPIV*)(struct _IMAGE_THUNK_DATA64*, struct _IMAGE_THUNK_DATA64*, IndexFromPImgThunkData726_ptr);
            using InitBlurShader727_ptr = int64_t (WINAPIV*)();
            using InitBlurShader727_clbk = int64_t (WINAPIV*)(InitBlurShader727_ptr);
            using InitCheatCommand729_ptr = void (WINAPIV*)(struct CHEAT_COMMAND*, char*);
            using InitCheatCommand729_clbk = void (WINAPIV*)(struct CHEAT_COMMAND*, char*, InitCheatCommand729_ptr);
            using InitConsole730_ptr = void (WINAPIV*)();
            using InitConsole730_clbk = void (WINAPIV*)(InitConsole730_ptr);
            using InitCore731_ptr = void (WINAPIV*)();
            using InitCore731_clbk = void (WINAPIV*)(InitCore731_ptr);
            using InitFullScreenEffect732_ptr = void (WINAPIV*)();
            using InitFullScreenEffect732_clbk = void (WINAPIV*)(InitFullScreenEffect732_ptr);
            using InitFunctionKey733_ptr = void (WINAPIV*)(char*);
            using InitFunctionKey733_clbk = void (WINAPIV*)(char*, InitFunctionKey733_ptr);
            using InitJmalloc734_ptr = int64_t (WINAPIV*)(int);
            using InitJmalloc734_clbk = int64_t (WINAPIV*)(int, InitJmalloc734_ptr);
            using InitMasteryFormula736_ptr = void (WINAPIV*)(struct CRecordData*, struct CRecordData*);
            using InitMasteryFormula736_clbk = void (WINAPIV*)(struct CRecordData*, struct CRecordData*, InitMasteryFormula736_ptr);
            using InitR3Engine737_ptr = void (WINAPIV*)(int);
            using InitR3Engine737_clbk = void (WINAPIV*)(int, InitR3Engine737_ptr);
            using InitR3Particle738_ptr = void (WINAPIV*)();
            using InitR3Particle738_clbk = void (WINAPIV*)(InitR3Particle738_ptr);
            using InitR3SoundSystem739_ptr = int64_t (WINAPIV*)(char*);
            using InitR3SoundSystem739_clbk = int64_t (WINAPIV*)(char*, InitR3SoundSystem739_ptr);
            using InitR3Text740_ptr = void (WINAPIV*)();
            using InitR3Text740_clbk = void (WINAPIV*)(InitR3Text740_ptr);
            using InitSpriteManager741_ptr = void (WINAPIV*)();
            using InitSpriteManager741_clbk = void (WINAPIV*)(InitSpriteManager741_ptr);
            using InitWebBrowser742_ptr = void (WINAPIV*)(int32_t);
            using InitWebBrowser742_clbk = void (WINAPIV*)(int32_t, InitWebBrowser742_ptr);
            using InsertConsoleStringQ743_ptr = void (WINAPIV*)(char*);
            using InsertConsoleStringQ743_clbk = void (WINAPIV*)(char*, InsertConsoleStringQ743_ptr);
            using InsertDynamicLight744_ptr = int64_t (WINAPIV*)(float*, float, uint32_t, void*, int, uint32_t);
            using InsertDynamicLight744_clbk = int64_t (WINAPIV*)(float*, float, uint32_t, void*, int, uint32_t, InsertDynamicLight744_ptr);
            using InterpolatedNoise_1745_ptr = float (WINAPIV*)(float);
            using InterpolatedNoise_1745_clbk = float (WINAPIV*)(float, InterpolatedNoise_1745_ptr);
            using InterpolatedNoise_1746_ptr = float (WINAPIV*)(float, float);
            using InterpolatedNoise_1746_clbk = float (WINAPIV*)(float, float, InterpolatedNoise_1746_ptr);
            using IsAbrItem748_ptr = int (WINAPIV*)(int, int);
            using IsAbrItem748_clbk = int (WINAPIV*)(int, int, IsAbrItem748_ptr);
            using IsAddAbleTalikToItem750_ptr = bool (WINAPIV*)(char, uint16_t, unsigned int, uint16_t, int*);
            using IsAddAbleTalikToItem750_clbk = bool (WINAPIV*)(char, uint16_t, unsigned int, uint16_t, int*, IsAddAbleTalikToItem750_ptr);
            using IsAniTex751_ptr = int64_t (WINAPIV*)(char*);
            using IsAniTex751_clbk = int64_t (WINAPIV*)(char*, IsAniTex751_ptr);
            using IsBBoxInFrustum752_ptr = int64_t (WINAPIV*)(float*, float*);
            using IsBBoxInFrustum752_clbk = int64_t (WINAPIV*)(float*, float*, IsBBoxInFrustum752_ptr);
            using IsBBoxInFrustum753_ptr = int64_t (WINAPIV*)(int16_t*, int16_t*);
            using IsBBoxInFrustum753_clbk = int64_t (WINAPIV*)(int16_t*, int16_t*, IsBBoxInFrustum753_ptr);
            using IsBeNearStore755_ptr = struct CItemStore* (WINAPIV*)(struct CPlayer*, int);
            using IsBeNearStore755_clbk = struct CItemStore* (WINAPIV*)(struct CPlayer*, int, IsBeNearStore755_ptr);
            using IsBothRayAABB756_ptr = int64_t (WINAPIV*)(float*, float*, float*, float*);
            using IsBothRayAABB756_clbk = int64_t (WINAPIV*)(float*, float*, float*, float*, IsBothRayAABB756_ptr);
            using IsBothRayAABB757_ptr = int (WINAPIV*)(int16_t*, int16_t*, float*, float*);
            using IsBothRayAABB757_clbk = int (WINAPIV*)(int16_t*, int16_t*, float*, float*, IsBothRayAABB757_ptr);
            using IsCashItem759_ptr = int (WINAPIV*)(char, unsigned int);
            using IsCashItem759_clbk = int (WINAPIV*)(char, unsigned int, IsCashItem759_ptr);
            using IsCollisionBBox760_ptr = int64_t (WINAPIV*)(float*, float*, float*, float*);
            using IsCollisionBBox760_clbk = int64_t (WINAPIV*)(float*, float*, float*, float*, IsCollisionBBox760_ptr);
            using IsCollisionBBox761_ptr = int64_t (WINAPIV*)(int16_t*, int16_t*, float*, float*);
            using IsCollisionBBox761_clbk = int64_t (WINAPIV*)(int16_t*, int16_t*, float*, float*, IsCollisionBBox761_ptr);
            using IsCollisionBBox762_ptr = int64_t (WINAPIV*)(int16_t*, int16_t*, int16_t*, int16_t*);
            using IsCollisionBBox762_clbk = int64_t (WINAPIV*)(int16_t*, int16_t*, int16_t*, int16_t*, IsCollisionBBox762_ptr);
            using IsCollisionBBoxPoint763_ptr = int64_t (WINAPIV*)(float*, float*, float*);
            using IsCollisionBBoxPoint763_clbk = int64_t (WINAPIV*)(float*, float*, float*, IsCollisionBBoxPoint763_ptr);
            using IsCollisionBBoxPoint764_ptr = int64_t (WINAPIV*)(int16_t*, int16_t*, float*);
            using IsCollisionBBoxPoint764_clbk = int64_t (WINAPIV*)(int16_t*, int16_t*, float*, IsCollisionBBoxPoint764_ptr);
            using IsCollisionBBoxToFace765_ptr = int64_t (WINAPIV*)(float**, float*, float*, float*);
            using IsCollisionBBoxToFace765_clbk = int64_t (WINAPIV*)(float**, float*, float*, float*, IsCollisionBBoxToFace765_ptr);
            using IsDayChanged767_ptr = bool (WINAPIV*)(int*);
            using IsDayChanged767_clbk = bool (WINAPIV*)(int*, IsDayChanged767_ptr);
            using IsEnableBBoShasiShader768_ptr = int64_t (WINAPIV*)();
            using IsEnableBBoShasiShader768_clbk = int64_t (WINAPIV*)(IsEnableBBoShasiShader768_ptr);
            using IsEnableShader769_ptr = int64_t (WINAPIV*)(uint32_t);
            using IsEnableShader769_clbk = int64_t (WINAPIV*)(uint32_t, IsEnableShader769_ptr);
            using IsExchangeItem771_ptr = int (WINAPIV*)(int, int);
            using IsExchangeItem771_clbk = int (WINAPIV*)(int, int, IsExchangeItem771_ptr);
            using IsExistDarkHoleOpenGate773_ptr = bool (WINAPIV*)();
            using IsExistDarkHoleOpenGate773_clbk = bool (WINAPIV*)(IsExistDarkHoleOpenGate773_ptr);
            using IsExistFile774_ptr = int64_t (WINAPIV*)(char*);
            using IsExistFile774_clbk = int64_t (WINAPIV*)(char*, IsExistFile774_ptr);
            using IsExistFileAndMergeFile775_ptr = int64_t (WINAPIV*)(char*);
            using IsExistFileAndMergeFile775_clbk = int64_t (WINAPIV*)(char*, IsExistFileAndMergeFile775_ptr);
            using IsExistFromSoundSpt776_ptr = int64_t (WINAPIV*)(uint32_t);
            using IsExistFromSoundSpt776_clbk = int64_t (WINAPIV*)(uint32_t, IsExistFromSoundSpt776_ptr);
            using IsExistItem778_ptr = int (WINAPIV*)(int, int);
            using IsExistItem778_clbk = int (WINAPIV*)(int, int, IsExistItem778_ptr);
            using IsFadeIn779_ptr = int64_t (WINAPIV*)();
            using IsFadeIn779_clbk = int64_t (WINAPIV*)(IsFadeIn779_ptr);
            using IsFadeOut780_ptr = int64_t (WINAPIV*)();
            using IsFadeOut780_clbk = int64_t (WINAPIV*)(IsFadeOut780_ptr);
            using IsFading781_ptr = int64_t (WINAPIV*)();
            using IsFading781_clbk = int64_t (WINAPIV*)(IsFading781_ptr);
            using IsFarDistance782_ptr = int64_t (WINAPIV*)(struct _BSP_LEAF*);
            using IsFarDistance782_clbk = int64_t (WINAPIV*)(struct _BSP_LEAF*, IsFarDistance782_ptr);
            using IsGroundableItem784_ptr = int (WINAPIV*)(int, int);
            using IsGroundableItem784_clbk = int (WINAPIV*)(int, int, IsGroundableItem784_ptr);
            using IsInitCore785_ptr = int64_t (WINAPIV*)();
            using IsInitCore785_clbk = int64_t (WINAPIV*)(IsInitCore785_ptr);
            using IsInitR3Engine786_ptr = int64_t (WINAPIV*)();
            using IsInitR3Engine786_clbk = int64_t (WINAPIV*)(IsInitR3Engine786_ptr);
            using IsItemCombineExKind788_ptr = int (WINAPIV*)(int);
            using IsItemCombineExKind788_clbk = int (WINAPIV*)(int, IsItemCombineExKind788_ptr);
            using IsItemEquipCivil790_ptr = int (WINAPIV*)(int, int, char);
            using IsItemEquipCivil790_clbk = int (WINAPIV*)(int, int, char, IsItemEquipCivil790_ptr);
            using IsItemSerialNum792_ptr = int (WINAPIV*)(int);
            using IsItemSerialNum792_clbk = int (WINAPIV*)(int, IsItemSerialNum792_ptr);
            using IsLoadedMaterial793_ptr = int64_t (WINAPIV*)();
            using IsLoadedMaterial793_clbk = int64_t (WINAPIV*)(IsLoadedMaterial793_ptr);
            using IsLoadedRTCamera794_ptr = int64_t (WINAPIV*)();
            using IsLoadedRTCamera794_clbk = int64_t (WINAPIV*)(IsLoadedRTCamera794_ptr);
            using IsLoadedRTMovie795_ptr = int64_t (WINAPIV*)();
            using IsLoadedRTMovie795_clbk = int64_t (WINAPIV*)(IsLoadedRTMovie795_ptr);
            using IsMagicLight796_ptr = int64_t (WINAPIV*)(uint32_t);
            using IsMagicLight796_clbk = int64_t (WINAPIV*)(uint32_t, IsMagicLight796_ptr);
            using IsNormalAccountUsable798_ptr = int (WINAPIV*)(int, int);
            using IsNormalAccountUsable798_clbk = int (WINAPIV*)(int, int, IsNormalAccountUsable798_ptr);
            using IsOneBackCollision799_ptr = int64_t (WINAPIV*)();
            using IsOneBackCollision799_clbk = int64_t (WINAPIV*)(IsOneBackCollision799_ptr);
            using IsOtherInvalidObjNear801_ptr = char (WINAPIV*)(struct CGameObject*, float*, struct CTrap*, struct _TrapItem_fld*);
            using IsOtherInvalidObjNear801_clbk = char (WINAPIV*)(struct CGameObject*, float*, struct CTrap*, struct _TrapItem_fld*, IsOtherInvalidObjNear801_ptr);
            using IsOtherTowerNear803_ptr = bool (WINAPIV*)(struct CGameObject*, float*, struct CGuardTower*);
            using IsOtherTowerNear803_clbk = bool (WINAPIV*)(struct CGameObject*, float*, struct CGuardTower*, IsOtherTowerNear803_ptr);
            using IsOverLapItem805_ptr = int (WINAPIV*)(int);
            using IsOverLapItem805_clbk = int (WINAPIV*)(int, IsOverLapItem805_ptr);
            using IsParticle806_ptr = int64_t (WINAPIV*)(char*);
            using IsParticle806_clbk = int64_t (WINAPIV*)(char*, IsParticle806_ptr);
            using IsPlayMP3807_ptr = int64_t (WINAPIV*)();
            using IsPlayMP3807_clbk = int64_t (WINAPIV*)(IsPlayMP3807_ptr);
            using IsProtectItem809_ptr = int (WINAPIV*)(int);
            using IsProtectItem809_clbk = int (WINAPIV*)(int, IsProtectItem809_ptr);
            using IsRTMoviePlaying810_ptr = int64_t (WINAPIV*)();
            using IsRTMoviePlaying810_clbk = int64_t (WINAPIV*)(IsRTMoviePlaying810_ptr);
            using IsRayAABB811_ptr = int64_t (WINAPIV*)(float*, float*, float*, float*, float**);
            using IsRayAABB811_clbk = int64_t (WINAPIV*)(float*, float*, float*, float*, float**, IsRayAABB811_ptr);
            using IsRayAABB812_ptr = int64_t (WINAPIV*)(float**, int16_t*, float*, float*, float**);
            using IsRayAABB812_clbk = int64_t (WINAPIV*)(float**, int16_t*, float*, float*, float**, IsRayAABB812_ptr);
            using IsRenderTargetFrameBuffer813_ptr = int64_t (WINAPIV*)();
            using IsRenderTargetFrameBuffer813_clbk = int64_t (WINAPIV*)(IsRenderTargetFrameBuffer813_ptr);
            using IsRepairableItem815_ptr = int (WINAPIV*)(int, int);
            using IsRepairableItem815_clbk = int (WINAPIV*)(int, int, IsRepairableItem815_ptr);
            using IsRightIndexedUP816_ptr = int64_t (WINAPIV*)(int, uint16_t*, int);
            using IsRightIndexedUP816_clbk = int64_t (WINAPIV*)(int, uint16_t*, int, IsRightIndexedUP816_ptr);
            using IsSQLValidString818_ptr = bool (WINAPIV*)(char*);
            using IsSQLValidString818_clbk = bool (WINAPIV*)(char*, IsSQLValidString818_ptr);
            using IsSaveItem820_ptr = int (WINAPIV*)(int);
            using IsSaveItem820_clbk = int (WINAPIV*)(int, IsSaveItem820_ptr);
            using IsSellItem822_ptr = int (WINAPIV*)(int, int);
            using IsSellItem822_clbk = int (WINAPIV*)(int, int, IsSellItem822_ptr);
            using IsServerMode823_ptr = int64_t (WINAPIV*)();
            using IsServerMode823_clbk = int64_t (WINAPIV*)(IsServerMode823_ptr);
            using IsStorageCodeWithItemKind825_ptr = int (WINAPIV*)(int, int);
            using IsStorageCodeWithItemKind825_clbk = int (WINAPIV*)(int, int, IsStorageCodeWithItemKind825_ptr);
            using IsStorageRange827_ptr = int (WINAPIV*)(char, char);
            using IsStorageRange827_clbk = int (WINAPIV*)(char, char, IsStorageRange827_ptr);
            using IsTalikAboutTol829_ptr = int (WINAPIV*)(int);
            using IsTalikAboutTol829_clbk = int (WINAPIV*)(int, IsTalikAboutTol829_ptr);
            using IsTargeting831_ptr = bool (WINAPIV*)(struct CGameObject*);
            using IsTargeting831_clbk = bool (WINAPIV*)(struct CGameObject*, IsTargeting831_ptr);
            using IsTextureFormatOk832_ptr = int64_t (WINAPIV*)(_D3DFORMAT, _D3DFORMAT);
            using IsTextureFormatOk832_clbk = int64_t (WINAPIV*)(_D3DFORMAT, _D3DFORMAT, IsTextureFormatOk832_ptr);
            using IsTimeItem834_ptr = int (WINAPIV*)(char, unsigned int);
            using IsTimeItem834_clbk = int (WINAPIV*)(char, unsigned int, IsTimeItem834_ptr);
            using IsTrunkIOAble836_ptr = int (WINAPIV*)(int, int);
            using IsTrunkIOAble836_clbk = int (WINAPIV*)(int, int, IsTrunkIOAble836_ptr);
            using IsUsableTempEffectAtStoneState838_ptr = bool (WINAPIV*)(int);
            using IsUsableTempEffectAtStoneState838_clbk = bool (WINAPIV*)(int, IsUsableTempEffectAtStoneState838_ptr);
            using IsWebBrowserMode839_ptr = int64_t (WINAPIV*)();
            using IsWebBrowserMode839_clbk = int64_t (WINAPIV*)(IsWebBrowserMode839_ptr);
            using Jmalloc840_ptr = uint8_t* (WINAPIV*)(int);
            using Jmalloc840_clbk = uint8_t* (WINAPIV*)(int, Jmalloc840_ptr);
            using JpegFileToRGB841_ptr = uint8_t* (WINAPIV*)(char*, unsigned int*, unsigned int*);
            using JpegFileToRGB841_clbk = uint8_t* (WINAPIV*)(char*, unsigned int*, unsigned int*, JpegFileToRGB841_ptr);
            using LightMappingTex1842_ptr = void (WINAPIV*)(struct _BSP_MAT_GROUP*);
            using LightMappingTex1842_clbk = void (WINAPIV*)(struct _BSP_MAT_GROUP*, LightMappingTex1842_ptr);
            using LinearInterpolate843_ptr = float (WINAPIV*)(float, float, float);
            using LinearInterpolate843_clbk = float (WINAPIV*)(float, float, float, LinearInterpolate843_ptr);
            using LoadAndCreateShader844_ptr = int32_t (WINAPIV*)(char*, uint32_t*, uint32_t, int, uint32_t*);
            using LoadAndCreateShader844_clbk = int32_t (WINAPIV*)(char*, uint32_t*, uint32_t, int, uint32_t*, LoadAndCreateShader844_ptr);
            using LoadBmp845_ptr = uint8_t* (WINAPIV*)(char*, int*, int*);
            using LoadBmp845_clbk = uint8_t* (WINAPIV*)(char*, int*, int*, LoadBmp845_ptr);
            using LoadCreateShader846_ptr = int32_t (WINAPIV*)(char*, uint32_t*, uint32_t, int, uint32_t*);
            using LoadCreateShader846_clbk = int32_t (WINAPIV*)(char*, uint32_t*, uint32_t, int, uint32_t*, LoadCreateShader846_ptr);
            using LoadIndependenceR3M847_ptr = struct _R3MATERIAL* (WINAPIV*)(char*);
            using LoadIndependenceR3M847_clbk = struct _R3MATERIAL* (WINAPIV*)(char*, LoadIndependenceR3M847_ptr);
            using LoadLightMap848_ptr = void (WINAPIV*)(char*);
            using LoadLightMap848_clbk = void (WINAPIV*)(char*, LoadLightMap848_ptr);
            using LoadMainMaterial849_ptr = struct _R3MATERIAL* (WINAPIV*)(char*);
            using LoadMainMaterial849_clbk = struct _R3MATERIAL* (WINAPIV*)(char*, LoadMainMaterial849_ptr);
            using LoadMainR3M850_ptr = struct _R3MATERIAL* (WINAPIV*)(char*);
            using LoadMainR3M850_clbk = struct _R3MATERIAL* (WINAPIV*)(char*, LoadMainR3M850_ptr);
            using LoadMasteryLimFile852_ptr = bool (WINAPIV*)(char*);
            using LoadMasteryLimFile852_clbk = bool (WINAPIV*)(char*, LoadMasteryLimFile852_ptr);
            using LoadR3MP3853_ptr = int64_t (WINAPIV*)(char*, uint32_t);
            using LoadR3MP3853_clbk = int64_t (WINAPIV*)(char*, uint32_t, LoadR3MP3853_ptr);
            using LoadR3T854_ptr = void (WINAPIV*)(R3Texture*);
            using LoadR3T854_clbk = void (WINAPIV*)(R3Texture*, LoadR3T854_ptr);
            using LoadR3TLightMap855_ptr = _LIGHTMAP** (WINAPIV*)(R3Texture*, _D3DFORMAT);
            using LoadR3TLightMap855_clbk = _LIGHTMAP** (WINAPIV*)(R3Texture*, _D3DFORMAT, LoadR3TLightMap855_ptr);
            using LoadR3X856_ptr = void (WINAPIV*)(char*);
            using LoadR3X856_clbk = void (WINAPIV*)(char*, LoadR3X856_ptr);
            using LoadRegionData858_ptr = bool (WINAPIV*)(int, char**, char*);
            using LoadRegionData858_clbk = bool (WINAPIV*)(int, char**, char*, LoadRegionData858_ptr);
            using LoadStreamR3MP3859_ptr = int64_t (WINAPIV*)(char*, uint32_t);
            using LoadStreamR3MP3859_clbk = int64_t (WINAPIV*)(char*, uint32_t, LoadStreamR3MP3859_ptr);
            using LoadSubMaterial860_ptr = struct _R3MATERIAL* (WINAPIV*)(char*);
            using LoadSubMaterial860_clbk = struct _R3MATERIAL* (WINAPIV*)(char*, LoadSubMaterial860_ptr);
            using LoadSubR3M862_ptr = struct _R3MATERIAL* (WINAPIV*)(char*);
            using LoadSubR3M862_clbk = struct _R3MATERIAL* (WINAPIV*)(char*, LoadSubR3M862_ptr);
            using LoadVertexShaderList863_ptr = void (WINAPIV*)();
            using LoadVertexShaderList863_clbk = void (WINAPIV*)(LoadVertexShaderList863_ptr);
            using LoadWaveList864_ptr = void (WINAPIV*)(char*);
            using LoadWaveList864_clbk = void (WINAPIV*)(char*, LoadWaveList864_ptr);
            using LuaScripAlert866_ptr = int (WINAPIV*)(lua_State*);
            using LuaScripAlert866_clbk = int (WINAPIV*)(lua_State*, LuaScripAlert866_ptr);
            using M2W868_ptr = bool (WINAPIV*)(char*, char*, unsigned int);
            using M2W868_clbk = bool (WINAPIV*)(char*, char*, unsigned int, M2W868_ptr);
            using MakeBinaryStr870_ptr = void (WINAPIV*)(char*, uint64_t, char*, uint64_t);
            using MakeBinaryStr870_clbk = void (WINAPIV*)(char*, uint64_t, char*, uint64_t, MakeBinaryStr870_ptr);
            using MakeDwordAlignedBuf871_ptr = uint8_t* (WINAPIV*)(uint8_t*, unsigned int, unsigned int, unsigned int*);
            using MakeDwordAlignedBuf871_clbk = uint8_t* (WINAPIV*)(uint8_t*, unsigned int, unsigned int, unsigned int*, MakeDwordAlignedBuf871_ptr);
            using MakeGrayScale872_ptr = int64_t (WINAPIV*)(uint8_t*, unsigned int, unsigned int);
            using MakeGrayScale872_clbk = int64_t (WINAPIV*)(uint8_t*, unsigned int, unsigned int, MakeGrayScale872_ptr);
            using MakeLoot874_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(char, uint16_t);
            using MakeLoot874_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(char, uint16_t, MakeLoot874_ptr);
            using MakeMipMap875_ptr = void (WINAPIV*)(uint16_t, uint16_t, uint16_t*, uint8_t*);
            using MakeMipMap875_clbk = void (WINAPIV*)(uint16_t, uint16_t, uint16_t*, uint8_t*, MakeMipMap875_ptr);
            using MakeMipMap876_ptr = void (WINAPIV*)(uint16_t, uint16_t, uint16_t*, uint16_t*);
            using MakeMipMap876_clbk = void (WINAPIV*)(uint16_t, uint16_t, uint16_t*, uint16_t*, MakeMipMap876_ptr);
            using MakeUV877_ptr = void (WINAPIV*)(struct _D3DR3VERTEX_TEX1*, int, void*, struct _R3MATERIAL*, int);
            using MakeUV877_clbk = void (WINAPIV*)(struct _D3DR3VERTEX_TEX1*, int, void*, struct _R3MATERIAL*, int, MakeUV877_ptr);
            using MakeUV878_ptr = void (WINAPIV*)(struct _D3DR3VERTEX_TEX2*, int, void*, void*, struct _R3MATERIAL*, int, float);
            using MakeUV878_clbk = void (WINAPIV*)(struct _D3DR3VERTEX_TEX2*, int, void*, void*, struct _R3MATERIAL*, int, float, MakeUV878_ptr);
            using MakeUVExt879_ptr = void (WINAPIV*)(struct _D3DR3VERTEX_TEX2*, int, void*, void*, struct _R3MATERIAL*, int, float);
            using MakeUVExt879_clbk = void (WINAPIV*)(struct _D3DR3VERTEX_TEX2*, int, void*, void*, struct _R3MATERIAL*, int, float, MakeUVExt879_ptr);
            using MatrixCopy880_ptr = void (WINAPIV*)(float**, float**);
            using MatrixCopy880_clbk = void (WINAPIV*)(float**, float**, MatrixCopy880_ptr);
            using MatrixFromQuaternion881_ptr = void (WINAPIV*)(float**, float, float, float, float);
            using MatrixFromQuaternion881_clbk = void (WINAPIV*)(float**, float, float, float, float, MatrixFromQuaternion881_ptr);
            using MatrixIdentity882_ptr = void (WINAPIV*)(float**);
            using MatrixIdentity882_clbk = void (WINAPIV*)(float**, MatrixIdentity882_ptr);
            using MatrixInvert883_ptr = int64_t (WINAPIV*)(float**, float**);
            using MatrixInvert883_clbk = int64_t (WINAPIV*)(float**, float**, MatrixInvert883_ptr);
            using MatrixMultiply884_ptr = void (WINAPIV*)(float**, float**, float**);
            using MatrixMultiply884_clbk = void (WINAPIV*)(float**, float**, float**, MatrixMultiply884_ptr);
            using MatrixRotate885_ptr = void (WINAPIV*)(float**, float, float, float);
            using MatrixRotate885_clbk = void (WINAPIV*)(float**, float, float, float, MatrixRotate885_ptr);
            using MatrixRotateX886_ptr = void (WINAPIV*)(float**, float);
            using MatrixRotateX886_clbk = void (WINAPIV*)(float**, float, MatrixRotateX886_ptr);
            using MatrixRotateY887_ptr = void (WINAPIV*)(float**, float);
            using MatrixRotateY887_clbk = void (WINAPIV*)(float**, float, MatrixRotateY887_ptr);
            using MatrixRotateZ888_ptr = void (WINAPIV*)(float**, float);
            using MatrixRotateZ888_clbk = void (WINAPIV*)(float**, float, MatrixRotateZ888_ptr);
            using MatrixScale889_ptr = void (WINAPIV*)(float**, float, float, float);
            using MatrixScale889_clbk = void (WINAPIV*)(float**, float, float, float, MatrixScale889_ptr);
            using MaxFixFloatToInt890_ptr = int64_t (WINAPIV*)(float);
            using MaxFixFloatToInt890_clbk = int64_t (WINAPIV*)(float, MaxFixFloatToInt890_ptr);
            using MinFixFloatToInt891_ptr = int64_t (WINAPIV*)(float);
            using MinFixFloatToInt891_clbk = int64_t (WINAPIV*)(float, MinFixFloatToInt891_ptr);
            using MultiTexOff892_ptr = void (WINAPIV*)();
            using MultiTexOff892_clbk = void (WINAPIV*)(MultiTexOff892_ptr);
            using MultiTexOn893_ptr = void (WINAPIV*)();
            using MultiTexOn893_clbk = void (WINAPIV*)(MultiTexOn893_ptr);
            using MyCrtDebugReportHook895_ptr = int (WINAPIV*)(int, char*, int*);
            using MyCrtDebugReportHook895_clbk = int (WINAPIV*)(int, char*, int*, MyCrtDebugReportHook895_ptr);
            using MyMessageBox897_ptr = void (WINAPIV*)(char*, char*);
            using MyMessageBox897_clbk = void (WINAPIV*)(char*, char*, MyMessageBox897_ptr);
            using NetTrace899_ptr = void (WINAPIV*)(char*);
            using NetTrace899_clbk = void (WINAPIV*)(char*, NetTrace899_ptr);
            using Noise900_ptr = float (WINAPIV*)(int32_t, int32_t);
            using Noise900_clbk = float (WINAPIV*)(int32_t, int32_t, Noise900_ptr);
            using Noise1901_ptr = float (WINAPIV*)(int32_t);
            using Noise1901_clbk = float (WINAPIV*)(int32_t, Noise1901_ptr);
            using Normalize903_ptr = void (WINAPIV*)(float*);
            using Normalize903_clbk = void (WINAPIV*)(float*, Normalize903_ptr);
            using OnLoop_GuildSystem905_ptr = void (WINAPIV*)(bool);
            using OnLoop_GuildSystem905_clbk = void (WINAPIV*)(bool, OnLoop_GuildSystem905_ptr);
            using OnLoop_VoteSystem907_ptr = void (WINAPIV*)();
            using OnLoop_VoteSystem907_clbk = void (WINAPIV*)(OnLoop_VoteSystem907_ptr);
            using OutputDebugLog909_ptr = void (WINAPIV*)(char*);
            using OutputDebugLog909_clbk = void (WINAPIV*)(char*, OutputDebugLog909_ptr);
            using PFromRva910_ptr = HINSTANCE__** (WINAPIV*)(unsigned int);
            using PFromRva910_clbk = HINSTANCE__** (WINAPIV*)(unsigned int, PFromRva910_ptr);
            using PFromRva911_ptr = struct ImgDelayDescr* (WINAPIV*)(unsigned int);
            using PFromRva911_clbk = struct ImgDelayDescr* (WINAPIV*)(unsigned int, PFromRva911_ptr);
            using PFromRva912_ptr = struct _IMAGE_IMPORT_BY_NAME* (WINAPIV*)(unsigned int);
            using PFromRva912_clbk = struct _IMAGE_IMPORT_BY_NAME* (WINAPIV*)(unsigned int, PFromRva912_ptr);
            using PFromRva913_ptr = struct _IMAGE_THUNK_DATA64* (WINAPIV*)(unsigned int);
            using PFromRva913_clbk = struct _IMAGE_THUNK_DATA64* (WINAPIV*)(unsigned int, PFromRva913_ptr);
            using PFromRva914_ptr = struct _IMAGE_THUNK_DATA64* (WINAPIV*)(unsigned int);
            using PFromRva914_clbk = struct _IMAGE_THUNK_DATA64* (WINAPIV*)(unsigned int, PFromRva914_ptr);
            using PFromRva915_ret = int64_t (WINAPIV**)();
            using PFromRva915_ptr = PFromRva915_ret (WINAPIV*)(unsigned int);
            using PFromRva915_clbk = PFromRva915_ret (WINAPIV*)(unsigned int, PFromRva915_ptr);
            using PFromRva916_ptr = char* (WINAPIV*)(unsigned int);
            using PFromRva916_clbk = char* (WINAPIV*)(unsigned int, PFromRva916_ptr);
            using ParsingCommandA918_ptr = int (WINAPIV*)(char*, int, char**, int);
            using ParsingCommandA918_clbk = int (WINAPIV*)(char*, int, char**, int, ParsingCommandA918_ptr);
            using ParsingCommandW920_ptr = int (WINAPIV*)(char*, int, char**, int);
            using ParsingCommandW920_clbk = int (WINAPIV*)(char*, int, char**, int, ParsingCommandW920_ptr);
            using PerlinNoise_1D921_ptr = float (WINAPIV*)(float, float, uint32_t);
            using PerlinNoise_1D921_clbk = float (WINAPIV*)(float, float, uint32_t, PerlinNoise_1D921_ptr);
            using PerlinNoise_2D922_ptr = float (WINAPIV*)(float, float, float, uint32_t);
            using PerlinNoise_2D922_clbk = float (WINAPIV*)(float, float, float, uint32_t, PerlinNoise_2D922_ptr);
            using PixelFromFramebuffer923_ptr = uint32_t (WINAPIV*)(int16_t, int16_t);
            using PixelFromFramebuffer923_clbk = uint32_t (WINAPIV*)(int16_t, int16_t, PixelFromFramebuffer923_ptr);
            using PlayR3MP3924_ptr = void (WINAPIV*)();
            using PlayR3MP3924_clbk = void (WINAPIV*)(PlayR3MP3924_ptr);
            using PlayStreamR3MP3925_ptr = void (WINAPIV*)();
            using PlayStreamR3MP3925_clbk = void (WINAPIV*)(PlayStreamR3MP3925_ptr);
            using PlayWave926_ptr = void (WINAPIV*)(uint32_t, uint32_t, float, float);
            using PlayWave926_clbk = void (WINAPIV*)(uint32_t, uint32_t, float, float, PlayWave926_ptr);
            using PopViewPortArea927_ptr = void (WINAPIV*)();
            using PopViewPortArea927_clbk = void (WINAPIV*)(PopViewPortArea927_ptr);
            using PowInterpolate928_ptr = float (WINAPIV*)(float, float, float);
            using PowInterpolate928_clbk = float (WINAPIV*)(float, float, float, PowInterpolate928_ptr);
            using PreRenderOneLayer929_ptr = void (WINAPIV*)(struct CVertexBuffer*, struct _BSP_MAT_GROUP*);
            using PreRenderOneLayer929_clbk = void (WINAPIV*)(struct CVertexBuffer*, struct _BSP_MAT_GROUP*, PreRenderOneLayer929_ptr);
            using PreRenderSetting930_ptr = void (WINAPIV*)(int, struct CVertexBuffer*, struct _BSP_MAT_GROUP*);
            using PreRenderSetting930_clbk = void (WINAPIV*)(int, struct CVertexBuffer*, struct _BSP_MAT_GROUP*, PreRenderSetting930_ptr);
            using PrepareAllShadow931_ptr = void (WINAPIV*)();
            using PrepareAllShadow931_clbk = void (WINAPIV*)(PrepareAllShadow931_ptr);
            using ProcessCheatCommand933_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
            using ProcessCheatCommand933_clbk = bool (WINAPIV*)(struct CPlayer*, char*, ProcessCheatCommand933_ptr);
            using ProgressConsole934_ptr = void (WINAPIV*)(struct CLevel*);
            using ProgressConsole934_clbk = void (WINAPIV*)(struct CLevel*, ProgressConsole934_ptr);
            using ProgressFunctionKey935_ptr = void (WINAPIV*)();
            using ProgressFunctionKey935_clbk = void (WINAPIV*)(ProgressFunctionKey935_ptr);
            using PushViewPortArea936_ptr = void (WINAPIV*)();
            using PushViewPortArea936_clbk = void (WINAPIV*)(PushViewPortArea936_ptr);
            using PutMessage937_ptr = void (WINAPIV*)(char*, char*);
            using PutMessage937_clbk = void (WINAPIV*)(char*, char*, PutMessage937_ptr);
            using QuaternionFromRotation938_ptr = void (WINAPIV*)(float*, float*, float);
            using QuaternionFromRotation938_clbk = void (WINAPIV*)(float*, float*, float, QuaternionFromRotation938_ptr);
            using QuaternionSlerp939_ptr = void (WINAPIV*)(float*, float*, float*, float*, float, float, float, float, float, float, float, float, float);
            using QuaternionSlerp939_clbk = void (WINAPIV*)(float*, float*, float*, float*, float, float, float, float, float, float, float, float, float, QuaternionSlerp939_ptr);
            using R3BeginScene940_ptr = int64_t (WINAPIV*)();
            using R3BeginScene940_clbk = int64_t (WINAPIV*)(R3BeginScene940_ptr);
            using R3CalcStrIndexPitInWidthA941_ptr = int64_t (WINAPIV*)(char*, int, int, uint32_t);
            using R3CalcStrIndexPitInWidthA941_clbk = int64_t (WINAPIV*)(char*, int, int, uint32_t, R3CalcStrIndexPitInWidthA941_ptr);
            using R3CalcStrIndexPitInWidthW942_ptr = int64_t (WINAPIV*)(wchar_t*, int, int, uint32_t);
            using R3CalcStrIndexPitInWidthW942_clbk = int64_t (WINAPIV*)(wchar_t*, int, int, uint32_t, R3CalcStrIndexPitInWidthW942_ptr);
            using R3CalcStrPixelSizeA943_ptr = int64_t (WINAPIV*)(char*, struct tagSIZE*, int, uint32_t);
            using R3CalcStrPixelSizeA943_clbk = int64_t (WINAPIV*)(char*, struct tagSIZE*, int, uint32_t, R3CalcStrPixelSizeA943_ptr);
            using R3CalcStrPixelSizeW944_ptr = int64_t (WINAPIV*)(wchar_t*, struct tagSIZE*, int, uint32_t);
            using R3CalcStrPixelSizeW944_clbk = int64_t (WINAPIV*)(wchar_t*, struct tagSIZE*, int, uint32_t, R3CalcStrPixelSizeW944_ptr);
            using R3CalculateTime945_ptr = void (WINAPIV*)();
            using R3CalculateTime945_clbk = void (WINAPIV*)(R3CalculateTime945_ptr);
            using R3ClearFrameBuffer946_ptr = void (WINAPIV*)();
            using R3ClearFrameBuffer946_clbk = void (WINAPIV*)(R3ClearFrameBuffer946_ptr);
            using R3ConfirmDevice947_ptr = int32_t (WINAPIV*)(struct _D3DCAPS8*, uint32_t, _D3DFORMAT);
            using R3ConfirmDevice947_clbk = int32_t (WINAPIV*)(struct _D3DCAPS8*, uint32_t, _D3DFORMAT, R3ConfirmDevice947_ptr);
            using R3DeleteDevice948_ptr = int32_t (WINAPIV*)();
            using R3DeleteDevice948_clbk = int32_t (WINAPIV*)(R3DeleteDevice948_ptr);
            using R3Draw2DLine949_ptr = void (WINAPIV*)(float*, float*, uint32_t);
            using R3Draw2DLine949_clbk = void (WINAPIV*)(float*, float*, uint32_t, R3Draw2DLine949_ptr);
            using R3Draw2DLineList950_ptr = void (WINAPIV*)(float**, uint32_t, uint32_t);
            using R3Draw2DLineList950_clbk = void (WINAPIV*)(float**, uint32_t, uint32_t, R3Draw2DLineList950_ptr);
            using R3DrawLine951_ptr = void (WINAPIV*)(float*, float*, uint32_t);
            using R3DrawLine951_clbk = void (WINAPIV*)(float*, float*, uint32_t, R3DrawLine951_ptr);
            using R3EndScene952_ptr = void (WINAPIV*)();
            using R3EndScene952_clbk = void (WINAPIV*)(R3EndScene952_ptr);
            using R3EnvironmentQuake953_ptr = void (WINAPIV*)(float, float);
            using R3EnvironmentQuake953_clbk = void (WINAPIV*)(float, float, R3EnvironmentQuake953_ptr);
            using R3EnvironmentShake954_ptr = void (WINAPIV*)(float, float);
            using R3EnvironmentShake954_clbk = void (WINAPIV*)(float, float, R3EnvironmentShake954_ptr);
            using R3EnvironmentShakeOff955_ptr = void (WINAPIV*)();
            using R3EnvironmentShakeOff955_clbk = void (WINAPIV*)(R3EnvironmentShakeOff955_ptr);
            using R3EnvironmentShakeState956_ptr = int64_t (WINAPIV*)();
            using R3EnvironmentShakeState956_clbk = int64_t (WINAPIV*)(R3EnvironmentShakeState956_ptr);
            using R3FlyMove957_ptr = void (WINAPIV*)(float, float*);
            using R3FlyMove957_clbk = void (WINAPIV*)(float, float*, R3FlyMove957_ptr);
            using R3FlyMoveSetPos958_ptr = void (WINAPIV*)(float*);
            using R3FlyMoveSetPos958_clbk = void (WINAPIV*)(float*, R3FlyMoveSetPos958_ptr);
            using R3GetLoopTime959_ptr = float (WINAPIV*)();
            using R3GetLoopTime959_clbk = float (WINAPIV*)(R3GetLoopTime959_ptr);
            using R3GetPreAniTextureId960_ptr = void (WINAPIV*)(char*, char*, int32_t*, int32_t*);
            using R3GetPreAniTextureId960_clbk = void (WINAPIV*)(char*, char*, int32_t*, int32_t*, R3GetPreAniTextureId960_ptr);
            using R3GetPreTextureId961_ptr = int64_t (WINAPIV*)(char*);
            using R3GetPreTextureId961_clbk = int64_t (WINAPIV*)(char*, R3GetPreTextureId961_ptr);
            using R3GetQuakeMatrix962_ptr = void (WINAPIV*)(float**);
            using R3GetQuakeMatrix962_clbk = void (WINAPIV*)(float**, R3GetQuakeMatrix962_ptr);
            using R3GetQuakeVector963_ptr = void (WINAPIV*)(float*);
            using R3GetQuakeVector963_clbk = void (WINAPIV*)(float*, R3GetQuakeVector963_ptr);
            using R3GetShakeVector964_ptr = void (WINAPIV*)(float*);
            using R3GetShakeVector964_clbk = void (WINAPIV*)(float*, R3GetShakeVector964_ptr);
            using R3GetSurface965_ptr = struct IDirect3DTexture8* (WINAPIV*)(int);
            using R3GetSurface965_clbk = struct IDirect3DTexture8* (WINAPIV*)(int, R3GetSurface965_ptr);
            using R3GetTexInfoR3T966_ptr = R3Texture* (WINAPIV*)(char*, uint32_t);
            using R3GetTexInfoR3T966_clbk = R3Texture* (WINAPIV*)(char*, uint32_t, R3GetTexInfoR3T966_ptr);
            using R3GetTexName967_ptr = char* (WINAPIV*)(int);
            using R3GetTexName967_clbk = char* (WINAPIV*)(int, R3GetTexName967_ptr);
            using R3GetTime968_ptr = float (WINAPIV*)();
            using R3GetTime968_clbk = float (WINAPIV*)(R3GetTime968_ptr);
            using R3GetToggle15fps969_ptr = int32_t (WINAPIV*)();
            using R3GetToggle15fps969_clbk = int32_t (WINAPIV*)(R3GetToggle15fps969_ptr);
            using R3GetToggle30fps970_ptr = int32_t (WINAPIV*)();
            using R3GetToggle30fps970_clbk = int32_t (WINAPIV*)(R3GetToggle30fps970_ptr);
            using R3GetToggle7fps971_ptr = int32_t (WINAPIV*)();
            using R3GetToggle7fps971_clbk = int32_t (WINAPIV*)(R3GetToggle7fps971_ptr);
            using R3GetViewPort972_ptr = void (WINAPIV*)(int32_t*, int32_t*, int32_t*, int32_t*);
            using R3GetViewPort972_clbk = void (WINAPIV*)(int32_t*, int32_t*, int32_t*, int32_t*, R3GetViewPort972_ptr);
            using R3InitDevice973_ptr = int32_t (WINAPIV*)(struct IDirect3DDevice8*, int, int);
            using R3InitDevice973_clbk = int32_t (WINAPIV*)(struct IDirect3DDevice8*, int, int, R3InitDevice973_ptr);
            using R3InvalidateDevice974_ptr = int32_t (WINAPIV*)();
            using R3InvalidateDevice974_clbk = int32_t (WINAPIV*)(R3InvalidateDevice974_ptr);
            using R3LoadDDS975_ptr = struct IDirect3DTexture8* (WINAPIV*)(char*, uint32_t, uint32_t, uint32_t);
            using R3LoadDDS975_clbk = struct IDirect3DTexture8* (WINAPIV*)(char*, uint32_t, uint32_t, uint32_t, R3LoadDDS975_ptr);
            using R3LoadDDSAndTextureMem976_ptr = struct IDirect3DTexture8* (WINAPIV*)(char*, uint32_t);
            using R3LoadDDSAndTextureMem976_clbk = struct IDirect3DTexture8* (WINAPIV*)(char*, uint32_t, R3LoadDDSAndTextureMem976_ptr);
            using R3LoadDDSFromFP977_ptr = struct IDirect3DTexture8* (WINAPIV*)(FILE*, size_t, uint32_t, uint32_t, uint32_t);
            using R3LoadDDSFromFP977_clbk = struct IDirect3DTexture8* (WINAPIV*)(FILE*, size_t, uint32_t, uint32_t, uint32_t, R3LoadDDSFromFP977_ptr);
            using R3LoadTextTexture978_ptr = void (WINAPIV*)();
            using R3LoadTextTexture978_clbk = void (WINAPIV*)(R3LoadTextTexture978_ptr);
            using R3LoadTextureMem979_ptr = void (WINAPIV*)(int);
            using R3LoadTextureMem979_clbk = void (WINAPIV*)(int, R3LoadTextureMem979_ptr);
            using R3MouseInput980_ptr = void (WINAPIV*)();
            using R3MouseInput980_clbk = void (WINAPIV*)(R3MouseInput980_ptr);
            using R3MoveGetViewMatrix981_ptr = struct D3DXMATRIX* (WINAPIV*)();
            using R3MoveGetViewMatrix981_clbk = struct D3DXMATRIX* (WINAPIV*)(R3MoveGetViewMatrix981_ptr);
            using R3MsgProc982_ptr = void (WINAPIV*)(HWND, unsigned int, uint64_t, int64_t);
            using R3MsgProc982_clbk = void (WINAPIV*)(HWND, unsigned int, uint64_t, int64_t, R3MsgProc982_ptr);
            using R3ReleaseAllTextures983_ptr = void (WINAPIV*)();
            using R3ReleaseAllTextures983_clbk = void (WINAPIV*)(R3ReleaseAllTextures983_ptr);
            using R3ReleasePreTextures984_ptr = void (WINAPIV*)();
            using R3ReleasePreTextures984_clbk = void (WINAPIV*)(R3ReleasePreTextures984_ptr);
            using R3ReleaseTextTexture985_ptr = void (WINAPIV*)();
            using R3ReleaseTextTexture985_clbk = void (WINAPIV*)(R3ReleaseTextTexture985_ptr);
            using R3ReleaseTextureMem986_ptr = void (WINAPIV*)(int);
            using R3ReleaseTextureMem986_clbk = void (WINAPIV*)(int, R3ReleaseTextureMem986_ptr);
            using R3RestoreAllTextures987_ptr = void (WINAPIV*)();
            using R3RestoreAllTextures987_clbk = void (WINAPIV*)(R3RestoreAllTextures987_ptr);
            using R3RestoreDevice988_ptr = int32_t (WINAPIV*)();
            using R3RestoreDevice988_clbk = int32_t (WINAPIV*)(R3RestoreDevice988_ptr);
            using R3SetCameraMatrix989_ptr = void (WINAPIV*)(float*, float**);
            using R3SetCameraMatrix989_clbk = void (WINAPIV*)(float*, float**, R3SetCameraMatrix989_ptr);
            using R3SetLoopTime990_ptr = void (WINAPIV*)(float);
            using R3SetLoopTime990_clbk = void (WINAPIV*)(float, R3SetLoopTime990_ptr);
            using R3SetMinFPS991_ptr = void (WINAPIV*)(float);
            using R3SetMinFPS991_clbk = void (WINAPIV*)(float, R3SetMinFPS991_ptr);
            using R3SetTime992_ptr = void (WINAPIV*)(float);
            using R3SetTime992_clbk = void (WINAPIV*)(float, R3SetTime992_ptr);
            using R3SetViewPort993_ptr = void (WINAPIV*)(int32_t, int32_t, int32_t, int32_t);
            using R3SetViewPort993_clbk = void (WINAPIV*)(int32_t, int32_t, int32_t, int32_t, R3SetViewPort993_ptr);
            using RGBFromDWORDAligned994_ptr = uint8_t* (WINAPIV*)(uint8_t*, unsigned int, unsigned int, unsigned int);
            using RGBFromDWORDAligned994_clbk = uint8_t* (WINAPIV*)(uint8_t*, unsigned int, unsigned int, unsigned int, RGBFromDWORDAligned994_ptr);
            using RGBToJpegFile995_ptr = int64_t (WINAPIV*)(char*, uint8_t*, unsigned int, unsigned int, int, int);
            using RGBToJpegFile995_clbk = int64_t (WINAPIV*)(char*, uint8_t*, unsigned int, unsigned int, int, int, RGBToJpegFile995_ptr);
            using RTMoiveGetCameraMatrix996_ptr = void (WINAPIV*)(float**);
            using RTMoiveGetCameraMatrix996_clbk = void (WINAPIV*)(float**, RTMoiveGetCameraMatrix996_ptr);
            using RTMoiveGetCameraPos997_ptr = void (WINAPIV*)(float*);
            using RTMoiveGetCameraPos997_clbk = void (WINAPIV*)(float*, RTMoiveGetCameraPos997_ptr);
            using RTMovieAddState998_ptr = void (WINAPIV*)(uint32_t);
            using RTMovieAddState998_clbk = void (WINAPIV*)(uint32_t, RTMovieAddState998_ptr);
            using RTMovieCreate999_ptr = void (WINAPIV*)(char*, struct CLevel*);
            using RTMovieCreate999_clbk = void (WINAPIV*)(char*, struct CLevel*, RTMovieCreate999_ptr);
            using RTMovieFrameMove1000_ptr = void (WINAPIV*)();
            using RTMovieFrameMove1000_clbk = void (WINAPIV*)(RTMovieFrameMove1000_ptr);
            using RTMovieGetState1001_ptr = uint32_t (WINAPIV*)();
            using RTMovieGetState1001_clbk = uint32_t (WINAPIV*)(RTMovieGetState1001_ptr);
            using RTMoviePause1002_ptr = void (WINAPIV*)(int);
            using RTMoviePause1002_clbk = void (WINAPIV*)(int, RTMoviePause1002_ptr);
            using RTMovieRelease1003_ptr = void (WINAPIV*)();
            using RTMovieRelease1003_clbk = void (WINAPIV*)(RTMovieRelease1003_ptr);
            using RTMovieRender1004_ptr = void (WINAPIV*)();
            using RTMovieRender1004_clbk = void (WINAPIV*)(RTMovieRender1004_ptr);
            using RTMovieSetState1005_ptr = void (WINAPIV*)(uint32_t);
            using RTMovieSetState1005_clbk = void (WINAPIV*)(uint32_t, RTMovieSetState1005_ptr);
            using RTMovieSkipShadowState1006_ptr = void (WINAPIV*)(int);
            using RTMovieSkipShadowState1006_clbk = void (WINAPIV*)(int, RTMovieSkipShadowState1006_ptr);
            using RTMovieStartPlay1007_ptr = void (WINAPIV*)(int);
            using RTMovieStartPlay1007_clbk = void (WINAPIV*)(int, RTMovieStartPlay1007_ptr);
            using RTMovieSubState1008_ptr = void (WINAPIV*)(uint32_t);
            using RTMovieSubState1008_clbk = void (WINAPIV*)(uint32_t, RTMovieSubState1008_ptr);
            using ReAlloc1009_ptr = void* (WINAPIV*)(void*, int, int);
            using ReAlloc1009_clbk = void* (WINAPIV*)(void*, int, int, ReAlloc1009_ptr);
            using ReLoadMaterial1010_ptr = void (WINAPIV*)(char*, struct _R3MATERIAL*);
            using ReLoadMaterial1010_clbk = void (WINAPIV*)(char*, struct _R3MATERIAL*, ReLoadMaterial1010_ptr);
            using ReMoveCamera1011_ptr = void (WINAPIV*)(_MOVE_CAMERA*);
            using ReMoveCamera1011_clbk = void (WINAPIV*)(_MOVE_CAMERA*, ReMoveCamera1011_ptr);
            using ReleaesR3MP31012_ptr = void (WINAPIV*)();
            using ReleaesR3MP31012_clbk = void (WINAPIV*)(ReleaesR3MP31012_ptr);
            using ReleaesStreamR3MP31013_ptr = void (WINAPIV*)();
            using ReleaesStreamR3MP31013_clbk = void (WINAPIV*)(ReleaesStreamR3MP31013_ptr);
            using ReleaseAllSpriteTexMem1014_ptr = void (WINAPIV*)();
            using ReleaseAllSpriteTexMem1014_clbk = void (WINAPIV*)(ReleaseAllSpriteTexMem1014_ptr);
            using ReleaseBlurShader1015_ptr = void (WINAPIV*)();
            using ReleaseBlurShader1015_clbk = void (WINAPIV*)(ReleaseBlurShader1015_ptr);
            using ReleaseBlurVBuffer1016_ptr = void (WINAPIV*)();
            using ReleaseBlurVBuffer1016_clbk = void (WINAPIV*)(ReleaseBlurVBuffer1016_ptr);
            using ReleaseConsole1017_ptr = void (WINAPIV*)();
            using ReleaseConsole1017_clbk = void (WINAPIV*)(ReleaseConsole1017_ptr);
            using ReleaseCore1018_ptr = void (WINAPIV*)();
            using ReleaseCore1018_clbk = void (WINAPIV*)(ReleaseCore1018_ptr);
            using ReleaseFullScreenEffect1019_ptr = void (WINAPIV*)();
            using ReleaseFullScreenEffect1019_clbk = void (WINAPIV*)(ReleaseFullScreenEffect1019_ptr);
            using ReleaseFunctionKey1020_ptr = void (WINAPIV*)();
            using ReleaseFunctionKey1020_clbk = void (WINAPIV*)(ReleaseFunctionKey1020_ptr);
            using ReleaseJmalloc1021_ptr = void (WINAPIV*)();
            using ReleaseJmalloc1021_clbk = void (WINAPIV*)(ReleaseJmalloc1021_ptr);
            using ReleaseLightMap1022_ptr = void (WINAPIV*)();
            using ReleaseLightMap1022_clbk = void (WINAPIV*)(ReleaseLightMap1022_ptr);
            using ReleaseMainMaterial1023_ptr = void (WINAPIV*)();
            using ReleaseMainMaterial1023_clbk = void (WINAPIV*)(ReleaseMainMaterial1023_ptr);
            using ReleaseR3Engine1024_ptr = void (WINAPIV*)();
            using ReleaseR3Engine1024_clbk = void (WINAPIV*)(ReleaseR3Engine1024_ptr);
            using ReleaseR3Particle1025_ptr = void (WINAPIV*)();
            using ReleaseR3Particle1025_clbk = void (WINAPIV*)(ReleaseR3Particle1025_ptr);
            using ReleaseR3SoundSystem1026_ptr = void (WINAPIV*)();
            using ReleaseR3SoundSystem1026_clbk = void (WINAPIV*)(ReleaseR3SoundSystem1026_ptr);
            using ReleaseR3Text1027_ptr = void (WINAPIV*)();
            using ReleaseR3Text1027_clbk = void (WINAPIV*)(ReleaseR3Text1027_ptr);
            using ReleaseSpriteManager1028_ptr = void (WINAPIV*)(CSprite*);
            using ReleaseSpriteManager1028_clbk = void (WINAPIV*)(CSprite*, ReleaseSpriteManager1028_ptr);
            using ReleaseSpriteManager1029_ptr = void (WINAPIV*)();
            using ReleaseSpriteManager1029_clbk = void (WINAPIV*)(ReleaseSpriteManager1029_ptr);
            using ReleaseSubMaterial1030_ptr = void (WINAPIV*)(struct _R3MATERIAL*);
            using ReleaseSubMaterial1030_clbk = void (WINAPIV*)(struct _R3MATERIAL*, ReleaseSubMaterial1030_ptr);
            using ReleaseSystemTexture1031_ptr = void (WINAPIV*)();
            using ReleaseSystemTexture1031_clbk = void (WINAPIV*)(ReleaseSystemTexture1031_ptr);
            using ReleaseVertexShaderList1032_ptr = void (WINAPIV*)();
            using ReleaseVertexShaderList1032_clbk = void (WINAPIV*)(ReleaseVertexShaderList1032_ptr);
            using ReleaseWaveList1033_ptr = void (WINAPIV*)();
            using ReleaseWaveList1033_clbk = void (WINAPIV*)(ReleaseWaveList1033_ptr);
            using ReleaseWebBrowser1034_ptr = void (WINAPIV*)();
            using ReleaseWebBrowser1034_clbk = void (WINAPIV*)(ReleaseWebBrowser1034_ptr);
            using ResetTexMemSize1035_ptr = void (WINAPIV*)();
            using ResetTexMemSize1035_clbk = void (WINAPIV*)(ResetTexMemSize1035_ptr);
            using ResetTotalVertexBufferInfo1036_ptr = void (WINAPIV*)();
            using ResetTotalVertexBufferInfo1036_clbk = void (WINAPIV*)(ResetTotalVertexBufferInfo1036_ptr);
            using ResetVertexBufferCache1037_ptr = void (WINAPIV*)();
            using ResetVertexBufferCache1037_clbk = void (WINAPIV*)(ResetVertexBufferCache1037_ptr);
            using RestoreAllSpriteTexMem1038_ptr = void (WINAPIV*)();
            using RestoreAllSpriteTexMem1038_clbk = void (WINAPIV*)(RestoreAllSpriteTexMem1038_ptr);
            using RestoreSpriteManager1039_ptr = void (WINAPIV*)(struct CSprite*);
            using RestoreSpriteManager1039_clbk = void (WINAPIV*)(struct CSprite*, RestoreSpriteManager1039_ptr);
            using RestoreSystemTexture1040_ptr = void (WINAPIV*)();
            using RestoreSystemTexture1040_clbk = void (WINAPIV*)(RestoreSystemTexture1040_ptr);
            using Rijndael_VC60Workaround1041_ptr = void (WINAPIV*)();
            using Rijndael_VC60Workaround1041_clbk = void (WINAPIV*)(Rijndael_VC60Workaround1041_ptr);
            using RotateLeftBits1043_ptr = unsigned int (WINAPIV*)(unsigned int, unsigned int);
            using RotateLeftBits1043_clbk = unsigned int (WINAPIV*)(unsigned int, unsigned int, RotateLeftBits1043_ptr);
            using Round11045_ptr = void (WINAPIV*)(unsigned int*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int);
            using Round11045_clbk = void (WINAPIV*)(unsigned int*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, Round11045_ptr);
            using Round21047_ptr = void (WINAPIV*)(unsigned int*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int);
            using Round21047_clbk = void (WINAPIV*)(unsigned int*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, Round21047_ptr);
            using Round31049_ptr = void (WINAPIV*)(unsigned int*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int);
            using Round31049_clbk = void (WINAPIV*)(unsigned int*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, Round31049_ptr);
            using Round41051_ptr = void (WINAPIV*)(unsigned int*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int);
            using Round41051_clbk = void (WINAPIV*)(unsigned int*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, Round41051_ptr);
            using SaveBMP1052_ptr = void (WINAPIV*)(char*, int64_t, int, int, uint8_t*);
            using SaveBMP1052_clbk = void (WINAPIV*)(char*, int64_t, int, int, uint8_t*, SaveBMP1052_ptr);
            using SaveJPG1053_ptr = void (WINAPIV*)(char*, int, int, int, uint8_t*);
            using SaveJPG1053_clbk = void (WINAPIV*)(char*, int, int, int, uint8_t*, SaveJPG1053_ptr);
            using ScanCodeToVKCode1054_ptr = int64_t (WINAPIV*)(int);
            using ScanCodeToVKCode1054_clbk = int64_t (WINAPIV*)(int, ScanCodeToVKCode1054_ptr);
            using SearchAvatorWithCMS1056_ptr = struct CUserDB* (WINAPIV*)(struct CUserDB*, int, char*);
            using SearchAvatorWithCMS1056_clbk = struct CUserDB* (WINAPIV*)(struct CUserDB*, int, char*, SearchAvatorWithCMS1056_ptr);
            using SearchAvatorWithName1058_ptr = struct CUserDB* (WINAPIV*)(struct CUserDB*, int, char*);
            using SearchAvatorWithName1058_clbk = struct CUserDB* (WINAPIV*)(struct CUserDB*, int, char*, SearchAvatorWithName1058_ptr);
            using SearchEmptyMonster1060_ptr = struct CMonster* (WINAPIV*)(bool);
            using SearchEmptyMonster1060_clbk = struct CMonster* (WINAPIV*)(bool, SearchEmptyMonster1060_ptr);
            using SearchJobCommandFn1062_ret = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using SearchJobCommandFn1062_ptr = SearchJobCommandFn1062_ret (WINAPIV*)(char*);
            using SearchJobCommandFn1062_clbk = SearchJobCommandFn1062_ret (WINAPIV*)(char*, SearchJobCommandFn1062_ptr);
            using SearchMissionCommandFn1064_ret = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using SearchMissionCommandFn1064_ptr = SearchMissionCommandFn1064_ret (WINAPIV*)(char*);
            using SearchMissionCommandFn1064_clbk = SearchMissionCommandFn1064_ret (WINAPIV*)(char*, SearchMissionCommandFn1064_ptr);
            using SearchQuestCommandFn1066_ret = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using SearchQuestCommandFn1066_ptr = SearchQuestCommandFn1066_ret (WINAPIV*)(char*);
            using SearchQuestCommandFn1066_clbk = SearchQuestCommandFn1066_ret (WINAPIV*)(char*, SearchQuestCommandFn1066_ptr);
            using SendLoop1068_ptr = unsigned int (WINAPIV*)(void*);
            using SendLoop1068_clbk = unsigned int (WINAPIV*)(void*, SendLoop1068_ptr);
            using SendMsg_EconomyDataToWeb1070_ptr = void (WINAPIV*)();
            using SendMsg_EconomyDataToWeb1070_clbk = void (WINAPIV*)(SendMsg_EconomyDataToWeb1070_ptr);
            using SerarchEmptyDarkHole1072_ptr = struct CDarkHole* (WINAPIV*)();
            using SerarchEmptyDarkHole1072_clbk = struct CDarkHole* (WINAPIV*)(SerarchEmptyDarkHole1072_ptr);
            using ServerProgramExit1074_ptr = void (WINAPIV*)(char*, bool);
            using ServerProgramExit1074_clbk = void (WINAPIV*)(char*, bool, ServerProgramExit1074_ptr);
            using SetConsoleStateB1075_ptr = void (WINAPIV*)(int, int);
            using SetConsoleStateB1075_clbk = void (WINAPIV*)(int, int, SetConsoleStateB1075_ptr);
            using SetContrast1076_ptr = void (WINAPIV*)(float*);
            using SetContrast1076_clbk = void (WINAPIV*)(float*, SetContrast1076_ptr);
            using SetContrast1077_ptr = void (WINAPIV*)(uint8_t*);
            using SetContrast1077_clbk = void (WINAPIV*)(uint8_t*, SetContrast1077_ptr);
            using SetDDSTexNameDebug1078_ptr = void (WINAPIV*)(char*);
            using SetDDSTexNameDebug1078_clbk = void (WINAPIV*)(char*, SetDDSTexNameDebug1078_ptr);
            using SetDDSTexSizeDebug1079_ptr = void (WINAPIV*)(uint32_t);
            using SetDDSTexSizeDebug1079_clbk = void (WINAPIV*)(uint32_t, SetDDSTexSizeDebug1079_ptr);
            using SetDefaultFlags1080_ptr = void (WINAPIV*)();
            using SetDefaultFlags1080_clbk = void (WINAPIV*)(SetDefaultFlags1080_ptr);
            using SetDefaultFov1081_ptr = void (WINAPIV*)();
            using SetDefaultFov1081_clbk = void (WINAPIV*)(SetDefaultFov1081_ptr);
            using SetDetailTexVS1082_ptr = void (WINAPIV*)(struct D3DXMATRIX*);
            using SetDetailTexVS1082_clbk = void (WINAPIV*)(struct D3DXMATRIX*, SetDetailTexVS1082_ptr);
            using SetDynamicLight1083_ptr = void (WINAPIV*)(float*, float, uint32_t, void*, int, uint32_t);
            using SetDynamicLight1083_clbk = void (WINAPIV*)(float*, float, uint32_t, void*, int, uint32_t, SetDynamicLight1083_ptr);
            using SetErrorButRunMessageProc1084_ptr = void (WINAPIV*)(void (WINAPIV*)(char*));
            using SetErrorButRunMessageProc1084_clbk = void (WINAPIV*)(void (WINAPIV*)(char*), SetErrorButRunMessageProc1084_ptr);
            using SetErrorMessageProc1085_ptr = void (WINAPIV*)(void (WINAPIV*)(char*));
            using SetErrorMessageProc1085_clbk = void (WINAPIV*)(void (WINAPIV*)(char*), SetErrorMessageProc1085_ptr);
            using SetFadeFactor1086_ptr = void (WINAPIV*)(uint32_t, float, void*);
            using SetFadeFactor1086_clbk = void (WINAPIV*)(uint32_t, float, void*, SetFadeFactor1086_ptr);
            using SetFadeIn1087_ptr = void (WINAPIV*)(uint32_t, float, float);
            using SetFadeIn1087_clbk = void (WINAPIV*)(uint32_t, float, float, SetFadeIn1087_ptr);
            using SetFadeOut1088_ptr = void (WINAPIV*)(uint32_t, float, float);
            using SetFadeOut1088_clbk = void (WINAPIV*)(uint32_t, float, float, SetFadeOut1088_ptr);
            using SetFadeSky1089_ptr = void (WINAPIV*)(uint32_t, float);
            using SetFadeSky1089_clbk = void (WINAPIV*)(uint32_t, float, SetFadeSky1089_ptr);
            using SetFadeTex1090_ptr = void (WINAPIV*)(uint32_t, float, void*);
            using SetFadeTex1090_clbk = void (WINAPIV*)(uint32_t, float, void*, SetFadeTex1090_ptr);
            using SetFov1091_ptr = void (WINAPIV*)(float);
            using SetFov1091_clbk = void (WINAPIV*)(float, SetFov1091_ptr);
            using SetFreePointer1092_ptr = void (WINAPIV*)();
            using SetFreePointer1092_clbk = void (WINAPIV*)(SetFreePointer1092_ptr);
            using SetFrustumNormalPlane1093_ptr = void (WINAPIV*)();
            using SetFrustumNormalPlane1093_clbk = void (WINAPIV*)(SetFrustumNormalPlane1093_ptr);
            using SetGamma1094_ptr = void (WINAPIV*)(float, int);
            using SetGamma1094_clbk = void (WINAPIV*)(float, int, SetGamma1094_ptr);
            using SetGlobalMusicVolume1095_ptr = int64_t (WINAPIV*)(float);
            using SetGlobalMusicVolume1095_clbk = int64_t (WINAPIV*)(float, SetGlobalMusicVolume1095_ptr);
            using SetGlobalWavVolume1096_ptr = int64_t (WINAPIV*)(float);
            using SetGlobalWavVolume1096_clbk = int64_t (WINAPIV*)(float, SetGlobalWavVolume1096_ptr);
            using SetGrassVS1097_ptr = void (WINAPIV*)(float, float*, float, uint32_t);
            using SetGrassVS1097_clbk = void (WINAPIV*)(float, float*, float, uint32_t, SetGrassVS1097_ptr);
            using SetLightMap1098_ptr = void (WINAPIV*)(int32_t);
            using SetLightMap1098_clbk = void (WINAPIV*)(int32_t, SetLightMap1098_ptr);
            using SetLitGrassVS1099_ptr = void (WINAPIV*)(float, float*, float, uint32_t, float*);
            using SetLitGrassVS1099_clbk = void (WINAPIV*)(float, float*, float, uint32_t, float*, SetLitGrassVS1099_ptr);
            using SetMainLight1100_ptr = void (WINAPIV*)(float*);
            using SetMainLight1100_clbk = void (WINAPIV*)(float*, SetMainLight1100_ptr);
            using SetMatAlphaColor1101_ptr = void (WINAPIV*)(uint32_t);
            using SetMatAlphaColor1101_clbk = void (WINAPIV*)(uint32_t, SetMatAlphaColor1101_ptr);
            using SetMergeFileManager1102_ptr = void (WINAPIV*)(struct CMergeFileManager*);
            using SetMergeFileManager1102_clbk = void (WINAPIV*)(struct CMergeFileManager*, SetMergeFileManager1102_ptr);
            using SetMotionBlurLength1103_ptr = void (WINAPIV*)(float);
            using SetMotionBlurLength1103_clbk = void (WINAPIV*)(float, SetMotionBlurLength1103_ptr);
            using SetMultiLayerCamera1104_ptr = void (WINAPIV*)(float*);
            using SetMultiLayerCamera1104_clbk = void (WINAPIV*)(float*, SetMultiLayerCamera1104_ptr);
            using SetMultiLayerTime1105_ptr = void (WINAPIV*)(float);
            using SetMultiLayerTime1105_clbk = void (WINAPIV*)(float, SetMultiLayerTime1105_ptr);
            using SetNoLodTextere1106_ptr = void (WINAPIV*)();
            using SetNoLodTextere1106_clbk = void (WINAPIV*)(SetNoLodTextere1106_ptr);
            using SetNowR3D3DTexCnt1107_ptr = void (WINAPIV*)(int);
            using SetNowR3D3DTexCnt1107_clbk = void (WINAPIV*)(int, SetNowR3D3DTexCnt1107_ptr);
            using SetNowR3TexCnt1108_ptr = void (WINAPIV*)(int);
            using SetNowR3TexCnt1108_clbk = void (WINAPIV*)(int, SetNowR3TexCnt1108_ptr);
            using SetOpStack1109_ptr = void (WINAPIV*)(int);
            using SetOpStack1109_clbk = void (WINAPIV*)(int, SetOpStack1109_ptr);
            using SetOutLineColor1110_ptr = void (WINAPIV*)(uint32_t);
            using SetOutLineColor1110_clbk = void (WINAPIV*)(uint32_t, SetOutLineColor1110_ptr);
            using SetOutLineColorFont161111_ptr = void (WINAPIV*)(uint32_t);
            using SetOutLineColorFont161111_clbk = void (WINAPIV*)(uint32_t, SetOutLineColorFont161111_ptr);
            using SetOutLineColorFont241112_ptr = void (WINAPIV*)(uint32_t);
            using SetOutLineColorFont241112_clbk = void (WINAPIV*)(uint32_t, SetOutLineColorFont241112_ptr);
            using SetPlayMusicState1113_ptr = void (WINAPIV*)(int);
            using SetPlayMusicState1113_clbk = void (WINAPIV*)(int, SetPlayMusicState1113_ptr);
            using SetPlayWaveState1114_ptr = void (WINAPIV*)(int);
            using SetPlayWaveState1114_clbk = void (WINAPIV*)(int, SetPlayWaveState1114_ptr);
            using SetProjectShadow1115_ptr = void (WINAPIV*)(float*, void*, float, uint32_t, float, float);
            using SetProjectShadow1115_clbk = void (WINAPIV*)(float*, void*, float, uint32_t, float, float, SetProjectShadow1115_ptr);
            using SetR3D3DTexture1116_ptr = void (WINAPIV*)(uint32_t, uint32_t);
            using SetR3D3DTexture1116_clbk = void (WINAPIV*)(uint32_t, uint32_t, SetR3D3DTexture1116_ptr);
            using SetR3TexManageFlag1117_ptr = void (WINAPIV*)(uint32_t);
            using SetR3TexManageFlag1117_clbk = void (WINAPIV*)(uint32_t, SetR3TexManageFlag1117_ptr);
            using SetR3TextFont1118_ptr = void (WINAPIV*)(char*, uint32_t, uint32_t, uint32_t);
            using SetR3TextFont1118_clbk = void (WINAPIV*)(char*, uint32_t, uint32_t, uint32_t, SetR3TextFont1118_ptr);
            using SetReLoadState1119_ptr = void (WINAPIV*)(int);
            using SetReLoadState1119_clbk = void (WINAPIV*)(int, SetReLoadState1119_ptr);
            using SetReflectionState1120_ptr = void (WINAPIV*)(int);
            using SetReflectionState1120_clbk = void (WINAPIV*)(int, SetReflectionState1120_ptr);
            using SetReflectionVS1121_ptr = void (WINAPIV*)();
            using SetReflectionVS1121_clbk = void (WINAPIV*)(SetReflectionVS1121_ptr);
            using SetSkipOneBboShasi11122_ptr = void (WINAPIV*)(int);
            using SetSkipOneBboShasi11122_clbk = void (WINAPIV*)(int, SetSkipOneBboShasi11122_ptr);
            using SetSkyVS1123_ptr = int64_t (WINAPIV*)();
            using SetSkyVS1123_clbk = int64_t (WINAPIV*)(SetSkyVS1123_ptr);
            using SetStateFullScreenEffect1124_ptr = void (WINAPIV*)(uint32_t);
            using SetStateFullScreenEffect1124_clbk = void (WINAPIV*)(uint32_t, SetStateFullScreenEffect1124_ptr);
            using SetTextureRenderTargetFrameBuffer1125_ptr = void (WINAPIV*)(int);
            using SetTextureRenderTargetFrameBuffer1125_clbk = void (WINAPIV*)(int, SetTextureRenderTargetFrameBuffer1125_ptr);
            using SetTimerRate1126_ptr = void (WINAPIV*)(float);
            using SetTimerRate1126_clbk = void (WINAPIV*)(float, SetTimerRate1126_ptr);
            using SetTransformClipInfo1127_ptr = void (WINAPIV*)(float, float, float, float);
            using SetTransformClipInfo1127_clbk = void (WINAPIV*)(float, float, float, float, SetTransformClipInfo1127_ptr);
            using SetVPIPTex11128_ptr = void (WINAPIV*)(void*, void*);
            using SetVPIPTex11128_clbk = void (WINAPIV*)(void*, void*, SetVPIPTex11128_ptr);
            using SetVPIPTex1IndexPrimitive1129_ptr = void (WINAPIV*)(void*, void*);
            using SetVPIPTex1IndexPrimitive1129_clbk = void (WINAPIV*)(void*, void*, SetVPIPTex1IndexPrimitive1129_ptr);
            using SetVPIPTex1IndexPrimitiveTL1130_ptr = void (WINAPIV*)(void*, void*);
            using SetVPIPTex1IndexPrimitiveTL1130_clbk = void (WINAPIV*)(void*, void*, SetVPIPTex1IndexPrimitiveTL1130_ptr);
            using SetVPIPTex21131_ptr = void (WINAPIV*)(void*, void*);
            using SetVPIPTex21131_clbk = void (WINAPIV*)(void*, void*, SetVPIPTex21131_ptr);
            using SetViewPortArea1132_ptr = void (WINAPIV*)(int32_t, int32_t, int32_t, int32_t);
            using SetViewPortArea1132_clbk = void (WINAPIV*)(int32_t, int32_t, int32_t, int32_t, SetViewPortArea1132_ptr);
            using SetVolumeMP31133_ptr = int64_t (WINAPIV*)(float);
            using SetVolumeMP31133_clbk = int64_t (WINAPIV*)(float, SetVolumeMP31133_ptr);
            using SetVolumeStreamMP31134_ptr = int64_t (WINAPIV*)(float);
            using SetVolumeStreamMP31134_clbk = int64_t (WINAPIV*)(float, SetVolumeStreamMP31134_ptr);
            using SetWarningMessageProc1135_ptr = void (WINAPIV*)(void (WINAPIV*)(char*));
            using SetWarningMessageProc1135_clbk = void (WINAPIV*)(void (WINAPIV*)(char*), SetWarningMessageProc1135_ptr);
            using SetWaveVolAndPan1136_ptr = void (WINAPIV*)(uint32_t, float, float);
            using SetWaveVolAndPan1136_clbk = void (WINAPIV*)(uint32_t, float, float, SetWaveVolAndPan1136_ptr);
            using SetWorldViewMatrixVS1137_ptr = void (WINAPIV*)(float**);
            using SetWorldViewMatrixVS1137_clbk = void (WINAPIV*)(float**, SetWorldViewMatrixVS1137_ptr);
            using ShadowBeginScene1138_ptr = void (WINAPIV*)();
            using ShadowBeginScene1138_clbk = void (WINAPIV*)(ShadowBeginScene1138_ptr);
            using ShadowEndScene1139_ptr = void (WINAPIV*)();
            using ShadowEndScene1139_clbk = void (WINAPIV*)(ShadowEndScene1139_ptr);
            using ShortToFloat1141_ptr = void (WINAPIV*)(int16_t*, float*, int);
            using ShortToFloat1141_clbk = void (WINAPIV*)(int16_t*, float*, int, ShortToFloat1141_ptr);
            using SmoothedNoise11142_ptr = float (WINAPIV*)(int32_t);
            using SmoothedNoise11142_clbk = float (WINAPIV*)(int32_t, SmoothedNoise11142_ptr);
            using SmoothedNoise11143_ptr = float (WINAPIV*)(int32_t, int32_t);
            using SmoothedNoise11143_clbk = float (WINAPIV*)(int32_t, int32_t, SmoothedNoise11143_ptr);
            using SplitString1145_ptr = uint64_t (WINAPIV*)(char*, char*, struct std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >*);
            using SplitString1145_clbk = uint64_t (WINAPIV*)(char*, char*, struct std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >*, SplitString1145_ptr);
            using StopR3MP31146_ptr = void (WINAPIV*)();
            using StopR3MP31146_clbk = void (WINAPIV*)(StopR3MP31146_ptr);
            using StopStreamR3MP31147_ptr = void (WINAPIV*)();
            using StopStreamR3MP31147_clbk = void (WINAPIV*)(StopStreamR3MP31147_ptr);
            using StopWave1148_ptr = void (WINAPIV*)(uint32_t);
            using StopWave1148_clbk = void (WINAPIV*)(uint32_t, StopWave1148_ptr);
            using StripEXT1149_ptr = void (WINAPIV*)(char*);
            using StripEXT1149_clbk = void (WINAPIV*)(char*, StripEXT1149_ptr);
            using StripFinalPath1150_ptr = void (WINAPIV*)(char*);
            using StripFinalPath1150_clbk = void (WINAPIV*)(char*, StripFinalPath1150_ptr);
            using StripName1151_ptr = void (WINAPIV*)(char*);
            using StripName1151_clbk = void (WINAPIV*)(char*, StripName1151_ptr);
            using StripPath1152_ptr = void (WINAPIV*)(char*);
            using StripPath1152_clbk = void (WINAPIV*)(char*, StripPath1152_ptr);
            using TestAllGroup1153_ptr = void (WINAPIV*)(void*, void*, int, int);
            using TestAllGroup1153_clbk = void (WINAPIV*)(void*, void*, int, int, TestAllGroup1153_ptr);
            using TestWriteFile1154_ptr = void (WINAPIV*)(char*, char*);
            using TestWriteFile1154_clbk = void (WINAPIV*)(char*, char*, TestWriteFile1154_ptr);
            using TextureCopy1155_ptr = void (WINAPIV*)(struct CTextureRender*, void*);
            using TextureCopy1155_clbk = void (WINAPIV*)(struct CTextureRender*, void*, TextureCopy1155_ptr);
            using TextureSplatting1156_ptr = void (WINAPIV*)(struct _BSP_MAT_GROUP*, int);
            using TextureSplatting1156_clbk = void (WINAPIV*)(struct _BSP_MAT_GROUP*, int, TextureSplatting1156_ptr);
            using TransformVertex1157_ptr = int64_t (WINAPIV*)(float*, float**, float*);
            using TransformVertex1157_clbk = int64_t (WINAPIV*)(float*, float**, float*, TransformVertex1157_ptr);
            using TransformVertex1158_ptr = int64_t (WINAPIV*)(float*, float**, float*, float*);
            using TransformVertex1158_clbk = int64_t (WINAPIV*)(float*, float**, float*, float*, TransformVertex1158_ptr);
            using UnLightMappingTex11159_ptr = void (WINAPIV*)();
            using UnLightMappingTex11159_clbk = void (WINAPIV*)(UnLightMappingTex11159_ptr);
            using UnSetDynamicLight1160_ptr = void (WINAPIV*)();
            using UnSetDynamicLight1160_clbk = void (WINAPIV*)(UnSetDynamicLight1160_ptr);
            using UnSetMatAlphaColor1161_ptr = void (WINAPIV*)();
            using UnSetMatAlphaColor1161_clbk = void (WINAPIV*)(UnSetMatAlphaColor1161_ptr);
            using UnSetProjectedShadow1162_ptr = void (WINAPIV*)();
            using UnSetProjectedShadow1162_clbk = void (WINAPIV*)(UnSetProjectedShadow1162_ptr);
            using UnTextureSplatting1163_ptr = void (WINAPIV*)();
            using UnTextureSplatting1163_clbk = void (WINAPIV*)(UnTextureSplatting1163_ptr);
            using UpdateStreamMP31164_ptr = void (WINAPIV*)();
            using UpdateStreamMP31164_clbk = void (WINAPIV*)(UpdateStreamMP31164_ptr);
            using Vector3fTransform1165_ptr = void (WINAPIV*)(float*, float*, float**);
            using Vector3fTransform1165_clbk = void (WINAPIV*)(float*, float*, float**, Vector3fTransform1165_ptr);
            using VertFlipBuf1166_ptr = int64_t (WINAPIV*)(uint8_t*, unsigned int, unsigned int);
            using VertFlipBuf1166_clbk = int64_t (WINAPIV*)(uint8_t*, unsigned int, unsigned int, VertFlipBuf1166_ptr);
            using W2M1168_ptr = bool (WINAPIV*)(char*, char*, unsigned int);
            using W2M1168_clbk = bool (WINAPIV*)(char*, char*, unsigned int, W2M1168_ptr);
            using WCHARTOMULTI1170_ptr = bool (WINAPIV*)(wchar_t*, char*, unsigned int);
            using WCHARTOMULTI1170_clbk = bool (WINAPIV*)(wchar_t*, char*, unsigned int, WCHARTOMULTI1170_ptr);
            using Warning1171_ptr = void (WINAPIV*)(char*, char*);
            using Warning1171_clbk = void (WINAPIV*)(char*, char*, Warning1171_ptr);
            using WndProc1172_ptr = int64_t (WINAPIV*)(HWND, unsigned int, uint64_t, int64_t);
            using WndProc1172_clbk = int64_t (WINAPIV*)(HWND, unsigned int, uint64_t, int64_t, WndProc1172_ptr);
            using WriteCheatLog1174_ptr = void (WINAPIV*)(char*, struct CPlayer*);
            using WriteCheatLog1174_clbk = void (WINAPIV*)(char*, struct CPlayer*, WriteCheatLog1174_ptr);
            using WriteLogFileLong1176_ptr = bool (WINAPIV*)(char*, char*);
            using WriteLogFileLong1176_clbk = bool (WINAPIV*)(char*, char*, WriteLogFileLong1176_ptr);
            using WriteServerStartHistory1178_ptr = void (WINAPIV*)(char*);
            using WriteServerStartHistory1178_clbk = void (WINAPIV*)(char*, WriteServerStartHistory1178_ptr);
            using WriteTableData1180_ptr = bool (WINAPIV*)(int, struct CRecordData*, bool, char*);
            using WriteTableData1180_clbk = bool (WINAPIV*)(int, struct CRecordData*, bool, char*, WriteTableData1180_ptr);
            using WriteTableDataPart1182_ptr = bool (WINAPIV*)(int, struct CRecordData*, char*);
            using WriteTableDataPart1182_clbk = bool (WINAPIV*)(int, struct CRecordData*, char*, WriteTableDataPart1182_ptr);
            using _AfxInitManaged1184_ptr = int (WINAPIV*)();
            using _AfxInitManaged1184_clbk = int (WINAPIV*)(_AfxInitManaged1184_ptr);
            using _CalcPayExgRatePerRace1186_ptr = int (WINAPIV*)(float*, int);
            using _CalcPayExgRatePerRace1186_clbk = int (WINAPIV*)(float*, int, _CalcPayExgRatePerRace1186_ptr);
            using _CcrFG_rs_CloseUserContext1187_ptr = void (WINAPIV*)(void**);
            using _CcrFG_rs_CloseUserContext1187_clbk = void (WINAPIV*)(void**, _CcrFG_rs_CloseUserContext1187_ptr);
            using _CcrFG_rs_CreateUserContext1188_ptr = void* (WINAPIV*)(uint32_t, uint8_t*, uint32_t, uint8_t*, uint32_t, uint32_t, void*);
            using _CcrFG_rs_CreateUserContext1188_clbk = void* (WINAPIV*)(uint32_t, uint8_t*, uint32_t, uint8_t*, uint32_t, uint32_t, void*, _CcrFG_rs_CreateUserContext1188_ptr);
            using _CcrFG_rs_DecryptPacket1189_ptr = int (WINAPIV*)(void*, uint8_t*, int);
            using _CcrFG_rs_DecryptPacket1189_clbk = int (WINAPIV*)(void*, uint8_t*, int, _CcrFG_rs_DecryptPacket1189_ptr);
            using _CcrFG_rs_EncryptPacket1190_ptr = int (WINAPIV*)(void*, uint8_t*, int);
            using _CcrFG_rs_EncryptPacket1190_clbk = int (WINAPIV*)(void*, uint8_t*, int, _CcrFG_rs_EncryptPacket1190_ptr);
            using _CcrFG_rs_GetLastError1191_ptr = uint32_t (WINAPIV*)();
            using _CcrFG_rs_GetLastError1191_clbk = uint32_t (WINAPIV*)(_CcrFG_rs_GetLastError1191_ptr);
            using _CcrFG_rs_Initialize1192_ptr = int (WINAPIV*)(int (WINAPIV*)(int32_t, void*, void*, int, void*), uint8_t*, uint32_t);
            using _CcrFG_rs_Initialize1192_clbk = int (WINAPIV*)(int (WINAPIV*)(int32_t, void*, void*, int, void*), uint8_t*, uint32_t, _CcrFG_rs_Initialize1192_ptr);
            using _CcrFG_rs_Uninitialize1193_ptr = void (WINAPIV*)();
            using _CcrFG_rs_Uninitialize1193_clbk = void (WINAPIV*)(_CcrFG_rs_Uninitialize1193_ptr);
            using _CheckCumulativeSF1195_ptr = int (WINAPIV*)(char, unsigned int, int*, int*, char**);
            using _CheckCumulativeSF1195_clbk = int (WINAPIV*)(char, unsigned int, int*, int*, char**, _CheckCumulativeSF1195_ptr);
            using _CheckDestMonsterLimitLv1197_ptr = bool (WINAPIV*)(int, int, char);
            using _CheckDestMonsterLimitLv1197_clbk = bool (WINAPIV*)(int, int, char, _CheckDestMonsterLimitLv1197_ptr);
            using _CheckPotionData1199_ptr = bool (WINAPIV*)(struct _CheckPotion_fld::_CheckEffectCode*, struct CPlayer*);
            using _CheckPotionData1199_clbk = bool (WINAPIV*)(struct _CheckPotion_fld::_CheckEffectCode*, struct CPlayer*, _CheckPotionData1199_ptr);
            using _CheckSameItem1201_ptr = bool (WINAPIV*)(char*, char*, char*, bool);
            using _CheckSameItem1201_clbk = bool (WINAPIV*)(char*, char*, char*, bool, _CheckSameItem1201_ptr);
            using _Check_SF_UseType1203_ptr = int (WINAPIV*)(struct _base_fld*, int);
            using _Check_SF_UseType1203_clbk = int (WINAPIV*)(struct _base_fld*, int, _Check_SF_UseType1203_ptr);
            using _CreateLootingNovusItem1205_ptr = void (WINAPIV*)(char*, char*, struct LuaParam3, struct LuaParam3);
            using _CreateLootingNovusItem1205_clbk = void (WINAPIV*)(char*, char*, struct LuaParam3, struct LuaParam3, _CreateLootingNovusItem1205_ptr);
            using _CreateMon1207_ptr = struct CMonster* (WINAPIV*)(char*, char*, float, float, float);
            using _CreateMon1207_clbk = struct CMonster* (WINAPIV*)(char*, char*, float, float, float, _CreateMon1207_ptr);
            using _DbgOut1209_ptr = HRESULT (WINAPIV*)(char*, unsigned int, HRESULT, char*);
            using _DbgOut1209_clbk = HRESULT (WINAPIV*)(char*, unsigned int, HRESULT, char*, _DbgOut1209_ptr);
            using _DeleteExceptionPtr1210_ptr = void (WINAPIV*)(__ExceptionPtr*);
            using _DeleteExceptionPtr1210_clbk = void (WINAPIV*)(__ExceptionPtr*, _DeleteExceptionPtr1210_ptr);
            using _FailItemShortBuffer1212_ptr = bool (WINAPIV*)(int*, char, struct _ItemCombine_exp_fld::_material*, struct _STORAGE_LIST::_db_con**);
            using _FailItemShortBuffer1212_clbk = bool (WINAPIV*)(int*, char, struct _ItemCombine_exp_fld::_material*, struct _STORAGE_LIST::_db_con**, _FailItemShortBuffer1212_ptr);
            using _GetLuaEventMgr1214_ptr = struct CLuaEventMgr* (WINAPIV*)();
            using _GetLuaEventMgr1214_clbk = struct CLuaEventMgr* (WINAPIV*)(_GetLuaEventMgr1214_ptr);
            using _GetMonsterContTime1216_ptr = uint16_t (WINAPIV*)(char, char);
            using _GetMonsterContTime1216_clbk = uint16_t (WINAPIV*)(char, char, _GetMonsterContTime1216_ptr);
            using _GetTempEffectValue1218_ptr = bool (WINAPIV*)(struct _skill_fld*, int, float*);
            using _GetTempEffectValue1218_clbk = bool (WINAPIV*)(struct _skill_fld*, int, float*, _GetTempEffectValue1218_ptr);
            using _GetTransTBL1220_ptr = struct _trand_tbl* (WINAPIV*)(int);
            using _GetTransTBL1220_clbk = struct _trand_tbl* (WINAPIV*)(int, _GetTransTBL1220_ptr);
            using _IsXmasSnowEffect1222_ptr = bool (WINAPIV*)(struct _sf_continous*);
            using _IsXmasSnowEffect1222_clbk = bool (WINAPIV*)(struct _sf_continous*, _IsXmasSnowEffect1222_ptr);
            using _KickReason1224_ptr = char* (WINAPIV*)(char);
            using _KickReason1224_clbk = char* (WINAPIV*)(char, _KickReason1224_ptr);
            using _Pow_int1226_ptr = long double (WINAPIV*)(long double, int);
            using _Pow_int1226_clbk = long double (WINAPIV*)(long double, int, _Pow_int1226_ptr);
            using _Pow_int1228_ptr = float (WINAPIV*)(float, int);
            using _Pow_int1228_clbk = float (WINAPIV*)(float, int, _Pow_int1228_ptr);
            using _RTC_AllocaFailure1229_ptr = void (WINAPIV*)(void*, struct _RTC_ALLOCA_NODE*, int);
            using _RTC_AllocaFailure1229_clbk = void (WINAPIV*)(void*, struct _RTC_ALLOCA_NODE*, int, _RTC_AllocaFailure1229_ptr);
            using _RTC_Failure1230_ptr = void (WINAPIV*)(void*, int);
            using _RTC_Failure1230_clbk = void (WINAPIV*)(void*, int, _RTC_Failure1230_ptr);
            using _RTC_GetErrorFunc1231_ret = int (WINAPIV*)(int, char*, int, char*, char*);
            using _RTC_GetErrorFunc1231_ptr = _RTC_GetErrorFunc1231_ret (WINAPIV*)(void*);
            using _RTC_GetErrorFunc1231_clbk = _RTC_GetErrorFunc1231_ret (WINAPIV*)(void*, _RTC_GetErrorFunc1231_ptr);
            using _RTC_GetErrorFuncW1232_ret = int (WINAPIV*)(int, wchar_t*, int, wchar_t*, wchar_t*);
            using _RTC_GetErrorFuncW1232_ptr = _RTC_GetErrorFuncW1232_ret (WINAPIV*)(void*);
            using _RTC_GetErrorFuncW1232_clbk = _RTC_GetErrorFuncW1232_ret (WINAPIV*)(void*, _RTC_GetErrorFuncW1232_ptr);
            using _RTC_GetSrcLine1233_ptr = int (WINAPIV*)(char*, wchar_t*, unsigned int, int*, wchar_t*, unsigned int);
            using _RTC_GetSrcLine1233_clbk = int (WINAPIV*)(char*, wchar_t*, unsigned int, int*, wchar_t*, unsigned int, _RTC_GetSrcLine1233_ptr);
            using _RTC_StackFailure1234_ptr = void (WINAPIV*)(void*, char*);
            using _RTC_StackFailure1234_clbk = void (WINAPIV*)(void*, char*, _RTC_StackFailure1234_ptr);
            using _ReadEconomyIniFile1236_ptr = bool (WINAPIV*)();
            using _ReadEconomyIniFile1236_clbk = bool (WINAPIV*)(_ReadEconomyIniFile1236_ptr);
            using _SearchPlayer1238_ptr = unsigned int (WINAPIV*)(char*);
            using _SearchPlayer1238_clbk = unsigned int (WINAPIV*)(char*, _SearchPlayer1238_ptr);
            using _UpdateNewEconomy1240_ptr = void (WINAPIV*)(struct _economy_calc_data*);
            using _UpdateNewEconomy1240_clbk = void (WINAPIV*)(struct _economy_calc_data*, _UpdateNewEconomy1240_ptr);
            using _UpdateRateSendToAllPlayer1242_ptr = void (WINAPIV*)();
            using _UpdateRateSendToAllPlayer1242_clbk = void (WINAPIV*)(_UpdateRateSendToAllPlayer1242_ptr);
            using __ArrayUnwind1243_ptr = void (WINAPIV*)(void*, uint64_t, int, void (WINAPIV*)(void*));
            using __ArrayUnwind1243_clbk = void (WINAPIV*)(void*, uint64_t, int, void (WINAPIV*)(void*), __ArrayUnwind1243_ptr);
            using __CxxUnhandledExceptionFilter1244_ptr = int (WINAPIV*)(struct _EXCEPTION_POINTERS*);
            using __CxxUnhandledExceptionFilter1244_clbk = int (WINAPIV*)(struct _EXCEPTION_POINTERS*, __CxxUnhandledExceptionFilter1244_ptr);
            using __destroy_item1246_ptr = bool (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_result_zocl::__item*, struct _STORAGE_LIST::_db_con*, struct _combine_ex_item_request_clzo::_list*, int);
            using __destroy_item1246_clbk = bool (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_result_zocl::__item*, struct _STORAGE_LIST::_db_con*, struct _combine_ex_item_request_clzo::_list*, int, __destroy_item1246_ptr);
            using __make_item1248_ptr = char (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_result_zocl::__item*, struct _ItemCombine_exp_fld*, int, char);
            using __make_item1248_clbk = char (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_result_zocl::__item*, struct _ItemCombine_exp_fld*, int, char, __make_item1248_ptr);
            using __trace1250_ptr = void (WINAPIV*)(char*);
            using __trace1250_clbk = void (WINAPIV*)(char*, __trace1250_ptr);
            using _false1252_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*);
            using _false1252_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, _false1252_ptr);
            using clear_file1269_ptr = void (WINAPIV*)(char*, unsigned int);
            using clear_file1269_clbk = void (WINAPIV*)(char*, unsigned int, clear_file1269_ptr);
            using ct_CashEventStart1273_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_CashEventStart1273_clbk = bool (WINAPIV*)(struct CPlayer*, ct_CashEventStart1273_ptr);
            using ct_CdeEndup1275_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_CdeEndup1275_clbk = bool (WINAPIV*)(struct CPlayer*, ct_CdeEndup1275_ptr);
            using ct_CdeStart1277_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_CdeStart1277_clbk = bool (WINAPIV*)(struct CPlayer*, ct_CdeStart1277_ptr);
            using ct_ClassRefineEvent1279_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_ClassRefineEvent1279_clbk = bool (WINAPIV*)(struct CPlayer*, ct_ClassRefineEvent1279_ptr);
            using ct_ClearSettleOwnerGuild1281_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_ClearSettleOwnerGuild1281_clbk = bool (WINAPIV*)(struct CPlayer*, ct_ClearSettleOwnerGuild1281_ptr);
            using ct_ConEventStart1283_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_ConEventStart1283_clbk = bool (WINAPIV*)(struct CPlayer*, ct_ConEventStart1283_ptr);
            using ct_Gold_Age_Event_Status1285_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_Gold_Age_Event_Status1285_clbk = bool (WINAPIV*)(struct CPlayer*, ct_Gold_Age_Event_Status1285_ptr);
            using ct_Gold_Age_Get_Box_Cnt1287_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_Gold_Age_Get_Box_Cnt1287_clbk = bool (WINAPIV*)(struct CPlayer*, ct_Gold_Age_Get_Box_Cnt1287_ptr);
            using ct_Gold_Age_Set_Event_Status1289_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_Gold_Age_Set_Event_Status1289_clbk = bool (WINAPIV*)(struct CPlayer*, ct_Gold_Age_Set_Event_Status1289_ptr);
            using ct_HolyKeeperAttack1291_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_HolyKeeperAttack1291_clbk = bool (WINAPIV*)(struct CPlayer*, ct_HolyKeeperAttack1291_ptr);
            using ct_HolySystem1293_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_HolySystem1293_clbk = bool (WINAPIV*)(struct CPlayer*, ct_HolySystem1293_ptr);
            using ct_HolySystem_Jp1295_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_HolySystem_Jp1295_clbk = bool (WINAPIV*)(struct CPlayer*, ct_HolySystem_Jp1295_ptr);
            using ct_InformCristalBattleBeforeAnHour1297_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_InformCristalBattleBeforeAnHour1297_clbk = bool (WINAPIV*)(struct CPlayer*, ct_InformCristalBattleBeforeAnHour1297_ptr);
            using ct_InformPatriarchProcessor1299_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_InformPatriarchProcessor1299_clbk = bool (WINAPIV*)(struct CPlayer*, ct_InformPatriarchProcessor1299_ptr);
            using ct_NuAfterEffect1301_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_NuAfterEffect1301_clbk = bool (WINAPIV*)(struct CPlayer*, ct_NuAfterEffect1301_ptr);
            using ct_PcBandPrimium1303_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_PcBandPrimium1303_clbk = bool (WINAPIV*)(struct CPlayer*, ct_PcBandPrimium1303_ptr);
            using ct_PvpLimitInit1305_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_PvpLimitInit1305_clbk = bool (WINAPIV*)(struct CPlayer*, ct_PvpLimitInit1305_ptr);
            using ct_ReqChangeHonorGuild1307_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_ReqChangeHonorGuild1307_clbk = bool (WINAPIV*)(struct CPlayer*, ct_ReqChangeHonorGuild1307_ptr);
            using ct_ReqPunishment1309_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_ReqPunishment1309_clbk = bool (WINAPIV*)(struct CPlayer*, ct_ReqPunishment1309_ptr);
            using ct_SetGuildGrade1311_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetGuildGrade1311_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetGuildGrade1311_ptr);
            using ct_SetGuildGradeByGuildSerial1313_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetGuildGradeByGuildSerial1313_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetGuildGradeByGuildSerial1313_ptr);
            using ct_SetGuildGradeByName1315_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetGuildGradeByName1315_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetGuildGradeByName1315_ptr);
            using ct_SetGuildMaster1317_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetGuildMaster1317_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetGuildMaster1317_ptr);
            using ct_SetMaxLevelLimit1319_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetMaxLevelLimit1319_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetMaxLevelLimit1319_ptr);
            using ct_SetPatriarchAuto1321_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetPatriarchAuto1321_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetPatriarchAuto1321_ptr);
            using ct_SetPatriarchClear1323_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetPatriarchClear1323_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetPatriarchClear1323_ptr);
            using ct_SetPatriarchGroup1325_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetPatriarchGroup1325_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetPatriarchGroup1325_ptr);
            using ct_SetPatriarchProcessor1327_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetPatriarchProcessor1327_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetPatriarchProcessor1327_ptr);
            using ct_SetSettleOwnerGuild1329_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_SetSettleOwnerGuild1329_clbk = bool (WINAPIV*)(struct CPlayer*, ct_SetSettleOwnerGuild1329_ptr);
            using ct_Win_RaceWar1331_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_Win_RaceWar1331_clbk = bool (WINAPIV*)(struct CPlayer*, ct_Win_RaceWar1331_ptr);
            using ct_action_point_set1333_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_action_point_set1333_clbk = bool (WINAPIV*)(struct CPlayer*, ct_action_point_set1333_ptr);
            using ct_add_guild_schedule1335_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_add_guild_schedule1335_clbk = bool (WINAPIV*)(struct CPlayer*, ct_add_guild_schedule1335_ptr);
            using ct_add_one_day_guild_schedule1337_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_add_one_day_guild_schedule1337_clbk = bool (WINAPIV*)(struct CPlayer*, ct_add_one_day_guild_schedule1337_ptr);
            using ct_all_item_muzi1339_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_all_item_muzi1339_clbk = bool (WINAPIV*)(struct CPlayer*, ct_all_item_muzi1339_ptr);
            using ct_all_map1341_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_all_map1341_clbk = bool (WINAPIV*)(struct CPlayer*, ct_all_map1341_ptr);
            using ct_alter_cashbag1343_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_alter_cashbag1343_clbk = bool (WINAPIV*)(struct CPlayer*, ct_alter_cashbag1343_ptr);
            using ct_alter_dalant1345_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_alter_dalant1345_clbk = bool (WINAPIV*)(struct CPlayer*, ct_alter_dalant1345_ptr);
            using ct_alter_exp1347_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_alter_exp1347_clbk = bool (WINAPIV*)(struct CPlayer*, ct_alter_exp1347_ptr);
            using ct_alter_gold1349_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_alter_gold1349_clbk = bool (WINAPIV*)(struct CPlayer*, ct_alter_gold1349_ptr);
            using ct_alter_inven_dur1351_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_alter_inven_dur1351_clbk = bool (WINAPIV*)(struct CPlayer*, ct_alter_inven_dur1351_ptr);
            using ct_alter_lv1353_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_alter_lv1353_clbk = bool (WINAPIV*)(struct CPlayer*, ct_alter_lv1353_ptr);
            using ct_alter_pvp1355_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_alter_pvp1355_clbk = bool (WINAPIV*)(struct CPlayer*, ct_alter_pvp1355_ptr);
            using ct_amp_full1357_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_amp_full1357_clbk = bool (WINAPIV*)(struct CPlayer*, ct_amp_full1357_ptr);
            using ct_amp_set1359_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_amp_set1359_clbk = bool (WINAPIV*)(struct CPlayer*, ct_amp_set1359_ptr);
            using ct_animus_attack_grade1361_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_animus_attack_grade1361_clbk = bool (WINAPIV*)(struct CPlayer*, ct_animus_attack_grade1361_ptr);
            using ct_animus_recall_term1363_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_animus_recall_term1363_clbk = bool (WINAPIV*)(struct CPlayer*, ct_animus_recall_term1363_ptr);
            using ct_animusexp1365_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_animusexp1365_clbk = bool (WINAPIV*)(struct CPlayer*, ct_animusexp1365_ptr);
            using ct_basemastery1367_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_basemastery1367_clbk = bool (WINAPIV*)(struct CPlayer*, ct_basemastery1367_ptr);
            using ct_boss_sms_cancel1369_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_boss_sms_cancel1369_clbk = bool (WINAPIV*)(struct CPlayer*, ct_boss_sms_cancel1369_ptr);
            using ct_buf_potion_use1371_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_buf_potion_use1371_clbk = bool (WINAPIV*)(struct CPlayer*, ct_buf_potion_use1371_ptr);
            using ct_cashitembuy1373_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_cashitembuy1373_clbk = bool (WINAPIV*)(struct CPlayer*, ct_cashitembuy1373_ptr);
            using ct_change_class1375_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_change_class1375_clbk = bool (WINAPIV*)(struct CPlayer*, ct_change_class1375_ptr);
            using ct_change_degree1377_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_change_degree1377_clbk = bool (WINAPIV*)(struct CPlayer*, ct_change_degree1377_ptr);
            using ct_change_master_elect1379_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_change_master_elect1379_clbk = bool (WINAPIV*)(struct CPlayer*, ct_change_master_elect1379_ptr);
            using ct_change_mastery1381_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_change_mastery1381_clbk = bool (WINAPIV*)(struct CPlayer*, ct_change_mastery1381_ptr);
            using ct_chatsave1383_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_chatsave1383_clbk = bool (WINAPIV*)(struct CPlayer*, ct_chatsave1383_ptr);
            using ct_check_guild_batlle_goal1385_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_check_guild_batlle_goal1385_clbk = bool (WINAPIV*)(struct CPlayer*, ct_check_guild_batlle_goal1385_ptr);
            using ct_circle_mon_kill1387_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_circle_mon_kill1387_clbk = bool (WINAPIV*)(struct CPlayer*, ct_circle_mon_kill1387_ptr);
            using ct_circle_user_num1389_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_circle_user_num1389_clbk = bool (WINAPIV*)(struct CPlayer*, ct_circle_user_num1389_ptr);
            using ct_combine_ex_result1391_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_combine_ex_result1391_clbk = bool (WINAPIV*)(struct CPlayer*, ct_combine_ex_result1391_ptr);
            using ct_complete_quest1393_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_complete_quest1393_clbk = bool (WINAPIV*)(struct CPlayer*, ct_complete_quest1393_ptr);
            using ct_complete_quest_other1395_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_complete_quest_other1395_clbk = bool (WINAPIV*)(struct CPlayer*, ct_complete_quest_other1395_ptr);
            using ct_cont_effet_clear1397_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_cont_effet_clear1397_clbk = bool (WINAPIV*)(struct CPlayer*, ct_cont_effet_clear1397_ptr);
            using ct_cont_effet_time1399_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_cont_effet_time1399_clbk = bool (WINAPIV*)(struct CPlayer*, ct_cont_effet_time1399_ptr);
            using ct_continue_palytime_inc1401_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_continue_palytime_inc1401_clbk = bool (WINAPIV*)(struct CPlayer*, ct_continue_palytime_inc1401_ptr);
            using ct_copy_avator1403_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_copy_avator1403_clbk = bool (WINAPIV*)(struct CPlayer*, ct_copy_avator1403_ptr);
            using ct_create_guildbattle_field_object1405_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_create_guildbattle_field_object1405_clbk = bool (WINAPIV*)(struct CPlayer*, ct_create_guildbattle_field_object1405_ptr);
            using ct_cur_guildbattle_color1407_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_cur_guildbattle_color1407_clbk = bool (WINAPIV*)(struct CPlayer*, ct_cur_guildbattle_color1407_ptr);
            using ct_darkholereward1409_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_darkholereward1409_clbk = bool (WINAPIV*)(struct CPlayer*, ct_darkholereward1409_ptr);
            using ct_defense_item_grace1411_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_defense_item_grace1411_clbk = bool (WINAPIV*)(struct CPlayer*, ct_defense_item_grace1411_ptr);
            using ct_defense_item_grace_Jp1413_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_defense_item_grace_Jp1413_clbk = bool (WINAPIV*)(struct CPlayer*, ct_defense_item_grace_Jp1413_ptr);
            using ct_destroy_gravitystone1415_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_destroy_gravitystone1415_clbk = bool (WINAPIV*)(struct CPlayer*, ct_destroy_gravitystone1415_ptr);
            using ct_destroy_guildbattle_field_object1417_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_destroy_guildbattle_field_object1417_clbk = bool (WINAPIV*)(struct CPlayer*, ct_destroy_guildbattle_field_object1417_ptr);
            using ct_destroy_system_tower1419_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_destroy_system_tower1419_clbk = bool (WINAPIV*)(struct CPlayer*, ct_destroy_system_tower1419_ptr);
            using ct_die1421_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_die1421_clbk = bool (WINAPIV*)(struct CPlayer*, ct_die1421_ptr);
            using ct_drop_gravitystone1423_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_drop_gravitystone1423_clbk = bool (WINAPIV*)(struct CPlayer*, ct_drop_gravitystone1423_ptr);
            using ct_drop_jade1425_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_drop_jade1425_clbk = bool (WINAPIV*)(struct CPlayer*, ct_drop_jade1425_ptr);
            using ct_elect_info_player1427_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_elect_info_player1427_clbk = bool (WINAPIV*)(struct CPlayer*, ct_elect_info_player1427_ptr);
            using ct_elect_set_env1429_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_elect_set_env1429_clbk = bool (WINAPIV*)(struct CPlayer*, ct_elect_set_env1429_ptr);
            using ct_elect_set_player1431_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_elect_set_player1431_clbk = bool (WINAPIV*)(struct CPlayer*, ct_elect_set_player1431_ptr);
            using ct_eventset_start1433_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_eventset_start1433_clbk = bool (WINAPIV*)(struct CPlayer*, ct_eventset_start1433_ptr);
            using ct_eventset_stop1435_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_eventset_stop1435_clbk = bool (WINAPIV*)(struct CPlayer*, ct_eventset_stop1435_ptr);
            using ct_exception1437_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_exception1437_clbk = bool (WINAPIV*)(struct CPlayer*, ct_exception1437_ptr);
            using ct_exip_keeper1439_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_exip_keeper1439_clbk = bool (WINAPIV*)(struct CPlayer*, ct_exip_keeper1439_ptr);
            using ct_exit_stone1441_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_exit_stone1441_clbk = bool (WINAPIV*)(struct CPlayer*, ct_exit_stone1441_ptr);
            using ct_expire_pcbang1443_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_expire_pcbang1443_clbk = bool (WINAPIV*)(struct CPlayer*, ct_expire_pcbang1443_ptr);
            using ct_free_ride_ship1445_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_free_ride_ship1445_clbk = bool (WINAPIV*)(struct CPlayer*, ct_free_ride_ship1445_ptr);
            using ct_free_sf_by_class1447_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_free_sf_by_class1447_clbk = bool (WINAPIV*)(struct CPlayer*, ct_free_sf_by_class1447_ptr);
            using ct_full_animus_gauge1449_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_full_animus_gauge1449_clbk = bool (WINAPIV*)(struct CPlayer*, ct_full_animus_gauge1449_ptr);
            using ct_full_force1451_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_full_force1451_clbk = bool (WINAPIV*)(struct CPlayer*, ct_full_force1451_ptr);
            using ct_full_gauge1453_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_full_gauge1453_clbk = bool (WINAPIV*)(struct CPlayer*, ct_full_gauge1453_ptr);
            using ct_fullset1455_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_fullset1455_clbk = bool (WINAPIV*)(struct CPlayer*, ct_fullset1455_ptr);
            using ct_get_gravitystone1457_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_get_gravitystone1457_clbk = bool (WINAPIV*)(struct CPlayer*, ct_get_gravitystone1457_ptr);
            using ct_goto_char1459_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_goto_char1459_clbk = bool (WINAPIV*)(struct CPlayer*, ct_goto_char1459_ptr);
            using ct_goto_mine1461_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_goto_mine1461_clbk = bool (WINAPIV*)(struct CPlayer*, ct_goto_mine1461_ptr);
            using ct_goto_monster1463_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_goto_monster1463_clbk = bool (WINAPIV*)(struct CPlayer*, ct_goto_monster1463_ptr);
            using ct_goto_npc1465_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_goto_npc1465_clbk = bool (WINAPIV*)(struct CPlayer*, ct_goto_npc1465_ptr);
            using ct_goto_shipport_eder1467_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_goto_shipport_eder1467_clbk = bool (WINAPIV*)(struct CPlayer*, ct_goto_shipport_eder1467_ptr);
            using ct_goto_shipport_town1469_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_goto_shipport_town1469_clbk = bool (WINAPIV*)(struct CPlayer*, ct_goto_shipport_town1469_ptr);
            using ct_goto_stone1471_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_goto_stone1471_clbk = bool (WINAPIV*)(struct CPlayer*, ct_goto_stone1471_ptr);
            using ct_guild_battle_force_stone1473_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_guild_battle_force_stone1473_clbk = bool (WINAPIV*)(struct CPlayer*, ct_guild_battle_force_stone1473_ptr);
            using ct_guild_call1475_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_guild_call1475_clbk = bool (WINAPIV*)(struct CPlayer*, ct_guild_call1475_ptr);
            using ct_guild_info1477_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_guild_info1477_clbk = bool (WINAPIV*)(struct CPlayer*, ct_guild_info1477_ptr);
            using ct_guild_suggest1479_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_guild_suggest1479_clbk = bool (WINAPIV*)(struct CPlayer*, ct_guild_suggest1479_ptr);
            using ct_half_gauge1481_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_half_gauge1481_clbk = bool (WINAPIV*)(struct CPlayer*, ct_half_gauge1481_ptr);
            using ct_init_monster1483_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_init_monster1483_clbk = bool (WINAPIV*)(struct CPlayer*, ct_init_monster1483_ptr);
            using ct_inven_empty1485_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_inven_empty1485_clbk = bool (WINAPIV*)(struct CPlayer*, ct_inven_empty1485_ptr);
            using ct_itemloot1487_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_itemloot1487_clbk = bool (WINAPIV*)(struct CPlayer*, ct_itemloot1487_ptr);
            using ct_jump_to_pos1489_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_jump_to_pos1489_clbk = bool (WINAPIV*)(struct CPlayer*, ct_jump_to_pos1489_ptr);
            using ct_kick_player1491_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_kick_player1491_clbk = bool (WINAPIV*)(struct CPlayer*, ct_kick_player1491_ptr);
            using ct_loadcashamount1493_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_loadcashamount1493_clbk = bool (WINAPIV*)(struct CPlayer*, ct_loadcashamount1493_ptr);
            using ct_look_like_boss1495_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_look_like_boss1495_clbk = bool (WINAPIV*)(struct CPlayer*, ct_look_like_boss1495_ptr);
            using ct_loot_bag1497_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_loot_bag1497_clbk = bool (WINAPIV*)(struct CPlayer*, ct_loot_bag1497_ptr);
            using ct_loot_dungeon1499_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_loot_dungeon1499_clbk = bool (WINAPIV*)(struct CPlayer*, ct_loot_dungeon1499_ptr);
            using ct_loot_item1501_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_loot_item1501_clbk = bool (WINAPIV*)(struct CPlayer*, ct_loot_item1501_ptr);
            using ct_loot_material1503_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_loot_material1503_clbk = bool (WINAPIV*)(struct CPlayer*, ct_loot_material1503_ptr);
            using ct_loot_mine1505_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_loot_mine1505_clbk = bool (WINAPIV*)(struct CPlayer*, ct_loot_mine1505_ptr);
            using ct_loot_tower1507_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_loot_tower1507_clbk = bool (WINAPIV*)(struct CPlayer*, ct_loot_tower1507_ptr);
            using ct_loot_upgrade1509_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_loot_upgrade1509_clbk = bool (WINAPIV*)(struct CPlayer*, ct_loot_upgrade1509_ptr);
            using ct_loot_upgrade_item1511_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_loot_upgrade_item1511_clbk = bool (WINAPIV*)(struct CPlayer*, ct_loot_upgrade_item1511_ptr);
            using ct_lua_command1513_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_lua_command1513_clbk = bool (WINAPIV*)(struct CPlayer*, ct_lua_command1513_ptr);
            using ct_make_system_tower1515_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_make_system_tower1515_clbk = bool (WINAPIV*)(struct CPlayer*, ct_make_system_tower1515_ptr);
            using ct_makeitem_need_matrial1517_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_makeitem_need_matrial1517_clbk = bool (WINAPIV*)(struct CPlayer*, ct_makeitem_need_matrial1517_ptr);
            using ct_makeitem_no_matrial1519_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_makeitem_no_matrial1519_clbk = bool (WINAPIV*)(struct CPlayer*, ct_makeitem_no_matrial1519_ptr);
            using ct_manage_guild1521_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_manage_guild1521_clbk = bool (WINAPIV*)(struct CPlayer*, ct_manage_guild1521_ptr);
            using ct_max_attack1523_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_max_attack1523_clbk = bool (WINAPIV*)(struct CPlayer*, ct_max_attack1523_ptr);
            using ct_mepcbang1525_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_mepcbang1525_clbk = bool (WINAPIV*)(struct CPlayer*, ct_mepcbang1525_ptr);
            using ct_min_attack1527_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_min_attack1527_clbk = bool (WINAPIV*)(struct CPlayer*, ct_min_attack1527_ptr);
            using ct_minespeed1529_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_minespeed1529_clbk = bool (WINAPIV*)(struct CPlayer*, ct_minespeed1529_ptr);
            using ct_mormal_attack1531_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_mormal_attack1531_clbk = bool (WINAPIV*)(struct CPlayer*, ct_mormal_attack1531_ptr);
            using ct_party_call1533_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_party_call1533_clbk = bool (WINAPIV*)(struct CPlayer*, ct_party_call1533_ptr);
            using ct_pass_dungeon1535_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pass_dungeon1535_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pass_dungeon1535_ptr);
            using ct_pass_sch1537_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pass_sch1537_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pass_sch1537_ptr);
            using ct_pcanimusexp1539_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pcanimusexp1539_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pcanimusexp1539_ptr);
            using ct_pcbangitemget1541_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pcbangitemget1541_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pcbangitemget1541_ptr);
            using ct_pcbasemastery1543_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pcbasemastery1543_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pcbasemastery1543_ptr);
            using ct_pcitemloot1545_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pcitemloot1545_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pcitemloot1545_ptr);
            using ct_pcminespeed1547_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pcminespeed1547_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pcminespeed1547_ptr);
            using ct_pcplayerexp1549_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pcplayerexp1549_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pcplayerexp1549_ptr);
            using ct_pcroom_premium1551_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pcroom_premium1551_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pcroom_premium1551_ptr);
            using ct_pcsfmastery1553_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_pcsfmastery1553_clbk = bool (WINAPIV*)(struct CPlayer*, ct_pcsfmastery1553_ptr);
            using ct_period_time_set1555_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_period_time_set1555_clbk = bool (WINAPIV*)(struct CPlayer*, ct_period_time_set1555_ptr);
            using ct_playerexp1557_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_playerexp1557_clbk = bool (WINAPIV*)(struct CPlayer*, ct_playerexp1557_ptr);
            using ct_premium_rate1559_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_premium_rate1559_clbk = bool (WINAPIV*)(struct CPlayer*, ct_premium_rate1559_ptr);
            using ct_query_remain_ore1561_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_query_remain_ore1561_clbk = bool (WINAPIV*)(struct CPlayer*, ct_query_remain_ore1561_ptr);
            using ct_recall_monster1563_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_recall_monster1563_clbk = bool (WINAPIV*)(struct CPlayer*, ct_recall_monster1563_ptr);
            using ct_recall_player1565_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_recall_player1565_clbk = bool (WINAPIV*)(struct CPlayer*, ct_recall_player1565_ptr);
            using ct_recv_change_atrad_taxrate1567_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_recv_change_atrad_taxrate1567_clbk = bool (WINAPIV*)(struct CPlayer*, ct_recv_change_atrad_taxrate1567_ptr);
            using ct_recv_current_battle_info1569_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_recv_current_battle_info1569_clbk = bool (WINAPIV*)(struct CPlayer*, ct_recv_current_battle_info1569_ptr);
            using ct_recv_pvp_guild_rank1571_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_recv_pvp_guild_rank1571_clbk = bool (WINAPIV*)(struct CPlayer*, ct_recv_pvp_guild_rank1571_ptr);
            using ct_recv_reserved_schedulelist1573_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_recv_reserved_schedulelist1573_clbk = bool (WINAPIV*)(struct CPlayer*, ct_recv_reserved_schedulelist1573_ptr);
            using ct_recv_total_guild_rank1575_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_recv_total_guild_rank1575_clbk = bool (WINAPIV*)(struct CPlayer*, ct_recv_total_guild_rank1575_ptr);
            using ct_regen_gravitystone1577_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_regen_gravitystone1577_clbk = bool (WINAPIV*)(struct CPlayer*, ct_regen_gravitystone1577_ptr);
            using ct_release_loot_free1579_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_release_loot_free1579_clbk = bool (WINAPIV*)(struct CPlayer*, ct_release_loot_free1579_ptr);
            using ct_release_make_succ1581_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_release_make_succ1581_clbk = bool (WINAPIV*)(struct CPlayer*, ct_release_make_succ1581_ptr);
            using ct_release_matchless1583_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_release_matchless1583_clbk = bool (WINAPIV*)(struct CPlayer*, ct_release_matchless1583_ptr);
            using ct_release_never_die1585_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_release_never_die1585_clbk = bool (WINAPIV*)(struct CPlayer*, ct_release_never_die1585_ptr);
            using ct_release_punishment1587_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_release_punishment1587_clbk = bool (WINAPIV*)(struct CPlayer*, ct_release_punishment1587_ptr);
            using ct_remove_sf_delay1589_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_remove_sf_delay1589_clbk = bool (WINAPIV*)(struct CPlayer*, ct_remove_sf_delay1589_ptr);
            using ct_report_cri_hp1591_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_report_cri_hp1591_clbk = bool (WINAPIV*)(struct CPlayer*, ct_report_cri_hp1591_ptr);
            using ct_report_position1593_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_report_position1593_clbk = bool (WINAPIV*)(struct CPlayer*, ct_report_position1593_ptr);
            using ct_request_delete_quest1595_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_request_delete_quest1595_clbk = bool (WINAPIV*)(struct CPlayer*, ct_request_delete_quest1595_ptr);
            using ct_request_npc_quest1597_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_request_npc_quest1597_clbk = bool (WINAPIV*)(struct CPlayer*, ct_request_npc_quest1597_ptr);
            using ct_respawn_start1599_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_respawn_start1599_clbk = bool (WINAPIV*)(struct CPlayer*, ct_respawn_start1599_ptr);
            using ct_respawn_stop1601_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_respawn_stop1601_clbk = bool (WINAPIV*)(struct CPlayer*, ct_respawn_stop1601_ptr);
            using ct_resurrect_player1603_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_resurrect_player1603_clbk = bool (WINAPIV*)(struct CPlayer*, ct_resurrect_player1603_ptr);
            using ct_server_rate1605_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_server_rate1605_clbk = bool (WINAPIV*)(struct CPlayer*, ct_server_rate1605_ptr);
            using ct_server_time1607_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_server_time1607_clbk = bool (WINAPIV*)(struct CPlayer*, ct_server_time1607_ptr);
            using ct_set_animus_exp1609_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_animus_exp1609_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_animus_exp1609_ptr);
            using ct_set_animus_lv1611_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_animus_lv1611_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_animus_lv1611_ptr);
            using ct_set_damage_part1613_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_damage_part1613_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_damage_part1613_ptr);
            using ct_set_exp_rate1615_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_exp_rate1615_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_exp_rate1615_ptr);
            using ct_set_guildbattle_color1617_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_guildbattle_color1617_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_guildbattle_color1617_ptr);
            using ct_set_hfs_full1619_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_hfs_full1619_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_hfs_full1619_ptr);
            using ct_set_hp1621_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_hp1621_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_hp1621_ptr);
            using ct_set_jade_effect1623_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_jade_effect1623_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_jade_effect1623_ptr);
            using ct_set_kill_list_init1625_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_kill_list_init1625_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_kill_list_init1625_ptr);
            using ct_set_loot_free1627_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_loot_free1627_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_loot_free1627_ptr);
            using ct_set_make_succ1629_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_make_succ1629_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_make_succ1629_ptr);
            using ct_set_matchless1631_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_matchless1631_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_matchless1631_ptr);
            using ct_set_never_die1633_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_never_die1633_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_never_die1633_ptr);
            using ct_set_ore_amount1635_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_ore_amount1635_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_ore_amount1635_ptr);
            using ct_set_temp_cash_point1637_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_set_temp_cash_point1637_clbk = bool (WINAPIV*)(struct CPlayer*, ct_set_temp_cash_point1637_ptr);
            using ct_sfmastery1639_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_sfmastery1639_clbk = bool (WINAPIV*)(struct CPlayer*, ct_sfmastery1639_ptr);
            using ct_start_cri1641_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_start_cri1641_clbk = bool (WINAPIV*)(struct CPlayer*, ct_start_cri1641_ptr);
            using ct_start_keeper1643_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_start_keeper1643_clbk = bool (WINAPIV*)(struct CPlayer*, ct_start_keeper1643_ptr);
            using ct_take_gravitystone1645_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_take_gravitystone1645_clbk = bool (WINAPIV*)(struct CPlayer*, ct_take_gravitystone1645_ptr);
            using ct_takeholymental1647_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_takeholymental1647_clbk = bool (WINAPIV*)(struct CPlayer*, ct_takeholymental1647_ptr);
            using ct_telekinesis1649_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_telekinesis1649_clbk = bool (WINAPIV*)(struct CPlayer*, ct_telekinesis1649_ptr);
            using ct_tl_info_set1651_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_tl_info_set1651_clbk = bool (WINAPIV*)(struct CPlayer*, ct_tl_info_set1651_ptr);
            using ct_tl_info_view1653_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_tl_info_view1653_clbk = bool (WINAPIV*)(struct CPlayer*, ct_tl_info_view1653_ptr);
            using ct_tl_system_setting1655_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_tl_system_setting1655_clbk = bool (WINAPIV*)(struct CPlayer*, ct_tl_system_setting1655_ptr);
            using ct_tracing_hide1657_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_tracing_hide1657_clbk = bool (WINAPIV*)(struct CPlayer*, ct_tracing_hide1657_ptr);
            using ct_tracing_show1659_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_tracing_show1659_clbk = bool (WINAPIV*)(struct CPlayer*, ct_tracing_show1659_ptr);
            using ct_trap_attack_grade1661_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_trap_attack_grade1661_clbk = bool (WINAPIV*)(struct CPlayer*, ct_trap_attack_grade1661_ptr);
            using ct_trunk_init1663_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_trunk_init1663_clbk = bool (WINAPIV*)(struct CPlayer*, ct_trunk_init1663_ptr);
            using ct_up_allskill1665_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_up_allskill1665_clbk = bool (WINAPIV*)(struct CPlayer*, ct_up_allskill1665_ptr);
            using ct_up_allskill_pt1667_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_up_allskill_pt1667_clbk = bool (WINAPIV*)(struct CPlayer*, ct_up_allskill_pt1667_ptr);
            using ct_up_forceitem1669_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_up_forceitem1669_clbk = bool (WINAPIV*)(struct CPlayer*, ct_up_forceitem1669_ptr);
            using ct_up_forcemastery1671_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_up_forcemastery1671_clbk = bool (WINAPIV*)(struct CPlayer*, ct_up_forcemastery1671_ptr);
            using ct_up_skill1673_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_up_skill1673_clbk = bool (WINAPIV*)(struct CPlayer*, ct_up_skill1673_ptr);
            using ct_user_num1675_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_user_num1675_clbk = bool (WINAPIV*)(struct CPlayer*, ct_user_num1675_ptr);
            using ct_userchatban1677_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_userchatban1677_clbk = bool (WINAPIV*)(struct CPlayer*, ct_userchatban1677_ptr);
            using ct_ut_cancel_regist1679_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_ut_cancel_regist1679_clbk = bool (WINAPIV*)(struct CPlayer*, ct_ut_cancel_regist1679_ptr);
            using ct_ut_cancel_registlogout1681_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_ut_cancel_registlogout1681_clbk = bool (WINAPIV*)(struct CPlayer*, ct_ut_cancel_registlogout1681_ptr);
            using ct_view_method1683_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_view_method1683_clbk = bool (WINAPIV*)(struct CPlayer*, ct_view_method1683_ptr);
            using ct_vote_enable1685_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_vote_enable1685_clbk = bool (WINAPIV*)(struct CPlayer*, ct_vote_enable1685_ptr);
            using ct_whoami1687_ptr = bool (WINAPIV*)(struct CPlayer*);
            using ct_whoami1687_clbk = bool (WINAPIV*)(struct CPlayer*, ct_whoami1687_ptr);
            using cvt_string1689_ptr = char* (WINAPIV*)(int);
            using cvt_string1689_clbk = char* (WINAPIV*)(int, cvt_string1689_ptr);
            using eAddCutOre1691_ptr = void (WINAPIV*)(int, char, int);
            using eAddCutOre1691_clbk = void (WINAPIV*)(int, char, int, eAddCutOre1691_ptr);
            using eAddDalant1693_ptr = void (WINAPIV*)(int, int);
            using eAddDalant1693_clbk = void (WINAPIV*)(int, int, eAddDalant1693_ptr);
            using eAddGold1695_ptr = void (WINAPIV*)(int, int);
            using eAddGold1695_clbk = void (WINAPIV*)(int, int, eAddGold1695_ptr);
            using eAddMineOre1697_ptr = void (WINAPIV*)(int, char, int);
            using eAddMineOre1697_clbk = void (WINAPIV*)(int, char, int, eAddMineOre1697_ptr);
            using eGetCutOre1699_ptr = long double (WINAPIV*)(int, char);
            using eGetCutOre1699_clbk = long double (WINAPIV*)(int, char, eGetCutOre1699_ptr);
            using eGetDalant1701_ptr = long double (WINAPIV*)(int);
            using eGetDalant1701_clbk = long double (WINAPIV*)(int, eGetDalant1701_ptr);
            using eGetGold1703_ptr = long double (WINAPIV*)(int);
            using eGetGold1703_clbk = long double (WINAPIV*)(int, eGetGold1703_ptr);
            using eGetGuide1705_ptr = uint16_t (WINAPIV*)(int);
            using eGetGuide1705_clbk = uint16_t (WINAPIV*)(int, eGetGuide1705_ptr);
            using eGetGuideHistory1707_ptr = struct _economy_history_data* (WINAPIV*)();
            using eGetGuideHistory1707_clbk = struct _economy_history_data* (WINAPIV*)(eGetGuideHistory1707_ptr);
            using eGetLocalDate1709_ptr = unsigned int (WINAPIV*)();
            using eGetLocalDate1709_clbk = unsigned int (WINAPIV*)(eGetLocalDate1709_ptr);
            using eGetMgrValue1711_ptr = int (WINAPIV*)();
            using eGetMgrValue1711_clbk = int (WINAPIV*)(eGetMgrValue1711_ptr);
            using eGetMineOre1713_ptr = long double (WINAPIV*)(int, char);
            using eGetMineOre1713_clbk = long double (WINAPIV*)(int, char, eGetMineOre1713_ptr);
            using eGetOldCutOre1715_ptr = long double (WINAPIV*)(int, char);
            using eGetOldCutOre1715_clbk = long double (WINAPIV*)(int, char, eGetOldCutOre1715_ptr);
            using eGetOldDalant1717_ptr = long double (WINAPIV*)(int);
            using eGetOldDalant1717_clbk = long double (WINAPIV*)(int, eGetOldDalant1717_ptr);
            using eGetOldGold1719_ptr = long double (WINAPIV*)(int);
            using eGetOldGold1719_clbk = long double (WINAPIV*)(int, eGetOldGold1719_ptr);
            using eGetOldMineOre1721_ptr = long double (WINAPIV*)(int, char);
            using eGetOldMineOre1721_clbk = long double (WINAPIV*)(int, char, eGetOldMineOre1721_ptr);
            using eGetOreRate1723_ptr = float (WINAPIV*)(int);
            using eGetOreRate1723_clbk = float (WINAPIV*)(int, eGetOreRate1723_ptr);
            using eGetRate1725_ptr = int (WINAPIV*)(int);
            using eGetRate1725_clbk = int (WINAPIV*)(int, eGetRate1725_ptr);
            using eGetTex1727_ptr = float (WINAPIV*)(int);
            using eGetTex1727_clbk = float (WINAPIV*)(int, eGetTex1727_ptr);
            using eGetTexRate1729_ptr = unsigned int (WINAPIV*)(int);
            using eGetTexRate1729_clbk = unsigned int (WINAPIV*)(int, eGetTexRate1729_ptr);
            using eInitEconomySystem1731_ptr = bool (WINAPIV*)(int, int, struct _economy_history_data*, int, struct _economy_history_data*);
            using eInitEconomySystem1731_clbk = bool (WINAPIV*)(int, int, struct _economy_history_data*, int, struct _economy_history_data*, eInitEconomySystem1731_ptr);
            using eUpdateEconomySystem1733_ptr = void (WINAPIV*)(bool*);
            using eUpdateEconomySystem1733_clbk = void (WINAPIV*)(bool*, eUpdateEconomySystem1733_ptr);
            using jc_Contents1746_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using jc_Contents1746_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, jc_Contents1746_ptr);
            using jc_Count1748_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using jc_Count1748_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, jc_Count1748_ptr);
            using jc_Description1750_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using jc_Description1750_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, jc_Description1750_ptr);
            using jc_ReactContents1752_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using jc_ReactContents1752_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, jc_ReactContents1752_ptr);
            using jc_ReactType1754_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using jc_ReactType1754_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, jc_ReactType1754_ptr);
            using jc_Type1756_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using jc_Type1756_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, jc_Type1756_ptr);
            using lua_my_open1759_ptr = lua_State* (WINAPIV*)();
            using lua_my_open1759_clbk = lua_State* (WINAPIV*)(lua_my_open1759_ptr);
            using mc_AddMonster1761_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_AddMonster1761_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_AddMonster1761_ptr);
            using mc_AddTime1763_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_AddTime1763_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_AddTime1763_ptr);
            using mc_Area1765_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_Area1765_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_Area1765_ptr);
            using mc_ChangeMonster1767_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_ChangeMonster1767_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_ChangeMonster1767_ptr);
            using mc_CompleteMsg1769_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_CompleteMsg1769_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_CompleteMsg1769_ptr);
            using mc_Description1771_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_Description1771_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_Description1771_ptr);
            using mc_GatePos1773_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_GatePos1773_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_GatePos1773_ptr);
            using mc_If1775_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_If1775_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_If1775_ptr);
            using mc_Inner1777_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_Inner1777_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_Inner1777_ptr);
            using mc_JobOrder1779_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_JobOrder1779_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_JobOrder1779_ptr);
            using mc_LimTimeSec1781_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_LimTimeSec1781_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_LimTimeSec1781_ptr);
            using mc_LootItem1783_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_LootItem1783_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_LootItem1783_ptr);
            using mc_RespawnMonster1785_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_RespawnMonster1785_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_RespawnMonster1785_ptr);
            using mc_RespawnMonsterOption1787_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_RespawnMonsterOption1787_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_RespawnMonsterOption1787_ptr);
            using mc_ResultContents1789_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_ResultContents1789_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_ResultContents1789_ptr);
            using mc_ResultType1791_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_ResultType1791_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_ResultType1791_ptr);
            using mc_StartPos1793_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_StartPos1793_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_StartPos1793_ptr);
            using mc_respond1795_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using mc_respond1795_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, mc_respond1795_ptr);
            using qc_Dalant1819_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_Dalant1819_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_Dalant1819_ptr);
            using qc_Description1821_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_Description1821_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_Description1821_ptr);
            using qc_DummyBlock1823_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_DummyBlock1823_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_DummyBlock1823_ptr);
            using qc_LimitLvMax1825_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_LimitLvMax1825_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_LimitLvMax1825_ptr);
            using qc_LimitLvMin1827_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_LimitLvMin1827_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_LimitLvMin1827_ptr);
            using qc_MemberNum1829_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_MemberNum1829_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_MemberNum1829_ptr);
            using qc_MembershipParty1831_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_MembershipParty1831_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_MembershipParty1831_ptr);
            using qc_RewardExp1833_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_RewardExp1833_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_RewardExp1833_ptr);
            using qc_RewardItem1835_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_RewardItem1835_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_RewardItem1835_ptr);
            using qc_RewardPvp1837_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_RewardPvp1837_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_RewardPvp1837_ptr);
            using qc_StartMission1839_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_StartMission1839_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_StartMission1839_ptr);
            using qc_UseMap1841_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_UseMap1841_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_UseMap1841_ptr);
            using qc_monsterGroup1843_ptr = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
            using qc_monsterGroup1843_clbk = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*, qc_monsterGroup1843_ptr);
            using wa_EnterWorld1916_ptr = void (WINAPIV*)(struct _WA_AVATOR_CODE*, uint16_t);
            using wa_EnterWorld1916_clbk = void (WINAPIV*)(struct _WA_AVATOR_CODE*, uint16_t, wa_EnterWorld1916_ptr);
            using wa_ExitWorld1918_ptr = void (WINAPIV*)(struct _CLID*);
            using wa_ExitWorld1918_clbk = void (WINAPIV*)(struct _CLID*, wa_ExitWorld1918_ptr);
            using wa_PartyDisjoint1920_ptr = void (WINAPIV*)(struct _CLID*);
            using wa_PartyDisjoint1920_clbk = void (WINAPIV*)(struct _CLID*, wa_PartyDisjoint1920_ptr);
            using wa_PartyForceLeave1922_ptr = void (WINAPIV*)(struct _CLID*, struct _CLID*);
            using wa_PartyForceLeave1922_clbk = void (WINAPIV*)(struct _CLID*, struct _CLID*, wa_PartyForceLeave1922_ptr);
            using wa_PartyJoin1924_ptr = void (WINAPIV*)(struct _CLID*, struct _CLID*);
            using wa_PartyJoin1924_clbk = void (WINAPIV*)(struct _CLID*, struct _CLID*, wa_PartyJoin1924_ptr);
            using wa_PartyLock1926_ptr = void (WINAPIV*)(struct _CLID*, bool);
            using wa_PartyLock1926_clbk = void (WINAPIV*)(struct _CLID*, bool, wa_PartyLock1926_ptr);
            using wa_PartyLootShareSystem1928_ptr = void (WINAPIV*)(struct _CLID*, char);
            using wa_PartyLootShareSystem1928_clbk = void (WINAPIV*)(struct _CLID*, char, wa_PartyLootShareSystem1928_ptr);
            using wa_PartySelfLeave1930_ptr = void (WINAPIV*)(struct _CLID*);
            using wa_PartySelfLeave1930_clbk = void (WINAPIV*)(struct _CLID*, wa_PartySelfLeave1930_ptr);
            using wa_PartySuccession1932_ptr = void (WINAPIV*)(struct _CLID*, struct _CLID*);
            using wa_PartySuccession1932_clbk = void (WINAPIV*)(struct _CLID*, struct _CLID*, wa_PartySuccession1932_ptr);
        }; // end namespace Info
    }; // end namespace Global
END_ATF_NAMESPACE

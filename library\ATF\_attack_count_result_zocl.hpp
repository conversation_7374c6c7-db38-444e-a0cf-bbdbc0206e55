// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_attack_gen_result_zocl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _attack_count_result_zocl
    {
        unsigned int dwAtterSerial;
        char by<PERSON>ttackPart;
        bool bCritical;
        bool bWPActive;
        char by<PERSON>ist<PERSON><PERSON>;
        _attack_gen_result_zocl::_dam_list DamList[32];
    public:
        _attack_count_result_zocl();
        void ctor__attack_count_result_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_attack_count_result_zocl, 328>(), "_attack_count_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _nuclear_bomb_destruction_zocl
    {
        char byRaceCode;
        char byUseClass;
    public:
        _nuclear_bomb_destruction_zocl();
        void ctor__nuclear_bomb_destruction_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_nuclear_bomb_destruction_zocl, 2>(), "_nuclear_bomb_destruction_zocl");
END_ATF_NAMESPACE

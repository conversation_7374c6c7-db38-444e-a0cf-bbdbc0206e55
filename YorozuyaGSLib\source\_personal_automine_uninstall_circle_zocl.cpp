#include <_personal_automine_uninstall_circle_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_uninstall_circle_zocl::_personal_automine_uninstall_circle_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_uninstall_circle_zocl*);
        (org_ptr(0x1402ddfe0L))(this);
    };
    void _personal_automine_uninstall_circle_zocl::ctor__personal_automine_uninstall_circle_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_uninstall_circle_zocl*);
        (org_ptr(0x1402ddfe0L))(this);
    };
    int _personal_automine_uninstall_circle_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_uninstall_circle_zocl*);
        return (org_ptr(0x1402de030L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBilling.hpp>
#include <CUserDB.hpp>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    struct  CBillingID : CBilling
    {
    public:
        void Alive(struct CUserDB* pUserDB);
        void BillingClose(char* szID);
        CBillingID();
        void ctor_CBillingID();
        void Login(struct CUserDB* pUserDB);
        void Logout(struct CUserDB* pUserDB);
        bool SendMsg_Login(char* szID, char* szIP, char* szCMS, int16_t iType, struct _SYSTEMTIME* pstEndDate, int lRemainTime);
        ~CBillingID();
        void dtor_CBillingID();
    };
END_ATF_NAMESPACE

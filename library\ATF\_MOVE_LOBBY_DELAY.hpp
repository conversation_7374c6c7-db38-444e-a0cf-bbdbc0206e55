// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DELAY_PROCESS.hpp>


START_ATF_NAMESPACE
    struct  _MOVE_LOBBY_DELAY : _DELAY_PROCESS
    {
    public:
        void Process(unsigned int dwIndex, unsigned int dwSerial);
        _MOVE_LOBBY_DELAY();
        void ctor__MOVE_LOBBY_DELAY();
        ~_MOVE_LOBBY_DELAY();
        void dtor__MOVE_LOBBY_DELAY();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CComObjectRootBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComObjectRootEx<CComSingleThreadModel> : CComObjectRootBase
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComAutoDeleteCriticalSection.hpp>
#include <ATL__CComObjectRootBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComObjectRootEx<CComMultiThreadModel> : CComObjectRootBase
        {
            CComAutoDeleteCriticalSection m_critsec;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

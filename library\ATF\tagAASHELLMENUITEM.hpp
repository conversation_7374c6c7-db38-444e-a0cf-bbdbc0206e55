// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagAAMENUFILENAME.hpp>



START_ATF_NAMESPACE
    struct tagAASHELLMENUITEM
    {
        void *lpReserved1;
        int iReserved;
        unsigned int uiReserved;
        tagAAMENUFILENAME *lpName;
        wchar_t *psz;
    };
END_ATF_NAMESPACE

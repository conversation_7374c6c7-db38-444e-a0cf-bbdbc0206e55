// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _event_info
    {
        bool bEnable;
        unsigned int dwStartDate;
        unsigned int dwEndDate;
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_event_info, 12>(), "_event_info");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _cashdb_setting_result_acwr
    {
        char szIP[16];
        char szDSN[32];
        char szD<PERSON><PERSON><PERSON>[32];
        char szAccount[32];
        char szPassword[32];
        unsigned int dwPort;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct IAtlAutoThreadModuleVtbl
        {
            HRESULT (WINAPIV *CreateInstance)(IAtlAutoThreadModule *_this, void *, _GUID *, void **);
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

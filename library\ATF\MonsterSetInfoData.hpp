// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_mon_block.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct MonsterSetInfoData
    {
        char m_byLevel_ContSFTime[4][7];
        int m_nMonsterLostTargetDistance;
        float m_fMonsterForcePowerRate;
        char (*m_strRotMonBlk_Ar)[64];
        int m_nMonBlkCount;
        float m_fToleranceProbMax[7];
        int m_bLoad;
        int m_iMonsterLootRateSame;
        int m_iMonsterLootingRateUp[11];
        int m_iMonsterLootingRateDown[11];
    public:
        char GetLevelContSFTime(char byEffectCode, char byLevel);
        int GetLostMonsterTargetDistance();
        float GetMaxToleranceProbMax(int nMonGrade);
        unsigned int GetMonsterDropRate(int iDiffLevel);
        float GetMonsterForcePowerRate();
        void Init();
        bool IsRotateBlock(struct _mon_block* pBlock);
        int Load(char* strFileName);
        MonsterSetInfoData();
        void ctor_MonsterSetInfoData();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<MonsterSetInfoData, 176>(), "MonsterSetInfoData");
END_ATF_NAMESPACE

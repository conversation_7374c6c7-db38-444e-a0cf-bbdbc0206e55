// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum CS_RCODE
    {
      CsSucceed = 0x0,
      CsWrongQuerySentence = 0x1,
      CsWrongUserID = 0x2,
      CsWrongGameCode = 0x3,
      CsWrongItemStringCode = 0x4,
      CsWrongPrice = 0x5,
      CsWrongOverlapNum = 0x6,
      CsWrongDiscount = 0x7,
      CsWrongUID = 0x8,
      CsWrongServerName = 0x9,
      CsWrongAvatorName = 0xA,
      CsWrongStoreIndex = 0xB,
      CsUnfitForUse = 0xC,
      CsNotEnoughCashAmount = 0xD,
      CsRollbackFailed = 0xE,
      CsItemInsertToInvenFailed = 0xF,
      CsEmptySlotLack = 0x10,
      CsNonmatchCashAmountLocalAndDb = 0x11,
      CsWrongEvent = 0x12,
      CsNotUseCouponType = 0x13,
      CsNoUseCouponWithEvent = 0x14,
      CsNotEnoughLimitedSaleNum = 0x15,
      CsNotEventTime = 0x16,
    };
END_ATF_NAMESPACE

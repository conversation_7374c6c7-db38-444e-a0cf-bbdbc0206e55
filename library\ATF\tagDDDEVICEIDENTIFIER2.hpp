// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagDDDEVICEIDENTIFIER2
    {
        char szDriver[512];
        char szDescription[512];
        _LARGE_INTEGER liDriverVersion;
        unsigned int dwVendorId;
        unsigned int dwDeviceId;
        unsigned int dwSubSysId;
        unsigned int dwRevision;
        _GUID guidDeviceIdentifier;
        unsigned int dwWHQLLevel;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

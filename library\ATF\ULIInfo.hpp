// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ULI.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using UL<PERSON>ink1_ptr = void (WINAPIV*)(struct ULI*);
        using ULILink1_clbk = void (WINAPIV*)(struct ULI*, ULILink1_ptr);
        
        using ULIctor_ULI2_ptr = void (WINAPIV*)(struct ULI*, struct ImgDelayDescr*);
        using ULIctor_ULI2_clbk = void (WINAPIV*)(struct ULI*, struct ImgDelayDescr*, ULIctor_ULI2_ptr);
        using ULIUnlink3_ptr = void (WINAPIV*)(struct ULI*);
        using ULIUnlink3_clbk = void (WINAPIV*)(struct ULI*, ULIUnlink3_ptr);
        
        using ULIdtor_ULI7_ptr = void (WINAPIV*)(struct ULI*);
        using ULIdtor_ULI7_clbk = void (WINAPIV*)(struct ULI*, ULIdtor_ULI7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

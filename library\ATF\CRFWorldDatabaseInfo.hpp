// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRFWorldDatabase.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CRFWorldDatabaseAdd_PvpPoint2_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int);
        using CRFWorldDatabaseAdd_PvpPoint2_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int, CRFWorldDatabaseAdd_PvpPoint2_ptr);
        
        using CRFWorldDatabasector_CRFWorldDatabase4_ptr = void (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabasector_CRFWorldDatabase4_clbk = void (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabasector_CRFWorldDatabase4_ptr);
        using CRFWorldDatabaseCheck_GuildMemberCount6_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseCheck_GuildMemberCount6_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseCheck_GuildMemberCount6_ptr);
        using CRFWorldDatabaseCreateCharacterSelectLogTable8_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseCreateCharacterSelectLogTable8_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseCreateCharacterSelectLogTable8_ptr);
        using CRFWorldDatabaseCreateGuildBattleRankTable10_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseCreateGuildBattleRankTable10_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseCreateGuildBattleRankTable10_ptr);
        using CRFWorldDatabaseCreate_PvpPointGuildRankTable12_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseCreate_PvpPointGuildRankTable12_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseCreate_PvpPointGuildRankTable12_ptr);
        using CRFWorldDatabaseDeleteGuildBattleInfo14_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseDeleteGuildBattleInfo14_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseDeleteGuildBattleInfo14_ptr);
        using CRFWorldDatabaseDeleteGuildBattleScheduleInfo16_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseDeleteGuildBattleScheduleInfo16_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseDeleteGuildBattleScheduleInfo16_ptr);
        using CRFWorldDatabaseDelete_CharacterData18_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseDelete_CharacterData18_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseDelete_CharacterData18_ptr);
        using CRFWorldDatabaseDelete_Guild20_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseDelete_Guild20_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseDelete_Guild20_ptr);
        using CRFWorldDatabaseDelete_ItemCharge22_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseDelete_ItemCharge22_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseDelete_ItemCharge22_ptr);
        using CRFWorldDatabaseDelete_PatriarchComm24_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*);
        using CRFWorldDatabaseDelete_PatriarchComm24_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, CRFWorldDatabaseDelete_PatriarchComm24_ptr);
        using CRFWorldDatabaseDelete_TrunkItemCharge26_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseDelete_TrunkItemCharge26_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseDelete_TrunkItemCharge26_ptr);
        using CRFWorldDatabaseDelete_TrunkItemCharge_Extend28_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseDelete_TrunkItemCharge_Extend28_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseDelete_TrunkItemCharge_Extend28_ptr);
        using CRFWorldDatabaseInsertChangeClassLogAfterInitClass30_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, char*, char*, int, char, uint16_t, char, char, char, char, char);
        using CRFWorldDatabaseInsertChangeClassLogAfterInitClass30_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, char*, char*, int, char, uint16_t, char, char, char, char, char, CRFWorldDatabaseInsertChangeClassLogAfterInitClass30_ptr);
        using CRFWorldDatabaseInsertCharacterSelectLog32_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int, char*, uint16_t, char, char, char, char, char);
        using CRFWorldDatabaseInsertCharacterSelectLog32_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int, char*, uint16_t, char, char, char, char, char, CRFWorldDatabaseInsertCharacterSelectLog32_ptr);
        using CRFWorldDatabaseInsertGuildBattleDefaultRecord34_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsertGuildBattleDefaultRecord34_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsertGuildBattleDefaultRecord34_ptr);
        using CRFWorldDatabaseInsertGuildBattleRankRecord36_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsertGuildBattleRankRecord36_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsertGuildBattleRankRecord36_ptr);
        using CRFWorldDatabaseInsertGuildBattleScheduleDefaultRecord38_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, char);
        using CRFWorldDatabaseInsertGuildBattleScheduleDefaultRecord38_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, char, CRFWorldDatabaseInsertGuildBattleScheduleDefaultRecord38_ptr);
        using CRFWorldDatabaseInsert_AccountTrunk40_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_AccountTrunk40_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_AccountTrunk40_ptr);
        using CRFWorldDatabaseInsert_AccountTrunkExtend42_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_AccountTrunkExtend42_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_AccountTrunkExtend42_ptr);
        using CRFWorldDatabaseInsert_AnimusData44_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*);
        using CRFWorldDatabaseInsert_AnimusData44_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*, CRFWorldDatabaseInsert_AnimusData44_ptr);
        using CRFWorldDatabaseInsert_AnimusLog46_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, long double, long double);
        using CRFWorldDatabaseInsert_AnimusLog46_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, long double, long double, CRFWorldDatabaseInsert_AnimusLog46_ptr);
        using CRFWorldDatabaseInsert_BossCryRecord48_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_BossCryRecord48_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_BossCryRecord48_ptr);
        using CRFWorldDatabaseInsert_Buddy50_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_Buddy50_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_Buddy50_ptr);
        using CRFWorldDatabaseInsert_CashLimSale52_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseInsert_CashLimSale52_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseInsert_CashLimSale52_ptr);
        using CRFWorldDatabaseInsert_CharacterData54_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, unsigned int, char*, char, char, unsigned int, int, unsigned int*);
        using CRFWorldDatabaseInsert_CharacterData54_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, unsigned int, char*, char, char, unsigned int, int, unsigned int*, CRFWorldDatabaseInsert_CharacterData54_ptr);
        using CRFWorldDatabaseInsert_DefaultWeeklyGuildPvpPointSumRecord56_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseInsert_DefaultWeeklyGuildPvpPointSumRecord56_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseInsert_DefaultWeeklyGuildPvpPointSumRecord56_ptr);
        using CRFWorldDatabaseInsert_Economy_History58_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_economy_history_info_array::_worlddb_economy_history_info*);
        using CRFWorldDatabaseInsert_Economy_History58_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_economy_history_info_array::_worlddb_economy_history_info*, CRFWorldDatabaseInsert_Economy_History58_ptr);
        using CRFWorldDatabaseInsert_GoldenBoxItem60_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseInsert_GoldenBoxItem60_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseInsert_GoldenBoxItem60_ptr);
        using CRFWorldDatabaseInsert_GreetingRecord62_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, int, char*, char*);
        using CRFWorldDatabaseInsert_GreetingRecord62_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, int, char*, char*, CRFWorldDatabaseInsert_GreetingRecord62_ptr);
        using CRFWorldDatabaseInsert_GuidRoom64_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, char);
        using CRFWorldDatabaseInsert_GuidRoom64_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, char, CRFWorldDatabaseInsert_GuidRoom64_ptr);
        using CRFWorldDatabaseInsert_Guild66_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char);
        using CRFWorldDatabaseInsert_Guild66_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char, CRFWorldDatabaseInsert_Guild66_ptr);
        using CRFWorldDatabaseInsert_GuildBatlleResultLog68_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, unsigned int, char*, unsigned int, char*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, char, unsigned int, char*, unsigned int, char*, char, unsigned int, char*);
        using CRFWorldDatabaseInsert_GuildBatlleResultLog68_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, unsigned int, char*, unsigned int, char*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, char, unsigned int, char*, unsigned int, char*, char, unsigned int, char*, CRFWorldDatabaseInsert_GuildBatlleResultLog68_ptr);
        using CRFWorldDatabaseInsert_GuildBatlleResultLogBattelInfo70_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, unsigned int, char*, unsigned int, char*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, char, unsigned int, char*, unsigned int, char*, char, unsigned int, char*);
        using CRFWorldDatabaseInsert_GuildBatlleResultLogBattelInfo70_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, unsigned int, char*, unsigned int, char*, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, char, unsigned int, char*, unsigned int, char*, char, unsigned int, char*, CRFWorldDatabaseInsert_GuildBatlleResultLogBattelInfo70_ptr);
        using CRFWorldDatabaseInsert_GuildMoneyHistory72_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, long double, long double, long double, char*, unsigned int, char*);
        using CRFWorldDatabaseInsert_GuildMoneyHistory72_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, long double, long double, long double, char*, unsigned int, char*, CRFWorldDatabaseInsert_GuildMoneyHistory72_ptr);
        using CRFWorldDatabaseInsert_ItemChargeInGame74_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, uint64_t, unsigned int, char);
        using CRFWorldDatabaseInsert_ItemChargeInGame74_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, uint64_t, unsigned int, char, CRFWorldDatabaseInsert_ItemChargeInGame74_ptr);
        using CRFWorldDatabaseInsert_ItemCombineEx76_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_ItemCombineEx76_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_ItemCombineEx76_ptr);
        using CRFWorldDatabaseInsert_Level_Log78_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, unsigned int);
        using CRFWorldDatabaseInsert_Level_Log78_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, unsigned int, CRFWorldDatabaseInsert_Level_Log78_ptr);
        using CRFWorldDatabaseInsert_LimitItemRecord80_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*);
        using CRFWorldDatabaseInsert_LimitItemRecord80_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, CRFWorldDatabaseInsert_LimitItemRecord80_ptr);
        using CRFWorldDatabaseInsert_MacroData82_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_MacroData82_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_MacroData82_ptr);
        using CRFWorldDatabaseInsert_NpcData84_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_NpcData84_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_NpcData84_ptr);
        using CRFWorldDatabaseInsert_NpcData86_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*);
        using CRFWorldDatabaseInsert_NpcData86_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, CRFWorldDatabaseInsert_NpcData86_ptr);
        using CRFWorldDatabaseInsert_NpcLog88_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, unsigned int, unsigned int);
        using CRFWorldDatabaseInsert_NpcLog88_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, unsigned int, unsigned int, CRFWorldDatabaseInsert_NpcLog88_ptr);
        using CRFWorldDatabaseInsert_NpcQuest_History90_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_NpcQuest_History90_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_NpcQuest_History90_ptr);
        using CRFWorldDatabaseInsert_OreCutting92_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_OreCutting92_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_OreCutting92_ptr);
        using CRFWorldDatabaseInsert_OreReset_Log94_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, int, unsigned int, unsigned int);
        using CRFWorldDatabaseInsert_OreReset_Log94_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, int, unsigned int, unsigned int, CRFWorldDatabaseInsert_OreReset_Log94_ptr);
        using CRFWorldDatabaseInsert_PSDefaultRecord96_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_PSDefaultRecord96_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_PSDefaultRecord96_ptr);
        using CRFWorldDatabaseInsert_PatriarchComm98_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char*);
        using CRFWorldDatabaseInsert_PatriarchComm98_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char*, CRFWorldDatabaseInsert_PatriarchComm98_ptr);
        using CRFWorldDatabaseInsert_PatrirchItemChargeRefund100_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseInsert_PatrirchItemChargeRefund100_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseInsert_PatrirchItemChargeRefund100_ptr);
        using CRFWorldDatabaseInsert_PcBangFavorItem102_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_PcBangFavorItem102_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_PcBangFavorItem102_ptr);
        using CRFWorldDatabaseInsert_PlayerTimeLimitInfo104_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_PlayerTimeLimitInfo104_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_PlayerTimeLimitInfo104_ptr);
        using CRFWorldDatabaseInsert_PostStorageRecord106_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseInsert_PostStorageRecord106_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseInsert_PostStorageRecord106_ptr);
        using CRFWorldDatabaseInsert_PotionDelay108_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_PotionDelay108_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_PotionDelay108_ptr);
        using CRFWorldDatabaseInsert_PrimiumPlayTime110_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_PrimiumPlayTime110_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_PrimiumPlayTime110_ptr);
        using CRFWorldDatabaseInsert_PvpOrderViewInfo112_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_PvpOrderViewInfo112_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_PvpOrderViewInfo112_ptr);
        using CRFWorldDatabaseInsert_PvpPointGuildRankData114_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseInsert_PvpPointGuildRankData114_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseInsert_PvpPointGuildRankData114_ptr);
        using CRFWorldDatabaseInsert_PvpPointLimitInfoRecord116_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_PvpPointLimitInfoRecord116_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_PvpPointLimitInfoRecord116_ptr);
        using CRFWorldDatabaseInsert_Quest118_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_Quest118_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_Quest118_ptr);
        using CRFWorldDatabaseInsert_RFEvent_ClassRefine120_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_RFEvent_ClassRefine120_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_RFEvent_ClassRefine120_ptr);
        using CRFWorldDatabaseInsert_RaceBattleLog122_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _race_battle_log_info*);
        using CRFWorldDatabaseInsert_RaceBattleLog122_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _race_battle_log_info*, CRFWorldDatabaseInsert_RaceBattleLog122_ptr);
        using CRFWorldDatabaseInsert_RenamePotionLog124_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char*);
        using CRFWorldDatabaseInsert_RenamePotionLog124_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char*, CRFWorldDatabaseInsert_RenamePotionLog124_ptr);
        using CRFWorldDatabaseInsert_SFDelayInfo126_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_sf_delay_info*);
        using CRFWorldDatabaseInsert_SFDelayInfo126_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_sf_delay_info*, CRFWorldDatabaseInsert_SFDelayInfo126_ptr);
        using CRFWorldDatabaseInsert_Set_Limit_Run128_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, int);
        using CRFWorldDatabaseInsert_Set_Limit_Run128_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, int, CRFWorldDatabaseInsert_Set_Limit_Run128_ptr);
        using CRFWorldDatabaseInsert_SettlementOwnerLog130_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, char*, uint16_t, char, long double, long double, unsigned int);
        using CRFWorldDatabaseInsert_SettlementOwnerLog130_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, char*, uint16_t, char, long double, long double, unsigned int, CRFWorldDatabaseInsert_SettlementOwnerLog130_ptr);
        using CRFWorldDatabaseInsert_Start_NpcQuest_History132_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, char*, int64_t);
        using CRFWorldDatabaseInsert_Start_NpcQuest_History132_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, char*, int64_t, CRFWorldDatabaseInsert_Start_NpcQuest_History132_ptr);
        using CRFWorldDatabaseInsert_Supplement134_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_Supplement134_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_Supplement134_ptr);
        using CRFWorldDatabaseInsert_Unit136_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_Unit136_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_Unit136_ptr);
        using CRFWorldDatabaseInsert_UnitData138_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*);
        using CRFWorldDatabaseInsert_UnitData138_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*, CRFWorldDatabaseInsert_UnitData138_ptr);
        using CRFWorldDatabaseInsert_UnitLog140_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, long double, long double);
        using CRFWorldDatabaseInsert_UnitLog140_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, long double, long double, CRFWorldDatabaseInsert_UnitLog140_ptr);
        using CRFWorldDatabaseInsert_UnmannedTraderItemStateRecord142_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, wchar_t**);
        using CRFWorldDatabaseInsert_UnmannedTraderItemStateRecord142_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, wchar_t**, CRFWorldDatabaseInsert_UnmannedTraderItemStateRecord142_ptr);
        using CRFWorldDatabaseInsert_UnmannedTraderSingleDefaultRecord144_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_UnmannedTraderSingleDefaultRecord144_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_UnmannedTraderSingleDefaultRecord144_ptr);
        using CRFWorldDatabaseInsert_UserInterface146_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_UserInterface146_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_UserInterface146_ptr);
        using CRFWorldDatabaseInsert_UserNum_Log148_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, int, int);
        using CRFWorldDatabaseInsert_UserNum_Log148_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, int, int, CRFWorldDatabaseInsert_UserNum_Log148_ptr);
        using CRFWorldDatabaseInsert_WeeklyGuildPvpPointSum150_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseInsert_WeeklyGuildPvpPointSum150_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseInsert_WeeklyGuildPvpPointSum150_ptr);
        using CRFWorldDatabaseLoadGreetingMsg152_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, char*, char*, char*, char*, char*, char*);
        using CRFWorldDatabaseLoadGreetingMsg152_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, char*, char*, char*, char*, char*, char*, CRFWorldDatabaseLoadGreetingMsg152_ptr);
        using CRFWorldDatabaseLoadGuildBattleInfo154_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, struct _worlddb_guild_battle_info*);
        using CRFWorldDatabaseLoadGuildBattleInfo154_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, struct _worlddb_guild_battle_info*, CRFWorldDatabaseLoadGuildBattleInfo154_ptr);
        using CRFWorldDatabaseLoadGuildBattleScheduleInfo156_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, struct _worlddb_guild_battle_schedule_list*);
        using CRFWorldDatabaseLoadGuildBattleScheduleInfo156_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, struct _worlddb_guild_battle_schedule_list*, CRFWorldDatabaseLoadGuildBattleScheduleInfo156_ptr);
        using CRFWorldDatabaseRebirth_Base158_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*);
        using CRFWorldDatabaseRebirth_Base158_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, CRFWorldDatabaseRebirth_Base158_ptr);
        using CRFWorldDatabaseRegist_UnmannedTraderSingleItem160_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _unmannedtrader_registsingleitem*, bool);
        using CRFWorldDatabaseRegist_UnmannedTraderSingleItem160_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _unmannedtrader_registsingleitem*, bool, CRFWorldDatabaseRegist_UnmannedTraderSingleItem160_ptr);
        using CRFWorldDatabaseSelectAllGuildSerial162_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, unsigned int*);
        using CRFWorldDatabaseSelectAllGuildSerial162_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, unsigned int*, CRFWorldDatabaseSelectAllGuildSerial162_ptr);
        using CRFWorldDatabaseSelectAllGuildSerialGrade164_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, unsigned int*, char*);
        using CRFWorldDatabaseSelectAllGuildSerialGrade164_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, unsigned int*, char*, CRFWorldDatabaseSelectAllGuildSerialGrade164_ptr);
        using CRFWorldDatabaseSelectGuildBattleRankList166_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, struct _worlddb_guild_battle_rank_list*);
        using CRFWorldDatabaseSelectGuildBattleRankList166_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, struct _worlddb_guild_battle_rank_list*, CRFWorldDatabaseSelectGuildBattleRankList166_ptr);
        using CRFWorldDatabaseSelectGuildBattleRankRecord168_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseSelectGuildBattleRankRecord168_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseSelectGuildBattleRankRecord168_ptr);
        using CRFWorldDatabaseSelectGuildBattleRerservedList170_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, struct _worlddb_guild_battle_reserved_schedule_info*);
        using CRFWorldDatabaseSelectGuildBattleRerservedList170_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, struct _worlddb_guild_battle_reserved_schedule_info*, CRFWorldDatabaseSelectGuildBattleRerservedList170_ptr);
        using CRFWorldDatabaseSelectGuildBattleScheduleInfoID172_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseSelectGuildBattleScheduleInfoID172_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseSelectGuildBattleScheduleInfoID172_ptr);
        using CRFWorldDatabaseSelectRowCountGuildBattleInfo174_ptr = int (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseSelectRowCountGuildBattleInfo174_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseSelectRowCountGuildBattleInfo174_ptr);
        using CRFWorldDatabaseSelectRowCountGuildBattleScheduleInfo176_ptr = int (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseSelectRowCountGuildBattleScheduleInfo176_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseSelectRowCountGuildBattleScheduleInfo176_ptr);
        using CRFWorldDatabaseSelect_AccountByAvatorName178_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*);
        using CRFWorldDatabaseSelect_AccountByAvatorName178_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, CRFWorldDatabaseSelect_AccountByAvatorName178_ptr);
        using CRFWorldDatabaseSelect_AccountItemCharge180_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, long double*, unsigned int*, uint64_t*, unsigned int*, char*, unsigned int*, int*);
        using CRFWorldDatabaseSelect_AccountItemCharge180_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, long double*, unsigned int*, uint64_t*, unsigned int*, char*, unsigned int*, int*, CRFWorldDatabaseSelect_AccountItemCharge180_ptr);
        using CRFWorldDatabaseSelect_AccountItemCharge_Extend182_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*, uint64_t*, unsigned int*, char*, unsigned int*, int*);
        using CRFWorldDatabaseSelect_AccountItemCharge_Extend182_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*, uint64_t*, unsigned int*, char*, unsigned int*, int*, CRFWorldDatabaseSelect_AccountItemCharge_Extend182_ptr);
        using CRFWorldDatabaseSelect_AccountSerial184_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, unsigned int*);
        using CRFWorldDatabaseSelect_AccountSerial184_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, unsigned int*, CRFWorldDatabaseSelect_AccountSerial184_ptr);
        using CRFWorldDatabaseSelect_AccountTrunk186_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, struct _worlddb_trunk_info*);
        using CRFWorldDatabaseSelect_AccountTrunk186_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, struct _worlddb_trunk_info*, CRFWorldDatabaseSelect_AccountTrunk186_ptr);
        using CRFWorldDatabaseSelect_AccountTrunkExtend188_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_trunk_info*);
        using CRFWorldDatabaseSelect_AccountTrunkExtend188_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_trunk_info*, CRFWorldDatabaseSelect_AccountTrunkExtend188_ptr);
        using CRFWorldDatabaseSelect_AllGuildData190_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _worlddb_guild_info*);
        using CRFWorldDatabaseSelect_AllGuildData190_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _worlddb_guild_info*, CRFWorldDatabaseSelect_AllGuildData190_ptr);
        using CRFWorldDatabaseSelect_AllGuildNum192_ptr = uint16_t (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseSelect_AllGuildNum192_clbk = uint16_t (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseSelect_AllGuildNum192_ptr);
        using CRFWorldDatabaseSelect_AnimusData194_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, long double*);
        using CRFWorldDatabaseSelect_AnimusData194_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, long double*, CRFWorldDatabaseSelect_AnimusData194_ptr);
        using CRFWorldDatabaseSelect_ArrangeInfo196_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseSelect_ArrangeInfo196_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseSelect_ArrangeInfo196_ptr);
        using CRFWorldDatabaseSelect_BattleResultLogLatest198_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*);
        using CRFWorldDatabaseSelect_BattleResultLogLatest198_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, CRFWorldDatabaseSelect_BattleResultLogLatest198_ptr);
        using CRFWorldDatabaseSelect_BattleTournamentInfo200_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, struct TournamentWinner*, int);
        using CRFWorldDatabaseSelect_BattleTournamentInfo200_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, struct TournamentWinner*, int, CRFWorldDatabaseSelect_BattleTournamentInfo200_ptr);
        using CRFWorldDatabaseSelect_BossCryMsg202_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_crymsg_info*);
        using CRFWorldDatabaseSelect_BossCryMsg202_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_crymsg_info*, CRFWorldDatabaseSelect_BossCryMsg202_ptr);
        using CRFWorldDatabaseSelect_Buddy204_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_buddy_info*);
        using CRFWorldDatabaseSelect_Buddy204_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_buddy_info*, CRFWorldDatabaseSelect_Buddy204_ptr);
        using CRFWorldDatabaseSelect_CashLimSale206_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, struct _worlddb_cash_limited_sale*);
        using CRFWorldDatabaseSelect_CashLimSale206_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, struct _worlddb_cash_limited_sale*, CRFWorldDatabaseSelect_CashLimSale206_ptr);
        using CRFWorldDatabaseSelect_CharNumInWorld208_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*);
        using CRFWorldDatabaseSelect_CharNumInWorld208_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, CRFWorldDatabaseSelect_CharNumInWorld208_ptr);
        using CRFWorldDatabaseSelect_CharacterBaseInfo210_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_base_info*);
        using CRFWorldDatabaseSelect_CharacterBaseInfo210_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_base_info*, CRFWorldDatabaseSelect_CharacterBaseInfo210_ptr);
        using CRFWorldDatabaseSelect_CharacterBaseInfoByName212_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char*, struct _worlddb_character_base_info*);
        using CRFWorldDatabaseSelect_CharacterBaseInfoByName212_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char*, struct _worlddb_character_base_info*, CRFWorldDatabaseSelect_CharacterBaseInfoByName212_ptr);
        using CRFWorldDatabaseSelect_CharacterBaseInfoBySerial214_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_base_info_array*);
        using CRFWorldDatabaseSelect_CharacterBaseInfoBySerial214_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_base_info_array*, CRFWorldDatabaseSelect_CharacterBaseInfoBySerial214_ptr);
        using CRFWorldDatabaseSelect_CharacterGeneralInfo216_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_general_info*);
        using CRFWorldDatabaseSelect_CharacterGeneralInfo216_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_general_info*, CRFWorldDatabaseSelect_CharacterGeneralInfo216_ptr);
        using CRFWorldDatabaseSelect_CharacterName218_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char*);
        using CRFWorldDatabaseSelect_CharacterName218_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char*, CRFWorldDatabaseSelect_CharacterName218_ptr);
        using CRFWorldDatabaseSelect_CharacterReName220_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*);
        using CRFWorldDatabaseSelect_CharacterReName220_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*, CRFWorldDatabaseSelect_CharacterReName220_ptr);
        using CRFWorldDatabaseSelect_CharacterSerial222_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*);
        using CRFWorldDatabaseSelect_CharacterSerial222_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*, CRFWorldDatabaseSelect_CharacterSerial222_ptr);
        using CRFWorldDatabaseSelect_CharactersInfo224_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_array_info*);
        using CRFWorldDatabaseSelect_CharactersInfo224_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_array_info*, CRFWorldDatabaseSelect_CharactersInfo224_ptr);
        using CRFWorldDatabaseSelect_CheckGreetRecord226_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, int);
        using CRFWorldDatabaseSelect_CheckGreetRecord226_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, int, CRFWorldDatabaseSelect_CheckGreetRecord226_ptr);
        using CRFWorldDatabaseSelect_CheckSumValue228_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_CheckSumValue228_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, CRFWorldDatabaseSelect_CheckSumValue228_ptr);
        using CRFWorldDatabaseSelect_ChracterSerialRace230_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*, char*);
        using CRFWorldDatabaseSelect_ChracterSerialRace230_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*, char*, CRFWorldDatabaseSelect_ChracterSerialRace230_ptr);
        using CRFWorldDatabaseSelect_ClearHonorGuild232_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int*);
        using CRFWorldDatabaseSelect_ClearHonorGuild232_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int*, CRFWorldDatabaseSelect_ClearHonorGuild232_ptr);
        using CRFWorldDatabaseSelect_Economy_History234_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, struct _worlddb_economy_history_info_array*, unsigned int);
        using CRFWorldDatabaseSelect_Economy_History234_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, struct _worlddb_economy_history_info_array*, unsigned int, CRFWorldDatabaseSelect_Economy_History234_ptr);
        using CRFWorldDatabaseSelect_Equal_DeleteName_NoArranged236_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseSelect_Equal_DeleteName_NoArranged236_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseSelect_Equal_DeleteName_NoArranged236_ptr);
        using CRFWorldDatabaseSelect_Equal_Name238_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseSelect_Equal_Name238_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseSelect_Equal_Name238_ptr);
        using CRFWorldDatabaseSelect_Exist_Economy240_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_economy_history_info_array::_worlddb_economy_history_info*);
        using CRFWorldDatabaseSelect_Exist_Economy240_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_economy_history_info_array::_worlddb_economy_history_info*, CRFWorldDatabaseSelect_Exist_Economy240_ptr);
        using CRFWorldDatabaseSelect_FailBattleCount242_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_FailBattleCount242_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*, CRFWorldDatabaseSelect_FailBattleCount242_ptr);
        using CRFWorldDatabaseSelect_GetCharSerialByNameRace244_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char*, char, unsigned int*);
        using CRFWorldDatabaseSelect_GetCharSerialByNameRace244_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char*, char, unsigned int*, CRFWorldDatabaseSelect_GetCharSerialByNameRace244_ptr);
        using CRFWorldDatabaseSelect_GodenBoxItem246_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, struct _worlddb_golden_box_item*, int*);
        using CRFWorldDatabaseSelect_GodenBoxItem246_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, struct _worlddb_golden_box_item*, int*, CRFWorldDatabaseSelect_GodenBoxItem246_ptr);
        using CRFWorldDatabaseSelect_GuildBattleRecord248_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _guildbattle_totalrecord*);
        using CRFWorldDatabaseSelect_GuildBattleRecord248_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _guildbattle_totalrecord*, CRFWorldDatabaseSelect_GuildBattleRecord248_ptr);
        using CRFWorldDatabaseSelect_GuildData250_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_guild_info::__guild_info*);
        using CRFWorldDatabaseSelect_GuildData250_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_guild_info::__guild_info*, CRFWorldDatabaseSelect_GuildData250_ptr);
        using CRFWorldDatabaseSelect_GuildMasterLastConn252_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_GuildMasterLastConn252_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int*, CRFWorldDatabaseSelect_GuildMasterLastConn252_ptr);
        using CRFWorldDatabaseSelect_GuildMemberData254_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, uint16_t, unsigned int, struct _worlddb_guild_member_info*);
        using CRFWorldDatabaseSelect_GuildMemberData254_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, uint16_t, unsigned int, struct _worlddb_guild_member_info*, CRFWorldDatabaseSelect_GuildMemberData254_ptr);
        using CRFWorldDatabaseSelect_GuildMoneyIOData256_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_guild_money_io_info*);
        using CRFWorldDatabaseSelect_GuildMoneyIOData256_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_guild_money_io_info*, CRFWorldDatabaseSelect_GuildMoneyIOData256_ptr);
        using CRFWorldDatabaseSelect_GuildRoomInfo258_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _guildroom_info*);
        using CRFWorldDatabaseSelect_GuildRoomInfo258_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _guildroom_info*, CRFWorldDatabaseSelect_GuildRoomInfo258_ptr);
        using CRFWorldDatabaseSelect_GuildSerial260_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*);
        using CRFWorldDatabaseSelect_GuildSerial260_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*, CRFWorldDatabaseSelect_GuildSerial260_ptr);
        using CRFWorldDatabaseSelect_HonorGuild262_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, struct _guild_honor_list_result_zocl*, bool);
        using CRFWorldDatabaseSelect_HonorGuild262_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, struct _guild_honor_list_result_zocl*, bool, CRFWorldDatabaseSelect_HonorGuild262_ptr);
        using CRFWorldDatabaseSelect_Inven264_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, uint16_t, struct _worlddb_inven_info*);
        using CRFWorldDatabaseSelect_Inven264_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, uint16_t, struct _worlddb_inven_info*, CRFWorldDatabaseSelect_Inven264_ptr);
        using CRFWorldDatabaseSelect_IsValidChar266_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_IsValidChar266_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, CRFWorldDatabaseSelect_IsValidChar266_ptr);
        using CRFWorldDatabaseSelect_ItemCharge268_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*, uint64_t*, unsigned int*, unsigned int*, int*);
        using CRFWorldDatabaseSelect_ItemCharge268_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*, uint64_t*, unsigned int*, unsigned int*, int*, CRFWorldDatabaseSelect_ItemCharge268_ptr);
        using CRFWorldDatabaseSelect_ItemCombineEx270_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_itemcombineex_info*);
        using CRFWorldDatabaseSelect_ItemCombineEx270_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_itemcombineex_info*, CRFWorldDatabaseSelect_ItemCombineEx270_ptr);
        using CRFWorldDatabaseSelect_LimitInfo272_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char*, uint64_t);
        using CRFWorldDatabaseSelect_LimitInfo272_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char*, uint64_t, CRFWorldDatabaseSelect_LimitInfo272_ptr);
        using CRFWorldDatabaseSelect_LimitItemEmptyRecord274_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*);
        using CRFWorldDatabaseSelect_LimitItemEmptyRecord274_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, CRFWorldDatabaseSelect_LimitItemEmptyRecord274_ptr);
        using CRFWorldDatabaseSelect_LimitItemUsedRecord276_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_LimitItemUsedRecord276_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int, unsigned int*, CRFWorldDatabaseSelect_LimitItemUsedRecord276_ptr);
        using CRFWorldDatabaseSelect_Limit_Run_Record278_ptr = char (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseSelect_Limit_Run_Record278_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseSelect_Limit_Run_Record278_ptr);
        using CRFWorldDatabaseSelect_LoseBattleCount280_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_LoseBattleCount280_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*, CRFWorldDatabaseSelect_LoseBattleCount280_ptr);
        using CRFWorldDatabaseSelect_MacroData282_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _AIOC_A_MACRODATA*);
        using CRFWorldDatabaseSelect_MacroData282_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _AIOC_A_MACRODATA*, CRFWorldDatabaseSelect_MacroData282_ptr);
        using CRFWorldDatabaseSelect_NotArrangeCharacter284_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_arrange_char_info*);
        using CRFWorldDatabaseSelect_NotArrangeCharacter284_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_arrange_char_info*, CRFWorldDatabaseSelect_NotArrangeCharacter284_ptr);
        using CRFWorldDatabaseSelect_NpcData286_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_NpcData286_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, CRFWorldDatabaseSelect_NpcData286_ptr);
        using CRFWorldDatabaseSelect_NpcQuest_History288_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_npc_quest_complete_history*);
        using CRFWorldDatabaseSelect_NpcQuest_History288_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_npc_quest_complete_history*, CRFWorldDatabaseSelect_NpcQuest_History288_ptr);
        using CRFWorldDatabaseSelect_OldVerPatriarchGroup290_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, struct _candidate_info*);
        using CRFWorldDatabaseSelect_OldVerPatriarchGroup290_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, struct _candidate_info*, CRFWorldDatabaseSelect_OldVerPatriarchGroup290_ptr);
        using CRFWorldDatabaseSelect_OreCutting292_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_ore_cutting*);
        using CRFWorldDatabaseSelect_OreCutting292_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_ore_cutting*, CRFWorldDatabaseSelect_OreCutting292_ptr);
        using CRFWorldDatabaseSelect_PatriarchCandidate294_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, struct _candidate_info*);
        using CRFWorldDatabaseSelect_PatriarchCandidate294_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, struct _candidate_info*, CRFWorldDatabaseSelect_PatriarchCandidate294_ptr);
        using CRFWorldDatabaseSelect_PatriarchComm296_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _patriarch_comm_list*);
        using CRFWorldDatabaseSelect_PatriarchComm296_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _patriarch_comm_list*, CRFWorldDatabaseSelect_PatriarchComm296_ptr);
        using CRFWorldDatabaseSelect_PatriarchCommCount298_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*);
        using CRFWorldDatabaseSelect_PatriarchCommCount298_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*, CRFWorldDatabaseSelect_PatriarchCommCount298_ptr);
        using CRFWorldDatabaseSelect_PatriarchElectState300_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, struct _sel_patriarch_elect_state*);
        using CRFWorldDatabaseSelect_PatriarchElectState300_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, struct _sel_patriarch_elect_state*, CRFWorldDatabaseSelect_PatriarchElectState300_ptr);
        using CRFWorldDatabaseSelect_PatriarchGroup302_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, struct _candidate_info*);
        using CRFWorldDatabaseSelect_PatriarchGroup302_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, struct _candidate_info*, CRFWorldDatabaseSelect_PatriarchGroup302_ptr);
        using CRFWorldDatabaseSelect_PatriarchRefundCount304_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_PatriarchRefundCount304_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*, CRFWorldDatabaseSelect_PatriarchRefundCount304_ptr);
        using CRFWorldDatabaseSelect_PatriarchVoted306_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, bool*);
        using CRFWorldDatabaseSelect_PatriarchVoted306_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, bool*, CRFWorldDatabaseSelect_PatriarchVoted306_ptr);
        using CRFWorldDatabaseSelect_PatriarchWinCnt308_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_PatriarchWinCnt308_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*, CRFWorldDatabaseSelect_PatriarchWinCnt308_ptr);
        using CRFWorldDatabaseSelect_PcBangFavorItem310_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_pcbang_favor_item*);
        using CRFWorldDatabaseSelect_PcBangFavorItem310_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_pcbang_favor_item*, CRFWorldDatabaseSelect_PcBangFavorItem310_ptr);
        using CRFWorldDatabaseSelect_PlayerTimeLimitInfo312_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_time_limit_info*);
        using CRFWorldDatabaseSelect_PlayerTimeLimitInfo312_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_time_limit_info*, CRFWorldDatabaseSelect_PlayerTimeLimitInfo312_ptr);
        using CRFWorldDatabaseSelect_PlayerTimeLimitInfo314_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, char*);
        using CRFWorldDatabaseSelect_PlayerTimeLimitInfo314_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, char*, CRFWorldDatabaseSelect_PlayerTimeLimitInfo314_ptr);
        using CRFWorldDatabaseSelect_Player_Last_LogoutTime316_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_Player_Last_LogoutTime316_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, CRFWorldDatabaseSelect_Player_Last_LogoutTime316_ptr);
        using CRFWorldDatabaseSelect_PostContent318_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, int);
        using CRFWorldDatabaseSelect_PostContent318_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, int, CRFWorldDatabaseSelect_PostContent318_ptr);
        using CRFWorldDatabaseSelect_PostRecvSerialFromName320_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*, unsigned int*, unsigned int*);
        using CRFWorldDatabaseSelect_PostRecvSerialFromName320_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int*, unsigned int*, unsigned int*, CRFWorldDatabaseSelect_PostRecvSerialFromName320_ptr);
        using CRFWorldDatabaseSelect_PostRecvStorageCheck322_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseSelect_PostRecvStorageCheck322_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseSelect_PostRecvStorageCheck322_ptr);
        using CRFWorldDatabaseSelect_PostRegistryData324_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct CPostData*);
        using CRFWorldDatabaseSelect_PostRegistryData324_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct CPostData*, CRFWorldDatabaseSelect_PostRegistryData324_ptr);
        using CRFWorldDatabaseSelect_PostStorageEmptyRecord326_ptr = int (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseSelect_PostStorageEmptyRecord326_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseSelect_PostStorageEmptyRecord326_ptr);
        using CRFWorldDatabaseSelect_PostStorageEmptyRecordSerial328_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*);
        using CRFWorldDatabaseSelect_PostStorageEmptyRecordSerial328_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, CRFWorldDatabaseSelect_PostStorageEmptyRecordSerial328_ptr);
        using CRFWorldDatabaseSelect_PostStorageList330_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _post_storage_list*);
        using CRFWorldDatabaseSelect_PostStorageList330_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _post_storage_list*, CRFWorldDatabaseSelect_PostStorageList330_ptr);
        using CRFWorldDatabaseSelect_PostStorageRecordCheck332_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseSelect_PostStorageRecordCheck332_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseSelect_PostStorageRecordCheck332_ptr);
        using CRFWorldDatabaseSelect_PotionDelay334_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_potion_delay_info*);
        using CRFWorldDatabaseSelect_PotionDelay334_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_potion_delay_info*, CRFWorldDatabaseSelect_PotionDelay334_ptr);
        using CRFWorldDatabaseSelect_PrimiumPlayTime336_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _PCBANG_PLAY_TIME*);
        using CRFWorldDatabaseSelect_PrimiumPlayTime336_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _PCBANG_PLAY_TIME*, CRFWorldDatabaseSelect_PrimiumPlayTime336_ptr);
        using CRFWorldDatabaseSelect_Punishment338_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, unsigned int*);
        using CRFWorldDatabaseSelect_Punishment338_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, unsigned int*, CRFWorldDatabaseSelect_Punishment338_ptr);
        using CRFWorldDatabaseSelect_PunishmentCount340_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_PunishmentCount340_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*, CRFWorldDatabaseSelect_PunishmentCount340_ptr);
        using CRFWorldDatabaseSelect_PvpOrderViewInfo342_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _pvporderview_info*);
        using CRFWorldDatabaseSelect_PvpOrderViewInfo342_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _pvporderview_info*, CRFWorldDatabaseSelect_PvpOrderViewInfo342_ptr);
        using CRFWorldDatabaseSelect_PvpPointGuildRank344_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char*, struct _pvppoint_guild_rank_info*);
        using CRFWorldDatabaseSelect_PvpPointGuildRank344_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char*, struct _pvppoint_guild_rank_info*, CRFWorldDatabaseSelect_PvpPointGuildRank344_ptr);
        using CRFWorldDatabaseSelect_PvpPointLimitInfo346_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _pvppointlimit_info*);
        using CRFWorldDatabaseSelect_PvpPointLimitInfo346_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _pvppointlimit_info*, CRFWorldDatabaseSelect_PvpPointLimitInfo346_ptr);
        using CRFWorldDatabaseSelect_PvpRankInfo348_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, char*, struct _PVP_RANK_DATA*);
        using CRFWorldDatabaseSelect_PvpRankInfo348_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, char*, struct _PVP_RANK_DATA*, CRFWorldDatabaseSelect_PvpRankInfo348_ptr);
        using CRFWorldDatabaseSelect_PvpRate350_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*, uint16_t*);
        using CRFWorldDatabaseSelect_PvpRate350_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*, uint16_t*, CRFWorldDatabaseSelect_PvpRate350_ptr);
        using CRFWorldDatabaseSelect_Quest352_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_quest_array*);
        using CRFWorldDatabaseSelect_Quest352_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_quest_array*, CRFWorldDatabaseSelect_Quest352_ptr);
        using CRFWorldDatabaseSelect_RFEvent_ClassRefine354_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*);
        using CRFWorldDatabaseSelect_RFEvent_ClassRefine354_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, unsigned int*, CRFWorldDatabaseSelect_RFEvent_ClassRefine354_ptr);
        using CRFWorldDatabaseSelect_RaceBossAccumulationWinRate356_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, struct _raceboss_acc_winrate*);
        using CRFWorldDatabaseSelect_RaceBossAccumulationWinRate356_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, struct _raceboss_acc_winrate*, CRFWorldDatabaseSelect_RaceBossAccumulationWinRate356_ptr);
        using CRFWorldDatabaseSelect_RaceBossCurrentWinRate358_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, char*, unsigned int*, unsigned int*);
        using CRFWorldDatabaseSelect_RaceBossCurrentWinRate358_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, char*, unsigned int*, unsigned int*, CRFWorldDatabaseSelect_RaceBossCurrentWinRate358_ptr);
        using CRFWorldDatabaseSelect_RegeAvator_For_Lobby_Logout360_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _rege_char_data*);
        using CRFWorldDatabaseSelect_RegeAvator_For_Lobby_Logout360_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _rege_char_data*, CRFWorldDatabaseSelect_RegeAvator_For_Lobby_Logout360_ptr);
        using CRFWorldDatabaseSelect_ReturnPost362_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, struct _return_post_list*);
        using CRFWorldDatabaseSelect_ReturnPost362_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, struct _return_post_list*, CRFWorldDatabaseSelect_ReturnPost362_ptr);
        using CRFWorldDatabaseSelect_SFDelayInfo364_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_sf_delay_info*);
        using CRFWorldDatabaseSelect_SFDelayInfo364_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_sf_delay_info*, CRFWorldDatabaseSelect_SFDelayInfo364_ptr);
        using CRFWorldDatabaseSelect_Start_NpcQuest_History366_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_start_npc_quest_complete_history*, unsigned int);
        using CRFWorldDatabaseSelect_Start_NpcQuest_History366_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_start_npc_quest_complete_history*, unsigned int, CRFWorldDatabaseSelect_Start_NpcQuest_History366_ptr);
        using CRFWorldDatabaseSelect_Start_NpcQuest_History_Count368_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_Start_NpcQuest_History_Count368_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, CRFWorldDatabaseSelect_Start_NpcQuest_History_Count368_ptr);
        using CRFWorldDatabaseSelect_StoreLimitItem370_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, struct _qry_case_all_store_limit_item*);
        using CRFWorldDatabaseSelect_StoreLimitItem370_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, struct _qry_case_all_store_limit_item*, CRFWorldDatabaseSelect_StoreLimitItem370_ptr);
        using CRFWorldDatabaseSelect_Supplement372_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_supplement_info*);
        using CRFWorldDatabaseSelect_Supplement372_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_supplement_info*, CRFWorldDatabaseSelect_Supplement372_ptr);
        using CRFWorldDatabaseSelect_Supplement_ActPoint374_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_supplement_info*);
        using CRFWorldDatabaseSelect_Supplement_ActPoint374_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_supplement_info*, CRFWorldDatabaseSelect_Supplement_ActPoint374_ptr);
        using CRFWorldDatabaseSelect_Supplement_Ex376_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_supplement_info*);
        using CRFWorldDatabaseSelect_Supplement_Ex376_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_character_supplement_info*, CRFWorldDatabaseSelect_Supplement_Ex376_ptr);
        using CRFWorldDatabaseSelect_TakeItem378_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_item_list*);
        using CRFWorldDatabaseSelect_TakeItem378_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_item_list*, CRFWorldDatabaseSelect_TakeItem378_ptr);
        using CRFWorldDatabaseSelect_TotalGuildRank380_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char*, struct _total_guild_rank_info*);
        using CRFWorldDatabaseSelect_TotalGuildRank380_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char*, struct _total_guild_rank_info*, CRFWorldDatabaseSelect_TotalGuildRank380_ptr);
        using CRFWorldDatabaseSelect_TotalRecordNum382_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*);
        using CRFWorldDatabaseSelect_TotalRecordNum382_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, CRFWorldDatabaseSelect_TotalRecordNum382_ptr);
        using CRFWorldDatabaseSelect_Trade384_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, struct _worlddb_trade_info*);
        using CRFWorldDatabaseSelect_Trade384_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, struct _worlddb_trade_info*, CRFWorldDatabaseSelect_Trade384_ptr);
        using CRFWorldDatabaseSelect_TrunkMoney386_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*);
        using CRFWorldDatabaseSelect_TrunkMoney386_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*, CRFWorldDatabaseSelect_TrunkMoney386_ptr);
        using CRFWorldDatabaseSelect_Unit388_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_unit_info_array*);
        using CRFWorldDatabaseSelect_Unit388_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_unit_info_array*, CRFWorldDatabaseSelect_Unit388_ptr);
        using CRFWorldDatabaseSelect_UnitData390_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*);
        using CRFWorldDatabaseSelect_UnitData390_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*, CRFWorldDatabaseSelect_UnitData390_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderBuySingleItemInfo392_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, struct _unmannedtrader_buy_item_info*);
        using CRFWorldDatabaseSelect_UnmannedTraderBuySingleItemInfo392_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, struct _unmannedtrader_buy_item_info*, CRFWorldDatabaseSelect_UnmannedTraderBuySingleItemInfo392_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderItemRecordCntByState394_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int*, uint16_t, uint16_t*);
        using CRFWorldDatabaseSelect_UnmannedTraderItemRecordCntByState394_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int*, uint16_t, uint16_t*, CRFWorldDatabaseSelect_UnmannedTraderItemRecordCntByState394_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderItemState396_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char*);
        using CRFWorldDatabaseSelect_UnmannedTraderItemState396_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char*, CRFWorldDatabaseSelect_UnmannedTraderItemState396_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderItemStateInfo398_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, struct _unmannedtrader_stade_id_info*, unsigned int);
        using CRFWorldDatabaseSelect_UnmannedTraderItemStateInfo398_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, struct _unmannedtrader_stade_id_info*, unsigned int, CRFWorldDatabaseSelect_UnmannedTraderItemStateInfo398_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderItemStateInfoCnt400_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*);
        using CRFWorldDatabaseSelect_UnmannedTraderItemStateInfoCnt400_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, CRFWorldDatabaseSelect_UnmannedTraderItemStateInfoCnt400_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderRegister402_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_UnmannedTraderRegister402_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*, CRFWorldDatabaseSelect_UnmannedTraderRegister402_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderReservedSchedule404_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _unmannedtrader_reserved_schedule_info*);
        using CRFWorldDatabaseSelect_UnmannedTraderReservedSchedule404_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _unmannedtrader_reserved_schedule_info*, CRFWorldDatabaseSelect_UnmannedTraderReservedSchedule404_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderSearchGroupTotalRowCount406_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, char, char, char, char, unsigned int*);
        using CRFWorldDatabaseSelect_UnmannedTraderSearchGroupTotalRowCount406_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, char, char, char, char, unsigned int*, CRFWorldDatabaseSelect_UnmannedTraderSearchGroupTotalRowCount406_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderSearchPageInfo408_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, char, char, char, char, unsigned int, unsigned int, char*, struct _unmannedtrader_page_info*);
        using CRFWorldDatabaseSelect_UnmannedTraderSearchPageInfo408_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, char, char, char, char, unsigned int, unsigned int, char*, struct _unmannedtrader_page_info*, CRFWorldDatabaseSelect_UnmannedTraderSearchPageInfo408_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderSellInfo410_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, struct _unmannedtrader_seller_info*);
        using CRFWorldDatabaseSelect_UnmannedTraderSellInfo410_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, struct _unmannedtrader_seller_info*, CRFWorldDatabaseSelect_UnmannedTraderSellInfo410_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderSingleItemBottomSerial412_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*);
        using CRFWorldDatabaseSelect_UnmannedTraderSingleItemBottomSerial412_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, CRFWorldDatabaseSelect_UnmannedTraderSingleItemBottomSerial412_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderSingleItemEmptyRecordCnt414_ptr = int (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseSelect_UnmannedTraderSingleItemEmptyRecordCnt414_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseSelect_UnmannedTraderSingleItemEmptyRecordCnt414_ptr);
        using CRFWorldDatabaseSelect_UnmannedTraderSingleItemEmptyRecordSerial416_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*);
        using CRFWorldDatabaseSelect_UnmannedTraderSingleItemEmptyRecordSerial416_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, CRFWorldDatabaseSelect_UnmannedTraderSingleItemEmptyRecordSerial416_ptr);
        using CRFWorldDatabaseSelect_UsedLimitItemRecordNum418_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*);
        using CRFWorldDatabaseSelect_UsedLimitItemRecordNum418_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int*, CRFWorldDatabaseSelect_UsedLimitItemRecordNum418_ptr);
        using CRFWorldDatabaseSelect_UserCountInfo420_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, struct _worlddb_user_count_info*);
        using CRFWorldDatabaseSelect_UserCountInfo420_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char*, struct _worlddb_user_count_info*, CRFWorldDatabaseSelect_UserCountInfo420_ptr);
        using CRFWorldDatabaseSelect_UserInterface422_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_userinterface_info*);
        using CRFWorldDatabaseSelect_UserInterface422_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_userinterface_info*, CRFWorldDatabaseSelect_UserInterface422_ptr);
        using CRFWorldDatabaseSelect_WaitItem424_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_item_list*);
        using CRFWorldDatabaseSelect_WaitItem424_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_item_list*, CRFWorldDatabaseSelect_WaitItem424_ptr);
        using CRFWorldDatabaseSelect_WeeklyGuildRankOwnerGuild426_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char*, char, char, struct _weeklyguildrank_owner_info*);
        using CRFWorldDatabaseSelect_WeeklyGuildRankOwnerGuild426_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char*, char, char, struct _weeklyguildrank_owner_info*, CRFWorldDatabaseSelect_WeeklyGuildRankOwnerGuild426_ptr);
        using CRFWorldDatabaseSelect_WinBattleCount428_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_WinBattleCount428_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*, CRFWorldDatabaseSelect_WinBattleCount428_ptr);
        using CRFWorldDatabaseSelect_utSellWaitItems_SalesTotals430_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*);
        using CRFWorldDatabaseSelect_utSellWaitItems_SalesTotals430_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int*, CRFWorldDatabaseSelect_utSellWaitItems_SalesTotals430_ptr);
        using CRFWorldDatabaseTruncate_UnmannedTraderItemStateRecord432_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseTruncate_UnmannedTraderItemStateRecord432_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseTruncate_UnmannedTraderItemStateRecord432_ptr);
        using CRFWorldDatabaseUpdateClearGuildBattleInfo434_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdateClearGuildBattleInfo434_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, CRFWorldDatabaseUpdateClearGuildBattleInfo434_ptr);
        using CRFWorldDatabaseUpdateClearGuildBattleRank436_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseUpdateClearGuildBattleRank436_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseUpdateClearGuildBattleRank436_ptr);
        using CRFWorldDatabaseUpdateClearGuildBattleScheduleInfo438_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdateClearGuildBattleScheduleInfo438_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, CRFWorldDatabaseUpdateClearGuildBattleScheduleInfo438_ptr);
        using CRFWorldDatabaseUpdateClearGuildBattleScheduleInfo440_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdateClearGuildBattleScheduleInfo440_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdateClearGuildBattleScheduleInfo440_ptr);
        using CRFWorldDatabaseUpdateDrawGuildBattleResult442_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdateDrawGuildBattleResult442_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, CRFWorldDatabaseUpdateDrawGuildBattleResult442_ptr);
        using CRFWorldDatabaseUpdateGuildBattleInfo444_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int, unsigned int, char);
        using CRFWorldDatabaseUpdateGuildBattleInfo444_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int, unsigned int, char, CRFWorldDatabaseUpdateGuildBattleInfo444_ptr);
        using CRFWorldDatabaseUpdateGuildBattleScheduleInfo446_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, int64_t, uint16_t);
        using CRFWorldDatabaseUpdateGuildBattleScheduleInfo446_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, int64_t, uint16_t, CRFWorldDatabaseUpdateGuildBattleScheduleInfo446_ptr);
        using CRFWorldDatabaseUpdateGuildMoney448_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, long double);
        using CRFWorldDatabaseUpdateGuildMoney448_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, long double, CRFWorldDatabaseUpdateGuildMoney448_ptr);
        using CRFWorldDatabaseUpdateLoseGuildBattleResult450_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdateLoseGuildBattleResult450_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, CRFWorldDatabaseUpdateLoseGuildBattleResult450_ptr);
        using CRFWorldDatabaseUpdateServerResetToken452_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, uint16_t, unsigned int);
        using CRFWorldDatabaseUpdateServerResetToken452_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, uint16_t, unsigned int, CRFWorldDatabaseUpdateServerResetToken452_ptr);
        using CRFWorldDatabaseUpdateVotedReset_Cheat454_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdateVotedReset_Cheat454_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdateVotedReset_Cheat454_ptr);
        using CRFWorldDatabaseUpdateVotedReset_General456_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdateVotedReset_General456_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdateVotedReset_General456_ptr);
        using CRFWorldDatabaseUpdateVotedReset_Supplement458_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdateVotedReset_Supplement458_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdateVotedReset_Supplement458_ptr);
        using CRFWorldDatabaseUpdateWinGuildBattleResult460_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdateWinGuildBattleResult460_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, CRFWorldDatabaseUpdateWinGuildBattleResult460_ptr);
        using CRFWorldDatabaseUpdate_AnimusData462_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, long double*);
        using CRFWorldDatabaseUpdate_AnimusData462_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, long double*, CRFWorldDatabaseUpdate_AnimusData462_ptr);
        using CRFWorldDatabaseUpdate_BattleResultLogBattleResultAndPvpPoint464_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdate_BattleResultLogBattleResultAndPvpPoint464_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int, CRFWorldDatabaseUpdate_BattleResultLogBattleResultAndPvpPoint464_ptr);
        using CRFWorldDatabaseUpdate_CharSlot466_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdate_CharSlot466_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdate_CharSlot466_ptr);
        using CRFWorldDatabaseUpdate_CharacterData468_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_update_char_query*);
        using CRFWorldDatabaseUpdate_CharacterData468_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_update_char_query*, CRFWorldDatabaseUpdate_CharacterData468_ptr);
        using CRFWorldDatabaseUpdate_CharacterReName470_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int);
        using CRFWorldDatabaseUpdate_CharacterReName470_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int, CRFWorldDatabaseUpdate_CharacterReName470_ptr);
        using CRFWorldDatabaseUpdate_ClearWeeklyPvpPointSum472_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseUpdate_ClearWeeklyPvpPointSum472_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseUpdate_ClearWeeklyPvpPointSum472_ptr);
        using CRFWorldDatabaseUpdate_CristalBattleCharInfo474_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, char, int, uint16_t, uint16_t);
        using CRFWorldDatabaseUpdate_CristalBattleCharInfo474_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, char, int, uint16_t, uint16_t, CRFWorldDatabaseUpdate_CristalBattleCharInfo474_ptr);
        using CRFWorldDatabaseUpdate_Dalant476_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdate_Dalant476_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, CRFWorldDatabaseUpdate_Dalant476_ptr);
        using CRFWorldDatabaseUpdate_DisableInstanceStore478_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdate_DisableInstanceStore478_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdate_DisableInstanceStore478_ptr);
        using CRFWorldDatabaseUpdate_DisappearOwnerRecord480_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseUpdate_DisappearOwnerRecord480_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseUpdate_DisappearOwnerRecord480_ptr);
        using CRFWorldDatabaseUpdate_GmGreet482_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _qry_case_gm_greetingmsg*);
        using CRFWorldDatabaseUpdate_GmGreet482_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _qry_case_gm_greetingmsg*, CRFWorldDatabaseUpdate_GmGreet482_ptr);
        using CRFWorldDatabaseUpdate_Gold484_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdate_Gold484_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, CRFWorldDatabaseUpdate_Gold484_ptr);
        using CRFWorldDatabaseUpdate_GuildEmblem486_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdate_GuildEmblem486_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, unsigned int, unsigned int, CRFWorldDatabaseUpdate_GuildEmblem486_ptr);
        using CRFWorldDatabaseUpdate_GuildGrade488_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseUpdate_GuildGrade488_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseUpdate_GuildGrade488_ptr);
        using CRFWorldDatabaseUpdate_GuildGreet490_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _qry_case_guild_greetingmsg*);
        using CRFWorldDatabaseUpdate_GuildGreet490_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _qry_case_guild_greetingmsg*, CRFWorldDatabaseUpdate_GuildGreet490_ptr);
        using CRFWorldDatabaseUpdate_GuildMaster492_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char);
        using CRFWorldDatabaseUpdate_GuildMaster492_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, CRFWorldDatabaseUpdate_GuildMaster492_ptr);
        using CRFWorldDatabaseUpdate_GuildMemberCount494_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, uint16_t);
        using CRFWorldDatabaseUpdate_GuildMemberCount494_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, uint16_t, CRFWorldDatabaseUpdate_GuildMemberCount494_ptr);
        using CRFWorldDatabaseUpdate_GuildRank496_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_GuildRank496_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_GuildRank496_ptr);
        using CRFWorldDatabaseUpdate_GuildRank_Step1498_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_GuildRank_Step1498_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_GuildRank_Step1498_ptr);
        using CRFWorldDatabaseUpdate_GuildRank_Step2500_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_GuildRank_Step2500_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_GuildRank_Step2500_ptr);
        using CRFWorldDatabaseUpdate_GuildRank_Step3502_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_GuildRank_Step3502_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_GuildRank_Step3502_ptr);
        using CRFWorldDatabaseUpdate_GuildRoom504_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdate_GuildRoom504_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdate_GuildRoom504_ptr);
        using CRFWorldDatabaseUpdate_IncreaseWeeklyGuildGuildBattlePvpPointSum506_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double);
        using CRFWorldDatabaseUpdate_IncreaseWeeklyGuildGuildBattlePvpPointSum506_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, CRFWorldDatabaseUpdate_IncreaseWeeklyGuildGuildBattlePvpPointSum506_ptr);
        using CRFWorldDatabaseUpdate_IncreaseWeeklyGuildKillPvpPointSum508_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double);
        using CRFWorldDatabaseUpdate_IncreaseWeeklyGuildKillPvpPointSum508_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, CRFWorldDatabaseUpdate_IncreaseWeeklyGuildKillPvpPointSum508_ptr);
        using CRFWorldDatabaseUpdate_InputGuildMoney510_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdate_InputGuildMoney510_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int, CRFWorldDatabaseUpdate_InputGuildMoney510_ptr);
        using CRFWorldDatabaseUpdate_Level512_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char);
        using CRFWorldDatabaseUpdate_Level512_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, CRFWorldDatabaseUpdate_Level512_ptr);
        using CRFWorldDatabaseUpdate_LimitItemNum514_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_LimitItemNum514_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_LimitItemNum514_ptr);
        using CRFWorldDatabaseUpdate_MacroData516_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _AIOC_A_MACRODATA*);
        using CRFWorldDatabaseUpdate_MacroData516_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _AIOC_A_MACRODATA*, CRFWorldDatabaseUpdate_MacroData516_ptr);
        using CRFWorldDatabaseUpdate_NpcData518_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*);
        using CRFWorldDatabaseUpdate_NpcData518_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int*, CRFWorldDatabaseUpdate_NpcData518_ptr);
        using CRFWorldDatabaseUpdate_OutputGuildMoney520_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdate_OutputGuildMoney520_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, unsigned int, CRFWorldDatabaseUpdate_OutputGuildMoney520_ptr);
        using CRFWorldDatabaseUpdate_PatriarchComm522_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char*);
        using CRFWorldDatabaseUpdate_PatriarchComm522_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char*, CRFWorldDatabaseUpdate_PatriarchComm522_ptr);
        using CRFWorldDatabaseUpdate_Player_TimeLimit_Info524_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char);
        using CRFWorldDatabaseUpdate_Player_TimeLimit_Info524_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, CRFWorldDatabaseUpdate_Player_TimeLimit_Info524_ptr);
        using CRFWorldDatabaseUpdate_Player_Vote_Info526_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, char, unsigned int);
        using CRFWorldDatabaseUpdate_Player_Vote_Info526_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, char, unsigned int, CRFWorldDatabaseUpdate_Player_Vote_Info526_ptr);
        using CRFWorldDatabaseUpdate_Post528_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_Post528_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_Post528_ptr);
        using CRFWorldDatabaseUpdate_PostRegistry530_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char*, char*, char*, char*, int, uint64_t, unsigned int, unsigned int, char, char, uint64_t);
        using CRFWorldDatabaseUpdate_PostRegistry530_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char*, char*, char*, char*, int, uint64_t, unsigned int, unsigned int, char, char, uint64_t, CRFWorldDatabaseUpdate_PostRegistry530_ptr);
        using CRFWorldDatabaseUpdate_PostRegistryDisable532_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdate_PostRegistryDisable532_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdate_PostRegistryDisable532_ptr);
        using CRFWorldDatabaseUpdate_PostStorageSendToRecver534_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, char*, char*, char*, char*, int, uint64_t, unsigned int, unsigned int, char, uint16_t, char*, bool, uint64_t);
        using CRFWorldDatabaseUpdate_PostStorageSendToRecver534_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, char*, char*, char*, char*, int, uint64_t, unsigned int, unsigned int, char, uint16_t, char*, bool, uint64_t, CRFWorldDatabaseUpdate_PostStorageSendToRecver534_ptr);
        using CRFWorldDatabaseUpdate_Punishment536_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_Punishment536_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_Punishment536_ptr);
        using CRFWorldDatabaseUpdate_PvpPointGuildRankRecord538_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int, uint16_t);
        using CRFWorldDatabaseUpdate_PvpPointGuildRankRecord538_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, unsigned int, uint16_t, CRFWorldDatabaseUpdate_PvpPointGuildRankRecord538_ptr);
        using CRFWorldDatabaseUpdate_PvpPointGuildRankSumLv540_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char, char, char);
        using CRFWorldDatabaseUpdate_PvpPointGuildRankSumLv540_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, char, char, char, CRFWorldDatabaseUpdate_PvpPointGuildRankSumLv540_ptr);
        using CRFWorldDatabaseUpdate_PvpPointInfo542_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, int16_t*, long double);
        using CRFWorldDatabaseUpdate_PvpPointInfo542_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, int16_t*, long double, CRFWorldDatabaseUpdate_PvpPointInfo542_ptr);
        using CRFWorldDatabaseUpdate_RFEvent_ClassRefine544_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, unsigned int);
        using CRFWorldDatabaseUpdate_RFEvent_ClassRefine544_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, unsigned int, CRFWorldDatabaseUpdate_RFEvent_ClassRefine544_ptr);
        using CRFWorldDatabaseUpdate_RaceGreet546_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _qry_case_race_greetingmsg*);
        using CRFWorldDatabaseUpdate_RaceGreet546_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, struct _qry_case_race_greetingmsg*, CRFWorldDatabaseUpdate_RaceGreet546_ptr);
        using CRFWorldDatabaseUpdate_RaceRank548_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank548_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank548_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step1550_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step1550_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step1550_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step2552_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step2552_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step2552_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step3554_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step3554_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step3554_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step4556_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step4556_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step4556_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step5558_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step5558_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step5558_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step6560_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step6560_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step6560_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step7562_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step7562_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step7562_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step8564_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step8564_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step8564_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step9566_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step9566_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step9566_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step_6_1568_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step_6_1568_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step_6_1568_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step_6_2570_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step_6_2570_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step_6_2570_ptr);
        using CRFWorldDatabaseUpdate_RaceRank_Step_6_3572_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseUpdate_RaceRank_Step_6_3572_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseUpdate_RaceRank_Step_6_3572_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild574_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_rankinguild_info*);
        using CRFWorldDatabaseUpdate_RankInGuild574_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_rankinguild_info*, CRFWorldDatabaseUpdate_RankInGuild574_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild_Step1576_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdate_RankInGuild_Step1576_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdate_RankInGuild_Step1576_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild_Step2578_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdate_RankInGuild_Step2578_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdate_RankInGuild_Step2578_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild_Step3580_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdate_RankInGuild_Step3580_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdate_RankInGuild_Step3580_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild_Step4582_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseUpdate_RankInGuild_Step4582_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseUpdate_RankInGuild_Step4582_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild_Step5584_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_rankinguild_info*);
        using CRFWorldDatabaseUpdate_RankInGuild_Step5584_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_rankinguild_info*, CRFWorldDatabaseUpdate_RankInGuild_Step5584_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild_Step6586_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseUpdate_RankInGuild_Step6586_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseUpdate_RankInGuild_Step6586_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild_Step7588_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseUpdate_RankInGuild_Step7588_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseUpdate_RankInGuild_Step7588_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild_Step8590_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseUpdate_RankInGuild_Step8590_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseUpdate_RankInGuild_Step8590_ptr);
        using CRFWorldDatabaseUpdate_RankInGuild_Step9592_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseUpdate_RankInGuild_Step9592_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseUpdate_RankInGuild_Step9592_ptr);
        using CRFWorldDatabaseUpdate_SFDelayInfo594_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_sf_delay_info*);
        using CRFWorldDatabaseUpdate_SFDelayInfo594_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _worlddb_sf_delay_info*, CRFWorldDatabaseUpdate_SFDelayInfo594_ptr);
        using CRFWorldDatabaseUpdate_SetActive596_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char);
        using CRFWorldDatabaseUpdate_SetActive596_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, CRFWorldDatabaseUpdate_SetActive596_ptr);
        using CRFWorldDatabaseUpdate_SetGuildMoney598_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, long double);
        using CRFWorldDatabaseUpdate_SetGuildMoney598_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double, long double, CRFWorldDatabaseUpdate_SetGuildMoney598_ptr);
        using CRFWorldDatabaseUpdate_Set_Limit_Run600_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, int);
        using CRFWorldDatabaseUpdate_Set_Limit_Run600_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, int, CRFWorldDatabaseUpdate_Set_Limit_Run600_ptr);
        using CRFWorldDatabaseUpdate_Start_NpcQuest_History602_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, char*, int64_t);
        using CRFWorldDatabaseUpdate_Start_NpcQuest_History602_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, char, char*, int64_t, CRFWorldDatabaseUpdate_Start_NpcQuest_History602_ptr);
        using CRFWorldDatabaseUpdate_UnitData604_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*);
        using CRFWorldDatabaseUpdate_UnitData604_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, long double*, CRFWorldDatabaseUpdate_UnitData604_ptr);
        using CRFWorldDatabaseUpdate_UnmannedTraderCheatUpdateRegistDate606_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int);
        using CRFWorldDatabaseUpdate_UnmannedTraderCheatUpdateRegistDate606_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, CRFWorldDatabaseUpdate_UnmannedTraderCheatUpdateRegistDate606_ptr);
        using CRFWorldDatabaseUpdate_UnmannedTraderClearDanglingOwnerRecord608_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabaseUpdate_UnmannedTraderClearDanglingOwnerRecord608_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabaseUpdate_UnmannedTraderClearDanglingOwnerRecord608_ptr);
        using CRFWorldDatabaseUpdate_UnmannedTraderItemState610_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, struct _SYSTEMTIME*);
        using CRFWorldDatabaseUpdate_UnmannedTraderItemState610_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, struct _SYSTEMTIME*, CRFWorldDatabaseUpdate_UnmannedTraderItemState610_ptr);
        using CRFWorldDatabaseUpdate_UnmannedTraderReRegist612_ptr = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, unsigned int, unsigned int, struct _SYSTEMTIME*);
        using CRFWorldDatabaseUpdate_UnmannedTraderReRegist612_clbk = char (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, unsigned int, unsigned int, struct _SYSTEMTIME*, CRFWorldDatabaseUpdate_UnmannedTraderReRegist612_ptr);
        using CRFWorldDatabaseUpdate_UnmannedTraderResutlInfo614_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, unsigned int, unsigned int, struct _SYSTEMTIME*);
        using CRFWorldDatabaseUpdate_UnmannedTraderResutlInfo614_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char, unsigned int, unsigned int, struct _SYSTEMTIME*, CRFWorldDatabaseUpdate_UnmannedTraderResutlInfo614_ptr);
        using CRFWorldDatabaseUpdate_UnmannedTraderSellInfo616_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _unmannedtrader_registsingleitem*, struct _SYSTEMTIME*);
        using CRFWorldDatabaseUpdate_UnmannedTraderSellInfo616_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _unmannedtrader_registsingleitem*, struct _SYSTEMTIME*, CRFWorldDatabaseUpdate_UnmannedTraderSellInfo616_ptr);
        using CRFWorldDatabaseUpdate_UnmannedTraderSellInfoPrice618_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int, unsigned int);
        using CRFWorldDatabaseUpdate_UnmannedTraderSellInfoPrice618_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, unsigned int, unsigned int, CRFWorldDatabaseUpdate_UnmannedTraderSellInfoPrice618_ptr);
        using CRFWorldDatabaseUpdate_UnmannedTraderSingleItemInfo620_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _unmannedtrader_registsingleitem*);
        using CRFWorldDatabaseUpdate_UnmannedTraderSingleItemInfo620_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _unmannedtrader_registsingleitem*, CRFWorldDatabaseUpdate_UnmannedTraderSingleItemInfo620_ptr);
        using CRFWorldDatabaseUpdate_UnmannedTraderSingleTypeClearUseCompleteRecord622_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _SYSTEMTIME*);
        using CRFWorldDatabaseUpdate_UnmannedTraderSingleTypeClearUseCompleteRecord622_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _SYSTEMTIME*, CRFWorldDatabaseUpdate_UnmannedTraderSingleTypeClearUseCompleteRecord622_ptr);
        using CRFWorldDatabaseUpdate_UserGuildData624_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char);
        using CRFWorldDatabaseUpdate_UserGuildData624_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, unsigned int, char, CRFWorldDatabaseUpdate_UserGuildData624_ptr);
        using CRFWorldDatabaseUpdatet_Account_Vote_Available626_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*);
        using CRFWorldDatabaseUpdatet_Account_Vote_Available626_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char*, CRFWorldDatabaseUpdatet_Account_Vote_Available626_ptr);
        using CRFWorldDatabasecreate_amine_personal631_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabasecreate_amine_personal631_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabasecreate_amine_personal631_ptr);
        using CRFWorldDatabasecreate_automine_table633_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabasecreate_automine_table633_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabasecreate_automine_table633_ptr);
        using CRFWorldDatabasecreate_sumtotal_dungeon635_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, int, char**);
        using CRFWorldDatabasecreate_sumtotal_dungeon635_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, int, char**, CRFWorldDatabasecreate_sumtotal_dungeon635_ptr);
        using CRFWorldDatabasecreate_table_atrade_taxrate637_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabasecreate_table_atrade_taxrate637_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabasecreate_table_atrade_taxrate637_ptr);
        using CRFWorldDatabaseexist_aminpersonal_inven639_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseexist_aminpersonal_inven639_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseexist_aminpersonal_inven639_ptr);
        using CRFWorldDatabaseexist_automine641_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, char);
        using CRFWorldDatabaseexist_automine641_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, char, CRFWorldDatabaseexist_automine641_ptr);
        using CRFWorldDatabaseinsert_amine_newowner643_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int);
        using CRFWorldDatabaseinsert_amine_newowner643_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, CRFWorldDatabaseinsert_amine_newowner643_ptr);
        using CRFWorldDatabaseinsert_amine_personal645_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseinsert_amine_personal645_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseinsert_amine_personal645_ptr);
        using CRFWorldDatabaseinsert_atrade_taxrate647_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char*, unsigned int, char*, char, unsigned int);
        using CRFWorldDatabaseinsert_atrade_taxrate647_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, unsigned int, char*, unsigned int, char*, char, unsigned int, CRFWorldDatabaseinsert_atrade_taxrate647_ptr);
        using CRFWorldDatabaseselect_amine_personal649_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int);
        using CRFWorldDatabaseselect_amine_personal649_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, CRFWorldDatabaseselect_amine_personal649_ptr);
        using CRFWorldDatabaseselect_amine_personal651_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _personal_amine_inven*);
        using CRFWorldDatabaseselect_amine_personal651_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, struct _personal_amine_inven*, CRFWorldDatabaseselect_amine_personal651_ptr);
        using CRFWorldDatabaseselect_atrade_taxrate653_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, char, char*, char*, char*);
        using CRFWorldDatabaseselect_atrade_taxrate653_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, char, char*, char*, char*, CRFWorldDatabaseselect_atrade_taxrate653_ptr);
        using CRFWorldDatabaseselect_automine655_ptr = int (WINAPIV*)(struct CRFWorldDatabase*, struct _DB_LOAD_AUTOMINE_MACHINE*);
        using CRFWorldDatabaseselect_automine655_clbk = int (WINAPIV*)(struct CRFWorldDatabase*, struct _DB_LOAD_AUTOMINE_MACHINE*, CRFWorldDatabaseselect_automine655_ptr);
        using CRFWorldDatabaseupdate_amine_battery657_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, unsigned int);
        using CRFWorldDatabaseupdate_amine_battery657_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, unsigned int, CRFWorldDatabaseupdate_amine_battery657_ptr);
        using CRFWorldDatabaseupdate_amine_dck659_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int);
        using CRFWorldDatabaseupdate_amine_dck659_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, CRFWorldDatabaseupdate_amine_dck659_ptr);
        using CRFWorldDatabaseupdate_amine_mineore661_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, char, unsigned int, char, unsigned int);
        using CRFWorldDatabaseupdate_amine_mineore661_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, char, unsigned int, char, unsigned int, CRFWorldDatabaseupdate_amine_mineore661_ptr);
        using CRFWorldDatabaseupdate_amine_moveore663_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, char, unsigned int, char, char, unsigned int, char);
        using CRFWorldDatabaseupdate_amine_moveore663_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, char, unsigned int, char, char, unsigned int, char, CRFWorldDatabaseupdate_amine_moveore663_ptr);
        using CRFWorldDatabaseupdate_amine_personal665_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char*);
        using CRFWorldDatabaseupdate_amine_personal665_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char*, CRFWorldDatabaseupdate_amine_personal665_ptr);
        using CRFWorldDatabaseupdate_amine_selore667_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, char);
        using CRFWorldDatabaseupdate_amine_selore667_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, char, CRFWorldDatabaseupdate_amine_selore667_ptr);
        using CRFWorldDatabaseupdate_amine_workstate669_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, bool);
        using CRFWorldDatabaseupdate_amine_workstate669_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, char, char, unsigned int, bool, CRFWorldDatabaseupdate_amine_workstate669_ptr);
        using CRFWorldDatabaseupdate_cristalbattle_date671_ptr = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char);
        using CRFWorldDatabaseupdate_cristalbattle_date671_clbk = bool (WINAPIV*)(struct CRFWorldDatabase*, unsigned int, char, CRFWorldDatabaseupdate_cristalbattle_date671_ptr);
        
        using CRFWorldDatabasedtor_CRFWorldDatabase673_ptr = void (WINAPIV*)(struct CRFWorldDatabase*);
        using CRFWorldDatabasedtor_CRFWorldDatabase673_clbk = void (WINAPIV*)(struct CRFWorldDatabase*, CRFWorldDatabasedtor_CRFWorldDatabase673_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuildMasterEffect.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CGuildMasterEffectctor_CGuildMasterEffect2_ptr = void (WINAPIV*)(struct CGuildMasterEffect*);
        using CGuildMasterEffectctor_CGuildMasterEffect2_clbk = void (WINAPIV*)(struct CGuildMasterEffect*, CGuildMasterEffectctor_CGuildMasterEffect2_ptr);
        using CGuildMasterEffectGetInstance4_ptr = struct CGuildMasterEffect* (WINAPIV*)();
        using CGuildMasterEffectGetInstance4_clbk = struct CGuildMasterEffect* (WINAPIV*)(CGuildMasterEffectGetInstance4_ptr);
        using CGuildMasterEffectadjust_effect6_ptr = void (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char, bool);
        using CGuildMasterEffectadjust_effect6_clbk = void (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char, bool, CGuildMasterEffectadjust_effect6_ptr);
        using CGuildMasterEffectchange_player8_ptr = bool (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char, char);
        using CGuildMasterEffectchange_player8_clbk = bool (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char, char, CGuildMasterEffectchange_player8_ptr);
        using CGuildMasterEffectget_AdjustableGrade10_ptr = char (WINAPIV*)(struct CGuildMasterEffect*);
        using CGuildMasterEffectget_AdjustableGrade10_clbk = char (WINAPIV*)(struct CGuildMasterEffect*, CGuildMasterEffectget_AdjustableGrade10_ptr);
        using CGuildMasterEffectget_AttactValueByGrade12_ptr = char (WINAPIV*)(struct CGuildMasterEffect*, char);
        using CGuildMasterEffectget_AttactValueByGrade12_clbk = char (WINAPIV*)(struct CGuildMasterEffect*, char, CGuildMasterEffectget_AttactValueByGrade12_ptr);
        using CGuildMasterEffectget_DefenceValueByGrade14_ptr = char (WINAPIV*)(struct CGuildMasterEffect*, char);
        using CGuildMasterEffectget_DefenceValueByGrade14_clbk = char (WINAPIV*)(struct CGuildMasterEffect*, char, CGuildMasterEffectget_DefenceValueByGrade14_ptr);
        using CGuildMasterEffectin_player16_ptr = bool (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char);
        using CGuildMasterEffectin_player16_clbk = bool (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char, CGuildMasterEffectin_player16_ptr);
        using CGuildMasterEffectinit18_ptr = bool (WINAPIV*)(struct CGuildMasterEffect*);
        using CGuildMasterEffectinit18_clbk = bool (WINAPIV*)(struct CGuildMasterEffect*, CGuildMasterEffectinit18_ptr);
        using CGuildMasterEffectout_player20_ptr = bool (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char);
        using CGuildMasterEffectout_player20_clbk = bool (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char, CGuildMasterEffectout_player20_ptr);
        using CGuildMasterEffectshow_to_all22_ptr = void (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char, char, char);
        using CGuildMasterEffectshow_to_all22_clbk = void (WINAPIV*)(struct CGuildMasterEffect*, struct CPlayer*, char, char, char, CGuildMasterEffectshow_to_all22_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _dummy_positionSetActiveMonNum2_ptr = void (WINAPIV*)(struct _dummy_position*, int);
        using _dummy_positionSetActiveMonNum2_clbk = void (WINAPIV*)(struct _dummy_position*, int, _dummy_positionSetActiveMonNum2_ptr);
        
        using _dummy_positionctor__dummy_position4_ptr = void (WINAPIV*)(struct _dummy_position*);
        using _dummy_positionctor__dummy_position4_clbk = void (WINAPIV*)(struct _dummy_position*, _dummy_positionctor__dummy_position4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace CComEnumImplIEnumUnknown,&_GUID_00000100_0000_0000_c000_000000000046,IUnknown *,ATL
        {
            namespace _CopyInterfaceIUnknown 
            {
                enum FlagBits
                {
                  BitCopy = 0x1,
                  BitOwn = 0x2,
                };
            }; // end namespace _CopyInterfaceIUnknown 
        }; // end namespace CComEnumImplIEnumUnknown,&_GUID_00000100_0000_0000_c000_000000000046,IUnknown *,ATL
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AFX_EXCEPTION_CONTEXT.hpp>
#include <AFX_MODULE_STATE.hpp>
#include <CFrameWnd.hpp>
#include <CNoTrackObject.hpp>
#include <CPoint.hpp>
#include <CPushRoutingFrame.hpp>
#include <CPushRoutingView.hpp>
#include <CView.hpp>
#include <CWnd.hpp>
#include <HHOOK__.hpp>
#include <HMENU__.hpp>
#include <HWND__.hpp>
#include <tagMSG.hpp>


START_ATF_NAMESPACE
    struct  _AFX_THREAD_STATE : CNoTrackObject
    {
        AFX_MODULE_STATE *m_pModuleState;
        AFX_MODULE_STATE *m_pPrevModuleState;
        void *m_pSafetyPoolBuffer;
        AFX_EXCEPTION_CONTEXT m_exceptionContext;
        CWnd *m_pWndInit;
        CWnd *m_pAlternateWndInit;
        unsigned int m_dwPropStyle;
        unsigned int m_dwPropExStyle;
        HWND__ *m_hWndInit;
        HHOOK__ *m_hHookOldCbtFilter;
        HHOOK__ *m_hHookOldMsgFilter;
        tagMSG m_msgCur;
        CPoint m_ptCursorLast;
        unsigned int m_nMsgLast;
        int m_nDisablePumpCount;
        tagMSG m_lastSentMsg;
        HWND__ *m_hTrackingWindow;
        HMENU__ *m_hTrackingMenu;
        char m_szTempClassName[96];
        HWND__ *m_hLockoutNotifyWindow;
        int m_bInMsgFilter;
        CView *m_pRoutingView;
        CPushRoutingView *m_pPushRoutingView;
        CFrameWnd *m_pRoutingFrame;
        CPushRoutingFrame *m_pPushRoutingFrame;
        int m_bWaitForDataSource;
        CWnd *m_pWndPark;
        int m_nCtrlRef;
        int m_bNeedTerm;
    };
END_ATF_NAMESPACE

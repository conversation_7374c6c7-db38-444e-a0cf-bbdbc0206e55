// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_coll_point.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _coll_pointInitPoint2_ptr = void (WINAPIV*)(struct _coll_point*, struct CMapData*, float*, struct CRect*);
        using _coll_pointInitPoint2_clbk = void (WINAPIV*)(struct _coll_point*, struct CMapData*, float*, struct CRect*, _coll_pointInitPoint2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

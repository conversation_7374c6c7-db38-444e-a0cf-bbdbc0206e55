// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_OVERLAPPED.hpp>


START_ATF_NAMESPACE
    struct $6A435823DE2D5F9D1069AA43A5DA44DD
    {
        _OVERLAPPED *lpOverlapped;
        void (WINAPIV *lpfnCompletionProc)(unsigned int, unsigned int, _OVERLAPPED *, unsigned int);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_REGED_AVATOR_DB.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _REGED_AVATOR_DBInit2_ptr = void (WINAPIV*)(struct _REGED_AVATOR_DB*);
        using _REGED_AVATOR_DBInit2_clbk = void (WINAPIV*)(struct _REGED_AVATOR_DB*, _REGED_AVATOR_DBInit2_ptr);
        
        using _REGED_AVATOR_DBctor__REGED_AVATOR_DB4_ptr = void (WINAPIV*)(struct _REGED_AVATOR_DB*);
        using _REGED_AVATOR_DBctor__REGED_AVATOR_DB4_clbk = void (WINAPIV*)(struct _REGED_AVATOR_DB*, _REGED_AVATOR_DBctor__REGED_AVATOR_DB4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

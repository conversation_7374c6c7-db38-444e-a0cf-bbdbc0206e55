// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryCN.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryCNctor_CNationSettingFactoryCN2_ptr = void (WINAPIV*)(struct CNationSettingFactoryCN*);
        using CNationSettingFactoryCNctor_CNationSettingFactoryCN2_clbk = void (WINAPIV*)(struct CNationSettingFactoryCN*, CNationSettingFactoryCNctor_CNationSettingFactoryCN2_ptr);
        using CNationSettingFactoryCNCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryCN*, int, char*, bool);
        using CNationSettingFactoryCNCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryCN*, int, char*, bool, CNationSettingFactoryCNCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Worker.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using WorkerInitialize2_ptr = bool (WINAPIV*)(struct Worker*, int, int);
        using WorkerInitialize2_clbk = bool (WINAPIV*)(struct Worker*, int, int, WorkerInitialize2_ptr);
        using WorkerStart4_ptr = void (WINAPIV*)(struct Worker*);
        using WorkerStart4_clbk = void (WINAPIV*)(struct Worker*, WorkerStart4_ptr);
        using WorkerStop6_ptr = void (WINAPIV*)(struct Worker*);
        using WorkerStop6_clbk = void (WINAPIV*)(struct Worker*, WorkerStop6_ptr);
        
        using Workerctor_Worker8_ptr = void (WINAPIV*)(struct Worker*, char*, int, int);
        using Workerctor_Worker8_clbk = void (WINAPIV*)(struct Worker*, char*, int, int, Workerctor_Worker8_ptr);
        using Workers_loop13_ptr = void (WINAPIV*)(void*);
        using Workers_loop13_clbk = void (WINAPIV*)(void*, Workers_loop13_ptr);
        
        using Workerdtor_Worker15_ptr = void (WINAPIV*)(struct Worker*);
        using Workerdtor_Worker15_clbk = void (WINAPIV*)(struct Worker*, Workerdtor_Worker15_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

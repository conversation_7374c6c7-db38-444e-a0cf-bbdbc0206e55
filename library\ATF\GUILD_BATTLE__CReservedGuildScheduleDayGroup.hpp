// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CReservedGuildScheduleMapGroup.hpp>
#include <GUILD_BATTLE__CReservedGuildSchedulePage.hpp>
#include <_worlddb_guild_battle_reserved_schedule_info.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CReservedGuildScheduleDayGroup
        {
            unsigned int m_uiMapCnt;
            char m_byToday;
            char m_byTommorow;
            CReservedGuildScheduleMapGroup *m_pkList;
        public:
            CReservedGuildScheduleDayGroup();
            void ctor_CReservedGuildScheduleDayGroup();
            bool Clear();
            struct CReservedGuildSchedulePage* Find(unsigned int dwGuildSerial);
            void Flip();
            bool Init(unsigned int uiMapCnt);
            bool Load(char byDayID, unsigned int uiMapInx, struct _worlddb_guild_battle_reserved_schedule_info* kInfo);
            void Send(char byDayID, unsigned int uiMapID, int n, unsigned int dwVer, char byPage, unsigned int dwGuildSerial);
            ~CReservedGuildScheduleDayGroup();
            void dtor_CReservedGuildScheduleDayGroup();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$3E60C3DACEF2E33FF1D1871D4F2565FA.hpp>
#include <CEntity.hpp>
#include <_PARTICLE_ELEMENT.hpp>


START_ATF_NAMESPACE
    struct CParticle
    {
        char mEntityName[128];
        int mNum;
        _PARTICLE_ELEMENT *mElement;
        CEntity *mEntity;
        float mTotalTime;
        float mLiveTime;
        float mStartTimeRange;
        float mTimeSpeed;
        float mGravity[3];
        float mStartPower[2][3];
        float mStartScale[2];
        float mStartZRot[2];
        float mStartYRot[2];
        char mFlickerAlpha;
        float mFlickerTime;
        float mStartARGB[4][2];
        float mOnePerTimeEpsilon;
        float mRotMat[4][4];
        $3E60C3DACEF2E33FF1D1871D4F2565FA ___u18;
        float mZFront;
        float mEmitTime;
        unsigned __int16 mSpecialID;
        unsigned __int16 mTrackCnt;
        float mTimeTrack[12];
        char mTrackFlag[12];
        char mATrack[12];
        char mRTrack[12][2];
        char mGTrack[12][2];
        char mBTrack[12][2];
        float mScaleTrack[12][2];
        float mZRotTrack[12][2];
        float mYRotTrack[12][2];
        float mPowerTrack[12][2][3];
        float mSpecialARGV[2][3];
        void *mBsp;
        unsigned int mFlag;
        unsigned int mAlphaType;
        float mStartPos[2][3];
        float mCreatePos[3];
        int mState;
        float mOnePerTime;
        float mOnePerTimeEpsilonTemp;
        float mParticleTimer;
        float mNextCreatorTime;
    public:
        void CheckCollision(int arg_0, float arg_1);
        void CopyParticleToSaveParticle(struct _SAVE_PARTICLE* arg_0);
        void CopySaveParticleToParticle(struct _SAVE_PARTICLE* arg_0);
        void GetBBox(float* arg_0, float* arg_1);
        void GetFlickerARGB(int arg_0, uint32_t* arg_1);
        void GetPartcleStep(int arg_0, float arg_1);
        uint32_t GetParticleState();
        void InitElement(int arg_0, float arg_1);
        void InitParticle();
        int64_t LoadParticleSPT(char* arg_0, uint32_t arg_1);
        int32_t Loop();
        void ReInitParticle(int arg_0);
        int32_t RealLoop();
        void ReleaseEntity();
        void ReleaseParticle();
        void ResetOnePerTime();
        void SetCreatePos(float* arg_0);
        void SetParticleState(uint32_t arg_0);
        void SetPreCalcParticle(uint32_t arg_0);
        void SetStartBoxArea();
        int32_t SpecialLoop();
        int32_t SpecialLoop2();
        ~CParticle();
        int64_t dtor_CParticle();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _PRINTER_ENUM_VALUESA
    {
        char *pValueName;
        unsigned int cbValueName;
        unsigned int dwType;
        char *pData;
        unsigned int cbData;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

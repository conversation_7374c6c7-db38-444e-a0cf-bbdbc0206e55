// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_insert_candidate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_insert_candidatector__qry_case_insert_candidate2_ptr = void (WINAPIV*)(struct _qry_case_insert_candidate*, char, uint16_t, unsigned int, unsigned int);
        using _qry_case_insert_candidatector__qry_case_insert_candidate2_clbk = void (WINAPIV*)(struct _qry_case_insert_candidate*, char, uint16_t, unsigned int, unsigned int, _qry_case_insert_candidatector__qry_case_insert_candidate2_ptr);
        using _qry_case_insert_candidatesize4_ptr = int (WINAPIV*)(struct _qry_case_insert_candidate*);
        using _qry_case_insert_candidatesize4_clbk = int (WINAPIV*)(struct _qry_case_insert_candidate*, _qry_case_insert_candidatesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

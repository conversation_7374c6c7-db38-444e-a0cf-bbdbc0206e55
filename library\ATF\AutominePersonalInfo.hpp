// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AutominePersonal.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using AutominePersonalctor_AutominePersonal2_ptr = void (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalctor_AutominePersonal2_clbk = void (WINAPIV*)(struct AutominePersonal*, AutominePersonalctor_AutominePersonal2_ptr);
        using AutominePersonalGetDefFC4_ptr = int (WINAPIV*)(struct AutominePersonal*, int, struct CCharacter*, int*);
        using AutominePersonalGetDefFC4_clbk = int (WINAPIV*)(struct AutominePersonal*, int, struct CCharacter*, int*, AutominePersonalGetDefFC4_ptr);
        using AutominePersonalGetDefFacing6_ptr = float (WINAPIV*)(struct AutominePersonal*, int);
        using AutominePersonalGetDefFacing6_clbk = float (WINAPIV*)(struct AutominePersonal*, int, AutominePersonalGetDefFacing6_ptr);
        using AutominePersonalGetDefGap8_ptr = float (WINAPIV*)(struct AutominePersonal*, int);
        using AutominePersonalGetDefGap8_clbk = float (WINAPIV*)(struct AutominePersonal*, int, AutominePersonalGetDefGap8_ptr);
        using AutominePersonalGetHP10_ptr = int (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalGetHP10_clbk = int (WINAPIV*)(struct AutominePersonal*, AutominePersonalGetHP10_ptr);
        using AutominePersonalGetMaxHP12_ptr = int (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalGetMaxHP12_clbk = int (WINAPIV*)(struct AutominePersonal*, AutominePersonalGetMaxHP12_ptr);
        using AutominePersonalGetObjRace14_ptr = int (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalGetObjRace14_clbk = int (WINAPIV*)(struct AutominePersonal*, AutominePersonalGetObjRace14_ptr);
        using AutominePersonalGetOwner16_ptr = struct CPlayer* (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalGetOwner16_clbk = struct CPlayer* (WINAPIV*)(struct AutominePersonal*, AutominePersonalGetOwner16_ptr);
        using AutominePersonalIsBeAttackedAble18_ptr = bool (WINAPIV*)(struct AutominePersonal*, bool);
        using AutominePersonalIsBeAttackedAble18_clbk = bool (WINAPIV*)(struct AutominePersonal*, bool, AutominePersonalIsBeAttackedAble18_ptr);
        using AutominePersonalIsBeDamagedAble20_ptr = bool (WINAPIV*)(struct AutominePersonal*, struct CCharacter*);
        using AutominePersonalIsBeDamagedAble20_clbk = bool (WINAPIV*)(struct AutominePersonal*, struct CCharacter*, AutominePersonalIsBeDamagedAble20_ptr);
        using AutominePersonalLoadDBComplete22_ptr = void (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalLoadDBComplete22_clbk = void (WINAPIV*)(struct AutominePersonal*, AutominePersonalLoadDBComplete22_ptr);
        using AutominePersonalLoop24_ptr = void (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalLoop24_clbk = void (WINAPIV*)(struct AutominePersonal*, AutominePersonalLoop24_ptr);
        using AutominePersonalSendMsg_FixPosition26_ptr = void (WINAPIV*)(struct AutominePersonal*, int);
        using AutominePersonalSendMsg_FixPosition26_clbk = void (WINAPIV*)(struct AutominePersonal*, int, AutominePersonalSendMsg_FixPosition26_ptr);
        using AutominePersonalSetDamage28_ptr = int (WINAPIV*)(struct AutominePersonal*, int, struct CCharacter*, int, bool, int, unsigned int, bool);
        using AutominePersonalSetDamage28_clbk = int (WINAPIV*)(struct AutominePersonal*, int, struct CCharacter*, int, bool, int, unsigned int, bool, AutominePersonalSetDamage28_ptr);
        using AutominePersonaldo_automine34_ptr = bool (WINAPIV*)(struct AutominePersonal*, unsigned int);
        using AutominePersonaldo_automine34_clbk = bool (WINAPIV*)(struct AutominePersonal*, unsigned int, AutominePersonaldo_automine34_ptr);
        using AutominePersonalextract_battery36_ptr = bool (WINAPIV*)(struct AutominePersonal*, char, struct _STORAGE_LIST::_db_con*);
        using AutominePersonalextract_battery36_clbk = bool (WINAPIV*)(struct AutominePersonal*, char, struct _STORAGE_LIST::_db_con*, AutominePersonalextract_battery36_ptr);
        using AutominePersonalget_battery38_ptr = unsigned int (WINAPIV*)(struct AutominePersonal*, int);
        using AutominePersonalget_battery38_clbk = unsigned int (WINAPIV*)(struct AutominePersonal*, int, AutominePersonalget_battery38_ptr);
        using AutominePersonalget_battery40_ptr = unsigned int (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalget_battery40_clbk = unsigned int (WINAPIV*)(struct AutominePersonal*, AutominePersonalget_battery40_ptr);
        using AutominePersonalget_item42_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalget_item42_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct AutominePersonal*, AutominePersonalget_item42_ptr);
        using AutominePersonalget_itemserial44_ptr = uint16_t (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalget_itemserial44_clbk = uint16_t (WINAPIV*)(struct AutominePersonal*, AutominePersonalget_itemserial44_ptr);
        using AutominePersonalget_objserial46_ptr = unsigned int (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalget_objserial46_clbk = unsigned int (WINAPIV*)(struct AutominePersonal*, AutominePersonalget_objserial46_ptr);
        using AutominePersonalget_owner48_ptr = struct CPlayer* (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalget_owner48_clbk = struct CPlayer* (WINAPIV*)(struct AutominePersonal*, AutominePersonalget_owner48_ptr);
        using AutominePersonalget_ownerserial50_ptr = unsigned int (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalget_ownerserial50_clbk = unsigned int (WINAPIV*)(struct AutominePersonal*, AutominePersonalget_ownerserial50_ptr);
        using AutominePersonalinitialize52_ptr = bool (WINAPIV*)(struct AutominePersonal*, uint16_t);
        using AutominePersonalinitialize52_clbk = bool (WINAPIV*)(struct AutominePersonal*, uint16_t, AutominePersonalinitialize52_ptr);
        using AutominePersonalinsert_battery54_ptr = bool (WINAPIV*)(struct AutominePersonal*, char, uint16_t);
        using AutominePersonalinsert_battery54_clbk = bool (WINAPIV*)(struct AutominePersonal*, char, uint16_t, AutominePersonalinsert_battery54_ptr);
        using AutominePersonalis_installed56_ptr = bool (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalis_installed56_clbk = bool (WINAPIV*)(struct AutominePersonal*, AutominePersonalis_installed56_ptr);
        using AutominePersonalis_run58_ptr = bool (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalis_run58_clbk = bool (WINAPIV*)(struct AutominePersonal*, AutominePersonalis_run58_ptr);
        using AutominePersonalmake_minepacket60_ptr = void (WINAPIV*)(struct AutominePersonal*, uint16_t, uint16_t, char, uint16_t, unsigned int);
        using AutominePersonalmake_minepacket60_clbk = void (WINAPIV*)(struct AutominePersonal*, uint16_t, uint16_t, char, uint16_t, unsigned int, AutominePersonalmake_minepacket60_ptr);
        using AutominePersonalregist_to_map62_ptr = bool (WINAPIV*)(struct AutominePersonal*, struct CPlayer*, struct _STORAGE_LIST::_db_con*, char, unsigned int, float);
        using AutominePersonalregist_to_map62_clbk = bool (WINAPIV*)(struct AutominePersonal*, struct CPlayer*, struct _STORAGE_LIST::_db_con*, char, unsigned int, float, AutominePersonalregist_to_map62_ptr);
        using AutominePersonalsend_attacked64_ptr = void (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalsend_attacked64_clbk = void (WINAPIV*)(struct AutominePersonal*, AutominePersonalsend_attacked64_ptr);
        using AutominePersonalsend_changed_packet66_ptr = void (WINAPIV*)(struct AutominePersonal*, int);
        using AutominePersonalsend_changed_packet66_clbk = void (WINAPIV*)(struct AutominePersonal*, int, AutominePersonalsend_changed_packet66_ptr);
        using AutominePersonalsend_current_state68_ptr = void (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalsend_current_state68_clbk = void (WINAPIV*)(struct AutominePersonal*, AutominePersonalsend_current_state68_ptr);
        using AutominePersonalsend_ecode70_ptr = void (WINAPIV*)(struct AutominePersonal*, char);
        using AutominePersonalsend_ecode70_clbk = void (WINAPIV*)(struct AutominePersonal*, char, AutominePersonalsend_ecode70_ptr);
        using AutominePersonalsend_installed72_ptr = void (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalsend_installed72_clbk = void (WINAPIV*)(struct AutominePersonal*, AutominePersonalsend_installed72_ptr);
        using AutominePersonalset_delay74_ptr = void (WINAPIV*)(struct AutominePersonal*, unsigned int);
        using AutominePersonalset_delay74_clbk = void (WINAPIV*)(struct AutominePersonal*, unsigned int, AutominePersonalset_delay74_ptr);
        using AutominePersonalset_delaysec76_ptr = void (WINAPIV*)(struct AutominePersonal*, unsigned int);
        using AutominePersonalset_delaysec76_clbk = void (WINAPIV*)(struct AutominePersonal*, unsigned int, AutominePersonalset_delaysec76_ptr);
        using AutominePersonalset_openUI_Inven78_ptr = void (WINAPIV*)(struct AutominePersonal*, bool);
        using AutominePersonalset_openUI_Inven78_clbk = void (WINAPIV*)(struct AutominePersonal*, bool, AutominePersonalset_openUI_Inven78_ptr);
        using AutominePersonalset_openUI_battery80_ptr = void (WINAPIV*)(struct AutominePersonal*, bool);
        using AutominePersonalset_openUI_battery80_clbk = void (WINAPIV*)(struct AutominePersonal*, bool, AutominePersonalset_openUI_battery80_ptr);
        using AutominePersonalset_selore82_ptr = void (WINAPIV*)(struct AutominePersonal*, char);
        using AutominePersonalset_selore82_clbk = void (WINAPIV*)(struct AutominePersonal*, char, AutominePersonalset_selore82_ptr);
        using AutominePersonalset_work84_ptr = void (WINAPIV*)(struct AutominePersonal*, bool);
        using AutominePersonalset_work84_clbk = void (WINAPIV*)(struct AutominePersonal*, bool, AutominePersonalset_work84_ptr);
        using AutominePersonalsub_battery86_ptr = char (WINAPIV*)(struct AutominePersonal*, unsigned int);
        using AutominePersonalsub_battery86_clbk = char (WINAPIV*)(struct AutominePersonal*, unsigned int, AutominePersonalsub_battery86_ptr);
        using AutominePersonalsub_filledslot88_ptr = void (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonalsub_filledslot88_clbk = void (WINAPIV*)(struct AutominePersonal*, AutominePersonalsub_filledslot88_ptr);
        using AutominePersonalunregist_from_map90_ptr = bool (WINAPIV*)(struct AutominePersonal*, char);
        using AutominePersonalunregist_from_map90_clbk = bool (WINAPIV*)(struct AutominePersonal*, char, AutominePersonalunregist_from_map90_ptr);
        
        using AutominePersonaldtor_AutominePersonal92_ptr = void (WINAPIV*)(struct AutominePersonal*);
        using AutominePersonaldtor_AutominePersonal92_clbk = void (WINAPIV*)(struct AutominePersonal*, AutominePersonaldtor_AutominePersonal92_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

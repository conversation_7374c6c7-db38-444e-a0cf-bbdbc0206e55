// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_apex_send_login.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _apex_send_loginsize2_ptr = int (WINAPIV*)(struct _apex_send_login*);
        using _apex_send_loginsize2_clbk = int (WINAPIV*)(struct _apex_send_login*, _apex_send_loginsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

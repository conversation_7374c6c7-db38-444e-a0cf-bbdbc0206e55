// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <EmotionPresentationChecker.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using EmotionPresentationCheckerCheckEmotionState2_ptr = bool (WINAPIV*)(struct EmotionPresentationChecker*, struct CMonster*, char, struct CCharacter*);
        using EmotionPresentationCheckerCheckEmotionState2_clbk = bool (WINAPIV*)(struct EmotionPresentationChecker*, struct CMonster*, char, struct CCharacter*, EmotionPresentationCheckerCheckEmotionState2_ptr);
        
        using EmotionPresentationCheckerctor_EmotionPresentationChecker4_ptr = void (WINAPIV*)(struct EmotionPresentationChecker*);
        using EmotionPresentationCheckerctor_EmotionPresentationChecker4_clbk = void (WINAPIV*)(struct EmotionPresentationChecker*, EmotionPresentationCheckerctor_EmotionPresentationChecker4_ptr);
        using EmotionPresentationCheckerReSet6_ptr = void (WINAPIV*)(struct EmotionPresentationChecker*);
        using EmotionPresentationCheckerReSet6_clbk = void (WINAPIV*)(struct EmotionPresentationChecker*, EmotionPresentationCheckerReSet6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonsterEventSetVtbl.hpp>
#include <CPlayer.hpp>
#include <_FILETIME.hpp>
#include <_event_set.hpp>
#include <_event_set_looting.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMonsterEventSet
    {
        CMonsterEventSetVtbl *vfptr;
        _event_set m_EventSet[10];
        _event_set_looting m_EventSetLootingList[100];
        _FILETIME m_ftWrite;
        _FILETIME m_ftLootingWrite;
        bool m_bLoadEventLooting;
    public:
        CMonsterEventSet();
        void ctor_CMonsterEventSet();
        void CheckEventSetRespawn();
        struct _event_set* GetEmptyEventSet();
        struct _event_set_looting* GetEvenSetLooting(char* pszCode);
        struct _event_set::_monster_set* GetMonsterSet(struct _event_set* pEventSet);
        bool IsINIFileChanged(char* pszFileName, struct _FILETIME ftCurr);
        bool LoadEventSet(char* pwszErrCode);
        bool LoadEventSetLooting();
        bool StartEventSet(char* pszEventCode, char* pwszErrCode, struct CPlayer* pOne);
        bool StopEventSet(char* pszEventCode, char* pwszErrCode);
        ~CMonsterEventSet();
        void dtor_CMonsterEventSet();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

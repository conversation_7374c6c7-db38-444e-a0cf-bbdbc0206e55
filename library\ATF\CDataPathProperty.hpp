// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CAsyncMonikerFile.hpp>
#include <COleControl.hpp>


START_ATF_NAMESPACE
    struct  CDataPathProperty : CAsyncMonikerFile
    {
        COleControl *m_pControl;
        ATL::CStringT<char> m_strPath;
    };
END_ATF_NAMESPACE

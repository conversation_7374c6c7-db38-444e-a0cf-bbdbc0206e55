// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_src_guild_out_guildbattlecost
    {
        unsigned int dwGuildIndex;
        unsigned int dwGuildSerial;
        unsigned int dwSubGold;
        unsigned int dwSubDalant;
        char byDate[4];
        long double out_totalgold;
        long double out_totaldalant;
        unsigned int dwSrcGuildSerial;
        unsigned int dwStartTimeInx;
        unsigned int dwMemberCntInx;
        unsigned int dwMapInx;
        char byProcRet;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_src_guild_out_guildbattlecost, 64>(), "_qry_case_src_guild_out_guildbattlecost");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <D3DXVECTOR2.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using D3DXVECTOR2ctor_D3DXVECTOR22_ptr = void (WINAPIV*)(struct D3DXVECTOR2*);
        using D3DXVECTOR2ctor_D3DXVECTOR22_clbk = void (WINAPIV*)(struct D3DXVECTOR2*, D3DXVECTOR2ctor_D3DXVECTOR22_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

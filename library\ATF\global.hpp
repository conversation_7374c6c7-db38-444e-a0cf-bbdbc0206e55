// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAnimus.hpp>
#include <CCharacter.hpp>
#include <CDarkHole.hpp>
#include <CDarkHoleDungeonQuestSetup.hpp>
#include <CGameObject.hpp>
#include <CGuardTower.hpp>
#include <CGuild.hpp>
#include <CHEAT_COMMAND.hpp>
#include <CItemBox.hpp>
#include <CItemStore.hpp>
#include <CLuaEventMgr.hpp>
#include <CMapData.hpp>
#include <CMerchant.hpp>
#include <CMonster.hpp>
#include <CNationCodeStr.hpp>
#include <CParkingUnit.hpp>
#include <CPlayer.hpp>
#include <CRecordData.hpp>
#include <CTrap.hpp>
#include <CUserDB.hpp>
#include <D3DXVECTOR2.hpp>
#include <HICON.hpp>
#include <HINSTANCE.hpp>
#include <HINSTANCE__.hpp>
#include <HKEY__.hpp>
#include <HWND.hpp>
#include <ImgDelayDescr.hpp>
#include <LuaParam3.hpp>
#include <TIMER_COMMAND.hpp>
#include <_CLID.hpp>
#include <_CheckPotion_fld.hpp>
#include <_EQUIP_MASTERY_LIM.hpp>
#include <_EXCEPTION_POINTERS.hpp>
#include <_FILETIME.hpp>
#include <_GUID.hpp>
#include <_IMAGE_IMPORT_BY_NAME.hpp>
#include <_IMAGE_THUNK_DATA64.hpp>
#include <_ItemCombine_exp_fld.hpp>
#include <_RTC_ALLOCA_NODE.hpp>
#include <_STORAGE_LIST.hpp>
#include <_TrapItem_fld.hpp>
#include <_WA_AVATOR_CODE.hpp>
#include <_animus_fld.hpp>
#include <_base_fld.hpp>
#include <_class_fld.hpp>
#include <_combine_ex_item_request_clzo.hpp>
#include <_combine_ex_item_result_zocl.hpp>
#include <_dummy_position.hpp>
#include <_economy_calc_data.hpp>
#include <_economy_history_data.hpp>
#include <_mon_active.hpp>
#include <_react_area.hpp>
#include <_react_obj.hpp>
#include <_sf_continous.hpp>
#include <_skill_fld.hpp>
#include <_trand_tbl.hpp>
#include <sockaddr_in.hpp>
#include <sockaddr_ipx.hpp>
#include <std__vector.hpp>
#include <strFILE.hpp>
#include <TIMER_COMMAND.hpp>
#include <std__vector.hpp>
#include <std__basic_string.hpp>
#include <_D3DFORMAT.hpp>
#include <_D3DMATERIAL8.hpp>
#include <_D3DLIGHTTYPE.hpp>
#include <GlobalNames.hpp>


START_ATF_NAMESPACE
    namespace Global
    {
        int64_t ARGBToJpegFile(char* arg_0, uint8_t* arg_1, unsigned int arg_2, unsigned int arg_3, unsigned int arg_4, int arg_5, int arg_6);
        int AddEnvVariable(char* lpName, char* lpValue, int nValueLen);
        void AdjustIndependenceR3M(struct _R3MATERIAL* arg_0, int32_t arg_1, int32_t arg_2);
        void AfterRenderOneLayer(struct CVertexBuffer* arg_0, struct _BSP_MAT_GROUP* arg_1);
        void AfterRenderSetting(int arg_0, struct CVertexBuffer* arg_1, struct _BSP_MAT_GROUP* arg_2);
        int64_t AfxAssertFailedLine(char* arg_0, int arg_1);
        int AfxCrtErrorCheck(int error);
        void AfxEnableControlContainer(struct COccManager* arg_0);
        HINSTANCE AfxGetInstanceHandle();
        struct AFX_MODULE_STATE* AfxGetModuleState();
        int AfxInitialize(int bDLL, unsigned int dwVersion);
        void AfxThrowInvalidArgException();
        void AfxThrowMemoryException();
        void AfxThrowOleException(int32_t arg_0);
        int64_t AfxWinMain(HINSTANCE arg_0, HINSTANCE arg_1, char* arg_2, int arg_3);
        wchar_t* AtlA2WHelper(wchar_t* lpw, char* lpa, int nChars, unsigned int acp);
        char* AtlW2AHelper(char* lpa, wchar_t* lpw, int nChars, unsigned int acp);
        void AtoH(char* src, char* dest, int destlen);
        bool AuthorityFilter(struct CHEAT_COMMAND* pCmd, struct CPlayer* pOne);
        int64_t BGRFromRGB(uint8_t* arg_0, unsigned int arg_1, unsigned int arg_2);
        void BlendOff();
        void BlendOn(int arg_0);
        void BlurFilterShader(uint32_t arg_0, struct CTextureRender* arg_1, struct CTextureRender* arg_2);
        void BlurFilterSprite(struct CTextureRender* _this, uint32_t arg_0, void* arg_1);
        int32_t BlurShaderVSPS(uint32_t arg_0, struct CTextureRender* arg_1, struct CTextureRender* arg_2);
        char BtoH(char ch);
        void ByteSortForShort(uint32_t arg_0, uint32_t* arg_1, int16_t* arg_2, uint32_t* arg_3, int16_t* arg_4);
        void CN_CalculateSunAndFieldColor(float arg_0);
        float CN_GetAccselateTime();
        struct Atmosphere* CN_GetAtmosphere();
        void CN_GetDayTime(uint32_t* arg_0, uint32_t* arg_1, uint32_t* arg_2);
        float CN_GetDayTime();
        uint32_t CN_GetFieldColor();
        uint32_t CN_GetFogColor(float* arg_0);
        void CN_GetRealDayTime(uint32_t* arg_0, uint32_t* arg_1, uint32_t* arg_2);
        float CN_GetRealDayTime();
        struct Sky* CN_GetSky();
        struct Sun* CN_GetSun();
        uint32_t CN_GetSunColor();
        void CN_GetSunDirection(float* arg_0);
        float CN_GetWeather();
        void CN_InvalidateNature();
        int64_t CN_IsEnableSky();
        uint32_t CN_MixDayColor(uint32_t arg_0);
        void CN_NatureFrameMove();
        void CN_RenderSky();
        void CN_RestoreNature();
        void CN_SetAccselateTime(float arg_0);
        void CN_SetDayTime(float arg_0);
        void CN_SetDayTime(uint32_t arg_0, uint32_t arg_1, uint32_t arg_2);
        void CN_SetEnableSky(int arg_0);
        void CN_SetRealDayTime(float arg_0);
        void CN_SetRealDayTime(uint32_t arg_0, uint32_t arg_1, uint32_t arg_2);
        void CN_SetWeather(float arg_0);
        void CN_SkyVertexShaderConstants();
        float CalcBi_n(int arg_0, int arg_1, double arg_2);
        unsigned int* CalcCodeKey(unsigned int* pdwCode);
        void CalcCubicCurve(float** arg_0, int arg_1, float* arg_2);
        float CalcEvalCubicCurve(float* arg_0, float arg_1);
        int CalcFileSize(char* pszFileName);
        int CalcMastery(int nMasteryCode, int nMasteryIndex, unsigned int dwMasteryCum, int nRaceCode);
        int CalcRoundUp(float fVal);
        uint32_t CalcSnakeVertexList(struct _D3DR3VERTEX_TEX1* arg_0, float** arg_1, uint32_t arg_2, float arg_3, uint32_t arg_4);
        int CalcSquare(int nLoot, int nMulti);
        void CalculateMoveCamera(struct _MOVE_CAMERA* arg_0);
        bool CanAddMoneyForMaxLimGold(uint64_t ui64AddGold, uint64_t ui64HasGold);
        bool CanAddMoneyForMaxLimMoney(uint64_t ui64AddMoney, uint64_t ui64HasMoney);
        int CcrFgCallback(int lCallbackCode, void* hUserContext, void* pCallbackParameter, int nParameterSize, void* pReservedParameter);
        int64_t CheckEdge(float* arg_0, float* arg_1, float* arg_2, float* arg_3, float arg_4);
        int64_t CheckEdgeEpsilon(float* arg_0, float* arg_1, float* arg_2, float* arg_3, float arg_4);
        bool CheckSameItemFromString_CodeIndex(char* psItemCode, char byTableCode, uint16_t wIndex);
        void Clean2DRectangleZbuffer(int32_t arg_0, int32_t arg_1, int32_t arg_2, int32_t arg_3);
        void CleanViewPortStack();
        void CleanZbuffer(float arg_0, float arg_1, float arg_2, float arg_3);
        void ClearDynamicLight();
        unsigned int CombineExCheckKeyGen(unsigned int dwtimeGetTime, unsigned int dwCombineExcelRecordIndex);
        int CompareGradeAndPvpPoint(void* arg1, void* arg2);
        void ConvAniObject(int arg_0, uint8_t* arg_1, struct _READ_ANI_OBJECT* arg_2, struct _ANI_OBJECT* arg_3);
        struct _STORAGE_LIST::_db_con* ConvertCodeIntoItem(char* pszItemCode, char byOverlapNum, char bySocketConfig);
        int ConvertErrorCode_Jap(char state);
        uint32_t ConvertHexa(char* arg_0);
        float CosineInterpolate(float arg_0, float arg_1, float arg_2);
        unsigned int CountOfImports(struct _IMAGE_THUNK_DATA64* pitdBase);
        void CreateAndWriteUVOffsets(int arg_0, int arg_1);
        bool CreateAnimus(struct CMapData* pMap, uint16_t wLayer, float* fPos, char byClass, int nHP, int nFP, unsigned int dwExp, struct CPlayer* pMaster);
        void CreateBlurVBuffer(uint32_t arg_0, uint32_t arg_1);
        struct CGuardTower* CreateGuardTower(struct CMapData* pMap, uint16_t wLayer, float* fPos, struct _STORAGE_LIST::_db_con* pItem, struct CPlayer* pMaster, char byRaceCode, bool bQuick);
        struct CItemBox* CreateItemBox(struct _STORAGE_LIST::_db_con* pItem, struct CPlayer* pOwner, unsigned int dwPartyBossSerial, bool bPartyShare, struct CCharacter* pThrower, char byCreateCode, struct CMapData* pMap, uint16_t wLayerIndex, float* pStdPos, bool bHide);
        struct CItemBox* CreateItemBox(struct _STORAGE_LIST::_db_con* pItem, char byCreateCode, struct CMapData* pMap, uint16_t wLayerIndex, float* pStdPos, bool bHide, struct CPlayer* pAttacker, int bHolyScanner, char byEventItemLootAuth);
        struct CMonster* CreateRepMonster(struct CMapData* pMap, uint16_t wLayer, float* fPos, char* pszMonsterCode, struct CMonster* pParent, bool bRobExp, bool bRewardExp, bool bDungeon, bool bWithoutFail, bool bApplyRopExpField);
        struct CMonster* CreateRespawnMonster(struct CMapData* pMap, uint16_t wLayer, int nMonsterIndex, struct _mon_active* pActiveRec, struct _dummy_position* pDumPosition, bool bRobExp, bool bRewardExp, bool bDungeon, bool bWithoutFail, bool bApplyRopExpField);
        struct CGuardTower* CreateSystemTower(struct CMapData* pMap, uint16_t wLayer, float* fPos, int nTowerIndex, char byRaceCode, int nIniIndex);
        struct CTrap* CreateTrap(struct CMapData* pMap, uint16_t wLayer, float* fPos, struct CPlayer* pMaster, int nTrapItemIndex);
        void CrossVector(float* arg_0, float* arg_1, float* arg_2);
        float CubicInterpolate(float arg_0, float arg_1, float arg_2, float arg_3, float arg_4);
        void DDX_Control(struct CDataExchange* arg_0, int arg_1, struct CWnd* arg_2);
        void DDX_Text(struct CDataExchange* arg_0, int arg_1, int* arg_2);
        void DDX_Text(struct CDataExchange* arg_0, int arg_1, unsigned int* arg_2);
        bool DE_AllContDamageForceRemove(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_AllContHelpForceRemove(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_AllContHelpSkillRemove(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_AttHPtoDstFP(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_BattleMode_RecallCommonPlayer(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_ContDamageTimeInc(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_ContHelpTimeInc(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_ConvertMonsterTarget(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_ConvertTargetDest(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_DamStun(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_DetectTrap(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_FPDec(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_HPInc(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_IncHPCircleParty(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_IncreaseDP(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_LateContDamageRemove(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_LateContHelpForceRemove(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_LateContHelpSkillRemove(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_LayTrap(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_MakeGuardTower(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_MakePortalReturnBindPositionPartyMember(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_MakeZeroAnimusRecallTimeOnce(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_OthersContHelpSFRemove(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_OverHealing(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_AllContHelpSkillRemove_Once(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Buf_Extend(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Chaos_Dec_Time(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Chaos_Inc_Time(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_CharReName(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Class_Refine(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Cont_Damage_Remove(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_DecHalfSFContDam(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Exp_Increase_Absolute(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Exp_Increase_Percentage(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_FP_In_Value(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Gold_Point(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_HFP_Full_Recover(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_HP_In_Value(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Race_Debuff_Clear_One(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Race_Debuff_Clear_Two(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_RemoveAfterEffect(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_RemoveAllContinousEffect(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Revival_Die_Position(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_SP_In_Value(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Potion_Trunk_Extend(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Quick_Revival_Die_Position(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_RecallCommonPlayer(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_RecallPartyMember(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Recall_After_Stone(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_RecoverAllReturnStateAnimusHPFull(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Recovery(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_ReleaseMonsterTarget(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_RemoveAllContHelp(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_ReturnBindPosition(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_SPDec(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_STInc(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_SelfDestruction(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_SkillContHelpTimeInc(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Stun(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_TeleportCommonPlayer(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_Teleport_After_Stone(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_TransDestHP(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_TransMonsterHP(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DE_ViewWeakPoint(struct CCharacter* pActChar, struct CCharacter* pTargetChar, float fEffectValue, char* byRet);
        bool DTradeEqualPerson(struct CPlayer* lp_pOne, struct CPlayer** lpp_pDst);
        void DXUtil_ConvertAnsiStringToGeneric(char* tstrDestination, char* strSource, int cchDestChar);
        void DXUtil_ConvertAnsiStringToWide(wchar_t* wstrDestination, char* strSource, int cchDestChar);
        void DXUtil_ConvertGenericStringToAnsi(char* strDestination, char* tstrSource, int cchDestChar);
        void DXUtil_ConvertGenericStringToWide(wchar_t* wstrDestination, char* tstrSource, int cchDestChar);
        void DXUtil_ConvertWideStringToAnsi(char* strDestination, wchar_t* wstrSource, int cchDestChar);
        void DXUtil_ConvertWideStringToGeneric(char* tstrDestination, wchar_t* wstrSource, int cchDestChar);
        HRESULT DXUtil_FindMediaFile(char* strPath, char* strFilename);
        char* DXUtil_GetDXSDKMediaPath();
        HRESULT DXUtil_ReadBoolRegKey(struct HKEY__* hKey, char* strRegName, int* pbValue, int bDefault);
        HRESULT DXUtil_ReadGuidRegKey(struct HKEY__* hKey, char* strRegName, struct _GUID* pGuidValue, struct _GUID* guidDefault);
        HRESULT DXUtil_ReadIntRegKey(struct HKEY__* hKey, char* strRegName, unsigned int* pdwValue, unsigned int dwDefault);
        HRESULT DXUtil_ReadStringRegKey(struct HKEY__* hKey, char* strRegName, char* strValue, unsigned int dwLength, char* strDefault);
        float DXUtil_Timer(TIMER_COMMAND command);
        void DXUtil_Trace(char* strMsg);
        HRESULT DXUtil_WriteBoolRegKey(struct HKEY__* hKey, char* strRegName, int bValue);
        HRESULT DXUtil_WriteGuidRegKey(struct HKEY__* hKey, char* strRegName, struct _GUID guidValue);
        HRESULT DXUtil_WriteIntRegKey(struct HKEY__* hKey, char* strRegName, unsigned int dwValue);
        HRESULT DXUtil_WriteStringRegKey(struct HKEY__* hKey, char* strRegName, char* strValue);
        void DeCryptString(char* pStr, int nSize, char byPlus, uint16_t wCryptKey);
        void DeCrypt_Move(char* pStr, int nSize, char byPlus, uint16_t wCryptKey);
        void DebugDrawIndexedPrimitiveTLTex1(int arg_0, int arg_1, uint16_t* arg_2, void* arg_3);
        void DebugDrawIndexedPrimitiveUPTex1(int arg_0, int arg_1, uint16_t* arg_2, void* arg_3);
        void DebugPushEntityNum(uint32_t arg_0);
        void DebugPushMagicNum(uint32_t arg_0);
        void DetailTextureOffStage2();
        void DetailTextureOnStage2(struct _R3MATERIAL* arg_0);
        void Dfree(void* arg_0);
        void DisplayANSICodePageStrOutputDebug();
        char* DisplayItemUpgInfo(int nTableCode, unsigned int dwLvBit);
        void* Dmalloc(int arg_0);
        float DotProduct(float* arg_0, float* arg_1);
        void EnCryptString(char* pStr, int nSize, char byPlus, uint16_t wCryptKey);
        void EnCrypt_Move(char* pStr, int nSize, char byPlus, uint16_t wCryptKey);
        void Error(char* arg_0, char* arg_1);
        void ErrorButRun(char* arg_0, char* arg_1);
        void ExtractVertex(uint16_t arg_0, int arg_1, float** arg_2, void* arg_3, float* arg_4, float arg_5);
        unsigned int F(unsigned int x, unsigned int y, unsigned int z);
        uint32_t F2DW(float arg_0);
        void FastBBoShasiToFrameBuffer(struct CLevel* arg_0, uint32_t arg_1);
        int64_t FastCmp(uint32_t* arg_0, uint32_t* arg_1, uint32_t arg_2);
        void FatalError(char* arg_0, char* arg_1);
        int FindAllFile(char* pszDirectory, char** ppszFileName, int nMax);
        struct CAnimus* FindEmptyAnimus(struct CAnimus* pObjArray, int nMax);
        struct CMerchant* FindEmptyNPC(struct CMerchant* pList, int nMax);
        struct CParkingUnit* FindEmptyParkingUnit(struct CParkingUnit* pItem, int nMax);
        int FixTalikItemIndex(char byTalikEffectNum);
        void FloatToShort(float* pFloat, int16_t* pShort, int size);
        void Force32BitRendering();
        void ForceFullScreen();
        void FramebufferToBMP(char* Filename);
        void FramebufferToJPG(char* arg_0);
        void FreePointer(void* arg_0);
        uint32_t FtoDW(float arg_0);
        void FullScreenEffect();
        unsigned int G(unsigned int x, unsigned int y, unsigned int z);
        struct CTextureRender* Get1st1024x1024TexRender();
        struct CTextureRender* Get1st256x256TexRender();
        struct CTextureRender* Get1st512x512TexRender();
        void Get2DTo3DTranslation(float** arg_0, float* arg_1, float* arg_2, float arg_3, float arg_4, float arg_5, float arg_6, float arg_7);
        struct CTextureRender* Get2nd256x256TexRender();
        struct CTextureRender* Get2nd512x512TexRender();
        float Get3DSqrt(float* Pos, float* Tar);
        void GetAddrString(struct sockaddr_ipx* pSAddr, char* dest);
        uint32_t GetAllPlayingWaves();
        float GetAngle(float* mon, float* plr);
        void GetAniMatrix(float** arg_0, struct _ANI_OBJECT* arg_1, float arg_2);
        struct _animus_fld* GetAnimusFldFromExp(int nAnimusClass, uint64_t dwExp);
        struct _animus_fld* GetAnimusFldFromLv(int nAnimusClass, unsigned int dwLv);
        uint32_t GetAvailableVidMem();
        void GetBBoxRotate(float** arg_0, float** arg_1, float* arg_2, float* arg_3, float arg_4);
        void GetBezierPoint(float** arg_0, float** arg_1, uint32_t arg_2, float arg_3);
        void GetBillboardMatrix(struct D3DXMATRIX* arg_0, float* arg_1, float* arg_2);
        unsigned int GetBitAfterDowngrade(unsigned int dwCurBit, char byCurLv);
        unsigned int GetBitAfterSetLimSocket(char byLimSocketNum);
        unsigned int GetBitAfterUpgrade(unsigned int dwCurBit, unsigned int dwTalikCode, char byCurLv);
        int32_t GetBoldSubLeng(char* arg_0);
        void GetBumpFactor(float* arg_0);
        void GetCameraPos(float** arg_0);
        int64_t GetCharFromKey();
        unsigned int GetCheckTimeFromCombineExCheckKey(unsigned int dwCombineExCheckKey);
        uint32_t GetColorToColorAlpha(uint32_t arg_0, uint32_t arg_1, float arg_2);
        unsigned int GetConnectTime_AddBySec(int iSec);
        int64_t GetConsoleStateB(int arg_0);
        void GetContrast(uint8_t* arg_0);
        int GetCurDay();
        int GetCurHour();
        int GetCurrentDay();
        int GetCurrentHour();
        int GetCurrentMin();
        int GetCurrentMonth();
        int GetCurrentSec();
        int GetCurrentYear();
        int GetCurwDay();
        struct IDirect3DTexture8* GetD3DTexture(uint16_t arg_0, uint16_t arg_1, _D3DFORMAT arg_2, uint16_t arg_3, uint16_t arg_4, _D3DFORMAT arg_5, uint8_t* arg_6, uint32_t arg_7);
        struct IDirect3DTexture8* GetD3DTextureFromBuffer(uint8_t* arg_0, uint32_t arg_1, uint32_t arg_2);
        struct IDirect3DDevice8* GetD3dDevice();
        void* GetDDSTexFromBuffer(uint16_t arg_0, uint16_t arg_1, uint32_t arg_2, uint8_t* arg_3);
        bool GetDateStrAfterDay(char* szDate, int iBuffSize, uint16_t wDayAfter);
        bool GetDateTimeStr(char* szTime);
        char GetDefItemUpgSocketNum(int nTableCode, int nItemIndex);
        float GetDensityFromPos(float* arg_0);
        void GetDirection(float** cur, float** tar, float** out, float deg);
        float GetDist(float* arg_0, float* arg_1);
        uint32_t GetDmallocCnt();
        int64_t GetDmallocSize();
        float GetDuration();
        void GetDynamicLight(uint32_t arg_0, float* arg_1, float* arg_2, uint32_t* arg_3, void** arg_4, uint32_t* arg_5);
        void GetDynamicLightBBox(uint32_t arg_0, float** arg_1, float** arg_2);
        uint32_t GetDynamicLightNum();
        void GetEXT(char* arg_0, char* arg_1);
        struct CGuild* GetEmptyGuildData(struct CGuild* pData, int nNum);
        void GetEntityAnimationPos(float* arg_0, struct CParticle* arg_1);
        uint16_t GetExcelIndexFromCombineExCheckKey(unsigned int dwCombineExCheckKey);
        float GetFPS();
        int64_t GetFileSize(char* arg_0);
        uint32_t GetFileSizeAndMergeFile(char* arg_0);
        void GetFinalBilloardMatrix(float** arg_0, float** arg_1, struct CParticle* arg_2, uint32_t arg_3);
        void GetFinalPath(char* arg_0, char* arg_1);
        float GetFloatMod(float arg_0, float arg_1);
        uint32_t GetFogColor();
        void GetFrustumNormalPlane(float** arg_0);
        float GetGlobalMusicVolume();
        float GetGlobalWavVolume();
        float GetGravity(float arg_0);
        struct CGuild* GetGuildDataFromSerial(struct CGuild* pData, int nNum, unsigned int dwSerial);
        struct CGuild* GetGuildPtrFromName(struct CGuild* pData, int nNum, char* pwszGuildName);
        void GetHeadMatrix(struct D3DXMATRIX* arg_0, struct R3Camera* arg_1, float arg_2, int32_t arg_3, int32_t arg_4);
        int GetIPAddress(char* szAddr);
        int GetIPAddress(struct sockaddr_in* pAddr);
        unsigned int GetIPAddress();
        void GetInverseTransformVertex(float* arg_0, float* arg_1);
        int GetItemDurPoint(int nTableCode, int nIndex);
        char* GetItemEquipCivil(int nTableCode, int nItemIndex);
        int GetItemEquipGrade(int nTableCode, char* szRecordCode);
        int GetItemEquipGrade(int nTableCode, int nItemIndex);
        int GetItemEquipLevel(int nTableCode, int nItemIndex);
        struct _EQUIP_MASTERY_LIM* GetItemEquipMastery(int nTableCode, int nItemIndex, int* pnLimNum);
        int GetItemEquipUpLevel(int nTableCode, int nItemIndex);
        int GetItemGoldPoint(int nTableCode, int nItemIndex, int nRace, char* pbyMoneyKind);
        char GetItemGrade(int nTableCode, int nItemIndex);
        int GetItemKillPoint(int nTableCode, int nItemIndex, int nRace, char* pbyMoneyKind);
        char GetItemKindCode(int nTableCode);
        char* GetItemKorName(int nTableCode, int nItemIndex);
        int GetItemProcPoint(int nTableCode, int nItemIndex, int nRace, char* pbyMoneyKind);
        int GetItemStdPoint(int nTableCode, int nItemIndex, int nRace, char* pbyMoneyKind);
        int GetItemStdPrice(int nTableCode, int nItemIndex, int nRace, char* pbyMoneyKind);
        int GetItemStoragePrice(int nTableCode, int nItemIndex, int nRace);
        int GetItemTableCode(char* psItemCode);
        char GetItemUpgLimSocket(unsigned int dwLvBit);
        char GetItemUpgedLv(unsigned int dwLvBit);
        int64_t GetJPGDimensions(char* arg_0, unsigned int* arg_1, unsigned int* arg_2);
        unsigned int GetKorLocalTime();
        bool GetLastWriteFileTime(char* szFileName, struct _FILETIME* ftWrite);
        uint32_t GetLightMapColor(float* arg_0, int arg_1);
        void* GetLightMapSurface(int arg_0);
        uint32_t GetLightMapTexSize();
        unsigned int GetLocalDate();
        unsigned int GetLoopTime();
        void GetMacAddrString(char* szMac, uint64_t tBuffSize);
        struct _R3MATERIAL* GetMainMaterial();
        uint32_t GetMainMaterialNum();
        void GetMatLightFromColor(struct _D3DLIGHT8* Dst, _D3DMATERIAL8* Dst2, uint32_t arg_0, float arg_1);
        int64_t GetMaterialNameNum(struct _R3MATERIAL* arg_0);
        void GetMatrixFrom3DSMAXMatrix(float** arg_0);
        void GetMatrixFromAtoB(float** arg_0, float* arg_1, float* arg_2);
        void GetMatrixFromAtoB2(float** arg_0, float* arg_1, float* arg_2);
        void GetMatrixFromVector(float** arg_0, float* arg_1, float* arg_2);
        unsigned int GetMaxParamFromExp(int nAnimusClass, uint64_t dwExp);
        int GetMaxResKind();
        struct CMergeFileManager* GetMergeFileManager();
        int64_t GetMipMapSkipSize(struct _DDSURFACEDESC2* arg_0, uint32_t arg_1, uint32_t arg_2, uint32_t arg_3);
        float GetMotionBlurLength();
        float GetMultiLayerTime();
        int GetNextDay();
        void GetNormal(float* arg_0, float* arg_1, float* arg_2, float* arg_3);
        void GetNowDateTime(char* szDateTime);
        float GetNowFrame();
        int64_t GetNowFreeJmallocSize();
        HWND GetNowHWnd();
        int64_t GetNowJmallocSize();
        int64_t GetNowPath(char* arg_0);
        int64_t GetNowR3D3DTexCnt();
        int64_t GetNowR3TexCnt();
        uint32_t GetNowTexMemSize();
        int GetNumAllFile(char* pszDirectory);
        void GetObjectMatrix(float** arg_0, uint16_t arg_1, struct _ANI_OBJECT* arg_2, float arg_3);
        struct IDirect3DSurface8* GetOldRenderTarget();
        struct IDirect3DSurface8* GetOldStencilZ();
        uint32_t GetOneNameFromPath(char* arg_0, char* arg_1, uint32_t* arg_2);
        uint32_t GetOutLineColor();
        uint32_t GetOutLineColorFont16();
        uint32_t GetOutLineColorFont24();
        uint32_t GetPlaneCrossPoint(float* arg_0, float* arg_1, float* arg_2, float* arg_3, float arg_4);
        int64_t GetPlaneCrossPoint(float* arg_0, float* arg_1, float* arg_2, float* arg_3, float arg_4, float arg_5);
        void GetPointCamera(float** arg_0);
        void GetPosByDistFromATOB(float** arg_0, float* arg_1, float* arg_2, float arg_3);
        struct _PRE_PARTICLE_LIST* GetPreParticleList(int arg_0);
        int64_t GetPreParticleListNum();
        int GetPrevDay();
        void GetProjectMatrix(struct D3DXMATRIX* arg_0);
        struct CPlayer* GetPtrPlayerFromAccount(struct CPlayer* pData, int nNum, char* szAccount);
        struct CPlayer* GetPtrPlayerFromAccountSerial(struct CPlayer* pData, int nNum, unsigned int dwSerial);
        struct CPlayer* GetPtrPlayerFromCharSerial(int nNum, unsigned int dwSerial);
        struct CPlayer* GetPtrPlayerFromName(struct CPlayer* pData, int nNum, char* pwszName);
        struct CPlayer* GetPtrPlayerFromSerial(struct CPlayer* pData, int nNum, unsigned int dwSerial);
        int GetQLen(float* fPos, float* fTar);
        void GetQuaternionFromVector(float* arg_0, float* arg_1);
        void GetQuaternionFromVector(float* arg_0, float* arg_1, float* arg_2);
        uint32_t GetR3TexManageFlag();
        int64_t GetRandOrNum(FILE* File, float* arg_0, float* arg_1);
        bool GetReactArea(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, struct _react_area* poutReactArea, unsigned int dwDesiredApply, char* pszoutErrMsg, char* pszoutEventCode);
        bool GetReactObject(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, struct _react_obj* poutReactObject, bool bReadCnt, unsigned int dwDesiredApply, char* pszoutErrMsg, char* pszoutEventCode);
        int64_t GetReflectionState();
        int GetRegionIndex(int nMapIndex, unsigned int x, unsigned int y, unsigned int dwMaxX, unsigned int dwMaxY);
        char* GetRegionName(int nMapIndex, unsigned int x, unsigned int y, unsigned int dwMaxX, unsigned int dwMaxY);
        int GetRewardItemNumChangeClass(struct _class_fld* pClassFld);
        int GetSFLevel(int nLv, unsigned int dwHitCount);
        struct CTextureRender* GetShadowRenderTexture();
        struct IDirect3DTexture8* GetShadowTexture();
        bool GetSocketName(uint64_t socket, char* pOut);
        float GetSqrt(float* fPos, float* fTar);
        int GetStaffMastery(unsigned int* pdwForceLvCum);
        uint32_t GetStateFullScreenEffect();
        void GetSubDayStr(int nSubDay, char* szOutDay);
        float GetSwimU(float arg_0);
        float GetSwimV(float arg_0);
        char GetTalikFromSocket(unsigned int dwLvBit, char bySocketIndex);
        void* GetTempBuffer();
        uint32_t GetTempBufferSize();
        int GetTextureMatrix(struct _R3MATERIAL* arg_0, int arg_1, struct D3DXMATRIX* arg_2, float arg_3);
        int64_t GetTileAniTextureAddId(struct _R3MATERIAL* arg_0, int arg_1, float arg_2);
        void GetTodayStr(char* szToday);
        uint32_t GetTokenFloat(char* arg_0, float* arg_1);
        uint32_t GetTotalFrame();
        int64_t GetTotalFreeJmallocSize();
        uint32_t GetTotalVertexBufferCnt();
        uint32_t GetTotalVertexBufferSize();
        int64_t GetTransformVertex(float** arg_0, float* arg_1);
        int GetUsePcCashType(char byTblCode, int nIndex);
        void GetVertexFromBVertex(float* arg_0, void* arg_1, struct _BSP_READ_M_GROUP* arg_2);
        void GetVertexFromFVertex(float* arg_0, void* arg_1, struct _BSP_READ_M_GROUP* arg_2);
        void GetVertexFromWVertex(float* arg_0, void* arg_1, struct _BSP_READ_M_GROUP* arg_2);
        void GetViewMatrix(struct D3DXMATRIX* arg_0);
        void GetViewVector(float* arg_0);
        char GetWeaponClass(int nItemIndex);
        void GetWebBrowserRect(struct tagRECT* arg_0);
        float GetXAngle(float* arg_0, float* arg_1);
        int GetYAngle(float* arg_0, float* arg_1, float* arg_2);
        float GetYAngle(float* Pos, float* Tar);
        void GetYBillboardMatrix(struct D3DXMATRIX* arg_0, int32_t arg_1, struct D3DXVECTOR3* arg_2);
        void GetYBillboardMatrix(struct D3DXMATRIX* arg_0, float* arg_1, float* arg_2);
        void GetZBillboardMatrix(struct D3DXMATRIX* arg_0, float* arg_1, float* arg_2);
        void Get_CashEvent_Name(char byEventType, char* szEventName);
        unsigned int H(unsigned int x, unsigned int y, unsigned int z);
        void HtoA(char* src, char* dest, int srclen);
        char HtoB(char ch);
        unsigned int I(unsigned int x, unsigned int y, unsigned int z);
        uint32_t IM_LoadWave(char* arg_0, uint32_t arg_1);
        void IM_PlayWave(uint32_t arg_0, float arg_1, float arg_2);
        void IM_ReleaseAllWaves();
        void IM_ReleaseWave(uint32_t arg_0);
        void IM_SetLoopCntWave(uint32_t arg_0, uint32_t arg_1);
        void IM_SetWaveVolumeAndPan(uint32_t arg_0, float arg_1, float arg_2);
        void IM_StopWave(uint32_t arg_0);
        bool IPConvertingStringToByte(char* szIP, char* pbyIP);
        unsigned int IndexFromPImgThunkData(struct _IMAGE_THUNK_DATA64* pitdCur, struct _IMAGE_THUNK_DATA64* pitdBase);
        int64_t InitBlurShader();
        void InitCheatCommand(struct CHEAT_COMMAND* pCmdList, char* byCommandSizeList);
        void InitConsole();
        void InitCore();
        void InitFullScreenEffect();
        void InitFunctionKey(char* arg_0);
        int64_t InitJmalloc(int arg_0);
        void InitMasteryFormula(struct CRecordData* pSkillData, struct CRecordData* pForceData);
        void InitR3Engine(int arg_0);
        void InitR3Particle();
        int64_t InitR3SoundSystem(char* arg_0);
        void InitR3Text();
        void InitSpriteManager();
        void InitWebBrowser(int32_t arg_0);
        void InsertConsoleStringQ(char* Src);
        int64_t InsertDynamicLight(float* arg_0, float arg_1, uint32_t arg_2, void* arg_3, int arg_4, uint32_t arg_5);
        float InterpolatedNoise_1(float arg_0);
        float InterpolatedNoise_1(float arg_0, float arg_1);
        int IsAbrItem(int nTableCode, int nItemIndex);
        bool IsAddAbleTalikToItem(char byItemTableCode, uint16_t wItemIndex, unsigned int dwItemCurLv, uint16_t wTalikIndex, int* pnTalikLim);
        int64_t IsAniTex(char* arg_0);
        int64_t IsBBoxInFrustum(float* arg_0, float* arg_1);
        int64_t IsBBoxInFrustum(int16_t* arg_0, int16_t* arg_1);
        struct CItemStore* IsBeNearStore(struct CPlayer* p, int nStoreCode);
        int64_t IsBothRayAABB(float* arg_0, float* arg_1, float* arg_2, float* arg_3);
        int IsBothRayAABB(int16_t* arg_0, int16_t* arg_1, float* arg_2, float* arg_3);
        int IsCashItem(char byTblCode, unsigned int dwIndex);
        int64_t IsCollisionBBox(float* arg_0, float* arg_1, float* arg_2, float* arg_3);
        int64_t IsCollisionBBox(int16_t* arg_0, int16_t* arg_1, float* arg_2, float* arg_3);
        int64_t IsCollisionBBox(int16_t* arg_0, int16_t* arg_1, int16_t* arg_2, int16_t* arg_3);
        int64_t IsCollisionBBoxPoint(float* arg_0, float* arg_1, float* arg_2);
        int64_t IsCollisionBBoxPoint(int16_t* arg_0, int16_t* arg_1, float* arg_2);
        int64_t IsCollisionBBoxToFace(float** arg_0, float* arg_1, float* arg_2, float* arg_3);
        bool IsDayChanged(int* iOldDay);
        int64_t IsEnableBBoShasiShader();
        int64_t IsEnableShader(uint32_t arg_0);
        int IsExchangeItem(int nTableCode, int nItemIndex);
        bool IsExistDarkHoleOpenGate();
        int64_t IsExistFile(char* arg_0);
        int64_t IsExistFileAndMergeFile(char* arg_0);
        int64_t IsExistFromSoundSpt(uint32_t arg_0);
        int IsExistItem(int nTableCode, int nItemIndex);
        int64_t IsFadeIn();
        int64_t IsFadeOut();
        int64_t IsFading();
        int64_t IsFarDistance(struct _BSP_LEAF* arg_0);
        int IsGroundableItem(int nTableCode, int nItemIndex);
        int64_t IsInitCore();
        int64_t IsInitR3Engine();
        int IsItemCombineExKind(int nTableCode);
        int IsItemEquipCivil(int nTableCode, int nItemIndex, char byRaceSex);
        int IsItemSerialNum(int nTableCode);
        int64_t IsLoadedMaterial();
        int64_t IsLoadedRTCamera();
        int64_t IsLoadedRTMovie();
        int64_t IsMagicLight(uint32_t arg_0);
        int IsNormalAccountUsable(int nTableCode, int nItemIndex);
        int64_t IsOneBackCollision();
        char IsOtherInvalidObjNear(struct CGameObject* pEster, float* pfEstPos, struct CTrap* pEstObj, struct _TrapItem_fld* pEstTrapItemInfo);
        bool IsOtherTowerNear(struct CGameObject* pEster, float* pfEstPos, struct CGuardTower* pEstObj);
        int IsOverLapItem(int nTableCode);
        int64_t IsParticle(char* arg_0);
        int64_t IsPlayMP3();
        int IsProtectItem(int nTableCode);
        int64_t IsRTMoviePlaying();
        int64_t IsRayAABB(float* arg_0, float* arg_1, float* arg_2, float* arg_3, float** arg_4);
        int64_t IsRayAABB(float** arg_0, int16_t* arg_1, float* arg_2, float* arg_3, float** arg_4);
        int64_t IsRenderTargetFrameBuffer();
        int IsRepairableItem(int nTableCode, int nItemIndex);
        int64_t IsRightIndexedUP(int arg_0, uint16_t* arg_1, int arg_2);
        bool IsSQLValidString(char* wszStr);
        int IsSaveItem(int nTableCode);
        int IsSellItem(int nTableCode, int nItemIndex);
        int64_t IsServerMode();
        int IsStorageCodeWithItemKind(int nTableCode, int nStorageCode);
        int IsStorageRange(char byStorageCode, char byStorageIndex);
        int IsTalikAboutTol(int nTalikIndex);
        bool IsTargeting(struct CGameObject* pTar);
        int64_t IsTextureFormatOk(_D3DFORMAT arg_0, _D3DFORMAT arg_1);
        int IsTimeItem(char byTblCode, unsigned int dwIndex);
        int IsTrunkIOAble(int nTableCode, int nItemIndex);
        bool IsUsableTempEffectAtStoneState(int nTempEffectType);
        int64_t IsWebBrowserMode();
        uint8_t* Jmalloc(int arg_0);
        uint8_t* JpegFileToRGB(char* arg_0, unsigned int* arg_1, unsigned int* arg_2);
        void LightMappingTex1(struct _BSP_MAT_GROUP* arg_0);
        float LinearInterpolate(float arg_0, float arg_1, float arg_2);
        int32_t LoadAndCreateShader(char* arg_0, uint32_t* arg_1, uint32_t arg_2, int arg_3, uint32_t* arg_4);
        uint8_t* LoadBmp(char* arg_0, int* arg_1, int* arg_2);
        int32_t LoadCreateShader(char* arg_0, uint32_t* arg_1, uint32_t arg_2, int arg_3, uint32_t* arg_4);
        struct _R3MATERIAL* LoadIndependenceR3M(char* arg_0);
        void LoadLightMap(char* arg_0);
        struct _R3MATERIAL* LoadMainMaterial(char* arg_0);
        struct _R3MATERIAL* LoadMainR3M(char* arg_0);
        bool LoadMasteryLimFile(char* pszErrMsg);
        int64_t LoadR3MP3(char* arg_0, uint32_t arg_1);
        void LoadR3T(struct R3Texture* arg_0);
        struct _LIGHTMAP** LoadR3TLightMap(struct R3Texture* arg_0, _D3DFORMAT arg_1);
        void LoadR3X(char* arg_0);
        bool LoadRegionData(int nMapNum, char** ppszMapNameList, char* pszErrMsg);
        int64_t LoadStreamR3MP3(char* arg_0, uint32_t arg_1);
        struct _R3MATERIAL* LoadSubMaterial(char* arg_0);
        struct _R3MATERIAL* LoadSubR3M(char* arg_0);
        void LoadVertexShaderList();
        void LoadWaveList(char* arg_0);
        int LuaScripAlert(lua_State* L);
        bool M2W(char* lpStr, char* wszTran, unsigned int wTranBuffSize);
        void MakeBinaryStr(char* pBuff, uint64_t tBufSize, char* pOut, uint64_t tOutSize);
        uint8_t* MakeDwordAlignedBuf(uint8_t* arg_0, unsigned int arg_1, unsigned int arg_2, unsigned int* arg_3);
        int64_t MakeGrayScale(uint8_t* arg_0, unsigned int arg_1, unsigned int arg_2);
        struct _STORAGE_LIST::_db_con* MakeLoot(char byTableCode, uint16_t wItemIndex);
        void MakeMipMap(uint16_t arg_0, uint16_t arg_1, uint16_t* arg_2, uint8_t* arg_3);
        void MakeMipMap(uint16_t arg_0, uint16_t arg_1, uint16_t* arg_2, uint16_t* arg_3);
        void MakeUV(struct _D3DR3VERTEX_TEX1* arg_0, int arg_1, void* arg_2, struct _R3MATERIAL* arg_3, int arg_4);
        void MakeUV(struct _D3DR3VERTEX_TEX2* arg_0, int arg_1, void* arg_2, void* arg_3, struct _R3MATERIAL* arg_4, int arg_5, float arg_6);
        void MakeUVExt(struct _D3DR3VERTEX_TEX2* arg_0, int arg_1, void* arg_2, void* arg_3, struct _R3MATERIAL* arg_4, int arg_5, float arg_6);
        void MatrixCopy(float** arg_0, float** arg_1);
        void MatrixFromQuaternion(float** arg_0, float arg_1, float arg_2, float arg_3, float arg_4);
        void MatrixIdentity(float** arg_0);
        int64_t MatrixInvert(float** arg_0, float** arg_1);
        void MatrixMultiply(float** arg_0, float** arg_1, float** arg_2);
        void MatrixRotate(float** arg_0, float arg_1, float arg_2, float arg_3);
        void MatrixRotateX(float** arg_0, float arg_1);
        void MatrixRotateY(float** arg_0, float arg_1);
        void MatrixRotateZ(float** arg_0, float arg_1);
        void MatrixScale(float** arg_0, float arg_1, float arg_2, float arg_3);
        int64_t MaxFixFloatToInt(float arg_0);
        int64_t MinFixFloatToInt(float arg_0);
        void MultiTexOff();
        void MultiTexOn();
        int MyCrtDebugReportHook(int nRptType, char* szMsg, int* retVal);
        void MyMessageBox(char* szTitle, char* szMessage);
        void NetTrace(char* fmt);
        float Noise(int32_t arg_0, int32_t arg_1);
        float Noise1(int32_t arg_0);
        void Normalize(float* v);
        void OnLoop_GuildSystem(bool bChangeDay);
        void OnLoop_VoteSystem();
        void OutputDebugLog(char* szFormat);
        int ParsingCommandA(char* pszSrc, int nMaxWordNum, char** ppszDst, int nMaxWordSize);
        int ParsingCommandW(char* pwszSrc, int nMaxWordNum, char** ppwszDst, int nMaxWordSize);
        float PerlinNoise_1D(float arg_0, float arg_1, uint32_t arg_2);
        float PerlinNoise_2D(float arg_0, float arg_1, float arg_2, uint32_t arg_3);
        uint32_t PixelFromFramebuffer(int16_t arg_0, int16_t arg_1);
        void PlayR3MP3();
        void PlayStreamR3MP3();
        void PlayWave(uint32_t arg_0, uint32_t arg_1, float arg_2, float arg_3);
        void PopViewPortArea();
        float PowInterpolate(float arg_0, float arg_1, float arg_2);
        void PreRenderOneLayer(struct CVertexBuffer* arg_0, struct _BSP_MAT_GROUP* arg_1);
        void PreRenderSetting(int arg_0, struct CVertexBuffer* arg_1, struct _BSP_MAT_GROUP* arg_2);
        void PrepareAllShadow();
        bool ProcessCheatCommand(struct CPlayer* pOne, char* pwszCommand);
        void ProgressConsole(struct CLevel* arg_0);
        void ProgressFunctionKey();
        void PushViewPortArea();
        void PutMessage(char* arg_0, char* arg_1);
        void QuaternionFromRotation(float* arg_0, float* arg_1, float arg_2);
        void QuaternionSlerp(float* arg_0, float* arg_1, float* arg_2, float* arg_3, float arg_4, float arg_5, float arg_6, float arg_7, float arg_8, float arg_9, float arg_10, float arg_11, float arg_12);
        int64_t R3BeginScene();
        int64_t R3CalcStrIndexPitInWidthA(char* arg_0, int arg_1, int arg_2, uint32_t arg_3);
        int64_t R3CalcStrIndexPitInWidthW(wchar_t* arg_0, int arg_1, int arg_2, uint32_t arg_3);
        int64_t R3CalcStrPixelSizeA(char* arg_0, struct tagSIZE* arg_1, int arg_2, uint32_t arg_3);
        int64_t R3CalcStrPixelSizeW(wchar_t* arg_0, struct tagSIZE* arg_1, int arg_2, uint32_t arg_3);
        void R3CalculateTime();
        void R3ClearFrameBuffer();
        int32_t R3ConfirmDevice(struct _D3DCAPS8* arg_0, uint32_t arg_1, _D3DFORMAT arg_2);
        int32_t R3DeleteDevice();
        void R3Draw2DLine(float* arg_0, float* arg_1, uint32_t arg_2);
        void R3Draw2DLineList(float** arg_0, uint32_t arg_1, uint32_t arg_2);
        void R3DrawLine(float* arg_0, float* arg_1, uint32_t arg_2);
        void R3EndScene();
        void R3EnvironmentQuake(float arg_0, float arg_1);
        void R3EnvironmentShake(float arg_0, float arg_1);
        void R3EnvironmentShakeOff();
        int64_t R3EnvironmentShakeState();
        void R3FlyMove(float arg_0, float* arg_1);
        void R3FlyMoveSetPos(float* arg_0);
        float R3GetLoopTime();
        void R3GetPreAniTextureId(char* arg_0, char* arg_1, int32_t* arg_2, int32_t* arg_3);
        int64_t R3GetPreTextureId(char* arg_0);
        void R3GetQuakeMatrix(float** arg_0);
        void R3GetQuakeVector(float* arg_0);
        void R3GetShakeVector(float* arg_0);
        struct IDirect3DTexture8* R3GetSurface(int arg_0);
        R3Texture* R3GetTexInfoR3T(char* arg_0, uint32_t arg_1);
        char* R3GetTexName(int arg_0);
        float R3GetTime();
        int32_t R3GetToggle15fps();
        int32_t R3GetToggle30fps();
        int32_t R3GetToggle7fps();
        void R3GetViewPort(int32_t* arg_0, int32_t* arg_1, int32_t* arg_2, int32_t* arg_3);
        int32_t R3InitDevice(struct IDirect3DDevice8* arg_0, int arg_1, int arg_2);
        int32_t R3InvalidateDevice();
        struct IDirect3DTexture8* R3LoadDDS(char* arg_0, uint32_t arg_1, uint32_t arg_2, uint32_t arg_3);
        struct IDirect3DTexture8* R3LoadDDSAndTextureMem(char* arg_0, uint32_t arg_1);
        struct IDirect3DTexture8* R3LoadDDSFromFP(FILE* File, size_t Size, uint32_t arg_0, uint32_t arg_1, uint32_t arg_2);
        void R3LoadTextTexture();
        void R3LoadTextureMem(int arg_0);
        void R3MouseInput();
        struct D3DXMATRIX* R3MoveGetViewMatrix();
        void R3MsgProc(HWND arg_0, unsigned int arg_1, uint64_t arg_2, int64_t arg_3);
        void R3ReleaseAllTextures();
        void R3ReleasePreTextures();
        void R3ReleaseTextTexture();
        void R3ReleaseTextureMem(int arg_0);
        void R3RestoreAllTextures();
        int32_t R3RestoreDevice();
        void R3SetCameraMatrix(float* arg_0, float** arg_1);
        void R3SetLoopTime(float arg_0);
        void R3SetMinFPS(float arg_0);
        void R3SetTime(float arg_0);
        void R3SetViewPort(int32_t arg_0, int32_t arg_1, int32_t arg_2, int32_t arg_3);
        uint8_t* RGBFromDWORDAligned(uint8_t* arg_0, unsigned int arg_1, unsigned int arg_2, unsigned int arg_3);
        int64_t RGBToJpegFile(char* arg_0, uint8_t* arg_1, unsigned int arg_2, unsigned int arg_3, int arg_4, int arg_5);
        void RTMoiveGetCameraMatrix(float** arg_0);
        void RTMoiveGetCameraPos(float* arg_0);
        void RTMovieAddState(uint32_t arg_0);
        void RTMovieCreate(char* arg_0, struct CLevel* arg_1);
        void RTMovieFrameMove();
        uint32_t RTMovieGetState();
        void RTMoviePause(int arg_0);
        void RTMovieRelease();
        void RTMovieRender();
        void RTMovieSetState(uint32_t arg_0);
        void RTMovieSkipShadowState(int arg_0);
        void RTMovieStartPlay(int arg_0);
        void RTMovieSubState(uint32_t arg_0);
        void* ReAlloc(void* arg_0, int arg_1, int arg_2);
        void ReLoadMaterial(char* arg_0, struct _R3MATERIAL* arg_1);
        void ReMoveCamera(_MOVE_CAMERA* arg_0);
        void ReleaesR3MP3();
        void ReleaesStreamR3MP3();
        void ReleaseAllSpriteTexMem();
        void ReleaseBlurShader();
        void ReleaseBlurVBuffer();
        void ReleaseConsole();
        void ReleaseCore();
        void ReleaseFullScreenEffect();
        void ReleaseFunctionKey();
        void ReleaseJmalloc();
        void ReleaseLightMap();
        void ReleaseMainMaterial();
        void ReleaseR3Engine();
        void ReleaseR3Particle();
        void ReleaseR3SoundSystem();
        void ReleaseR3Text();
        void ReleaseSpriteManager(struct CSprite* arg_0);
        void ReleaseSpriteManager();
        void ReleaseSubMaterial(struct _R3MATERIAL* arg_0);
        void ReleaseSystemTexture();
        void ReleaseVertexShaderList();
        void ReleaseWaveList();
        void ReleaseWebBrowser();
        void ResetTexMemSize();
        void ResetTotalVertexBufferInfo();
        void ResetVertexBufferCache();
        void RestoreAllSpriteTexMem();
        void RestoreSpriteManager(struct CSprite* arg_0);
        void RestoreSystemTexture();
        void Rijndael_VC60Workaround();
        unsigned int RotateLeftBits(unsigned int x, unsigned int n);
        void Round1(unsigned int* output, unsigned int x, unsigned int y, unsigned int z, unsigned int decode, unsigned int bits, unsigned int AC);
        void Round2(unsigned int* output, unsigned int x, unsigned int y, unsigned int z, unsigned int decode, unsigned int bits, unsigned int AC);
        void Round3(unsigned int* output, unsigned int x, unsigned int y, unsigned int z, unsigned int decode, unsigned int bits, unsigned int AC);
        void Round4(unsigned int* output, unsigned int x, unsigned int y, unsigned int z, unsigned int decode, unsigned int bits, unsigned int AC);
        void SaveBMP(char* Filename, int64_t arg_0, int arg_1, int arg_2, uint8_t* arg_3);
        void SaveJPG(char* arg_0, int arg_1, int arg_2, int arg_3, uint8_t* arg_4);
        int64_t ScanCodeToVKCode(int arg_0);
        struct CUserDB* SearchAvatorWithCMS(struct CUserDB* pList, int nMax, char* pCMS);
        struct CUserDB* SearchAvatorWithName(struct CUserDB* pList, int nMax, char* pwszName);
        struct CMonster* SearchEmptyMonster(bool bWithoutFail);
        using SearchJobCommandFn_ret = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
        SearchJobCommandFn_ret SearchJobCommandFn(char* pszCommand);
        using SearchMissionCommandFn_ret = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
        SearchMissionCommandFn_ret SearchMissionCommandFn(char* pszCommand);
        using SearchQuestCommandFn_ret = bool (WINAPIV*)(struct strFILE*, struct CDarkHoleDungeonQuestSetup*, char*);
        SearchQuestCommandFn_ret SearchQuestCommandFn(char* pszCommand);
        unsigned int SendLoop(void* pVoid);
        void SendMsg_EconomyDataToWeb();
        struct CDarkHole* SerarchEmptyDarkHole();
        void ServerProgramExit(char* pszCall, bool bSave);
        void SetConsoleStateB(int arg_0, int arg_1);
        void SetContrast(float* arg_0);
        void SetContrast(uint8_t* Src);
        void SetDDSTexNameDebug(char* arg_0);
        void SetDDSTexSizeDebug(uint32_t arg_0);
        void SetDefaultFlags();
        void SetDefaultFov();
        void SetDetailTexVS(struct D3DXMATRIX* arg_0);
        void SetDynamicLight(float* arg_0, float arg_1, uint32_t arg_2, void* arg_3, int arg_4, uint32_t arg_5);
        void SetErrorButRunMessageProc(void (WINAPIV* arg_0)(char*));
        void SetErrorMessageProc(void (WINAPIV* arg_0)(char*));
        void SetFadeFactor(uint32_t arg_0, float arg_1, void* arg_2);
        void SetFadeIn(uint32_t arg_0, float arg_1, float arg_2);
        void SetFadeOut(uint32_t arg_0, float arg_1, float arg_2);
        void SetFadeSky(uint32_t arg_0, float arg_1);
        void SetFadeTex(uint32_t arg_0, float arg_1, void* arg_2);
        void SetFov(float arg_0);
        void SetFreePointer();
        void SetFrustumNormalPlane();
        void SetGamma(float arg_0, int arg_1);
        int64_t SetGlobalMusicVolume(float arg_0);
        int64_t SetGlobalWavVolume(float arg_0);
        void SetGrassVS(float arg_0, float* arg_1, float arg_2, uint32_t arg_3);
        void SetLightMap(int32_t arg_0);
        void SetLitGrassVS(float arg_0, float* arg_1, float arg_2, uint32_t arg_3, float* arg_4);
        void SetMainLight(float* arg_0);
        void SetMatAlphaColor(uint32_t arg_0);
        void SetMergeFileManager(struct CMergeFileManager* arg_0);
        void SetMotionBlurLength(float arg_0);
        void SetMultiLayerCamera(float* arg_0);
        void SetMultiLayerTime(float arg_0);
        void SetNoLodTextere();
        void SetNowR3D3DTexCnt(int arg_0);
        void SetNowR3TexCnt(int arg_0);
        void SetOpStack(int arg_0);
        void SetOutLineColor(uint32_t arg_0);
        void SetOutLineColorFont16(uint32_t arg_0);
        void SetOutLineColorFont24(uint32_t arg_0);
        void SetPlayMusicState(int arg_0);
        void SetPlayWaveState(int arg_0);
        void SetProjectShadow(float* arg_0, void* arg_1, float arg_2, uint32_t arg_3, float arg_4, float arg_5);
        void SetR3D3DTexture(uint32_t arg_0, uint32_t arg_1);
        void SetR3TexManageFlag(uint32_t arg_0);
        void SetR3TextFont(char* Source, uint32_t arg_0, uint32_t arg_1, uint32_t arg_2);
        void SetReLoadState(int arg_0);
        void SetReflectionState(int arg_0);
        void SetReflectionVS();
        void SetSkipOneBboShasi1(int arg_0);
        int64_t SetSkyVS();
        void SetStateFullScreenEffect(uint32_t arg_0);
        void SetTextureRenderTargetFrameBuffer(int arg_0);
        void SetTimerRate(float arg_0);
        void SetTransformClipInfo(float arg_0, float arg_1, float arg_2, float arg_3);
        void SetVPIPTex1(void* arg_0, void* arg_1);
        void SetVPIPTex1IndexPrimitive(void* arg_0, void* arg_1);
        void SetVPIPTex1IndexPrimitiveTL(void* arg_0, void* arg_1);
        void SetVPIPTex2(void* arg_0, void* arg_1);
        void SetViewPortArea(int32_t arg_0, int32_t arg_1, int32_t arg_2, int32_t arg_3);
        int64_t SetVolumeMP3(float arg_0);
        int64_t SetVolumeStreamMP3(float arg_0);
        void SetWarningMessageProc(void (WINAPIV* arg_0)(char*));
        void SetWaveVolAndPan(uint32_t arg_0, float arg_1, float arg_2);
        void SetWorldViewMatrixVS(float** arg_0);
        void ShadowBeginScene();
        void ShadowEndScene();
        void ShortToFloat(int16_t* pShort, float* pFloat, int size);
        float SmoothedNoise1(int32_t arg_0);
        float SmoothedNoise1(int32_t arg_0, int32_t arg_1);
        uint64_t SplitString(char* strSrc, char* _Delim, std::vector<std::basic_string<char>>* stringlist);
        void StopR3MP3();
        void StopStreamR3MP3();
        void StopWave(uint32_t arg_0);
        void StripEXT(char* arg_0);
        void StripFinalPath(char* arg_0);
        void StripName(char* arg_0);
        void StripPath(char* arg_0);
        void TestAllGroup(void* arg_0, void* arg_1, int arg_2, int arg_3);
        void TestWriteFile(char* arg_0, char* arg_1);
        void TextureCopy(struct CTextureRender* _this, void* arg_0);
        void TextureSplatting(struct _BSP_MAT_GROUP* arg_0, int arg_1);
        int64_t TransformVertex(float* arg_0, float** arg_1, float* arg_2);
        int64_t TransformVertex(float* arg_0, float** arg_1, float* arg_2, float* arg_3);
        void UnLightMappingTex1();
        void UnSetDynamicLight();
        void UnSetMatAlphaColor();
        void UnSetProjectedShadow();
        void UnTextureSplatting();
        void UpdateStreamMP3();
        void Vector3fTransform(float* arg_0, float* arg_1, float** arg_2);
        int64_t VertFlipBuf(uint8_t* arg_0, unsigned int arg_1, unsigned int arg_2);
        bool W2M(char* lpwStr, char* szTran, unsigned int wTranBuffSize);
        bool WCHARTOMULTI(wchar_t* lpwStr, char* szTran, unsigned int wTranBuffSize);
        void Warning(char* arg_0, char* arg_1);
        int64_t WndProc(HWND arg_0, unsigned int arg_1, uint64_t arg_2, int64_t arg_3);
        void WriteCheatLog(char* pwszCommand, struct CPlayer* pOne);
        bool WriteLogFileLong(char* pszFileName, char* log);
        void WriteServerStartHistory(char* fmt);
        bool WriteTableData(int nItemNum, struct CRecordData* pItemData, bool bUseHash, char* szErrCode);
        bool WriteTableDataPart(int nTableCode, struct CRecordData* pItemData, char* szErrCode);
        int _AfxInitManaged();
        int _CalcPayExgRatePerRace(float* pfAvrTradeMoney, int nRaceCode);
        void _CcrFG_rs_CloseUserContext(void** arg_0);
        void* _CcrFG_rs_CreateUserContext(uint32_t arg_0, uint8_t* arg_1, uint32_t arg_2, uint8_t* arg_3, uint32_t arg_4, uint32_t arg_5, void* arg_6);
        int _CcrFG_rs_DecryptPacket(void* arg_0, uint8_t* arg_1, int arg_2);
        int _CcrFG_rs_EncryptPacket(void* arg_0, uint8_t* arg_1, int arg_2);
        uint32_t _CcrFG_rs_GetLastError();
        int _CcrFG_rs_Initialize(int (WINAPIV* arg_0)(int32_t, void*, void*, int, void*), uint8_t* arg_1, uint32_t arg_2);
        void _CcrFG_rs_Uninitialize();
        int _CheckCumulativeSF(char byEffectCode, unsigned int dwEffectIndex, int* nCumulMax, int* nEffectCount, char** pstrLinkCode);
        bool _CheckDestMonsterLimitLv(int MyLevel, int iDstLevel, char byID);
        bool _CheckPotionData(struct _CheckPotion_fld::_CheckEffectCode* CheckEffectNode, struct CPlayer* pApplyPlayer);
        bool _CheckSameItem(char* strLinkItem, char* strDst, char* bySelectLinkIndex, bool bStuff);
        int _Check_SF_UseType(struct _base_fld* pFld, int nEffectType);
        void _CreateLootingNovusItem(char* strItemCode, char* strMapName, struct LuaParam3 Pos, struct LuaParam3 vParam);
        struct CMonster* _CreateMon(char* strMapName, char* MonCode, float fX, float fY, float fZ);
        HRESULT _DbgOut(char* strFile, unsigned int dwLine, HRESULT hr, char* strMsg);
        void _DeleteExceptionPtr(struct __ExceptionPtr* arg_0);
        bool _FailItemShortBuffer(int* nBuffer, char byMtSlotNum, struct _ItemCombine_exp_fld::_material* pMtlList, struct _STORAGE_LIST::_db_con** pMt_Sv_Inv);
        struct CLuaEventMgr* _GetLuaEventMgr();
        uint16_t _GetMonsterContTime(char byEffectCode, char byLv);
        bool _GetTempEffectValue(struct _skill_fld* pEffectFld, int nTempEffectType, float* fValue);
        struct _trand_tbl* _GetTransTBL(int nState);
        bool _IsXmasSnowEffect(struct _sf_continous* pSFCont);
        char* _KickReason(char byReason);
        long double _Pow_int(long double _X, int _Y);
        float _Pow_int(float _X, int _Y);
        void _RTC_AllocaFailure(void* retaddr, struct _RTC_ALLOCA_NODE* pn, int num);
        void _RTC_Failure(void* retaddr, int errnum);
        using _RTC_GetErrorFunc_ret = int (WINAPIV*)(int, char*, int, char*, char*);
        _RTC_GetErrorFunc_ret _RTC_GetErrorFunc(void* addr);
        using _RTC_GetErrorFuncW_ret = int (WINAPIV*)(int, wchar_t*, int, wchar_t*, wchar_t*);
        _RTC_GetErrorFuncW_ret _RTC_GetErrorFuncW(void* addr);
        int _RTC_GetSrcLine(char* address, wchar_t* source, unsigned int sourcelen, int* pline, wchar_t* moduleName, unsigned int modulelen);
        void _RTC_StackFailure(void* retaddr, char* varname);
        bool _ReadEconomyIniFile();
        unsigned int _SearchPlayer(char* szCharName);
        void _UpdateNewEconomy(struct _economy_calc_data* pData);
        void _UpdateRateSendToAllPlayer();
        void __ArrayUnwind(void* ptr, uint64_t size, int count, void (WINAPIV* pDtor)(void*));
        int __CxxUnhandledExceptionFilter(struct _EXCEPTION_POINTERS* pPtrs);
        bool __destroy_item(struct CPlayer* pMaster, struct _combine_ex_item_result_zocl::__item* pItem, struct _STORAGE_LIST::_db_con* pSvItem, struct _combine_ex_item_request_clzo::_list* pMeterial, int nSocketIndex);
        char __make_item(struct CPlayer* pMaster, struct _combine_ex_item_result_zocl::__item* pItem, struct _ItemCombine_exp_fld* pfld, int nIndex, char byLinkTableIndex);
        void clear_file(char* pszDir, unsigned int dwCutDay);
        bool ct_CashEventStart(struct CPlayer* pOne);
        bool ct_CdeEndup(struct CPlayer* pOne);
        bool ct_CdeStart(struct CPlayer* pOne);
        bool ct_ClassRefineEvent(struct CPlayer* pOne);
        bool ct_ClearSettleOwnerGuild(struct CPlayer* pOne);
        bool ct_ConEventStart(struct CPlayer* pOne);
        bool ct_Gold_Age_Event_Status(struct CPlayer* pOne);
        bool ct_Gold_Age_Get_Box_Cnt(struct CPlayer* pOne);
        bool ct_Gold_Age_Set_Event_Status(struct CPlayer* pOne);
        bool ct_HolyKeeperAttack(struct CPlayer* pOne);
        bool ct_HolySystem(struct CPlayer* pOne);
        bool ct_HolySystem_Jp(struct CPlayer* pOne);
        bool ct_InformCristalBattleBeforeAnHour(struct CPlayer* pOne);
        bool ct_InformPatriarchProcessor(struct CPlayer* pOne);
        bool ct_NuAfterEffect(struct CPlayer* pOne);
        bool ct_PcBandPrimium(struct CPlayer* pOne);
        bool ct_PvpLimitInit(struct CPlayer* pOne);
        bool ct_ReqChangeHonorGuild(struct CPlayer* pOne);
        bool ct_ReqPunishment(struct CPlayer* pOne);
        bool ct_SetGuildGrade(struct CPlayer* pOne);
        bool ct_SetGuildGradeByGuildSerial(struct CPlayer* pOne);
        bool ct_SetGuildGradeByName(struct CPlayer* pOne);
        bool ct_SetGuildMaster(struct CPlayer* pOne);
        bool ct_SetMaxLevelLimit(struct CPlayer* pOne);
        bool ct_SetPatriarchAuto(struct CPlayer* pOne);
        bool ct_SetPatriarchClear(struct CPlayer* pOne);
        bool ct_SetPatriarchGroup(struct CPlayer* pOne);
        bool ct_SetPatriarchProcessor(struct CPlayer* pOne);
        bool ct_SetSettleOwnerGuild(struct CPlayer* pOne);
        bool ct_Win_RaceWar(struct CPlayer* pOne);
        bool ct_action_point_set(struct CPlayer* pOne);
        bool ct_add_guild_schedule(struct CPlayer* pOne);
        bool ct_add_one_day_guild_schedule(struct CPlayer* pOne);
        bool ct_all_item_muzi(struct CPlayer* pOne);
        bool ct_all_map(struct CPlayer* pOne);
        bool ct_alter_cashbag(struct CPlayer* pOne);
        bool ct_alter_dalant(struct CPlayer* pOne);
        bool ct_alter_exp(struct CPlayer* pOne);
        bool ct_alter_gold(struct CPlayer* pOne);
        bool ct_alter_inven_dur(struct CPlayer* pOne);
        bool ct_alter_lv(struct CPlayer* pOne);
        bool ct_alter_pvp(struct CPlayer* pOne);
        bool ct_amp_full(struct CPlayer* pOne);
        bool ct_amp_set(struct CPlayer* pOne);
        bool ct_animus_attack_grade(struct CPlayer* pOne);
        bool ct_animus_recall_term(struct CPlayer* pOne);
        bool ct_animusexp(struct CPlayer* pOne);
        bool ct_basemastery(struct CPlayer* pOne);
        bool ct_boss_sms_cancel(struct CPlayer* pOne);
        bool ct_buf_potion_use(struct CPlayer* pOne);
        bool ct_cashitembuy(struct CPlayer* pOne);
        bool ct_change_class(struct CPlayer* pOne);
        bool ct_change_degree(struct CPlayer* pOne);
        bool ct_change_master_elect(struct CPlayer* pOne);
        bool ct_change_mastery(struct CPlayer* pOne);
        bool ct_chatsave(struct CPlayer* pOne);
        bool ct_check_guild_batlle_goal(struct CPlayer* pOne);
        bool ct_circle_mon_kill(struct CPlayer* pOne);
        bool ct_circle_user_num(struct CPlayer* pOne);
        bool ct_combine_ex_result(struct CPlayer* pOne);
        bool ct_complete_quest(struct CPlayer* pOne);
        bool ct_complete_quest_other(struct CPlayer* pOne);
        bool ct_cont_effet_clear(struct CPlayer* pOne);
        bool ct_cont_effet_time(struct CPlayer* pOne);
        bool ct_continue_palytime_inc(struct CPlayer* pOne);
        bool ct_copy_avator(struct CPlayer* pOne);
        bool ct_create_guildbattle_field_object(struct CPlayer* pOne);
        bool ct_cur_guildbattle_color(struct CPlayer* pOne);
        bool ct_darkholereward(struct CPlayer* pOne);
        bool ct_defense_item_grace(struct CPlayer* pOne);
        bool ct_defense_item_grace_Jp(struct CPlayer* pOne);
        bool ct_destroy_gravitystone(struct CPlayer* pOne);
        bool ct_destroy_guildbattle_field_object(struct CPlayer* pOne);
        bool ct_destroy_system_tower(struct CPlayer* pOne);
        bool ct_die(struct CPlayer* pOne);
        bool ct_drop_gravitystone(struct CPlayer* pOne);
        bool ct_drop_jade(struct CPlayer* pOne);
        bool ct_elect_info_player(struct CPlayer* pOne);
        bool ct_elect_set_env(struct CPlayer* pOne);
        bool ct_elect_set_player(struct CPlayer* pOne);
        bool ct_eventset_start(struct CPlayer* pOne);
        bool ct_eventset_stop(struct CPlayer* pOne);
        bool ct_exception(struct CPlayer* pOne);
        bool ct_exip_keeper(struct CPlayer* pOne);
        bool ct_exit_stone(struct CPlayer* pOne);
        bool ct_expire_pcbang(struct CPlayer* pOne);
        bool ct_free_ride_ship(struct CPlayer* pOne);
        bool ct_free_sf_by_class(struct CPlayer* pOne);
        bool ct_full_animus_gauge(struct CPlayer* pOne);
        bool ct_full_force(struct CPlayer* pOne);
        bool ct_full_gauge(struct CPlayer* pOne);
        bool ct_fullset(struct CPlayer* pOne);
        bool ct_get_gravitystone(struct CPlayer* pOne);
        bool ct_goto_char(struct CPlayer* pOne);
        bool ct_goto_mine(struct CPlayer* pOne);
        bool ct_goto_monster(struct CPlayer* pOne);
        bool ct_goto_npc(struct CPlayer* pOne);
        bool ct_goto_shipport_eder(struct CPlayer* pOne);
        bool ct_goto_shipport_town(struct CPlayer* pOne);
        bool ct_goto_stone(struct CPlayer* pOne);
        bool ct_guild_battle_force_stone(struct CPlayer* pOne);
        bool ct_guild_call(struct CPlayer* pOne);
        bool ct_guild_info(struct CPlayer* pOne);
        bool ct_guild_suggest(struct CPlayer* pOne);
        bool ct_half_gauge(struct CPlayer* pOne);
        bool ct_init_monster(struct CPlayer* pOne);
        bool ct_inven_empty(struct CPlayer* pOne);
        bool ct_itemloot(struct CPlayer* pOne);
        bool ct_jump_to_pos(struct CPlayer* pOne);
        bool ct_kick_player(struct CPlayer* pOne);
        bool ct_loadcashamount(struct CPlayer* pOne);
        bool ct_look_like_boss(struct CPlayer* pOne);
        bool ct_loot_bag(struct CPlayer* pOne);
        bool ct_loot_dungeon(struct CPlayer* pOne);
        bool ct_loot_item(struct CPlayer* pOne);
        bool ct_loot_material(struct CPlayer* pOne);
        bool ct_loot_mine(struct CPlayer* pOne);
        bool ct_loot_tower(struct CPlayer* pOne);
        bool ct_loot_upgrade(struct CPlayer* pOne);
        bool ct_loot_upgrade_item(struct CPlayer* pOne);
        bool ct_lua_command(struct CPlayer* pOne);
        bool ct_make_system_tower(struct CPlayer* pOne);
        bool ct_makeitem_need_matrial(struct CPlayer* pOne);
        bool ct_makeitem_no_matrial(struct CPlayer* pOne);
        bool ct_manage_guild(struct CPlayer* pOne);
        bool ct_max_attack(struct CPlayer* pOne);
        bool ct_mepcbang(struct CPlayer* pOne);
        bool ct_min_attack(struct CPlayer* pOne);
        bool ct_minespeed(struct CPlayer* pOne);
        bool ct_mormal_attack(struct CPlayer* pOne);
        bool ct_party_call(struct CPlayer* pOne);
        bool ct_pass_dungeon(struct CPlayer* pOne);
        bool ct_pass_sch(struct CPlayer* pOne);
        bool ct_pcanimusexp(struct CPlayer* pOne);
        bool ct_pcbangitemget(struct CPlayer* pOne);
        bool ct_pcbasemastery(struct CPlayer* pOne);
        bool ct_pcitemloot(struct CPlayer* pOne);
        bool ct_pcminespeed(struct CPlayer* pOne);
        bool ct_pcplayerexp(struct CPlayer* pOne);
        bool ct_pcroom_premium(struct CPlayer* pOne);
        bool ct_pcsfmastery(struct CPlayer* pOne);
        bool ct_period_time_set(struct CPlayer* pOne);
        bool ct_playerexp(struct CPlayer* pOne);
        bool ct_premium_rate(struct CPlayer* pOne);
        bool ct_query_remain_ore(struct CPlayer* pOne);
        bool ct_recall_monster(struct CPlayer* pOne);
        bool ct_recall_player(struct CPlayer* pOne);
        bool ct_recv_change_atrad_taxrate(struct CPlayer* pOne);
        bool ct_recv_current_battle_info(struct CPlayer* pOne);
        bool ct_recv_pvp_guild_rank(struct CPlayer* pOne);
        bool ct_recv_reserved_schedulelist(struct CPlayer* pOne);
        bool ct_recv_total_guild_rank(struct CPlayer* pOne);
        bool ct_regen_gravitystone(struct CPlayer* pOne);
        bool ct_release_loot_free(struct CPlayer* pOne);
        bool ct_release_make_succ(struct CPlayer* pOne);
        bool ct_release_matchless(struct CPlayer* pOne);
        bool ct_release_never_die(struct CPlayer* pOne);
        bool ct_release_punishment(struct CPlayer* pOne);
        bool ct_remove_sf_delay(struct CPlayer* pOne);
        bool ct_report_cri_hp(struct CPlayer* pOne);
        bool ct_report_position(struct CPlayer* pOne);
        bool ct_request_delete_quest(struct CPlayer* pOne);
        bool ct_request_npc_quest(struct CPlayer* pOne);
        bool ct_respawn_start(struct CPlayer* pOne);
        bool ct_respawn_stop(struct CPlayer* pOne);
        bool ct_resurrect_player(struct CPlayer* pOne);
        bool ct_server_rate(struct CPlayer* pOne);
        bool ct_server_time(struct CPlayer* pOne);
        bool ct_set_animus_exp(struct CPlayer* pOne);
        bool ct_set_animus_lv(struct CPlayer* pOne);
        bool ct_set_damage_part(struct CPlayer* pOne);
        bool ct_set_exp_rate(struct CPlayer* pOne);
        bool ct_set_guildbattle_color(struct CPlayer* pOne);
        bool ct_set_hfs_full(struct CPlayer* pOne);
        bool ct_set_hp(struct CPlayer* pOne);
        bool ct_set_jade_effect(struct CPlayer* pOne);
        bool ct_set_kill_list_init(struct CPlayer* pOne);
        bool ct_set_loot_free(struct CPlayer* pOne);
        bool ct_set_make_succ(struct CPlayer* pOne);
        bool ct_set_matchless(struct CPlayer* pOne);
        bool ct_set_never_die(struct CPlayer* pOne);
        bool ct_set_ore_amount(struct CPlayer* pOne);
        bool ct_set_temp_cash_point(struct CPlayer* pOne);
        bool ct_sfmastery(struct CPlayer* pOne);
        bool ct_start_cri(struct CPlayer* pOne);
        bool ct_start_keeper(struct CPlayer* pOne);
        bool ct_take_gravitystone(struct CPlayer* pOne);
        bool ct_takeholymental(struct CPlayer* pOne);
        bool ct_telekinesis(struct CPlayer* pOne);
        bool ct_tl_info_set(struct CPlayer* pOne);
        bool ct_tl_info_view(struct CPlayer* pOne);
        bool ct_tl_system_setting(struct CPlayer* pOne);
        bool ct_tracing_hide(struct CPlayer* pOne);
        bool ct_tracing_show(struct CPlayer* pOne);
        bool ct_trap_attack_grade(struct CPlayer* pOne);
        bool ct_trunk_init(struct CPlayer* pOne);
        bool ct_up_allskill(struct CPlayer* pOne);
        bool ct_up_allskill_pt(struct CPlayer* pOne);
        bool ct_up_forceitem(struct CPlayer* pOne);
        bool ct_up_forcemastery(struct CPlayer* pOne);
        bool ct_up_skill(struct CPlayer* pOne);
        bool ct_user_num(struct CPlayer* pOne);
        bool ct_userchatban(struct CPlayer* pOne);
        bool ct_ut_cancel_regist(struct CPlayer* pOne);
        bool ct_ut_cancel_registlogout(struct CPlayer* pOne);
        bool ct_view_method(struct CPlayer* pOne);
        bool ct_vote_enable(struct CPlayer* pOne);
        bool ct_whoami(struct CPlayer* pOne);
        char* cvt_string(int nVal);
        void eAddCutOre(int nRaceCode, char byKind, int nAdd);
        void eAddDalant(int nRaceCode, int nAdd);
        void eAddGold(int nRaceCode, int nAdd);
        void eAddMineOre(int nRaceCode, char byKind, int nAdd);
        long double eGetCutOre(int nRaceCode, char byKind);
        long double eGetDalant(int nRaceCode);
        long double eGetGold(int nRaceCode);
        uint16_t eGetGuide(int nRaceCode);
        struct _economy_history_data* eGetGuideHistory();
        unsigned int eGetLocalDate();
        int eGetMgrValue();
        long double eGetMineOre(int nRaceCode, char byKind);
        long double eGetOldCutOre(int nRaceCode, char byKind);
        long double eGetOldDalant(int nRaceCode);
        long double eGetOldGold(int nRaceCode);
        long double eGetOldMineOre(int nRaceCode, char byKind);
        float eGetOreRate(int nRaceCode);
        int eGetRate(int nRaceCode);
        float eGetTex(int nRaceCode);
        unsigned int eGetTexRate(int nRaceCode);
        bool eInitEconomySystem(int nCurMgrValue, int nNextMgrValue, struct _economy_history_data* pData, int nHisNum, struct _economy_history_data* pCurData);
        void eUpdateEconomySystem(bool* pbChangeDay);
        bool jc_Contents(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool jc_Count(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool jc_Description(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool jc_ReactContents(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool jc_ReactType(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool jc_Type(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        lua_State* lua_my_open();
        bool mc_AddMonster(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_AddTime(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_Area(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_ChangeMonster(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_CompleteMsg(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_Description(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_GatePos(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_If(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_Inner(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_JobOrder(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_LimTimeSec(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_LootItem(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_RespawnMonster(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_RespawnMonsterOption(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_ResultContents(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_ResultType(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_StartPos(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool mc_respond(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_Dalant(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_Description(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_DummyBlock(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_LimitLvMax(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_LimitLvMin(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_MemberNum(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_MembershipParty(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_RewardExp(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_RewardItem(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_RewardPvp(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_StartMission(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_UseMap(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        bool qc_monsterGroup(struct strFILE* fstr, struct CDarkHoleDungeonQuestSetup* pSetup, char* pszoutErrMsg);
        void wa_EnterWorld(struct _WA_AVATOR_CODE* pData, uint16_t wZoneIndex);
        void wa_ExitWorld(struct _CLID* pidWorld);
        void wa_PartyDisjoint(struct _CLID* pidBoss);
        void wa_PartyForceLeave(struct _CLID* pidBoss, struct _CLID* pidLeaver);
        void wa_PartyJoin(struct _CLID* pidBoss, struct _CLID* pidJoiner);
        void wa_PartyLock(struct _CLID* pidBoss, bool bLock);
        void wa_PartyLootShareSystem(struct _CLID* pidBoss, char byLootShareMode);
        void wa_PartySelfLeave(struct _CLID* pidLeaver);
        void wa_PartySuccession(struct _CLID* pidBoss, struct _CLID* pidSuccessor);
    }; // end namespace Global
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMHDR.hpp>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    struct tagNMMOUSE
    {
        tagNMHDR hdr;
        unsigned __int64 dwItemSpec;
        unsigned __int64 dwItemData;
        tagPOINT pt;
        __int64 dwHitInfo;
    };
END_ATF_NAMESPACE

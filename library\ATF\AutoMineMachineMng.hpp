// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AutoMineMachine.hpp>
#include <CGuild.hpp>
#include <CPlayer.hpp>


START_ATF_NAMESPACE
    struct AutoMineMachineMng
    {
        AutoMineMachine m_Machine[3][2];
    public:
        AutoMineMachineMng();
        void ctor_AutoMineMachineMng();
        bool BatteryCharge(int n, char* pMsg);
        void ChangeOwner(int nRaceCode, struct CGuild* pGuild, char byCollisionType);
        bool CloseUI(int n);
        bool Command(int n, int nType, char* pMsg);
        struct AutoMineMachine* GetMachine(char byRace, char byCollisionType);
        bool GetOutOre(int n, char* pMsg);
        bool Initialzie();
        static struct AutoMineMachineMng* Instance();
        void Loop();
        bool MoveOrePos(int n, char* pMsg);
        bool OpenUI(int n);
        bool OreMerge(int n, char* pMsg);
        static void Release();
        bool SelectOreType(int n, char* pMsg);
        void SendMsg_ResultCode(int n, char byType, char byRetCode);
        bool StartWorkMachine(int n);
        bool StopWorkMachine(int n);
        char _db_qry_insert_newowner(char* pdata);
        char _db_qry_update_battery_charge(char* pdata);
        char _db_qry_update_battery_discharge(char* pdata);
        char _db_qry_update_mineore(char* pdata);
        char _db_qry_update_moveore(char* pdata);
        char _db_qry_update_selore(char* pdata);
        char _db_qry_update_workstate(char* pdata);
        char get_type(struct CPlayer* pUser, char byRace);
        char request_db_query(char* pdata);
        void result_db_query(char byRet, char* pdata);
        ~AutoMineMachineMng();
        void dtor_AutoMineMachineMng();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _FILE_NOTIFY_INFORMATION
    {
        unsigned int NextEntryOffset;
        unsigned int Action;
        unsigned int FileNameLength;
        wchar_t FileName[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

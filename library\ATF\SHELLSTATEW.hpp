// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct SHELLSTATEW
    {
        __int32 fShowAllObjects : 1;
        __int32 fShowExtensions : 1;
        __int32 fNoConfirmRecycle : 1;
        __int32 fShowSysFiles : 1;
        __int32 fShowCompColor : 1;
        __int32 fDoubleClickInWebView : 1;
        __int32 fDesktopHTML : 1;
        __int32 fWin95Classic : 1;
        __int32 fDontPrettyPath : 1;
        __int32 fShowAttribCol : 1;
        __int32 fMapNetDrvBtn : 1;
        __int32 fShowInfoTip : 1;
        __int32 fHideIcons : 1;
        __int32 fWebView : 1;
        __int32 fFilter : 1;
        __int32 fShowSuperHidden : 1;
        __int32 fNoNetCrawling : 1;
        unsigned int dwWin95Unused;
        unsigned int uWin95Unused;
        int lParamSort;
        int iSortDirection;
        unsigned int version;
        unsigned int uNotUsed;
        __int32 fSepProcess : 1;
        __int32 fStartPanelOn : 1;
        __int32 fShowStartPage : 1;
        unsigned __int32 fSpareFlags : 13;
    };
END_ATF_NAMESPACE

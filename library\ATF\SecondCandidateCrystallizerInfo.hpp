// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <SecondCandidateCrystallizer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using SecondCandidateCrystallizerDoit2_ptr = int (WINAPIV*)(struct SecondCandidateCrystallizer*, Cmd, struct CPlayer*, char*);
        using SecondCandidateCrystallizerDoit2_clbk = int (WINAPIV*)(struct SecondCandidateCrystallizer*, Cmd, struct CPlayer*, char*, SecondCandidateCrystallizerDoit2_ptr);
        using SecondCandidateCrystallizerInitialize4_ptr = bool (WINAPIV*)(struct SecondCandidateCrystallizer*);
        using SecondCandidateCrystallizerInitialize4_clbk = bool (WINAPIV*)(struct SecondCandidateCrystallizer*, SecondCandidateCrystallizerInitialize4_ptr);
        
        using SecondCandidateCrystallizerctor_SecondCandidateCrystallizer6_ptr = void (WINAPIV*)(struct SecondCandidateCrystallizer*);
        using SecondCandidateCrystallizerctor_SecondCandidateCrystallizer6_clbk = void (WINAPIV*)(struct SecondCandidateCrystallizer*, SecondCandidateCrystallizerctor_SecondCandidateCrystallizer6_ptr);
        
        using SecondCandidateCrystallizerdtor_SecondCandidateCrystallizer11_ptr = void (WINAPIV*)(struct SecondCandidateCrystallizer*);
        using SecondCandidateCrystallizerdtor_SecondCandidateCrystallizer11_clbk = void (WINAPIV*)(struct SecondCandidateCrystallizer*, SecondCandidateCrystallizerdtor_SecondCandidateCrystallizer11_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

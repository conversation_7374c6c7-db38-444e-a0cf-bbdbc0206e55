// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RACE_BOSS_MSG__CMsg.hpp>


START_ATF_NAMESPACE
    namespace RACE_BOSS_MSG
    {
        namespace Info
        {
            
            using RACE_BOSS_MSG__CMsgctor_CMsg2_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, char, unsigned int);
            using RACE_BOSS_MSG__CMsgctor_CMsg2_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, char, unsigned int, RACE_BOSS_MSG__CMsgctor_CMsg2_ptr);
            using RACE_BOSS_MSG__CMsgClear4_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgClear4_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgClear4_ptr);
            using RACE_BOSS_MSG__CMsgGetBossName6_ptr = char* (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgGetBossName6_clbk = char* (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgGetBossName6_ptr);
            using RACE_BOSS_MSG__CMsgGetID8_ptr = unsigned int (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgGetID8_clbk = unsigned int (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgGetID8_ptr);
            using RACE_BOSS_MSG__CMsgGetMsg10_ptr = char* (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgGetMsg10_clbk = char* (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgGetMsg10_ptr);
            using RACE_BOSS_MSG__CMsgGetSerial12_ptr = unsigned int (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgGetSerial12_clbk = unsigned int (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgGetSerial12_ptr);
            using RACE_BOSS_MSG__CMsgGetWebDBID14_ptr = unsigned int (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgGetWebDBID14_clbk = unsigned int (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgGetWebDBID14_ptr);
            using RACE_BOSS_MSG__CMsgIsDayChanged16_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgIsDayChanged16_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgIsDayChanged16_ptr);
            using RACE_BOSS_MSG__CMsgIsSendFromWeb18_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgIsSendFromWeb18_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgIsSendFromWeb18_ptr);
            using RACE_BOSS_MSG__CMsgIsSendTime20_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgIsSendTime20_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgIsSendTime20_ptr);
            using RACE_BOSS_MSG__CMsgIsWait22_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgIsWait22_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgIsWait22_ptr);
            using RACE_BOSS_MSG__CMsgLoad24_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, char, unsigned int);
            using RACE_BOSS_MSG__CMsgLoad24_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, char, unsigned int, RACE_BOSS_MSG__CMsgLoad24_ptr);
            using RACE_BOSS_MSG__CMsgSave26_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, char);
            using RACE_BOSS_MSG__CMsgSave26_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, char, RACE_BOSS_MSG__CMsgSave26_ptr);
            using RACE_BOSS_MSG__CMsgSet28_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, unsigned int, char*, char*, unsigned int);
            using RACE_BOSS_MSG__CMsgSet28_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, unsigned int, char*, char*, unsigned int, RACE_BOSS_MSG__CMsgSet28_ptr);
            using RACE_BOSS_MSG__CMsgSetDayChanged30_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgSetDayChanged30_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgSetDayChanged30_ptr);
            using RACE_BOSS_MSG__CMsgSetDone32_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgSetDone32_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgSetDone32_ptr);
            
            using RACE_BOSS_MSG__CMsgdtor_CMsg36_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgdtor_CMsg36_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgdtor_CMsg36_ptr);
        }; // end namespace Info
    }; // end namespace RACE_BOSS_MSG
END_ATF_NAMESPACE

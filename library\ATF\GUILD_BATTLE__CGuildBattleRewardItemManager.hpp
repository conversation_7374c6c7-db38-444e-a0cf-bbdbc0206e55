// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <GUILD_BATTLE__CGuildBattleRewardItem.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleRewardItemManager
        {
            std::vector<CGuildBattleRewardItem,std::allocator<CGuildBattleRewardItem> > m_kItem;
        public:
            CGuildBattleRewardItemManager();
            void ctor_CGuildBattleRewardItemManager();
            struct CGuildBattleRewardItem* Give(struct CPlayer* pkPlayer);
            bool Init();
            static struct CGuildBattleRewardItemManager* Instance();
            ~CGuildBattleRewardItemManager();
            void dtor_CGuildBattleRewardItemManager();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

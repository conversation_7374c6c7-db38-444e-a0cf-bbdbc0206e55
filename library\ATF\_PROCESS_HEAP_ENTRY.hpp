// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$D1308D449603D8C919B6B38A50359ED3.hpp>


START_ATF_NAMESPACE
    struct _PROCESS_HEAP_ENTRY
    {
        void *lpData;
        unsigned int cbData;
        char cbOverhead;
        char iRegionIndex;
        unsigned __int16 wFlags;
        $D1308D449603D8C919B6B38A50359ED3 ___u5;
    };
END_ATF_NAMESPACE

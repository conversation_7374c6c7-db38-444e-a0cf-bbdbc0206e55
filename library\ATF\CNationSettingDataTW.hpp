// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingData.hpp>
#include <CashDbWorker.hpp>
#include <_CashShop_str_fld.hpp>
#include <_NameTxt_fld.hpp>


START_ATF_NAMESPACE
    struct  CNationSettingDataTW : CNationSettingData
    {
    public:
        CNationSettingDataTW();
        void ctor_CNationSettingDataTW();
        struct CashDbWorker* CreateWorker();
        int GetCashItemPrice(struct _CashShop_str_fld* pFld);
        char* GetItemName(struct _NameTxt_fld* pFld);
        int Init();
        bool IsPersonalFreeFixedAmountBillingType(int16_t* pDest1, int16_t* pDest2);
        bool ReadSystemPass();
        bool ValidMacAddress();
    };
END_ATF_NAMESPACE

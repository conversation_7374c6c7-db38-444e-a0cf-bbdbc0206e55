// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetIndexList.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNetIndexListctor_CNetIndexList2_ptr = void (WINAPIV*)(struct CNetIndexList*);
        using CNetIndexListctor_CNetIndexList2_clbk = void (WINAPIV*)(struct CNetIndexList*, CNetIndexListctor_CNetIndexList2_ptr);
        using CNetIndexListCopyFront4_ptr = bool (WINAPIV*)(struct CNetIndexList*, unsigned int*);
        using CNetIndexListCopyFront4_clbk = bool (WINAPIV*)(struct CNetIndexList*, unsigned int*, CNetIndexListCopyFront4_ptr);
        using CNetIndexListCopyIndexList6_ptr = int (WINAPIV*)(struct CNetIndexList*, unsigned int*, int);
        using CNetIndexListCopyIndexList6_clbk = int (WINAPIV*)(struct CNetIndexList*, unsigned int*, int, CNetIndexListCopyIndexList6_ptr);
        using CNetIndexListFindNode8_ptr = struct CNetIndexList::_index_node* (WINAPIV*)(struct CNetIndexList*, unsigned int);
        using CNetIndexListFindNode8_clbk = struct CNetIndexList::_index_node* (WINAPIV*)(struct CNetIndexList*, unsigned int, CNetIndexListFindNode8_ptr);
        using CNetIndexListIsInList10_ptr = bool (WINAPIV*)(struct CNetIndexList*, unsigned int);
        using CNetIndexListIsInList10_clbk = bool (WINAPIV*)(struct CNetIndexList*, unsigned int, CNetIndexListIsInList10_ptr);
        using CNetIndexListPopNode_Back12_ptr = bool (WINAPIV*)(struct CNetIndexList*, unsigned int*);
        using CNetIndexListPopNode_Back12_clbk = bool (WINAPIV*)(struct CNetIndexList*, unsigned int*, CNetIndexListPopNode_Back12_ptr);
        using CNetIndexListPopNode_Front14_ptr = bool (WINAPIV*)(struct CNetIndexList*, unsigned int*);
        using CNetIndexListPopNode_Front14_clbk = bool (WINAPIV*)(struct CNetIndexList*, unsigned int*, CNetIndexListPopNode_Front14_ptr);
        using CNetIndexListPushNode_Back16_ptr = bool (WINAPIV*)(struct CNetIndexList*, unsigned int);
        using CNetIndexListPushNode_Back16_clbk = bool (WINAPIV*)(struct CNetIndexList*, unsigned int, CNetIndexListPushNode_Back16_ptr);
        using CNetIndexListPushNode_Front18_ptr = bool (WINAPIV*)(struct CNetIndexList*, unsigned int);
        using CNetIndexListPushNode_Front18_clbk = bool (WINAPIV*)(struct CNetIndexList*, unsigned int, CNetIndexListPushNode_Front18_ptr);
        using CNetIndexListResetList20_ptr = void (WINAPIV*)(struct CNetIndexList*);
        using CNetIndexListResetList20_clbk = void (WINAPIV*)(struct CNetIndexList*, CNetIndexListResetList20_ptr);
        using CNetIndexListSetList22_ptr = bool (WINAPIV*)(struct CNetIndexList*, unsigned int);
        using CNetIndexListSetList22_clbk = bool (WINAPIV*)(struct CNetIndexList*, unsigned int, CNetIndexListSetList22_ptr);
        using CNetIndexListsize26_ptr = int (WINAPIV*)(struct CNetIndexList*);
        using CNetIndexListsize26_clbk = int (WINAPIV*)(struct CNetIndexList*, CNetIndexListsize26_ptr);
        
        using CNetIndexListdtor_CNetIndexList28_ptr = void (WINAPIV*)(struct CNetIndexList*);
        using CNetIndexListdtor_CNetIndexList28_clbk = void (WINAPIV*)(struct CNetIndexList*, CNetIndexListdtor_CNetIndexList28_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

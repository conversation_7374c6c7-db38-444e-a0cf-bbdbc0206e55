// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _NameChangeBuddyInfo
    {
        bool bNameChange;
        int nSendNum;
    public:
        void Init();
        _NameChangeBuddyInfo();
        void ctor__NameChangeBuddyInfo();
    };    
    static_assert(ATF::checkSize<_NameChangeBuddyInfo, 8>(), "_NameChangeBuddyInfo");
END_ATF_NAMESPACE

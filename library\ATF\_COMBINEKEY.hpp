// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _COMBINEKEY
    {
        char byRewardIndex;
        char byTableCode;
        unsigned __int16 wItemIndex;
    public:
        int CovDBKey();
        bool IsFilled();
        void LoadDBKey(int pl_nKey);
        void SetRelease();
    };    
    static_assert(ATF::checkSize<_COMBINEKEY, 4>(), "_COMBINEKEY");
END_ATF_NAMESPACE

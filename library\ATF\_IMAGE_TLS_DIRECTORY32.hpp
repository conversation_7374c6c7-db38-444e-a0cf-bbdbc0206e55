// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_TLS_DIRECTORY32
    {
        unsigned int StartAddressOfRawData;
        unsigned int EndAddressOfRawData;
        unsigned int AddressOfIndex;
        unsigned int AddressOfCallBacks;
        unsigned int SizeOfZeroFill;
        unsigned int Characteristics;
    };
END_ATF_NAMESPACE

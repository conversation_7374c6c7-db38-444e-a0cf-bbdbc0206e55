// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SOUND_ENTITIES_LIST.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _SOUND_ENTITIES_LISTGetBoxIntensity1_ptr = float (WINAPIV*)(struct _SOUND_ENTITIES_LIST*, float*);
        using _SOUND_ENTITIES_LISTGetBoxIntensity1_clbk = float (WINAPIV*)(struct _SOUND_ENTITIES_LIST*, float*, _SOUND_ENTITIES_LISTGetBoxIntensity1_ptr);
        using _SOUND_ENTITIES_LISTGetPan2_ptr = float (WINAPIV*)(struct _SOUND_ENTITIES_LIST*, float*);
        using _SOUND_ENTITIES_LISTGetPan2_clbk = float (WINAPIV*)(struct _SOUND_ENTITIES_LIST*, float*, _SOUND_ENTITIES_LISTGetPan2_ptr);
        using _SOUND_ENTITIES_LISTGetVolume3_ptr = float (WINAPIV*)(struct _SOUND_ENTITIES_LIST*, float*);
        using _SOUND_ENTITIES_LISTGetVolume3_clbk = float (WINAPIV*)(struct _SOUND_ENTITIES_LIST*, float*, _SOUND_ENTITIES_LISTGetVolume3_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBuffInfoByHolyQuestList.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CRaceBuffInfoByHolyQuestListApply2_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, unsigned int, int, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestListApply2_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, unsigned int, int, struct CPlayer*, CRaceBuffInfoByHolyQuestListApply2_ptr);
        
        using CRaceBuffInfoByHolyQuestListctor_CRaceBuffInfoByHolyQuestList4_ptr = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*);
        using CRaceBuffInfoByHolyQuestListctor_CRaceBuffInfoByHolyQuestList4_clbk = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, CRaceBuffInfoByHolyQuestListctor_CRaceBuffInfoByHolyQuestList4_ptr);
        using CRaceBuffInfoByHolyQuestListCreateComplete6_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, unsigned int, int, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestListCreateComplete6_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, unsigned int, int, struct CPlayer*, CRaceBuffInfoByHolyQuestListCreateComplete6_ptr);
        using CRaceBuffInfoByHolyQuestListGetMaxThCnt8_ptr = unsigned int (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*);
        using CRaceBuffInfoByHolyQuestListGetMaxThCnt8_clbk = unsigned int (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, CRaceBuffInfoByHolyQuestListGetMaxThCnt8_ptr);
        using CRaceBuffInfoByHolyQuestListInit10_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*);
        using CRaceBuffInfoByHolyQuestListInit10_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, CRaceBuffInfoByHolyQuestListInit10_ptr);
        using CRaceBuffInfoByHolyQuestListRelease12_ptr = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, unsigned int, int, struct CPlayer*);
        using CRaceBuffInfoByHolyQuestListRelease12_clbk = bool (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, unsigned int, int, struct CPlayer*, CRaceBuffInfoByHolyQuestListRelease12_ptr);
        
        using CRaceBuffInfoByHolyQuestListdtor_CRaceBuffInfoByHolyQuestList14_ptr = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*);
        using CRaceBuffInfoByHolyQuestListdtor_CRaceBuffInfoByHolyQuestList14_clbk = void (WINAPIV*)(struct CRaceBuffInfoByHolyQuestList*, CRaceBuffInfoByHolyQuestListdtor_CRaceBuffInfoByHolyQuestList14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

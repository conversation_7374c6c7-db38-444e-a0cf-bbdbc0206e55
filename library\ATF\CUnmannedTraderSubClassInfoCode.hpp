// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CUnmannedTraderItemCodeInfo.hpp>
#include <CUnmannedTraderSubClassInfo.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct  CUnmannedTraderSubClassInfoCode : CUnmannedTraderSubClassInfo
    {
        std::vector<CUnmannedTraderItemCodeInfo> m_vecCodeList;
    public:
        CUnmannedTraderSubClassInfoCode(unsigned int dwID);
        void ctor_CUnmannedTraderSubClassInfoCode(unsigned int dwID);
        struct CUnmannedTraderSubClassInfo* Create(unsigned int dwID);
        bool GetGroupID(char byTableCode, uint16_t wItemTableIndex, char* bySubClass);
        bool LoadXML(struct TiXmlElement* elemSubClass, struct CLogFile* kLogger, unsigned int dwDivisionID, unsigned int dwClassID);
        ~CUnmannedTraderSubClassInfoCode();
        void dtor_CUnmannedTraderSubClassInfoCode();
    };
END_ATF_NAMESPACE

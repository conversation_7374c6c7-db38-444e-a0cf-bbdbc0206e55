// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct IFixedStringLogVtbl
        {
            void (WINAPIV *OnAllocateSpill)(IFixedStringLog *_this, int, int, CStringData *);
            void (WINAPIV *OnReallocateSpill)(IFixedStringLog *_this, int, int, CStringData *);
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CInternetSession.hpp>
#include <CObject.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CInternetConnection : CObject
    {
        void *m_hConnection;
        unsigned __int64 m_dwContext;
        CInternetSession *m_pSession;
        ATL::CStringT<char> m_strServerName;
        unsigned __int16 m_nPort;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPlayerAddDalant2_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, bool);
        using CPlayerAddDalant2_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, bool, CPlayerAddDalant2_ptr);
        using CPlayerAddGold4_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, bool);
        using CPlayerAddGold4_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, bool, CPlayerAddGold4_ptr);
        using CPlayerAlterDalant6_ptr = void (WINAPIV*)(struct CPlayer*, long double);
        using CPlayerAlterDalant6_clbk = void (WINAPIV*)(struct CPlayer*, long double, CPlayerAlterDalant6_ptr);
        using CPlayerAlterExp8_ptr = void (WINAPIV*)(struct CPlayer*, long double, bool, bool, bool);
        using CPlayerAlterExp8_clbk = void (WINAPIV*)(struct CPlayer*, long double, bool, bool, bool, CPlayerAlterExp8_ptr);
        using CPlayerAlterExp_Animus10_ptr = void (WINAPIV*)(struct CPlayer*, int64_t);
        using CPlayerAlterExp_Animus10_clbk = void (WINAPIV*)(struct CPlayer*, int64_t, CPlayerAlterExp_Animus10_ptr);
        using CPlayerAlterExp_Potion12_ptr = void (WINAPIV*)(struct CPlayer*, long double);
        using CPlayerAlterExp_Potion12_clbk = void (WINAPIV*)(struct CPlayer*, long double, CPlayerAlterExp_Potion12_ptr);
        using CPlayerAlterFP_Animus14_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerAlterFP_Animus14_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerAlterFP_Animus14_ptr);
        using CPlayerAlterGold16_ptr = void (WINAPIV*)(struct CPlayer*, long double);
        using CPlayerAlterGold16_clbk = void (WINAPIV*)(struct CPlayer*, long double, CPlayerAlterGold16_ptr);
        using CPlayerAlterHP_Animus18_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerAlterHP_Animus18_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerAlterHP_Animus18_ptr);
        using CPlayerAlterMaxLevel20_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerAlterMaxLevel20_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerAlterMaxLevel20_ptr);
        using CPlayerAlterMode_Animus22_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerAlterMode_Animus22_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerAlterMode_Animus22_ptr);
        using CPlayerAlterPvPCashBag24_ptr = void (WINAPIV*)(struct CPlayer*, long double, PVP_MONEY_ALTER_TYPE);
        using CPlayerAlterPvPCashBag24_clbk = void (WINAPIV*)(struct CPlayer*, long double, PVP_MONEY_ALTER_TYPE, CPlayerAlterPvPCashBag24_ptr);
        using CPlayerAlterPvPPoint26_ptr = void (WINAPIV*)(struct CPlayer*, long double, PVP_ALTER_TYPE, unsigned int);
        using CPlayerAlterPvPPoint26_clbk = void (WINAPIV*)(struct CPlayer*, long double, PVP_ALTER_TYPE, unsigned int, CPlayerAlterPvPPoint26_ptr);
        using CPlayerAlterPvpPointLeak28_ptr = void (WINAPIV*)(struct CPlayer*, long double);
        using CPlayerAlterPvpPointLeak28_clbk = void (WINAPIV*)(struct CPlayer*, long double, CPlayerAlterPvpPointLeak28_ptr);
        using CPlayerAlterSec30_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerAlterSec30_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerAlterSec30_ptr);
        using CPlayerApplyEquipItemEffect32_ptr = bool (WINAPIV*)(struct CPlayer*, int, bool);
        using CPlayerApplyEquipItemEffect32_clbk = bool (WINAPIV*)(struct CPlayer*, int, bool, CPlayerApplyEquipItemEffect32_ptr);
        using CPlayerApplySetItemEffect34_ptr = void (WINAPIV*)(struct CPlayer*, struct si_interpret*, unsigned int, char, char, bool);
        using CPlayerApplySetItemEffect34_clbk = void (WINAPIV*)(struct CPlayer*, struct si_interpret*, unsigned int, char, char, bool, CPlayerApplySetItemEffect34_ptr);
        using CPlayerAttackableHeight36_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerAttackableHeight36_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerAttackableHeight36_ptr);
        using CPlayerAutoCharge_Booster38_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerAutoCharge_Booster38_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerAutoCharge_Booster38_ptr);
        using CPlayerAutoRecover40_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerAutoRecover40_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerAutoRecover40_ptr);
        using CPlayerAutoRecover_Animus42_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerAutoRecover_Animus42_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerAutoRecover_Animus42_ptr);
        using CPlayerBilling_Logout44_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerBilling_Logout44_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerBilling_Logout44_ptr);
        using CPlayerBreakCloakBooster46_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerBreakCloakBooster46_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerBreakCloakBooster46_ptr);
        
        using CPlayerctor_CPlayer48_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerctor_CPlayer48_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerctor_CPlayer48_ptr);
        using CPlayerCalPvpCashPoint50_ptr = long double (WINAPIV*)(struct CPlayer*, int, int, char*);
        using CPlayerCalPvpCashPoint50_clbk = long double (WINAPIV*)(struct CPlayer*, int, int, char*, CPlayerCalPvpCashPoint50_ptr);
        using CPlayerCalPvpTempCash52_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, char);
        using CPlayerCalPvpTempCash52_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, char, CPlayerCalPvpTempCash52_ptr);
        using CPlayerCalcAddPointByClass54_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCalcAddPointByClass54_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCalcAddPointByClass54_ptr);
        using CPlayerCalcCurFPRate56_ptr = uint16_t (WINAPIV*)(struct CPlayer*);
        using CPlayerCalcCurFPRate56_clbk = uint16_t (WINAPIV*)(struct CPlayer*, CPlayerCalcCurFPRate56_ptr);
        using CPlayerCalcCurHPRate58_ptr = uint16_t (WINAPIV*)(struct CPlayer*);
        using CPlayerCalcCurHPRate58_clbk = uint16_t (WINAPIV*)(struct CPlayer*, CPlayerCalcCurHPRate58_ptr);
        using CPlayerCalcCurSPRate60_ptr = uint16_t (WINAPIV*)(struct CPlayer*);
        using CPlayerCalcCurSPRate60_clbk = uint16_t (WINAPIV*)(struct CPlayer*, CPlayerCalcCurSPRate60_ptr);
        using CPlayerCalcDPRate62_ptr = float (WINAPIV*)(struct CPlayer*);
        using CPlayerCalcDPRate62_clbk = float (WINAPIV*)(struct CPlayer*, CPlayerCalcDPRate62_ptr);
        using CPlayerCalcDefTol64_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCalcDefTol64_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCalcDefTol64_ptr);
        using CPlayerCalcEquipAttackDelay66_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerCalcEquipAttackDelay66_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerCalcEquipAttackDelay66_ptr);
        using CPlayerCalcEquipMaxDP68_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerCalcEquipMaxDP68_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerCalcEquipMaxDP68_ptr);
        using CPlayerCalcEquipSpeed70_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCalcEquipSpeed70_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCalcEquipSpeed70_ptr);
        using CPlayerCalcExp72_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, int, struct CPartyModeKillMonsterExpNotify*);
        using CPlayerCalcExp72_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, int, struct CPartyModeKillMonsterExpNotify*, CPlayerCalcExp72_ptr);
        using CPlayerCalcPvP74_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, char);
        using CPlayerCalcPvP74_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, char, CPlayerCalcPvP74_ptr);
        using CPlayerCheckAlterMaxPoint76_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCheckAlterMaxPoint76_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCheckAlterMaxPoint76_ptr);
        using CPlayerCheckBattleMode78_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCheckBattleMode78_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCheckBattleMode78_ptr);
        using CPlayerCheckGroupMapPoint80_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCheckGroupMapPoint80_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCheckGroupMapPoint80_ptr);
        using CPlayerCheckGroupTargeting82_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCheckGroupTargeting82_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCheckGroupTargeting82_ptr);
        using CPlayerCheckMentalTakeAndUpdateLastMetalTicket84_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerCheckMentalTakeAndUpdateLastMetalTicket84_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerCheckMentalTakeAndUpdateLastMetalTicket84_ptr);
        using CPlayerCheckNameChange86_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCheckNameChange86_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCheckNameChange86_ptr);
        using CPlayerCheckPosInTown88_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCheckPosInTown88_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCheckPosInTown88_ptr);
        using CPlayerCheckPos_Region90_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCheckPos_Region90_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCheckPos_Region90_ptr);
        using CPlayerCheckUnitCutTime92_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCheckUnitCutTime92_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCheckUnitCutTime92_ptr);
        using CPlayerCheet_BufEffectEnd94_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCheet_BufEffectEnd94_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCheet_BufEffectEnd94_ptr);
        using CPlayerClearGravityStone96_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerClearGravityStone96_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerClearGravityStone96_ptr);
        using CPlayerCorpse98_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerCorpse98_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerCorpse98_ptr);
        using CPlayerCreate100_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerCreate100_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerCreate100_ptr);
        using CPlayerCreateComplete102_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerCreateComplete102_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerCreateComplete102_ptr);
        using CPlayerDTradeInit104_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerDTradeInit104_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerDTradeInit104_ptr);
        using CPlayerDecHalfSFContDam106_ptr = bool (WINAPIV*)(struct CPlayer*, float);
        using CPlayerDecHalfSFContDam106_clbk = bool (WINAPIV*)(struct CPlayer*, float, CPlayerDecHalfSFContDam106_ptr);
        using CPlayerDelPostData108_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerDelPostData108_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerDelPostData108_ptr);
        using CPlayerDeleteCouponItem110_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, int);
        using CPlayerDeleteCouponItem110_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, int, CPlayerDeleteCouponItem110_ptr);
        using CPlayerDeleteUseConsumeItem112_ptr = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con**, int*, bool*);
        using CPlayerDeleteUseConsumeItem112_clbk = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con**, int*, bool*, CPlayerDeleteUseConsumeItem112_ptr);
        using CPlayerEmb_AddStorage114_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_storage_con*, bool, bool);
        using CPlayerEmb_AddStorage114_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_storage_con*, bool, bool, CPlayerEmb_AddStorage114_ptr);
        using CPlayerEmb_AlterDurPoint116_ptr = unsigned int (WINAPIV*)(struct CPlayer*, char, char, int, bool, bool);
        using CPlayerEmb_AlterDurPoint116_clbk = unsigned int (WINAPIV*)(struct CPlayer*, char, char, int, bool, bool, CPlayerEmb_AlterDurPoint116_ptr);
        using CPlayerEmb_AlterStat118_ptr = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, char, char*, bool);
        using CPlayerEmb_AlterStat118_clbk = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, char, char*, bool, CPlayerEmb_AlterStat118_ptr);
        using CPlayerEmb_AlterStat_F120_ptr = void (WINAPIV*)(struct CPlayer*, char, char, float, char);
        using CPlayerEmb_AlterStat_F120_clbk = void (WINAPIV*)(struct CPlayer*, char, char, float, char, CPlayerEmb_AlterStat_F120_ptr);
        using CPlayerEmb_CheckActForQuest122_ptr = bool (WINAPIV*)(struct CPlayer*, int, char*, uint16_t, bool);
        using CPlayerEmb_CheckActForQuest122_clbk = bool (WINAPIV*)(struct CPlayer*, int, char*, uint16_t, bool, CPlayerEmb_CheckActForQuest122_ptr);
        using CPlayerEmb_CheckActForQuestParty124_ptr = void (WINAPIV*)(struct CPlayer*, int, char*, uint16_t);
        using CPlayerEmb_CheckActForQuestParty124_clbk = void (WINAPIV*)(struct CPlayer*, int, char*, uint16_t, CPlayerEmb_CheckActForQuestParty124_ptr);
        using CPlayerEmb_CompleteQuest126_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char);
        using CPlayerEmb_CompleteQuest126_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, CPlayerEmb_CompleteQuest126_ptr);
        using CPlayerEmb_CreateNPCQuest128_ptr = bool (WINAPIV*)(struct CPlayer*, char*, unsigned int);
        using CPlayerEmb_CreateNPCQuest128_clbk = bool (WINAPIV*)(struct CPlayer*, char*, unsigned int, CPlayerEmb_CreateNPCQuest128_ptr);
        using CPlayerEmb_CreateQuestEvent130_ptr = bool (WINAPIV*)(struct CPlayer*, QUEST_HAPPEN, char*);
        using CPlayerEmb_CreateQuestEvent130_clbk = bool (WINAPIV*)(struct CPlayer*, QUEST_HAPPEN, char*, CPlayerEmb_CreateQuestEvent130_ptr);
        using CPlayerEmb_DelStorage132_ptr = bool (WINAPIV*)(struct CPlayer*, char, char, bool, bool, char*);
        using CPlayerEmb_DelStorage132_clbk = bool (WINAPIV*)(struct CPlayer*, char, char, bool, bool, char*, CPlayerEmb_DelStorage132_ptr);
        using CPlayerEmb_EquipLink134_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerEmb_EquipLink134_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerEmb_EquipLink134_ptr);
        using CPlayerEmb_ItemUpgrade136_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char, unsigned int);
        using CPlayerEmb_ItemUpgrade136_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, unsigned int, CPlayerEmb_ItemUpgrade136_ptr);
        using CPlayerEmb_RidindUnit138_ptr = void (WINAPIV*)(struct CPlayer*, bool, struct CParkingUnit*);
        using CPlayerEmb_RidindUnit138_clbk = void (WINAPIV*)(struct CPlayer*, bool, struct CParkingUnit*, CPlayerEmb_RidindUnit138_ptr);
        using CPlayerEmb_StartQuest140_ptr = bool (WINAPIV*)(struct CPlayer*, char, struct _happen_event_cont*);
        using CPlayerEmb_StartQuest140_clbk = bool (WINAPIV*)(struct CPlayer*, char, struct _happen_event_cont*, CPlayerEmb_StartQuest140_ptr);
        using CPlayerEmb_UpdateStat142_ptr = int (WINAPIV*)(struct CPlayer*, unsigned int, unsigned int, unsigned int);
        using CPlayerEmb_UpdateStat142_clbk = int (WINAPIV*)(struct CPlayer*, unsigned int, unsigned int, unsigned int, CPlayerEmb_UpdateStat142_ptr);
        using CPlayerExitUpdateDataToWorld144_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerExitUpdateDataToWorld144_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerExitUpdateDataToWorld144_ptr);
        using CPlayerExtractStringToTime146_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, struct _SYSTEMTIME*);
        using CPlayerExtractStringToTime146_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, struct _SYSTEMTIME*, CPlayerExtractStringToTime146_ptr);
        using CPlayerFindFarChatPlayerWithTemp148_ptr = struct CPlayer* (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerFindFarChatPlayerWithTemp148_clbk = struct CPlayer* (WINAPIV*)(struct CPlayer*, char*, CPlayerFindFarChatPlayerWithTemp148_ptr);
        using CPlayerFixTargetWhile150_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, unsigned int);
        using CPlayerFixTargetWhile150_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, unsigned int, CPlayerFixTargetWhile150_ptr);
        using CPlayerForcePullUnit152_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerForcePullUnit152_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerForcePullUnit152_ptr);
        using CPlayerGetAddSpeed154_ptr = float (WINAPIV*)(struct CPlayer*);
        using CPlayerGetAddSpeed154_clbk = float (WINAPIV*)(struct CPlayer*, CPlayerGetAddSpeed154_ptr);
        using CPlayerGetAfterEffect156_ptr = struct _sf_continous* (WINAPIV*)(struct CPlayer*);
        using CPlayerGetAfterEffect156_clbk = struct _sf_continous* (WINAPIV*)(struct CPlayer*, CPlayerGetAfterEffect156_ptr);
        using CPlayerGetAttackDP158_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetAttackDP158_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetAttackDP158_ptr);
        using CPlayerGetAttackLevel160_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetAttackLevel160_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetAttackLevel160_ptr);
        using CPlayerGetAttackRange162_ptr = float (WINAPIV*)(struct CPlayer*);
        using CPlayerGetAttackRange162_clbk = float (WINAPIV*)(struct CPlayer*, CPlayerGetAttackRange162_ptr);
        using CPlayerGetAvoidRate164_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetAvoidRate164_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetAvoidRate164_ptr);
        using CPlayerGetBillingType166_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetBillingType166_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetBillingType166_ptr);
        using CPlayerGetBindDummy168_ptr = struct _dummy_position* (WINAPIV*)(struct CPlayer*);
        using CPlayerGetBindDummy168_clbk = struct _dummy_position* (WINAPIV*)(struct CPlayer*, CPlayerGetBindDummy168_ptr);
        using CPlayerGetBindMap170_ptr = struct CMapData* (WINAPIV*)(struct CPlayer*, float*, bool);
        using CPlayerGetBindMap170_clbk = struct CMapData* (WINAPIV*)(struct CPlayer*, float*, bool, CPlayerGetBindMap170_ptr);
        using CPlayerGetBindMapData172_ptr = struct CMapData* (WINAPIV*)(struct CPlayer*);
        using CPlayerGetBindMapData172_clbk = struct CMapData* (WINAPIV*)(struct CPlayer*, CPlayerGetBindMapData172_ptr);
        using CPlayerGetCashAmount174_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetCashAmount174_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetCashAmount174_ptr);
        using CPlayerGetDP176_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetDP176_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetDP176_ptr);
        using CPlayerGetDamageDP178_ptr = int (WINAPIV*)(struct CPlayer*, int);
        using CPlayerGetDamageDP178_clbk = int (WINAPIV*)(struct CPlayer*, int, CPlayerGetDamageDP178_ptr);
        using CPlayerGetDamageLevel180_ptr = int (WINAPIV*)(struct CPlayer*, int);
        using CPlayerGetDamageLevel180_clbk = int (WINAPIV*)(struct CPlayer*, int, CPlayerGetDamageLevel180_ptr);
        using CPlayerGetDefFC182_ptr = int (WINAPIV*)(struct CPlayer*, int, struct CCharacter*, int*);
        using CPlayerGetDefFC182_clbk = int (WINAPIV*)(struct CPlayer*, int, struct CCharacter*, int*, CPlayerGetDefFC182_ptr);
        using CPlayerGetDefFacing184_ptr = float (WINAPIV*)(struct CPlayer*, int);
        using CPlayerGetDefFacing184_clbk = float (WINAPIV*)(struct CPlayer*, int, CPlayerGetDefFacing184_ptr);
        using CPlayerGetDefGap186_ptr = float (WINAPIV*)(struct CPlayer*, int);
        using CPlayerGetDefGap186_clbk = float (WINAPIV*)(struct CPlayer*, int, CPlayerGetDefGap186_ptr);
        using CPlayerGetDefSkill188_ptr = int (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerGetDefSkill188_clbk = int (WINAPIV*)(struct CPlayer*, bool, CPlayerGetDefSkill188_ptr);
        using CPlayerGetEffectEquipCode190_ptr = char (WINAPIV*)(struct CPlayer*, char, char);
        using CPlayerGetEffectEquipCode190_clbk = char (WINAPIV*)(struct CPlayer*, char, char, CPlayerGetEffectEquipCode190_ptr);
        using CPlayerGetFP192_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetFP192_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetFP192_ptr);
        using CPlayerGetFireTol194_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetFireTol194_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetFireTol194_ptr);
        using CPlayerGetGauge196_ptr = int (WINAPIV*)(struct CPlayer*, int);
        using CPlayerGetGauge196_clbk = int (WINAPIV*)(struct CPlayer*, int, CPlayerGetGauge196_ptr);
        using CPlayerGetGenAttackProb198_ptr = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, int, bool);
        using CPlayerGetGenAttackProb198_clbk = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, int, bool, CPlayerGetGenAttackProb198_ptr);
        using CPlayerGetGroupTarget200_ptr = struct CPlayer::__target* (WINAPIV*)(struct CPlayer*, char);
        using CPlayerGetGroupTarget200_clbk = struct CPlayer::__target* (WINAPIV*)(struct CPlayer*, char, CPlayerGetGroupTarget200_ptr);
        using CPlayerGetHP202_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetHP202_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetHP202_ptr);
        using CPlayerGetInitClassCost204_ptr = unsigned int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetInitClassCost204_clbk = unsigned int (WINAPIV*)(struct CPlayer*, CPlayerGetInitClassCost204_ptr);
        using CPlayerGetLevel206_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetLevel206_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetLevel206_ptr);
        using CPlayerGetMasteryCumAfterAttack208_ptr = int (WINAPIV*)(struct CPlayer*, int);
        using CPlayerGetMasteryCumAfterAttack208_clbk = int (WINAPIV*)(struct CPlayer*, int, CPlayerGetMasteryCumAfterAttack208_ptr);
        using CPlayerGetMaxDP210_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetMaxDP210_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetMaxDP210_ptr);
        using CPlayerGetMaxFP212_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetMaxFP212_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetMaxFP212_ptr);
        using CPlayerGetMaxHP214_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetMaxHP214_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetMaxHP214_ptr);
        using CPlayerGetMaxSP216_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetMaxSP216_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetMaxSP216_ptr);
        using CPlayerGetMoney218_ptr = unsigned int (WINAPIV*)(struct CPlayer*, char);
        using CPlayerGetMoney218_clbk = unsigned int (WINAPIV*)(struct CPlayer*, char, CPlayerGetMoney218_ptr);
        using CPlayerGetMoveSpeed220_ptr = float (WINAPIV*)(struct CPlayer*);
        using CPlayerGetMoveSpeed220_clbk = float (WINAPIV*)(struct CPlayer*, CPlayerGetMoveSpeed220_ptr);
        using CPlayerGetObjName222_ptr = char* (WINAPIV*)(struct CPlayer*);
        using CPlayerGetObjName222_clbk = char* (WINAPIV*)(struct CPlayer*, CPlayerGetObjName222_ptr);
        using CPlayerGetObjRace224_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetObjRace224_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetObjRace224_ptr);
        using CPlayerGetPartyExpDistributionRate226_ptr = float (WINAPIV*)(struct CPlayer*, int, int, int);
        using CPlayerGetPartyExpDistributionRate226_clbk = float (WINAPIV*)(struct CPlayer*, int, int, int, CPlayerGetPartyExpDistributionRate226_ptr);
        using CPlayerGetPvpOrderView228_ptr = struct CPvpOrderView* (WINAPIV*)(struct CPlayer*);
        using CPlayerGetPvpOrderView228_clbk = struct CPvpOrderView* (WINAPIV*)(struct CPlayer*, CPlayerGetPvpOrderView228_ptr);
        using CPlayerGetPvpPointLeak230_ptr = long double (WINAPIV*)(struct CPlayer*);
        using CPlayerGetPvpPointLeak230_clbk = long double (WINAPIV*)(struct CPlayer*, CPlayerGetPvpPointLeak230_ptr);
        using CPlayerGetPvpPointLimiter232_ptr = struct CPvpPointLimiter* (WINAPIV*)(struct CPlayer*, struct CPvpPointLimiter*);
        using CPlayerGetPvpPointLimiter232_clbk = struct CPvpPointLimiter* (WINAPIV*)(struct CPlayer*, struct CPvpPointLimiter*, CPlayerGetPvpPointLimiter232_ptr);
        using CPlayerGetRecallAnimus234_ptr = struct CAnimus* (WINAPIV*)(struct CPlayer*);
        using CPlayerGetRecallAnimus234_clbk = struct CAnimus* (WINAPIV*)(struct CPlayer*, CPlayerGetRecallAnimus234_ptr);
        using CPlayerGetRewardItems_DarkDungeon236_ptr = int (WINAPIV*)(struct CPlayer*, struct _dh_reward_sub_setup*, struct _STORAGE_LIST::_db_con*, int);
        using CPlayerGetRewardItems_DarkDungeon236_clbk = int (WINAPIV*)(struct CPlayer*, struct _dh_reward_sub_setup*, struct _STORAGE_LIST::_db_con*, int, CPlayerGetRewardItems_DarkDungeon236_ptr);
        using CPlayerGetSP238_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetSP238_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetSP238_ptr);
        using CPlayerGetSoilTol240_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetSoilTol240_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetSoilTol240_ptr);
        using CPlayerGetStateFlag242_ptr = uint64_t (WINAPIV*)(struct CPlayer*);
        using CPlayerGetStateFlag242_clbk = uint64_t (WINAPIV*)(struct CPlayer*, CPlayerGetStateFlag242_ptr);
        using CPlayerGetTargetObj244_ptr = struct CGameObject* (WINAPIV*)(struct CPlayer*);
        using CPlayerGetTargetObj244_clbk = struct CGameObject* (WINAPIV*)(struct CPlayer*, CPlayerGetTargetObj244_ptr);
        using CPlayerGetUseConsumeItem246_ptr = bool (WINAPIV*)(struct CPlayer*, struct _consume_item_list*, uint16_t*, struct _STORAGE_LIST::_db_con**, int*, bool*);
        using CPlayerGetUseConsumeItem246_clbk = bool (WINAPIV*)(struct CPlayer*, struct _consume_item_list*, uint16_t*, struct _STORAGE_LIST::_db_con**, int*, bool*, CPlayerGetUseConsumeItem246_ptr);
        using CPlayerGetVisualVer248_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetVisualVer248_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetVisualVer248_ptr);
        using CPlayerGetWaterTol250_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetWaterTol250_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetWaterTol250_ptr);
        using CPlayerGetWeaponAdjust252_ptr = float (WINAPIV*)(struct CPlayer*);
        using CPlayerGetWeaponAdjust252_clbk = float (WINAPIV*)(struct CPlayer*, CPlayerGetWeaponAdjust252_ptr);
        using CPlayerGetWeaponClass254_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetWeaponClass254_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetWeaponClass254_ptr);
        using CPlayerGetWeaponRange256_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetWeaponRange256_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetWeaponRange256_ptr);
        using CPlayerGetWidth258_ptr = float (WINAPIV*)(struct CPlayer*);
        using CPlayerGetWidth258_clbk = float (WINAPIV*)(struct CPlayer*, CPlayerGetWidth258_ptr);
        using CPlayerGetWindTol260_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayerGetWindTol260_clbk = int (WINAPIV*)(struct CPlayer*, CPlayerGetWindTol260_ptr);
        using CPlayerGuild_Buy_Emblem_Complete262_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using CPlayerGuild_Buy_Emblem_Complete262_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, CPlayerGuild_Buy_Emblem_Complete262_ptr);
        using CPlayerGuild_Disjoint_Complete264_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using CPlayerGuild_Disjoint_Complete264_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, CPlayerGuild_Disjoint_Complete264_ptr);
        using CPlayerGuild_Force_Leave_Complete266_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using CPlayerGuild_Force_Leave_Complete266_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, CPlayerGuild_Force_Leave_Complete266_ptr);
        using CPlayerGuild_Insert_Complete268_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using CPlayerGuild_Insert_Complete268_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, CPlayerGuild_Insert_Complete268_ptr);
        using CPlayerGuild_Join_Accept_Complete270_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using CPlayerGuild_Join_Accept_Complete270_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, CPlayerGuild_Join_Accept_Complete270_ptr);
        using CPlayerGuild_Pop_Money_Complete272_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using CPlayerGuild_Pop_Money_Complete272_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, CPlayerGuild_Pop_Money_Complete272_ptr);
        using CPlayerGuild_Push_Money_Complete274_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using CPlayerGuild_Push_Money_Complete274_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, CPlayerGuild_Push_Money_Complete274_ptr);
        using CPlayerGuild_Self_Leave_Complete276_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using CPlayerGuild_Self_Leave_Complete276_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, CPlayerGuild_Self_Leave_Complete276_ptr);
        using CPlayerGuild_Update_GuildMater_Complete278_ptr = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*);
        using CPlayerGuild_Update_GuildMater_Complete278_clbk = void (WINAPIV*)(struct _DB_QRY_SYN_DATA*, CPlayerGuild_Update_GuildMater_Complete278_ptr);
        using CPlayerHSKQuestEnd_Att280_ptr = void (WINAPIV*)(struct CPlayer*, char, struct CPlayer*);
        using CPlayerHSKQuestEnd_Att280_clbk = void (WINAPIV*)(struct CPlayer*, char, struct CPlayer*, CPlayerHSKQuestEnd_Att280_ptr);
        using CPlayerHideNameEffect282_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerHideNameEffect282_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerHideNameEffect282_ptr);
        using CPlayerIncCriEffKillPoint284_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerIncCriEffKillPoint284_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerIncCriEffKillPoint284_ptr);
        using CPlayerIncCriEffPvPCashBag286_ptr = void (WINAPIV*)(struct CPlayer*, long double);
        using CPlayerIncCriEffPvPCashBag286_clbk = void (WINAPIV*)(struct CPlayer*, long double, CPlayerIncCriEffPvPCashBag286_ptr);
        using CPlayerIncPvPPoint288_ptr = void (WINAPIV*)(struct CPlayer*, long double, PVP_ALTER_TYPE, unsigned int);
        using CPlayerIncPvPPoint288_clbk = void (WINAPIV*)(struct CPlayer*, long double, PVP_ALTER_TYPE, unsigned int, CPlayerIncPvPPoint288_ptr);
        using CPlayerInit290_ptr = bool (WINAPIV*)(struct CPlayer*, struct _object_id*);
        using CPlayerInit290_clbk = bool (WINAPIV*)(struct CPlayer*, struct _object_id*, CPlayerInit290_ptr);
        using CPlayerIntoMap292_ptr = bool (WINAPIV*)(struct CPlayer*, char);
        using CPlayerIntoMap292_clbk = bool (WINAPIV*)(struct CPlayer*, char, CPlayerIntoMap292_ptr);
        using CPlayerIsActingSiegeMode294_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsActingSiegeMode294_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsActingSiegeMode294_ptr);
        using CPlayerIsApplyPcbangPrimium296_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsApplyPcbangPrimium296_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsApplyPcbangPrimium296_ptr);
        using CPlayerIsBeAttackedAble298_ptr = bool (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerIsBeAttackedAble298_clbk = bool (WINAPIV*)(struct CPlayer*, bool, CPlayerIsBeAttackedAble298_ptr);
        using CPlayerIsBeDamagedAble300_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerIsBeDamagedAble300_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerIsBeDamagedAble300_ptr);
        using CPlayerIsBulletValidity302_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerIsBulletValidity302_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerIsBulletValidity302_ptr);
        using CPlayerIsChaosMode304_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsChaosMode304_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsChaosMode304_ptr);
        using CPlayerIsEffBulletValidity306_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerIsEffBulletValidity306_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerIsEffBulletValidity306_ptr);
        using CPlayerIsEffectableEquip308_ptr = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_storage_con*);
        using CPlayerIsEffectableEquip308_clbk = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_storage_con*, CPlayerIsEffectableEquip308_ptr);
        using CPlayerIsEquipAbleGrade310_ptr = bool (WINAPIV*)(struct CPlayer*, char);
        using CPlayerIsEquipAbleGrade310_clbk = bool (WINAPIV*)(struct CPlayer*, char, CPlayerIsEquipAbleGrade310_ptr);
        using CPlayerIsHaveMentalTicket312_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsHaveMentalTicket312_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsHaveMentalTicket312_ptr);
        using CPlayerIsInTown314_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsInTown314_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsInTown314_ptr);
        using CPlayerIsLastAttBuff316_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsLastAttBuff316_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsLastAttBuff316_ptr);
        using CPlayerIsMapLoading318_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsMapLoading318_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsMapLoading318_ptr);
        using CPlayerIsMineMode320_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsMineMode320_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsMineMode320_ptr);
        using CPlayerIsMiningByMinigTicket322_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsMiningByMinigTicket322_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsMiningByMinigTicket322_ptr);
        using CPlayerIsOutExtraStopPos324_ptr = bool (WINAPIV*)(struct CPlayer*, float*);
        using CPlayerIsOutExtraStopPos324_clbk = bool (WINAPIV*)(struct CPlayer*, float*, CPlayerIsOutExtraStopPos324_ptr);
        using CPlayerIsOverOneDay326_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsOverOneDay326_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsOverOneDay326_ptr);
        using CPlayerIsPassExpLimitLvDiff328_ptr = bool (WINAPIV*)(struct CPlayer*, int, bool*);
        using CPlayerIsPassExpLimitLvDiff328_clbk = bool (WINAPIV*)(struct CPlayer*, int, bool*, CPlayerIsPassExpLimitLvDiff328_ptr);
        using CPlayerIsPassMasteryLimitLvDiff330_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayerIsPassMasteryLimitLvDiff330_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayerIsPassMasteryLimitLvDiff330_ptr);
        using CPlayerIsPunished332_ptr = bool (WINAPIV*)(struct CPlayer*, char, bool);
        using CPlayerIsPunished332_clbk = bool (WINAPIV*)(struct CPlayer*, char, bool, CPlayerIsPunished332_ptr);
        using CPlayerIsRecallAnimus334_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsRecallAnimus334_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsRecallAnimus334_ptr);
        using CPlayerIsRecvableContEffect336_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsRecvableContEffect336_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsRecvableContEffect336_ptr);
        using CPlayerIsReturnPostUpdate338_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsReturnPostUpdate338_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsReturnPostUpdate338_ptr);
        using CPlayerIsRidingShip340_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsRidingShip340_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsRidingShip340_ptr);
        using CPlayerIsRidingUnit342_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsRidingUnit342_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsRidingUnit342_ptr);
        using CPlayerIsSFActableByClass344_ptr = bool (WINAPIV*)(struct CPlayer*, char, struct _base_fld*);
        using CPlayerIsSFActableByClass344_clbk = bool (WINAPIV*)(struct CPlayer*, char, struct _base_fld*, CPlayerIsSFActableByClass344_ptr);
        using CPlayerIsSFUsableGauge346_ptr = bool (WINAPIV*)(struct CPlayer*, char, uint16_t, uint16_t*);
        using CPlayerIsSFUsableGauge346_clbk = bool (WINAPIV*)(struct CPlayer*, char, uint16_t, uint16_t*, CPlayerIsSFUsableGauge346_ptr);
        using CPlayerIsSFUsableSFMastery348_ptr = bool (WINAPIV*)(struct CPlayer*, char, int);
        using CPlayerIsSFUsableSFMastery348_clbk = bool (WINAPIV*)(struct CPlayer*, char, int, CPlayerIsSFUsableSFMastery348_ptr);
        using CPlayerIsSFUseableRace350_ptr = bool (WINAPIV*)(struct CPlayer*, char, uint16_t);
        using CPlayerIsSFUseableRace350_clbk = bool (WINAPIV*)(struct CPlayer*, char, uint16_t, CPlayerIsSFUseableRace350_ptr);
        using CPlayerIsSiegeMode352_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsSiegeMode352_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsSiegeMode352_ptr);
        using CPlayerIsTargetObj354_ptr = bool (WINAPIV*)(struct CPlayer*, struct CGameObject*);
        using CPlayerIsTargetObj354_clbk = bool (WINAPIV*)(struct CPlayer*, struct CGameObject*, CPlayerIsTargetObj354_ptr);
        using CPlayerIsUsableAccountType356_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayerIsUsableAccountType356_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayerIsUsableAccountType356_ptr);
        using CPlayerIsUseCloakBooster358_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsUseCloakBooster358_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsUseCloakBooster358_ptr);
        using CPlayerIsUseReleaseRaceBuffPotion360_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIsUseReleaseRaceBuffPotion360_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIsUseReleaseRaceBuffPotion360_ptr);
        using CPlayerIs_Battle_Mode362_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerIs_Battle_Mode362_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerIs_Battle_Mode362_ptr);
        using CPlayerLimLvNpcQuestDelete364_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerLimLvNpcQuestDelete364_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerLimLvNpcQuestDelete364_ptr);
        using CPlayerLoad366_ptr = bool (WINAPIV*)(struct CPlayer*, struct CUserDB*, bool);
        using CPlayerLoad366_clbk = bool (WINAPIV*)(struct CPlayer*, struct CUserDB*, bool, CPlayerLoad366_ptr);
        using CPlayerLoop368_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerLoop368_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerLoop368_ptr);
        using CPlayerNetClose370_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerNetClose370_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerNetClose370_ptr);
        using CPlayerNewViewCircleObject372_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerNewViewCircleObject372_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerNewViewCircleObject372_ptr);
        using CPlayerOnLoop_Static374_ptr = void (WINAPIV*)();
        using CPlayerOnLoop_Static374_clbk = void (WINAPIV*)(CPlayerOnLoop_Static374_ptr);
        using CPlayerOutOfMap376_ptr = bool (WINAPIV*)(struct CPlayer*, struct CMapData*, uint16_t, char, float*);
        using CPlayerOutOfMap376_clbk = bool (WINAPIV*)(struct CPlayer*, struct CMapData*, uint16_t, char, float*, CPlayerOutOfMap376_ptr);
        using CPlayerOutOfSec378_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerOutOfSec378_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerOutOfSec378_ptr);
        using CPlayerPastWhisperInit380_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerPastWhisperInit380_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerPastWhisperInit380_ptr);
        using CPlayerPotion_Buf_Extend382_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerPotion_Buf_Extend382_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerPotion_Buf_Extend382_ptr);
        using CPlayerPushDQSCheatPlyerVoteInfo384_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerPushDQSCheatPlyerVoteInfo384_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerPushDQSCheatPlyerVoteInfo384_ptr);
        using CPlayerPushDQSUpdatePlyerVoteInfo386_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerPushDQSUpdatePlyerVoteInfo386_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerPushDQSUpdatePlyerVoteInfo386_ptr);
        using CPlayerPushDQSUpdateVoteAvilable388_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerPushDQSUpdateVoteAvilable388_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerPushDQSUpdateVoteAvilable388_ptr);
        using CPlayerReCalcMaxHFSP390_ptr = void (WINAPIV*)(struct CPlayer*, bool, bool);
        using CPlayerReCalcMaxHFSP390_clbk = void (WINAPIV*)(struct CPlayer*, bool, bool, CPlayerReCalcMaxHFSP390_ptr);
        using CPlayerRecallRandomPositionInRange392_ptr = void (WINAPIV*)(struct CPlayer*, struct CMapData*, uint16_t, float*, int);
        using CPlayerRecallRandomPositionInRange392_clbk = void (WINAPIV*)(struct CPlayer*, struct CMapData*, uint16_t, float*, int, CPlayerRecallRandomPositionInRange392_ptr);
        using CPlayerRecvHSKQuest394_ptr = void (WINAPIV*)(struct CPlayer*, char, char, int, uint16_t, uint16_t, char);
        using CPlayerRecvHSKQuest394_clbk = void (WINAPIV*)(struct CPlayer*, char, char, int, uint16_t, uint16_t, char, CPlayerRecvHSKQuest394_ptr);
        using CPlayerRecvKillMessage396_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerRecvKillMessage396_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerRecvKillMessage396_ptr);
        using CPlayerReservationForceClose398_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerReservationForceClose398_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerReservationForceClose398_ptr);
        using CPlayerResurrect400_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerResurrect400_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerResurrect400_ptr);
        using CPlayerReturn_AnimusAsk402_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerReturn_AnimusAsk402_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerReturn_AnimusAsk402_ptr);
        using CPlayerRewardChangeClass404_ptr = void (WINAPIV*)(struct CPlayer*, struct _class_fld*, char);
        using CPlayerRewardChangeClass404_clbk = void (WINAPIV*)(struct CPlayer*, struct _class_fld*, char, CPlayerRewardChangeClass404_ptr);
        using CPlayerRewardChangeClassMastery406_ptr = void (WINAPIV*)(struct CPlayer*, struct _class_fld*);
        using CPlayerRewardChangeClassMastery406_clbk = void (WINAPIV*)(struct CPlayer*, struct _class_fld*, CPlayerRewardChangeClassMastery406_ptr);
        using CPlayerRewardChangeClassRewardItem408_ptr = void (WINAPIV*)(struct CPlayer*, struct _class_fld*, char);
        using CPlayerRewardChangeClassRewardItem408_clbk = void (WINAPIV*)(struct CPlayer*, struct _class_fld*, char, CPlayerRewardChangeClassRewardItem408_ptr);
        using CPlayerRewardRaceWarPvpCash410_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerRewardRaceWarPvpCash410_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerRewardRaceWarPvpCash410_ptr);
        using CPlayerReward_DarkDungeon412_ptr = void (WINAPIV*)(struct CPlayer*, struct _dh_reward_sub_setup*, char*, int, struct _STORAGE_LIST::_db_con*, int*);
        using CPlayerReward_DarkDungeon412_clbk = void (WINAPIV*)(struct CPlayer*, struct _dh_reward_sub_setup*, char*, int, struct _STORAGE_LIST::_db_con*, int*, CPlayerReward_DarkDungeon412_ptr);
        using CPlayerRobbedHP414_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, int);
        using CPlayerRobbedHP414_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, int, CPlayerRobbedHP414_ptr);
        using CPlayerSFContDelMessage416_ptr = void (WINAPIV*)(struct CPlayer*, char, char, bool, bool);
        using CPlayerSFContDelMessage416_clbk = void (WINAPIV*)(struct CPlayer*, char, char, bool, bool, CPlayerSFContDelMessage416_ptr);
        using CPlayerSFContInsertMessage418_ptr = void (WINAPIV*)(struct CPlayer*, char, char, bool, struct CPlayer*);
        using CPlayerSFContInsertMessage418_clbk = void (WINAPIV*)(struct CPlayer*, char, char, bool, struct CPlayer*, CPlayerSFContInsertMessage418_ptr);
        using CPlayerSFContUpdateTimeMessage420_ptr = void (WINAPIV*)(struct CPlayer*, char, char, int);
        using CPlayerSFContUpdateTimeMessage420_clbk = void (WINAPIV*)(struct CPlayer*, char, char, int, CPlayerSFContUpdateTimeMessage420_ptr);
        using CPlayerSF_AllContDamageForceRemove_Once422_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSF_AllContDamageForceRemove_Once422_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSF_AllContDamageForceRemove_Once422_ptr);
        using CPlayerSF_AllContDamageRemove_Once424_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSF_AllContDamageRemove_Once424_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSF_AllContDamageRemove_Once424_ptr);
        using CPlayerSF_AllContHelpForceRemove_Once426_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSF_AllContHelpForceRemove_Once426_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSF_AllContHelpForceRemove_Once426_ptr);
        using CPlayerSF_AllContHelpSkillRemove_Once428_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSF_AllContHelpSkillRemove_Once428_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSF_AllContHelpSkillRemove_Once428_ptr);
        using CPlayerSF_AttHPtoDstFP_Once430_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_AttHPtoDstFP_Once430_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_AttHPtoDstFP_Once430_ptr);
        using CPlayerSF_ContDamageTimeInc_Once432_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_ContDamageTimeInc_Once432_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_ContDamageTimeInc_Once432_ptr);
        using CPlayerSF_ContHelpTimeInc_Once434_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_ContHelpTimeInc_Once434_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_ContHelpTimeInc_Once434_ptr);
        using CPlayerSF_ConvertMonsterTarget436_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_ConvertMonsterTarget436_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_ConvertMonsterTarget436_ptr);
        using CPlayerSF_ConvertTargetDest438_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_ConvertTargetDest438_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_ConvertTargetDest438_ptr);
        using CPlayerSF_DamageAndStun440_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_DamageAndStun440_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_DamageAndStun440_ptr);
        using CPlayerSF_FPDec442_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_FPDec442_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_FPDec442_ptr);
        using CPlayerSF_HFSInc_Once444_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSF_HFSInc_Once444_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSF_HFSInc_Once444_ptr);
        using CPlayerSF_HPInc_Once446_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_HPInc_Once446_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_HPInc_Once446_ptr);
        using CPlayerSF_IncHPCircleParty448_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_IncHPCircleParty448_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_IncHPCircleParty448_ptr);
        using CPlayerSF_IncreaseDP450_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_IncreaseDP450_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_IncreaseDP450_ptr);
        using CPlayerSF_LateContDamageRemove_Once452_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSF_LateContDamageRemove_Once452_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSF_LateContDamageRemove_Once452_ptr);
        using CPlayerSF_LateContHelpForceRemove_Once454_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSF_LateContHelpForceRemove_Once454_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSF_LateContHelpForceRemove_Once454_ptr);
        using CPlayerSF_LateContHelpSkillRemove_Once456_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSF_LateContHelpSkillRemove_Once456_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSF_LateContHelpSkillRemove_Once456_ptr);
        using CPlayerSF_MakePortalReturnBindPositionPartyMember458_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, char*);
        using CPlayerSF_MakePortalReturnBindPositionPartyMember458_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, char*, CPlayerSF_MakePortalReturnBindPositionPartyMember458_ptr);
        using CPlayerSF_MakeZeroAnimusRecallTimeOnce460_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_MakeZeroAnimusRecallTimeOnce460_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_MakeZeroAnimusRecallTimeOnce460_ptr);
        using CPlayerSF_OthersContHelpSFRemove_Once462_ptr = bool (WINAPIV*)(struct CPlayer*, float);
        using CPlayerSF_OthersContHelpSFRemove_Once462_clbk = bool (WINAPIV*)(struct CPlayer*, float, CPlayerSF_OthersContHelpSFRemove_Once462_ptr);
        using CPlayerSF_OverHealing_Once464_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_OverHealing_Once464_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_OverHealing_Once464_ptr);
        using CPlayerSF_RecoverAllReturnStateAnimusHPFull466_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_RecoverAllReturnStateAnimusHPFull466_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_RecoverAllReturnStateAnimusHPFull466_ptr);
        using CPlayerSF_ReleaseMonsterTarget468_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_ReleaseMonsterTarget468_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_ReleaseMonsterTarget468_ptr);
        using CPlayerSF_RemoveAllContHelp_Once470_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_RemoveAllContHelp_Once470_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_RemoveAllContHelp_Once470_ptr);
        using CPlayerSF_Resurrect_Once472_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSF_Resurrect_Once472_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSF_Resurrect_Once472_ptr);
        using CPlayerSF_ReturnBindPosition474_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_ReturnBindPosition474_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_ReturnBindPosition474_ptr);
        using CPlayerSF_SPDec476_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_SPDec476_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_SPDec476_ptr);
        using CPlayerSF_STInc_Once478_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_STInc_Once478_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_STInc_Once478_ptr);
        using CPlayerSF_SelfDestruction480_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_SelfDestruction480_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_SelfDestruction480_ptr);
        using CPlayerSF_SkillContHelpTimeInc_Once482_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_SkillContHelpTimeInc_Once482_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_SkillContHelpTimeInc_Once482_ptr);
        using CPlayerSF_Stun484_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_Stun484_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_Stun484_ptr);
        using CPlayerSF_TeleportToDestination486_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, bool);
        using CPlayerSF_TeleportToDestination486_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, bool, CPlayerSF_TeleportToDestination486_ptr);
        using CPlayerSF_TransDestHP488_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, char*);
        using CPlayerSF_TransDestHP488_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, char*, CPlayerSF_TransDestHP488_ptr);
        using CPlayerSF_TransMonsterHP490_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float);
        using CPlayerSF_TransMonsterHP490_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, float, CPlayerSF_TransMonsterHP490_ptr);
        using CPlayerSendData_ChatTrans492_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char, bool, char*, char, char*);
        using CPlayerSendData_ChatTrans492_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char, bool, char*, char, char*, CPlayerSendData_ChatTrans492_ptr);
        using CPlayerSendData_PartyMemberEffect494_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char);
        using CPlayerSendData_PartyMemberEffect494_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char, CPlayerSendData_PartyMemberEffect494_ptr);
        using CPlayerSendData_PartyMemberFP496_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendData_PartyMemberFP496_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendData_PartyMemberFP496_ptr);
        using CPlayerSendData_PartyMemberHP498_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendData_PartyMemberHP498_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendData_PartyMemberHP498_ptr);
        using CPlayerSendData_PartyMemberInfo500_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerSendData_PartyMemberInfo500_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerSendData_PartyMemberInfo500_ptr);
        using CPlayerSendData_PartyMemberInfoToMembers502_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendData_PartyMemberInfoToMembers502_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendData_PartyMemberInfoToMembers502_ptr);
        using CPlayerSendData_PartyMemberLv504_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendData_PartyMemberLv504_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendData_PartyMemberLv504_ptr);
        using CPlayerSendData_PartyMemberMaxHFSP506_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendData_PartyMemberMaxHFSP506_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendData_PartyMemberMaxHFSP506_ptr);
        using CPlayerSendData_PartyMemberPos508_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendData_PartyMemberPos508_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendData_PartyMemberPos508_ptr);
        using CPlayerSendData_PartyMemberSP510_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendData_PartyMemberSP510_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendData_PartyMemberSP510_ptr);
        using CPlayerSendMsg_AMPInvenDownloadResult512_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AMPInvenDownloadResult512_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AMPInvenDownloadResult512_ptr);
        using CPlayerSendMsg_AddBagResult514_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_AddBagResult514_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_AddBagResult514_ptr);
        using CPlayerSendMsg_AddEffect516_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char, uint16_t, unsigned int, char*);
        using CPlayerSendMsg_AddEffect516_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, uint16_t, unsigned int, char*, CPlayerSendMsg_AddEffect516_ptr);
        using CPlayerSendMsg_AdjustAmountInform518_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, unsigned int);
        using CPlayerSendMsg_AdjustAmountInform518_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, unsigned int, CPlayerSendMsg_AdjustAmountInform518_ptr);
        using CPlayerSendMsg_AlterBooster520_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterBooster520_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterBooster520_ptr);
        using CPlayerSendMsg_AlterContEffectTime522_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_AlterContEffectTime522_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_AlterContEffectTime522_ptr);
        using CPlayerSendMsg_AlterEquipSPInform524_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterEquipSPInform524_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterEquipSPInform524_ptr);
        using CPlayerSendMsg_AlterExpInform526_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterExpInform526_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterExpInform526_ptr);
        using CPlayerSendMsg_AlterGradeInform528_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterGradeInform528_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterGradeInform528_ptr);
        using CPlayerSendMsg_AlterHPInform530_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterHPInform530_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterHPInform530_ptr);
        using CPlayerSendMsg_AlterItemDurInform532_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, uint64_t);
        using CPlayerSendMsg_AlterItemDurInform532_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, uint64_t, CPlayerSendMsg_AlterItemDurInform532_ptr);
        using CPlayerSendMsg_AlterMaxDP534_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterMaxDP534_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterMaxDP534_ptr);
        using CPlayerSendMsg_AlterMoneyInform536_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_AlterMoneyInform536_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_AlterMoneyInform536_ptr);
        using CPlayerSendMsg_AlterPvPCash538_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSendMsg_AlterPvPCash538_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSendMsg_AlterPvPCash538_ptr);
        using CPlayerSendMsg_AlterPvPPoint540_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterPvPPoint540_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterPvPPoint540_ptr);
        using CPlayerSendMsg_AlterPvPRank542_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int);
        using CPlayerSendMsg_AlterPvPRank542_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int, CPlayerSendMsg_AlterPvPRank542_ptr);
        using CPlayerSendMsg_AlterRegionInform544_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSendMsg_AlterRegionInform544_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSendMsg_AlterRegionInform544_ptr);
        using CPlayerSendMsg_AlterSPInform546_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterSPInform546_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterSPInform546_ptr);
        using CPlayerSendMsg_AlterTol548_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterTol548_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterTol548_ptr);
        using CPlayerSendMsg_AlterTowerHP550_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t);
        using CPlayerSendMsg_AlterTowerHP550_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, CPlayerSendMsg_AlterTowerHP550_ptr);
        using CPlayerSendMsg_AlterTownOrField552_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AlterTownOrField552_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AlterTownOrField552_ptr);
        using CPlayerSendMsg_AlterUnitBulletInform554_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t);
        using CPlayerSendMsg_AlterUnitBulletInform554_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, CPlayerSendMsg_AlterUnitBulletInform554_ptr);
        using CPlayerSendMsg_AlterUnitHPInform556_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_AlterUnitHPInform556_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_AlterUnitHPInform556_ptr);
        using CPlayerSendMsg_AlterWeaponBulletInform558_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t);
        using CPlayerSendMsg_AlterWeaponBulletInform558_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, CPlayerSendMsg_AlterWeaponBulletInform558_ptr);
        using CPlayerSendMsg_Alter_Action_Point560_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_Alter_Action_Point560_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_Alter_Action_Point560_ptr);
        using CPlayerSendMsg_AnimusExpInform562_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AnimusExpInform562_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AnimusExpInform562_ptr);
        using CPlayerSendMsg_AnimusFPInform564_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AnimusFPInform564_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AnimusFPInform564_ptr);
        using CPlayerSendMsg_AnimusHPInform566_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_AnimusHPInform566_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_AnimusHPInform566_ptr);
        using CPlayerSendMsg_AnimusInvenChange568_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_AnimusInvenChange568_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_AnimusInvenChange568_ptr);
        using CPlayerSendMsg_AnimusModeInform570_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_AnimusModeInform570_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_AnimusModeInform570_ptr);
        using CPlayerSendMsg_AnimusRecallResult572_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, struct CAnimus*);
        using CPlayerSendMsg_AnimusRecallResult572_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, struct CAnimus*, CPlayerSendMsg_AnimusRecallResult572_ptr);
        using CPlayerSendMsg_AnimusRecallWaitTimeFree574_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSendMsg_AnimusRecallWaitTimeFree574_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSendMsg_AnimusRecallWaitTimeFree574_ptr);
        using CPlayerSendMsg_AnimusReturnResult576_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char);
        using CPlayerSendMsg_AnimusReturnResult576_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char, CPlayerSendMsg_AnimusReturnResult576_ptr);
        using CPlayerSendMsg_AnimusTargetResult578_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_AnimusTargetResult578_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_AnimusTargetResult578_ptr);
        using CPlayerSendMsg_ApexInform580_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char*);
        using CPlayerSendMsg_ApexInform580_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char*, CPlayerSendMsg_ApexInform580_ptr);
        using CPlayerSendMsg_AttackResult_Count582_ptr = void (WINAPIV*)(struct CPlayer*, struct CAttack*);
        using CPlayerSendMsg_AttackResult_Count582_clbk = void (WINAPIV*)(struct CPlayer*, struct CAttack*, CPlayerSendMsg_AttackResult_Count582_ptr);
        using CPlayerSendMsg_AttackResult_Error584_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSendMsg_AttackResult_Error584_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSendMsg_AttackResult_Error584_ptr);
        using CPlayerSendMsg_AttackResult_Force586_ptr = void (WINAPIV*)(struct CPlayer*, struct CAttack*);
        using CPlayerSendMsg_AttackResult_Force586_clbk = void (WINAPIV*)(struct CPlayer*, struct CAttack*, CPlayerSendMsg_AttackResult_Force586_ptr);
        using CPlayerSendMsg_AttackResult_Gen588_ptr = void (WINAPIV*)(struct CPlayer*, struct CAttack*, uint16_t);
        using CPlayerSendMsg_AttackResult_Gen588_clbk = void (WINAPIV*)(struct CPlayer*, struct CAttack*, uint16_t, CPlayerSendMsg_AttackResult_Gen588_ptr);
        using CPlayerSendMsg_AttackResult_SelfDestruction590_ptr = void (WINAPIV*)(struct CPlayer*, struct CAttack*);
        using CPlayerSendMsg_AttackResult_SelfDestruction590_clbk = void (WINAPIV*)(struct CPlayer*, struct CAttack*, CPlayerSendMsg_AttackResult_SelfDestruction590_ptr);
        using CPlayerSendMsg_AttackResult_Siege592_ptr = void (WINAPIV*)(struct CPlayer*, struct CAttack*, uint16_t);
        using CPlayerSendMsg_AttackResult_Siege592_clbk = void (WINAPIV*)(struct CPlayer*, struct CAttack*, uint16_t, CPlayerSendMsg_AttackResult_Siege592_ptr);
        using CPlayerSendMsg_AttackResult_Skill594_ptr = void (WINAPIV*)(struct CPlayer*, char, struct CPlayerAttack*, uint16_t);
        using CPlayerSendMsg_AttackResult_Skill594_clbk = void (WINAPIV*)(struct CPlayer*, char, struct CPlayerAttack*, uint16_t, CPlayerSendMsg_AttackResult_Skill594_ptr);
        using CPlayerSendMsg_AttackResult_Unit596_ptr = void (WINAPIV*)(struct CPlayer*, struct CAttack*, char, uint16_t);
        using CPlayerSendMsg_AttackResult_Unit596_clbk = void (WINAPIV*)(struct CPlayer*, struct CAttack*, char, uint16_t, CPlayerSendMsg_AttackResult_Unit596_ptr);
        using CPlayerSendMsg_AwayPartyInvitationQuestion598_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerSendMsg_AwayPartyInvitationQuestion598_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerSendMsg_AwayPartyInvitationQuestion598_ptr);
        using CPlayerSendMsg_AwayPartyRequestResult600_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_AwayPartyRequestResult600_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_AwayPartyRequestResult600_ptr);
        using CPlayerSendMsg_BackTowerResult602_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, uint16_t);
        using CPlayerSendMsg_BackTowerResult602_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, uint16_t, CPlayerSendMsg_BackTowerResult602_ptr);
        using CPlayerSendMsg_BackTrapResult604_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_BackTrapResult604_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_BackTrapResult604_ptr);
        using CPlayerSendMsg_BaseDownloadResult606_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_BaseDownloadResult606_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_BaseDownloadResult606_ptr);
        using CPlayerSendMsg_BillingExipreInform608_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t);
        using CPlayerSendMsg_BillingExipreInform608_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, CPlayerSendMsg_BillingExipreInform608_ptr);
        using CPlayerSendMsg_BillingTypeChangeInform610_ptr = void (WINAPIV*)(struct CPlayer*, int16_t, int, struct _SYSTEMTIME*, char);
        using CPlayerSendMsg_BillingTypeChangeInform610_clbk = void (WINAPIV*)(struct CPlayer*, int16_t, int, struct _SYSTEMTIME*, char, CPlayerSendMsg_BillingTypeChangeInform610_ptr);
        using CPlayerSendMsg_BreakdownEquipItem612_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t);
        using CPlayerSendMsg_BreakdownEquipItem612_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, CPlayerSendMsg_BreakdownEquipItem612_ptr);
        using CPlayerSendMsg_BuddhaEventMsg614_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_BuddhaEventMsg614_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_BuddhaEventMsg614_ptr);
        using CPlayerSendMsg_BuddyAddAnswerResult616_ptr = void (WINAPIV*)(struct CPlayer*, char, bool, unsigned int, uint16_t, unsigned int, char*);
        using CPlayerSendMsg_BuddyAddAnswerResult616_clbk = void (WINAPIV*)(struct CPlayer*, char, bool, unsigned int, uint16_t, unsigned int, char*, CPlayerSendMsg_BuddyAddAnswerResult616_ptr);
        using CPlayerSendMsg_BuddyAddAsk618_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int, char*);
        using CPlayerSendMsg_BuddyAddAsk618_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int, char*, CPlayerSendMsg_BuddyAddAsk618_ptr);
        using CPlayerSendMsg_BuddyAddFail620_ptr = void (WINAPIV*)(struct CPlayer*, char, char*);
        using CPlayerSendMsg_BuddyAddFail620_clbk = void (WINAPIV*)(struct CPlayer*, char, char*, CPlayerSendMsg_BuddyAddFail620_ptr);
        using CPlayerSendMsg_BuddyDelResult622_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_BuddyDelResult622_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_BuddyDelResult622_ptr);
        using CPlayerSendMsg_BuddyLoginInform624_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char, char);
        using CPlayerSendMsg_BuddyLoginInform624_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char, char, CPlayerSendMsg_BuddyLoginInform624_ptr);
        using CPlayerSendMsg_BuddyLogoffInform626_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerSendMsg_BuddyLogoffInform626_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerSendMsg_BuddyLogoffInform626_ptr);
        using CPlayerSendMsg_BuddyNameReNewal628_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char*);
        using CPlayerSendMsg_BuddyNameReNewal628_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char*, CPlayerSendMsg_BuddyNameReNewal628_ptr);
        using CPlayerSendMsg_BuddyPosInform630_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char, char);
        using CPlayerSendMsg_BuddyPosInform630_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char, char, CPlayerSendMsg_BuddyPosInform630_ptr);
        using CPlayerSendMsg_BuyCashItemMode632_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_BuyCashItemMode632_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_BuyCashItemMode632_ptr);
        using CPlayerSendMsg_BuyItemStoreResult634_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, int, struct _buy_offer*, char);
        using CPlayerSendMsg_BuyItemStoreResult634_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, int, struct _buy_offer*, char, CPlayerSendMsg_BuyItemStoreResult634_ptr);
        using CPlayerSendMsg_CancelSuggestResult636_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_CancelSuggestResult636_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_CancelSuggestResult636_ptr);
        using CPlayerSendMsg_CastVoteResult638_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_CastVoteResult638_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_CastVoteResult638_ptr);
        using CPlayerSendMsg_ChangeClassCommand640_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_ChangeClassCommand640_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_ChangeClassCommand640_ptr);
        using CPlayerSendMsg_CharacterRenameCashResult642_ptr = void (WINAPIV*)(struct CPlayer*, bool, char);
        using CPlayerSendMsg_CharacterRenameCashResult642_clbk = void (WINAPIV*)(struct CPlayer*, bool, char, CPlayerSendMsg_CharacterRenameCashResult642_ptr);
        using CPlayerSendMsg_ChatFarFailure644_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSendMsg_ChatFarFailure644_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSendMsg_ChatFarFailure644_ptr);
        using CPlayerSendMsg_Circle_DelEffect646_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char, bool);
        using CPlayerSendMsg_Circle_DelEffect646_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char, bool, CPlayerSendMsg_Circle_DelEffect646_ptr);
        using CPlayerSendMsg_ClassSkillResult648_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, uint16_t);
        using CPlayerSendMsg_ClassSkillResult648_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, uint16_t, CPlayerSendMsg_ClassSkillResult648_ptr);
        using CPlayerSendMsg_ClearDarkHole650_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ClearDarkHole650_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ClearDarkHole650_ptr);
        using CPlayerSendMsg_CombineItemExAcceptResult652_ptr = void (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_accept_result_zocl*);
        using CPlayerSendMsg_CombineItemExAcceptResult652_clbk = void (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_accept_result_zocl*, CPlayerSendMsg_CombineItemExAcceptResult652_ptr);
        using CPlayerSendMsg_CombineItemExResult654_ptr = void (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_result_zocl*);
        using CPlayerSendMsg_CombineItemExResult654_clbk = void (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_result_zocl*, CPlayerSendMsg_CombineItemExResult654_ptr);
        using CPlayerSendMsg_CombineItemResult656_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_CombineItemResult656_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_CombineItemResult656_ptr);
        using CPlayerSendMsg_CombineLendItemResult658_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_CombineLendItemResult658_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_CombineLendItemResult658_ptr);
        using CPlayerSendMsg_CreateTowerResult660_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_CreateTowerResult660_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_CreateTowerResult660_ptr);
        using CPlayerSendMsg_CreateTrapResult662_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_CreateTrapResult662_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_CreateTrapResult662_ptr);
        using CPlayerSendMsg_CumDownloadResult664_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_CumDownloadResult664_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_CumDownloadResult664_ptr);
        using CPlayerSendMsg_CuttingCompleteResult666_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_CuttingCompleteResult666_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_CuttingCompleteResult666_ptr);
        using CPlayerSendMsg_DTradeAccomplishInform668_ptr = void (WINAPIV*)(struct CPlayer*, bool, uint16_t);
        using CPlayerSendMsg_DTradeAccomplishInform668_clbk = void (WINAPIV*)(struct CPlayer*, bool, uint16_t, CPlayerSendMsg_DTradeAccomplishInform668_ptr);
        using CPlayerSendMsg_DTradeAddInform670_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, char);
        using CPlayerSendMsg_DTradeAddInform670_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, char, CPlayerSendMsg_DTradeAddInform670_ptr);
        using CPlayerSendMsg_DTradeAddResult672_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeAddResult672_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeAddResult672_ptr);
        using CPlayerSendMsg_DTradeAnswerResult674_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeAnswerResult674_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeAnswerResult674_ptr);
        using CPlayerSendMsg_DTradeAskInform676_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*);
        using CPlayerSendMsg_DTradeAskInform676_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, CPlayerSendMsg_DTradeAskInform676_ptr);
        using CPlayerSendMsg_DTradeAskResult678_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeAskResult678_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeAskResult678_ptr);
        using CPlayerSendMsg_DTradeBetInform680_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_DTradeBetInform680_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_DTradeBetInform680_ptr);
        using CPlayerSendMsg_DTradeBetResult682_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeBetResult682_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeBetResult682_ptr);
        using CPlayerSendMsg_DTradeCancleInform684_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_DTradeCancleInform684_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_DTradeCancleInform684_ptr);
        using CPlayerSendMsg_DTradeCancleResult686_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeCancleResult686_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeCancleResult686_ptr);
        using CPlayerSendMsg_DTradeCloseInform688_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeCloseInform688_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeCloseInform688_ptr);
        using CPlayerSendMsg_DTradeDelInform690_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeDelInform690_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeDelInform690_ptr);
        using CPlayerSendMsg_DTradeDelResult692_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeDelResult692_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeDelResult692_ptr);
        using CPlayerSendMsg_DTradeLockInform694_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_DTradeLockInform694_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_DTradeLockInform694_ptr);
        using CPlayerSendMsg_DTradeLockResult696_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeLockResult696_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeLockResult696_ptr);
        using CPlayerSendMsg_DTradeOKInform698_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_DTradeOKInform698_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_DTradeOKInform698_ptr);
        using CPlayerSendMsg_DTradeOKResult700_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_DTradeOKResult700_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_DTradeOKResult700_ptr);
        using CPlayerSendMsg_DTradeStartInform702_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, struct CPlayer*, unsigned int*);
        using CPlayerSendMsg_DTradeStartInform702_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, struct CPlayer*, unsigned int*, CPlayerSendMsg_DTradeStartInform702_ptr);
        using CPlayerSendMsg_DTradeUnitAddInform704_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, struct _UNIT_DB_BASE::_LIST*);
        using CPlayerSendMsg_DTradeUnitAddInform704_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, struct _UNIT_DB_BASE::_LIST*, CPlayerSendMsg_DTradeUnitAddInform704_ptr);
        using CPlayerSendMsg_DTradeUnitInfoInform706_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _UNIT_DB_BASE::_LIST*);
        using CPlayerSendMsg_DTradeUnitInfoInform706_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _UNIT_DB_BASE::_LIST*, CPlayerSendMsg_DTradeUnitInfoInform706_ptr);
        using CPlayerSendMsg_DamageResult708_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_DamageResult708_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_DamageResult708_ptr);
        using CPlayerSendMsg_DarkHoleOpenFail710_ptr = void (WINAPIV*)(struct CPlayer*, int, char);
        using CPlayerSendMsg_DarkHoleOpenFail710_clbk = void (WINAPIV*)(struct CPlayer*, int, char, CPlayerSendMsg_DarkHoleOpenFail710_ptr);
        using CPlayerSendMsg_DarkHoleOpenResult712_ptr = void (WINAPIV*)(struct CPlayer*, int, int, char, uint16_t, unsigned int);
        using CPlayerSendMsg_DarkHoleOpenResult712_clbk = void (WINAPIV*)(struct CPlayer*, int, int, char, uint16_t, unsigned int, CPlayerSendMsg_DarkHoleOpenResult712_ptr);
        using CPlayerSendMsg_DarkHoleRewardMessage714_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, unsigned int, int);
        using CPlayerSendMsg_DarkHoleRewardMessage714_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, unsigned int, int, CPlayerSendMsg_DarkHoleRewardMessage714_ptr);
        using CPlayerSendMsg_DelEffect716_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char);
        using CPlayerSendMsg_DelEffect716_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char, CPlayerSendMsg_DelEffect716_ptr);
        using CPlayerSendMsg_DeleteStorageInform718_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t);
        using CPlayerSendMsg_DeleteStorageInform718_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, CPlayerSendMsg_DeleteStorageInform718_ptr);
        using CPlayerSendMsg_Destroy720_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_Destroy720_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_Destroy720_ptr);
        using CPlayerSendMsg_Die722_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_Die722_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_Die722_ptr);
        using CPlayerSendMsg_EconomyHistoryInform724_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_EconomyHistoryInform724_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_EconomyHistoryInform724_ptr);
        using CPlayerSendMsg_EconomyRateInform726_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSendMsg_EconomyRateInform726_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSendMsg_EconomyRateInform726_ptr);
        using CPlayerSendMsg_EmbellishResult728_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_EmbellishResult728_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_EmbellishResult728_ptr);
        using CPlayerSendMsg_EnterDarkHole730_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_EnterDarkHole730_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_EnterDarkHole730_ptr);
        using CPlayerSendMsg_EquipItemLevelLimit732_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSendMsg_EquipItemLevelLimit732_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSendMsg_EquipItemLevelLimit732_ptr);
        using CPlayerSendMsg_EquipPartChange734_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_EquipPartChange734_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_EquipPartChange734_ptr);
        using CPlayerSendMsg_EquipPartResult736_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_EquipPartResult736_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_EquipPartResult736_ptr);
        using CPlayerSendMsg_ExchangeItemResult738_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_ExchangeItemResult738_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_ExchangeItemResult738_ptr);
        using CPlayerSendMsg_ExchangeLendItemResult740_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_ExchangeLendItemResult740_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_ExchangeLendItemResult740_ptr);
        using CPlayerSendMsg_ExchangeMoneyResult742_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ExchangeMoneyResult742_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ExchangeMoneyResult742_ptr);
        using CPlayerSendMsg_ExitWorldResult744_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ExitWorldResult744_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ExitWorldResult744_ptr);
        using CPlayerSendMsg_ExtTrunkExtendResult746_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char);
        using CPlayerSendMsg_ExtTrunkExtendResult746_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, CPlayerSendMsg_ExtTrunkExtendResult746_ptr);
        using CPlayerSendMsg_FanfareItem748_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, struct CItemBox*);
        using CPlayerSendMsg_FanfareItem748_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, struct CItemBox*, CPlayerSendMsg_FanfareItem748_ptr);
        using CPlayerSendMsg_FcitemInform750_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int);
        using CPlayerSendMsg_FcitemInform750_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int, CPlayerSendMsg_FcitemInform750_ptr);
        using CPlayerSendMsg_FixPosition752_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSendMsg_FixPosition752_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSendMsg_FixPosition752_ptr);
        using CPlayerSendMsg_ForceDownloadResult754_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_ForceDownloadResult754_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_ForceDownloadResult754_ptr);
        using CPlayerSendMsg_ForceInvenChange756_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ForceInvenChange756_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ForceInvenChange756_ptr);
        using CPlayerSendMsg_ForceResult758_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, struct _STORAGE_LIST::_db_con*, int);
        using CPlayerSendMsg_ForceResult758_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, struct _STORAGE_LIST::_db_con*, int, CPlayerSendMsg_ForceResult758_ptr);
        using CPlayerSendMsg_GM_Greeting760_ptr = void (WINAPIV*)(struct CPlayer*, char*, char*);
        using CPlayerSendMsg_GM_Greeting760_clbk = void (WINAPIV*)(struct CPlayer*, char*, char*, CPlayerSendMsg_GM_Greeting760_ptr);
        using CPlayerSendMsg_GUILD_Greeting762_ptr = void (WINAPIV*)(struct CPlayer*, char*, char*);
        using CPlayerSendMsg_GUILD_Greeting762_clbk = void (WINAPIV*)(struct CPlayer*, char*, char*, CPlayerSendMsg_GUILD_Greeting762_ptr);
        using CPlayerSendMsg_GestureInform764_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_GestureInform764_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_GestureInform764_ptr);
        using CPlayerSendMsg_GiveupDarkHole766_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_GiveupDarkHole766_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_GiveupDarkHole766_ptr);
        using CPlayerSendMsg_GotoBasePortalResult768_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_GotoBasePortalResult768_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_GotoBasePortalResult768_ptr);
        using CPlayerSendMsg_GotoRecallResult770_ptr = void (WINAPIV*)(struct CPlayer*, char, char, float*, char);
        using CPlayerSendMsg_GotoRecallResult770_clbk = void (WINAPIV*)(struct CPlayer*, char, char, float*, char, CPlayerSendMsg_GotoRecallResult770_ptr);
        using CPlayerSendMsg_GroupTargetInform772_ptr = void (WINAPIV*)(struct CPlayer*, char, char*);
        using CPlayerSendMsg_GroupTargetInform772_clbk = void (WINAPIV*)(struct CPlayer*, char, char*, CPlayerSendMsg_GroupTargetInform772_ptr);
        using CPlayerSendMsg_GuildEstablishFail774_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_GuildEstablishFail774_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_GuildEstablishFail774_ptr);
        using CPlayerSendMsg_GuildForceLeaveBoradori776_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_GuildForceLeaveBoradori776_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_GuildForceLeaveBoradori776_ptr);
        using CPlayerSendMsg_GuildJoinAcceptFail778_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_GuildJoinAcceptFail778_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_GuildJoinAcceptFail778_ptr);
        using CPlayerSendMsg_GuildJoinApplyCancelResult780_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_GuildJoinApplyCancelResult780_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_GuildJoinApplyCancelResult780_ptr);
        using CPlayerSendMsg_GuildJoinApplyRejectInform782_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_GuildJoinApplyRejectInform782_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_GuildJoinApplyRejectInform782_ptr);
        using CPlayerSendMsg_GuildJoinApplyResult784_ptr = void (WINAPIV*)(struct CPlayer*, char, struct CGuild*);
        using CPlayerSendMsg_GuildJoinApplyResult784_clbk = void (WINAPIV*)(struct CPlayer*, char, struct CGuild*, CPlayerSendMsg_GuildJoinApplyResult784_ptr);
        using CPlayerSendMsg_GuildJoinOtherInform786_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_GuildJoinOtherInform786_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_GuildJoinOtherInform786_ptr);
        using CPlayerSendMsg_GuildManageResult788_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_GuildManageResult788_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_GuildManageResult788_ptr);
        using CPlayerSendMsg_GuildMasterEffect790_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char, char, char, char);
        using CPlayerSendMsg_GuildMasterEffect790_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, char, char, char, CPlayerSendMsg_GuildMasterEffect790_ptr);
        using CPlayerSendMsg_GuildPushMoneyResult792_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_GuildPushMoneyResult792_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_GuildPushMoneyResult792_ptr);
        using CPlayerSendMsg_GuildRoomEnterResult794_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char, uint16_t, float*, int);
        using CPlayerSendMsg_GuildRoomEnterResult794_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, uint16_t, float*, int, CPlayerSendMsg_GuildRoomEnterResult794_ptr);
        using CPlayerSendMsg_GuildRoomOutResult796_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, float*);
        using CPlayerSendMsg_GuildRoomOutResult796_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, float*, CPlayerSendMsg_GuildRoomOutResult796_ptr);
        using CPlayerSendMsg_GuildRoomRentResult798_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char);
        using CPlayerSendMsg_GuildRoomRentResult798_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, CPlayerSendMsg_GuildRoomRentResult798_ptr);
        using CPlayerSendMsg_GuildRoomRestTimeResult800_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_GuildRoomRestTimeResult800_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_GuildRoomRestTimeResult800_ptr);
        using CPlayerSendMsg_GuildSelfLeaveResult802_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_GuildSelfLeaveResult802_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_GuildSelfLeaveResult802_ptr);
        using CPlayerSendMsg_GuildSetHonorResult804_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_GuildSetHonorResult804_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_GuildSetHonorResult804_ptr);
        using CPlayerSendMsg_HSKQuestActCum806_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_HSKQuestActCum806_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_HSKQuestActCum806_ptr);
        using CPlayerSendMsg_HSKQuestSucc808_ptr = void (WINAPIV*)(struct CPlayer*, char, bool);
        using CPlayerSendMsg_HSKQuestSucc808_clbk = void (WINAPIV*)(struct CPlayer*, char, bool, CPlayerSendMsg_HSKQuestSucc808_ptr);
        using CPlayerSendMsg_HonorGuildMark810_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_HonorGuildMark810_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_HonorGuildMark810_ptr);
        using CPlayerSendMsg_InformTaxIncome812_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*);
        using CPlayerSendMsg_InformTaxIncome812_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, CPlayerSendMsg_InformTaxIncome812_ptr);
        using CPlayerSendMsg_Init_Action_Point814_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_Init_Action_Point814_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_Init_Action_Point814_ptr);
        using CPlayerSendMsg_InsertItemInform816_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_InsertItemInform816_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_InsertItemInform816_ptr);
        using CPlayerSendMsg_InsertNewQuest818_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _QUEST_DB_BASE::_LIST*);
        using CPlayerSendMsg_InsertNewQuest818_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _QUEST_DB_BASE::_LIST*, CPlayerSendMsg_InsertNewQuest818_ptr);
        using CPlayerSendMsg_InsertNextQuest820_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _QUEST_DB_BASE::_LIST*);
        using CPlayerSendMsg_InsertNextQuest820_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _QUEST_DB_BASE::_LIST*, CPlayerSendMsg_InsertNextQuest820_ptr);
        using CPlayerSendMsg_InsertQuestFailure822_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char);
        using CPlayerSendMsg_InsertQuestFailure822_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char, CPlayerSendMsg_InsertQuestFailure822_ptr);
        using CPlayerSendMsg_InsertQuestItemInform824_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_InsertQuestItemInform824_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_InsertQuestItemInform824_ptr);
        using CPlayerSendMsg_InvenDownloadResult826_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_InvenDownloadResult826_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_InvenDownloadResult826_ptr);
        using CPlayerSendMsg_ItemDowngrade828_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ItemDowngrade828_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ItemDowngrade828_ptr);
        using CPlayerSendMsg_ItemStorageRefresh830_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ItemStorageRefresh830_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ItemStorageRefresh830_ptr);
        using CPlayerSendMsg_ItemUpgrade832_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ItemUpgrade832_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ItemUpgrade832_ptr);
        using CPlayerSendMsg_JadeEffectErr834_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_JadeEffectErr834_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_JadeEffectErr834_ptr);
        using CPlayerSendMsg_LendItemTimeExpired836_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t);
        using CPlayerSendMsg_LendItemTimeExpired836_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, CPlayerSendMsg_LendItemTimeExpired836_ptr);
        using CPlayerSendMsg_Level838_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSendMsg_Level838_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSendMsg_Level838_ptr);
        using CPlayerSendMsg_LinkBoardDownloadResult840_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_LinkBoardDownloadResult840_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_LinkBoardDownloadResult840_ptr);
        using CPlayerSendMsg_MacroRequest842_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_MacroRequest842_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_MacroRequest842_ptr);
        using CPlayerSendMsg_MadeTrapNumInform844_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_MadeTrapNumInform844_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_MadeTrapNumInform844_ptr);
        using CPlayerSendMsg_MakeItemCheatSendButtonEnable846_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSendMsg_MakeItemCheatSendButtonEnable846_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSendMsg_MakeItemCheatSendButtonEnable846_ptr);
        using CPlayerSendMsg_MakeItemResult848_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_MakeItemResult848_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_MakeItemResult848_ptr);
        using CPlayerSendMsg_MapEnvInform850_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_MapEnvInform850_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_MapEnvInform850_ptr);
        using CPlayerSendMsg_MapOut852_ptr = void (WINAPIV*)(struct CPlayer*, char, char);
        using CPlayerSendMsg_MapOut852_clbk = void (WINAPIV*)(struct CPlayer*, char, char, CPlayerSendMsg_MapOut852_ptr);
        using CPlayerSendMsg_MaxHFSP854_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_MaxHFSP854_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_MaxHFSP854_ptr);
        using CPlayerSendMsg_MaxPvpPointInform856_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSendMsg_MaxPvpPointInform856_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSendMsg_MaxPvpPointInform856_ptr);
        using CPlayerSendMsg_MineCancle858_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_MineCancle858_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_MineCancle858_ptr);
        using CPlayerSendMsg_MineCompleteResult860_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, char, uint16_t);
        using CPlayerSendMsg_MineCompleteResult860_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, char, uint16_t, CPlayerSendMsg_MineCompleteResult860_ptr);
        using CPlayerSendMsg_MineStartResult862_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_MineStartResult862_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_MineStartResult862_ptr);
        using CPlayerSendMsg_MonsterAggroData864_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayerSendMsg_MonsterAggroData864_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayerSendMsg_MonsterAggroData864_ptr);
        using CPlayerSendMsg_MoveError866_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_MoveError866_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_MoveError866_ptr);
        using CPlayerSendMsg_MoveNext868_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSendMsg_MoveNext868_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSendMsg_MoveNext868_ptr);
        using CPlayerSendMsg_MovePortal870_ptr = void (WINAPIV*)(struct CPlayer*, char, float*, char);
        using CPlayerSendMsg_MovePortal870_clbk = void (WINAPIV*)(struct CPlayer*, char, float*, char, CPlayerSendMsg_MovePortal870_ptr);
        using CPlayerSendMsg_MovePortal872_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char, float*, bool);
        using CPlayerSendMsg_MovePortal872_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, float*, bool, CPlayerSendMsg_MovePortal872_ptr);
        using CPlayerSendMsg_MoveToOwnStoneMapInform874_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_MoveToOwnStoneMapInform874_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_MoveToOwnStoneMapInform874_ptr);
        using CPlayerSendMsg_MoveToOwnStoneMapResult876_ptr = void (WINAPIV*)(struct CPlayer*, char, char, float*);
        using CPlayerSendMsg_MoveToOwnStoneMapResult876_clbk = void (WINAPIV*)(struct CPlayer*, char, char, float*, CPlayerSendMsg_MoveToOwnStoneMapResult876_ptr);
        using CPlayerSendMsg_NPCLinkItemCheckResult878_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_POS_INDIV*);
        using CPlayerSendMsg_NPCLinkItemCheckResult878_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_POS_INDIV*, CPlayerSendMsg_NPCLinkItemCheckResult878_ptr);
        using CPlayerSendMsg_NewMovePotionResult880_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_NewMovePotionResult880_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_NewMovePotionResult880_ptr);
        using CPlayerSendMsg_NewViewOther882_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_NewViewOther882_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_NewViewOther882_ptr);
        using CPlayerSendMsg_NotifyEffectForGetItem884_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, struct _STORAGE_LIST::_db_con*, bool);
        using CPlayerSendMsg_NotifyEffectForGetItem884_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, struct _STORAGE_LIST::_db_con*, bool, CPlayerSendMsg_NotifyEffectForGetItem884_ptr);
        using CPlayerSendMsg_NotifyGetExpInfo886_ptr = void (WINAPIV*)(struct CPlayer*, long double, long double, long double);
        using CPlayerSendMsg_NotifyGetExpInfo886_clbk = void (WINAPIV*)(struct CPlayer*, long double, long double, long double, CPlayerSendMsg_NotifyGetExpInfo886_ptr);
        using CPlayerSendMsg_Notify_ExceptFromRaceRanking888_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSendMsg_Notify_ExceptFromRaceRanking888_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSendMsg_Notify_ExceptFromRaceRanking888_ptr);
        using CPlayerSendMsg_Notify_Get_Golden_Box890_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, struct _STORAGE_LIST::_db_con*, bool);
        using CPlayerSendMsg_Notify_Get_Golden_Box890_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, struct _STORAGE_LIST::_db_con*, bool, CPlayerSendMsg_Notify_Get_Golden_Box890_ptr);
        using CPlayerSendMsg_Notify_Gravity_Stone_Owner_Die892_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_Notify_Gravity_Stone_Owner_Die892_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_Notify_Gravity_Stone_Owner_Die892_ptr);
        using CPlayerSendMsg_Notify_Me_Get_Golden_Box894_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_Notify_Me_Get_Golden_Box894_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_Notify_Me_Get_Golden_Box894_ptr);
        using CPlayerSendMsg_NpcQuestHistoryInform896_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_NpcQuestHistoryInform896_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_NpcQuestHistoryInform896_ptr);
        using CPlayerSendMsg_NpcQuestListResult898_ptr = void (WINAPIV*)(struct CPlayer*, struct _NPCQuestIndexTempData*);
        using CPlayerSendMsg_NpcQuestListResult898_clbk = void (WINAPIV*)(struct CPlayer*, struct _NPCQuestIndexTempData*, CPlayerSendMsg_NpcQuestListResult898_ptr);
        using CPlayerSendMsg_OffPartResult900_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_OffPartResult900_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_OffPartResult900_ptr);
        using CPlayerSendMsg_OfferSuggestResult902_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_OfferSuggestResult902_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_OfferSuggestResult902_ptr);
        using CPlayerSendMsg_OreCuttingResult904_ptr = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int);
        using CPlayerSendMsg_OreCuttingResult904_clbk = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, CPlayerSendMsg_OreCuttingResult904_ptr);
        using CPlayerSendMsg_OreIntoBagResult906_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char, unsigned int);
        using CPlayerSendMsg_OreIntoBagResult906_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char, unsigned int, CPlayerSendMsg_OreIntoBagResult906_ptr);
        using CPlayerSendMsg_OreTransferCount908_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_OreTransferCount908_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_OreTransferCount908_ptr);
        using CPlayerSendMsg_OtherShapeAll910_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*);
        using CPlayerSendMsg_OtherShapeAll910_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, CPlayerSendMsg_OtherShapeAll910_ptr);
        using CPlayerSendMsg_OtherShapeError912_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, char);
        using CPlayerSendMsg_OtherShapeError912_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, char, CPlayerSendMsg_OtherShapeError912_ptr);
        using CPlayerSendMsg_OtherShapePart914_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*);
        using CPlayerSendMsg_OtherShapePart914_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, CPlayerSendMsg_OtherShapePart914_ptr);
        using CPlayerSendMsg_OtherShapePartEx_CashChange916_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, CPlayer::CashChangeStateFlag, CPlayer::CashChangeStateFlag);
        using CPlayerSendMsg_OtherShapePartEx_CashChange916_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, CPlayer::CashChangeStateFlag, CPlayer::CashChangeStateFlag, CPlayerSendMsg_OtherShapePartEx_CashChange916_ptr);
        using CPlayerSendMsg_PartyAlterLootShareResult918_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_PartyAlterLootShareResult918_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_PartyAlterLootShareResult918_ptr);
        using CPlayerSendMsg_PartyDisjointResult920_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_PartyDisjointResult920_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_PartyDisjointResult920_ptr);
        using CPlayerSendMsg_PartyJoinApplicationQuestion922_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*);
        using CPlayerSendMsg_PartyJoinApplicationQuestion922_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, CPlayerSendMsg_PartyJoinApplicationQuestion922_ptr);
        using CPlayerSendMsg_PartyJoinFailLevel924_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_PartyJoinFailLevel924_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_PartyJoinFailLevel924_ptr);
        using CPlayerSendMsg_PartyJoinInvitationQuestion926_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerSendMsg_PartyJoinInvitationQuestion926_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerSendMsg_PartyJoinInvitationQuestion926_ptr);
        using CPlayerSendMsg_PartyJoinJoinerResult928_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_PartyJoinJoinerResult928_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_PartyJoinJoinerResult928_ptr);
        using CPlayerSendMsg_PartyJoinMemberResult930_ptr = void (WINAPIV*)(struct CPlayer*, struct CPartyPlayer*, char);
        using CPlayerSendMsg_PartyJoinMemberResult930_clbk = void (WINAPIV*)(struct CPlayer*, struct CPartyPlayer*, char, CPlayerSendMsg_PartyJoinMemberResult930_ptr);
        using CPlayerSendMsg_PartyLeaveCompulsionResult932_ptr = void (WINAPIV*)(struct CPlayer*, struct CPartyPlayer*);
        using CPlayerSendMsg_PartyLeaveCompulsionResult932_clbk = void (WINAPIV*)(struct CPlayer*, struct CPartyPlayer*, CPlayerSendMsg_PartyLeaveCompulsionResult932_ptr);
        using CPlayerSendMsg_PartyLeaveSelfResult934_ptr = void (WINAPIV*)(struct CPlayer*, struct CPartyPlayer*, bool);
        using CPlayerSendMsg_PartyLeaveSelfResult934_clbk = void (WINAPIV*)(struct CPlayer*, struct CPartyPlayer*, bool, CPlayerSendMsg_PartyLeaveSelfResult934_ptr);
        using CPlayerSendMsg_PartyLockResult936_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_PartyLockResult936_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_PartyLockResult936_ptr);
        using CPlayerSendMsg_PartyLootItemInform938_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char, uint16_t, char);
        using CPlayerSendMsg_PartyLootItemInform938_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char, uint16_t, char, CPlayerSendMsg_PartyLootItemInform938_ptr);
        using CPlayerSendMsg_PartySuccessResult940_ptr = void (WINAPIV*)(struct CPlayer*, struct CPartyPlayer*);
        using CPlayerSendMsg_PartySuccessResult940_clbk = void (WINAPIV*)(struct CPlayer*, struct CPartyPlayer*, CPlayerSendMsg_PartySuccessResult940_ptr);
        using CPlayerSendMsg_PcRoomCharClass942_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerSendMsg_PcRoomCharClass942_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerSendMsg_PcRoomCharClass942_ptr);
        using CPlayerSendMsg_PcRoomError944_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_PcRoomError944_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_PcRoomError944_ptr);
        using CPlayerSendMsg_PostContent946_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, char, uint16_t, uint64_t, unsigned int, unsigned int);
        using CPlayerSendMsg_PostContent946_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, char, uint16_t, uint64_t, unsigned int, unsigned int, CPlayerSendMsg_PostContent946_ptr);
        using CPlayerSendMsg_PostDelete948_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_PostDelete948_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_PostDelete948_ptr);
        using CPlayerSendMsg_PostDelivery950_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, char*, bool, bool, char);
        using CPlayerSendMsg_PostDelivery950_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, char*, bool, bool, char, CPlayerSendMsg_PostDelivery950_ptr);
        using CPlayerSendMsg_PostItemGold952_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_PostItemGold952_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_PostItemGold952_ptr);
        using CPlayerSendMsg_PostReturn954_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, char*, char*, char, uint16_t, uint64_t, unsigned int, unsigned int);
        using CPlayerSendMsg_PostReturn954_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, char*, char*, char, uint16_t, uint64_t, unsigned int, unsigned int, CPlayerSendMsg_PostReturn954_ptr);
        using CPlayerSendMsg_PostReturnConfirm956_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_PostReturnConfirm956_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_PostReturnConfirm956_ptr);
        using CPlayerSendMsg_PostSendReply958_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_PostSendReply958_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_PostSendReply958_ptr);
        using CPlayerSendMsg_PotionDelayTime960_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int*, unsigned int);
        using CPlayerSendMsg_PotionDelayTime960_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int*, unsigned int, CPlayerSendMsg_PotionDelayTime960_ptr);
        using CPlayerSendMsg_PotionDivision962_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char, uint16_t, char, int);
        using CPlayerSendMsg_PotionDivision962_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, uint16_t, char, int, CPlayerSendMsg_PotionDivision962_ptr);
        using CPlayerSendMsg_PotionSeparation964_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char, uint16_t, char, int);
        using CPlayerSendMsg_PotionSeparation964_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, uint16_t, char, int, CPlayerSendMsg_PotionSeparation964_ptr);
        using CPlayerSendMsg_PremiumCashItemUse966_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerSendMsg_PremiumCashItemUse966_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerSendMsg_PremiumCashItemUse966_ptr);
        using CPlayerSendMsg_ProposeVoteResult968_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ProposeVoteResult968_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ProposeVoteResult968_ptr);
        using CPlayerSendMsg_PvpRankListVersionUp970_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_PvpRankListVersionUp970_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_PvpRankListVersionUp970_ptr);
        using CPlayerSendMsg_QuestComplete972_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_QuestComplete972_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_QuestComplete972_ptr);
        using CPlayerSendMsg_QuestDownloadResult974_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_QuestDownloadResult974_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_QuestDownloadResult974_ptr);
        using CPlayerSendMsg_QuestFailure976_ptr = void (WINAPIV*)(struct CPlayer*, char, char);
        using CPlayerSendMsg_QuestFailure976_clbk = void (WINAPIV*)(struct CPlayer*, char, char, CPlayerSendMsg_QuestFailure976_ptr);
        using CPlayerSendMsg_QuestGiveUpResult978_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_QuestGiveUpResult978_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_QuestGiveUpResult978_ptr);
        using CPlayerSendMsg_QuestHistoryDownloadResult980_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_QuestHistoryDownloadResult980_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_QuestHistoryDownloadResult980_ptr);
        using CPlayerSendMsg_QuestProcess982_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t);
        using CPlayerSendMsg_QuestProcess982_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, CPlayerSendMsg_QuestProcess982_ptr);
        using CPlayerSendMsg_RACE_Greeting984_ptr = void (WINAPIV*)(struct CPlayer*, char*, char*);
        using CPlayerSendMsg_RACE_Greeting984_clbk = void (WINAPIV*)(struct CPlayer*, char*, char*, CPlayerSendMsg_RACE_Greeting984_ptr);
        using CPlayerSendMsg_RaceBattlePenelty986_ptr = void (WINAPIV*)(struct CPlayer*, int, char);
        using CPlayerSendMsg_RaceBattlePenelty986_clbk = void (WINAPIV*)(struct CPlayer*, int, char, CPlayerSendMsg_RaceBattlePenelty986_ptr);
        using CPlayerSendMsg_RaceBossCryMsg988_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_RaceBossCryMsg988_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_RaceBossCryMsg988_ptr);
        using CPlayerSendMsg_RaceTopInform990_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSendMsg_RaceTopInform990_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSendMsg_RaceTopInform990_ptr);
        using CPlayerSendMsg_RadarCharSearchResult992_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_RadarCharSearchResult992_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_RadarCharSearchResult992_ptr);
        using CPlayerSendMsg_RadarDelayTime994_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerSendMsg_RadarDelayTime994_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerSendMsg_RadarDelayTime994_ptr);
        using CPlayerSendMsg_ReEnterAsk996_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int);
        using CPlayerSendMsg_ReEnterAsk996_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int, CPlayerSendMsg_ReEnterAsk996_ptr);
        using CPlayerSendMsg_ReEnterDarkHoleResult998_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ReEnterDarkHoleResult998_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ReEnterDarkHoleResult998_ptr);
        using CPlayerSendMsg_RealMovePoint1000_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSendMsg_RealMovePoint1000_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSendMsg_RealMovePoint1000_ptr);
        using CPlayerSendMsg_Recover1002_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_Recover1002_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_Recover1002_ptr);
        using CPlayerSendMsg_RecvHSKQuest1004_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_RecvHSKQuest1004_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_RecvHSKQuest1004_ptr);
        using CPlayerSendMsg_RefeshGroupTargetPosition1006_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_RefeshGroupTargetPosition1006_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_RefeshGroupTargetPosition1006_ptr);
        using CPlayerSendMsg_RegistBindResult1008_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_RegistBindResult1008_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_RegistBindResult1008_ptr);
        using CPlayerSendMsg_ReleaseGroupTargetObjectResult1010_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ReleaseGroupTargetObjectResult1010_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ReleaseGroupTargetObjectResult1010_ptr);
        using CPlayerSendMsg_ReleaseSiegeModeResult1012_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ReleaseSiegeModeResult1012_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ReleaseSiegeModeResult1012_ptr);
        using CPlayerSendMsg_RemainOreRate1014_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_RemainOreRate1014_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_RemainOreRate1014_ptr);
        using CPlayerSendMsg_RemainTimeInform1016_ptr = void (WINAPIV*)(struct CPlayer*, int16_t, int, struct _SYSTEMTIME*);
        using CPlayerSendMsg_RemainTimeInform1016_clbk = void (WINAPIV*)(struct CPlayer*, int16_t, int, struct _SYSTEMTIME*, CPlayerSendMsg_RemainTimeInform1016_ptr);
        using CPlayerSendMsg_ResDivision1018_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_ResDivision1018_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_ResDivision1018_ptr);
        using CPlayerSendMsg_ResSeparation1020_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_ResSeparation1020_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_ResSeparation1020_ptr);
        using CPlayerSendMsg_ResultChangeTaxRate1022_ptr = void (WINAPIV*)(struct CPlayer*, char, char);
        using CPlayerSendMsg_ResultChangeTaxRate1022_clbk = void (WINAPIV*)(struct CPlayer*, char, char, CPlayerSendMsg_ResultChangeTaxRate1022_ptr);
        using CPlayerSendMsg_ResultNpcQuest1024_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSendMsg_ResultNpcQuest1024_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSendMsg_ResultNpcQuest1024_ptr);
        using CPlayerSendMsg_Resurrect1026_ptr = void (WINAPIV*)(struct CPlayer*, char, bool);
        using CPlayerSendMsg_Resurrect1026_clbk = void (WINAPIV*)(struct CPlayer*, char, bool, CPlayerSendMsg_Resurrect1026_ptr);
        using CPlayerSendMsg_ResurrectInform1028_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_ResurrectInform1028_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_ResurrectInform1028_ptr);
        using CPlayerSendMsg_Revival1030_ptr = void (WINAPIV*)(struct CPlayer*, char, bool);
        using CPlayerSendMsg_Revival1030_clbk = void (WINAPIV*)(struct CPlayer*, char, bool, CPlayerSendMsg_Revival1030_ptr);
        using CPlayerSendMsg_RevivalOfJade1032_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerSendMsg_RevivalOfJade1032_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerSendMsg_RevivalOfJade1032_ptr);
        using CPlayerSendMsg_RewardAddItem1034_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, char);
        using CPlayerSendMsg_RewardAddItem1034_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, char, CPlayerSendMsg_RewardAddItem1034_ptr);
        using CPlayerSendMsg_SFDelayRequest1036_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_SFDelayRequest1036_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_SFDelayRequest1036_ptr);
        using CPlayerSendMsg_SelectClassResult1038_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t);
        using CPlayerSendMsg_SelectClassResult1038_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, CPlayerSendMsg_SelectClassResult1038_ptr);
        using CPlayerSendMsg_SelectQuestReward1040_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_SelectQuestReward1040_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_SelectQuestReward1040_ptr);
        using CPlayerSendMsg_SelectWaitedQuest1042_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char);
        using CPlayerSendMsg_SelectWaitedQuest1042_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char, CPlayerSendMsg_SelectWaitedQuest1042_ptr);
        using CPlayerSendMsg_SellItemStoreResult1044_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, char);
        using CPlayerSendMsg_SellItemStoreResult1044_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, char, CPlayerSendMsg_SellItemStoreResult1044_ptr);
        using CPlayerSendMsg_SetDPInform1046_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_SetDPInform1046_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_SetDPInform1046_ptr);
        using CPlayerSendMsg_SetFPInform1048_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_SetFPInform1048_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_SetFPInform1048_ptr);
        using CPlayerSendMsg_SetGroupMapPoint1050_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char, float*, char);
        using CPlayerSendMsg_SetGroupMapPoint1050_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, float*, char, CPlayerSendMsg_SetGroupMapPoint1050_ptr);
        using CPlayerSendMsg_SetGroupTargetObjectResult1052_ptr = void (WINAPIV*)(struct CPlayer*, char, char);
        using CPlayerSendMsg_SetGroupTargetObjectResult1052_clbk = void (WINAPIV*)(struct CPlayer*, char, char, CPlayerSendMsg_SetGroupTargetObjectResult1052_ptr);
        using CPlayerSendMsg_SetHPInform1054_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_SetHPInform1054_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_SetHPInform1054_ptr);
        using CPlayerSendMsg_SetItemCheckResult1056_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char);
        using CPlayerSendMsg_SetItemCheckResult1056_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char, CPlayerSendMsg_SetItemCheckResult1056_ptr);
        using CPlayerSendMsg_SetSPInform1058_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_SetSPInform1058_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_SetSPInform1058_ptr);
        using CPlayerSendMsg_SetTargetObjectResult1060_ptr = void (WINAPIV*)(struct CPlayer*, char, bool);
        using CPlayerSendMsg_SetTargetObjectResult1060_clbk = void (WINAPIV*)(struct CPlayer*, char, bool, CPlayerSendMsg_SetTargetObjectResult1060_ptr);
        using CPlayerSendMsg_SkillResult1062_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, char, int);
        using CPlayerSendMsg_SkillResult1062_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, char, int, CPlayerSendMsg_SkillResult1062_ptr);
        using CPlayerSendMsg_SpecialDownloadResult1064_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_SpecialDownloadResult1064_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_SpecialDownloadResult1064_ptr);
        using CPlayerSendMsg_StartContSF1066_ptr = void (WINAPIV*)(struct CPlayer*, struct _sf_continous*);
        using CPlayerSendMsg_StartContSF1066_clbk = void (WINAPIV*)(struct CPlayer*, struct _sf_continous*, CPlayerSendMsg_StartContSF1066_ptr);
        using CPlayerSendMsg_StartNewPos1068_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_StartNewPos1068_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_StartNewPos1068_ptr);
        using CPlayerSendMsg_StartShopping1070_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_StartShopping1070_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_StartShopping1070_ptr);
        using CPlayerSendMsg_StatInform1072_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char);
        using CPlayerSendMsg_StatInform1072_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char, CPlayerSendMsg_StatInform1072_ptr);
        using CPlayerSendMsg_StateInform1074_ptr = void (WINAPIV*)(struct CPlayer*, uint64_t);
        using CPlayerSendMsg_StateInform1074_clbk = void (WINAPIV*)(struct CPlayer*, uint64_t, CPlayerSendMsg_StateInform1074_ptr);
        using CPlayerSendMsg_Stop1076_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSendMsg_Stop1076_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSendMsg_Stop1076_ptr);
        using CPlayerSendMsg_StoreLimitItemAmountInfo1078_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, struct _limit_amount_info*);
        using CPlayerSendMsg_StoreLimitItemAmountInfo1078_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, struct _limit_amount_info*, CPlayerSendMsg_StoreLimitItemAmountInfo1078_ptr);
        using CPlayerSendMsg_StoreListResult1080_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_StoreListResult1080_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_StoreListResult1080_ptr);
        using CPlayerSendMsg_TLStatusInfo1082_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char);
        using CPlayerSendMsg_TLStatusInfo1082_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char, CPlayerSendMsg_TLStatusInfo1082_ptr);
        using CPlayerSendMsg_TLStatusPenalty1084_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_TLStatusPenalty1084_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_TLStatusPenalty1084_ptr);
        using CPlayerSendMsg_TakeAddResult1086_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_TakeAddResult1086_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_TakeAddResult1086_ptr);
        using CPlayerSendMsg_TakeNewResult1088_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_TakeNewResult1088_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_TakeNewResult1088_ptr);
        using CPlayerSendMsg_TalikCrystalExchangeResult1090_ptr = void (WINAPIV*)(struct CPlayer*, char, char, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_TalikCrystalExchangeResult1090_clbk = void (WINAPIV*)(struct CPlayer*, char, char, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_TalikCrystalExchangeResult1090_ptr);
        using CPlayerSendMsg_TargetObjectHPInform1092_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendMsg_TargetObjectHPInform1092_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendMsg_TargetObjectHPInform1092_ptr);
        using CPlayerSendMsg_TeleportError1094_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_TeleportError1094_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_TeleportError1094_ptr);
        using CPlayerSendMsg_TestAttackResult1096_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, char, char, int16_t*);
        using CPlayerSendMsg_TestAttackResult1096_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, char, char, int16_t*, CPlayerSendMsg_TestAttackResult1096_ptr);
        using CPlayerSendMsg_ThrowSkillResult1098_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, char);
        using CPlayerSendMsg_ThrowSkillResult1098_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, char, CPlayerSendMsg_ThrowSkillResult1098_ptr);
        using CPlayerSendMsg_ThrowStorageResult1100_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_ThrowStorageResult1100_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_ThrowStorageResult1100_ptr);
        using CPlayerSendMsg_ThrowUnitResult1102_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, uint16_t);
        using CPlayerSendMsg_ThrowUnitResult1102_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, uint16_t, CPlayerSendMsg_ThrowUnitResult1102_ptr);
        using CPlayerSendMsg_TowerContinue1104_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, struct CGuardTower*);
        using CPlayerSendMsg_TowerContinue1104_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, struct CGuardTower*, CPlayerSendMsg_TowerContinue1104_ptr);
        using CPlayerSendMsg_TransShipRenewTicketResult1106_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_TransShipRenewTicketResult1106_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_TransShipRenewTicketResult1106_ptr);
        using CPlayerSendMsg_TransformSiegeModeResult1108_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_TransformSiegeModeResult1108_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_TransformSiegeModeResult1108_ptr);
        using CPlayerSendMsg_TrunkChangPasswdResult1110_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_TrunkChangPasswdResult1110_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_TrunkChangPasswdResult1110_ptr);
        using CPlayerSendMsg_TrunkDownloadResult1112_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_TrunkDownloadResult1112_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_TrunkDownloadResult1112_ptr);
        using CPlayerSendMsg_TrunkEstResult1114_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_TrunkEstResult1114_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_TrunkEstResult1114_ptr);
        using CPlayerSendMsg_TrunkExtendResult1116_ptr = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, unsigned int);
        using CPlayerSendMsg_TrunkExtendResult1116_clbk = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, unsigned int, CPlayerSendMsg_TrunkExtendResult1116_ptr);
        using CPlayerSendMsg_TrunkHintAnswerResult1118_ptr = void (WINAPIV*)(struct CPlayer*, char, char*);
        using CPlayerSendMsg_TrunkHintAnswerResult1118_clbk = void (WINAPIV*)(struct CPlayer*, char, char*, CPlayerSendMsg_TrunkHintAnswerResult1118_ptr);
        using CPlayerSendMsg_TrunkIoMoneyResult1120_ptr = void (WINAPIV*)(struct CPlayer*, char, long double, long double, unsigned int, unsigned int, unsigned int);
        using CPlayerSendMsg_TrunkIoMoneyResult1120_clbk = void (WINAPIV*)(struct CPlayer*, char, long double, long double, unsigned int, unsigned int, unsigned int, CPlayerSendMsg_TrunkIoMoneyResult1120_ptr);
        using CPlayerSendMsg_TrunkIoResult1122_ptr = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, unsigned int);
        using CPlayerSendMsg_TrunkIoResult1122_clbk = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, unsigned int, CPlayerSendMsg_TrunkIoResult1122_ptr);
        using CPlayerSendMsg_TrunkPotionDivision1124_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, uint16_t, uint16_t, int);
        using CPlayerSendMsg_TrunkPotionDivision1124_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, uint16_t, uint16_t, int, CPlayerSendMsg_TrunkPotionDivision1124_ptr);
        using CPlayerSendMsg_TrunkPwHintIndexResult1126_ptr = void (WINAPIV*)(struct CPlayer*, char, char);
        using CPlayerSendMsg_TrunkPwHintIndexResult1126_clbk = void (WINAPIV*)(struct CPlayer*, char, char, CPlayerSendMsg_TrunkPwHintIndexResult1126_ptr);
        using CPlayerSendMsg_TrunkResDivision1128_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*);
        using CPlayerSendMsg_TrunkResDivision1128_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _STORAGE_LIST::_db_con*, struct _STORAGE_LIST::_db_con*, CPlayerSendMsg_TrunkResDivision1128_ptr);
        using CPlayerSendMsg_UILock_FindPW_Result1130_ptr = void (WINAPIV*)(struct CPlayer*, char, char*, char);
        using CPlayerSendMsg_UILock_FindPW_Result1130_clbk = void (WINAPIV*)(struct CPlayer*, char, char*, char, CPlayerSendMsg_UILock_FindPW_Result1130_ptr);
        using CPlayerSendMsg_UILock_Init_Request_ToAccount1132_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char*, uint16_t, char, char*);
        using CPlayerSendMsg_UILock_Init_Request_ToAccount1132_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char*, uint16_t, char, char*, CPlayerSendMsg_UILock_Init_Request_ToAccount1132_ptr);
        using CPlayerSendMsg_UILock_Init_Result1134_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_UILock_Init_Result1134_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_UILock_Init_Result1134_ptr);
        using CPlayerSendMsg_UILock_Login_Result1136_ptr = void (WINAPIV*)(struct CPlayer*, char, char);
        using CPlayerSendMsg_UILock_Login_Result1136_clbk = void (WINAPIV*)(struct CPlayer*, char, char, CPlayerSendMsg_UILock_Login_Result1136_ptr);
        using CPlayerSendMsg_UILock_Update_Request_ToAccount1138_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char*, uint16_t, char, char*);
        using CPlayerSendMsg_UILock_Update_Request_ToAccount1138_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char*, uint16_t, char, char*, CPlayerSendMsg_UILock_Update_Request_ToAccount1138_ptr);
        using CPlayerSendMsg_UILock_Update_Result1140_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_UILock_Update_Result1140_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_UILock_Update_Result1140_ptr);
        using CPlayerSendMsg_UnitAlterFeeInform1142_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_UnitAlterFeeInform1142_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_UnitAlterFeeInform1142_ptr);
        using CPlayerSendMsg_UnitBulletFillResult1144_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t*, unsigned int*);
        using CPlayerSendMsg_UnitBulletFillResult1144_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t*, unsigned int*, CPlayerSendMsg_UnitBulletFillResult1144_ptr);
        using CPlayerSendMsg_UnitBulletReplaceResult1146_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_UnitBulletReplaceResult1146_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_UnitBulletReplaceResult1146_ptr);
        using CPlayerSendMsg_UnitDeliveryResult1148_ptr = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, unsigned int);
        using CPlayerSendMsg_UnitDeliveryResult1148_clbk = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, unsigned int, CPlayerSendMsg_UnitDeliveryResult1148_ptr);
        using CPlayerSendMsg_UnitDestroy1150_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_UnitDestroy1150_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_UnitDestroy1150_ptr);
        using CPlayerSendMsg_UnitForceReturnInform1152_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_UnitForceReturnInform1152_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_UnitForceReturnInform1152_ptr);
        using CPlayerSendMsg_UnitFrameBuyResult1154_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char, uint16_t, uint16_t, unsigned int*);
        using CPlayerSendMsg_UnitFrameBuyResult1154_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, uint16_t, uint16_t, unsigned int*, CPlayerSendMsg_UnitFrameBuyResult1154_ptr);
        using CPlayerSendMsg_UnitFrameRepairResult1156_ptr = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, unsigned int);
        using CPlayerSendMsg_UnitFrameRepairResult1156_clbk = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, unsigned int, CPlayerSendMsg_UnitFrameRepairResult1156_ptr);
        using CPlayerSendMsg_UnitLeaveResult1158_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_UnitLeaveResult1158_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_UnitLeaveResult1158_ptr);
        using CPlayerSendMsg_UnitPackFillResult1160_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char, struct _unit_pack_fill_request_clzo::__list*, unsigned int*);
        using CPlayerSendMsg_UnitPackFillResult1160_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, struct _unit_pack_fill_request_clzo::__list*, unsigned int*, CPlayerSendMsg_UnitPackFillResult1160_ptr);
        using CPlayerSendMsg_UnitPartTuningResult1162_ptr = void (WINAPIV*)(struct CPlayer*, char, char, int*);
        using CPlayerSendMsg_UnitPartTuningResult1162_clbk = void (WINAPIV*)(struct CPlayer*, char, char, int*, CPlayerSendMsg_UnitPartTuningResult1162_ptr);
        using CPlayerSendMsg_UnitReturnResult1164_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSendMsg_UnitReturnResult1164_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSendMsg_UnitReturnResult1164_ptr);
        using CPlayerSendMsg_UnitRideChange1166_ptr = void (WINAPIV*)(struct CPlayer*, bool, struct CParkingUnit*);
        using CPlayerSendMsg_UnitRideChange1166_clbk = void (WINAPIV*)(struct CPlayer*, bool, struct CParkingUnit*, CPlayerSendMsg_UnitRideChange1166_ptr);
        using CPlayerSendMsg_UnitSellResult1168_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, int, unsigned int, unsigned int, unsigned int);
        using CPlayerSendMsg_UnitSellResult1168_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, int, unsigned int, unsigned int, unsigned int, CPlayerSendMsg_UnitSellResult1168_ptr);
        using CPlayerSendMsg_UnitTakeResult1170_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSendMsg_UnitTakeResult1170_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSendMsg_UnitTakeResult1170_ptr);
        using CPlayerSendMsg_UpdateTLStatusInfo1172_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char);
        using CPlayerSendMsg_UpdateTLStatusInfo1172_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char, CPlayerSendMsg_UpdateTLStatusInfo1172_ptr);
        using CPlayerSendMsg_UsPotionResultOther1174_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, struct CPlayer*, bool);
        using CPlayerSendMsg_UsPotionResultOther1174_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, struct CPlayer*, bool, CPlayerSendMsg_UsPotionResultOther1174_ptr);
        using CPlayerSendMsg_UseJadeResult1176_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t);
        using CPlayerSendMsg_UseJadeResult1176_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, CPlayerSendMsg_UseJadeResult1176_ptr);
        using CPlayerSendMsg_UsePotionResult1178_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char);
        using CPlayerSendMsg_UsePotionResult1178_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, char, CPlayerSendMsg_UsePotionResult1178_ptr);
        using CPlayerSendMsg_UseRadarResult1180_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, unsigned int);
        using CPlayerSendMsg_UseRadarResult1180_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, unsigned int, CPlayerSendMsg_UseRadarResult1180_ptr);
        using CPlayerSendMsg_VoteResult1182_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char);
        using CPlayerSendMsg_VoteResult1182_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char, CPlayerSendMsg_VoteResult1182_ptr);
        using CPlayerSendTargetMonsterSFContInfo1184_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendTargetMonsterSFContInfo1184_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendTargetMonsterSFContInfo1184_ptr);
        using CPlayerSendTargetPlayerDamageContInfo1186_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSendTargetPlayerDamageContInfo1186_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSendTargetPlayerDamageContInfo1186_ptr);
        using CPlayerSenseState1188_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSenseState1188_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSenseState1188_ptr);
        using CPlayerSetAttackPart1190_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSetAttackPart1190_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSetAttackPart1190_ptr);
        using CPlayerSetBattleMode1192_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSetBattleMode1192_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSetBattleMode1192_ptr);
        using CPlayerSetBindDummy1194_ptr = void (WINAPIV*)(struct CPlayer*, struct _dummy_position*);
        using CPlayerSetBindDummy1194_clbk = void (WINAPIV*)(struct CPlayer*, struct _dummy_position*, CPlayerSetBindDummy1194_ptr);
        using CPlayerSetBindMapData1196_ptr = void (WINAPIV*)(struct CPlayer*, struct CMapData*);
        using CPlayerSetBindMapData1196_clbk = void (WINAPIV*)(struct CPlayer*, struct CMapData*, CPlayerSetBindMapData1196_ptr);
        using CPlayerSetBindPosition1198_ptr = bool (WINAPIV*)(struct CPlayer*, struct CMapData*, struct _dummy_position*);
        using CPlayerSetBindPosition1198_clbk = bool (WINAPIV*)(struct CPlayer*, struct CMapData*, struct _dummy_position*, CPlayerSetBindPosition1198_ptr);
        using CPlayerSetCashAmount1200_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSetCashAmount1200_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSetCashAmount1200_ptr);
        using CPlayerSetCntEnable1202_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSetCntEnable1202_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSetCntEnable1202_ptr);
        using CPlayerSetDP1204_ptr = bool (WINAPIV*)(struct CPlayer*, int, bool);
        using CPlayerSetDP1204_clbk = bool (WINAPIV*)(struct CPlayer*, int, bool, CPlayerSetDP1204_ptr);
        using CPlayerSetDamage1206_ptr = int (WINAPIV*)(struct CPlayer*, int, struct CCharacter*, int, bool, int, unsigned int, bool);
        using CPlayerSetDamage1206_clbk = int (WINAPIV*)(struct CPlayer*, int, struct CCharacter*, int, bool, int, unsigned int, bool, CPlayerSetDamage1206_ptr);
        using CPlayerSetEffectEquipCode1208_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char);
        using CPlayerSetEffectEquipCode1208_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, CPlayerSetEffectEquipCode1208_ptr);
        using CPlayerSetEquipEffect1210_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_storage_con*, bool);
        using CPlayerSetEquipEffect1210_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_storage_con*, bool, CPlayerSetEquipEffect1210_ptr);
        using CPlayerSetEquipJadeEffect1212_ptr = void (WINAPIV*)(struct CPlayer*, int, float, bool);
        using CPlayerSetEquipJadeEffect1212_clbk = void (WINAPIV*)(struct CPlayer*, int, float, bool, CPlayerSetEquipJadeEffect1212_ptr);
        using CPlayerSetFP1214_ptr = bool (WINAPIV*)(struct CPlayer*, int, bool);
        using CPlayerSetFP1214_clbk = bool (WINAPIV*)(struct CPlayer*, int, bool, CPlayerSetFP1214_ptr);
        using CPlayerSetGauge1216_ptr = void (WINAPIV*)(struct CPlayer*, int, int, bool);
        using CPlayerSetGauge1216_clbk = void (WINAPIV*)(struct CPlayer*, int, int, bool, CPlayerSetGauge1216_ptr);
        using CPlayerSetGrade1218_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSetGrade1218_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSetGrade1218_ptr);
        using CPlayerSetHP1220_ptr = bool (WINAPIV*)(struct CPlayer*, int, bool);
        using CPlayerSetHP1220_clbk = bool (WINAPIV*)(struct CPlayer*, int, bool, CPlayerSetHP1220_ptr);
        using CPlayerSetHaveEffect1222_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSetHaveEffect1222_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSetHaveEffect1222_ptr);
        using CPlayerSetHaveEffectUseTime1224_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, bool);
        using CPlayerSetHaveEffectUseTime1224_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, bool, CPlayerSetHaveEffectUseTime1224_ptr);
        using CPlayerSetLastAttBuff1226_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerSetLastAttBuff1226_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerSetLastAttBuff1226_ptr);
        using CPlayerSetLevel1228_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSetLevel1228_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSetLevel1228_ptr);
        using CPlayerSetLevelD1230_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerSetLevelD1230_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerSetLevelD1230_ptr);
        using CPlayerSetMstHaveEffect1232_ptr = void (WINAPIV*)(struct CPlayer*, struct _ResourceItem_fld*, struct _STORAGE_LIST::_db_con*, bool, int);
        using CPlayerSetMstHaveEffect1232_clbk = void (WINAPIV*)(struct CPlayer*, struct _ResourceItem_fld*, struct _STORAGE_LIST::_db_con*, bool, int, CPlayerSetMstHaveEffect1232_ptr);
        using CPlayerSetMstPt1234_ptr = void (WINAPIV*)(struct CPlayer*, int, float, bool, int);
        using CPlayerSetMstPt1234_clbk = void (WINAPIV*)(struct CPlayer*, int, float, bool, int, CPlayerSetMstPt1234_ptr);
        using CPlayerSetPotionActDelay1236_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, unsigned int);
        using CPlayerSetPotionActDelay1236_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, unsigned int, CPlayerSetPotionActDelay1236_ptr);
        using CPlayerSetPvpPointLeak1238_ptr = void (WINAPIV*)(struct CPlayer*, long double);
        using CPlayerSetPvpPointLeak1238_clbk = void (WINAPIV*)(struct CPlayer*, long double, CPlayerSetPvpPointLeak1238_ptr);
        using CPlayerSetRankRate1240_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int);
        using CPlayerSetRankRate1240_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int, CPlayerSetRankRate1240_ptr);
        using CPlayerSetSP1242_ptr = bool (WINAPIV*)(struct CPlayer*, int, bool);
        using CPlayerSetSP1242_clbk = bool (WINAPIV*)(struct CPlayer*, int, bool, CPlayerSetSP1242_ptr);
        using CPlayerSetShapeAllBuffer1244_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSetShapeAllBuffer1244_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSetShapeAllBuffer1244_ptr);
        using CPlayerSetSiege1246_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*);
        using CPlayerSetSiege1246_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, CPlayerSetSiege1246_ptr);
        using CPlayerSetStateFlag1248_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSetStateFlag1248_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSetStateFlag1248_ptr);
        using CPlayerSetStaticMember1250_ptr = void (WINAPIV*)();
        using CPlayerSetStaticMember1250_clbk = void (WINAPIV*)(CPlayerSetStaticMember1250_ptr);
        using CPlayerSetTarPos1252_ptr = bool (WINAPIV*)(struct CPlayer*, float*, bool);
        using CPlayerSetTarPos1252_clbk = bool (WINAPIV*)(struct CPlayer*, float*, bool, CPlayerSetTarPos1252_ptr);
        using CPlayerSetUseReleaseRaceBuffPotion1254_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerSetUseReleaseRaceBuffPotion1254_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerSetUseReleaseRaceBuffPotion1254_ptr);
        using CPlayerSetVote1256_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSetVote1256_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSetVote1256_ptr);
        using CPlayerSortPost1258_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerSortPost1258_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerSortPost1258_ptr);
        using CPlayerSubActPoint1260_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerSubActPoint1260_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerSubActPoint1260_ptr);
        using CPlayerSubDalant1262_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerSubDalant1262_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerSubDalant1262_ptr);
        using CPlayerSubGold1264_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerSubGold1264_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerSubGold1264_ptr);
        using CPlayerSubPoint1266_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerSubPoint1266_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerSubPoint1266_ptr);
        using CPlayerSumMinuteBetween1268_ptr = unsigned int (WINAPIV*)(struct CPlayer*, struct _SYSTEMTIME*, struct _SYSTEMTIME*);
        using CPlayerSumMinuteBetween1268_clbk = unsigned int (WINAPIV*)(struct CPlayer*, struct _SYSTEMTIME*, struct _SYSTEMTIME*, CPlayerSumMinuteBetween1268_ptr);
        using CPlayerSumMinuteOne1270_ptr = unsigned int (WINAPIV*)(struct CPlayer*, struct _SYSTEMTIME*);
        using CPlayerSumMinuteOne1270_clbk = unsigned int (WINAPIV*)(struct CPlayer*, struct _SYSTEMTIME*, CPlayerSumMinuteOne1270_ptr);
        using CPlayerTakeGravityStone1272_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerTakeGravityStone1272_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerTakeGravityStone1272_ptr);
        using CPlayerUpdateAuraSFCont1274_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerUpdateAuraSFCont1274_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerUpdateAuraSFCont1274_ptr);
        using CPlayerUpdateChaosModeState1276_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerUpdateChaosModeState1276_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerUpdateChaosModeState1276_ptr);
        using CPlayerUpdateDelPost1278_ptr = bool (WINAPIV*)(struct CPlayer*, unsigned int, int);
        using CPlayerUpdateDelPost1278_clbk = bool (WINAPIV*)(struct CPlayer*, unsigned int, int, CPlayerUpdateDelPost1278_ptr);
        using CPlayerUpdateLastCriTicket1280_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char, char, char, char);
        using CPlayerUpdateLastCriTicket1280_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, char, char, char, CPlayerUpdateLastCriTicket1280_ptr);
        using CPlayerUpdateLastMetalTicket1282_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char, char, char, char);
        using CPlayerUpdateLastMetalTicket1282_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, char, char, char, CPlayerUpdateLastMetalTicket1282_ptr);
        using CPlayerUpdatePost1284_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerUpdatePost1284_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerUpdatePost1284_ptr);
        using CPlayerUpdatePostAddLog1286_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, bool, int);
        using CPlayerUpdatePostAddLog1286_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, bool, int, CPlayerUpdatePostAddLog1286_ptr);
        using CPlayerUpdatePvpOrderView1288_ptr = void (WINAPIV*)(struct CPlayer*, int64_t);
        using CPlayerUpdatePvpOrderView1288_clbk = void (WINAPIV*)(struct CPlayer*, int64_t, CPlayerUpdatePvpOrderView1288_ptr);
        using CPlayerUpdatePvpPointLimiter1290_ptr = void (WINAPIV*)(struct CPlayer*, int64_t);
        using CPlayerUpdatePvpPointLimiter1290_clbk = void (WINAPIV*)(struct CPlayer*, int64_t, CPlayerUpdatePvpPointLimiter1290_ptr);
        using CPlayerUpdateReturnPost1292_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerUpdateReturnPost1292_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerUpdateReturnPost1292_ptr);
        using CPlayerUpdateVisualVer1294_ptr = void (WINAPIV*)(struct CPlayer*, CPlayer::CashChangeStateFlag);
        using CPlayerUpdateVisualVer1294_clbk = void (WINAPIV*)(struct CPlayer*, CPlayer::CashChangeStateFlag, CPlayerUpdateVisualVer1294_ptr);
        using CPlayerUpdate_GoldPoint1296_ptr = bool (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerUpdate_GoldPoint1296_clbk = bool (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerUpdate_GoldPoint1296_ptr);
        using CPlayerUpdatedMasteryWriteHistory1298_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerUpdatedMasteryWriteHistory1298_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerUpdatedMasteryWriteHistory1298_ptr);
        using CPlayerWPActiveForce1300_ptr = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int, struct _force_fld*);
        using CPlayerWPActiveForce1300_clbk = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int, struct _force_fld*, CPlayerWPActiveForce1300_ptr);
        using CPlayerWPActiveSkill1302_ptr = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int, struct _skill_fld*, int);
        using CPlayerWPActiveSkill1302_clbk = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int, struct _skill_fld*, int, CPlayerWPActiveSkill1302_ptr);
        using CPlayerWeaponSFActive1304_ptr = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int*, int*, uint16_t);
        using CPlayerWeaponSFActive1304_clbk = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int*, int*, uint16_t, CPlayerWeaponSFActive1304_ptr);
        using CPlayer_AnimusReturn1306_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayer_AnimusReturn1306_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayer_AnimusReturn1306_ptr);
        using CPlayer_CalcMaxFP1308_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayer_CalcMaxFP1308_clbk = int (WINAPIV*)(struct CPlayer*, CPlayer_CalcMaxFP1308_ptr);
        using CPlayer_CalcMaxHP1310_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayer_CalcMaxHP1310_clbk = int (WINAPIV*)(struct CPlayer*, CPlayer_CalcMaxHP1310_ptr);
        using CPlayer_CalcMaxSP1312_ptr = int (WINAPIV*)(struct CPlayer*);
        using CPlayer_CalcMaxSP1312_clbk = int (WINAPIV*)(struct CPlayer*, CPlayer_CalcMaxSP1312_ptr);
        using CPlayer_CheckForcePullUnit1314_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayer_CheckForcePullUnit1314_clbk = void (WINAPIV*)(struct CPlayer*, CPlayer_CheckForcePullUnit1314_ptr);
        using CPlayer_DeleteUnitKey1316_ptr = uint16_t (WINAPIV*)(struct CPlayer*, char);
        using CPlayer_DeleteUnitKey1316_clbk = uint16_t (WINAPIV*)(struct CPlayer*, char, CPlayer_DeleteUnitKey1316_ptr);
        using CPlayer_GetItemEffect1318_ptr = struct _ITEM_EFFECT* (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*);
        using CPlayer_GetItemEffect1318_clbk = struct _ITEM_EFFECT* (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, CPlayer_GetItemEffect1318_ptr);
        using CPlayer_GetPartyMemberInCircle1320_ptr = char (WINAPIV*)(struct CPlayer*, struct CPlayer**, int, bool);
        using CPlayer_GetPartyMemberInCircle1320_clbk = char (WINAPIV*)(struct CPlayer*, struct CPlayer**, int, bool, CPlayer_GetPartyMemberInCircle1320_ptr);
        using CPlayer_LockUnitKey1322_ptr = bool (WINAPIV*)(struct CPlayer*, char, bool);
        using CPlayer_LockUnitKey1322_clbk = bool (WINAPIV*)(struct CPlayer*, char, bool, CPlayer_LockUnitKey1322_ptr);
        using CPlayer_Reward_Quest1324_ptr = struct _Quest_fld* (WINAPIV*)(struct CPlayer*, struct _Quest_fld*, char);
        using CPlayer_Reward_Quest1324_clbk = struct _Quest_fld* (WINAPIV*)(struct CPlayer*, struct _Quest_fld*, char, CPlayer_Reward_Quest1324_ptr);
        using CPlayer_TowerAllReturn1326_ptr = void (WINAPIV*)(struct CPlayer*, char, bool);
        using CPlayer_TowerAllReturn1326_clbk = void (WINAPIV*)(struct CPlayer*, char, bool, CPlayer_TowerAllReturn1326_ptr);
        using CPlayer_TowerDestroy1328_ptr = void (WINAPIV*)(struct CPlayer*, struct CGuardTower*);
        using CPlayer_TowerDestroy1328_clbk = void (WINAPIV*)(struct CPlayer*, struct CGuardTower*, CPlayer_TowerDestroy1328_ptr);
        using CPlayer_TowerReturn1330_ptr = uint16_t (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*);
        using CPlayer_TowerReturn1330_clbk = uint16_t (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, CPlayer_TowerReturn1330_ptr);
        using CPlayer_TrapDestroy1332_ptr = void (WINAPIV*)(struct CPlayer*, struct CTrap*, char);
        using CPlayer_TrapDestroy1332_clbk = void (WINAPIV*)(struct CPlayer*, struct CTrap*, char, CPlayer_TrapDestroy1332_ptr);
        using CPlayer_TrapReturn1334_ptr = void (WINAPIV*)(struct CPlayer*, struct CTrap*, uint16_t);
        using CPlayer_TrapReturn1334_clbk = void (WINAPIV*)(struct CPlayer*, struct CTrap*, uint16_t, CPlayer_TrapReturn1334_ptr);
        using CPlayer_UnitDestroy1336_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayer_UnitDestroy1336_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayer_UnitDestroy1336_ptr);
        using CPlayer_UpdateUnitDebt1338_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayer_UpdateUnitDebt1338_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayer_UpdateUnitDebt1338_ptr);
        using CPlayer_check_dst_param_after_attack1340_ptr = void (WINAPIV*)(struct CPlayer*, int, struct CCharacter*);
        using CPlayer_check_dst_param_after_attack1340_clbk = void (WINAPIV*)(struct CPlayer*, int, struct CCharacter*, CPlayer_check_dst_param_after_attack1340_ptr);
        using CPlayer_check_embel_part1342_ptr = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*);
        using CPlayer_check_embel_part1342_clbk = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, CPlayer_check_embel_part1342_ptr);
        using CPlayer_check_equip_part1344_ptr = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*);
        using CPlayer_check_equip_part1344_clbk = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, CPlayer_check_equip_part1344_ptr);
        using CPlayer_check_equipmastery_lim1346_ptr = int (WINAPIV*)(struct CPlayer*, int);
        using CPlayer_check_equipmastery_lim1346_clbk = int (WINAPIV*)(struct CPlayer*, int, CPlayer_check_equipmastery_lim1346_ptr);
        using CPlayer_check_exp_after_attack1348_ptr = int (WINAPIV*)(struct CPlayer*, int, struct _be_damaged_char*, struct CPartyModeKillMonsterExpNotify*);
        using CPlayer_check_exp_after_attack1348_clbk = int (WINAPIV*)(struct CPlayer*, int, struct _be_damaged_char*, struct CPartyModeKillMonsterExpNotify*, CPlayer_check_exp_after_attack1348_ptr);
        using CPlayer_check_guild_target_object1350_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayer_check_guild_target_object1350_clbk = void (WINAPIV*)(struct CPlayer*, CPlayer_check_guild_target_object1350_ptr);
        using CPlayer_check_hp_send_party1352_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayer_check_hp_send_party1352_clbk = void (WINAPIV*)(struct CPlayer*, CPlayer_check_hp_send_party1352_ptr);
        using CPlayer_check_mastery_cum_lim1354_ptr = unsigned int (WINAPIV*)(struct CPlayer*, char, char);
        using CPlayer_check_mastery_cum_lim1354_clbk = unsigned int (WINAPIV*)(struct CPlayer*, char, char, CPlayer_check_mastery_cum_lim1354_ptr);
        using CPlayer_check_mastery_lim1356_ptr = unsigned int (WINAPIV*)(struct CPlayer*, char, char);
        using CPlayer_check_mastery_lim1356_clbk = unsigned int (WINAPIV*)(struct CPlayer*, char, char, CPlayer_check_mastery_lim1356_ptr);
        using CPlayer_check_party_target_object1358_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayer_check_party_target_object1358_clbk = void (WINAPIV*)(struct CPlayer*, CPlayer_check_party_target_object1358_ptr);
        using CPlayer_check_race_target_object1360_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayer_check_race_target_object1360_clbk = void (WINAPIV*)(struct CPlayer*, CPlayer_check_race_target_object1360_ptr);
        using CPlayer_check_target_object1362_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayer_check_target_object1362_clbk = void (WINAPIV*)(struct CPlayer*, CPlayer_check_target_object1362_ptr);
        using CPlayer_pre_check_force_attack1364_ptr = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, uint16_t, struct _force_fld**, struct _STORAGE_LIST::_db_con**, uint16_t*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**);
        using CPlayer_pre_check_force_attack1364_clbk = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, uint16_t, struct _force_fld**, struct _STORAGE_LIST::_db_con**, uint16_t*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, CPlayer_pre_check_force_attack1364_ptr);
        using CPlayer_pre_check_in_guild_battle1366_ptr = int (WINAPIV*)(struct CPlayer*, struct CCharacter*);
        using CPlayer_pre_check_in_guild_battle1366_clbk = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, CPlayer_pre_check_in_guild_battle1366_ptr);
        using CPlayer_pre_check_in_guild_battle_race1368_ptr = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, bool);
        using CPlayer_pre_check_in_guild_battle_race1368_clbk = bool (WINAPIV*)(struct CPlayer*, struct CCharacter*, bool, CPlayer_pre_check_in_guild_battle_race1368_ptr);
        using CPlayer_pre_check_normal_attack1370_ptr = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, uint16_t, bool, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**);
        using CPlayer_pre_check_normal_attack1370_clbk = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, uint16_t, bool, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, CPlayer_pre_check_normal_attack1370_ptr);
        using CPlayer_pre_check_siege_attack1372_ptr = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**);
        using CPlayer_pre_check_siege_attack1372_clbk = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, CPlayer_pre_check_siege_attack1372_ptr);
        using CPlayer_pre_check_skill_attack1374_ptr = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, struct _skill_fld*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, int, uint16_t*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**);
        using CPlayer_pre_check_skill_attack1374_clbk = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, struct _skill_fld*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, int, uint16_t*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, CPlayer_pre_check_skill_attack1374_ptr);
        using CPlayer_pre_check_skill_enable1376_ptr = bool (WINAPIV*)(struct CPlayer*, struct _skill_fld*);
        using CPlayer_pre_check_skill_enable1376_clbk = bool (WINAPIV*)(struct CPlayer*, struct _skill_fld*, CPlayer_pre_check_skill_enable1376_ptr);
        using CPlayer_pre_check_skill_gradelimit1378_ptr = bool (WINAPIV*)(struct CPlayer*, struct _skill_fld*);
        using CPlayer_pre_check_skill_gradelimit1378_clbk = bool (WINAPIV*)(struct CPlayer*, struct _skill_fld*, CPlayer_pre_check_skill_gradelimit1378_ptr);
        using CPlayer_pre_check_unit_attack1380_ptr = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, char, struct _UnitPart_fld**, struct _UnitBullet_fld**, struct _unit_bullet_param**);
        using CPlayer_pre_check_unit_attack1380_clbk = int (WINAPIV*)(struct CPlayer*, struct CCharacter*, char, struct _UnitPart_fld**, struct _UnitBullet_fld**, struct _unit_bullet_param**, CPlayer_pre_check_unit_attack1380_ptr);
        using CPlayer_pre_check_wpactive_force_attack1382_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayer_pre_check_wpactive_force_attack1382_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayer_pre_check_wpactive_force_attack1382_ptr);
        using CPlayer_pre_check_wpactive_skill_attack1384_ptr = bool (WINAPIV*)(struct CPlayer*, char, struct _skill_fld*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**);
        using CPlayer_pre_check_wpactive_skill_attack1384_clbk = bool (WINAPIV*)(struct CPlayer*, char, struct _skill_fld*, uint16_t, struct _STORAGE_LIST::_db_con**, struct _BulletItem_fld**, CPlayer_pre_check_wpactive_skill_attack1384_ptr);
        using CPlayer_set_db_sf_effect1386_ptr = void (WINAPIV*)(struct CPlayer*, struct _SFCONT_DB_BASE*);
        using CPlayer_set_db_sf_effect1386_clbk = void (WINAPIV*)(struct CPlayer*, struct _SFCONT_DB_BASE*, CPlayer_set_db_sf_effect1386_ptr);
        using CPlayerapply_case_equip_std_effect1391_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, bool);
        using CPlayerapply_case_equip_std_effect1391_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, bool, CPlayerapply_case_equip_std_effect1391_ptr);
        using CPlayerapply_case_equip_upgrade_effect1393_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, bool);
        using CPlayerapply_case_equip_upgrade_effect1393_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, bool, CPlayerapply_case_equip_upgrade_effect1393_ptr);
        using CPlayerapply_have_item_std_effect1395_ptr = void (WINAPIV*)(struct CPlayer*, int, float, bool, int);
        using CPlayerapply_have_item_std_effect1395_clbk = void (WINAPIV*)(struct CPlayer*, int, float, bool, int, CPlayerapply_have_item_std_effect1395_ptr);
        using CPlayerapply_normal_item_std_effect1397_ptr = void (WINAPIV*)(struct CPlayer*, int, float, bool);
        using CPlayerapply_normal_item_std_effect1397_clbk = void (WINAPIV*)(struct CPlayer*, int, float, bool, CPlayerapply_normal_item_std_effect1397_ptr);
        using CPlayerdev_SetGuildGrade1399_ptr = bool (WINAPIV*)(struct CPlayer*, char);
        using CPlayerdev_SetGuildGrade1399_clbk = bool (WINAPIV*)(struct CPlayer*, char, CPlayerdev_SetGuildGrade1399_ptr);
        using CPlayerdev_SetGuildGradeByGuildSerial1401_ptr = bool (WINAPIV*)(struct CPlayer*, unsigned int, char);
        using CPlayerdev_SetGuildGradeByGuildSerial1401_clbk = bool (WINAPIV*)(struct CPlayer*, unsigned int, char, CPlayerdev_SetGuildGradeByGuildSerial1401_ptr);
        using CPlayerdev_SetGuildGradeByName1403_ptr = bool (WINAPIV*)(struct CPlayer*, char*, char);
        using CPlayerdev_SetGuildGradeByName1403_clbk = bool (WINAPIV*)(struct CPlayer*, char*, char, CPlayerdev_SetGuildGradeByName1403_ptr);
        using CPlayerdev_after_effect1405_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_after_effect1405_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_after_effect1405_ptr);
        using CPlayerdev_all_kill1407_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_all_kill1407_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_all_kill1407_ptr);
        using CPlayerdev_animus_recall_time_free1409_ptr = bool (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerdev_animus_recall_time_free1409_clbk = bool (WINAPIV*)(struct CPlayer*, bool, CPlayerdev_animus_recall_time_free1409_ptr);
        using CPlayerdev_avator_copy1411_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerdev_avator_copy1411_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayerdev_avator_copy1411_ptr);
        using CPlayerdev_change_class1413_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerdev_change_class1413_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayerdev_change_class1413_ptr);
        using CPlayerdev_cont_effect_del1415_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_cont_effect_del1415_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_cont_effect_del1415_ptr);
        using CPlayerdev_cont_effect_time1417_ptr = bool (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerdev_cont_effect_time1417_clbk = bool (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerdev_cont_effect_time1417_ptr);
        using CPlayerdev_dalant1419_ptr = bool (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerdev_dalant1419_clbk = bool (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerdev_dalant1419_ptr);
        using CPlayerdev_die1421_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_die1421_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_die1421_ptr);
        using CPlayerdev_drop_item1423_ptr = bool (WINAPIV*)(struct CPlayer*, char*, int, char*, int);
        using CPlayerdev_drop_item1423_clbk = bool (WINAPIV*)(struct CPlayer*, char*, int, char*, int, CPlayerdev_drop_item1423_ptr);
        using CPlayerdev_free_sf_by_class1425_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_free_sf_by_class1425_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_free_sf_by_class1425_ptr);
        using CPlayerdev_full_animus_gauge1427_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_full_animus_gauge1427_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_full_animus_gauge1427_ptr);
        using CPlayerdev_full_force1429_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_full_force1429_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_full_force1429_ptr);
        using CPlayerdev_full_point1431_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_full_point1431_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_full_point1431_ptr);
        using CPlayerdev_gold1433_ptr = bool (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerdev_gold1433_clbk = bool (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerdev_gold1433_ptr);
        using CPlayerdev_goto_monster1435_ptr = bool (WINAPIV*)(struct CPlayer*, struct CMonster*);
        using CPlayerdev_goto_monster1435_clbk = bool (WINAPIV*)(struct CPlayer*, struct CMonster*, CPlayerdev_goto_monster1435_ptr);
        using CPlayerdev_goto_npc1437_ptr = bool (WINAPIV*)(struct CPlayer*, struct CMerchant*);
        using CPlayerdev_goto_npc1437_clbk = bool (WINAPIV*)(struct CPlayer*, struct CMerchant*, CPlayerdev_goto_npc1437_ptr);
        using CPlayerdev_half_inven_amount1439_ptr = bool (WINAPIV*)(struct CPlayer*, uint64_t);
        using CPlayerdev_half_inven_amount1439_clbk = bool (WINAPIV*)(struct CPlayer*, uint64_t, CPlayerdev_half_inven_amount1439_ptr);
        using CPlayerdev_half_point1441_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_half_point1441_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_half_point1441_ptr);
        using CPlayerdev_init_monster1443_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_init_monster1443_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_init_monster1443_ptr);
        using CPlayerdev_inven_empty1445_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_inven_empty1445_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_inven_empty1445_ptr);
        using CPlayerdev_item_make_no_use_matrial1447_ptr = bool (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerdev_item_make_no_use_matrial1447_clbk = bool (WINAPIV*)(struct CPlayer*, bool, CPlayerdev_item_make_no_use_matrial1447_ptr);
        using CPlayerdev_loot_bag1449_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_loot_bag1449_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_loot_bag1449_ptr);
        using CPlayerdev_loot_free1451_ptr = bool (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerdev_loot_free1451_clbk = bool (WINAPIV*)(struct CPlayer*, bool, CPlayerdev_loot_free1451_ptr);
        using CPlayerdev_loot_fullitem1453_ptr = bool (WINAPIV*)(struct CPlayer*, char);
        using CPlayerdev_loot_fullitem1453_clbk = bool (WINAPIV*)(struct CPlayer*, char, CPlayerdev_loot_fullitem1453_ptr);
        using CPlayerdev_loot_item1455_ptr = bool (WINAPIV*)(struct CPlayer*, char*, int, char*, int);
        using CPlayerdev_loot_item1455_clbk = bool (WINAPIV*)(struct CPlayer*, char*, int, char*, int, CPlayerdev_loot_item1455_ptr);
        using CPlayerdev_loot_material1457_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_loot_material1457_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_loot_material1457_ptr);
        using CPlayerdev_loot_mine1459_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_loot_mine1459_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_loot_mine1459_ptr);
        using CPlayerdev_loot_tower1461_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_loot_tower1461_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_loot_tower1461_ptr);
        using CPlayerdev_lv1463_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayerdev_lv1463_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayerdev_lv1463_ptr);
        using CPlayerdev_make_succ1465_ptr = bool (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerdev_make_succ1465_clbk = bool (WINAPIV*)(struct CPlayer*, bool, CPlayerdev_make_succ1465_ptr);
        using CPlayerdev_max_level_ext1467_ptr = bool (WINAPIV*)(struct CPlayer*, char);
        using CPlayerdev_max_level_ext1467_clbk = bool (WINAPIV*)(struct CPlayer*, char, CPlayerdev_max_level_ext1467_ptr);
        using CPlayerdev_never_die1469_ptr = bool (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerdev_never_die1469_clbk = bool (WINAPIV*)(struct CPlayer*, bool, CPlayerdev_never_die1469_ptr);
        using CPlayerdev_quest_complete1471_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_quest_complete1471_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_quest_complete1471_ptr);
        using CPlayerdev_quest_complete_other1473_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerdev_quest_complete_other1473_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayerdev_quest_complete_other1473_ptr);
        using CPlayerdev_set_animus_exp1475_ptr = bool (WINAPIV*)(struct CPlayer*, uint64_t);
        using CPlayerdev_set_animus_exp1475_clbk = bool (WINAPIV*)(struct CPlayer*, uint64_t, CPlayerdev_set_animus_exp1475_ptr);
        using CPlayerdev_set_animus_lv1477_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayerdev_set_animus_lv1477_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayerdev_set_animus_lv1477_ptr);
        using CPlayerdev_set_hp1479_ptr = bool (WINAPIV*)(struct CPlayer*, float);
        using CPlayerdev_set_hp1479_clbk = bool (WINAPIV*)(struct CPlayer*, float, CPlayerdev_set_hp1479_ptr);
        using CPlayerdev_trap_attack_grade1481_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayerdev_trap_attack_grade1481_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayerdev_trap_attack_grade1481_ptr);
        using CPlayerdev_up_all1483_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayerdev_up_all1483_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayerdev_up_all1483_ptr);
        using CPlayerdev_up_all_pt1485_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayerdev_up_all_pt1485_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayerdev_up_all_pt1485_ptr);
        using CPlayerdev_up_cashbag1487_ptr = bool (WINAPIV*)(struct CPlayer*, long double);
        using CPlayerdev_up_cashbag1487_clbk = bool (WINAPIV*)(struct CPlayer*, long double, CPlayerdev_up_cashbag1487_ptr);
        using CPlayerdev_up_forceitem1489_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayerdev_up_forceitem1489_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayerdev_up_forceitem1489_ptr);
        using CPlayerdev_up_forcemastery1491_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayerdev_up_forcemastery1491_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayerdev_up_forcemastery1491_ptr);
        using CPlayerdev_up_mastery1493_ptr = bool (WINAPIV*)(struct CPlayer*, int, int, int);
        using CPlayerdev_up_mastery1493_clbk = bool (WINAPIV*)(struct CPlayer*, int, int, int, CPlayerdev_up_mastery1493_ptr);
        using CPlayerdev_up_pvp1495_ptr = bool (WINAPIV*)(struct CPlayer*, long double);
        using CPlayerdev_up_pvp1495_clbk = bool (WINAPIV*)(struct CPlayer*, long double, CPlayerdev_up_pvp1495_ptr);
        using CPlayerdev_up_skill1497_ptr = bool (WINAPIV*)(struct CPlayer*, char*, int);
        using CPlayerdev_up_skill1497_clbk = bool (WINAPIV*)(struct CPlayer*, char*, int, CPlayerdev_up_skill1497_ptr);
        using CPlayerdev_view_boss1499_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerdev_view_boss1499_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerdev_view_boss1499_ptr);
        using CPlayerdev_view_method1501_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerdev_view_method1501_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayerdev_view_method1501_ptr);
        using CPlayermake_force_attack_param1503_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, struct _force_fld*, struct _STORAGE_LIST::_db_con*, float*, struct _attack_param*, struct _STORAGE_LIST::_db_con*, float);
        using CPlayermake_force_attack_param1503_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, struct _force_fld*, struct _STORAGE_LIST::_db_con*, float*, struct _attack_param*, struct _STORAGE_LIST::_db_con*, float, CPlayermake_force_attack_param1503_ptr);
        using CPlayermake_gen_attack_param1505_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, char, struct _BulletItem_fld*, float, struct _attack_param*, struct _BulletItem_fld*, float);
        using CPlayermake_gen_attack_param1505_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, char, struct _BulletItem_fld*, float, struct _attack_param*, struct _BulletItem_fld*, float, CPlayermake_gen_attack_param1505_ptr);
        using CPlayermake_siege_attack_param1507_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, struct _BulletItem_fld*, float, struct _attack_param*, struct _BulletItem_fld*, float);
        using CPlayermake_siege_attack_param1507_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, struct _BulletItem_fld*, float, struct _attack_param*, struct _BulletItem_fld*, float, CPlayermake_siege_attack_param1507_ptr);
        using CPlayermake_skill_attack_param1509_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, struct _skill_fld*, int, struct _STORAGE_LIST::_db_con*, float, struct _attack_param*, struct _STORAGE_LIST::_db_con*, float);
        using CPlayermake_skill_attack_param1509_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, struct _skill_fld*, int, struct _STORAGE_LIST::_db_con*, float, struct _attack_param*, struct _STORAGE_LIST::_db_con*, float, CPlayermake_skill_attack_param1509_ptr);
        using CPlayermake_unit_attack_param1511_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, struct _UnitPart_fld*, float, struct _attack_param*);
        using CPlayermake_unit_attack_param1511_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, struct _UnitPart_fld*, float, struct _attack_param*, CPlayermake_unit_attack_param1511_ptr);
        using CPlayermake_wpactive_force_attack_param1513_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, struct _force_fld*, float*, struct _attack_param*);
        using CPlayermake_wpactive_force_attack_param1513_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, struct _force_fld*, float*, struct _attack_param*, CPlayermake_wpactive_force_attack_param1513_ptr);
        using CPlayermake_wpactive_skill_attack_param1515_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, struct _skill_fld*, float*, char, int, struct _STORAGE_LIST::_db_con*, float, struct _attack_param*, int*);
        using CPlayermake_wpactive_skill_attack_param1515_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, struct _skill_fld*, float*, char, int, struct _STORAGE_LIST::_db_con*, float, struct _attack_param*, int*, CPlayermake_wpactive_skill_attack_param1515_ptr);
        using CPlayermgr_MaxAttackPoint1517_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayermgr_MaxAttackPoint1517_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayermgr_MaxAttackPoint1517_ptr);
        using CPlayermgr_TrunkInit1519_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayermgr_TrunkInit1519_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayermgr_TrunkInit1519_ptr);
        using CPlayermgr_all_item_muzi1521_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayermgr_all_item_muzi1521_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayermgr_all_item_muzi1521_ptr);
        using CPlayermgr_change_degree1523_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayermgr_change_degree1523_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayermgr_change_degree1523_ptr);
        using CPlayermgr_defense_item_grace1525_ptr = bool (WINAPIV*)(struct CPlayer*, char, int);
        using CPlayermgr_defense_item_grace1525_clbk = bool (WINAPIV*)(struct CPlayer*, char, int, CPlayermgr_defense_item_grace1525_ptr);
        using CPlayermgr_destroy_system_tower1527_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayermgr_destroy_system_tower1527_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayermgr_destroy_system_tower1527_ptr);
        using CPlayermgr_dungeon_pass1529_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayermgr_dungeon_pass1529_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayermgr_dungeon_pass1529_ptr);
        using CPlayermgr_exit_keeper1531_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayermgr_exit_keeper1531_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayermgr_exit_keeper1531_ptr);
        using CPlayermgr_exit_stone1533_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayermgr_exit_stone1533_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayermgr_exit_stone1533_ptr);
        using CPlayermgr_free_ride_ship1535_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayermgr_free_ride_ship1535_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayermgr_free_ride_ship1535_ptr);
        using CPlayermgr_gotoCoordinates1537_ptr = bool (WINAPIV*)(struct CPlayer*, char*, float, float, float);
        using CPlayermgr_gotoCoordinates1537_clbk = bool (WINAPIV*)(struct CPlayer*, char*, float, float, float, CPlayermgr_gotoCoordinates1537_ptr);
        using CPlayermgr_gotoDstCoordinates1539_ptr = bool (WINAPIV*)(struct CPlayer*, char*, char*, float, float, float);
        using CPlayermgr_gotoDstCoordinates1539_clbk = bool (WINAPIV*)(struct CPlayer*, char*, char*, float, float, float, CPlayermgr_gotoDstCoordinates1539_ptr);
        using CPlayermgr_goto_mine1541_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayermgr_goto_mine1541_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayermgr_goto_mine1541_ptr);
        using CPlayermgr_goto_shipport1543_ptr = bool (WINAPIV*)(struct CPlayer*, int, int);
        using CPlayermgr_goto_shipport1543_clbk = bool (WINAPIV*)(struct CPlayer*, int, int, CPlayermgr_goto_shipport1543_ptr);
        using CPlayermgr_goto_stone1545_ptr = bool (WINAPIV*)(struct CPlayer*, char);
        using CPlayermgr_goto_stone1545_clbk = bool (WINAPIV*)(struct CPlayer*, char, CPlayermgr_goto_stone1545_ptr);
        using CPlayermgr_goto_store1547_ptr = bool (WINAPIV*)(struct CPlayer*, int, char*);
        using CPlayermgr_goto_store1547_clbk = bool (WINAPIV*)(struct CPlayer*, int, char*, CPlayermgr_goto_store1547_ptr);
        using CPlayermgr_holykeeper_start1549_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayermgr_holykeeper_start1549_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayermgr_holykeeper_start1549_ptr);
        using CPlayermgr_holystone_start1551_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayermgr_holystone_start1551_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayermgr_holystone_start1551_ptr);
        using CPlayermgr_item_telekinesis1553_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayermgr_item_telekinesis1553_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayermgr_item_telekinesis1553_ptr);
        using CPlayermgr_kick1555_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayermgr_kick1555_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayermgr_kick1555_ptr);
        using CPlayermgr_make_system_tower1557_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayermgr_make_system_tower1557_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayermgr_make_system_tower1557_ptr);
        using CPlayermgr_matchless1559_ptr = bool (WINAPIV*)(struct CPlayer*, bool);
        using CPlayermgr_matchless1559_clbk = bool (WINAPIV*)(struct CPlayer*, bool, CPlayermgr_matchless1559_ptr);
        using CPlayermgr_pass_sch_one_step1561_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayermgr_pass_sch_one_step1561_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayermgr_pass_sch_one_step1561_ptr);
        using CPlayermgr_recall_guild_player1563_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayermgr_recall_guild_player1563_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayermgr_recall_guild_player1563_ptr);
        using CPlayermgr_recall_mon1565_ptr = bool (WINAPIV*)(struct CPlayer*, char*, int);
        using CPlayermgr_recall_mon1565_clbk = bool (WINAPIV*)(struct CPlayer*, char*, int, CPlayermgr_recall_mon1565_ptr);
        using CPlayermgr_recall_party_player1567_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayermgr_recall_party_player1567_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayermgr_recall_party_player1567_ptr);
        using CPlayermgr_recall_player1569_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayermgr_recall_player1569_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayermgr_recall_player1569_ptr);
        using CPlayermgr_resurrect_player1571_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayermgr_resurrect_player1571_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayermgr_resurrect_player1571_ptr);
        using CPlayermgr_set_animus_attack_point1573_ptr = bool (WINAPIV*)(struct CPlayer*, int);
        using CPlayermgr_set_animus_attack_point1573_clbk = bool (WINAPIV*)(struct CPlayer*, int, CPlayermgr_set_animus_attack_point1573_ptr);
        using CPlayermgr_tracing1575_ptr = bool (WINAPIV*)(struct CPlayer*, bool);
        using CPlayermgr_tracing1575_clbk = bool (WINAPIV*)(struct CPlayer*, bool, CPlayermgr_tracing1575_ptr);
        using CPlayermgr_user_ban1577_ptr = bool (WINAPIV*)(struct CPlayer*, char*, int, char*, char);
        using CPlayermgr_user_ban1577_clbk = bool (WINAPIV*)(struct CPlayer*, char*, int, char*, char, CPlayermgr_user_ban1577_ptr);
        using CPlayermgr_whisper1579_ptr = bool (WINAPIV*)(struct CPlayer*, char*);
        using CPlayermgr_whisper1579_clbk = bool (WINAPIV*)(struct CPlayer*, char*, CPlayermgr_whisper1579_ptr);
        using CPlayerpc_AddBag1581_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerpc_AddBag1581_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerpc_AddBag1581_ptr);
        using CPlayerpc_AlterItemSlotRequest1583_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _alter_item_slot_request_clzo::__list*);
        using CPlayerpc_AlterItemSlotRequest1583_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _alter_item_slot_request_clzo::__list*, CPlayerpc_AlterItemSlotRequest1583_ptr);
        using CPlayerpc_AlterLinkBoardSlotRequest1585_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _alter_link_slot_request_clzo::__list*, char);
        using CPlayerpc_AlterLinkBoardSlotRequest1585_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _alter_link_slot_request_clzo::__list*, char, CPlayerpc_AlterLinkBoardSlotRequest1585_ptr);
        using CPlayerpc_AlterWindowInfoRequest1587_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int*, unsigned int*, unsigned int*, unsigned int*, unsigned int, unsigned int*);
        using CPlayerpc_AlterWindowInfoRequest1587_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int*, unsigned int*, unsigned int*, unsigned int*, unsigned int, unsigned int*, CPlayerpc_AlterWindowInfoRequest1587_ptr);
        using CPlayerpc_AnimusCommandRequest1589_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_AnimusCommandRequest1589_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_AnimusCommandRequest1589_ptr);
        using CPlayerpc_AnimusInvenChange1591_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t);
        using CPlayerpc_AnimusInvenChange1591_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t, CPlayerpc_AnimusInvenChange1591_ptr);
        using CPlayerpc_AnimusRecallRequest1593_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, uint16_t);
        using CPlayerpc_AnimusRecallRequest1593_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, uint16_t, CPlayerpc_AnimusRecallRequest1593_ptr);
        using CPlayerpc_AnimusReturnRequest1595_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_AnimusReturnRequest1595_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_AnimusReturnRequest1595_ptr);
        using CPlayerpc_AnimusTargetRequest1597_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t, unsigned int);
        using CPlayerpc_AnimusTargetRequest1597_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t, unsigned int, CPlayerpc_AnimusTargetRequest1597_ptr);
        using CPlayerpc_AwayPartyJoinInvitationAnswer1599_ptr = void (WINAPIV*)(struct CPlayer*, struct _CLID*, char);
        using CPlayerpc_AwayPartyJoinInvitationAnswer1599_clbk = void (WINAPIV*)(struct CPlayer*, struct _CLID*, char, CPlayerpc_AwayPartyJoinInvitationAnswer1599_ptr);
        using CPlayerpc_AwaypartyInvitationRequest1601_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_AwaypartyInvitationRequest1601_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_AwaypartyInvitationRequest1601_ptr);
        using CPlayerpc_BackTowerRequest1603_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_BackTowerRequest1603_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_BackTowerRequest1603_ptr);
        using CPlayerpc_BackTrapRequest1605_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, uint16_t);
        using CPlayerpc_BackTrapRequest1605_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, uint16_t, CPlayerpc_BackTrapRequest1605_ptr);
        using CPlayerpc_BillingInfoRequest1607_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_BillingInfoRequest1607_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_BillingInfoRequest1607_ptr);
        using CPlayerpc_BriefPass1609_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_BriefPass1609_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_BriefPass1609_ptr);
        using CPlayerpc_BuddyAddAnswer1611_ptr = void (WINAPIV*)(struct CPlayer*, bool, uint16_t, unsigned int);
        using CPlayerpc_BuddyAddAnswer1611_clbk = void (WINAPIV*)(struct CPlayer*, bool, uint16_t, unsigned int, CPlayerpc_BuddyAddAnswer1611_ptr);
        using CPlayerpc_BuddyAddRequest1613_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int, char*);
        using CPlayerpc_BuddyAddRequest1613_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int, char*, CPlayerpc_BuddyAddRequest1613_ptr);
        using CPlayerpc_BuddyDelRequest1615_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_BuddyDelRequest1615_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_BuddyDelRequest1615_ptr);
        using CPlayerpc_BuddyDownloadRequest1617_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_BuddyDownloadRequest1617_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_BuddyDownloadRequest1617_ptr);
        using CPlayerpc_BuyItemStore1619_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, char, struct _buy_store_request_clzo::_list*, int);
        using CPlayerpc_BuyItemStore1619_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, char, struct _buy_store_request_clzo::_list*, int, CPlayerpc_BuyItemStore1619_ptr);
        using CPlayerpc_CanSelectClassRequest1621_ptr = char (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_CanSelectClassRequest1621_clbk = char (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_CanSelectClassRequest1621_ptr);
        using CPlayerpc_CastVoteRequest1623_ptr = void (WINAPIV*)(struct CPlayer*, int, char);
        using CPlayerpc_CastVoteRequest1623_clbk = void (WINAPIV*)(struct CPlayer*, int, char, CPlayerpc_CastVoteRequest1623_ptr);
        using CPlayerpc_ChangeModeType1625_ptr = void (WINAPIV*)(struct CPlayer*, int, int);
        using CPlayerpc_ChangeModeType1625_clbk = void (WINAPIV*)(struct CPlayer*, int, int, CPlayerpc_ChangeModeType1625_ptr);
        using CPlayerpc_CharacterRenameCash1627_ptr = bool (WINAPIV*)(struct CPlayer*, bool, struct _STORAGE_POS_INDIV*, char*);
        using CPlayerpc_CharacterRenameCash1627_clbk = bool (WINAPIV*)(struct CPlayer*, bool, struct _STORAGE_POS_INDIV*, char*, CPlayerpc_CharacterRenameCash1627_ptr);
        using CPlayerpc_CharacterRenameCheck1629_ptr = char (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_CharacterRenameCheck1629_clbk = char (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_CharacterRenameCheck1629_ptr);
        using CPlayerpc_ChatAllRequest1631_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatAllRequest1631_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatAllRequest1631_ptr);
        using CPlayerpc_ChatCircleRequest1633_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatCircleRequest1633_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatCircleRequest1633_ptr);
        using CPlayerpc_ChatFarRequest1635_ptr = void (WINAPIV*)(struct CPlayer*, char*, char*);
        using CPlayerpc_ChatFarRequest1635_clbk = void (WINAPIV*)(struct CPlayer*, char*, char*, CPlayerpc_ChatFarRequest1635_ptr);
        using CPlayerpc_ChatGmNoticeRequest1637_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatGmNoticeRequest1637_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatGmNoticeRequest1637_ptr);
        using CPlayerpc_ChatGuildEstSenRequest1639_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatGuildEstSenRequest1639_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatGuildEstSenRequest1639_ptr);
        using CPlayerpc_ChatGuildRequest1641_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char*);
        using CPlayerpc_ChatGuildRequest1641_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char*, CPlayerpc_ChatGuildRequest1641_ptr);
        using CPlayerpc_ChatMapRequest1643_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatMapRequest1643_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatMapRequest1643_ptr);
        using CPlayerpc_ChatMgrWhisperRequest1645_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatMgrWhisperRequest1645_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatMgrWhisperRequest1645_ptr);
        using CPlayerpc_ChatMultiFarRequest1647_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _w_name*, char*);
        using CPlayerpc_ChatMultiFarRequest1647_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _w_name*, char*, CPlayerpc_ChatMultiFarRequest1647_ptr);
        using CPlayerpc_ChatOperatorRequest1649_ptr = void (WINAPIV*)(struct CPlayer*, char, char*);
        using CPlayerpc_ChatOperatorRequest1649_clbk = void (WINAPIV*)(struct CPlayer*, char, char*, CPlayerpc_ChatOperatorRequest1649_ptr);
        using CPlayerpc_ChatPartyRequest1651_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatPartyRequest1651_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatPartyRequest1651_ptr);
        using CPlayerpc_ChatRaceBossCryRequest1653_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatRaceBossCryRequest1653_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatRaceBossCryRequest1653_ptr);
        using CPlayerpc_ChatRaceBossRequest1655_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatRaceBossRequest1655_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatRaceBossRequest1655_ptr);
        using CPlayerpc_ChatRaceRequest1657_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatRaceRequest1657_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatRaceRequest1657_ptr);
        using CPlayerpc_ChatRePresentationRequest1659_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_ChatRePresentationRequest1659_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_ChatRePresentationRequest1659_ptr);
        using CPlayerpc_ChatTradeRequestMsg1661_ptr = void (WINAPIV*)(struct CPlayer*, char, char*);
        using CPlayerpc_ChatTradeRequestMsg1661_clbk = void (WINAPIV*)(struct CPlayer*, char, char*, CPlayerpc_ChatTradeRequestMsg1661_ptr);
        using CPlayerpc_ClassSkillRequest1663_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, struct _CHRID*, uint16_t*);
        using CPlayerpc_ClassSkillRequest1663_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, struct _CHRID*, uint16_t*, CPlayerpc_ClassSkillRequest1663_ptr);
        using CPlayerpc_CombineItem1665_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char, struct _STORAGE_POS_INDIV*, uint16_t);
        using CPlayerpc_CombineItem1665_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, struct _STORAGE_POS_INDIV*, uint16_t, CPlayerpc_CombineItem1665_ptr);
        using CPlayerpc_CombineItemEx1667_ptr = void (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_request_clzo*);
        using CPlayerpc_CombineItemEx1667_clbk = void (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_request_clzo*, CPlayerpc_CombineItemEx1667_ptr);
        using CPlayerpc_CombineItemExAccept1669_ptr = void (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_accept_request_clzo*);
        using CPlayerpc_CombineItemExAccept1669_clbk = void (WINAPIV*)(struct CPlayer*, struct _combine_ex_item_accept_request_clzo*, CPlayerpc_CombineItemExAccept1669_ptr);
        using CPlayerpc_CuttingComplete1671_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_CuttingComplete1671_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_CuttingComplete1671_ptr);
        using CPlayerpc_DTradeAddRequest1673_ptr = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, char);
        using CPlayerpc_DTradeAddRequest1673_clbk = void (WINAPIV*)(struct CPlayer*, char, char, unsigned int, char, CPlayerpc_DTradeAddRequest1673_ptr);
        using CPlayerpc_DTradeAnswerRequest1675_ptr = void (WINAPIV*)(struct CPlayer*, struct _CLID*);
        using CPlayerpc_DTradeAnswerRequest1675_clbk = void (WINAPIV*)(struct CPlayer*, struct _CLID*, CPlayerpc_DTradeAnswerRequest1675_ptr);
        using CPlayerpc_DTradeAskRequest1677_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerpc_DTradeAskRequest1677_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerpc_DTradeAskRequest1677_ptr);
        using CPlayerpc_DTradeBetRequest1679_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int);
        using CPlayerpc_DTradeBetRequest1679_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, CPlayerpc_DTradeBetRequest1679_ptr);
        using CPlayerpc_DTradeCancleRequest1681_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_DTradeCancleRequest1681_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_DTradeCancleRequest1681_ptr);
        using CPlayerpc_DTradeDelRequest1683_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_DTradeDelRequest1683_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_DTradeDelRequest1683_ptr);
        using CPlayerpc_DTradeLockRequest1685_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_DTradeLockRequest1685_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_DTradeLockRequest1685_ptr);
        using CPlayerpc_DTradeOKRequest1687_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int*);
        using CPlayerpc_DTradeOKRequest1687_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int*, CPlayerpc_DTradeOKRequest1687_ptr);
        using CPlayerpc_DarkHoleAnswerReenterRequest1689_ptr = void (WINAPIV*)(struct CPlayer*, bool, uint16_t, unsigned int);
        using CPlayerpc_DarkHoleAnswerReenterRequest1689_clbk = void (WINAPIV*)(struct CPlayer*, bool, uint16_t, unsigned int, CPlayerpc_DarkHoleAnswerReenterRequest1689_ptr);
        using CPlayerpc_DarkHoleClearOutRequest1691_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_DarkHoleClearOutRequest1691_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_DarkHoleClearOutRequest1691_ptr);
        using CPlayerpc_DarkHoleEnterRequest1693_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int);
        using CPlayerpc_DarkHoleEnterRequest1693_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, unsigned int, CPlayerpc_DarkHoleEnterRequest1693_ptr);
        using CPlayerpc_DarkHoleGiveupOutRequest1695_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_DarkHoleGiveupOutRequest1695_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_DarkHoleGiveupOutRequest1695_ptr);
        using CPlayerpc_DarkHoleOpenRequest1697_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_DarkHoleOpenRequest1697_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_DarkHoleOpenRequest1697_ptr);
        using CPlayerpc_DowngradeItem1699_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, struct _STORAGE_POS_INDIV*, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_DowngradeItem1699_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, struct _STORAGE_POS_INDIV*, struct _STORAGE_POS_INDIV*, CPlayerpc_DowngradeItem1699_ptr);
        using CPlayerpc_EmbellishPart1701_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t);
        using CPlayerpc_EmbellishPart1701_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t, CPlayerpc_EmbellishPart1701_ptr);
        using CPlayerpc_EquipPart1703_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_EquipPart1703_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, CPlayerpc_EquipPart1703_ptr);
        using CPlayerpc_ExchangeDalantForGold1705_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_ExchangeDalantForGold1705_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_ExchangeDalantForGold1705_ptr);
        using CPlayerpc_ExchangeGoldForDalant1707_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_ExchangeGoldForDalant1707_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_ExchangeGoldForDalant1707_ptr);
        using CPlayerpc_ExchangeGoldForPvP1709_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_ExchangeGoldForPvP1709_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_ExchangeGoldForPvP1709_ptr);
        using CPlayerpc_ExchangeItem1711_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t);
        using CPlayerpc_ExchangeItem1711_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, CPlayerpc_ExchangeItem1711_ptr);
        using CPlayerpc_ExitWorldRequest1713_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_ExitWorldRequest1713_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_ExitWorldRequest1713_ptr);
        using CPlayerpc_ForceInvenChange1715_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t);
        using CPlayerpc_ForceInvenChange1715_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t, CPlayerpc_ForceInvenChange1715_ptr);
        using CPlayerpc_ForceRequest1717_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, struct _CHRID*, uint16_t*);
        using CPlayerpc_ForceRequest1717_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, struct _CHRID*, uint16_t*, CPlayerpc_ForceRequest1717_ptr);
        using CPlayerpc_GestureRequest1719_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_GestureRequest1719_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_GestureRequest1719_ptr);
        using CPlayerpc_GiveItem1721_ptr = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, char*, bool);
        using CPlayerpc_GiveItem1721_clbk = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_LIST::_db_con*, char*, bool, CPlayerpc_GiveItem1721_ptr);
        using CPlayerpc_GotoAvatorRequest1723_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_GotoAvatorRequest1723_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_GotoAvatorRequest1723_ptr);
        using CPlayerpc_GotoBasePortalRequest1725_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerpc_GotoBasePortalRequest1725_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerpc_GotoBasePortalRequest1725_ptr);
        using CPlayerpc_GuildBattleBlock1727_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerpc_GuildBattleBlock1727_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerpc_GuildBattleBlock1727_ptr);
        using CPlayerpc_GuildCancelSuggestRequest1729_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_GuildCancelSuggestRequest1729_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_GuildCancelSuggestRequest1729_ptr);
        using CPlayerpc_GuildDownLoadRequest1731_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_GuildDownLoadRequest1731_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_GuildDownLoadRequest1731_ptr);
        using CPlayerpc_GuildEstablishRequest1733_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_GuildEstablishRequest1733_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_GuildEstablishRequest1733_ptr);
        using CPlayerpc_GuildHonorListRequest1735_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_GuildHonorListRequest1735_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_GuildHonorListRequest1735_ptr);
        using CPlayerpc_GuildJoinAcceptRequest1737_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, bool);
        using CPlayerpc_GuildJoinAcceptRequest1737_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, bool, CPlayerpc_GuildJoinAcceptRequest1737_ptr);
        using CPlayerpc_GuildJoinApplyCancelRequest1739_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_GuildJoinApplyCancelRequest1739_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_GuildJoinApplyCancelRequest1739_ptr);
        using CPlayerpc_GuildJoinApplyRequest1741_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_GuildJoinApplyRequest1741_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_GuildJoinApplyRequest1741_ptr);
        using CPlayerpc_GuildListRequest1743_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_GuildListRequest1743_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_GuildListRequest1743_ptr);
        using CPlayerpc_GuildManageRequest1745_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, unsigned int, unsigned int, unsigned int);
        using CPlayerpc_GuildManageRequest1745_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, unsigned int, unsigned int, unsigned int, CPlayerpc_GuildManageRequest1745_ptr);
        using CPlayerpc_GuildNextHonorListRequest1747_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_GuildNextHonorListRequest1747_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_GuildNextHonorListRequest1747_ptr);
        using CPlayerpc_GuildOfferSuggestRequest1749_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, unsigned int, unsigned int, unsigned int);
        using CPlayerpc_GuildOfferSuggestRequest1749_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, char*, unsigned int, unsigned int, unsigned int, CPlayerpc_GuildOfferSuggestRequest1749_ptr);
        using CPlayerpc_GuildPushMoneyRequest1751_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, unsigned int);
        using CPlayerpc_GuildPushMoneyRequest1751_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, unsigned int, CPlayerpc_GuildPushMoneyRequest1751_ptr);
        using CPlayerpc_GuildQueryInfoRequest1753_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_GuildQueryInfoRequest1753_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_GuildQueryInfoRequest1753_ptr);
        using CPlayerpc_GuildRoomEnterRequest1755_ptr = void (WINAPIV*)(struct CPlayer*, struct _guildroom_enter_request_clzo*);
        using CPlayerpc_GuildRoomEnterRequest1755_clbk = void (WINAPIV*)(struct CPlayer*, struct _guildroom_enter_request_clzo*, CPlayerpc_GuildRoomEnterRequest1755_ptr);
        using CPlayerpc_GuildRoomOutRequest1757_ptr = void (WINAPIV*)(struct CPlayer*, struct _guildroom_out_request_clzo*);
        using CPlayerpc_GuildRoomOutRequest1757_clbk = void (WINAPIV*)(struct CPlayer*, struct _guildroom_out_request_clzo*, CPlayerpc_GuildRoomOutRequest1757_ptr);
        using CPlayerpc_GuildRoomRentRequest1759_ptr = void (WINAPIV*)(struct CPlayer*, struct _guildroom_rent_request_clzo*);
        using CPlayerpc_GuildRoomRentRequest1759_clbk = void (WINAPIV*)(struct CPlayer*, struct _guildroom_rent_request_clzo*, CPlayerpc_GuildRoomRentRequest1759_ptr);
        using CPlayerpc_GuildRoomRestTimeRequest1761_ptr = void (WINAPIV*)(struct CPlayer*, struct _guildroom_resttime_request_clzo*);
        using CPlayerpc_GuildRoomRestTimeRequest1761_clbk = void (WINAPIV*)(struct CPlayer*, struct _guildroom_resttime_request_clzo*, CPlayerpc_GuildRoomRestTimeRequest1761_ptr);
        using CPlayerpc_GuildSelfLeaveRequest1763_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_GuildSelfLeaveRequest1763_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_GuildSelfLeaveRequest1763_ptr);
        using CPlayerpc_GuildSetHonorRequest1765_ptr = void (WINAPIV*)(struct CPlayer*, struct _guild_honor_set_request_clzo*);
        using CPlayerpc_GuildSetHonorRequest1765_clbk = void (WINAPIV*)(struct CPlayer*, struct _guild_honor_set_request_clzo*, CPlayerpc_GuildSetHonorRequest1765_ptr);
        using CPlayerpc_GuildVoteRequest1767_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char);
        using CPlayerpc_GuildVoteRequest1767_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char, CPlayerpc_GuildVoteRequest1767_ptr);
        using CPlayerpc_InitClass1769_ptr = char (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_InitClass1769_clbk = char (WINAPIV*)(struct CPlayer*, CPlayerpc_InitClass1769_ptr);
        using CPlayerpc_InitClassRequest1771_ptr = char (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_InitClassRequest1771_clbk = char (WINAPIV*)(struct CPlayer*, CPlayerpc_InitClassRequest1771_ptr);
        using CPlayerpc_LimitItemNumRequest1773_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_LimitItemNumRequest1773_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_LimitItemNumRequest1773_ptr);
        using CPlayerpc_LinkBoardRequest1775_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_LinkBoardRequest1775_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_LinkBoardRequest1775_ptr);
        using CPlayerpc_MacroUpdate1777_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_MacroUpdate1777_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_MacroUpdate1777_ptr);
        using CPlayerpc_MakeItem1779_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t, char, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_MakeItem1779_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t, char, struct _STORAGE_POS_INDIV*, CPlayerpc_MakeItem1779_ptr);
        using CPlayerpc_MakeTowerRequest1781_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, char, struct _make_tower_request_clzo::__material*, float*, uint16_t*);
        using CPlayerpc_MakeTowerRequest1781_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, char, struct _make_tower_request_clzo::__material*, float*, uint16_t*, CPlayerpc_MakeTowerRequest1781_ptr);
        using CPlayerpc_MakeTrapRequest1783_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, float*, uint16_t*);
        using CPlayerpc_MakeTrapRequest1783_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, float*, uint16_t*, CPlayerpc_MakeTrapRequest1783_ptr);
        using CPlayerpc_MineCancle1785_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_MineCancle1785_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_MineCancle1785_ptr);
        using CPlayerpc_MineComplete1787_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_MineComplete1787_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_MineComplete1787_ptr);
        using CPlayerpc_MineStart1789_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t);
        using CPlayerpc_MineStart1789_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, CPlayerpc_MineStart1789_ptr);
        using CPlayerpc_MoveModeChangeRequest1791_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_MoveModeChangeRequest1791_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_MoveModeChangeRequest1791_ptr);
        using CPlayerpc_MoveNext1793_ptr = void (WINAPIV*)(struct CPlayer*, char, float*, float*, char);
        using CPlayerpc_MoveNext1793_clbk = void (WINAPIV*)(struct CPlayer*, char, float*, float*, char, CPlayerpc_MoveNext1793_ptr);
        using CPlayerpc_MovePortal1795_ptr = void (WINAPIV*)(struct CPlayer*, int, uint16_t*);
        using CPlayerpc_MovePortal1795_clbk = void (WINAPIV*)(struct CPlayer*, int, uint16_t*, CPlayerpc_MovePortal1795_ptr);
        using CPlayerpc_MoveStop1797_ptr = void (WINAPIV*)(struct CPlayer*, float*);
        using CPlayerpc_MoveStop1797_clbk = void (WINAPIV*)(struct CPlayer*, float*, CPlayerpc_MoveStop1797_ptr);
        using CPlayerpc_MoveToOwnStoneMapRequest1799_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_MoveToOwnStoneMapRequest1799_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_MoveToOwnStoneMapRequest1799_ptr);
        using CPlayerpc_NPCLinkCheckItemRequest1801_ptr = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_NPCLinkCheckItemRequest1801_clbk = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, CPlayerpc_NPCLinkCheckItemRequest1801_ptr);
        using CPlayerpc_NPCLinkCheckItemRequest_Check1803_ptr = char (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_NPCLinkCheckItemRequest_Check1803_clbk = char (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, CPlayerpc_NPCLinkCheckItemRequest_Check1803_ptr);
        using CPlayerpc_NPCLinkCheckItemRequest_Use1805_ptr = char (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_NPCLinkCheckItemRequest_Use1805_clbk = char (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, CPlayerpc_NPCLinkCheckItemRequest_Use1805_ptr);
        using CPlayerpc_NewPosStart1807_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_NewPosStart1807_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_NewPosStart1807_ptr);
        using CPlayerpc_NotifyRaceBossCryMsg1809_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_NotifyRaceBossCryMsg1809_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_NotifyRaceBossCryMsg1809_ptr);
        using CPlayerpc_NuclearAfterEffect1811_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_NuclearAfterEffect1811_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_NuclearAfterEffect1811_ptr);
        using CPlayerpc_OffPart1813_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_OffPart1813_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, CPlayerpc_OffPart1813_ptr);
        using CPlayerpc_OreCutting1815_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char);
        using CPlayerpc_OreCutting1815_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, CPlayerpc_OreCutting1815_ptr);
        using CPlayerpc_OreIntoBag1817_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, char);
        using CPlayerpc_OreIntoBag1817_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, char, CPlayerpc_OreIntoBag1817_ptr);
        using CPlayerpc_PartyAlterLootShareReqeuest1819_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_PartyAlterLootShareReqeuest1819_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_PartyAlterLootShareReqeuest1819_ptr);
        using CPlayerpc_PartyDisJointReqeuest1821_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_PartyDisJointReqeuest1821_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_PartyDisJointReqeuest1821_ptr);
        using CPlayerpc_PartyJoinApplication1823_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerpc_PartyJoinApplication1823_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerpc_PartyJoinApplication1823_ptr);
        using CPlayerpc_PartyJoinApplicationAnswer1825_ptr = void (WINAPIV*)(struct CPlayer*, struct _CLID*);
        using CPlayerpc_PartyJoinApplicationAnswer1825_clbk = void (WINAPIV*)(struct CPlayer*, struct _CLID*, CPlayerpc_PartyJoinApplicationAnswer1825_ptr);
        using CPlayerpc_PartyJoinInvitation1827_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerpc_PartyJoinInvitation1827_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerpc_PartyJoinInvitation1827_ptr);
        using CPlayerpc_PartyJoinInvitationAnswer1829_ptr = void (WINAPIV*)(struct CPlayer*, struct _CLID*);
        using CPlayerpc_PartyJoinInvitationAnswer1829_clbk = void (WINAPIV*)(struct CPlayer*, struct _CLID*, CPlayerpc_PartyJoinInvitationAnswer1829_ptr);
        using CPlayerpc_PartyLeaveCompulsionReqeuest1831_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_PartyLeaveCompulsionReqeuest1831_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_PartyLeaveCompulsionReqeuest1831_ptr);
        using CPlayerpc_PartyLeaveSelfReqeuest1833_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_PartyLeaveSelfReqeuest1833_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_PartyLeaveSelfReqeuest1833_ptr);
        using CPlayerpc_PartyLockReqeuest1835_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerpc_PartyLockReqeuest1835_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerpc_PartyLockReqeuest1835_ptr);
        using CPlayerpc_PartyReqBlock1837_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerpc_PartyReqBlock1837_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerpc_PartyReqBlock1837_ptr);
        using CPlayerpc_PartySuccessionReqeuest1839_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_PartySuccessionReqeuest1839_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_PartySuccessionReqeuest1839_ptr);
        using CPlayerpc_PlayAttack_Force1841_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, uint16_t, uint16_t*, uint16_t);
        using CPlayerpc_PlayAttack_Force1841_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, uint16_t, uint16_t*, uint16_t, CPlayerpc_PlayAttack_Force1841_ptr);
        using CPlayerpc_PlayAttack_Gen1843_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, char, uint16_t, uint16_t, bool);
        using CPlayerpc_PlayAttack_Gen1843_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, char, uint16_t, uint16_t, bool, CPlayerpc_PlayAttack_Gen1843_ptr);
        using CPlayerpc_PlayAttack_SelfDestruction1845_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_PlayAttack_SelfDestruction1845_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_PlayAttack_SelfDestruction1845_ptr);
        using CPlayerpc_PlayAttack_Siege1847_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, uint16_t, uint16_t);
        using CPlayerpc_PlayAttack_Siege1847_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, uint16_t, uint16_t, CPlayerpc_PlayAttack_Siege1847_ptr);
        using CPlayerpc_PlayAttack_Skill1849_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, uint16_t, uint16_t, uint16_t*, uint16_t);
        using CPlayerpc_PlayAttack_Skill1849_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, float*, char, uint16_t, uint16_t, uint16_t*, uint16_t, CPlayerpc_PlayAttack_Skill1849_ptr);
        using CPlayerpc_PlayAttack_Test1851_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, char, int16_t*);
        using CPlayerpc_PlayAttack_Test1851_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, char, int16_t*, CPlayerpc_PlayAttack_Test1851_ptr);
        using CPlayerpc_PlayAttack_Unit1853_ptr = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, char);
        using CPlayerpc_PlayAttack_Unit1853_clbk = void (WINAPIV*)(struct CPlayer*, struct CCharacter*, char, CPlayerpc_PlayAttack_Unit1853_ptr);
        using CPlayerpc_PostContentRequest1855_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_PostContentRequest1855_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_PostContentRequest1855_ptr);
        using CPlayerpc_PostDeleteRequest1857_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_PostDeleteRequest1857_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_PostDeleteRequest1857_ptr);
        using CPlayerpc_PostItemGoldRequest1859_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_PostItemGoldRequest1859_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_PostItemGoldRequest1859_ptr);
        using CPlayerpc_PostListRequest1861_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_PostListRequest1861_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_PostListRequest1861_ptr);
        using CPlayerpc_PostReturnConfirmRequest1863_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int);
        using CPlayerpc_PostReturnConfirmRequest1863_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, CPlayerpc_PostReturnConfirmRequest1863_ptr);
        using CPlayerpc_PotionDivision1865_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, char);
        using CPlayerpc_PotionDivision1865_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, char, CPlayerpc_PotionDivision1865_ptr);
        using CPlayerpc_PotionSeparation1867_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char);
        using CPlayerpc_PotionSeparation1867_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, CPlayerpc_PotionSeparation1867_ptr);
        using CPlayerpc_PotionUseTrunkExtend1869_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_PotionUseTrunkExtend1869_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_PotionUseTrunkExtend1869_ptr);
        using CPlayerpc_ProposeVoteRequest1871_ptr = void (WINAPIV*)(struct CPlayer*, char, char*);
        using CPlayerpc_ProposeVoteRequest1871_clbk = void (WINAPIV*)(struct CPlayer*, char, char*, CPlayerpc_ProposeVoteRequest1871_ptr);
        using CPlayerpc_PvpCashRecorver1873_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char);
        using CPlayerpc_PvpCashRecorver1873_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char, CPlayerpc_PvpCashRecorver1873_ptr);
        using CPlayerpc_QuestGiveupRequest1875_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_QuestGiveupRequest1875_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_QuestGiveupRequest1875_ptr);
        using CPlayerpc_RadarCharInfo1877_ptr = bool (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_RadarCharInfo1877_clbk = bool (WINAPIV*)(struct CPlayer*, CPlayerpc_RadarCharInfo1877_ptr);
        using CPlayerpc_RealMovPos1879_ptr = void (WINAPIV*)(struct CPlayer*, float*);
        using CPlayerpc_RealMovPos1879_clbk = void (WINAPIV*)(struct CPlayer*, float*, CPlayerpc_RealMovPos1879_ptr);
        using CPlayerpc_RefreshGroupTargetPosition1881_ptr = void (WINAPIV*)(struct CPlayer*, char, struct CGameObject*);
        using CPlayerpc_RefreshGroupTargetPosition1881_clbk = void (WINAPIV*)(struct CPlayer*, char, struct CGameObject*, CPlayerpc_RefreshGroupTargetPosition1881_ptr);
        using CPlayerpc_RegistBind1883_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemStore*);
        using CPlayerpc_RegistBind1883_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, CPlayerpc_RegistBind1883_ptr);
        using CPlayerpc_ReleaseGroupTargetObjectRequest1885_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_ReleaseGroupTargetObjectRequest1885_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_ReleaseGroupTargetObjectRequest1885_ptr);
        using CPlayerpc_ReleaseSiegeModeRequest1887_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_ReleaseSiegeModeRequest1887_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_ReleaseSiegeModeRequest1887_ptr);
        using CPlayerpc_ReleaseTargetObjectRequest1889_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_ReleaseTargetObjectRequest1889_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_ReleaseTargetObjectRequest1889_ptr);
        using CPlayerpc_RenameItemNConditionCheck1891_ptr = char (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, struct _STORAGE_LIST::_db_con**);
        using CPlayerpc_RenameItemNConditionCheck1891_clbk = char (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, struct _STORAGE_LIST::_db_con**, CPlayerpc_RenameItemNConditionCheck1891_ptr);
        using CPlayerpc_RequestChangeTaxRate1893_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_RequestChangeTaxRate1893_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_RequestChangeTaxRate1893_ptr);
        using CPlayerpc_RequestDialogWithNPC1895_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemStore*);
        using CPlayerpc_RequestDialogWithNPC1895_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, CPlayerpc_RequestDialogWithNPC1895_ptr);
        using CPlayerpc_RequestPatriarchPunishment1897_ptr = void (WINAPIV*)(struct CPlayer*, char, char*, char*);
        using CPlayerpc_RequestPatriarchPunishment1897_clbk = void (WINAPIV*)(struct CPlayer*, char, char*, char*, CPlayerpc_RequestPatriarchPunishment1897_ptr);
        using CPlayerpc_RequestQuestFromNPC1899_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, unsigned int);
        using CPlayerpc_RequestQuestFromNPC1899_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, unsigned int, CPlayerpc_RequestQuestFromNPC1899_ptr);
        using CPlayerpc_RequestQuestListFromNPC1901_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemStore*);
        using CPlayerpc_RequestQuestListFromNPC1901_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, CPlayerpc_RequestQuestListFromNPC1901_ptr);
        using CPlayerpc_RequestTaxRate1903_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_RequestTaxRate1903_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_RequestTaxRate1903_ptr);
        using CPlayerpc_RequestUILockCertify1905_ptr = void (WINAPIV*)(struct CPlayer*, struct CUserDB*, char*);
        using CPlayerpc_RequestUILockCertify1905_clbk = void (WINAPIV*)(struct CPlayer*, struct CUserDB*, char*, CPlayerpc_RequestUILockCertify1905_ptr);
        using CPlayerpc_RequestUILockFindPW1907_ptr = void (WINAPIV*)(struct CPlayer*, struct CUserDB*, char*);
        using CPlayerpc_RequestUILockFindPW1907_clbk = void (WINAPIV*)(struct CPlayer*, struct CUserDB*, char*, CPlayerpc_RequestUILockFindPW1907_ptr);
        using CPlayerpc_RequestUILockInit1909_ptr = void (WINAPIV*)(struct CPlayer*, struct CUserDB*, char*, char*, char, char*);
        using CPlayerpc_RequestUILockInit1909_clbk = void (WINAPIV*)(struct CPlayer*, struct CUserDB*, char*, char*, char, char*, CPlayerpc_RequestUILockInit1909_ptr);
        using CPlayerpc_RequestUILockUpdate1911_ptr = void (WINAPIV*)(struct CPlayer*, char*, char*, char*, char, char*);
        using CPlayerpc_RequestUILockUpdate1911_clbk = void (WINAPIV*)(struct CPlayer*, char*, char*, char*, char, char*, CPlayerpc_RequestUILockUpdate1911_ptr);
        using CPlayerpc_RequestWatchingWithNPC1913_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemStore*);
        using CPlayerpc_RequestWatchingWithNPC1913_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, CPlayerpc_RequestWatchingWithNPC1913_ptr);
        using CPlayerpc_ResDivision1915_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, char);
        using CPlayerpc_ResDivision1915_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, char, CPlayerpc_ResDivision1915_ptr);
        using CPlayerpc_ResSeparation1917_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char);
        using CPlayerpc_ResSeparation1917_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, CPlayerpc_ResSeparation1917_ptr);
        using CPlayerpc_Resurrect1919_ptr = bool (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerpc_Resurrect1919_clbk = bool (WINAPIV*)(struct CPlayer*, bool, CPlayerpc_Resurrect1919_ptr);
        using CPlayerpc_Revival1921_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerpc_Revival1921_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerpc_Revival1921_ptr);
        using CPlayerpc_SelectClassRequest1923_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, char);
        using CPlayerpc_SelectClassRequest1923_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, char, CPlayerpc_SelectClassRequest1923_ptr);
        using CPlayerpc_SelectQuestAfterHappenEvent1925_ptr = void (WINAPIV*)(struct CPlayer*, char);
        using CPlayerpc_SelectQuestAfterHappenEvent1925_clbk = void (WINAPIV*)(struct CPlayer*, char, CPlayerpc_SelectQuestAfterHappenEvent1925_ptr);
        using CPlayerpc_SelectQuestReward1927_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char);
        using CPlayerpc_SelectQuestReward1927_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, CPlayerpc_SelectQuestReward1927_ptr);
        using CPlayerpc_SellItemStore1929_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, char, struct _sell_store_request_clzo::_list*, int);
        using CPlayerpc_SellItemStore1929_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemStore*, char, struct _sell_store_request_clzo::_list*, int, CPlayerpc_SellItemStore1929_ptr);
        using CPlayerpc_SetGroupMapPointRequest1931_ptr = void (WINAPIV*)(struct CPlayer*, char, float*);
        using CPlayerpc_SetGroupMapPointRequest1931_clbk = void (WINAPIV*)(struct CPlayer*, char, float*, CPlayerpc_SetGroupMapPointRequest1931_ptr);
        using CPlayerpc_SetGroupTargetObjectRequest1933_ptr = void (WINAPIV*)(struct CPlayer*, struct CGameObject*, unsigned int, char);
        using CPlayerpc_SetGroupTargetObjectRequest1933_clbk = void (WINAPIV*)(struct CPlayer*, struct CGameObject*, unsigned int, char, CPlayerpc_SetGroupTargetObjectRequest1933_ptr);
        using CPlayerpc_SetInGuildBattle1935_ptr = void (WINAPIV*)(struct CPlayer*, bool, char);
        using CPlayerpc_SetInGuildBattle1935_clbk = void (WINAPIV*)(struct CPlayer*, bool, char, CPlayerpc_SetInGuildBattle1935_ptr);
        using CPlayerpc_SetItemCheckRequest1937_ptr = bool (WINAPIV*)(struct CPlayer*, unsigned int, char, char, bool);
        using CPlayerpc_SetItemCheckRequest1937_clbk = bool (WINAPIV*)(struct CPlayer*, unsigned int, char, char, bool, CPlayerpc_SetItemCheckRequest1937_ptr);
        using CPlayerpc_SetRaceBossCryMsg1939_ptr = void (WINAPIV*)(struct CPlayer*, char, char*);
        using CPlayerpc_SetRaceBossCryMsg1939_clbk = void (WINAPIV*)(struct CPlayer*, char, char*, CPlayerpc_SetRaceBossCryMsg1939_ptr);
        using CPlayerpc_SetTargetObjectRequest1941_ptr = void (WINAPIV*)(struct CPlayer*, struct CGameObject*, unsigned int, bool);
        using CPlayerpc_SetTargetObjectRequest1941_clbk = void (WINAPIV*)(struct CPlayer*, struct CGameObject*, unsigned int, bool, CPlayerpc_SetTargetObjectRequest1941_ptr);
        using CPlayerpc_SkillRequest1943_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, uint16_t*);
        using CPlayerpc_SkillRequest1943_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _CHRID*, uint16_t*, CPlayerpc_SkillRequest1943_ptr);
        using CPlayerpc_Stop1945_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_Stop1945_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_Stop1945_ptr);
        using CPlayerpc_TakeGroundingItem1947_ptr = void (WINAPIV*)(struct CPlayer*, struct CItemBox*, uint16_t);
        using CPlayerpc_TakeGroundingItem1947_clbk = void (WINAPIV*)(struct CPlayer*, struct CItemBox*, uint16_t, CPlayerpc_TakeGroundingItem1947_ptr);
        using CPlayerpc_TalikCrystalExchange1949_ptr = void (WINAPIV*)(struct CPlayer*, char, struct _talik_crystal_exchange_clzo::_list*);
        using CPlayerpc_TalikCrystalExchange1949_clbk = void (WINAPIV*)(struct CPlayer*, char, struct _talik_crystal_exchange_clzo::_list*, CPlayerpc_TalikCrystalExchange1949_ptr);
        using CPlayerpc_ThrowSkillRequest1951_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, struct _CHRID*, uint16_t*);
        using CPlayerpc_ThrowSkillRequest1951_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, struct _CHRID*, uint16_t*, CPlayerpc_ThrowSkillRequest1951_ptr);
        using CPlayerpc_ThrowStorageItem1953_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_ThrowStorageItem1953_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, CPlayerpc_ThrowStorageItem1953_ptr);
        using CPlayerpc_ThrowUnitRequest1955_ptr = void (WINAPIV*)(struct CPlayer*, struct _CHRID*, uint16_t*);
        using CPlayerpc_ThrowUnitRequest1955_clbk = void (WINAPIV*)(struct CPlayer*, struct _CHRID*, uint16_t*, CPlayerpc_ThrowUnitRequest1955_ptr);
        using CPlayerpc_TradeBlock1957_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerpc_TradeBlock1957_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerpc_TradeBlock1957_ptr);
        using CPlayerpc_TransShipRenewTicketRequest1959_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerpc_TransShipRenewTicketRequest1959_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerpc_TransShipRenewTicketRequest1959_ptr);
        using CPlayerpc_TransformSiegeModeRequest1961_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerpc_TransformSiegeModeRequest1961_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerpc_TransformSiegeModeRequest1961_ptr);
        using CPlayerpc_TrunkAlterItemSlotRequest1963_ptr = void (WINAPIV*)(struct CPlayer*, unsigned int, char, char);
        using CPlayerpc_TrunkAlterItemSlotRequest1963_clbk = void (WINAPIV*)(struct CPlayer*, unsigned int, char, char, CPlayerpc_TrunkAlterItemSlotRequest1963_ptr);
        using CPlayerpc_TrunkChangePasswdRequest1965_ptr = void (WINAPIV*)(struct CPlayer*, char*, char*, char, char*);
        using CPlayerpc_TrunkChangePasswdRequest1965_clbk = void (WINAPIV*)(struct CPlayer*, char*, char*, char, char*, CPlayerpc_TrunkChangePasswdRequest1965_ptr);
        using CPlayerpc_TrunkCreateCostIsFreeRequest1967_ptr = char (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_TrunkCreateCostIsFreeRequest1967_clbk = char (WINAPIV*)(struct CPlayer*, CPlayerpc_TrunkCreateCostIsFreeRequest1967_ptr);
        using CPlayerpc_TrunkDownloadRequest1969_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_TrunkDownloadRequest1969_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_TrunkDownloadRequest1969_ptr);
        using CPlayerpc_TrunkEstRequest1971_ptr = void (WINAPIV*)(struct CPlayer*, char*, char, char*);
        using CPlayerpc_TrunkEstRequest1971_clbk = void (WINAPIV*)(struct CPlayer*, char*, char, char*, CPlayerpc_TrunkEstRequest1971_ptr);
        using CPlayerpc_TrunkExtendRequest1973_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_TrunkExtendRequest1973_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_TrunkExtendRequest1973_ptr);
        using CPlayerpc_TrunkHintAnswerRequest1975_ptr = void (WINAPIV*)(struct CPlayer*, char*);
        using CPlayerpc_TrunkHintAnswerRequest1975_clbk = void (WINAPIV*)(struct CPlayer*, char*, CPlayerpc_TrunkHintAnswerRequest1975_ptr);
        using CPlayerpc_TrunkIoMergeRequest1977_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, uint16_t, uint16_t);
        using CPlayerpc_TrunkIoMergeRequest1977_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, uint16_t, uint16_t, CPlayerpc_TrunkIoMergeRequest1977_ptr);
        using CPlayerpc_TrunkIoMoneyRequest1979_ptr = void (WINAPIV*)(struct CPlayer*, char, unsigned int, unsigned int);
        using CPlayerpc_TrunkIoMoneyRequest1979_clbk = void (WINAPIV*)(struct CPlayer*, char, unsigned int, unsigned int, CPlayerpc_TrunkIoMoneyRequest1979_ptr);
        using CPlayerpc_TrunkIoMoveRequest1981_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, char);
        using CPlayerpc_TrunkIoMoveRequest1981_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, char, CPlayerpc_TrunkIoMoveRequest1981_ptr);
        using CPlayerpc_TrunkIoSwapRequest1983_ptr = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, uint16_t);
        using CPlayerpc_TrunkIoSwapRequest1983_clbk = void (WINAPIV*)(struct CPlayer*, char, char, uint16_t, uint16_t, CPlayerpc_TrunkIoSwapRequest1983_ptr);
        using CPlayerpc_TrunkPotionDivision1985_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, uint16_t, char);
        using CPlayerpc_TrunkPotionDivision1985_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, uint16_t, char, CPlayerpc_TrunkPotionDivision1985_ptr);
        using CPlayerpc_TrunkPwHintIndexRequest1987_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_TrunkPwHintIndexRequest1987_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_TrunkPwHintIndexRequest1987_ptr);
        using CPlayerpc_TrunkResDivision1989_ptr = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, uint16_t, char);
        using CPlayerpc_TrunkResDivision1989_clbk = void (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t, uint16_t, char, CPlayerpc_TrunkResDivision1989_ptr);
        using CPlayerpc_UnitBulletFillRequest1991_ptr = void (WINAPIV*)(struct CPlayer*, char, uint16_t*, int);
        using CPlayerpc_UnitBulletFillRequest1991_clbk = void (WINAPIV*)(struct CPlayer*, char, uint16_t*, int, CPlayerpc_UnitBulletFillRequest1991_ptr);
        using CPlayerpc_UnitBulletReplaceRequest1993_ptr = void (WINAPIV*)(struct CPlayer*, char, char, char);
        using CPlayerpc_UnitBulletReplaceRequest1993_clbk = void (WINAPIV*)(struct CPlayer*, char, char, char, CPlayerpc_UnitBulletReplaceRequest1993_ptr);
        using CPlayerpc_UnitDeliveryRequest1995_ptr = void (WINAPIV*)(struct CPlayer*, char, struct CItemStore*, bool, float*, int);
        using CPlayerpc_UnitDeliveryRequest1995_clbk = void (WINAPIV*)(struct CPlayer*, char, struct CItemStore*, bool, float*, int, CPlayerpc_UnitDeliveryRequest1995_ptr);
        using CPlayerpc_UnitFrameBuyRequest1997_ptr = void (WINAPIV*)(struct CPlayer*, char, int);
        using CPlayerpc_UnitFrameBuyRequest1997_clbk = void (WINAPIV*)(struct CPlayer*, char, int, CPlayerpc_UnitFrameBuyRequest1997_ptr);
        using CPlayerpc_UnitFrameRepairRequest1999_ptr = void (WINAPIV*)(struct CPlayer*, char, int);
        using CPlayerpc_UnitFrameRepairRequest1999_clbk = void (WINAPIV*)(struct CPlayer*, char, int, CPlayerpc_UnitFrameRepairRequest1999_ptr);
        using CPlayerpc_UnitLeaveRequest2001_ptr = void (WINAPIV*)(struct CPlayer*, float*);
        using CPlayerpc_UnitLeaveRequest2001_clbk = void (WINAPIV*)(struct CPlayer*, float*, CPlayerpc_UnitLeaveRequest2001_ptr);
        using CPlayerpc_UnitPackFillRequest2003_ptr = void (WINAPIV*)(struct CPlayer*, char, char, struct _unit_pack_fill_request_clzo::__list*, int);
        using CPlayerpc_UnitPackFillRequest2003_clbk = void (WINAPIV*)(struct CPlayer*, char, char, struct _unit_pack_fill_request_clzo::__list*, int, CPlayerpc_UnitPackFillRequest2003_ptr);
        using CPlayerpc_UnitPartTuningRequest2005_ptr = void (WINAPIV*)(struct CPlayer*, char, char, struct _tuning_data*, int);
        using CPlayerpc_UnitPartTuningRequest2005_clbk = void (WINAPIV*)(struct CPlayer*, char, char, struct _tuning_data*, int, CPlayerpc_UnitPartTuningRequest2005_ptr);
        using CPlayerpc_UnitReturnRequest2007_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_UnitReturnRequest2007_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_UnitReturnRequest2007_ptr);
        using CPlayerpc_UnitSellRequest2009_ptr = void (WINAPIV*)(struct CPlayer*, char, int);
        using CPlayerpc_UnitSellRequest2009_clbk = void (WINAPIV*)(struct CPlayer*, char, int, CPlayerpc_UnitSellRequest2009_ptr);
        using CPlayerpc_UnitTakeRequest2011_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_UnitTakeRequest2011_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_UnitTakeRequest2011_ptr);
        using CPlayerpc_UpdateDataForPostSend2013_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerpc_UpdateDataForPostSend2013_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerpc_UpdateDataForPostSend2013_ptr);
        using CPlayerpc_UpdateDataForTrade2015_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*);
        using CPlayerpc_UpdateDataForTrade2015_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, CPlayerpc_UpdateDataForTrade2015_ptr);
        using CPlayerpc_UpgradeItem2017_ptr = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, struct _STORAGE_POS_INDIV*, struct _STORAGE_POS_INDIV*, char, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_UpgradeItem2017_clbk = void (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, struct _STORAGE_POS_INDIV*, struct _STORAGE_POS_INDIV*, char, struct _STORAGE_POS_INDIV*, CPlayerpc_UpgradeItem2017_ptr);
        using CPlayerpc_UseFireCracker2019_ptr = int (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerpc_UseFireCracker2019_clbk = int (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerpc_UseFireCracker2019_ptr);
        using CPlayerpc_UsePotionItem2021_ptr = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, struct _STORAGE_POS_INDIV*);
        using CPlayerpc_UsePotionItem2021_clbk = void (WINAPIV*)(struct CPlayer*, struct CPlayer*, struct _STORAGE_POS_INDIV*, CPlayerpc_UsePotionItem2021_ptr);
        using CPlayerpc_UseRadarItem2023_ptr = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t*);
        using CPlayerpc_UseRadarItem2023_clbk = bool (WINAPIV*)(struct CPlayer*, struct _STORAGE_POS_INDIV*, uint16_t*, CPlayerpc_UseRadarItem2023_ptr);
        using CPlayerpc_UseRecoverLossExpItem2025_ptr = char (WINAPIV*)(struct CPlayer*, uint16_t);
        using CPlayerpc_UseRecoverLossExpItem2025_clbk = char (WINAPIV*)(struct CPlayer*, uint16_t, CPlayerpc_UseRecoverLossExpItem2025_ptr);
        using CPlayerpc_UserSoccerBall2027_ptr = char (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t*);
        using CPlayerpc_UserSoccerBall2027_clbk = char (WINAPIV*)(struct CPlayer*, uint16_t, uint16_t*, CPlayerpc_UserSoccerBall2027_ptr);
        using CPlayerpc_WPActiveAttack_Force2029_ptr = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int*, struct _force_fld*);
        using CPlayerpc_WPActiveAttack_Force2029_clbk = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int*, struct _force_fld*, CPlayerpc_WPActiveAttack_Force2029_ptr);
        using CPlayerpc_WPActiveAttack_Skill2031_ptr = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int*, int*, struct _skill_fld*, char, uint16_t);
        using CPlayerpc_WPActiveAttack_Skill2031_clbk = bool (WINAPIV*)(struct CPlayer*, struct _be_damaged_char*, int*, int*, struct _skill_fld*, char, uint16_t, CPlayerpc_WPActiveAttack_Skill2031_ptr);
        using CPlayerpc_WhisperBlock2033_ptr = void (WINAPIV*)(struct CPlayer*, bool);
        using CPlayerpc_WhisperBlock2033_clbk = void (WINAPIV*)(struct CPlayer*, bool, CPlayerpc_WhisperBlock2033_ptr);
        using CPlayerskill_process2035_ptr = char (WINAPIV*)(struct CPlayer*, int, int, struct _CHRID*, uint16_t*, int*);
        using CPlayerskill_process2035_clbk = char (WINAPIV*)(struct CPlayer*, int, int, struct _CHRID*, uint16_t*, int*, CPlayerskill_process2035_ptr);
        using CPlayerskill_process_for_aura2037_ptr = void (WINAPIV*)(struct CPlayer*, int);
        using CPlayerskill_process_for_aura2037_clbk = void (WINAPIV*)(struct CPlayer*, int, CPlayerskill_process_for_aura2037_ptr);
        using CPlayerskill_process_for_item2039_ptr = char (WINAPIV*)(struct CPlayer*, int, struct _CHRID*, int*);
        using CPlayerskill_process_for_item2039_clbk = char (WINAPIV*)(struct CPlayer*, int, struct _CHRID*, int*, CPlayerskill_process_for_item2039_ptr);
        
        using CPlayerdtor_CPlayer2041_ptr = void (WINAPIV*)(struct CPlayer*);
        using CPlayerdtor_CPlayer2041_clbk = void (WINAPIV*)(struct CPlayer*, CPlayerdtor_CPlayer2041_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

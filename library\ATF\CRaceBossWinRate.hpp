// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_raceboss_accumulation_winrate.hpp>


START_ATF_NAMESPACE
    struct CRaceBossWinRate
    {
        char m_byTotalBattleCnt;
        char m_byWinCnt[3];
        unsigned int m_dwAccBattleCnt[3];
        unsigned int m_dwAccWinCnt[3];
    public:
        CRaceBossWinRate();
        void ctor_CRaceBossWinRate();
        void CompleteBossAccmulationWinRate(struct _qry_case_raceboss_accumulation_winrate* pData);
        void Destroy();
        static struct CRaceBossWinRate* Instance();
        char LoadBossAccmulationWinRate(struct _qry_case_raceboss_accumulation_winrate* pData);
        bool LoadBossCurrentWinRate();
        bool LoadDB();
        bool LoadINI();
        void Notify(char byRace);
        void Notify(char byRace, uint16_t wIndex);
        void Notify();
        bool SaveINI();
        void UpdateRaceBossWinRate(char byRace);
        void UpdateRaceBossWinRate();
        void UpdateTotalCnt();
        void UpdateWinCnt(char byRace);
        ~CRaceBossWinRate();
        void dtor_CRaceBossWinRate();
    };
END_ATF_NAMESPACE

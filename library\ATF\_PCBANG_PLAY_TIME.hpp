// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _PCBANG_PLAY_TIME
    {
        unsigned int dwAccSerial;
        unsigned int dwLastConnTime;
        unsigned int dwContPlayTime;
        bool bForcedClose;
        char byReceiveCoupon;
        char byEnsureTime;
    public:
        void Init();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_PCBANG_PLAY_TIME, 15>(), "_PCBANG_PLAY_TIME");
END_ATF_NAMESPACE

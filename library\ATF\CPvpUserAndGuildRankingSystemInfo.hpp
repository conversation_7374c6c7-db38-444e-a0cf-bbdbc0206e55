// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPvpUserAndGuildRankingSystem.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPvpUserAndGuildRankingSystemApplyUpdatedBossInfo2_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*);
        using CPvpUserAndGuildRankingSystemApplyUpdatedBossInfo2_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, CPvpUserAndGuildRankingSystemApplyUpdatedBossInfo2_ptr);
        
        using CPvpUserAndGuildRankingSystemctor_CPvpUserAndGuildRankingSystem4_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*);
        using CPvpUserAndGuildRankingSystemctor_CPvpUserAndGuildRankingSystem4_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, CPvpUserAndGuildRankingSystemctor_CPvpUserAndGuildRankingSystem4_ptr);
        using CPvpUserAndGuildRankingSystemCompleteGuildRankStep16_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteGuildRankStep16_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteGuildRankStep16_ptr);
        using CPvpUserAndGuildRankingSystemCompleteGuildRankStep28_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteGuildRankStep28_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteGuildRankStep28_ptr);
        using CPvpUserAndGuildRankingSystemCompleteGuildRankStep310_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteGuildRankStep310_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteGuildRankStep310_ptr);
        using CPvpUserAndGuildRankingSystemCompleteGuildRankStep412_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteGuildRankStep412_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteGuildRankStep412_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep114_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep114_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep114_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep1016_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep1016_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep1016_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep1118_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep1118_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep1118_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep220_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep220_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep220_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep322_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep322_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep322_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep424_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep424_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep424_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep526_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep526_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep526_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep628_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep628_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep628_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep730_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep730_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep730_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep832_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep832_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep832_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep934_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRaceRankStep934_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRaceRankStep934_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep136_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep136_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRankInGuildStep136_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep238_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep238_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRankInGuildStep238_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep340_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep340_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRankInGuildStep340_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep442_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep442_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRankInGuildStep442_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep544_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep544_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRankInGuildStep544_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep646_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRankInGuildStep646_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRankInGuildStep646_ptr);
        using CPvpUserAndGuildRankingSystemCompleteRankUpdateAndSelectGarde48_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*);
        using CPvpUserAndGuildRankingSystemCompleteRankUpdateAndSelectGarde48_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char*, CPvpUserAndGuildRankingSystemCompleteRankUpdateAndSelectGarde48_ptr);
        using CPvpUserAndGuildRankingSystemDestroy50_ptr = void (WINAPIV*)();
        using CPvpUserAndGuildRankingSystemDestroy50_clbk = void (WINAPIV*)(CPvpUserAndGuildRankingSystemDestroy50_ptr);
        using CPvpUserAndGuildRankingSystemFindRank52_ptr = unsigned int (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, unsigned int);
        using CPvpUserAndGuildRankingSystemFindRank52_clbk = unsigned int (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, unsigned int, CPvpUserAndGuildRankingSystemFindRank52_ptr);
        using CPvpUserAndGuildRankingSystemGetBossType54_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, unsigned int);
        using CPvpUserAndGuildRankingSystemGetBossType54_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, unsigned int, CPvpUserAndGuildRankingSystemGetBossType54_ptr);
        using CPvpUserAndGuildRankingSystemGetCurrentPvpRankData56_ptr = struct _PVP_RANK_DATA* (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char);
        using CPvpUserAndGuildRankingSystemGetCurrentPvpRankData56_clbk = struct _PVP_RANK_DATA* (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char, CPvpUserAndGuildRankingSystemGetCurrentPvpRankData56_ptr);
        using CPvpUserAndGuildRankingSystemGetCurrentRaceBossSerial58_ptr = unsigned int (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char);
        using CPvpUserAndGuildRankingSystemGetCurrentRaceBossSerial58_clbk = unsigned int (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char, CPvpUserAndGuildRankingSystemGetCurrentRaceBossSerial58_ptr);
        using CPvpUserAndGuildRankingSystemInit60_ptr = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*);
        using CPvpUserAndGuildRankingSystemInit60_clbk = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, CPvpUserAndGuildRankingSystemInit60_ptr);
        using CPvpUserAndGuildRankingSystemInitLogger62_ptr = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*);
        using CPvpUserAndGuildRankingSystemInitLogger62_clbk = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, CPvpUserAndGuildRankingSystemInitLogger62_ptr);
        using CPvpUserAndGuildRankingSystemInstance64_ptr = struct CPvpUserAndGuildRankingSystem* (WINAPIV*)();
        using CPvpUserAndGuildRankingSystemInstance64_clbk = struct CPvpUserAndGuildRankingSystem* (WINAPIV*)(CPvpUserAndGuildRankingSystemInstance64_ptr);
        using CPvpUserAndGuildRankingSystemIsCurrentRaceBossGroup66_ptr = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, unsigned int);
        using CPvpUserAndGuildRankingSystemIsCurrentRaceBossGroup66_clbk = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, unsigned int, CPvpUserAndGuildRankingSystemIsCurrentRaceBossGroup66_ptr);
        using CPvpUserAndGuildRankingSystemIsRaceViceBoss68_ptr = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, unsigned int);
        using CPvpUserAndGuildRankingSystemIsRaceViceBoss68_clbk = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, unsigned int, CPvpUserAndGuildRankingSystemIsRaceViceBoss68_ptr);
        using CPvpUserAndGuildRankingSystemLoad70_ptr = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*);
        using CPvpUserAndGuildRankingSystemLoad70_clbk = bool (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, CPvpUserAndGuildRankingSystemLoad70_ptr);
        using CPvpUserAndGuildRankingSystemLog72_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemLog72_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemLog72_ptr);
        using CPvpUserAndGuildRankingSystemLoop74_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*);
        using CPvpUserAndGuildRankingSystemLoop74_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, CPvpUserAndGuildRankingSystemLoop74_ptr);
        using CPvpUserAndGuildRankingSystemPvpRankDataPacking76_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*);
        using CPvpUserAndGuildRankingSystemPvpRankDataPacking76_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, CPvpUserAndGuildRankingSystemPvpRankDataPacking76_ptr);
        using CPvpUserAndGuildRankingSystemPvpRankListRequest78_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, uint16_t, char, char, char);
        using CPvpUserAndGuildRankingSystemPvpRankListRequest78_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, uint16_t, char, char, char, CPvpUserAndGuildRankingSystemPvpRankListRequest78_ptr);
        using CPvpUserAndGuildRankingSystemSetCurrentRaceBossSerial80_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char, unsigned int);
        using CPvpUserAndGuildRankingSystemSetCurrentRaceBossSerial80_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char, unsigned int, CPvpUserAndGuildRankingSystemSetCurrentRaceBossSerial80_ptr);
        using CPvpUserAndGuildRankingSystemSetUpdateRaceBossSerial82_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char, unsigned int);
        using CPvpUserAndGuildRankingSystemSetUpdateRaceBossSerial82_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char, char, unsigned int, CPvpUserAndGuildRankingSystemSetUpdateRaceBossSerial82_ptr);
        using CPvpUserAndGuildRankingSystemUpdateAndSelectGuildGrade84_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateAndSelectGuildGrade84_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateAndSelectGuildGrade84_ptr);
        using CPvpUserAndGuildRankingSystemUpdateGuildRankStep186_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateGuildRankStep186_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateGuildRankStep186_ptr);
        using CPvpUserAndGuildRankingSystemUpdateGuildRankStep288_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateGuildRankStep288_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateGuildRankStep288_ptr);
        using CPvpUserAndGuildRankingSystemUpdateGuildRankStep390_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateGuildRankStep390_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateGuildRankStep390_ptr);
        using CPvpUserAndGuildRankingSystemUpdateGuildRankStep492_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateGuildRankStep492_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateGuildRankStep492_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep194_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep194_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep194_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep1096_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep1096_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep1096_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep1198_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep1198_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep1198_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep2100_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep2100_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep2100_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep3102_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep3102_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep3102_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep4104_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep4104_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep4104_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep5106_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep5106_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep5106_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep6108_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep6108_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep6108_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep7110_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep7110_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep7110_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep8112_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep8112_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep8112_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep9114_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRaceRankStep9114_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRaceRankStep9114_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep1116_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep1116_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRankinGuildStep1116_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep2118_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep2118_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRankinGuildStep2118_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep3120_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep3120_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRankinGuildStep3120_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep4122_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep4122_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRankinGuildStep4122_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep5124_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep5124_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRankinGuildStep5124_ptr);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep6126_ptr = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*);
        using CPvpUserAndGuildRankingSystemUpdateRankinGuildStep6126_clbk = char (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, char*, CPvpUserAndGuildRankingSystemUpdateRankinGuildStep6126_ptr);
        
        using CPvpUserAndGuildRankingSystemdtor_CPvpUserAndGuildRankingSystem130_ptr = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*);
        using CPvpUserAndGuildRankingSystemdtor_CPvpUserAndGuildRankingSystem130_clbk = void (WINAPIV*)(struct CPvpUserAndGuildRankingSystem*, CPvpUserAndGuildRankingSystemdtor_CPvpUserAndGuildRankingSystem130_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

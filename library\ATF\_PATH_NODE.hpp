// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _PATH_NODE
    {
        float TotalLeng;
        float Path[17][3];
        int WhatDirection;
        unsigned __int16 FrontLinetId;
        unsigned __int16 BackLineId;
        int PathCnt;
        int IsFind;
    };
END_ATF_NAMESPACE

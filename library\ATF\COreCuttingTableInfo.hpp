// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <COreCuttingTable.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using COreCuttingTablector_COreCuttingTable2_ptr = void (WINAPIV*)(struct COreCuttingTable*);
        using COreCuttingTablector_COreCuttingTable2_clbk = void (WINAPIV*)(struct COreCuttingTable*, COreCuttingTablector_COreCuttingTable2_ptr);
        using COreCuttingTableGetOreIndexFromRate4_ptr = unsigned int (WINAPIV*)(struct COreCuttingTable*, unsigned int, unsigned int);
        using COreCuttingTableGetOreIndexFromRate4_clbk = unsigned int (WINAPIV*)(struct COreCuttingTable*, unsigned int, unsigned int, COreCuttingTableGetOreIndexFromRate4_ptr);
        using COreCuttingTableIndexing6_ptr = bool (WINAPIV*)(struct COreCuttingTable*, struct CRecordData*, struct CRecordData*, char*);
        using COreCuttingTableIndexing6_clbk = bool (WINAPIV*)(struct COreCuttingTable*, struct CRecordData*, struct CRecordData*, char*, COreCuttingTableIndexing6_ptr);
        using COreCuttingTableReadRecord8_ptr = bool (WINAPIV*)(struct COreCuttingTable*, char*, struct CRecordData*, struct CRecordData*, char*);
        using COreCuttingTableReadRecord8_clbk = bool (WINAPIV*)(struct COreCuttingTable*, char*, struct CRecordData*, struct CRecordData*, char*, COreCuttingTableReadRecord8_ptr);
        
        using COreCuttingTabledtor_COreCuttingTable13_ptr = void (WINAPIV*)(struct COreCuttingTable*);
        using COreCuttingTabledtor_COreCuttingTable13_clbk = void (WINAPIV*)(struct COreCuttingTable*, COreCuttingTabledtor_COreCuttingTable13_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

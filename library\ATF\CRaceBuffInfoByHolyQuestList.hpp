// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CRaceBuffInfoByHolyQuestList
    {
        std::vector<struct CRaceBuffInfoByHolyQuestfGroup *> m_vecInfo;
    public:
        bool Apply(unsigned int uiContinueCnt, int iResultType, struct CPlayer* pkDest);
        CRaceBuffInfoByHolyQuestList();
        void ctor_CRaceBuffInfoByHolyQuestList();
        bool CreateComplete(unsigned int uiContinueCnt, int iResultType, struct CPlayer* pkDest);
        unsigned int GetMaxThCnt();
        bool Init();
        bool Release(unsigned int uiContinueCnt, int iResultType, struct CPlayer* pkDest);
        ~CRaceBuffInfoByHolyQuestList();
        void dtor_CRaceBuffInfoByHolyQuestList();
    };
END_ATF_NAMESPACE

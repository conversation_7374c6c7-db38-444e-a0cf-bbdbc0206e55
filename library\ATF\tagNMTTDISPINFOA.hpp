// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <tagNMHDR.hpp>


START_ATF_NAMESPACE
    struct tagNMTTDISPINFOA
    {
        tagNMHDR hdr;
        char *lpszText;
        char szText[80];
        HINSTANCE__ *hinst;
        unsigned int uFlags;
        __int64 lParam;
    };
END_ATF_NAMESPACE

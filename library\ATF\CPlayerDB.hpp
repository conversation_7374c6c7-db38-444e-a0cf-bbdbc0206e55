// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPostStorage.hpp>
#include <CPostReturnStorage.hpp>
#include <_AVATOR_DATA.hpp>
#include <_ITEMCOMBINE_DB_BASE.hpp>
#include <_QUEST_DB_BASE.hpp>
#include <_SFCONT_DB_BASE.hpp>
#include <_STORAGE_LIST.hpp>
#include <_UNIT_DB_BASE.hpp>
#include <_class_fld.hpp>
#include <_guild_member_info.hpp>
#include <_personal_amine_inven_db_load.hpp>
#include <_quick_link.hpp>
#include <_character_db_load.hpp>
#include <_bag_db_load.hpp>
#include <_equip_db_load.hpp>
#include <_embellish_db_load.hpp>
#include <_force_db_load.hpp>
#include <_animus_db_load.hpp>
#include <_trunk_db_load.hpp>
#include <_Exttrunk_db_load.hpp>


START_ATF_NAMESPACE
    struct CPlayerDB
    {
        char m_byPvPGrade;
        _character_db_load m_dbChar;
        _bag_db_load m_dbInven;
        _equip_db_load m_dbEquip;
        _embellish_db_load m_dbEmbellish;
        _force_db_load m_dbForce;
        _animus_db_load m_dbAnimus;
        _trunk_db_load m_dbTrunk;
        _Exttrunk_db_load m_dbExtTrunk;
        _STORAGE_LIST *m_pStoragePtr[8];
        _UNIT_DB_BASE m_UnitDB;
        _QUEST_DB_BASE m_QuestDB;
        _SFCONT_DB_BASE m_SFContDB;
        _ITEMCOMBINE_DB_BASE m_ItemCombineDB;
        CPostStorage m_PostStorage;
        CPostReturnStorage m_ReturnPostStorage;
        bool m_bPersonalAmineInven;
        struct AutominePersonal *m_pAPM;
        _personal_amine_inven_db_load m_dbPersonalAmineInven;
        unsigned __int16 *m_wCuttingResBuffer;
        char m_byNameLen;
        unsigned int m_dwAlterMastery[80];
        _class_fld *m_pClassData;
        _class_fld *m_pClassHistory[3];
        _class_fld **m_ppHistoryEffect[4];
        _quick_link m_QLink[50];
        struct CGuild *m_pGuild;
        struct _guild_member_info *m_pGuildMemPtr;
        char m_byClassInGuild;
        struct CGuild *m_pApplyGuild;
        bool m_bGuildLock;
        bool m_bTrunkOpen;
        char m_wszTrunkPasswd[13];
        long double m_dTrunkDalant;
        long double m_dTrunkGold;
        char m_byTrunkSlotNum;
        char m_byTrunkHintIndex;
        char m_wszTrunkHintAnswer[17];
        char m_byExtTrunkSlotNum;
        int m_nMakeTrapMaxNum;
        long double m_dPvpPointLeak;
        bool m_bLastAttBuff;
        unsigned __int16 m_wSerialCount;
        struct CPlayer *m_pThis;
        char m_aszName[17];
    public:
        void AddTrunkDalant(unsigned int dwPush);
        void AddTrunkGold(unsigned int dwPush);
        void AppointSerialStorageItem();
        bool BeHaveBoxOfAMP();
        CPlayerDB();
        void ctor_CPlayerDB();
        static char CalcCharGrade(char byLv, uint16_t wRankRate);
        bool ConvertAvatorDB(struct _AVATOR_DATA* pData);
        bool ConvertGeneralDB(struct _AVATOR_DATA* pData, struct _AVATOR_DATA* pOutData);
        bool DeleteItemCountFromCode(char* pszItemCode, int nCount);
        char GetBagNum();
        char* GetCharNameA();
        char* GetCharNameW();
        unsigned int GetCharSerial();
        char GetClassInGuild();
        uint16_t GetCurItemSerial();
        float* GetCurPos();
        int GetDP();
        unsigned int GetDalant();
        long double GetExp();
        char GetExtTrunkSlotNum();
        char GetExtTrunkSlotRace(unsigned int dwItemSerial);
        int GetFP();
        unsigned int GetGold();
        unsigned int GetGuildSerial();
        int GetHP();
        int GetHaveUnitNum();
        int GetInvenItemCountFromCode(char* pszItemCode);
        struct _STORAGE_LIST::_db_con* GetItem(char byInvenIndex);
        int GetLevel();
        long double GetLossExp();
        int GetMapCode();
        int GetMaxLevel();
        uint16_t GetNewItemSerial();
        struct _class_fld* GetPtrBaseClass();
        struct _class_fld* GetPtrCurClass();
        struct _STORAGE_LIST::_db_con* GetPtrItemStorage(uint16_t wSerial, char* pbyStorageCode);
        long double GetPvPCashBag();
        long double GetPvPPoint();
        unsigned int GetPvpRank();
        int GetRaceCode();
        int GetRaceSexCode();
        char GetResBufferNum();
        int GetSP();
        char* GetTrunkPasswdW();
        char GetTrunkSlotNum();
        char GetTrunkSlotRace(unsigned int dwItemSerial);
        char GetUseSlot();
        void InitAlterMastery();
        void InitClass();
        void InitPlayerDB(struct CPlayer* pThis);
        void InitResBuffer();
        bool IsActableClassSkill(char* pszSkillCode, int* pnClassGrade);
        bool IsClassChangeableLv();
        void PopLink(int nLinkIndex);
        bool PushLink(int nLinkIndex, uint16_t wSerail, bool bInit);
        void SelectClass(char byHistoryRecordNum, struct _class_fld* pSelectClass);
        void SetBagNum(char byNum);
        void SetClassInGuild(char byClassInGuild);
        void SetCurPos(float* fPos);
        void SetDP(unsigned int dwDP);
        void SetDalant(unsigned int dwDt);
        void SetExp(long double dExp);
        void SetFP(unsigned int dwFP);
        void SetGold(unsigned int dwGold);
        void SetHP(unsigned int dwHP);
        void SetHaveBoxOfAMP(bool bFlag);
        void SetLevel(int nLv);
        void SetLossExp(long double dLossExp);
        void SetMapCode(char byCode);
        void SetMaxLevel(int nLv);
        void SetPvPPoint(long double dPoint);
        void SetSP(unsigned int dwSP);
        void SubTrunkDalant(unsigned int dwSub);
        void SubTrunkGold(unsigned int dwSub);
        ~CPlayerDB();
        void dtor_CPlayerDB();
    };    
    static_assert(ATF::checkSize<CPlayerDB, 41536>(), "CPlayerDB");
END_ATF_NAMESPACE

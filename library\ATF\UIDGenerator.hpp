// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct  UIDGenerator
    {
        enum E_TYPE_SERIAL
        {
            __NONSERIAL = 0x0,
        };
    public:
        static uint64_t getuid(char n);
        static uint64_t getuid(char n, char ncode);
        static void tmuid(uint64_t uid, char* szBuf);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$E4F8E62569EB27D9115114E54E5E5B40.hpp>


START_ATF_NAMESPACE
    struct CIndexBuffer
    {
        unsigned int m_Flag;
        unsigned int m_Size;
        $E4F8E62569EB27D9115114E54E5E5B40 ___u2;
    public:
        void InitIndexBuffer(int arg_0, int arg_1);
        void ReleaseIndexBuffer();
        uint8_t* VPLock(int arg_0, int arg_1, uint32_t arg_2);
        void VPUnLock();
        ~CIndexBuffer();
        int64_t dtor_CIndexBuffer();
    };
END_ATF_NAMESPACE

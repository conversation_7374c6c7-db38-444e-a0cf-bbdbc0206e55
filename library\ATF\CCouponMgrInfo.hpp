// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCouponMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCouponMgrctor_CCouponMgr2_ptr = void (WINAPIV*)(struct CCouponMgr*);
        using CCouponMgrctor_CCouponMgr2_clbk = void (WINAPIV*)(struct CCouponMgr*, CCouponMgrctor_CCouponMgr2_ptr);
        using CCouponMgrGetCouponInfo4_ptr = struct CouponInfo* (WINAPIV*)(struct CCouponMgr*, char);
        using CCouponMgrGetCouponInfo4_clbk = struct CouponInfo* (WINAPIV*)(struct CCouponMgr*, char, CCouponMgrGetCouponInfo4_ptr);
        using CCouponMgrInit6_ptr = void (WINAPIV*)(struct CCouponMgr*, uint16_t);
        using CCouponMgrInit6_clbk = void (WINAPIV*)(struct CCouponMgr*, uint16_t, CCouponMgrInit6_ptr);
        using CCouponMgrInitCuponInfo8_ptr = void (WINAPIV*)(struct CCouponMgr*);
        using CCouponMgrInitCuponInfo8_clbk = void (WINAPIV*)(struct CCouponMgr*, CCouponMgrInitCuponInfo8_ptr);
        using CCouponMgrIsEnableGetCoupon10_ptr = bool (WINAPIV*)(struct CCouponMgr*);
        using CCouponMgrIsEnableGetCoupon10_clbk = bool (WINAPIV*)(struct CCouponMgr*, CCouponMgrIsEnableGetCoupon10_ptr);
        using CCouponMgrLoadData12_ptr = void (WINAPIV*)(struct CCouponMgr*, unsigned int, struct _PCBANG_PLAY_TIME*);
        using CCouponMgrLoadData12_clbk = void (WINAPIV*)(struct CCouponMgr*, unsigned int, struct _PCBANG_PLAY_TIME*, CCouponMgrLoadData12_ptr);
        using CCouponMgrLogOut14_ptr = void (WINAPIV*)(struct CCouponMgr*, bool);
        using CCouponMgrLogOut14_clbk = void (WINAPIV*)(struct CCouponMgr*, bool, CCouponMgrLogOut14_ptr);
        using CCouponMgrLoop16_ptr = void (WINAPIV*)(struct CCouponMgr*, uint16_t);
        using CCouponMgrLoop16_clbk = void (WINAPIV*)(struct CCouponMgr*, uint16_t, CCouponMgrLoop16_ptr);
        using CCouponMgrReceivePrimiumCoupon18_ptr = void (WINAPIV*)(struct CCouponMgr*, uint16_t);
        using CCouponMgrReceivePrimiumCoupon18_clbk = void (WINAPIV*)(struct CCouponMgr*, uint16_t, CCouponMgrReceivePrimiumCoupon18_ptr);
        using CCouponMgrSendMsg_CouponEnsure20_ptr = void (WINAPIV*)(struct CCouponMgr*, uint16_t, char);
        using CCouponMgrSendMsg_CouponEnsure20_clbk = void (WINAPIV*)(struct CCouponMgr*, uint16_t, char, CCouponMgrSendMsg_CouponEnsure20_ptr);
        using CCouponMgrSendMsg_CouponError22_ptr = void (WINAPIV*)(struct CCouponMgr*, uint16_t, char);
        using CCouponMgrSendMsg_CouponError22_clbk = void (WINAPIV*)(struct CCouponMgr*, uint16_t, char, CCouponMgrSendMsg_CouponError22_ptr);
        using CCouponMgrSendMsg_CouponLendResult24_ptr = void (WINAPIV*)(struct CCouponMgr*, uint16_t, struct _STORAGE_LIST::_db_con*);
        using CCouponMgrSendMsg_CouponLendResult24_clbk = void (WINAPIV*)(struct CCouponMgr*, uint16_t, struct _STORAGE_LIST::_db_con*, CCouponMgrSendMsg_CouponLendResult24_ptr);
        using CCouponMgrSendMsg_InPcBangTime26_ptr = void (WINAPIV*)(struct CCouponMgr*, uint16_t);
        using CCouponMgrSendMsg_InPcBangTime26_clbk = void (WINAPIV*)(struct CCouponMgr*, uint16_t, CCouponMgrSendMsg_InPcBangTime26_ptr);
        using CCouponMgrSendMsg_RemainCouponInform28_ptr = void (WINAPIV*)(struct CCouponMgr*, uint16_t, char);
        using CCouponMgrSendMsg_RemainCouponInform28_clbk = void (WINAPIV*)(struct CCouponMgr*, uint16_t, char, CCouponMgrSendMsg_RemainCouponInform28_ptr);
        using CCouponMgrSetCheetContTime30_ptr = bool (WINAPIV*)(struct CCouponMgr*, uint16_t, int);
        using CCouponMgrSetCheetContTime30_clbk = bool (WINAPIV*)(struct CCouponMgr*, uint16_t, int, CCouponMgrSetCheetContTime30_ptr);
        
        using CCouponMgrdtor_CCouponMgr32_ptr = void (WINAPIV*)(struct CCouponMgr*);
        using CCouponMgrdtor_CCouponMgr32_clbk = void (WINAPIV*)(struct CCouponMgr*, CCouponMgrdtor_CCouponMgr32_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

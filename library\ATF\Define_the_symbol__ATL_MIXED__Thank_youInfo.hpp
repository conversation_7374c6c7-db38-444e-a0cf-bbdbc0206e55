// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Define_the_symbol__ATL_MIXED__Thank_you.hpp>


START_ATF_NAMESPACE
    namespace Define_the_symbol__ATL_MIXED
    {
        namespace Info
        {
            
            using Define_the_symbol__ATL_MIXED__Thank_youctor_Thank_you2_ptr = void (WINAPIV*)(struct Define_the_symbol__ATL_MIXED::Thank_you*);
            using Define_the_symbol__ATL_MIXED__Thank_youctor_Thank_you2_clbk = void (WINAPIV*)(struct Define_the_symbol__ATL_MIXED::Thank_you*, Define_the_symbol__ATL_MIXED__Thank_youctor_Thank_you2_ptr);
            using Define_the_symbol__ATL_MIXED__Thank_youone4_ptr = void (WINAPIV*)(struct Define_the_symbol__ATL_MIXED::Thank_you*);
            using Define_the_symbol__ATL_MIXED__Thank_youone4_clbk = void (WINAPIV*)(struct Define_the_symbol__ATL_MIXED::Thank_you*, Define_the_symbol__ATL_MIXED__Thank_youone4_ptr);
        }; // end namespace Info
    }; // end namespace Define_the_symbol__ATL_MIXED
END_ATF_NAMESPACE

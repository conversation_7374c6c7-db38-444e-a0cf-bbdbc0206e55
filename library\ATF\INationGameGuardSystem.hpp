// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <INationGameGuardSystemVtbl.hpp>


START_ATF_NAMESPACE
    struct INationGameGuardSystem
    {
        INationGameGuardSystemVtbl *vfptr;
    public:
        INationGameGuardSystem();
        void ctor_INationGameGuardSystem();
        ~INationGameGuardSystem();
        void dtor_INationGameGuardSystem();
    };
END_ATF_NAMESPACE

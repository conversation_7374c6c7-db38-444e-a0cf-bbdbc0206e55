// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct TBBUTTONINFOW
    {
        unsigned int cbSize;
        unsigned int dwMask;
        int idCommand;
        int iImage;
        char fsState;
        char fsStyle;
        unsigned __int16 cx;
        unsigned __int64 lParam;
        wchar_t *pszText;
        int cchText;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dh_job_setup.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _dh_job_setupctor__dh_job_setup2_ptr = void (WINAPIV*)(struct _dh_job_setup*);
        using _dh_job_setupctor__dh_job_setup2_clbk = void (WINAPIV*)(struct _dh_job_setup*, _dh_job_setupctor__dh_job_setup2_ptr);
        
        using _dh_job_setupdtor__dh_job_setup7_ptr = void (WINAPIV*)(struct _dh_job_setup*);
        using _dh_job_setupdtor__dh_job_setup7_clbk = void (WINAPIV*)(struct _dh_job_setup*, _dh_job_setupdtor__dh_job_setup7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

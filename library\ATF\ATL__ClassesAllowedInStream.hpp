// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        union ClassesAllowedInStream
        {
            _GUID *rgclsidAllowed;
            HRESULT (WINAPIV *pfnClsidAllowed)(_GUID *, _GUID *, void **);
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

#include <_personal_automine_current_state_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_current_state_zocl::_personal_automine_current_state_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_current_state_zocl*);
        (org_ptr(0x1402de3e0L))(this);
    };
    void _personal_automine_current_state_zocl::ctor__personal_automine_current_state_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_current_state_zocl*);
        (org_ptr(0x1402de3e0L))(this);
    };
    int _personal_automine_current_state_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_current_state_zocl*);
        return (org_ptr(0x1402de430L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _personal_amine_mineore_zocl
    {
        struct __changed
        {
            unsigned __int16 wItemIndex;
            unsigned int dwDur;
            unsigned __int16 wItemSerial;
        public:
            __changed();
            void ctor___changed();
        };
        unsigned int dwObjSerial;
        char m_byUseBattery;
        unsigned int dwBattery;
        char byChanged<PERSON>um;
        __changed change[40];
    public:
        _personal_amine_mineore_zocl();
        void ctor__personal_amine_mineore_zocl();
        void clear();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_personal_amine_mineore_zocl, 330>(), "_personal_amine_mineore_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct DnBuffNodeVtbl
    {
        void *(WINAPIV *__vecDelDtor)(struct DnBuffNode *_this, unsigned int);
        void (WINAPIV *DnNodeClear)(struct DnBuffNode *_this);
        void (WINAPIV *DnNodeOpen)(struct DnBuffNode *_this, unsigned int);
        void (WINAPIV *DnNodeClose)(struct DnBuffNode *_this);
    };
END_ATF_NAMESPACE

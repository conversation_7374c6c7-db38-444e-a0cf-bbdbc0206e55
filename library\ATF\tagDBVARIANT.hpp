// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$1DFB567730D6DFA8B807B25747BF8460.hpp>


START_ATF_NAMESPACE
    struct tagDBVARIANT
    {
        unsigned __int16 vt;
        unsigned __int16 wReserved1;
        unsigned __int16 wReserved2;
        unsigned __int16 wReserved3;
        $1DFB567730D6DFA8B807B25747BF8460 ___u4;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RTL_CRITICAL_SECTION.hpp>


START_ATF_NAMESPACE
    struct CNetCriticalSection
    {
        _RTL_CRITICAL_SECTION m_cs;
    public:
        CNetCriticalSection();
        void ctor_CNetCriticalSection();
        void Lock();
        void Unlock();
        ~CNetCriticalSection();
        void dtor_CNetCriticalSection();
    };    
    static_assert(ATF::checkSize<CNetCriticalSection, 40>(), "CNetCriticalSection");
END_ATF_NAMESPACE

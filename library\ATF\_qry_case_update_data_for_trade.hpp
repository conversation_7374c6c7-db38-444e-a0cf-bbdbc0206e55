// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_update_data_for_trade
    {
        struct list
        {
            unsigned int dwSerial;
            unsigned int dwDalant;
            unsigned int dwGlod;
            struct _AVATOR_DATA *pNewData;
            struct _AVATOR_DATA *pOldData;
        };
        list tradelist[2];
    public:
        _qry_case_update_data_for_trade();
        void ctor__qry_case_update_data_for_trade();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_update_data_for_trade, 64>(), "_qry_case_update_data_for_trade");
END_ATF_NAMESPACE

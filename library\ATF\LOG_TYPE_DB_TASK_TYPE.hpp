// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum LOG_TYPE_DB_TASK_TYPE
    {
      lt_qry_case_none = 0xFFFFFFFF,
      lt_qry_case_insert_settlementowner_log = 0x0,
      lt_qry_case_unmandtrader_select_list = 0x1,
      lt_qry_case_rename_potion_log = 0x2,
      lt_qry_case_max = 0x3,
    };
END_ATF_NAMESPACE

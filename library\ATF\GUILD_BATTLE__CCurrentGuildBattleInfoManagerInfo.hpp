// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CCurrentGuildBattleInfoManager.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int, GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_ptr = void (WINAPIV*)();
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_clbk = void (WINAPIV*)(GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int, GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_ptr = struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* (WINAPIV*)();
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_clbk = struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* (WINAPIV*)(GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, int, unsigned int);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, int, unsigned int, GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int, char, unsigned int);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int, char, unsigned int, GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_ptr);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int, char, unsigned int);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, unsigned int, char, unsigned int, GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_ptr);
            
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*);
            using GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*, GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

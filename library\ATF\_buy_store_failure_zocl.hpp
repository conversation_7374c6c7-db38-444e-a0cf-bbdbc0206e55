// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _buy_store_failure_zocl
    {
        unsigned int dwDalant;
        unsigned int dwGold;
        long double dPoint;
        unsigned int dwActPoint[3];
        char byRetCode;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

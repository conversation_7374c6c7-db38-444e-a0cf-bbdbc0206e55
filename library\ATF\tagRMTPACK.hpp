// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IDispatch.hpp>
#include <ISequentialStream.hpp>
#include <IUnknown.hpp>
#include <tagPROPVARIANT.hpp>
#include <tagVARIANT.hpp>



START_ATF_NAMESPACE
    struct tagRMTPACK
    {
        ISequentialStream *pISeqStream;
        unsigned int cbData;
        unsigned int cBSTR;
        wchar_t **rgBSTR;
        unsigned int cVARIANT;
        tagVARIANT *rgVARIANT;
        unsigned int cIDISPATCH;
        IDispatch **rgIDISPATCH;
        unsigned int cIUNKNOWN;
        IUnknown **rgIUNKNOWN;
        unsigned int cPROPVARIANT;
        tagPROPVARIANT *rgPROPVARIANT;
        unsigned int cArray;
        tagVARIANT *rgArray;
    };
END_ATF_NAMESPACE

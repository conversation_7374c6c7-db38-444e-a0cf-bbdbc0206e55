// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _currencyfmtA
    {
        unsigned int NumDigits;
        unsigned int LeadingZero;
        unsigned int Grouping;
        char *lpDecimalSep;
        char *lpThousandSep;
        unsigned int NegativeOrder;
        unsigned int PositiveOrder;
        char *lpCurrencySymbol;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct  _force_db_load : _STORAGE_LIST
    {
        _STORAGE_LIST::_db_con m_List[88];
    public:
        _force_db_load();
        void ctor__force_db_load();
    };    
    static_assert(ATF::checkSize<_force_db_load, 4420>(), "_force_db_load");
END_ATF_NAMESPACE

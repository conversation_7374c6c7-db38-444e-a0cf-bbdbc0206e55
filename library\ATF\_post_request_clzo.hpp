// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS_INDIV.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _post_request_clzo
    {
        char wszRecvName[17];
        char wszTitle[21];
        char wsz<PERSON>ontent[201];
        _STORAGE_POS_INDIV Item;
        unsigned int dwGold;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

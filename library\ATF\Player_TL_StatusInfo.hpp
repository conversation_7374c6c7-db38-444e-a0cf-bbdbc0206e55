// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Player_TL_Status.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using Player_TL_StatusGetTLStatus2_ptr = char (WINAPIV*)(struct Player_TL_Status*);
        using Player_TL_StatusGetTLStatus2_clbk = char (WINAPIV*)(struct Player_TL_Status*, Player_TL_StatusGetTLStatus2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

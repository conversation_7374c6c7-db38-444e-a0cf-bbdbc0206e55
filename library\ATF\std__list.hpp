// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Bidit.hpp>
#include <std___List_val.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<typename _Ty, typename _Alloc = allocator<_Ty>>
        struct  list : _List_val<_Ty, _Alloc>
        {
            using _IteratorPtr = typename _List_nod<_Ty, _Alloc>::_Node*;

            struct _Const_iterator : _Bidit<_Ty, __int64, _<PERSON> const *, _Ty const &>
            {
                _IteratorPtr *_Ptr;
            };

            struct _Iterator : _Const_iterator
            {
            };

            _Iterator _Myhead;
            unsigned __int64 _Mysize;
        };
    }; // end namespace std
END_ATF_NAMESPACE

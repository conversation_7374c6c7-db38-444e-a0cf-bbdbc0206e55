// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ANI_CAMERA.hpp>
#include <_ANI_OBJECT.hpp>


START_ATF_NAMESPACE
    struct CAniCamera
    {
        _ANI_CAMERA *AniCamera;
        _ANI_OBJECT *mDummy;
        unsigned int mDummyNum;
        unsigned int mAniCameraNum;
        unsigned int mStartFrame;
        unsigned int mEndFrame;
        unsigned int mPlayStartFrame;
        unsigned int mPlayEndFrame;
        int mPlayIndex;
        int mIsSetPerspect;
        float mStartTick;
        unsigned int mFlag;
        float mOldFrame;
        float mNowFrame;
    public:
        uint32_t GetCameraIndex(char* arg_0);
        char* GetCameraName(uint32_t arg_0);
        uint32_t GetDummyID(char* arg_0, int arg_1);
        void GetDummyMatrix(float** arg_0, uint32_t arg_1, float arg_2);
        void GetDummyMatrixSub(float** arg_0, uint32_t arg_1, float arg_2);
        char* GetDummyParentID(uint32_t arg_0);
        float* GetMatrixAniCamera(float arg_0);
        int64_t IsLoadedAniCamera();
        void LoadAniCamera(char* Filename);
        int64_t PlayAniCamera(float** arg_0, float arg_1, int arg_2);
        void ReleaseAniCamera();
        int64_t SetExtCamAni();
        void SetNowFrame(float arg_0);
        void SetPrePlayCamera(uint32_t arg_0, uint32_t arg_1, uint32_t arg_2, uint32_t arg_3);
        ~CAniCamera();
        int64_t dtor_CAniCamera();
    };
END_ATF_NAMESPACE

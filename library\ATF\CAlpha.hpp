// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CAlpha
    {
        unsigned int mAlphaFaceCnt;
        unsigned int *mAlphaFace;
        __int16 *mAlphaFaceZ;
        unsigned int *mTempAlphaFace;
        __int16 *mTempAlphaFaceZ;
        int mAlphaSize;
        void *mBsp;
    public:
        void CheckAlphaAlloc();
        uint32_t* GetAlphaFace();
        uint32_t GetAlphaFaceCnt();
        void InitAlpha(void* arg_0);
        void LoopInitAlphaStack();
        void SetAlphaEntityStack(uint16_t arg_0);
        void SetAlphaStack(uint16_t arg_0);
        void SetCoronaStack(uint16_t arg_0);
        uint32_t* SortAlphaStack(float* arg_0);
        ~CAlpha();
        int64_t dtor_CAlpha();
    };
END_ATF_NAMESPACE

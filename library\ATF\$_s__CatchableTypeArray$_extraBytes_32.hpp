// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_s__CatchableType.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  $_s__CatchableTypeArray$_extraBytes_32
    {
        int nCatchableTypes;
        _s__CatchableType *arrayOfCatchableTypes[4];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

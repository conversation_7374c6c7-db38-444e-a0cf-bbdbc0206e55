// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonsterSkill.hpp>


START_ATF_NAMESPACE
    struct CMonsterSkillPool
    {
        struct CMonster *m_pMyMon;
        int m_nSize;
        CMonsterSkill m_MonSkill[16];
    public:
        CMonsterSkillPool();
        void ctor_CMonsterSkillPool();
        struct CMonsterSkill* GetMonSkill(int nIndex);
        struct CMonsterSkill* GetMonSkillKind(int nKind);
        void Init();
        int InsertSkill(struct CMonsterSkill* skill);
        int Set(struct CMonster* pMyMonster);
        ~CMonsterSkillPool();
        void dtor_CMonsterSkillPool();
    };
END_ATF_NAMESPACE

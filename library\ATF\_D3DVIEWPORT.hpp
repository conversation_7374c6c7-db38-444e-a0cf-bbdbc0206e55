// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _D3DVIEWPORT
    {
        unsigned int dwSize;
        unsigned int dwX;
        unsigned int dwY;
        unsigned int dwWidth;
        unsigned int dwHeight;
        float dvScaleX;
        float dvScaleY;
        float dvMaxX;
        float dvMaxY;
        float dvMinZ;
        float dvMaxZ;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSubClassInfoForceLiverGrade.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderSubClassInfoForceLiverGradector_CUnmannedTraderSubClassInfoForceLiverGrade2_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, struct CUnmannedTraderSubClassInfoForceLiverGrade*);
        using CUnmannedTraderSubClassInfoForceLiverGradector_CUnmannedTraderSubClassInfoForceLiverGrade2_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, struct CUnmannedTraderSubClassInfoForceLiverGrade*, CUnmannedTraderSubClassInfoForceLiverGradector_CUnmannedTraderSubClassInfoForceLiverGrade2_ptr);
        
        using CUnmannedTraderSubClassInfoForceLiverGradector_CUnmannedTraderSubClassInfoForceLiverGrade4_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, unsigned int);
        using CUnmannedTraderSubClassInfoForceLiverGradector_CUnmannedTraderSubClassInfoForceLiverGrade4_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, unsigned int, CUnmannedTraderSubClassInfoForceLiverGradector_CUnmannedTraderSubClassInfoForceLiverGrade4_ptr);
        using CUnmannedTraderSubClassInfoForceLiverGradeCreate6_ptr = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, unsigned int);
        using CUnmannedTraderSubClassInfoForceLiverGradeCreate6_clbk = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, unsigned int, CUnmannedTraderSubClassInfoForceLiverGradeCreate6_ptr);
        using CUnmannedTraderSubClassInfoForceLiverGradeGetGroupID8_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, char, uint16_t, char*);
        using CUnmannedTraderSubClassInfoForceLiverGradeGetGroupID8_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, char, uint16_t, char*, CUnmannedTraderSubClassInfoForceLiverGradeGetGroupID8_ptr);
        using CUnmannedTraderSubClassInfoForceLiverGradeLoadXML10_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int);
        using CUnmannedTraderSubClassInfoForceLiverGradeLoadXML10_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int, CUnmannedTraderSubClassInfoForceLiverGradeLoadXML10_ptr);
        
        using CUnmannedTraderSubClassInfoForceLiverGradedtor_CUnmannedTraderSubClassInfoForceLiverGrade14_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*);
        using CUnmannedTraderSubClassInfoForceLiverGradedtor_CUnmannedTraderSubClassInfoForceLiverGrade14_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoForceLiverGrade*, CUnmannedTraderSubClassInfoForceLiverGradedtor_CUnmannedTraderSubClassInfoForceLiverGrade14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

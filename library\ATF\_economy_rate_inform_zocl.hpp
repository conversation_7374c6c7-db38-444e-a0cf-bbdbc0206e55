// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _economy_rate_inform_zocl
    {
        bool bStart;
        float fPayExgRate;
        float fTexRate;
        unsigned __int16 wMgrValue;
        unsigned __int16 wEconomyGuide[3];
        float fOreSellRate;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

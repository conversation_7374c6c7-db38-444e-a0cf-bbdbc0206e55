// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $E5FC128E66C26C95254032BA69D36E74
    {
        BYTE gap0[8];
        int *plVal;
    };    
    static_assert(ATF::checkSize<$E5FC128E66C26C95254032BA69D36E74, 16>(), "$E5FC128E66C26C95254032BA69D36E74");
END_ATF_NAMESPACE

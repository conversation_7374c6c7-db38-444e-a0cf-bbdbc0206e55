// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CNationCodeStr
    {
        int m_iCode;
        char m_szStr[3];
    public:
        CNationCodeStr(int iCode, char* szCodeStr);
        void ctor_CNationCodeStr(int iCode, char* szCodeStr);
        int GetKey();
        char* GetStr();
        bool IsNULL();
        ~CNationCodeStr();
        void dtor_CNationCodeStr();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CNationCodeStr, 8>(), "CNationCodeStr");
END_ATF_NAMESPACE

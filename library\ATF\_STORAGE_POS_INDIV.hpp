// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _STORAGE_POS_INDIV : _STORAGE_POS
    {
        unsigned __int16 wItemSerial;
        char byNum;
    };
    #pragma pack(push, 1)
    static_assert(ATF::checkSize<_STORAGE_POS_INDIV, 4>(), "_STORAGE_POS_INDIV");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ICsSendInterface.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using ICsSendInterfaceSendMsg_BuyCashItem2_ptr = void (WINAPIV*)(uint16_t, struct _param_cash_update*, struct _param_cash_update*);
        using ICsSendInterfaceSendMsg_BuyCashItem2_clbk = void (WINAPIV*)(uint16_t, struct _param_cash_update*, struct _param_cash_update*, ICsSendInterfaceSendMsg_BuyCashItem2_ptr);
        using ICsSendInterfaceSendMsg_CashDiscountEventInform4_ptr = void (WINAPIV*)(uint16_t, char, struct _cash_discount_ini_*);
        using ICsSendInterfaceSendMsg_CashDiscountEventInform4_clbk = void (WINAPIV*)(uint16_t, char, struct _cash_discount_ini_*, ICsSendInterfaceSendMsg_CashDiscountEventInform4_ptr);
        using ICsSendInterfaceSendMsg_CashEventInform6_ptr = void (WINAPIV*)(uint16_t, char, char, struct _cash_event_ini*, struct _cash_lim_sale*);
        using ICsSendInterfaceSendMsg_CashEventInform6_clbk = void (WINAPIV*)(uint16_t, char, char, struct _cash_event_ini*, struct _cash_lim_sale*, ICsSendInterfaceSendMsg_CashEventInform6_ptr);
        using ICsSendInterfaceSendMsg_ConditionalEventInform8_ptr = void (WINAPIV*)(uint16_t, char, uint16_t, char, char*);
        using ICsSendInterfaceSendMsg_ConditionalEventInform8_clbk = void (WINAPIV*)(uint16_t, char, uint16_t, char, char*, ICsSendInterfaceSendMsg_ConditionalEventInform8_ptr);
        using ICsSendInterfaceSendMsg_Error10_ptr = void (WINAPIV*)(uint16_t, int);
        using ICsSendInterfaceSendMsg_Error10_clbk = void (WINAPIV*)(uint16_t, int, ICsSendInterfaceSendMsg_Error10_ptr);
        using ICsSendInterfaceSendMsg_GoodsList12_ptr = void (WINAPIV*)(uint16_t, struct _param_cash_select*);
        using ICsSendInterfaceSendMsg_GoodsList12_clbk = void (WINAPIV*)(uint16_t, struct _param_cash_select*, ICsSendInterfaceSendMsg_GoodsList12_ptr);
        using ICsSendInterfaceSendMsg_LimitedsaleEventInform14_ptr = void (WINAPIV*)(uint16_t, char, unsigned int, uint16_t);
        using ICsSendInterfaceSendMsg_LimitedsaleEventInform14_clbk = void (WINAPIV*)(uint16_t, char, unsigned int, uint16_t, ICsSendInterfaceSendMsg_LimitedsaleEventInform14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

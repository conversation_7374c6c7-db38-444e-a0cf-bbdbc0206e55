// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _announ_message_receipt_udp
    {
        char byMessageType;
        char bySenderRace;
        unsigned int dwSenderSerial;
        char wszSenderName[17];
        char byPvpGrade;
        char bySubType;
        char bySize;
        char wszChatData[256];
    public:
        _announ_message_receipt_udp();
        void ctor__announ_message_receipt_udp();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_announ_message_receipt_udp, 282>(), "_announ_message_receipt_udp");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderClassInfoTableCodeType.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderClassInfoTableCodeTypector_CUnmannedTraderClassInfoTableCodeType2_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, unsigned int);
        using CUnmannedTraderClassInfoTableCodeTypector_CUnmannedTraderClassInfoTableCodeType2_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, unsigned int, CUnmannedTraderClassInfoTableCodeTypector_CUnmannedTraderClassInfoTableCodeType2_ptr);
        using CUnmannedTraderClassInfoTableCodeTypeCreate4_ptr = struct CUnmannedTraderClassInfo* (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, unsigned int);
        using CUnmannedTraderClassInfoTableCodeTypeCreate4_clbk = struct CUnmannedTraderClassInfo* (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, unsigned int, CUnmannedTraderClassInfoTableCodeTypeCreate4_ptr);
        using CUnmannedTraderClassInfoTableCodeTypeGetGroupID6_ptr = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, char, uint16_t, char*);
        using CUnmannedTraderClassInfoTableCodeTypeGetGroupID6_clbk = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, char, uint16_t, char*, CUnmannedTraderClassInfoTableCodeTypeGetGroupID6_ptr);
        using CUnmannedTraderClassInfoTableCodeTypeGetGroupID8_ptr = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, char, uint16_t, char*, char*);
        using CUnmannedTraderClassInfoTableCodeTypeGetGroupID8_clbk = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, char, uint16_t, char*, char*, CUnmannedTraderClassInfoTableCodeTypeGetGroupID8_ptr);
        using CUnmannedTraderClassInfoTableCodeTypeLoadXML10_ptr = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, struct TiXmlElement*, struct CLogFile*, unsigned int);
        using CUnmannedTraderClassInfoTableCodeTypeLoadXML10_clbk = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, struct TiXmlElement*, struct CLogFile*, unsigned int, CUnmannedTraderClassInfoTableCodeTypeLoadXML10_ptr);
        
        using CUnmannedTraderClassInfoTableCodeTypedtor_CUnmannedTraderClassInfoTableCodeType14_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*);
        using CUnmannedTraderClassInfoTableCodeTypedtor_CUnmannedTraderClassInfoTableCodeType14_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableCodeType*, CUnmannedTraderClassInfoTableCodeTypedtor_CUnmannedTraderClassInfoTableCodeType14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

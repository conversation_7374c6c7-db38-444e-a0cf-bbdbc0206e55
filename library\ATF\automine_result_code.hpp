// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum automine_result_code
    {
      automine_succeed = 0x0,
      automine_working = 0x1,
      automine_stoped = 0x2,
      automine_orekind_index_over = 0x3,
      automine_change_owner_guild = 0x4,
      automine_isnot_owner_guild = 0x5,
      automine_isnot_master = 0x6,
      automine_not_enough_gold = 0x7,
      automine_charge_over = 0x8,
      automine_nonmatch_charge_value = 0x9,
      automine_battery_zero = 0xA,
      automine_inven_full = 0xB,
      automine_invalid_values = 0xC,
      automine_guild_db_io_wait = 0xD,
      automine_failed = 0xFF,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagRID_DEVICE_INFO_KEYBOARD
    {
        unsigned int dwType;
        unsigned int dwSubType;
        unsigned int dwKeyboardMode;
        unsigned int dwNumberOfFunctionKeys;
        unsigned int dwNumberOfIndicators;
        unsigned int dwNumberOfKeysTotal;
    };
END_ATF_NAMESPACE

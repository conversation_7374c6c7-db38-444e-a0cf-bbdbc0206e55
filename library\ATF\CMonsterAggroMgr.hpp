// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAggroNode.hpp>


START_ATF_NAMESPACE
    struct CMonsterAggroMgr
    {
        struct CCharacter *m_pTopAggroCharacter;
        struct CCharacter *m_pTopDamageCharacter;
        struct CCharacter *m_pKingPowerDamageCharacter;
        CAggroNode m_AggroPool[10];
        unsigned int m_dwAggroCount;
        unsigned int m_dwAllResetLastTime;
        unsigned int m_dwShortRankLastTime;
        unsigned int m_dwAllResetTimer;
        unsigned int m_dwShortRankTimer;
        struct CMonster *m_pMonster;
    public:
        CMonsterAggroMgr();
        void ctor_CMonsterAggroMgr();
        struct CCharacter* GetKingPowerDamageCharacter();
        struct CCharacter* GetTopAggroCharacter();
        struct CCharacter* GetTopDamageCharacter();
        void Init();
        void OnlyOnceInit(struct CMonster* pMonster);
        void Process();
        void ResetAggro();
        struct CAggroNode* SearchAggroNode(struct CCharacter* pCharacter);
        void SendChangeAggroData();
        void SetAggro(struct CCharacter* pCharacter, int nDam, int nAttackType, unsigned int dwAttackSerial, int bOtherPlayerSupport, int bTempSkill);
        void SetTopAggroCharacter(struct CCharacter* p);
        void ShortRankDelay(unsigned int dwDelayTime);
        struct CAggroNode* _GetBlinkNode();
        struct CAggroNode* _SearchAggroNode(struct CCharacter* pCharacter);
        void _ShortRank();
        ~CMonsterAggroMgr();
        void dtor_CMonsterAggroMgr();
    };
END_ATF_NAMESPACE

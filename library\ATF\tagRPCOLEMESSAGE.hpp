// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagRPCOLEMESSAGE
    {
        void *reserved1;
        unsigned int dataRepresentation;
        void *Buffer;
        unsigned int cbBuffer;
        unsigned int iMethod;
        void *reserved2[5];
        unsigned int rpcFlags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

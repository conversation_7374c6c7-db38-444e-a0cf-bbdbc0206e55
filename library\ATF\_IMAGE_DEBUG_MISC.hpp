// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _IMAGE_DEBUG_MISC
    {
        unsigned int DataType;
        unsigned int Length;
        char Unicode;
        char Reserved[3];
        char Data[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$22971E98F1DCAE471B592EC18ECB1551.hpp>


START_ATF_NAMESPACE
    struct _IMAGE_SECTION_HEADER
    {
        char Name[8];
        $22971E98F1DCAE471B592EC18ECB1551 Misc;
        unsigned int VirtualAddress;
        unsigned int SizeOfRawData;
        unsigned int PointerToRawData;
        unsigned int PointerToRelocations;
        unsigned int PointerToLinenumbers;
        unsigned __int16 NumberOfRelocations;
        unsigned __int16 NumberOfLinenumbers;
        unsigned int Characteristics;
    };
END_ATF_NAMESPACE

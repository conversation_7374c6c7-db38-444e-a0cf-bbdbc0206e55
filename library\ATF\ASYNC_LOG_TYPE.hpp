// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum ASYNC_LOG_TYPE
    {
      ALT_NONE = 0xFFFFFFFF,
      ALT_ASYNC_LOGGER_SYSTEM_LOG = 0x0,
      ALT_PERFORMANCE_COUNTER_SYSTEM = 0x1,
      ALT_PERFORMANCE_MAIN_LOOP = 0x2,
      ALT_PERFORMANCE_MAIN_DQS = 0x3,
      ALT_PERFORMANCE_LOG_DQS = 0x4,
      ALT_PERFORMANCE_CASH_DQS = 0x5,
      ALT_PERFORMANCE_NETWORK_DQS = 0x6,
      ALT_FIREGUARD_DETECT_LOG = 0x7,
      ALT_HONOR_GUILD_LOG = 0x8,
      ALT_CLASSREFINE_SERVICE_LOG = 0x9,
      ALT_BUY_CASH_ITEM_LOG = 0xA,
      ALT_HACKSHIELD_SYSTEM_LOG = 0xB,
      ALT_APEX_SYSTEM_LOG = 0xC,
      ALT_GETGOLDBAR_LOG = 0xD,
      ALT_GETEVENTCOUPON_LOG = 0xE,
      ALT_MAX = 0xF,
    };
END_ATF_NAMESPACE

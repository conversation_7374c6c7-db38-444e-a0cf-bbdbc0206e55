// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AP_BatterySlot.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using AP_BatterySlotctor_AP_BatterySlot2_ptr = void (WINAPIV*)(struct AP_BatterySlot*);
        using AP_BatterySlotctor_AP_BatterySlot2_clbk = void (WINAPIV*)(struct AP_BatterySlot*, AP_BatterySlotctor_AP_BatterySlot2_ptr);
        using AP_BatterySlotclear6_ptr = void (WINAPIV*)(struct AP_BatterySlot*);
        using AP_BatterySlotclear6_clbk = void (WINAPIV*)(struct AP_BatterySlot*, AP_BatterySlotclear6_ptr);
        using AP_BatterySlotextract8_ptr = bool (WINAPIV*)(struct AP_BatterySlot*, struct _STORAGE_LIST::_db_con*);
        using AP_BatterySlotextract8_clbk = bool (WINAPIV*)(struct AP_BatterySlot*, struct _STORAGE_LIST::_db_con*, AP_BatterySlotextract8_ptr);
        using AP_BatterySlotget_battery10_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct AP_BatterySlot*);
        using AP_BatterySlotget_battery10_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct AP_BatterySlot*, AP_BatterySlotget_battery10_ptr);
        using AP_BatterySlotget_dur12_ptr = unsigned int (WINAPIV*)(struct AP_BatterySlot*);
        using AP_BatterySlotget_dur12_clbk = unsigned int (WINAPIV*)(struct AP_BatterySlot*, AP_BatterySlotget_dur12_ptr);
        using AP_BatterySlotinsert14_ptr = int (WINAPIV*)(struct AP_BatterySlot*, struct _STORAGE_LIST::_db_con*);
        using AP_BatterySlotinsert14_clbk = int (WINAPIV*)(struct AP_BatterySlot*, struct _STORAGE_LIST::_db_con*, AP_BatterySlotinsert14_ptr);
        using AP_BatterySlotis_private_item16_ptr = bool (WINAPIV*)(struct AP_BatterySlot*, struct _STORAGE_LIST::_db_con*);
        using AP_BatterySlotis_private_item16_clbk = bool (WINAPIV*)(struct AP_BatterySlot*, struct _STORAGE_LIST::_db_con*, AP_BatterySlotis_private_item16_ptr);
        using AP_BatterySlotsub_dur18_ptr = unsigned int (WINAPIV*)(struct AP_BatterySlot*, unsigned int);
        using AP_BatterySlotsub_dur18_clbk = unsigned int (WINAPIV*)(struct AP_BatterySlot*, unsigned int, AP_BatterySlotsub_dur18_ptr);
        
        using AP_BatterySlotdtor_AP_BatterySlot20_ptr = void (WINAPIV*)(struct AP_BatterySlot*);
        using AP_BatterySlotdtor_AP_BatterySlot20_clbk = void (WINAPIV*)(struct AP_BatterySlot*, AP_BatterySlotdtor_AP_BatterySlot20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

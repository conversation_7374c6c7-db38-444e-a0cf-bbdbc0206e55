// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE.hpp>
#include <HWND.hpp>
#include <HWND__.hpp>
#include <_D3DFORMAT.hpp>


START_ATF_NAMESPACE
    struct CD3DApplication
    {
        enum _D3DDEVTYPE {
            D3DDEVTYPE_HAL = 1,
            D3DDEVTYPE_REF = 2,
            D3DDEVTYPE_SW = 3,
            D3DDEVTYPE_FORCE_DWORD = 0xffffffff
        };
    public:
        int32_t AdjustWindowForChange();
        int32_t BuildDeviceList();
        CD3DApplication();
        int64_t ctor_CD3DApplication();
        void Cleanup3DEnvironment();
        int32_t ConfirmDevice(struct _D3DCAPS8* arg_0, uint32_t arg_1, _D3DFORMAT arg_2);
        int32_t Create(HINSTANCE arg_0);
        int32_t CreateDirect3D();
        int32_t DeleteDeviceObjects();
        int32_t DisplayErrorMsg(int32_t arg_0, uint32_t arg_1);
        int32_t EndLoop();
        int32_t FinalCleanup();
        int64_t FindDepthStencilFormat(unsigned int arg_0, _D3DDEVTYPE arg_1, _D3DFORMAT arg_2, _D3DFORMAT* arg_3);
        int32_t ForceWindowed();
        int32_t FrameMove();
        int32_t InitDeviceObjects();
        int32_t Initialize3DEnvironment();
        int32_t InvalidateDeviceObjects();
        int64_t MsgProc(HWND hWnd, UINT Msg, WPARAM wParam, LPARAM lParam);
        int32_t OneTimeSceneInit();
        void Pause(int arg_0);
        int32_t PrepareLoop();
        int32_t Release();
        int32_t Render();
        int32_t Render3DEnvironment();
        int32_t Resize3DEnvironment();
        int32_t RestoreDeviceObjects();
        int64_t Run();
        static int64_t SelectDeviceProc(HWND__* arg_0, unsigned int arg_1, uint64_t arg_2, int64_t arg_3);
        int32_t ToggleFullscreen();
        int32_t UserSelectNewDevice();
    }
    ;
END_ATF_NAMESPACE

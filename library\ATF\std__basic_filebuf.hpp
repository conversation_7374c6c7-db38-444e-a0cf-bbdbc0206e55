// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_iobuf.hpp>
#include <std__basic_streambuf.hpp>
#include <std__codecvt.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  basic_filebuf<char,char_traits<char> > : basic_streambuf<char,char_traits<char> >
        {
            template<>
            enum _Initfl
            {
                _Newfl = 0x0,
                _Openfl = 0x1,
                _Closefl = 0x2,
            };
            codecvt<char,char,int> *_Pcvt;
            char _Mychar;
            bool _Wrotesome;
            int _State;
            bool _Closef;
            _iobuf *_Myfile;
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <_iobuf.hpp>
#include <std__basic_streambuf.hpp>
#include <std__codecvt.hpp>



START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  basic_filebuf<wchar_t,char_traits<wchar_t> > : basic_streambuf<wchar_t,char_traits<wchar_t> >
        {
            template<>
            typedef basic_filebuf<char,char_traits<char> >::_Initfl _Initfl;
            codecvt<wchar_t,char,int> *_Pcvt;
            wchar_t _Mychar;
            bool _Wrotesome;
            int _State;
            bool _Closef;
            _iobuf *_Myfile;
        };
    }; // end namespace std
END_ATF_NAMESPACE

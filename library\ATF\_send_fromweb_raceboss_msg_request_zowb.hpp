// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _send_fromweb_raceboss_msg_request_zowb
    {
        unsigned int dwWebSendDBID;
        int nCountIndex;
        int nWorldCode;
        char byRaceCode;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDarkHoleDungeonQuestSetup.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CDarkHoleDungeonQuestSetupctor_CDarkHoleDungeonQuestSetup2_ptr = void (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*);
        using CDarkHoleDungeonQuestSetupctor_CDarkHoleDungeonQuestSetup2_clbk = void (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, CDarkHoleDungeonQuestSetupctor_CDarkHoleDungeonQuestSetup2_ptr);
        using CDarkHoleDungeonQuestSetupGetErrorMsg4_ptr = char* (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*);
        using CDarkHoleDungeonQuestSetupGetErrorMsg4_clbk = char* (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, CDarkHoleDungeonQuestSetupGetErrorMsg4_ptr);
        using CDarkHoleDungeonQuestSetupGetQuestSetupPtr6_ptr = struct _dh_quest_setup* (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, unsigned int);
        using CDarkHoleDungeonQuestSetupGetQuestSetupPtr6_clbk = struct _dh_quest_setup* (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, unsigned int, CDarkHoleDungeonQuestSetupGetQuestSetupPtr6_ptr);
        using CDarkHoleDungeonQuestSetupSetupQuest8_ptr = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, char*);
        using CDarkHoleDungeonQuestSetupSetupQuest8_clbk = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, char*, CDarkHoleDungeonQuestSetupSetupQuest8_ptr);
        using CDarkHoleDungeonQuestSetup_Analysis_Job_Condition10_ptr = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*);
        using CDarkHoleDungeonQuestSetup_Analysis_Job_Condition10_clbk = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*, CDarkHoleDungeonQuestSetup_Analysis_Job_Condition10_ptr);
        using CDarkHoleDungeonQuestSetup_Analysis_Job_Setting12_ptr = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*);
        using CDarkHoleDungeonQuestSetup_Analysis_Job_Setting12_clbk = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*, CDarkHoleDungeonQuestSetup_Analysis_Job_Setting12_ptr);
        using CDarkHoleDungeonQuestSetup_Analysis_Mission_Condition14_ptr = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*);
        using CDarkHoleDungeonQuestSetup_Analysis_Mission_Condition14_clbk = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*, CDarkHoleDungeonQuestSetup_Analysis_Mission_Condition14_ptr);
        using CDarkHoleDungeonQuestSetup_Analysis_Mission_Setting16_ptr = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*);
        using CDarkHoleDungeonQuestSetup_Analysis_Mission_Setting16_clbk = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*, CDarkHoleDungeonQuestSetup_Analysis_Mission_Setting16_ptr);
        using CDarkHoleDungeonQuestSetup_Analysis_Quest_Condition18_ptr = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*);
        using CDarkHoleDungeonQuestSetup_Analysis_Quest_Condition18_clbk = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*, CDarkHoleDungeonQuestSetup_Analysis_Quest_Condition18_ptr);
        using CDarkHoleDungeonQuestSetup_Analysis_Quest_Setting20_ptr = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*);
        using CDarkHoleDungeonQuestSetup_Analysis_Quest_Setting20_clbk = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, struct strFILE*, CDarkHoleDungeonQuestSetup_Analysis_Quest_Setting20_ptr);
        using CDarkHoleDungeonQuestSetup_LastCheckScipt22_ptr = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, char*);
        using CDarkHoleDungeonQuestSetup_LastCheckScipt22_clbk = bool (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, char*, CDarkHoleDungeonQuestSetup_LastCheckScipt22_ptr);
        
        using CDarkHoleDungeonQuestSetupdtor_CDarkHoleDungeonQuestSetup27_ptr = void (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*);
        using CDarkHoleDungeonQuestSetupdtor_CDarkHoleDungeonQuestSetup27_clbk = void (WINAPIV*)(struct CDarkHoleDungeonQuestSetup*, CDarkHoleDungeonQuestSetupdtor_CDarkHoleDungeonQuestSetup27_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum amine_sub_qry
    {
      amine_sub_qry_changeowner = 0x0,
      amine_sub_qry_batterycharge = 0x1,
      amine_sub_qry_mineore = 0x2,
      amine_sub_qry_workstate = 0x3,
      amine_sub_qry_selore = 0x4,
      amine_sub_qry_battery_discharge = 0x5,
      amine_sub_qry_moveore = 0x6,
    };
END_ATF_NAMESPACE

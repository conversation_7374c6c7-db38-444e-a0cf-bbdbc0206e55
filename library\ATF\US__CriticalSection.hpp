// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RTL_CRITICAL_SECTION.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        struct CriticalSection
        {
            _RTL_CRITICAL_SECTION m_CS_;
        public:
            CriticalSection();
            void ctor_CriticalSection();
            void Lock();
            void UnLock();
            ~CriticalSection();
            void dtor_CriticalSection();
        };    
        static_assert(ATF::checkSize<US::CriticalSection, 40>(), "US::CriticalSection");
    }; // end namespace US
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTimeSpan.hpp>
#include <GUILD_BATTLE__CGuildBattleStateVtbl.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleState
        {
            enum GBS_ADVANCE
            {
                GBSA_REMAIN = 0x0,
                GBSA_END = 0x1,
                GBSA_NEXT = 0x2,
                GBSA_GOTO = 0x3,
            };
            CGuildBattleStateVtbl *vfptr;
        public:
            CGuildBattleState();
            void ctor_CGuildBattleState();
            int Enter(struct CGuildBattle* pkBattle);
            int Fin(struct CGuildBattle* pkBattle);
            ATL::CTimeSpan* GetTerm(ATL::CTimeSpan* result);
            void Log(char* szMsg);
            int Loop(struct CGuildBattle* pkBattle);
            ~CGuildBattleState();
            void dtor_CGuildBattleState();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CGuildBattleState, 8>(), "GUILD_BATTLE::CGuildBattleState");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

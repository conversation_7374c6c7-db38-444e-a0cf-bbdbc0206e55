// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CChatStealSystem.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CChatStealSystemctor_CChatStealSystem2_ptr = void (WINAPIV*)(struct CChatStealSystem*);
        using CChatStealSystemctor_CChatStealSystem2_clbk = void (WINAPIV*)(struct CChatStealSystem*, CChatStealSystemctor_CChatStealSystem2_ptr);
        using CChatStealSystemInstance4_ptr = struct CChatStealSystem* (WINAPIV*)();
        using CChatStealSystemInstance4_clbk = struct CChatStealSystem* (WINAPIV*)(CChatStealSystemInstance4_ptr);
        using CChatStealSystemSendStealMsg6_ptr = void (WINAPIV*)(struct CChatStealSystem*, struct CPlayer*, char, unsigned int, char*, char, char*);
        using CChatStealSystemSendStealMsg6_clbk = void (WINAPIV*)(struct CChatStealSystem*, struct CPlayer*, char, unsigned int, char*, char, char*, CChatStealSystemSendStealMsg6_ptr);
        using CChatStealSystemSetGm8_ptr = bool (WINAPIV*)(struct CChatStealSystem*, struct CPlayer*);
        using CChatStealSystemSetGm8_clbk = bool (WINAPIV*)(struct CChatStealSystem*, struct CPlayer*, CChatStealSystemSetGm8_ptr);
        using CChatStealSystemSetTargetInfoFromBoss10_ptr = bool (WINAPIV*)(struct CChatStealSystem*, char, char);
        using CChatStealSystemSetTargetInfoFromBoss10_clbk = bool (WINAPIV*)(struct CChatStealSystem*, char, char, CChatStealSystemSetTargetInfoFromBoss10_ptr);
        using CChatStealSystemSetTargetInfoFromCharacter12_ptr = bool (WINAPIV*)(struct CChatStealSystem*, char, char*);
        using CChatStealSystemSetTargetInfoFromCharacter12_clbk = bool (WINAPIV*)(struct CChatStealSystem*, char, char*, CChatStealSystemSetTargetInfoFromCharacter12_ptr);
        using CChatStealSystemSetTargetInfoFromRace14_ptr = bool (WINAPIV*)(struct CChatStealSystem*, char, char);
        using CChatStealSystemSetTargetInfoFromRace14_clbk = bool (WINAPIV*)(struct CChatStealSystem*, char, char, CChatStealSystemSetTargetInfoFromRace14_ptr);
        using CChatStealSystemStealChatMsg16_ptr = void (WINAPIV*)(struct CChatStealSystem*, struct CPlayer*, char, char*);
        using CChatStealSystemStealChatMsg16_clbk = void (WINAPIV*)(struct CChatStealSystem*, struct CPlayer*, char, char*, CChatStealSystemStealChatMsg16_ptr);
        
        using CChatStealSystemdtor_CChatStealSystem18_ptr = void (WINAPIV*)(struct CChatStealSystem*);
        using CChatStealSystemdtor_CChatStealSystem18_clbk = void (WINAPIV*)(struct CChatStealSystem*, CChatStealSystemdtor_CChatStealSystem18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

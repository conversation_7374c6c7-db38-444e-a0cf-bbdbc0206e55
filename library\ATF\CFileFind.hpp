// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CObject.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CFileFind : CObject
    {
        void *m_pFoundInfo;
        void *m_pNextInfo;
        void *m_hContext;
        ATL::CStringT<char> m_strRoot;
        char m_chDirSeparator;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$8662F439D215AAEDBB1F787C8B649648.hpp>


START_ATF_NAMESPACE
    struct _NT_TIB64
    {
        unsigned __int64 ExceptionList;
        unsigned __int64 StackBase;
        unsigned __int64 StackLimit;
        unsigned __int64 SubSystemTib;
        $8662F439D215AAEDBB1F787C8B649648 ___u4;
        unsigned __int64 ArbitraryUserPointer;
        unsigned __int64 Self;
    };
END_ATF_NAMESPACE

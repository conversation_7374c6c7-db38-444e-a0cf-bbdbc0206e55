// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _guild_join_accept_inform_zocl
    {
        unsigned int dwAccepterSerial;
        unsigned int dwApplierSerial;
        char wszName[17];
        char byClassInGuild;
        char byLv;
         unsigned int dwPvpPoint;
        char byGuildRank;
    };
END_ATF_NAMESPACE

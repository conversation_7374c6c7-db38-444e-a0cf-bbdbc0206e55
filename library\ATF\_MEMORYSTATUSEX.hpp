// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _MEMORYSTATUSEX
    {
        unsigned int dwLength;
        unsigned int dwMemoryLoad;
        unsigned __int64 ullTotalPhys;
        unsigned __int64 ullAvailPhys;
        unsigned __int64 ullTotalPageFile;
        unsigned __int64 ullAvailPageFile;
        unsigned __int64 ullTotalVirtual;
        unsigned __int64 ullAvailVirtual;
        unsigned __int64 ullAvailExtendedVirtual;
    };
END_ATF_NAMESPACE

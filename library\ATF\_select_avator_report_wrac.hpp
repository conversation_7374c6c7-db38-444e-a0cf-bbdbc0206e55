// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GLBID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _select_avator_report_wrac
    {
        _GLBID gidGlobal;
        char wszCharName[17];
        unsigned int dwAvatorSerial;
        char byLevel;
    public:
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

#include <_trunk_est_result_zocl.hpp>


START_ATF_NAMESPACE
    _trunk_est_result_zocl::_trunk_est_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _trunk_est_result_zocl*);
        (org_ptr(0x1400effa0L))(this);
    };
    void _trunk_est_result_zocl::ctor__trunk_est_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _trunk_est_result_zocl*);
        (org_ptr(0x1400effa0L))(this);
    };
END_ATF_NAMESPACE

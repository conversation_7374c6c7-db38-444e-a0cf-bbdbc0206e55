// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    const struct  _CashShop_fld : _base_fld
    {
        char m_strCsItemCode[64];
        int m_nCsPrice;
        int m_bView;
        char m_strItemPart[64];
        int m_nCsDiscount;
        int m_nCsEvent[8];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__exception.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            
            using std__exceptionctor_exception1_ptr = int64_t (WINAPIV*)(struct std::exception*, char**);
            using std__exceptionctor_exception1_clbk = int64_t (WINAPIV*)(struct std::exception*, char**, std__exceptionctor_exception1_ptr);
            
            using std__exceptionctor_exception2_ptr = int64_t (WINAPIV*)(struct std::exception*, char**, int);
            using std__exceptionctor_exception2_clbk = int64_t (WINAPIV*)(struct std::exception*, char**, int, std__exceptionctor_exception2_ptr);
            
            using std__exceptionctor_exception3_ptr = int64_t (WINAPIV*)(struct std::exception*, struct std::exception*);
            using std__exceptionctor_exception3_clbk = int64_t (WINAPIV*)(struct std::exception*, struct std::exception*, std__exceptionctor_exception3_ptr);
            
            using std__exceptionctor_exception4_ptr = int64_t (WINAPIV*)(struct std::exception*);
            using std__exceptionctor_exception4_clbk = int64_t (WINAPIV*)(struct std::exception*, std__exceptionctor_exception4_ptr);
            using std__exceptionwhat5_ptr = char* (WINAPIV*)(struct std::exception*);
            using std__exceptionwhat5_clbk = char* (WINAPIV*)(struct std::exception*, std__exceptionwhat5_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

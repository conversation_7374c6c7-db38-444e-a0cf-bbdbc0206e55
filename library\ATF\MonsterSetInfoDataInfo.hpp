// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <MonsterSetInfoData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using MonsterSetInfoDataGetLevelContSFTime2_ptr = char (WINAPIV*)(struct MonsterSetInfoData*, char, char);
        using MonsterSetInfoDataGetLevelContSFTime2_clbk = char (WINAPIV*)(struct MonsterSetInfoData*, char, char, MonsterSetInfoDataGetLevelContSFTime2_ptr);
        using MonsterSetInfoDataGetLostMonsterTargetDistance4_ptr = int (WINAPIV*)(struct MonsterSetInfoData*);
        using MonsterSetInfoDataGetLostMonsterTargetDistance4_clbk = int (WINAPIV*)(struct MonsterSetInfoData*, MonsterSetInfoDataGetLostMonsterTargetDistance4_ptr);
        using MonsterSetInfoDataGetMaxToleranceProbMax6_ptr = float (WINAPIV*)(struct MonsterSetInfoData*, int);
        using MonsterSetInfoDataGetMaxToleranceProbMax6_clbk = float (WINAPIV*)(struct MonsterSetInfoData*, int, MonsterSetInfoDataGetMaxToleranceProbMax6_ptr);
        using MonsterSetInfoDataGetMonsterDropRate8_ptr = unsigned int (WINAPIV*)(struct MonsterSetInfoData*, int);
        using MonsterSetInfoDataGetMonsterDropRate8_clbk = unsigned int (WINAPIV*)(struct MonsterSetInfoData*, int, MonsterSetInfoDataGetMonsterDropRate8_ptr);
        using MonsterSetInfoDataGetMonsterForcePowerRate10_ptr = float (WINAPIV*)(struct MonsterSetInfoData*);
        using MonsterSetInfoDataGetMonsterForcePowerRate10_clbk = float (WINAPIV*)(struct MonsterSetInfoData*, MonsterSetInfoDataGetMonsterForcePowerRate10_ptr);
        using MonsterSetInfoDataInit12_ptr = void (WINAPIV*)(struct MonsterSetInfoData*);
        using MonsterSetInfoDataInit12_clbk = void (WINAPIV*)(struct MonsterSetInfoData*, MonsterSetInfoDataInit12_ptr);
        using MonsterSetInfoDataIsRotateBlock14_ptr = bool (WINAPIV*)(struct MonsterSetInfoData*, struct _mon_block*);
        using MonsterSetInfoDataIsRotateBlock14_clbk = bool (WINAPIV*)(struct MonsterSetInfoData*, struct _mon_block*, MonsterSetInfoDataIsRotateBlock14_ptr);
        using MonsterSetInfoDataLoad16_ptr = int (WINAPIV*)(struct MonsterSetInfoData*, char*);
        using MonsterSetInfoDataLoad16_clbk = int (WINAPIV*)(struct MonsterSetInfoData*, char*, MonsterSetInfoDataLoad16_ptr);
        
        using MonsterSetInfoDatactor_MonsterSetInfoData18_ptr = void (WINAPIV*)(struct MonsterSetInfoData*);
        using MonsterSetInfoDatactor_MonsterSetInfoData18_clbk = void (WINAPIV*)(struct MonsterSetInfoData*, MonsterSetInfoDatactor_MonsterSetInfoData18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

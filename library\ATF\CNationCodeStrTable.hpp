// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHashMapPtrPool.hpp>
#include <CNationCodeStr.hpp>


START_ATF_NAMESPACE
    struct CNationCodeStrTable
    {
        CHashMapPtrPool<int,CNationCodeStr> m_kTable;
    public:
        CNationCodeStrTable();
        void ctor_CNationCodeStrTable();
        int GetCode(char* szCodeStr);
        char* GetStr(int iType);
        bool Init();
        int RegistCode();
        ~CNationCodeStrTable();
        void dtor_CNationCodeStrTable();
    };    
    //static_assert(ATF::checkSize<CNationCodeStrTable, 128>(), "CNationCodeStrTable");
END_ATF_NAMESPACE

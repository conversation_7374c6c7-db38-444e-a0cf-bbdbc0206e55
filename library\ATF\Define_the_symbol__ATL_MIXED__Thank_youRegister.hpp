// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Define_the_symbol__ATL_MIXED__Thank_youDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Define_the_symbol__ATL_MIXED
    {
        namespace Register
        {
            class Thank_youRegister : public IRegister
            {
                public: 
                    void Register() override
                    {
                        auto& hook_core = CATFCore::get_instance();
                        for (auto& r : Define_the_symbol__ATL_MIXED::Detail::Thank_you_functions)
                            hook_core.reg_wrapper(r.pBind, r);
                    }
            };
        }; // end namespace Register
    }; // end namespace Define_the_symbol__ATL_MIXED
END_ATF_NAMESPACE

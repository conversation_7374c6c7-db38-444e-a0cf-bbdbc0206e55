// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRecordDataVtbl.hpp>
#include <_base_fld.hpp>
#include <_record_bin_header.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CRecordData
    {
        CRecordDataVtbl *vfptr;
        bool m_bLoad;
        char m_szFileName[129];
        unsigned int m_dwTotalSize;
        _record_bin_header m_Header;
        int m_nLowNum;
        char **m_ppsRecord;
        unsigned int *m_pdwHashList;
    public:
        CRecordData();
        void ctor_CRecordData();
        unsigned int FileSize(char* szFile);
        struct _base_fld* GetRecord(char* szRecordCode);
        struct _base_fld* GetRecord(char* szRecordCode, int nCompareLen);
        struct _base_fld* GetRecord(int n);
        struct _base_fld* GetRecordByHash(char* szRecordCode, int offset, int len);
        int GetRecordNum();
        bool IsTableOpen();
        bool LoadRecordData(void* hFile, char* pszErrMsg);
        bool LoadRecordHeader(void* hFile, char* pszErrMsg);
        static unsigned int MakeHash(char* p, int len);
        bool MakeHashTable(int offset, int len, char* pszErrMsg);
        bool ReadRecord(char* szFile, unsigned int dwStructSize, char* pszErrMsg);
        bool ReadRecord_Ex(char* szFile1, char* szFile2, unsigned int dwStructSize, char* pszErrMsg);
        ~CRecordData();
        void dtor_CRecordData();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<CRecordData, 176>(), "CRecordData");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$CE1967C81C4C59269727E4AA1D2A6A5A.hpp>
#include <IUnknown.hpp>


START_ATF_NAMESPACE
    struct tagSTGMEDIUM
    {
        unsigned int tymed;
        $CE1967C81C4C59269727E4AA1D2A6A5A ___u1;
        IUnknown *pUnkForRelease;
    };
END_ATF_NAMESPACE

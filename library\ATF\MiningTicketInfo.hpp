// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <MiningTicket.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using MiningTicketAuthLastCriTicket2_ptr = int (WINAPIV*)(struct MiningTicket*, uint16_t, char, char, char, char);
        using MiningTicketAuthLastCriTicket2_clbk = int (WINAPIV*)(struct MiningTicket*, uint16_t, char, char, char, char, MiningTicketAuthLastCriTicket2_ptr);
        using MiningTicketAuthLastMentalTicket4_ptr = int (WINAPIV*)(struct MiningTicket*, uint16_t, char, char, char, char);
        using MiningTicketAuthLastMentalTicket4_clbk = int (WINAPIV*)(struct MiningTicket*, uint16_t, char, char, char, char, MiningTicketAuthLastMentalTicket4_ptr);
        using MiningTicketGetLastCriTicket6_ptr = unsigned int (WINAPIV*)(struct MiningTicket*);
        using MiningTicketGetLastCriTicket6_clbk = unsigned int (WINAPIV*)(struct MiningTicket*, MiningTicketGetLastCriTicket6_ptr);
        using MiningTicketGetLastMentalTicket8_ptr = unsigned int (WINAPIV*)(struct MiningTicket*);
        using MiningTicketGetLastMentalTicket8_clbk = unsigned int (WINAPIV*)(struct MiningTicket*, MiningTicketGetLastMentalTicket8_ptr);
        using MiningTicketInit10_ptr = void (WINAPIV*)(struct MiningTicket*);
        using MiningTicketInit10_clbk = void (WINAPIV*)(struct MiningTicket*, MiningTicketInit10_ptr);
        
        using MiningTicketctor_MiningTicket12_ptr = void (WINAPIV*)(struct MiningTicket*);
        using MiningTicketctor_MiningTicket12_clbk = void (WINAPIV*)(struct MiningTicket*, MiningTicketctor_MiningTicket12_ptr);
        using MiningTicketSetLastCriTicket14_ptr = void (WINAPIV*)(struct MiningTicket*, unsigned int);
        using MiningTicketSetLastCriTicket14_clbk = void (WINAPIV*)(struct MiningTicket*, unsigned int, MiningTicketSetLastCriTicket14_ptr);
        using MiningTicketSetLastCriTicket16_ptr = void (WINAPIV*)(struct MiningTicket*, uint16_t, char, char, char, char);
        using MiningTicketSetLastCriTicket16_clbk = void (WINAPIV*)(struct MiningTicket*, uint16_t, char, char, char, char, MiningTicketSetLastCriTicket16_ptr);
        using MiningTicketSetLastMentalTicket18_ptr = void (WINAPIV*)(struct MiningTicket*, unsigned int);
        using MiningTicketSetLastMentalTicket18_clbk = void (WINAPIV*)(struct MiningTicket*, unsigned int, MiningTicketSetLastMentalTicket18_ptr);
        using MiningTicketSetLastMentalTicket20_ptr = void (WINAPIV*)(struct MiningTicket*, uint16_t, char, char, char, char);
        using MiningTicketSetLastMentalTicket20_clbk = void (WINAPIV*)(struct MiningTicket*, uint16_t, char, char, char, char, MiningTicketSetLastMentalTicket20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

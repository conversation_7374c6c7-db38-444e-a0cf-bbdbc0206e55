// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_limit_item_num_info_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _limit_item_num_info_zoclctor__limit_item_num_info_zocl2_ptr = void (WINAPIV*)(struct _limit_item_num_info_zocl*);
        using _limit_item_num_info_zoclctor__limit_item_num_info_zocl2_clbk = void (WINAPIV*)(struct _limit_item_num_info_zocl*, _limit_item_num_info_zoclctor__limit_item_num_info_zocl2_ptr);
        using _limit_item_num_info_zoclsize4_ptr = int (WINAPIV*)(struct _limit_item_num_info_zocl*);
        using _limit_item_num_info_zoclsize4_clbk = int (WINAPIV*)(struct _limit_item_num_info_zocl*, _limit_item_num_info_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

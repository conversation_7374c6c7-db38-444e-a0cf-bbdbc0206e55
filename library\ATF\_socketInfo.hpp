// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_socket.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _socketInitParam2_ptr = void (WINAPIV*)(struct _socket*);
        using _socketInitParam2_clbk = void (WINAPIV*)(struct _socket*, _socketInitParam2_ptr);
        
        using _socketctor__socket4_ptr = void (WINAPIV*)(struct _socket*);
        using _socketctor__socket4_clbk = void (WINAPIV*)(struct _socket*, _socketctor__socket4_ptr);
        
        using _socketdtor__socket8_ptr = void (WINAPIV*)(struct _socket*);
        using _socketdtor__socket8_clbk = void (WINAPIV*)(struct _socket*, _socketdtor__socket8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _buy_store_success_zocl
    {
        struct  _list
        {
            unsigned __int16 wSerial;
            char byCsMethod;
            unsigned int dwT;
        };
        unsigned int dwLeftDalant;
        unsigned int dwLeftGold;
        unsigned int dwConsumDanlant;
        unsigned int dwConsumGold;
        unsigned int dwLeftPoint;
        unsigned int dwConsumPoint;
        unsigned int dwLeftActPoint[3];
        unsigned int dwConsumActPoint[3];
        char byDiscountRate;
        char byBuy<PERSON><PERSON>;
        _list OfferList[100];
    public:
        _buy_store_success_zocl();
        void ctor__buy_store_success_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_buy_store_success_zocl, 750>(), "_buy_store_success_zocl");
END_ATF_NAMESPACE

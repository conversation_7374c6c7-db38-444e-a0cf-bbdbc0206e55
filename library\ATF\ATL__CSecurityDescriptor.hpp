// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ACL.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct CSecurityDescriptor
        {
            void *m_pSD;
            void *m_pOwner;
            void *m_pGroup;
            _ACL *m_pDACL;
            _ACL *m_pSACL;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

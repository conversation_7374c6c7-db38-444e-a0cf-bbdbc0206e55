// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <DnBuffNodeVtbl.hpp>


START_ATF_NAMESPACE
    struct DnBuffNode
    {
        enum BuffUserState
        {
            eBuff_empty = 0x0,
            eBuff_open = 0x1,
            eBuf_close = 0x2,
        };
        DnBuffNodeVtbl *vfptr;
        unsigned int m_dwBuffIndex;
        int m_nBuffUserState;
    public:
        DnBuffNode();
        void ctor_DnBuffNode();
        void DnNodeClear();
        void DnNodeClose();
        void DnNodeOpen(unsigned int dwBuffIndex);
        unsigned int GetBuffIndex();
        ~DnBuffNode();
        void dtor_DnBuffNode();
    };
END_ATF_NAMESPACE

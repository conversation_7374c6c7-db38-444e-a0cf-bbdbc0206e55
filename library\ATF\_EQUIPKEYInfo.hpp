// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_EQUIPKEY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _EQUIPKEYCovDBKey2_ptr = int16_t (WINAPIV*)(struct _EQUIPKEY*);
        using _EQUIPKEYCovDBKey2_clbk = int16_t (WINAPIV*)(struct _EQUIPKEY*, _EQUIPKEYCovDBKey2_ptr);
        using _EQUIPKEYIsFilled4_ptr = bool (WINAPIV*)(struct _EQUIPKEY*);
        using _EQUIPKEYIsFilled4_clbk = bool (WINAPIV*)(struct _EQUIPKEY*, _EQUIPKEYIsFilled4_ptr);
        using _EQUIPKEYLoadDBKey6_ptr = void (WINAPIV*)(struct _EQUIPKEY*, int16_t);
        using _EQUIPKEYLoadDBKey6_clbk = void (WINAPIV*)(struct _EQUIPKEY*, int16_t, _EQUIPKEYLoadDBKey6_ptr);
        using _EQUIPKEYSetRelease8_ptr = void (WINAPIV*)(struct _EQUIPKEY*);
        using _EQUIPKEYSetRelease8_clbk = void (WINAPIV*)(struct _EQUIPKEY*, _EQUIPKEYSetRelease8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

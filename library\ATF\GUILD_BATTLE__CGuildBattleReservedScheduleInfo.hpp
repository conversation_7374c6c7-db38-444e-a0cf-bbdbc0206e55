// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleReservedSchedule.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            using GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int, unsigned int, struct GUILD_BATTLE::CGuildBattleSchedule**);
            using GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int, unsigned int, struct GUILD_BATTLE::CGuildBattleSchedule**, GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_ptr);
            
            using GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int, GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, int, GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleClear10_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleClear10_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleClear10_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleClear12_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleClear12_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduleClear12_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, bool*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, bool*, GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, bool, struct _worlddb_guild_battle_schedule_list*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, bool, struct _worlddb_guild_battle_schedule_list*, GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleNext32_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleNext32_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduleNext32_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_ptr = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_clbk = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_ptr);
            
            using GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*);
            using GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedSchedule*, GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

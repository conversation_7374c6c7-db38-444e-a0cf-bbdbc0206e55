// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RECTL.hpp>
#include <_TRIVERTEX.hpp>
#include <tagEMR.hpp>


START_ATF_NAMESPACE
    struct tagEMRGRADIENTFILL
    {
        tagEMR emr;
        _RECTL rclBounds;
        unsigned int nVer;
        unsigned int nTri;
        unsigned int ulMode;
        _TRIVERTEX Ver[1];
    };
END_ATF_NAMESPACE

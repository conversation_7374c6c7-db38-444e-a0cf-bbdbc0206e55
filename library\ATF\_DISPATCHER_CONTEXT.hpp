// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CONTEXT.hpp>
#include <_RUNTIME_FUNCTION.hpp>
#include <_UNWIND_HISTORY_TABLE.hpp>


START_ATF_NAMESPACE
    struct _DISPATCHER_CONTEXT
    {
        unsigned __int64 ControlPc;
        unsigned __int64 ImageBase;
        _RUNTIME_FUNCTION *FunctionEntry;
        unsigned __int64 EstablisherFrame;
        unsigned __int64 TargetIp;
        _CONTEXT *ContextRecord;
        _EXCEPTION_DISPOSITION (WINAPIV *LanguageHandler)(_EXCEPTION_RECORD *, void *, _CONTEXT *, void *);
        void *HandlerData;
        _UNWIND_HISTORY_TABLE *HistoryTable;
    };
END_ATF_NAMESPACE

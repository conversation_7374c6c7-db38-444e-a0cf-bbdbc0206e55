#include <_worlddb_start_npc_quest_complete_history.hpp>


START_ATF_NAMESPACE
    
    _worlddb_start_npc_quest_complete_history::__list::__list()
    {
        using org_ptr = void (WINAPIV*)(struct _worlddb_start_npc_quest_complete_history::__list*);
        (org_ptr(0x1401bf200L))(this);
    };
    void _worlddb_start_npc_quest_complete_history::__list::ctor___list()
    {
        using org_ptr = void (WINAPIV*)(struct _worlddb_start_npc_quest_complete_history::__list*);
        (org_ptr(0x1401bf200L))(this);
    };
END_ATF_NAMESPACE

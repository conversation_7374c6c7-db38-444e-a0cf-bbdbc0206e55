// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_attack_gen_result_zocl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _attack_force_result_zocl
    {
        char byAtterID;
        unsigned int dwAtterSerial;
        char byForceIndex;
        char byForceLv;
        __int16 zAreaPos[2];
        char by<PERSON><PERSON><PERSON><PERSON><PERSON>;
        bool bCritical;
        bool bWPActive;
        char byList<PERSON>um;
        _attack_gen_result_zocl::_dam_list DamList[32];
    public:
        _attack_force_result_zocl();
        void ctor__attack_force_result_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_attack_force_result_zocl, 335>(), "_attack_force_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateRound.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CNormalGuildBattleStateRoundStart : CNormalGuildBattleStateRound
        {
            struct CMyTimer *m_pkTimer;
        public:
            CNormalGuildBattleStateRoundStart();
            void ctor_CNormalGuildBattleStateRoundStart();
            int Enter(struct CNormalGuildBattle* pkBattle);
            int Fin(struct CNormalGuildBattle* pkBattle);
            int Loop(struct CNormalGuildBattle* pkBattle);
            ~CNormalGuildBattleStateRoundStart();
            void dtor_CNormalGuildBattleStateRoundStart();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CNormalGuildBattleStateRoundStart, 16>(), "GUILD_BATTLE::CNormalGuildBattleStateRoundStart");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _NOT_ARRANGED_AVATOR_DB
    {
        unsigned int dwSerial;
        char byLv;
        char byRaceSexCode;
        unsigned int dwDalant;
        unsigned int dwGold;
        char wszName[17];
        char szServer[33];
        char szClassCode[5];
    public:
        void Init();
        _NOT_ARRANGED_AVATOR_DB();
        void ctor__NOT_ARRANGED_AVATOR_DB();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_NOT_ARRANGED_AVATOR_DB, 69>(), "_NOT_ARRANGED_AVATOR_DB");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _map_fld : _base_fld
    {
        char m_strFileName[64];
        int m_nMapType;
        int m_nLayerNum;
        int m_nRaceVillageCode;
        int m_nMonsterSetFileNum;
        int m_nMapClass;
        int m_nRadius;
        int m_nLevelLimit;
        int m_nUpLevelLim;
        int m_nPotionLim;
        int m_nRacePvpUsable[3];
    public:
        _map_fld();
        void ctor__map_fld();
    };
END_ATF_NAMESPACE

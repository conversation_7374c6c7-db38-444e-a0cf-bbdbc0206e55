// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CNetFrameRate
    {
        unsigned int m_dwFrames;
        unsigned int m_dwFrameTime;
        unsigned int m_dwFrameCount;
    public:
        CNetFrameRate();
        void ctor_CNetFrameRate();
        void CalcFrameRate();
    };    
    static_assert(ATF::checkSize<CNetFrameRate, 12>(), "CNetFrameRate");
END_ATF_NAMESPACE

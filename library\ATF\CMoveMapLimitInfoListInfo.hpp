// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitInfoList.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMoveMapLimitInfoListctor_CMoveMapLimitInfoList2_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoList*);
        using CMoveMapLimitInfoListctor_CMoveMapLimitInfoList2_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoList*, CMoveMapLimitInfoListctor_CMoveMapLimitInfoList2_ptr);
        using CMoveMapLimitInfoListCleanUp4_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoList*);
        using CMoveMapLimitInfoListCleanUp4_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoList*, CMoveMapLimitInfoListCleanUp4_ptr);
        using CMoveMapLimitInfoListGet6_ptr = struct CMoveMapLimitInfo* (WINAPIV*)(struct CMoveMapLimitInfoList*, int, int, unsigned int);
        using CMoveMapLimitInfoListGet6_clbk = struct CMoveMapLimitInfo* (WINAPIV*)(struct CMoveMapLimitInfoList*, int, int, unsigned int, CMoveMapLimitInfoListGet6_ptr);
        using CMoveMapLimitInfoListInit8_ptr = bool (WINAPIV*)(struct CMoveMapLimitInfoList*, struct std::vector<int,std::allocator<int> >*);
        using CMoveMapLimitInfoListInit8_clbk = bool (WINAPIV*)(struct CMoveMapLimitInfoList*, struct std::vector<int,std::allocator<int> >*, CMoveMapLimitInfoListInit8_ptr);
        using CMoveMapLimitInfoListLoad10_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoList*, struct CPlayer*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoListLoad10_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoList*, struct CPlayer*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoListLoad10_ptr);
        using CMoveMapLimitInfoListLogIn12_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoList*, struct CPlayer*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoListLogIn12_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoList*, struct CPlayer*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoListLogIn12_ptr);
        using CMoveMapLimitInfoListLogOut14_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoList*, struct CPlayer*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoListLogOut14_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoList*, struct CPlayer*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoListLogOut14_ptr);
        using CMoveMapLimitInfoListLoop16_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoList*);
        using CMoveMapLimitInfoListLoop16_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoList*, CMoveMapLimitInfoListLoop16_ptr);
        using CMoveMapLimitInfoListRequest18_ptr = char (WINAPIV*)(struct CMoveMapLimitInfoList*, int, int, int, unsigned int, int, char*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoListRequest18_clbk = char (WINAPIV*)(struct CMoveMapLimitInfoList*, int, int, int, unsigned int, int, char*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoListRequest18_ptr);
        
        using CMoveMapLimitInfoListdtor_CMoveMapLimitInfoList20_ptr = void (WINAPIV*)(struct CMoveMapLimitInfoList*);
        using CMoveMapLimitInfoListdtor_CMoveMapLimitInfoList20_clbk = void (WINAPIV*)(struct CMoveMapLimitInfoList*, CMoveMapLimitInfoListdtor_CMoveMapLimitInfoList20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitRightInfoList.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMoveMapLimitRightInfoListctor_CMoveMapLimitRightInfoList2_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*);
        using CMoveMapLimitRightInfoListctor_CMoveMapLimitRightInfoList2_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, CMoveMapLimitRightInfoListctor_CMoveMapLimitRightInfoList2_ptr);
        using CMoveMapLimitRightInfoListCreateComplete4_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct CPlayer*);
        using CMoveMapLimitRightInfoListCreateComplete4_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct CPlayer*, CMoveMapLimitRightInfoListCreateComplete4_ptr);
        using CMoveMapLimitRightInfoListGet6_ptr = struct CMoveMapLimitRightInfo* (WINAPIV*)(struct CMoveMapLimitRightInfoList*, int);
        using CMoveMapLimitRightInfoListGet6_clbk = struct CMoveMapLimitRightInfo* (WINAPIV*)(struct CMoveMapLimitRightInfoList*, int, CMoveMapLimitRightInfoListGet6_ptr);
        using CMoveMapLimitRightInfoListInit8_ptr = bool (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct std::vector<int,std::allocator<int> >*);
        using CMoveMapLimitRightInfoListInit8_clbk = bool (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct std::vector<int,std::allocator<int> >*, CMoveMapLimitRightInfoListInit8_ptr);
        using CMoveMapLimitRightInfoListLoad10_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct CPlayer*);
        using CMoveMapLimitRightInfoListLoad10_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct CPlayer*, CMoveMapLimitRightInfoListLoad10_ptr);
        using CMoveMapLimitRightInfoListLogIn12_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct CPlayer*);
        using CMoveMapLimitRightInfoListLogIn12_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct CPlayer*, CMoveMapLimitRightInfoListLogIn12_ptr);
        using CMoveMapLimitRightInfoListLogOut14_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct CPlayer*);
        using CMoveMapLimitRightInfoListLogOut14_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, struct CPlayer*, CMoveMapLimitRightInfoListLogOut14_ptr);
        
        using CMoveMapLimitRightInfoListdtor_CMoveMapLimitRightInfoList16_ptr = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*);
        using CMoveMapLimitRightInfoListdtor_CMoveMapLimitRightInfoList16_clbk = void (WINAPIV*)(struct CMoveMapLimitRightInfoList*, CMoveMapLimitRightInfoListdtor_CMoveMapLimitRightInfoList16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_TOKEN_GROUPS.hpp>
#include <_TOKEN_PRIVILEGES.hpp>


START_ATF_NAMESPACE
    struct _JOBOBJECT_SECURITY_LIMIT_INFORMATION
    {
        unsigned int SecurityLimitFlags;
        void *JobToken;
        _TOKEN_GROUPS *SidsToDisable;
        _TOKEN_PRIVILEGES *PrivilegesToDelete;
        _TOKEN_GROUPS *RestrictedSids;
    };
END_ATF_NAMESPACE

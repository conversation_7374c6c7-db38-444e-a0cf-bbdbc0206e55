// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CSetItemEffect.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CSetItemEffectAttach_Set2_ptr = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int, char, char);
        using CSetItemEffectAttach_Set2_clbk = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int, char, char, CSetItemEffectAttach_Set2_ptr);
        
        using CSetItemEffectctor_CSetItemEffect4_ptr = void (WINAPIV*)(struct CSetItemEffect*);
        using CSetItemEffectctor_CSetItemEffect4_clbk = void (WINAPIV*)(struct CSetItemEffect*, CSetItemEffectctor_CSetItemEffect4_ptr);
        using CSetItemEffectCheck_Base_EquipItem6_ptr = char (WINAPIV*)(struct CSetItemEffect*, struct _AVATOR_DATA*, struct _SetItemEff_fld*);
        using CSetItemEffectCheck_Base_EquipItem6_clbk = char (WINAPIV*)(struct CSetItemEffect*, struct _AVATOR_DATA*, struct _SetItemEff_fld*, CSetItemEffectCheck_Base_EquipItem6_ptr);
        using CSetItemEffectCheck_EquipItem8_ptr = char (WINAPIV*)(struct CSetItemEffect*, struct _AVATOR_DATA*, struct _SetItemEff_fld*);
        using CSetItemEffectCheck_EquipItem8_clbk = char (WINAPIV*)(struct CSetItemEffect*, struct _AVATOR_DATA*, struct _SetItemEff_fld*, CSetItemEffectCheck_EquipItem8_ptr);
        using CSetItemEffectCheck_Other_EquipItem10_ptr = char (WINAPIV*)(struct CSetItemEffect*, struct _AVATOR_DATA*, struct _SetItemEff_fld*);
        using CSetItemEffectCheck_Other_EquipItem10_clbk = char (WINAPIV*)(struct CSetItemEffect*, struct _AVATOR_DATA*, struct _SetItemEff_fld*, CSetItemEffectCheck_Other_EquipItem10_ptr);
        using CSetItemEffectDetach_Set12_ptr = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int);
        using CSetItemEffectDetach_Set12_clbk = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int, CSetItemEffectDetach_Set12_ptr);
        using CSetItemEffectGetResetEffectNum14_ptr = char (WINAPIV*)(struct CSetItemEffect*);
        using CSetItemEffectGetResetEffectNum14_clbk = char (WINAPIV*)(struct CSetItemEffect*, CSetItemEffectGetResetEffectNum14_ptr);
        using CSetItemEffectGetResetIdx16_ptr = unsigned int (WINAPIV*)(struct CSetItemEffect*);
        using CSetItemEffectGetResetIdx16_clbk = unsigned int (WINAPIV*)(struct CSetItemEffect*, CSetItemEffectGetResetIdx16_ptr);
        using CSetItemEffectGetResetItemNum18_ptr = char (WINAPIV*)(struct CSetItemEffect*);
        using CSetItemEffectGetResetItemNum18_clbk = char (WINAPIV*)(struct CSetItemEffect*, CSetItemEffectGetResetItemNum18_ptr);
        using CSetItemEffectInit_Data20_ptr = void (WINAPIV*)(struct CSetItemEffect*, char);
        using CSetItemEffectInit_Data20_clbk = void (WINAPIV*)(struct CSetItemEffect*, char, CSetItemEffectInit_Data20_ptr);
        using CSetItemEffectInit_Info22_ptr = void (WINAPIV*)(struct CSetItemEffect*);
        using CSetItemEffectInit_Info22_clbk = void (WINAPIV*)(struct CSetItemEffect*, CSetItemEffectInit_Info22_ptr);
        using CSetItemEffectIsSetOn24_ptr = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int);
        using CSetItemEffectIsSetOn24_clbk = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int, CSetItemEffectIsSetOn24_ptr);
        using CSetItemEffectIsSetOnComplete26_ptr = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int, char, char);
        using CSetItemEffectIsSetOnComplete26_clbk = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int, char, char, CSetItemEffectIsSetOnComplete26_ptr);
        using CSetItemEffectReset_Set28_ptr = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int, char, char);
        using CSetItemEffectReset_Set28_clbk = bool (WINAPIV*)(struct CSetItemEffect*, unsigned int, char, char, CSetItemEffectReset_Set28_ptr);
        using CSetItemEffectSetOffEffect30_ptr = int (WINAPIV*)(struct CSetItemEffect*, unsigned int, char, char);
        using CSetItemEffectSetOffEffect30_clbk = int (WINAPIV*)(struct CSetItemEffect*, unsigned int, char, char, CSetItemEffectSetOffEffect30_ptr);
        using CSetItemEffectSetOnEffect32_ptr = int (WINAPIV*)(struct CSetItemEffect*, struct _AVATOR_DATA*, unsigned int, char, char);
        using CSetItemEffectSetOnEffect32_clbk = int (WINAPIV*)(struct CSetItemEffect*, struct _AVATOR_DATA*, unsigned int, char, char, CSetItemEffectSetOnEffect32_ptr);
        using CSetItemEffectSetResetInfo34_ptr = void (WINAPIV*)(struct CSetItemEffect*, bool, unsigned int, char, char);
        using CSetItemEffectSetResetInfo34_clbk = void (WINAPIV*)(struct CSetItemEffect*, bool, unsigned int, char, char, CSetItemEffectSetResetInfo34_ptr);
        
        using CSetItemEffectdtor_CSetItemEffect36_ptr = void (WINAPIV*)(struct CSetItemEffect*);
        using CSetItemEffectdtor_CSetItemEffect36_clbk = void (WINAPIV*)(struct CSetItemEffect*, CSetItemEffectdtor_CSetItemEffect36_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

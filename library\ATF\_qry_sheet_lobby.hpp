// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_AVATOR_DATA.hpp>


START_ATF_NAMESPACE
    struct _qry_sheet_lobby
    {
        unsigned int dwAvatorSerial;
        _AVATOR_DATA NewData;
        _AVATOR_DATA OldData;
        bool bUpdateRefineCnt;
        char byRefinedCnt;
        unsigned int dwRefineDate;
    public:
        _qry_sheet_lobby();
        void ctor__qry_sheet_lobby();
        int size();
        ~_qry_sheet_lobby();
        void dtor__qry_sheet_lobby();
    };    
    static_assert(ATF::checkSize<_qry_sheet_lobby, 74440>(), "_qry_sheet_lobby");
END_ATF_NAMESPACE

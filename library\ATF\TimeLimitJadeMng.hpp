// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <TimeLimitJade.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct TimeLimitJadeMng
    {
        TimeLimitJade **_ppkTimeLimitJade;
        CLogFile _kLogSys;
    public:
        bool DeleteList(uint16_t wIdx, struct _STORAGE_LIST::_db_con* pkItem);
        struct TimeLimitJade* GetSheet(uint16_t wIndex);
        bool Init();
        bool InsertList(uint16_t wIdx, struct _STORAGE_LIST::_db_con* pkItem);
        static struct TimeLimitJadeMng* Instance();
        void Release(uint16_t wIdx);
        void ReleaseAll();
        TimeLimitJadeMng();
        void ctor_TimeLimitJadeMng();
        ~TimeLimitJadeMng();
        void dtor_TimeLimitJadeMng();
    };
END_ATF_NAMESPACE

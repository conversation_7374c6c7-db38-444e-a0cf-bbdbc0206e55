// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CONTEXT.hpp>
#include <_EXCEPTION_RECORD.hpp>


START_ATF_NAMESPACE
    struct _EXCEPTION_POINTERS
    {
        _EXCEPTION_RECORD *ExceptionRecord;
        _CONTEXT *ContextRecord;
    };    
    static_assert(ATF::checkSize<_EXCEPTION_POINTERS, 16>(), "_EXCEPTION_POINTERS");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CSyncCS.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CSyncCSctor_CSyncCS2_ptr = void (WINAPIV*)(struct CSyncCS*);
        using CSyncCSctor_CSyncCS2_clbk = void (WINAPIV*)(struct CSyncCS*, CSyncCSctor_CSyncCS2_ptr);
        using CSyncCSIsUse4_ptr = bool (WINAPIV*)(struct CSyncCS*);
        using CSyncCSIsUse4_clbk = bool (WINAPIV*)(struct CSyncCS*, CSyncCSIsUse4_ptr);
        using CSyncCSLock6_ptr = void (WINAPIV*)(struct CSyncCS*);
        using CSyncCSLock6_clbk = void (WINAPIV*)(struct CSyncCS*, CSyncCSLock6_ptr);
        using CSyncCSSetUse8_ptr = void (WINAPIV*)(struct CSyncCS*, bool);
        using CSyncCSSetUse8_clbk = void (WINAPIV*)(struct CSyncCS*, bool, CSyncCSSetUse8_ptr);
        using CSyncCSUnlock10_ptr = void (WINAPIV*)(struct CSyncCS*);
        using CSyncCSUnlock10_clbk = void (WINAPIV*)(struct CSyncCS*, CSyncCSUnlock10_ptr);
        
        using CSyncCSdtor_CSyncCS15_ptr = void (WINAPIV*)(struct CSyncCS*);
        using CSyncCSdtor_CSyncCS15_clbk = void (WINAPIV*)(struct CSyncCS*, CSyncCSdtor_CSyncCS15_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

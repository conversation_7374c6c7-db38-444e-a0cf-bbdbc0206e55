// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _FORCEKEY
    {
        unsigned int dwKey;
    public:
        int CovDBKey();
        char GetIndex();
        unsigned int GetStat();
        bool IsFilled();
        void LoadDBKey(int pl_nKey);
        void SetKey(char pl_byItemIndex, unsigned int pl_dwStat);
        void SetRelease();
        void SetStat(unsigned int pl_dwStat);
    };    
    static_assert(ATF::checkSize<_FORCEKEY, 4>(), "_FORCEKEY");
END_ATF_NAMESPACE

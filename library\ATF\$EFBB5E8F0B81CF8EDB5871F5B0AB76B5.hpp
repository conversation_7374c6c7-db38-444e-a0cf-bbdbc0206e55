// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagHARDWAREINPUT.hpp>
#include <tagKEYBDINPUT.hpp>
#include <tagMOUSEINPUT.hpp>


START_ATF_NAMESPACE
    union $EFBB5E8F0B81CF8EDB5871F5B0AB76B5
    {
        tagMOUSEINPUT mi;
        tagKEYBDINPUT ki;
        tagHARDWAREINPUT hi;
    };
END_ATF_NAMESPACE

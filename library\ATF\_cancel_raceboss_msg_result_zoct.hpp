// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _cancel_raceboss_msg_result_zoct
    {
        char byRet;
        char byRaceCode;
        int nID;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_cancel_raceboss_msg_result_zoct, 6>(), "_cancel_raceboss_msg_result_zoct");
END_ATF_NAMESPACE

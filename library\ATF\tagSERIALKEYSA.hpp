// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagSERIALKEYSA
    {
        unsigned int cbSize;
        unsigned int dwFlags;
        char *lpszActivePort;
        char *lpszPort;
        unsigned int iBaudRate;
        unsigned int iPortState;
        unsigned int iActive;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

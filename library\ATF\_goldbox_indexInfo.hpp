// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_goldbox_index.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _goldbox_indexctor__goldbox_index2_ptr = void (WINAPIV*)(struct _goldbox_index*);
        using _goldbox_indexctor__goldbox_index2_clbk = void (WINAPIV*)(struct _goldbox_index*, _goldbox_indexctor__goldbox_index2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

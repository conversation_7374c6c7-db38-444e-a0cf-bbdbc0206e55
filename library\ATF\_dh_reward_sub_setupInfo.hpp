// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dh_reward_sub_setup.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _dh_reward_sub_setupctor__dh_reward_sub_setup2_ptr = void (WINAPIV*)(struct _dh_reward_sub_setup*);
        using _dh_reward_sub_setupctor__dh_reward_sub_setup2_clbk = void (WINAPIV*)(struct _dh_reward_sub_setup*, _dh_reward_sub_setupctor__dh_reward_sub_setup2_ptr);
        
        using _dh_reward_sub_setupdtor__dh_reward_sub_setup7_ptr = void (WINAPIV*)(struct _dh_reward_sub_setup*);
        using _dh_reward_sub_setupdtor__dh_reward_sub_setup7_clbk = void (WINAPIV*)(struct _dh_reward_sub_setup*, _dh_reward_sub_setupdtor__dh_reward_sub_setup7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

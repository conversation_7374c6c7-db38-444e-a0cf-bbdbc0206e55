// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Container_base.hpp>
#include <std__allocator.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 8)
        template<typename _Ty, typename _Alloc = allocator<_Ty>>
        struct _Vector_val : _Container_base
        {
            _Alloc _Alval;

            _Vector_val()
                : _Container_base((_Container_base*)this)
            {
            }
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE

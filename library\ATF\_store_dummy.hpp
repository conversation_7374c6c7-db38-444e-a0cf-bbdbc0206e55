// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    struct _store_dummy
    {
        int m_nStoreType;
        _base_fld *m_pStoreRec;
        _dummy_position *m_pDumPos;
    public:
        bool SetDummy(int nType, struct _base_fld* pRec, struct _dummy_position* pDumPos);
        _store_dummy();
        void ctor__store_dummy();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMHDR.hpp>


START_ATF_NAMESPACE
    struct tagNMREBAR
    {
        tagNMHDR hdr;
        unsigned int dwMask;
        unsigned int uBand;
        unsigned int fStyle;
        unsigned int wID;
        __int64 lParam;
    };
END_ATF_NAMESPACE

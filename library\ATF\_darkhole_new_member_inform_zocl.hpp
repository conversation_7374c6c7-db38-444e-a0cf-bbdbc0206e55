// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _darkhole_new_member_inform_zocl
    {
        unsigned int dwNewMemberSerial;
        char wszNewMemberName[17];
        bool bReconnect;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_darkhole_new_member_inform_zocl, 22>(), "_darkhole_new_member_inform_zocl");
END_ATF_NAMESPACE

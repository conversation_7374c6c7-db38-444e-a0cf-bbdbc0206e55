// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$4B3AC5E4704437C445220E2D10C00C40.hpp>


START_ATF_NAMESPACE
    struct _RPC_ASYNC_STATE
    {
        unsigned int Size;
        unsigned int Signature;
        int Lock;
        unsigned int Flags;
        void *StubInfo;
        void *UserInfo;
        void *RuntimeInfo;
        _RPC_ASYNC_EVENT Event;
        _RPC_NOTIFICATION_TYPES NotificationType;
        $4B3AC5E4704437C445220E2D10C00C40 u;
        __int64 Reserved[4];
    };
END_ATF_NAMESPACE

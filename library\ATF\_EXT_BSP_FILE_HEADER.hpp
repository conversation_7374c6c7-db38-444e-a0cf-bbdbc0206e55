// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ENTRY.hpp>


START_ATF_NAMESPACE
    struct _EXT_BSP_FILE_HEADER
    {
        unsigned int version;
        _ENTRY CFVertex;
        _ENTRY CFLine;
        _ENTRY CFLineId;
        _ENTRY CFLeaf;
        _ENTRY EntityList;
        _ENTRY EntityID;
        _ENTRY LeafEntityList;
        _ENTRY SoundEntityID;
        _ENTRY LeafSoundEntityList;
        _ENTRY ReadSpare[18];
        _ENTRY MapEntitiesList;
        _ENTRY SoundEntityList;
        _ENTRY SoundEntitiesList;
        _ENTRY FreeSpare[18];
    };
END_ATF_NAMESPACE

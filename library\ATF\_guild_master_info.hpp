// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _guild_master_info
    {
        unsigned int dwSerial;
        char byPrevGrade;
        struct _guild_member_info *pMember;
    public:
        bool IsFill();
        _guild_master_info();
        void ctor__guild_master_info();
        void init();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $42A3CB4E031CC824EFC3D0F4D1894CCF
    {
        unsigned __int32 nNumofTime : 4;
        unsigned __int32 nHour : 5;
        unsigned __int32 nDay : 5;
        unsigned __int32 nMonth : 4;
        unsigned __int32 nYear : 14;
    };    
    static_assert(ATF::checkSize<$42A3CB4E031CC824EFC3D0F4D1894CCF, 4>(), "$42A3CB4E031CC824EFC3D0F4D1894CCF");
END_ATF_NAMESPACE

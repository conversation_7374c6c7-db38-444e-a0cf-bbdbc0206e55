// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_character_rename.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_character_renamector__qry_case_character_rename2_ptr = void (WINAPIV*)(struct _qry_case_character_rename*);
        using _qry_case_character_renamector__qry_case_character_rename2_clbk = void (WINAPIV*)(struct _qry_case_character_rename*, _qry_case_character_renamector__qry_case_character_rename2_ptr);
        using _qry_case_character_renamesize4_ptr = int (WINAPIV*)(struct _qry_case_character_rename*);
        using _qry_case_character_renamesize4_clbk = int (WINAPIV*)(struct _qry_case_character_rename*, _qry_case_character_renamesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

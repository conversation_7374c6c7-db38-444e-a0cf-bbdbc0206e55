// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CObjectVtbl.hpp>


START_ATF_NAMESPACE
    struct CObject
    {
        CObjectVtbl *vfptr;
    public:
        int64_t IsKindOf(struct CRuntimeClass* arg_0);
        void Serialize(struct CArchive* arg_0);
    };    
    static_assert(ATF::checkSize<CObject, 8>(), "CObject");
END_ATF_NAMESPACE

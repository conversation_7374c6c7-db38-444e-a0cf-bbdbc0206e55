// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_mon_active.hpp>


START_ATF_NAMESPACE
    struct _LAYER_SET
    {
        int m_nSecNum;
        struct CObjectList *m_pListSectorObj;
        struct CObjectList *m_pListSectorPlayer;
        struct CObjectList *m_pListSectorTower;
        struct _MULTI_BLOCK *m_pMB;
        _mon_active m_MonAct[200][300];
        unsigned int m_dwStartActiveTime;
        unsigned int m_dwLastInertTime;
    public:
        void ActiveLayer(struct _MULTI_BLOCK* pMB);
        void CreateLayer(int nSecNum);
        bool InertLayer();
        bool IsActiveLayer();
        _LAYER_SET();
        void ctor__LAYER_SET();
        ~_LAYER_SET();
        void dtor__LAYER_SET();
    };
END_ATF_NAMESPACE

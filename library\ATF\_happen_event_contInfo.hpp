// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_happen_event_cont.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _happen_event_contctor__happen_event_cont2_ptr = void (WINAPIV*)(struct _happen_event_cont*);
        using _happen_event_contctor__happen_event_cont2_clbk = void (WINAPIV*)(struct _happen_event_cont*, _happen_event_contctor__happen_event_cont2_ptr);
        using _happen_event_continit4_ptr = void (WINAPIV*)(struct _happen_event_cont*);
        using _happen_event_continit4_clbk = void (WINAPIV*)(struct _happen_event_cont*, _happen_event_continit4_ptr);
        using _happen_event_contisset6_ptr = bool (WINAPIV*)(struct _happen_event_cont*);
        using _happen_event_contisset6_clbk = bool (WINAPIV*)(struct _happen_event_cont*, _happen_event_contisset6_ptr);
        using _happen_event_contset8_ptr = void (WINAPIV*)(struct _happen_event_cont*, struct _happen_event_node*, QUEST_HAPPEN, int, int);
        using _happen_event_contset8_clbk = void (WINAPIV*)(struct _happen_event_cont*, struct _happen_event_node*, QUEST_HAPPEN, int, int, _happen_event_contset8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

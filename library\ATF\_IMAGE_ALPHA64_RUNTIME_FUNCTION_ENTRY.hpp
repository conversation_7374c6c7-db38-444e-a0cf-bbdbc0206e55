// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_ALPHA64_RUNTIME_FUNCTION_ENTRY
    {
        unsigned __int64 BeginAddress;
        unsigned __int64 EndAddress;
        unsigned __int64 ExceptionHandler;
        unsigned __int64 HandlerData;
        unsigned __int64 PrologEndAddress;
    };
END_ATF_NAMESPACE

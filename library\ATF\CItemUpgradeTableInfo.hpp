// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CItemUpgradeTable.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CItemUpgradeTablector_CItemUpgradeTable2_ptr = void (WINAPIV*)(struct CItemUpgradeTable*);
        using CItemUpgradeTablector_CItemUpgradeTable2_clbk = void (WINAPIV*)(struct CItemUpgradeTable*, CItemUpgradeTablector_CItemUpgradeTable2_ptr);
        using CItemUpgradeTableGetRecord4_ptr = struct _ItemUpgrade_fld* (WINAPIV*)(struct CItemUpgradeTable*, unsigned int);
        using CItemUpgradeTableGetRecord4_clbk = struct _ItemUpgrade_fld* (WINAPIV*)(struct CItemUpgradeTable*, unsigned int, CItemUpgradeTableGetRecord4_ptr);
        using CItemUpgradeTableGetRecordFromRes6_ptr = struct _ItemUpgrade_fld* (WINAPIV*)(struct CItemUpgradeTable*, unsigned int);
        using CItemUpgradeTableGetRecordFromRes6_clbk = struct _ItemUpgrade_fld* (WINAPIV*)(struct CItemUpgradeTable*, unsigned int, CItemUpgradeTableGetRecordFromRes6_ptr);
        using CItemUpgradeTableGetSize8_ptr = int (WINAPIV*)(struct CItemUpgradeTable*);
        using CItemUpgradeTableGetSize8_clbk = int (WINAPIV*)(struct CItemUpgradeTable*, CItemUpgradeTableGetSize8_ptr);
        using CItemUpgradeTableIndexing10_ptr = bool (WINAPIV*)(struct CItemUpgradeTable*, struct CRecordData*, char*);
        using CItemUpgradeTableIndexing10_clbk = bool (WINAPIV*)(struct CItemUpgradeTable*, struct CRecordData*, char*, CItemUpgradeTableIndexing10_ptr);
        using CItemUpgradeTableReadRecord12_ptr = bool (WINAPIV*)(struct CItemUpgradeTable*, char*, struct CRecordData*, char*);
        using CItemUpgradeTableReadRecord12_clbk = bool (WINAPIV*)(struct CItemUpgradeTable*, char*, struct CRecordData*, char*, CItemUpgradeTableReadRecord12_ptr);
        
        using CItemUpgradeTabledtor_CItemUpgradeTable17_ptr = void (WINAPIV*)(struct CItemUpgradeTable*);
        using CItemUpgradeTabledtor_CItemUpgradeTable17_clbk = void (WINAPIV*)(struct CItemUpgradeTable*, CItemUpgradeTabledtor_CItemUpgradeTable17_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

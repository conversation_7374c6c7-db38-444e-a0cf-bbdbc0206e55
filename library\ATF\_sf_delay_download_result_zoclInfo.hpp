// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_sf_delay_download_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _sf_delay_download_result_zoclctor__sf_delay_download_result_zocl2_ptr = void (WINAPIV*)(struct _sf_delay_download_result_zocl*);
        using _sf_delay_download_result_zoclctor__sf_delay_download_result_zocl2_clbk = void (WINAPIV*)(struct _sf_delay_download_result_zocl*, _sf_delay_download_result_zoclctor__sf_delay_download_result_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace _CSTRING_IMPL_
        {
            template<>
            struct  _MFCDLLTraitsCheck<wchar_t,StrTraitMFC_DLL<wchar_t,ATL::ChTraitsCRT<wchar_t> > >
            {
            };
        }; // end namespace _CSTRING_IMPL_
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace _CSTRING_IMPL_
        {
            template<>
            struct  _MFCDLLTraitsCheck<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char> > >
            {
            };
        }; // end namespace _CSTRING_IMPL_
    }; // end namespace ATL
END_ATF_NAMESPACE

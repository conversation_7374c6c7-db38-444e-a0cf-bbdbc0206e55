// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryTW.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryTWctor_CNationSettingFactoryTW2_ptr = void (WINAPIV*)(struct CNationSettingFactoryTW*);
        using CNationSettingFactoryTWctor_CNationSettingFactoryTW2_clbk = void (WINAPIV*)(struct CNationSettingFactoryTW*, CNationSettingFactoryTWctor_CNationSettingFactoryTW2_ptr);
        using CNationSettingFactoryTWCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryTW*, int, char*, bool);
        using CNationSettingFactoryTWCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryTW*, int, char*, bool, CNationSettingFactoryTWCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

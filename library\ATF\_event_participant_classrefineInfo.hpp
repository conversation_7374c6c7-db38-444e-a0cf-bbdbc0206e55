// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_event_participant_classrefine.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _event_participant_classrefineIsChanged2_ptr = bool (WINAPIV*)(struct _event_participant_classrefine*);
        using _event_participant_classrefineIsChanged2_clbk = bool (WINAPIV*)(struct _event_participant_classrefine*, _event_participant_classrefineIsChanged2_ptr);
        using _event_participant_classrefinesize4_ptr = int (WINAPIV*)(struct _event_participant_classrefine*);
        using _event_participant_classrefinesize4_clbk = int (WINAPIV*)(struct _event_participant_classrefine*, _event_participant_classrefinesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

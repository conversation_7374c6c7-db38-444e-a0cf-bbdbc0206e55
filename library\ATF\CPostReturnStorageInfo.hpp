// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPostReturnStorage.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPostReturnStorageAddReturnPost2_ptr = struct CPostData* (WINAPIV*)(struct CPostReturnStorage*, char, unsigned int, char, char*, char*, char*, struct _INVENKEY, uint64_t, unsigned int, unsigned int, uint64_t);
        using CPostReturnStorageAddReturnPost2_clbk = struct CPostData* (WINAPIV*)(struct CPostReturnStorage*, char, unsigned int, char, char*, char*, char*, struct _INVENKEY, uint64_t, unsigned int, unsigned int, uint64_t, CPostReturnStorageAddReturnPost2_ptr);
        
        using CPostReturnStoragector_CPostReturnStorage4_ptr = void (WINAPIV*)(struct CPostReturnStorage*);
        using CPostReturnStoragector_CPostReturnStorage4_clbk = void (WINAPIV*)(struct CPostReturnStorage*, CPostReturnStoragector_CPostReturnStorage4_ptr);
        using CPostReturnStorageDelPostData6_ptr = void (WINAPIV*)(struct CPostReturnStorage*, unsigned int);
        using CPostReturnStorageDelPostData6_clbk = void (WINAPIV*)(struct CPostReturnStorage*, unsigned int, CPostReturnStorageDelPostData6_ptr);
        using CPostReturnStorageGetPostDataFromInx8_ptr = struct CPostData* (WINAPIV*)(struct CPostReturnStorage*, int);
        using CPostReturnStorageGetPostDataFromInx8_clbk = struct CPostData* (WINAPIV*)(struct CPostReturnStorage*, int, CPostReturnStorageGetPostDataFromInx8_ptr);
        using CPostReturnStorageGetPostDataFromSerial10_ptr = struct CPostData* (WINAPIV*)(struct CPostReturnStorage*, unsigned int);
        using CPostReturnStorageGetPostDataFromSerial10_clbk = struct CPostData* (WINAPIV*)(struct CPostReturnStorage*, unsigned int, CPostReturnStorageGetPostDataFromSerial10_ptr);
        using CPostReturnStorageGetReturnPostInx12_ptr = int (WINAPIV*)(struct CPostReturnStorage*);
        using CPostReturnStorageGetReturnPostInx12_clbk = int (WINAPIV*)(struct CPostReturnStorage*, CPostReturnStorageGetReturnPostInx12_ptr);
        using CPostReturnStorageGetSize14_ptr = int (WINAPIV*)(struct CPostReturnStorage*);
        using CPostReturnStorageGetSize14_clbk = int (WINAPIV*)(struct CPostReturnStorage*, CPostReturnStorageGetSize14_ptr);
        using CPostReturnStorageInit16_ptr = void (WINAPIV*)(struct CPostReturnStorage*);
        using CPostReturnStorageInit16_clbk = void (WINAPIV*)(struct CPostReturnStorage*, CPostReturnStorageInit16_ptr);
        
        using CPostReturnStoragedtor_CPostReturnStorage18_ptr = void (WINAPIV*)(struct CPostReturnStorage*);
        using CPostReturnStoragedtor_CPostReturnStorage18_clbk = void (WINAPIV*)(struct CPostReturnStorage*, CPostReturnStoragedtor_CPostReturnStorage18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

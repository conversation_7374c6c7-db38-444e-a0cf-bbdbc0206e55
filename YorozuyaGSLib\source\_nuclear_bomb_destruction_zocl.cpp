#include <_nuclear_bomb_destruction_zocl.hpp>


START_ATF_NAMESPACE
    _nuclear_bomb_destruction_zocl::_nuclear_bomb_destruction_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_bomb_destruction_zocl*);
        (org_ptr(0x14013e740L))(this);
    };
    void _nuclear_bomb_destruction_zocl::ctor__nuclear_bomb_destruction_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_bomb_destruction_zocl*);
        (org_ptr(0x14013e740L))(this);
    };
    int _nuclear_bomb_destruction_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _nuclear_bomb_destruction_zocl*);
        return (org_ptr(0x14013e790L))(this);
    };
END_ATF_NAMESPACE

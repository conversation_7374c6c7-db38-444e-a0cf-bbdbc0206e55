// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$4455AE054AB94A990614B59A9A2BD082.hpp>
#include <$AA4EA065BA97826FBC11F5FCBE2FD265.hpp>


START_ATF_NAMESPACE
    struct tagDBID
    {
        $AA4EA065BA97826FBC11F5FCBE2FD265 uGuid;
        unsigned int eKind;
        $4455AE054AB94A990614B59A9A2BD082 uName;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <Cmd.hpp>
#include <ElectProcessor.hpp>


START_ATF_NAMESPACE
    struct  FinalDecisionApplyer : ElectProcessor
    {
    public:
        int Doit(Cmd eCmd, struct CPlayer* pOne, char* pdata);
        FinalDecisionApplyer();
        void ctor_FinalDecisionApplyer();
        bool Initialize();
        void _FinalDecisionApply();
        ~FinalDecisionApplyer();
        void dtor_FinalDecisionApplyer();
    };
END_ATF_NAMESPACE

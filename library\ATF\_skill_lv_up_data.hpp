// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct _skill_lv_up_data
    {
        bool bUpdate;
        unsigned __int16 wIndex;
        char byLv;
    public:
        void init();
        void set(uint16_t index, char lv);
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_skill_lv_up_data, 6>(), "_skill_lv_up_data");
END_ATF_NAMESPACE

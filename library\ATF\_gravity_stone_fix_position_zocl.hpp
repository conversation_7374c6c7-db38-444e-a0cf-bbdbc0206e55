// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _gravity_stone_fix_position_zocl
    {
        unsigned __int16 wObjIndex;
        unsigned __int16 wRecIndex;
        unsigned int dwObjSerial;
        __int16 zPos[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

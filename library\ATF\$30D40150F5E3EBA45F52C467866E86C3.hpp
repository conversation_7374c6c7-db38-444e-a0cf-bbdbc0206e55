// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GOPHER_ABSTRACT_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_ADMIN_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_ASK_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_GEOGRAPHICAL_LOCATION_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_LOCATION_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_MOD_DATE_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_ORGANIZATION_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_PROVIDER_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_SCORE_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_SCORE_RANGE_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_SITE_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_TIMEZONE_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_TTL_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_UNKNOWN_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_VERONICA_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_VERSION_ATTRIBUTE_TYPE.hpp>
#include <GOPHER_VIEW_ATTRIBUTE_TYPE.hpp>


START_ATF_NAMESPACE
    union $30D40150F5E3EBA45F52C467866E86C3
    {
        GOPHER_ADMIN_ATTRIBUTE_TYPE Admin;
        GOPHER_MOD_DATE_ATTRIBUTE_TYPE ModDate;
        GOPHER_TTL_ATTRIBUTE_TYPE Ttl;
        GOPHER_SCORE_ATTRIBUTE_TYPE Score;
        GOPHER_SCORE_RANGE_ATTRIBUTE_TYPE ScoreRange;
        GOPHER_SITE_ATTRIBUTE_TYPE Site;
        GOPHER_ORGANIZATION_ATTRIBUTE_TYPE Organization;
        GOPHER_LOCATION_ATTRIBUTE_TYPE Location;
        GOPHER_GEOGRAPHICAL_LOCATION_ATTRIBUTE_TYPE GeographicalLocation;
        GOPHER_TIMEZONE_ATTRIBUTE_TYPE TimeZone;
        GOPHER_PROVIDER_ATTRIBUTE_TYPE Provider;
        GOPHER_VERSION_ATTRIBUTE_TYPE Version;
        GOPHER_ABSTRACT_ATTRIBUTE_TYPE Abstract;
        GOPHER_VIEW_ATTRIBUTE_TYPE View;
        GOPHER_VERONICA_ATTRIBUTE_TYPE Veronica;
        GOPHER_ASK_ATTRIBUTE_TYPE Ask;
        GOPHER_UNKNOWN_ATTRIBUTE_TYPE Unknown;
    };
END_ATF_NAMESPACE

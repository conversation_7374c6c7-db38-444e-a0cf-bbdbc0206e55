// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <INI_Key.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using INI_Keyctor_INI_Key2_ptr = void (WINAPIV*)(struct INI_Key*);
        using INI_Keyctor_INI_Key2_clbk = void (WINAPIV*)(struct INI_Key*, INI_Keyctor_INI_Key2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

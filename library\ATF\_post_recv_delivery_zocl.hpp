// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _post_recv_delivery_zocl
    {
        char byIndex;
        unsigned int dwPostSerial;
        char wszSendName[17];
        char wszTitle[21];
        bool bItem;
        bool bGold;
        char byState;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

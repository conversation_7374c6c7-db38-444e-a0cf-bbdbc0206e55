// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleState.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CNormalGuildBattleStateNotify : CNormalGuildBattleState
        {
        public:
            CNormalGuildBattleStateNotify();
            void ctor_CNormalGuildBattleStateNotify();
            int Enter(struct CNormalGuildBattle* pkBattle);
            struct ATL::CTimeSpan* GetTerm(struct ATL::CTimeSpan* result);
            ~CNormalGuildBattleStateNotify();
            void dtor_CNormalGuildBattleStateNotify();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CNormalGuildBattleStateNotify, 8>(), "GUILD_BATTLE::CNormalGuildBattleStateNotify");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

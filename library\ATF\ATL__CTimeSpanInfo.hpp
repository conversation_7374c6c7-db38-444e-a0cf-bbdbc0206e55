// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTimeSpan.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            
            using ATL__CTimeSpanctor_CTimeSpan1_ptr = void (WINAPIV*)(struct ATL::CTimeSpan*, int64_t);
            using ATL__CTimeSpanctor_CTimeSpan1_clbk = void (WINAPIV*)(struct ATL::CTimeSpan*, int64_t, ATL__CTimeSpanctor_CTimeSpan1_ptr);
            
            using ATL__CTimeSpanctor_CTimeSpan2_ptr = void (WINAPIV*)(struct ATL::CTimeSpan*, int, int, int, int);
            using ATL__CTimeSpanctor_CTimeSpan2_clbk = void (WINAPIV*)(struct ATL::CTimeSpan*, int, int, int, int, ATL__CTimeSpanctor_CTimeSpan2_ptr);
            
            using ATL__CTimeSpanctor_CTimeSpan3_ptr = void (WINAPIV*)(struct ATL::CTimeSpan*);
            using ATL__CTimeSpanctor_CTimeSpan3_clbk = void (WINAPIV*)(struct ATL::CTimeSpan*, ATL__CTimeSpanctor_CTimeSpan3_ptr);
            using ATL__CTimeSpanGetDays4_ptr = int64_t (WINAPIV*)(struct ATL::CTimeSpan*);
            using ATL__CTimeSpanGetDays4_clbk = int64_t (WINAPIV*)(struct ATL::CTimeSpan*, ATL__CTimeSpanGetDays4_ptr);
            using ATL__CTimeSpanGetHours5_ptr = int (WINAPIV*)(struct ATL::CTimeSpan*);
            using ATL__CTimeSpanGetHours5_clbk = int (WINAPIV*)(struct ATL::CTimeSpan*, ATL__CTimeSpanGetHours5_ptr);
            using ATL__CTimeSpanGetMinutes6_ptr = int (WINAPIV*)(struct ATL::CTimeSpan*);
            using ATL__CTimeSpanGetMinutes6_clbk = int (WINAPIV*)(struct ATL::CTimeSpan*, ATL__CTimeSpanGetMinutes6_ptr);
            using ATL__CTimeSpanGetSeconds7_ptr = int (WINAPIV*)(struct ATL::CTimeSpan*);
            using ATL__CTimeSpanGetSeconds7_clbk = int (WINAPIV*)(struct ATL::CTimeSpan*, ATL__CTimeSpanGetSeconds7_ptr);
            using ATL__CTimeSpanGetTimeSpan8_ptr = int64_t (WINAPIV*)(struct ATL::CTimeSpan*);
            using ATL__CTimeSpanGetTimeSpan8_clbk = int64_t (WINAPIV*)(struct ATL::CTimeSpan*, ATL__CTimeSpanGetTimeSpan8_ptr);
            using ATL__CTimeSpanGetTotalHours9_ptr = int64_t (WINAPIV*)(struct ATL::CTimeSpan*);
            using ATL__CTimeSpanGetTotalHours9_clbk = int64_t (WINAPIV*)(struct ATL::CTimeSpan*, ATL__CTimeSpanGetTotalHours9_ptr);
            using ATL__CTimeSpanGetTotalMinutes10_ptr = int64_t (WINAPIV*)(struct ATL::CTimeSpan*);
            using ATL__CTimeSpanGetTotalMinutes10_clbk = int64_t (WINAPIV*)(struct ATL::CTimeSpan*, ATL__CTimeSpanGetTotalMinutes10_ptr);
            using ATL__CTimeSpanGetTotalSeconds11_ptr = int64_t (WINAPIV*)(struct ATL::CTimeSpan*);
            using ATL__CTimeSpanGetTotalSeconds11_clbk = int64_t (WINAPIV*)(struct ATL::CTimeSpan*, ATL__CTimeSpanGetTotalSeconds11_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

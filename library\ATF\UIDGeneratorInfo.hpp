// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <UIDGenerator.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using UIDGeneratorgetuid2_ptr = uint64_t (WINAPIV*)(char);
        using UIDGeneratorgetuid2_clbk = uint64_t (WINAPIV*)(char, UIDGeneratorgetuid2_ptr);
        using UIDGeneratorgetuid4_ptr = uint64_t (WINAPIV*)(char, char);
        using UIDGeneratorgetuid4_clbk = uint64_t (WINAPIV*)(char, char, UIDGeneratorgetuid4_ptr);
        using UIDGeneratortmuid6_ptr = void (WINAPIV*)(uint64_t, char*);
        using UIDGeneratortmuid6_clbk = void (WINAPIV*)(uint64_t, char*, UIDGeneratortmuid6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CReservedGuildScheduleDayGroup.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleReservedScheduleListManager
        {
            unsigned int m_uiMapCnt;
            CReservedGuildScheduleDayGroup m_kList[2];
            CReservedGuildScheduleDayGroup *m_pkToday;
            CReservedGuildScheduleDayGroup *m_pkTomorrow;
        public:
            CGuildBattleReservedScheduleListManager();
            void ctor_CGuildBattleReservedScheduleListManager();
            void Clear();
            static void Destroy();
            void Flip();
            bool Init();
            static struct CGuildBattleReservedScheduleListManager* Instance();
            bool Load(int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTomorrow);
            bool LoadTodaySchedule();
            bool LoadTomorrowSchedule();
            void PushDQS(unsigned int dwMapID, unsigned int dwSLID);
            void Send(unsigned int uiMapID, int n, unsigned int dwVer, char byDay, char byPage, unsigned int dwGuildSerial);
            bool UpdateReservedShedule(unsigned int dwSLID, char* byOutData);
            bool UpdateTodaySchedule(unsigned int uiMapID);
            void UpdateTomorrowComplete(unsigned int dwMapID, char* pLoadData);
            bool UpdateTomorrowSchedule(unsigned int uiMapID);
            ~CGuildBattleReservedScheduleListManager();
            void dtor_CGuildBattleReservedScheduleListManager();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

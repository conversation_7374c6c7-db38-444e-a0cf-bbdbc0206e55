// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct ATLTRACEPROCESSSETTINGS
        {
            unsigned int nLevel;
            int bEnabled;
            int bFuncAndCategoryNames;
            int bFileNameAndLineNo;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

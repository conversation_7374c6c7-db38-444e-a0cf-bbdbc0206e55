// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CFrameRate.hpp>
#include <CMyTimer.hpp>
#include <CNetIndexList.hpp>
#include <CPartyPlayer.hpp>
#include <_MASTERY_PARAM.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMgrAvatorLvHistory
    {
        struct __LOG_DATA
        {
            char szFileName[64];
            int nLen;
        };
        struct  __LOG_DATA_2K : __LOG_DATA
        {
            char sData[2000];
        };
        struct  __LOG_DATA_1K : __LOG_DATA
        {
            char sData[1000];
        };
        struct  __LOG_DATA_200 : __LOG_DATA
        {
            char sData[200];
        };
        char m_szStdPath[128];
        unsigned int m_dwLastLocalDate;
        unsigned int m_dwLastLocalHour;
        CMyTimer m_tmrUpdateTime;
        char m_szCurDate[32];
        char m_szCurTime[32];
        __LOG_DATA_2K m_LogData_2K[254];
        CNetIndexList m_listLogData_2K;
        CNetIndexList m_listLogDataEmpty_2K;
        __LOG_DATA_1K m_LogData_1K[254];
        CNetIndexList m_listLogData_1K;
        CNetIndexList m_listLogDataEmpty_1K;
        __LOG_DATA_200 m_LogData_200[2532];
        CNetIndexList m_listLogData_200;
        CNetIndexList m_listLogDataEmpty_200;
        bool m_bIOThread;
        CFrameRate m_FrameRate;
    public:
        CMgrAvatorLvHistory();
        void ctor_CMgrAvatorLvHistory();
        void GetNewFileName(unsigned int dwAvatorSerial, char* pszFileName);
        int GetTotalWaitSize();
        static void IOThread(void* pv);
        void OnLoop();
        void WriteFile(char* pszFileName, char* pszLog);
        void adjust_pvpcash(bool bAdjust, long double dPvpCash, long double dPvpTempCash, char* pszFileName);
        void alter_pvp(int n, long double dPvpVariation, struct CPartyPlayer* pParty, char* pszFileName);
        void char_copy(int n, char* pszDstName, unsigned int dwDstSerial, char* pszFileName);
        void close(int n, char* pCloseCode, char* pszFileName);
        void die(int n, char* pszDstName, char* pszDeathName, char* pszFileName);
        void down_animus_exp(uint64_t dw64OldExp, uint64_t dw64NewExp, int64_t i64Alter, char* pszFileName);
        void down_exp(int n, long double dOldExp, uint16_t wOldExpRate, long double dNewExp, uint16_t wNewExpRate, char* pCause, char* pszFileName);
        void downgrade_lv(int n, unsigned int dwLv, int nGrade, int* pnMaxPoint, char* pszFileName);
        void recovery_exp(int n, long double dOldExp, uint16_t wOldExpRate, long double dNewExp, uint16_t wNewExpRate, long double dLossExp, int nProbPro, char* pCause, char* pszFileName);
        void start_mastery(int n, char* pszAvatorName, unsigned int dwLv, long double dExp, unsigned int dwExpRate, int nGrade, int* pnMaxPoint, struct _MASTERY_PARAM* pData, char* pszFileName);
        void update_mastery(int n, char byUserDgr, unsigned int dwLv, long double dExp, unsigned int dwExpRate, int nGrade, int* pnMaxPoint, struct _MASTERY_PARAM* pData, unsigned int* pdwAlter, char* pszFileName, char byLogType, char* pszTitle);
        void upgrade_lv(int n, unsigned int dwLv, int nGrade, int* pnMaxPoint, char* pszFileName);
        ~CMgrAvatorLvHistory();
        void dtor_CMgrAvatorLvHistory();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CMgrAvatorLvHistory, 1476344>(), "CMgrAvatorLvHistory");
END_ATF_NAMESPACE

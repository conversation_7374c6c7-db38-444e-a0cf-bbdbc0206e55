// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_AVATOR_DATA.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _AVATOR_DATAInitData2_ptr = void (WINAPIV*)(struct _AVATOR_DATA*);
        using _AVATOR_DATAInitData2_clbk = void (WINAPIV*)(struct _AVATOR_DATA*, _AVATOR_DATAInitData2_ptr);
        using _AVATOR_DATAPostUpdateInit4_ptr = void (WINAPIV*)(struct _AVATOR_DATA*);
        using _AVATOR_DATAPostUpdateInit4_clbk = void (WINAPIV*)(struct _AVATOR_DATA*, _AVATOR_DATAPostUpdateInit4_ptr);
        
        using _AVATOR_DATActor__AVATOR_DATA6_ptr = void (WINAPIV*)(struct _AVATOR_DATA*);
        using _AVATOR_DATActor__AVATOR_DATA6_clbk = void (WINAPIV*)(struct _AVATOR_DATA*, _AVATOR_DATActor__AVATOR_DATA6_ptr);
        
        using _AVATOR_DATAdtor__AVATOR_DATA8_ptr = void (WINAPIV*)(struct _AVATOR_DATA*);
        using _AVATOR_DATAdtor__AVATOR_DATA8_clbk = void (WINAPIV*)(struct _AVATOR_DATA*, _AVATOR_DATAdtor__AVATOR_DATA8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

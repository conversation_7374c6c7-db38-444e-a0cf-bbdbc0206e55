// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagSAFEARRAYBOUND.hpp>
#include <tagTYPEDESC.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagARRAYDESC
    {
        tagTYPEDESC tdescElem;
        unsigned __int16 cDims;
        tagSAFEARRAYBOUND rgbounds[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

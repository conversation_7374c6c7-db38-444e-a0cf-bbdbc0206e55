// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagNEWTEXTMETRICA
    {
        int tmHeight;
        int tmAscent;
        int tmDescent;
        int tmInternalLeading;
        int tmExternalLeading;
        int tmAveCharWidth;
        int tmMaxCharWidth;
        int tmWeight;
        int tmOverhang;
        int tmDigitizedAspectX;
        int tmDigitizedAspectY;
        char tmFirstChar;
        char tmLastChar;
        char tmDefaultChar;
        char tmBreakChar;
        char tmItalic;
        char tmUnderlined;
        char tmStruckOut;
        char tmPitchAndFamily;
        char tmCharSet;
        unsigned int ntmFlags;
        unsigned int ntmSizeEM;
        unsigned int ntmCellHeight;
        unsigned int ntmAvgWidth;
    };
END_ATF_NAMESPACE

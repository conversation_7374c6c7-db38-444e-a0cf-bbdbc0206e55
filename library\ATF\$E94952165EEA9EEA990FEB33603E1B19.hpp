// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $E94952165EEA9EEA990FEB33603E1B19
    {
        BYTE gap0[8];
        long double date;
    };    
    static_assert(ATF::checkSize<$E94952165EEA9EEA990FEB33603E1B19, 16>(), "$E94952165EEA9EEA990FEB33603E1B19");
END_ATF_NAMESPACE

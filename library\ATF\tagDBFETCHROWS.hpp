// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagDBFETCHROWS
    {
        unsigned __int64 cRowsRequested;
        unsigned int dwFlags;
        void *pData;
        void *pVarData;
        unsigned __int64 cbVarData;
        unsigned __int64 cRowsReturned;
    };
END_ATF_NAMESPACE

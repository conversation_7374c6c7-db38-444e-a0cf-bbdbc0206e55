// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCheckSum.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCheckSumctor_CCheckSum2_ptr = void (WINAPIV*)(struct CCheckSum*);
        using CCheckSumctor_CCheckSum2_clbk = void (WINAPIV*)(struct CCheckSum*, CCheckSumctor_CCheckSum2_ptr);
        using CCheckSumDecodeValue4_ptr = unsigned int (WINAPIV*)(struct CCheckSum*, char, unsigned int, unsigned int);
        using CCheckSumDecodeValue4_clbk = unsigned int (WINAPIV*)(struct CCheckSum*, char, unsigned int, unsigned int, CCheckSumDecodeValue4_ptr);
        using CCheckSumEncodeValue6_ptr = unsigned int (WINAPIV*)(struct CCheckSum*, char, unsigned int, unsigned int);
        using CCheckSumEncodeValue6_clbk = unsigned int (WINAPIV*)(struct CCheckSum*, char, unsigned int, unsigned int, CCheckSumEncodeValue6_ptr);
        using CCheckSumInit8_ptr = bool (WINAPIV*)(struct CCheckSum*);
        using CCheckSumInit8_clbk = bool (WINAPIV*)(struct CCheckSum*, CCheckSumInit8_ptr);
        
        using CCheckSumdtor_CCheckSum10_ptr = void (WINAPIV*)(struct CCheckSum*);
        using CCheckSumdtor_CCheckSum10_clbk = void (WINAPIV*)(struct CCheckSum*, CCheckSumdtor_CCheckSum10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

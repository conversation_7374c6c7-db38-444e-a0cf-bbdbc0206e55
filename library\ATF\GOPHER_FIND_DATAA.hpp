// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FILETIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct GOPHER_FIND_DATAA
    {
        char DisplayString[129];
        unsigned int GopherType;
        unsigned int SizeLow;
        unsigned int SizeHigh;
        _FILETIME LastModificationTime;
        char Locator[654];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaEventMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CLuaEventMgrAttachEvent2_ptr = bool (WINAPIV*)(struct CLuaEventMgr*, struct CLuaEventNode*);
        using CLuaEventMgrAttachEvent2_clbk = bool (WINAPIV*)(struct CLuaEventMgr*, struct CLuaEventNode*, CLuaEventMgrAttachEvent2_ptr);
        
        using CLuaEventMgrctor_CLuaEventMgr4_ptr = void (WINAPIV*)(struct CLuaEventMgr*);
        using CLuaEventMgrctor_CLuaEventMgr4_clbk = void (WINAPIV*)(struct CLuaEventMgr*, CLuaEventMgrctor_CLuaEventMgr4_ptr);
        using CLuaEventMgrDestroy6_ptr = void (WINAPIV*)();
        using CLuaEventMgrDestroy6_clbk = void (WINAPIV*)(CLuaEventMgrDestroy6_ptr);
        using CLuaEventMgrDettachEvent8_ptr = bool (WINAPIV*)(struct CLuaEventMgr*, struct CLuaEventNode*);
        using CLuaEventMgrDettachEvent8_clbk = bool (WINAPIV*)(struct CLuaEventMgr*, struct CLuaEventNode*, CLuaEventMgrDettachEvent8_ptr);
        using CLuaEventMgrInitSDM10_ptr = bool (WINAPIV*)(struct CLuaEventMgr*);
        using CLuaEventMgrInitSDM10_clbk = bool (WINAPIV*)(struct CLuaEventMgr*, CLuaEventMgrInitSDM10_ptr);
        using CLuaEventMgrInstance12_ptr = struct CLuaEventMgr* (WINAPIV*)();
        using CLuaEventMgrInstance12_clbk = struct CLuaEventMgr* (WINAPIV*)(CLuaEventMgrInstance12_ptr);
        using CLuaEventMgrLoop14_ptr = void (WINAPIV*)(struct CLuaEventMgr*);
        using CLuaEventMgrLoop14_clbk = void (WINAPIV*)(struct CLuaEventMgr*, CLuaEventMgrLoop14_ptr);
        using CLuaEventMgrNewEvent16_ptr = struct CLuaEventNode* (WINAPIV*)(struct CLuaEventMgr*);
        using CLuaEventMgrNewEvent16_clbk = struct CLuaEventNode* (WINAPIV*)(struct CLuaEventMgr*, CLuaEventMgrNewEvent16_ptr);
        using CLuaEventMgrRemoveEvent18_ptr = void (WINAPIV*)(struct CLuaEventMgr*, struct CLuaEventNode*);
        using CLuaEventMgrRemoveEvent18_clbk = void (WINAPIV*)(struct CLuaEventMgr*, struct CLuaEventNode*, CLuaEventMgrRemoveEvent18_ptr);
        using CLuaEventMgrSearchEvent20_ptr = struct CLuaEventNode* (WINAPIV*)(struct CLuaEventMgr*, char*);
        using CLuaEventMgrSearchEvent20_clbk = struct CLuaEventNode* (WINAPIV*)(struct CLuaEventMgr*, char*, CLuaEventMgrSearchEvent20_ptr);
        
        using CLuaEventMgrdtor_CLuaEventMgr24_ptr = void (WINAPIV*)(struct CLuaEventMgr*);
        using CLuaEventMgrdtor_CLuaEventMgr24_clbk = void (WINAPIV*)(struct CLuaEventMgr*, CLuaEventMgrdtor_CLuaEventMgr24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

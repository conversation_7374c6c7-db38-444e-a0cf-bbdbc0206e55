// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<IUnknown> : CComPtrBase<IUnknown>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<IPersist> : CComPtrBase<IPersist>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<ICreateErrorInfo> : CComPtrBase<ICreateErrorInfo>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<ITypeInfo> : CComPtrBase<ITypeInfo>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<IWebBrowser2> : CComPtrBase<IWebBrowser2>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<ITypeInfo2> : CComPtrBase<ITypeInfo2>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<IPersistStream> : CComPtrBase<IPersistStream>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<IHTMLDocument2> : CComPtrBase<IHTMLDocument2>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<IErrorInfo> : CComPtrBase<IErrorInfo>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<ITypeLib> : CComPtrBase<ITypeLib>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<IProvideClassInfo2> : CComPtrBase<IProvideClassInfo2>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<IMalloc> : CComPtrBase<IMalloc>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<ICatRegister> : CComPtrBase<ICatRegister>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ATL__CComPtrBase.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComPtr<IDispatch> : CComPtrBase<IDispatch>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

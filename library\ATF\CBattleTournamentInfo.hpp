// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <TournamentWinner.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct CBattleTournamentInfo
    {
        bool m_bLoad;
        int m_nCurNum;
        TournamentWinner m_WinnerInfo[48];
    public:
        CBattleTournamentInfo();
        void ctor_CBattleTournamentInfo();
        char Get<PERSON>innerGrade(unsigned int dwSerial, char* pwszCharName);
        void Init();
        void SetLoad(bool bLoad);
        bool SetWinnerInfo(unsigned int dwSerial, char* pwszCharName, char byGrade);
        ~CBattleTournamentInfo();
        void dtor_CBattleTournamentInfo();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CBattleTournamentInfo, 1160>(), "CBattleTournamentInfo");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_react_area.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _react_areactor__react_area2_ptr = void (WINAPIV*)(struct _react_area*);
        using _react_areactor__react_area2_clbk = void (WINAPIV*)(struct _react_area*, _react_areactor__react_area2_ptr);
        using _react_areacopy4_ptr = void (WINAPIV*)(struct _react_area*, struct _react_area*);
        using _react_areacopy4_clbk = void (WINAPIV*)(struct _react_area*, struct _react_area*, _react_areacopy4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

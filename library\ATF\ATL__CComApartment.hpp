// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        #pragma pack(push, 8)
        struct CComApartment
        {
            unsigned int m_dwThreadID;
            void *m_hThread;
            int m_nLockCnt;
        };
        #pragma pack(pop)
    }; // end namespace ATL
END_ATF_NAMESPACE

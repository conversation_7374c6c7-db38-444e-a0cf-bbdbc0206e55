#include <_personal_amine_mineore_zocl.hpp>


START_ATF_NAMESPACE
    _personal_amine_mineore_zocl::_personal_amine_mineore_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_mineore_zocl*);
        (org_ptr(0x14029d5a0L))(this);
    };
    void _personal_amine_mineore_zocl::ctor__personal_amine_mineore_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_mineore_zocl*);
        (org_ptr(0x14029d5a0L))(this);
    };
    void _personal_amine_mineore_zocl::clear()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_mineore_zocl*);
        (org_ptr(0x1402ddf10L))(this);
    };
    int _personal_amine_mineore_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_amine_mineore_zocl*);
        return (org_ptr(0x14029d650L))(this);
    };
    _personal_amine_mineore_zocl::__changed::__changed()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_mineore_zocl::__changed*);
        (org_ptr(0x14029d620L))(this);
    };
    void _personal_amine_mineore_zocl::__changed::ctor___changed()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_mineore_zocl::__changed*);
        (org_ptr(0x14029d620L))(this);
    };
END_ATF_NAMESPACE

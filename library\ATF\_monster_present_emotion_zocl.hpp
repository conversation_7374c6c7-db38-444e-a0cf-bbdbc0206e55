// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _monster_present_emotion_zocl
    {
        unsigned int dwSerial;
        char byType;
        unsigned __int16 wIndex;
        unsigned __int16 wRIndex;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

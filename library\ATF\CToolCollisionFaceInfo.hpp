// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CToolCollisionFace.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CToolCollisionFaceDrawCollisionPoint1_ptr = void (WINAPIV*)(struct CToolCollisionFace*);
        using CToolCollisionFaceDrawCollisionPoint1_clbk = void (WINAPIV*)(struct CToolCollisionFace*, CToolCollisionFaceDrawCollisionPoint1_ptr);
        using CToolCollisionFaceDrawCollisionPoly2_ptr = void (WINAPIV*)(struct CToolCollisionFace*);
        using CToolCollisionFaceDrawCollisionPoly2_clbk = void (WINAPIV*)(struct CToolCollisionFace*, CToolCollisionFaceDrawCollisionPoly2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagDBID.hpp>
#include <tagDBPROPSET.hpp>



START_ATF_NAMESPACE
    struct tagDBCONSTRAINTDESC
    {
        tagDBID *pConstraintID;
        unsigned int ConstraintType;
        unsigned __int64 cColumns;
        tagDBID *rgColumnList;
        tagDBID *pReferencedTableID;
        unsigned __int64 cForeignKeyColumns;
        tagDBID *rgForeignKeyColumnList;
        wchar_t *pwszConstraintText;
        unsigned int UpdateRule;
        unsigned int DeleteRule;
        unsigned int MatchType;
        unsigned int Deferrability;
        unsigned __int64 cReserved;
        tagDBPROPSET *rgReserved;
    };
END_ATF_NAMESPACE

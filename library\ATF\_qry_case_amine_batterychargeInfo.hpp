// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_amine_batterycharge.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_amine_batterychargector__qry_case_amine_batterycharge2_ptr = void (WINAPIV*)(struct _qry_case_amine_batterycharge*);
        using _qry_case_amine_batterychargector__qry_case_amine_batterycharge2_clbk = void (WINAPIV*)(struct _qry_case_amine_batterycharge*, _qry_case_amine_batterychargector__qry_case_amine_batterycharge2_ptr);
        using _qry_case_amine_batterychargesize4_ptr = int (WINAPIV*)(struct _qry_case_amine_batterycharge*);
        using _qry_case_amine_batterychargesize4_clbk = int (WINAPIV*)(struct _qry_case_amine_batterycharge*, _qry_case_amine_batterychargesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

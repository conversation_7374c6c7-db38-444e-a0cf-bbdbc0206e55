// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Deque_map.hpp>
#include <std__allocator.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 8)
        template<>
        struct  _Deque_val<RECV_DATA,allocator<RECV_DATA> > : _Deque_map<RECV_DATA,allocator<RECV_DATA> >
        {
            allocator<RECV_DATA> _Alval;
        };
        #pragma pack(pop)    
        static_assert(ATF::checkSize<std::_Deque_val<RECV_DATA,std::allocator<RECV_DATA> >, 24>(), "std::_Deque_val<RECV_DATA,std::allocator<RECV_DATA> >");
    }; // end namespace std
END_ATF_NAMESPACE

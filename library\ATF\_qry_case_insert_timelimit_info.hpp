// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_insert_timelimit_info
    {
        unsigned __int16 wIndex;
        unsigned int dwAccSerial;
        unsigned int dwLastLogoutDate;
        unsigned int dwFatigue;
        __int16 wStatus;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_insert_timelimit_info, 20>(), "_qry_case_insert_timelimit_info");
END_ATF_NAMESPACE

#include <_talik_crystal_exchange_zocl.hpp>


START_ATF_NAMESPACE
    _talik_crystal_exchange_zocl::_talik_crystal_exchange_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _talik_crystal_exchange_zocl*);
        (org_ptr(0x140432230L))(this);
    };
    void _talik_crystal_exchange_zocl::ctor__talik_crystal_exchange_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _talik_crystal_exchange_zocl*);
        (org_ptr(0x140432230L))(this);
    };
    int _talik_crystal_exchange_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _talik_crystal_exchange_zocl*);
        return (org_ptr(0x140432280L))(this);
    };
    
END_ATF_NAMESPACE

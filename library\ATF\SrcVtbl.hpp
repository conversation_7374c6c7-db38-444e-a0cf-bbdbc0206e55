// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct SrcVtbl
    {
        bool (WINAPIV *Close)(Src *_this);
        bool (WINAPIV *Remove)(Src *_this, const char *);
        bool (WINAPIV *QueryByName)(Src *_this, const char *, SrcHeaderOut *);
        bool (WINAPIV *GetData)(Src *_this, SrcHeaderOut *, void *);
        bool (WINAPIV *GetEnum)(Src *_this, EnumSrc **);
        bool (WINAPIV *GetHeaderBlock)(Src *_this, Src<PERSON><PERSON>er<PERSON>lock *);
        bool (WINAPIV *RemoveW)(Src *_this, wchar_t *);
        bool (WINAPIV *QueryByNameW)(Src *_this, wchar_t *, SrcHeaderOut *);
        bool (WINAPIV *AddW)(Src *_this, SrcHeaderW *, const void *);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBilling.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CBillingAlive2_ptr = void (WINAPIV*)(struct CBilling*, struct CUserDB*);
        using CBillingAlive2_clbk = void (WINAPIV*)(struct CBilling*, struct CUserDB*, CBillingAlive2_ptr);
        using CBillingBillingClose4_ptr = void (WINAPIV*)(struct CBilling*, char*);
        using CBillingBillingClose4_clbk = void (WINAPIV*)(struct CBilling*, char*, CBillingBillingClose4_ptr);
        
        using CBillingctor_CBilling6_ptr = void (WINAPIV*)(struct CBilling*);
        using CBillingctor_CBilling6_clbk = void (WINAPIV*)(struct CBilling*, CBillingctor_CBilling6_ptr);
        using CBillingChange_BillingType8_ptr = void (WINAPIV*)(struct CBilling*, char*, char*, int16_t, int, struct _SYSTEMTIME*, char);
        using CBillingChange_BillingType8_clbk = void (WINAPIV*)(struct CBilling*, char*, char*, int16_t, int, struct _SYSTEMTIME*, char, CBillingChange_BillingType8_ptr);
        using CBillingChange_Primium10_ptr = void (WINAPIV*)(struct CBilling*, char*, bool);
        using CBillingChange_Primium10_clbk = void (WINAPIV*)(struct CBilling*, char*, bool, CBillingChange_Primium10_ptr);
        using CBillingExpire_IPOverflow12_ptr = void (WINAPIV*)(struct CBilling*, char*);
        using CBillingExpire_IPOverflow12_clbk = void (WINAPIV*)(struct CBilling*, char*, CBillingExpire_IPOverflow12_ptr);
        using CBillingExpire_PCBang14_ptr = void (WINAPIV*)(struct CBilling*, char*);
        using CBillingExpire_PCBang14_clbk = void (WINAPIV*)(struct CBilling*, char*, CBillingExpire_PCBang14_ptr);
        using CBillingExpire_Personal16_ptr = void (WINAPIV*)(struct CBilling*, char*);
        using CBillingExpire_Personal16_clbk = void (WINAPIV*)(struct CBilling*, char*, CBillingExpire_Personal16_ptr);
        using CBillingLogin18_ptr = void (WINAPIV*)(struct CBilling*, struct CUserDB*);
        using CBillingLogin18_clbk = void (WINAPIV*)(struct CBilling*, struct CUserDB*, CBillingLogin18_ptr);
        using CBillingLogout20_ptr = void (WINAPIV*)(struct CBilling*, struct CUserDB*);
        using CBillingLogout20_clbk = void (WINAPIV*)(struct CBilling*, struct CUserDB*, CBillingLogout20_ptr);
        using CBillingRemaintime_PCBang22_ptr = void (WINAPIV*)(struct CBilling*, char*, int16_t, int, struct _SYSTEMTIME*);
        using CBillingRemaintime_PCBang22_clbk = void (WINAPIV*)(struct CBilling*, char*, int16_t, int, struct _SYSTEMTIME*, CBillingRemaintime_PCBang22_ptr);
        using CBillingRemaintime_Personal24_ptr = void (WINAPIV*)(struct CBilling*, char*, int16_t, int, struct _SYSTEMTIME*);
        using CBillingRemaintime_Personal24_clbk = void (WINAPIV*)(struct CBilling*, char*, int16_t, int, struct _SYSTEMTIME*, CBillingRemaintime_Personal24_ptr);
        using CBillingSendMsg_CurAllUserLogin26_ptr = void (WINAPIV*)(struct CBilling*);
        using CBillingSendMsg_CurAllUserLogin26_clbk = void (WINAPIV*)(struct CBilling*, CBillingSendMsg_CurAllUserLogin26_ptr);
        using CBillingSendMsg_Login28_ptr = bool (WINAPIV*)(struct CBilling*, char*, char*, char*, int16_t, struct _SYSTEMTIME*, int);
        using CBillingSendMsg_Login28_clbk = bool (WINAPIV*)(struct CBilling*, char*, char*, char*, int16_t, struct _SYSTEMTIME*, int, CBillingSendMsg_Login28_ptr);
        using CBillingSendMsg_StartBilling30_ptr = void (WINAPIV*)(struct CBilling*);
        using CBillingSendMsg_StartBilling30_clbk = void (WINAPIV*)(struct CBilling*, CBillingSendMsg_StartBilling30_ptr);
        using CBillingSendMsg_ZoneAliveCheck32_ptr = void (WINAPIV*)(struct CBilling*, unsigned int);
        using CBillingSendMsg_ZoneAliveCheck32_clbk = void (WINAPIV*)(struct CBilling*, unsigned int, CBillingSendMsg_ZoneAliveCheck32_ptr);
        using CBillingSetOper34_ptr = void (WINAPIV*)(struct CBilling*, bool);
        using CBillingSetOper34_clbk = void (WINAPIV*)(struct CBilling*, bool, CBillingSetOper34_ptr);
        using CBillingStart36_ptr = void (WINAPIV*)(struct CBilling*);
        using CBillingStart36_clbk = void (WINAPIV*)(struct CBilling*, CBillingStart36_ptr);
        
        using CBillingdtor_CBilling41_ptr = void (WINAPIV*)(struct CBilling*);
        using CBillingdtor_CBilling41_clbk = void (WINAPIV*)(struct CBilling*, CBillingdtor_CBilling41_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

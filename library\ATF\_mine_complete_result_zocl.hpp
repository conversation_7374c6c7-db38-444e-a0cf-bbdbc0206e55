// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _mine_complete_result_zocl
    {
        char byErrCode;
        unsigned __int16 wEquipLeftDur;
        unsigned __int16 wBatteryLeftDur;
        char byOreIndex;
        unsigned __int16 wOreSerial;
        char byOreDur;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

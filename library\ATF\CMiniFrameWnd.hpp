// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CFrameWnd.hpp>


START_ATF_NAMESPACE
    struct  CMiniFrameWnd : CFrameWnd
    {
        int m_bSysTracking;
        int m_bInSys;
        int m_bActive;
        ATL::CStringT<char> m_strCaption;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CItemStore.hpp>
#include <CMapData.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMapItemStoreList
    {
        bool m_bUse;
        char m_byType;
        int m_nSerial;
        int m_nItemStoreNum;
        CItemStore *m_ItemStore;
    public:
        CMapItemStoreList();
        void ctor_CMapItemStoreList();
        bool CopyItemStoreData(struct CMapItemStoreList* pDest);
        bool CreateStores(struct CMapData* pMap);
        struct CItemStore* GetItemStoreFromRecIndex(unsigned int dwRecIndex);
        bool SetItemStores(struct CMapData* pMap);
        void SetTypeNSerial(char byType, int nSerial);
        ~CMapItemStoreList();
        void dtor_CMapItemStoreList();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<CMapItemStoreList, 0x18>(), "CMapItemStoreList");
END_ATF_NAMESPACE

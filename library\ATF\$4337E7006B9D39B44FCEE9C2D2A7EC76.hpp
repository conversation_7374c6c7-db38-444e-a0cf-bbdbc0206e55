// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$409DFF2A380C4FFE2B587D0390AC124E.hpp>


START_ATF_NAMESPACE
    union $4337E7006B9D39B44FCEE9C2D2A7EC76
    {
        unsigned int dwOemId;
        $409DFF2A380C4FFE2B587D0390AC124E __s1;
    };    
    static_assert(ATF::checkSize<$4337E7006B9D39B44FCEE9C2D2A7EC76, 4>(), "$4337E7006B9D39B44FCEE9C2D2A7EC76");
END_ATF_NAMESPACE

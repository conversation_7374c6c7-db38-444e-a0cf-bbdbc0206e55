// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <INTERNET_SCHEME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct URL_COMPONENTSA
    {
        unsigned int dwStructSize;
        char *lpszScheme;
        unsigned int dwSchemeLength;
        INTERNET_SCHEME nScheme;
        char *lpszHostName;
        unsigned int dwHostNameLength;
        unsigned __int16 nPort;
        char *lpszUserName;
        unsigned int dwUserNameLength;
        char *lpszPassword;
        unsigned int dwPasswordLength;
        char *lpszUrlPath;
        unsigned int dwUrlPathLength;
        char *lpszExtraInfo;
        unsigned int dwExtraInfoLength;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  AtlLimits<int>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  AtlLimits<unsigned int>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  AtlLimits<long>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  AtlLimits<unsigned __int64>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  AtlLimits<unsigned long>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  AtlLimits<__int64>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

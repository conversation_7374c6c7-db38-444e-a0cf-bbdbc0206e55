// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagRECT.hpp>
#include <tagSIZE.hpp>


START_ATF_NAMESPACE
    struct tagLVTILEVIEWINFO
    {
        unsigned int cbSize;
        unsigned int dwMask;
        unsigned int dwFlags;
        tagSIZE sizeTile;
        int cLines;
        tagRECT rcLabelMargin;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_job_sub_setup.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _job_sub_setupctor__job_sub_setup2_ptr = void (WINAPIV*)(struct _job_sub_setup*);
        using _job_sub_setupctor__job_sub_setup2_clbk = void (WINAPIV*)(struct _job_sub_setup*, _job_sub_setupctor__job_sub_setup2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

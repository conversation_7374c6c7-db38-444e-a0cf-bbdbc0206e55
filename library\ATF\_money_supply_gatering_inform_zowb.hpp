// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _money_supply_gatering_inform_zowb
    {
        struct _money_supply
        {
            int nLv[4];
            int nRace[3];
            int nClass[60];
        };
        __int64 dwAmount[9];
        _money_supply ms_data[4];
        int nFeeLv[4];
        int nFeeRace[3];
        int nHonorGuildRace[2][3];
        int nUnitRepairLv[4];
        int nBuyUnitLv[4];
    public:
        void init();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_money_supply_gatering_inform_zowb, 1228>(), "_money_supply_gatering_inform_zowb");
END_ATF_NAMESPACE

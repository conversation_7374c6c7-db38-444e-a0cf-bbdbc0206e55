// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ASYNC_LOG_TYPE.hpp>
#include <CLogFile.hpp>
#include <CMyTimer.hpp>
#include <CNetCriticalSection.hpp>


START_ATF_NAMESPACE
    struct CAsyncLogInfo
    {
        ASYNC_LOG_TYPE m_eType;
        unsigned int m_dwLogCount;
        char *m_szLogDirPath;
        char *m_szLogFileName;
        char *m_szTypeName;
        CMyTimer *m_pkTimer;
        CNetCriticalSection m_csLock;
    public:
        CAsyncLogInfo();
        void ctor_CAsyncLogInfo();
        unsigned int GetCount();
        char* GetDirPath();
        char* GetFileName();
        char* GetTypeName();
        void IncreaseCount();
        bool Init(ASYNC_LOG_TYPE eType, char* szDirPath, char* szTypeName, bool bAddDateFileName, unsigned int dwUpdateFileNameDelay, struct CLogFile* logLoading);
        void UpdateLogFileName();
        ~CAsyncLogInfo();
        void dtor_CAsyncLogInfo();
    };
END_ATF_NAMESPACE

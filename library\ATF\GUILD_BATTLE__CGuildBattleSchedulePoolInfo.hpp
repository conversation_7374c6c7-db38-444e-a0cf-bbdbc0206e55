// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleSchedulePool.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattleSchedulePoolctor_CGuildBattleSchedulePool2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*);
            using GUILD_BATTLE__CGuildBattleSchedulePoolctor_CGuildBattleSchedulePool2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, GUILD_BATTLE__CGuildBattleSchedulePoolctor_CGuildBattleSchedulePool2_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulePoolClearAll4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*);
            using GUILD_BATTLE__CGuildBattleSchedulePoolClearAll4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, GUILD_BATTLE__CGuildBattleSchedulePoolClearAll4_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulePoolClearByDayID6_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int);
            using GUILD_BATTLE__CGuildBattleSchedulePoolClearByDayID6_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int, GUILD_BATTLE__CGuildBattleSchedulePoolClearByDayID6_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulePoolDestroy8_ptr = void (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleSchedulePoolDestroy8_clbk = void (WINAPIV*)(GUILD_BATTLE__CGuildBattleSchedulePoolDestroy8_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulePoolGet10_ptr = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleSchedulePoolGet10_clbk = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleSchedulePoolGet10_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulePoolGet12_ptr = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int);
            using GUILD_BATTLE__CGuildBattleSchedulePoolGet12_clbk = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int, GUILD_BATTLE__CGuildBattleSchedulePoolGet12_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulePoolGetRef14_ptr = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int);
            using GUILD_BATTLE__CGuildBattleSchedulePoolGetRef14_clbk = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int, GUILD_BATTLE__CGuildBattleSchedulePoolGetRef14_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulePoolGetSID16_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleSchedulePoolGetSID16_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleSchedulePoolGetSID16_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulePoolInit18_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int);
            using GUILD_BATTLE__CGuildBattleSchedulePoolInit18_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, unsigned int, GUILD_BATTLE__CGuildBattleSchedulePoolInit18_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulePoolInstance20_ptr = struct GUILD_BATTLE::CGuildBattleSchedulePool* (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleSchedulePoolInstance20_clbk = struct GUILD_BATTLE::CGuildBattleSchedulePool* (WINAPIV*)(GUILD_BATTLE__CGuildBattleSchedulePoolInstance20_ptr);
            
            using GUILD_BATTLE__CGuildBattleSchedulePooldtor_CGuildBattleSchedulePool24_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*);
            using GUILD_BATTLE__CGuildBattleSchedulePooldtor_CGuildBattleSchedulePool24_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedulePool*, GUILD_BATTLE__CGuildBattleSchedulePooldtor_CGuildBattleSchedulePool24_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

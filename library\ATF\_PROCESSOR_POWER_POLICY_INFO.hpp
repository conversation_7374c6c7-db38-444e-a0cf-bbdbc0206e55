// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _PROCESSOR_POWER_POLICY_INFO
    {
        unsigned int TimeCheck;
        unsigned int DemoteLimit;
        unsigned int PromoteLimit;
        char <PERSON>motePercent;
        char PromotePercent;
        char <PERSON>re[2];
        unsigned __int32 AllowDemotion : 1;
        unsigned __int32 AllowPromotion : 1;
        unsigned __int32 Reserved : 30;
    };
END_ATF_NAMESPACE

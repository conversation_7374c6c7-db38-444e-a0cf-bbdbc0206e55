// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <_FILETIME.hpp>
#include <_cash_event_time.hpp>
#include <_con_event_ini.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _con_event_
    {
        char m_conevent_status;
        _FILETIME m_conevent_ini_file_time;
        CLogFile m_conevent_log;
        bool m_bConEvent;
        _con_event_ini m_ini;
        _cash_event_time m_eventtime;
    public:
        _con_event_();
        void ctor__con_event_();
        ~_con_event_();
        void dtor__con_event_();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_a_trade_clear_item_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _a_trade_clear_item_result_zoclsize2_ptr = int (WINAPIV*)(struct _a_trade_clear_item_result_zocl*);
        using _a_trade_clear_item_result_zoclsize2_clbk = int (WINAPIV*)(struct _a_trade_clear_item_result_zocl*, _a_trade_clear_item_result_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

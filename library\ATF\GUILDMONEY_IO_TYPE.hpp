// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum GUILDMONEY_IO_TYPE
    {
      guildmoney_io_normal = 0x0,
      guildmoney_io_normal_emblem = 0x1,
      guildmoney_io_normal_buy_board = 0x2,
      guildmoney_io_normal_buy_trunk = 0x3,
      guildmoney_io_normal_scrumble_cost = 0x4,
      guildmoney_io_normal_scrumble_reward = 0x5,
      guildmoney_io_normal_atrade_tax_money = 0x6,
      guildmoney_io_normal_automine_charge = 0x7,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _cash_discount_event_inform_zocl
    {
        char byEventType;
        unsigned __int16 wCsDiscount;
        unsigned __int16 wYear[2];
        char by<PERSON>onth[2];
        char byDay[2];
        char byHour[2];
        char byMinute[2];
    public:
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

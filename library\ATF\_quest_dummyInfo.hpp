// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_quest_dummy.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _quest_dummySetDummy2_ptr = bool (WINAPIV*)(struct _quest_dummy*, struct _dummy_position*);
        using _quest_dummySetDummy2_clbk = bool (WINAPIV*)(struct _quest_dummy*, struct _dummy_position*, _quest_dummySetDummy2_ptr);
        
        using _quest_dummyctor__quest_dummy4_ptr = void (WINAPIV*)(struct _quest_dummy*);
        using _quest_dummyctor__quest_dummy4_clbk = void (WINAPIV*)(struct _quest_dummy*, _quest_dummyctor__quest_dummy4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

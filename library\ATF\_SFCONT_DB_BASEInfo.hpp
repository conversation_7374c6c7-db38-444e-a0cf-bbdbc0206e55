// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SFCONT_DB_BASE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _SFCONT_DB_BASEInit2_ptr = void (WINAPIV*)(struct _SFCONT_DB_BASE*);
        using _SFCONT_DB_BASEInit2_clbk = void (WINAPIV*)(struct _SFCONT_DB_BASE*, _SFCONT_DB_BASEInit2_ptr);
        
        using _SFCONT_DB_BASEctor__SFCONT_DB_BASE4_ptr = void (WINAPIV*)(struct _SFCONT_DB_BASE*);
        using _SFCONT_DB_BASEctor__SFCONT_DB_BASE4_clbk = void (WINAPIV*)(struct _SFCONT_DB_BASE*, _SFCONT_DB_BASEctor__SFCONT_DB_BASE4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

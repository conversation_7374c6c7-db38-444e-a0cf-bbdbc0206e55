// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__exceptionVtbl.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 8)
        struct exception
        {
            exceptionVtbl *vfptr;
            const char *_m_what;
            int _m_doFree;
        public:
            exception(char** arg_0);
            int64_t ctor_exception(char** arg_0);
            exception(char** arg_0, int arg_1);
            int64_t ctor_exception(char** arg_0, int arg_1);
            exception(struct exception* arg_0);
            int64_t ctor_exception(struct exception* arg_0);
            exception();
            int64_t ctor_exception();
            char* what();
        };
        #pragma pack(pop)    
        static_assert(ATF::checkSize<std::exception, 24>(), "std::exception");
    }; // end namespace std
END_ATF_NAMESPACE

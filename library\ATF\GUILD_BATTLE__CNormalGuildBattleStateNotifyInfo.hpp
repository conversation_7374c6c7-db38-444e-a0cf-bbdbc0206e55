// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateNotify.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateNotifyctor_CNormalGuildBattleStateNotify2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateNotify*);
            using GUILD_BATTLE__CNormalGuildBattleStateNotifyctor_CNormalGuildBattleStateNotify2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateNotify*, GUILD_BATTLE__CNormalGuildBattleStateNotifyctor_CNormalGuildBattleStateNotify2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateNotifyEnter4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateNotify*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateNotifyEnter4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateNotify*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateNotifyEnter4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateNotifyGetTerm6_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateNotify*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CNormalGuildBattleStateNotifyGetTerm6_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateNotify*, struct ATL::CTimeSpan*, GUILD_BATTLE__CNormalGuildBattleStateNotifyGetTerm6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateNotifydtor_CNormalGuildBattleStateNotify8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateNotify*);
            using GUILD_BATTLE__CNormalGuildBattleStateNotifydtor_CNormalGuildBattleStateNotify8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateNotify*, GUILD_BATTLE__CNormalGuildBattleStateNotifydtor_CNormalGuildBattleStateNotify8_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

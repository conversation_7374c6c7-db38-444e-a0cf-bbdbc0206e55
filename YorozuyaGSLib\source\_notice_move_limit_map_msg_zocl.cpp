#include <_notice_move_limit_map_msg_zocl.hpp>


START_ATF_NAMESPACE
    _notice_move_limit_map_msg_zocl::_notice_move_limit_map_msg_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _notice_move_limit_map_msg_zocl*);
        (org_ptr(0x1403a7100L))(this);
    };
    void _notice_move_limit_map_msg_zocl::ctor__notice_move_limit_map_msg_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _notice_move_limit_map_msg_zocl*);
        (org_ptr(0x1403a7100L))(this);
    };
    
END_ATF_NAMESPACE

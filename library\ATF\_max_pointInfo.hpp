// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_max_point.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _max_pointctor__max_point2_ptr = void (WINAPIV*)(struct _max_point*);
        using _max_pointctor__max_point2_clbk = void (WINAPIV*)(struct _max_point*, _max_pointctor__max_point2_ptr);
        using _max_pointinit4_ptr = void (WINAPIV*)(struct _max_point*);
        using _max_pointinit4_clbk = void (WINAPIV*)(struct _max_point*, _max_pointinit4_ptr);
        using _max_pointsize6_ptr = int (WINAPIV*)(struct _max_point*);
        using _max_pointsize6_clbk = int (WINAPIV*)(struct _max_point*, _max_pointsize6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

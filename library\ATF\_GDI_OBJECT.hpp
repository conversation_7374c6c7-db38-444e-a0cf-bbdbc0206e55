// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _GDI_OBJECT
    {
        union __MIDL_IAdviseSink_0002
        {
            _userHBITMAP *hBitmap;
            _userHPALETTE *hPalette;
            _userHGLOBAL *hGeneric;
        };
        unsigned int ObjectType;
        __MIDL_IAdviseSink_0002 u;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleScheduleManager.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            using GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int, unsigned int, struct GUILD_BATTLE::CGuildBattleSchedule**, unsigned int*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int, unsigned int, struct GUILD_BATTLE::CGuildBattleSchedule**, unsigned int*, GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_ptr);
            
            using GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerClear10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerClear10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerClear10_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_ptr = void (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_clbk = void (WINAPIV*)(GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int*, GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int*, GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerInit28_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int);
            using GUILD_BATTLE__CGuildBattleScheduleManagerInit28_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, GUILD_BATTLE__CGuildBattleScheduleManagerInit28_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_ptr = struct GUILD_BATTLE::CGuildBattleScheduleManager* (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_clbk = struct GUILD_BATTLE::CGuildBattleScheduleManager* (WINAPIV*)(GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, int, unsigned int, int, int, int, int);
            using GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, int, unsigned int, int, int, int, int, GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_ptr = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_clbk = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, unsigned int, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_ptr);
            
            using GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*);
            using GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduleManager*, GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

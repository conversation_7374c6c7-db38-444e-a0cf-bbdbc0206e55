// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CCharacter.hpp>
#include <CMapData.hpp>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    struct  CGravityStoneRegener : CCharacter
    {
        enum GSR_STATE
        {
            GSR_NONE = 0xFFFFFFFF,
            GSR_INIT = 0x0,
            GSR_CREATE = 0x1,
            GSR_REGEN = 0x2,
            GSR_TAKE = 0x3,
            GSR_DESTROY = 0x4,
            GSR_MAX = 0x6,
        };
        GSR_STATE m_eState;
        int m_iPortalInx;
        _dummy_position *m_pkRegenPos;
    public:
        CGravityStoneRegener();
        void ctor_CGravityStoneRegener();
        void CheatClearRegenState();
        bool ClearRegen();
        bool Create(struct CMapData* pkMap);
        void Destroy();
        int GetPortalInx();
        char* GetStateString(struct ATL::CStringT<char>* strState);
        bool Init(unsigned int uiMapInx, uint16_t wInx, struct CMapData* pkMap);
        bool IsNearPosition(float* pfCurPos);
        int Regen();
        void SendMsgAlterState();
        void SendMsg_FixPosition(int n);
        char Take(struct CMapData* pkMap, float* pfCurPos);
        ~CGravityStoneRegener();
        void dtor_CGravityStoneRegener();
    };
END_ATF_NAMESPACE

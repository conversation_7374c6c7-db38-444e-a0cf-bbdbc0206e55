// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_sheet_logout.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_sheet_logoutctor__qry_sheet_logout2_ptr = void (WINAPIV*)(struct _qry_sheet_logout*);
        using _qry_sheet_logoutctor__qry_sheet_logout2_clbk = void (WINAPIV*)(struct _qry_sheet_logout*, _qry_sheet_logoutctor__qry_sheet_logout2_ptr);
        using _qry_sheet_logoutsize4_ptr = int (WINAPIV*)(struct _qry_sheet_logout*);
        using _qry_sheet_logoutsize4_clbk = int (WINAPIV*)(struct _qry_sheet_logout*, _qry_sheet_logoutsize4_ptr);
        
        using _qry_sheet_logoutdtor__qry_sheet_logout6_ptr = void (WINAPIV*)(struct _qry_sheet_logout*);
        using _qry_sheet_logoutdtor__qry_sheet_logout6_clbk = void (WINAPIV*)(struct _qry_sheet_logout*, _qry_sheet_logoutdtor__qry_sheet_logout6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

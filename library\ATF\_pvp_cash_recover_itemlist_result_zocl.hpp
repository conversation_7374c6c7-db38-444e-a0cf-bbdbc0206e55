// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _pvp_cash_recover_itemlist_result_zocl
    {
        char byItemNum;
        int nTalikInfo[14];
    public:
        _pvp_cash_recover_itemlist_result_zocl();
        void ctor__pvp_cash_recover_itemlist_result_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_pvp_cash_recover_itemlist_result_zocl, 57>(), "_pvp_cash_recover_itemlist_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_nuclear_explosion_success_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _nuclear_explosion_success_zoclctor__nuclear_explosion_success_zocl2_ptr = void (WINAPIV*)(struct _nuclear_explosion_success_zocl*);
        using _nuclear_explosion_success_zoclctor__nuclear_explosion_success_zocl2_clbk = void (WINAPIV*)(struct _nuclear_explosion_success_zocl*, _nuclear_explosion_success_zoclctor__nuclear_explosion_success_zocl2_ptr);
        using _nuclear_explosion_success_zoclsize4_ptr = int (WINAPIV*)(struct _nuclear_explosion_success_zocl*);
        using _nuclear_explosion_success_zoclsize4_clbk = int (WINAPIV*)(struct _nuclear_explosion_success_zocl*, _nuclear_explosion_success_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CUnmannedTraderSchedule
    {
        enum STATE
        {
            EMPTY = 0x0,
            REG_WAIT = 0x1,
            WAIT_CANCEL = 0x2,
            CANCEL_SUCC_COMPLETE = 0x3,
            CANCEL_FAIL_COMPLETE = 0x4,
        };
        STATE m_eState;
        char m_byType;
        unsigned int m_dwRegistSerial;
        __int64 m_tEndTime;
        unsigned int m_dwOwnerSerial;
        char m_byItemTableCode;
        unsigned __int16 m_wItemTableIndex;
    public:
        CUnmannedTraderSchedule();
        void ctor_CUnmannedTraderSchedule();
        void Clear();
        void CompleteClear(char byDBQueryRet, char byProcRet);
        unsigned int GetOwnerSerial();
        unsigned int GetRegistSerial();
        char GetType();
        bool IsDone();
        bool IsWait();
        void PushClear(bool bTimeLimit);
        void Set(char byType, unsigned int dwSerial, int64_t tEndTime, unsigned int dwOwnerSerial, unsigned int dwK);
        ~CUnmannedTraderSchedule();
        void dtor_CUnmannedTraderSchedule();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CUnmannedTraderSchedule, 32>(), "CUnmannedTraderSchedule");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___String_val.hpp>
#include <std__char_traits.hpp>
#include <std__allocator.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<typename _Ty, typename _Traits = char_traits<_Ty>, typename _Alloc = allocator<_Ty>>
        struct basic_string : _String_val<_Ty, _Alloc>
        {
            union _Bxty
            {
                _Ty _Buf[16];
                _Ty *_Ptr;
            };
            struct _Size_type_nosscl
            {
                unsigned __int64 _Value;
            };
            struct  _No_debug_placeholder
            {
            };

            struct _Has_debug_it
            {
                bool _Value;
            };
            _Bxty _Bx;
            unsigned __int64 _Mysize;
            unsigned __int64 _Myres;

            basic_string() = default;
        };
    }; // end namespace std
END_ATF_NAMESPACE

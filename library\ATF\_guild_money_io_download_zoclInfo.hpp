// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_guild_money_io_download_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _guild_money_io_download_zoclctor__guild_money_io_download_zocl2_ptr = void (WINAPIV*)(struct _guild_money_io_download_zocl*);
        using _guild_money_io_download_zoclctor__guild_money_io_download_zocl2_clbk = void (WINAPIV*)(struct _guild_money_io_download_zocl*, _guild_money_io_download_zoclctor__guild_money_io_download_zocl2_ptr);
        using _guild_money_io_download_zoclsize4_ptr = int (WINAPIV*)(struct _guild_money_io_download_zocl*);
        using _guild_money_io_download_zoclsize4_clbk = int (WINAPIV*)(struct _guild_money_io_download_zocl*, _guild_money_io_download_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

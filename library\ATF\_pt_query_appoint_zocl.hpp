// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pt_query_appoint_zocl
    {
        char byClassType;
        char byRet;
        char wszAvator<PERSON><PERSON>[17];
    public:
        _pt_query_appoint_zocl();
        void ctor__pt_query_appoint_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_pt_query_appoint_zocl, 19>(), "_pt_query_appoint_zocl");
END_ATF_NAMESPACE

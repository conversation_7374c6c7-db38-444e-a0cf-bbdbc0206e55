// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace std
    {
        struct exceptionVtbl
        {
            void *(WINAPIV *__vecDelDtor)(struct exception *_this, unsigned int);
            const char *(WINAPIV *what)(struct exception *_this);
        };
    }; // end namespace std
END_ATF_NAMESPACE

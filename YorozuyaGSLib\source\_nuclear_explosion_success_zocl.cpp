#include <_nuclear_explosion_success_zocl.hpp>


START_ATF_NAMESPACE
    _nuclear_explosion_success_zocl::_nuclear_explosion_success_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_explosion_success_zocl*);
        (org_ptr(0x14013e6e0L))(this);
    };
    void _nuclear_explosion_success_zocl::ctor__nuclear_explosion_success_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_explosion_success_zocl*);
        (org_ptr(0x14013e6e0L))(this);
    };
    int _nuclear_explosion_success_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _nuclear_explosion_success_zocl*);
        return (org_ptr(0x14013e730L))(this);
    };
END_ATF_NAMESPACE

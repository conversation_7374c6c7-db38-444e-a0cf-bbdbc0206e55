// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum HSS_CHECK_TIMER
    {
      HSCT_CRI_BATTLE_START = 0x0,
      HSCT_CRI_BATTLE_END = 0x1,
      HSCT_KEEPER_START_ATTACKABLE = 0x2,
      HSCT_KEEPER_START_DEATTACKABLE = 0x3,
      HSCT_KEEPER_START_DIE = 0x4,
      HSCT_KEEPER_START_CHAOS = 0x5,
      HSCT_KEEPER_END = 0x6,
      HSCT_MAX = 0x7,
    };
END_ATF_NAMESPACE

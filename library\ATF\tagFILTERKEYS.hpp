// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagFILTERKEYS
    {
        unsigned int cbSize;
        unsigned int dwFlags;
        unsigned int iWaitMSec;
        unsigned int iDelayMSec;
        unsigned int iRepeatMSec;
        unsigned int iBounceMSec;
    };
END_ATF_NAMESPACE

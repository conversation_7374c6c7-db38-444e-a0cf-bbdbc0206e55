// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CHRID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _skill_request_clzo
    {
        char bySkillIndex;
        _CHRID idDst;
        char byAttackSerial;
        unsigned __int16 wConsumeItemSerial[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$0E46A56A77182B87014A92C137928472.hpp>
#include <$27084C353F8D83FFEFF8DBE111284AEC.hpp>
#include <$4227E445DA2FF1EBA7DB9512D01703C8.hpp>
#include <$67E9C695EA16A9CA771F38DA1DA2EED4.hpp>
#include <$7D7C45323A019F4043EE11E98D72E442.hpp>
#include <$91829873B94B3C28EA1CF55F9BB8D28E.hpp>
#include <$D0948A3EE1071355FC80BB395E0B84E2.hpp>
#include <$D899D5588E0495DB272D6B262CA405F5.hpp>


START_ATF_NAMESPACE
    struct _D3DTLVERTEX
    {
        $91829873B94B3C28EA1CF55F9BB8D28E ___u0;
        $67E9C695EA16A9CA771F38DA1DA2EED4 ___u1;
        $D899D5588E0495DB272D6B262CA405F5 ___u2;
        $4227E445DA2FF1EBA7DB9512D01703C8 ___u3;
        $D0948A3EE1071355FC80BB395E0B84E2 ___u4;
        $7D7C45323A019F4043EE11E98D72E442 ___u5;
        $27084C353F8D83FFEFF8DBE111284AEC ___u6;
        $0E46A56A77182B87014A92C137928472 ___u7;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagBITMAPCOREHEADER
    {
        unsigned int bcSize;
        unsigned __int16 bcWidth;
        unsigned __int16 bcHeight;
        unsigned __int16 bcPlanes;
        unsigned __int16 bcBitCount;
    };
END_ATF_NAMESPACE

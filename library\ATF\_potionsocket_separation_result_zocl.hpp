// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _potionsocket_separation_result_zocl
    {
        char sErrorCode;
        unsigned __int16 wParentSerial;
        char byParentAmount;
        unsigned __int16 wChildSerial;
        char byChildAmount;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

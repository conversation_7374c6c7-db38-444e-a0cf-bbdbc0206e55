// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _attack_force_request_clzo
    {
        char byID;
        unsigned __int16 wIndex;
        __int16 zAreaPos[2];
        unsigned __int16 wForceSerial;
        unsigned __int16 wConsumeItemSerial[3];
        unsigned __int16 wEffBulletSerial;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

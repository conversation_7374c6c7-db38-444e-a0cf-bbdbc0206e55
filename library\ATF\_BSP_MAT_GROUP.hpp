// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _BSP_MAT_GROUP
    {
        unsigned __int16 Type;
        unsigned __int16 TriNum;
        __int16 MtlId;
        __int16 LgtId;
        float BBMin[3];
        float BBMax[3];
        float Origin[3];
        unsigned int VBMinIndex;
        unsigned int IBMinIndex;
        unsigned int VertexBufferId;
        unsigned int VCnt;
        unsigned int CFaceStartVId;
        void *MultiSourceUV;
        void *MultiSourceST;
        unsigned __int16 ObjectId;
        float CoronaAlpha;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

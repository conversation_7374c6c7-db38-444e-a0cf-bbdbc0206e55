// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum tagDBREASON
    {
      DBREASON_DELETED = 0x1,
      DBREASON_INSERTED = 0x2,
      DB<PERSON>ASON_MODIFIED = 0x3,
      <PERSON><PERSON>AS<PERSON>_REMOVEDFROMCURSOR = 0x4,
      DBREASON_MOVEDINCURSOR = 0x5,
      DBREASON_MOVE = 0x6,
      DBREASON_FIND = 0x7,
      DBREASON_NEWINDEX = 0x8,
      DBREASON_ROWFIXUP = 0x9,
      DBREASON_RECALC = 0xA,
      DBREASON_REFRESH = 0xB,
      DBREASON_NEWPARAMETERS = 0xC,
      DBREASON_SORTCHANGED = 0xD,
      DBREASON_FILTERCHANGED = 0xE,
      DBREASON_QUERYSPECCHANGED = 0xF,
      DBREASON_SEEK = 0x10,
      DBREASON_PERCENT = 0x11,
      <PERSON><PERSON><PERSON><PERSON>_FINDCRITERIACHANGED = 0x12,
      <PERSON><PERSON>AS<PERSON>_SETRANGECHANGED = 0x13,
      DBREASON_ADDNEW = 0x14,
      DBREASON_MOVEPERCENT = 0x15,
      DBREASON_BEGINTRANSACT = 0x16,
      DBREASON_ROLLBACK = 0x17,
      DBREASON_COMMIT = 0x18,
      DBREASON_CLOSE = 0x19,
      DBREASON_BULK_ERROR = 0x1A,
      DBREASON_BULK_NOTTRANSACTABLE = 0x1B,
      DBREASON_BULK_ABOUTTOEXECUTE = 0x1C,
      DBREASON_CANCELUPDATE = 0x1D,
      DBREASON_SETCOLUMN = 0x1E,
      DBREASON_EDIT = 0x1F,
      DBREASON_UNLOAD = 0x20,
    };
END_ATF_NAMESPACE

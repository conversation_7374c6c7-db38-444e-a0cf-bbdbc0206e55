// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _qry_case_amine_moveore
    {
        char bySubQryCase;
        char byCollisionType;
        char byRace;
        unsigned int dwGuildSerial;
        char bySColmID;
        unsigned int dwSK;
        char bySOverlapNum;
        char byDColmID;
        unsigned int dwDK;
        char byDOverlapNum;
    public:
        _qry_case_amine_moveore();
        void ctor__qry_case_amine_moveore();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_amine_moveore, 19>(), "_qry_case_amine_moveore");
END_ATF_NAMESPACE

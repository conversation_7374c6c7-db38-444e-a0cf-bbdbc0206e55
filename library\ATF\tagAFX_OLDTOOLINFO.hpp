// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <HWND__.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagAFX_OLDTOOLINFO
    {
        unsigned int cbSize;
        unsigned int uFlags;
        HWND__ *hwnd;
        unsigned int uId;
        tagRECT rect;
        HINSTANCE__ *hinst;
        char *lpszText;
    };
END_ATF_NAMESPACE

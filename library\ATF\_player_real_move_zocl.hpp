// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _player_real_move_zocl
    {
        unsigned __int16 wIndex;
        unsigned int dwSerial;
        unsigned __int16 dwEquipVer;
        char byRaceCode;
        __int16 zCur[3];
        __int16 zTar[2];
        unsigned __int16 wLastEffectCode;
        unsigned __int64 dwStateFlag;
        __int16 nAddSpeed;
        char byDirect;
        char byColor;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

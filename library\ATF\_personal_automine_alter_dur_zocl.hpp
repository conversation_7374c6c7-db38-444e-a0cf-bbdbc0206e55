// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _personal_automine_alter_dur_zocl
    {
        unsigned int dwObjSerial;
        unsigned __int16 wHPRate;
    public:
        _personal_automine_alter_dur_zocl();
        void ctor__personal_automine_alter_dur_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_personal_automine_alter_dur_zocl, 6>(), "_personal_automine_alter_dur_zocl");
END_ATF_NAMESPACE

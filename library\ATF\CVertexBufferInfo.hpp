// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CVertexBuffer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CVertexBufferctor_CVertexBuffer1_ptr = void (WINAPIV*)(void*);
        using CVertexBufferctor_CVertexBuffer1_clbk = void (WINAPIV*)(void*, CVertexBufferctor_CVertexBuffer1_ptr);
        using CVertexBufferInitVertexBuffer2_ptr = void (WINAPIV*)(struct CVertexBuffer*, int, int, uint32_t);
        using CVertexBufferInitVertexBuffer2_clbk = void (WINAPIV*)(struct CVertexBuffer*, int, int, uint32_t, CVertexBufferInitVertexBuffer2_ptr);
        using CVertexBufferReleaseVertexBuffer3_ptr = void (WINAPIV*)(struct CVertexBuffer*);
        using CVertexBufferReleaseVertexBuffer3_clbk = void (WINAPIV*)(struct CVertexBuffer*, CVertexBufferReleaseVertexBuffer3_ptr);
        using CVertexBufferVPLock4_ptr = uint8_t* (WINAPIV*)(struct CVertexBuffer*, int, int, uint32_t);
        using CVertexBufferVPLock4_clbk = uint8_t* (WINAPIV*)(struct CVertexBuffer*, int, int, uint32_t, CVertexBufferVPLock4_ptr);
        using CVertexBufferVPUnLock5_ptr = void (WINAPIV*)(struct CVertexBuffer*);
        using CVertexBufferVPUnLock5_clbk = void (WINAPIV*)(struct CVertexBuffer*, CVertexBufferVPUnLock5_ptr);
        
        using CVertexBufferdtor_CVertexBuffer6_ptr = void (WINAPIV*)(void*);
        using CVertexBufferdtor_CVertexBuffer6_clbk = void (WINAPIV*)(void*, CVertexBufferdtor_CVertexBuffer6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

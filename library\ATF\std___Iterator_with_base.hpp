// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Iterator_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Iterator_with_base<output_iterator_tag,void,void,void,void,_Iterator_base> : _Iterator_base
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std___Iterator_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Iterator_with_base<input_iterator_tag,char,__int64,char *,char &,_Iterator_base> : _Iterator_base
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std___Iterator_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Iterator_with_base<input_iterator_tag,wchar_t,__int64,wchar_t *,wchar_t &,_Iterator_base> : _Iterator_base
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagWINDOWINFO
    {
        unsigned int cbSize;
        tagRECT rcWindow;
        tagRECT rcClient;
        unsigned int dwStyle;
        unsigned int dwExStyle;
        unsigned int dwWindowStatus;
        unsigned int cxWindowBorders;
        unsigned int cyWindowBorders;
        unsigned __int16 atomWindowType;
        unsigned __int16 wCreatorVersion;
    };
END_ATF_NAMESPACE

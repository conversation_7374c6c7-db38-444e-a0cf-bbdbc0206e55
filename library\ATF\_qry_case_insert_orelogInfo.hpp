// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_insert_orelog.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_insert_orelogctor__qry_case_insert_orelog2_ptr = void (WINAPIV*)(struct _qry_case_insert_orelog*);
        using _qry_case_insert_orelogctor__qry_case_insert_orelog2_clbk = void (WINAPIV*)(struct _qry_case_insert_orelog*, _qry_case_insert_orelogctor__qry_case_insert_orelog2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataNULL.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataNULLctor_CNationSettingDataNULL2_ptr = void (WINAPIV*)(struct CNationSettingDataNULL*);
        using CNationSettingDataNULLctor_CNationSettingDataNULL2_clbk = void (WINAPIV*)(struct CNationSettingDataNULL*, CNationSettingDataNULLctor_CNationSettingDataNULL2_ptr);
        using CNationSettingDataNULLCheckEnterWorldRequest4_ptr = bool (WINAPIV*)(struct CNationSettingDataNULL*, int, char*);
        using CNationSettingDataNULLCheckEnterWorldRequest4_clbk = bool (WINAPIV*)(struct CNationSettingDataNULL*, int, char*, CNationSettingDataNULLCheckEnterWorldRequest4_ptr);
        using CNationSettingDataNULLCreateBilling6_ptr = struct CBilling* (WINAPIV*)(struct CNationSettingDataNULL*);
        using CNationSettingDataNULLCreateBilling6_clbk = struct CBilling* (WINAPIV*)(struct CNationSettingDataNULL*, CNationSettingDataNULLCreateBilling6_ptr);
        using CNationSettingDataNULLCreateComplete8_ptr = void (WINAPIV*)(struct CNationSettingDataNULL*, struct CPlayer*);
        using CNationSettingDataNULLCreateComplete8_clbk = void (WINAPIV*)(struct CNationSettingDataNULL*, struct CPlayer*, CNationSettingDataNULLCreateComplete8_ptr);
        using CNationSettingDataNULLCreateWorker10_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataNULL*);
        using CNationSettingDataNULLCreateWorker10_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataNULL*, CNationSettingDataNULLCreateWorker10_ptr);
        using CNationSettingDataNULLGetCashItemPrice12_ptr = int (WINAPIV*)(struct CNationSettingDataNULL*, struct _CashShop_str_fld*);
        using CNationSettingDataNULLGetCashItemPrice12_clbk = int (WINAPIV*)(struct CNationSettingDataNULL*, struct _CashShop_str_fld*, CNationSettingDataNULLGetCashItemPrice12_ptr);
        using CNationSettingDataNULLGetItemName14_ptr = char* (WINAPIV*)(struct CNationSettingDataNULL*, struct _NameTxt_fld*);
        using CNationSettingDataNULLGetItemName14_clbk = char* (WINAPIV*)(struct CNationSettingDataNULL*, struct _NameTxt_fld*, CNationSettingDataNULLGetItemName14_ptr);
        using CNationSettingDataNULLInit16_ptr = int (WINAPIV*)(struct CNationSettingDataNULL*);
        using CNationSettingDataNULLInit16_clbk = int (WINAPIV*)(struct CNationSettingDataNULL*, CNationSettingDataNULLInit16_ptr);
        using CNationSettingDataNULLIsApplyPcbangPrimium18_ptr = bool (WINAPIV*)(struct CNationSettingDataNULL*, struct CPlayer*);
        using CNationSettingDataNULLIsApplyPcbangPrimium18_clbk = bool (WINAPIV*)(struct CNationSettingDataNULL*, struct CPlayer*, CNationSettingDataNULLIsApplyPcbangPrimium18_ptr);
        using CNationSettingDataNULLIsNormalChar20_ptr = bool (WINAPIV*)(struct CNationSettingDataNULL*, wchar_t);
        using CNationSettingDataNULLIsNormalChar20_clbk = bool (WINAPIV*)(struct CNationSettingDataNULL*, wchar_t, CNationSettingDataNULLIsNormalChar20_ptr);
        using CNationSettingDataNULLIsPersonalFreeFixedAmountBillingType22_ptr = bool (WINAPIV*)(struct CNationSettingDataNULL*, int16_t*, int16_t*);
        using CNationSettingDataNULLIsPersonalFreeFixedAmountBillingType22_clbk = bool (WINAPIV*)(struct CNationSettingDataNULL*, int16_t*, int16_t*, CNationSettingDataNULLIsPersonalFreeFixedAmountBillingType22_ptr);
        using CNationSettingDataNULLLoop24_ptr = void (WINAPIV*)(struct CNationSettingDataNULL*);
        using CNationSettingDataNULLLoop24_clbk = void (WINAPIV*)(struct CNationSettingDataNULL*, CNationSettingDataNULLLoop24_ptr);
        using CNationSettingDataNULLNetClose26_ptr = void (WINAPIV*)(struct CNationSettingDataNULL*, struct CPlayer*);
        using CNationSettingDataNULLNetClose26_clbk = void (WINAPIV*)(struct CNationSettingDataNULL*, struct CPlayer*, CNationSettingDataNULLNetClose26_ptr);
        using CNationSettingDataNULLSendCashDBDSNRequest28_ptr = void (WINAPIV*)(struct CNationSettingDataNULL*);
        using CNationSettingDataNULLSendCashDBDSNRequest28_clbk = void (WINAPIV*)(struct CNationSettingDataNULL*, CNationSettingDataNULLSendCashDBDSNRequest28_ptr);
        using CNationSettingDataNULLSetUnitPassiveValue30_ptr = void (WINAPIV*)(struct CNationSettingDataNULL*, float*);
        using CNationSettingDataNULLSetUnitPassiveValue30_clbk = void (WINAPIV*)(struct CNationSettingDataNULL*, float*, CNationSettingDataNULLSetUnitPassiveValue30_ptr);
        using CNationSettingDataNULLValidMacAddress32_ptr = bool (WINAPIV*)(struct CNationSettingDataNULL*);
        using CNationSettingDataNULLValidMacAddress32_clbk = bool (WINAPIV*)(struct CNationSettingDataNULL*, CNationSettingDataNULLValidMacAddress32_ptr);
        
        using CNationSettingDataNULLdtor_CNationSettingDataNULL34_ptr = void (WINAPIV*)(struct CNationSettingDataNULL*);
        using CNationSettingDataNULLdtor_CNationSettingDataNULL34_clbk = void (WINAPIV*)(struct CNationSettingDataNULL*, CNationSettingDataNULLdtor_CNationSettingDataNULL34_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

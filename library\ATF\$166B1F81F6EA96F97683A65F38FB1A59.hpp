// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $166B1F81F6EA96F97683A65F38FB1A59
    {
        BYTE gap0[8];
        char *pbVal;
    };    
    static_assert(ATF::checkSize<$166B1F81F6EA96F97683A65F38FB1A59, 16>(), "$166B1F81F6EA96F97683A65F38FB1A59");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_nuclear_bomb_explosion_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _nuclear_bomb_explosion_result_zoclctor__nuclear_bomb_explosion_result_zocl2_ptr = void (WINAPIV*)(struct _nuclear_bomb_explosion_result_zocl*);
        using _nuclear_bomb_explosion_result_zoclctor__nuclear_bomb_explosion_result_zocl2_clbk = void (WINAPIV*)(struct _nuclear_bomb_explosion_result_zocl*, _nuclear_bomb_explosion_result_zoclctor__nuclear_bomb_explosion_result_zocl2_ptr);
        using _nuclear_bomb_explosion_result_zoclsize4_ptr = int (WINAPIV*)(struct _nuclear_bomb_explosion_result_zocl*);
        using _nuclear_bomb_explosion_result_zoclsize4_clbk = int (WINAPIV*)(struct _nuclear_bomb_explosion_result_zocl*, _nuclear_bomb_explosion_result_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

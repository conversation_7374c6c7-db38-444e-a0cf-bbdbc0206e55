// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <INTERNET_SCHEME.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct URL_COMPONENTSW
    {
        unsigned int dwStructSize;
        wchar_t *lpszScheme;
        unsigned int dwSchemeLength;
        INTERNET_SCHEME nScheme;
        wchar_t *lpszHostName;
        unsigned int dwHostNameLength;
        unsigned __int16 nPort;
        wchar_t *lpszUserName;
        unsigned int dwUserNameLength;
        wchar_t *lpszPassword;
        unsigned int dwPasswordLength;
        wchar_t *lpszUrlPath;
        unsigned int dwUrlPathLength;
        wchar_t *lpszExtraInfo;
        unsigned int dwExtraInfoLength;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_updatedrawguildbattlerank
    {
        char by1PRace;
        unsigned int dw1PGuildSerial;
        char by2PRace;
        unsigned int dw2PGuildSerial;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_updatedrawguildbattlerank, 16>(), "_qry_case_updatedrawguildbattlerank");
END_ATF_NAMESPACE

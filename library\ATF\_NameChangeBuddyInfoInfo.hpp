// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_NameChangeBuddyInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _NameChangeBuddyInfoInit2_ptr = void (WINAPIV*)(struct _NameChangeBuddyInfo*);
        using _NameChangeBuddyInfoInit2_clbk = void (WINAPIV*)(struct _NameChangeBuddyInfo*, _NameChangeBuddyInfoInit2_ptr);
        
        using _NameChangeBuddyInfoctor__NameChangeBuddyInfo4_ptr = void (WINAPIV*)(struct _NameChangeBuddyInfo*);
        using _NameChangeBuddyInfoctor__NameChangeBuddyInfo4_clbk = void (WINAPIV*)(struct _NameChangeBuddyInfo*, _NameChangeBuddyInfoctor__NameChangeBuddyInfo4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

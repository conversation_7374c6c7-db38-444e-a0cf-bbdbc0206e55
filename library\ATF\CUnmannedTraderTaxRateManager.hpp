// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuild.hpp>
#include <CLogFile.hpp>
#include <CMyTimer.hpp>
#include <TRC_AutoTrade.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderTaxRateManager
    {
        CMyTimer *m_pkTimer;
        std::vector<TRC_AutoTrade *> m_vecTRC;
    public:
        CUnmannedTraderTaxRateManager();
        void ctor_CUnmannedTraderTaxRateManager();
        int ChangeOwner(char byRace, struct CGuild* pGuild);
        bool CheatChangeTaxRate(char byRace, unsigned int dwNewTaxRate, char* pCheaterName);
        void CleanUp();
        void CompleteCreate(uint16_t wInx);
        void DQSCompleteInAtradTaxMoney(char byRace, char* pdata);
        static void Destroy();
        unsigned int GetSuggestedTime(char byRace);
        unsigned int GetTax(char byRace, unsigned int dwGuildSerial, unsigned int dwPrice);
        float GetTaxRate(char byRace);
        bool Init(struct CLogFile* pkLogger);
        static struct CUnmannedTraderTaxRateManager* Instance();
        bool IsOwnerGuild(char byRace, unsigned int dwGuildSerial);
        bool Load();
        void Loop();
        void SendTaxRate(int n, char byRace);
        void SendTaxRatePatriarch(int n, char byRace);
        void SetGuildMaintainMoney(char byRace, unsigned int dwTax, unsigned int dwSeller);
        void SetPatriarchTaxMoney(char byRace, unsigned int dwTax);
        void SetSuggested(char byRace, char byMatterType, unsigned int dwMatterDst, char* wszMatterDst, unsigned int dwNext);
        ~CUnmannedTraderTaxRateManager();
        void dtor_CUnmannedTraderTaxRateManager();
    };
END_ATF_NAMESPACE

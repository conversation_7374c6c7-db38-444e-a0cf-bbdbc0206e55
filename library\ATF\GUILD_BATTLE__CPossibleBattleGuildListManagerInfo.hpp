// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CPossibleBattleGuildListManager.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_ptr = void (WINAPIV*)();
            using GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_clbk = void (WINAPIV*)(GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_ptr = struct GUILD_BATTLE::CPossibleBattleGuildListManager* (WINAPIV*)();
            using GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_clbk = struct GUILD_BATTLE::CPossibleBattleGuildListManager* (WINAPIV*)(GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, char, char, uint16_t*);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, char, char, uint16_t*, GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, int, char, char, unsigned int);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, int, char, char, unsigned int, GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, int, char);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, int, char, GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, int, char);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, int, char, GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, int, char, char, unsigned int);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, int, char, char, unsigned int, GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_ptr);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_ptr);
            
            using GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*);
            using GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CPossibleBattleGuildListManager*, GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _darkhole_reward_message_inform_zocl
    {
        int bIsRewarded;
        unsigned int dwPartyMemberIndex;
        char byTableCode;
        unsigned __int16 wItemIndex;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

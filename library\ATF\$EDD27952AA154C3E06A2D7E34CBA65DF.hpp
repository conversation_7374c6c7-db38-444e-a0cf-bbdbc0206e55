// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$BD85036DCC0442AA317E9171D0F2179B.hpp>
#include <$CC608A129C88F312E26B04B0A6BA5EDC.hpp>


START_ATF_NAMESPACE
    union $EDD27952AA154C3E06A2D7E34CBA65DF
    {
        $BD85036DCC0442AA317E9171D0F2179B S_un_b;
        $CC608A129C88F312E26B04B0A6BA5EDC S_un_w;
        unsigned int S_addr;
    };
END_ATF_NAMESPACE

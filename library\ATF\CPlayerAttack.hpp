// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAttack.hpp>


START_ATF_NAMESPACE
    struct CPlayerAttack : CAttack
    {
        struct CPlayer *m_pAttPlayer;
    public:
        void AttackSkill(struct _attack_param* pParam, bool bUseEffBullet);
        void AttackUnit(struct _attack_param* pParam);
        CPlayerAttack(struct CCharacter* pThis);
        void ctor_CPlayerAttack(struct CCharacter* pThis);
        void WPActiveAttackForce(struct _attack_param* pParam);
        void WPActiveAttackSkill(struct _attack_param* pParam);
        int _CalcSkillAttPnt(bool bUseEffBullet);
    };    
    static_assert(ATF::checkSize<CPlayerAttack, 760>(), "CPlayerAttack");
END_ATF_NAMESPACE

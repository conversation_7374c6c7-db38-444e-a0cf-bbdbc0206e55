// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CItemStore.hpp>
#include <CLogFile.hpp>
#include <CMapItemStoreList.hpp>
#include <CMyTimer.hpp>
#include <CRecordData.hpp>
#include <_limit_item_db_data.hpp>
#include <_qry_case_all_store_limit_item.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CItemStoreManager
    {
        CLogFile *m_pkLogger;
        CMyTimer m_tmrCheckTime;
        CMyTimer m_tmrSaveTime;
        __int64 m_tNextInitTime;
        _qry_case_all_store_limit_item m_Sheet;
        int m_nInstanceItemStoreListNum;
        CMapItemStoreList *m_InstanceItemStoreList;
        CRecordData m_tblItemStore;
        int m_nMapItemStoreListNum;
        CMapItemStoreList *m_MapItemStoreList;
        CItemStore *m_pLimitInitNormalStore[3];
    public:
        CItemStoreManager();
        void ctor_CItemStoreManager();
        void CompleteDisableInstanceStore(char* pData);
        void CompleteStoreLimitItem();
        static void Destroy();
        struct CMapItemStoreList* GetEmptyInstanceItemStore();
        struct CMapItemStoreList* GetInstanceStoreListBySerial(int nSerial);
        struct CItemStore* GetMapItemStoreFromList(int nMapNum, int nStoreNum);
        struct CMapItemStoreList* GetMapItemStoreListByNum(int nNum);
        struct CMapItemStoreList* GetMapItemStoreListBySerial(int nSerial);
        bool Init(int nNormalListNum, int nInstanceListNum);
        bool InitLogger();
        bool InsertNotEnoughLimitItemRecord(int nNum);
        static struct CItemStoreManager* Instance();
        bool Load();
        void Log(char* fmt);
        void Loop();
        void MakeLimitItemUpdateQuery(unsigned int dwSerial, char byStoreType, int nTypeSerial, unsigned int dwStoreIndex, struct _limit_item_db_data* pItemData, uint64_t dwLimitInitTime, char* pszQuery, int nBufSize);
        bool ResetInstanceItemStore(char byStoreType, int nSerial);
        bool SelectStoreLimitItem();
        bool SelectTotalRecordNum(unsigned int* pdwTotalNum);
        bool SelectUsedRecordNum(unsigned int* pdwUsedNum);
        void SetEnforceInitNormalStore();
        void SetNextEnforceInitTime();
        void SetStoreLimitItemData(struct _qry_case_all_store_limit_item::__list* pData);
        void SetUpdateDBDataDoNotCheck();
        char UpdateDisableInstanceStore(char* pData);
        char UpdateStoreLimitItem();
        ~CItemStoreManager();
        void dtor_CItemStoreManager();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<CItemStoreManager, 0x138>(), "CItemStoreManager");
END_ATF_NAMESPACE

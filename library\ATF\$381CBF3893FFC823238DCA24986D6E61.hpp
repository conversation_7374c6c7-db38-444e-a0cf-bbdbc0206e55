// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$A04CD8C0331024E133CCEC4B0AB36956.hpp>


START_ATF_NAMESPACE
    union $381CBF3893FFC823238DCA24986D6E61
    {
        $A04CD8C0331024E133CCEC4B0AB36956 __s0;
        unsigned __int64 Lo64;
    };    
    static_assert(ATF::checkSize<$381CBF3893FFC823238DCA24986D6E61, 8>(), "$381CBF3893FFC823238DCA24986D6E61");
END_ATF_NAMESPACE

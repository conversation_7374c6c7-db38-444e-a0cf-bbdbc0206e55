// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum EVENT_TIME
    {
      Time_begin = 0x0,
      Time_end = 0x1,
      Time_all = 0x2,
    };
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    enum Event_Time
    {
      begin = 0x0,
      end = 0x1,
      all = 0x2,
    };
END_ATF_NAMESPACE

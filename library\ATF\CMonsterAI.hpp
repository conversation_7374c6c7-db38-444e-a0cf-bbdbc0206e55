// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPathMgr.hpp>
#include <SF_Timer.hpp>
#include <UsStateTBL.hpp>
#include <Us_HFSM.hpp>


START_ATF_NAMESPACE
    struct  CMonsterAI : Us_HFSM
    {
        SF_Timer m_SFCheckTime[4];
        struct CMonster *m_pAsistMonster;
        CPathMgr m_PathFinder;
        unsigned int m_dwBattleModeTime;
        int m_nCurPathFindFailCount;
    public:
        CMonsterAI();
        void ctor_CMonsterAI();
        unsigned int GetBattleModeTime();
        struct CPathMgr* GetPathFinder();
        struct SF_Timer* GetTimer(int nIndex);
        void Init();
        void SetBattleModeTime(unsigned int dwTempBattleModeTime);
        int SetMyData(struct UsStateTBL* pStateTBL, void* pObject);
        ~CMonsterAI();
        void dtor_CMonsterAI();
    };
END_ATF_NAMESPACE

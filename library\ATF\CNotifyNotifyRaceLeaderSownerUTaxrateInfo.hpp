// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNotifyNotifyRaceLeaderSownerUTaxrate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNotifyNotifyRaceLeaderSownerUTaxratector_CNotifyNotifyRaceLeaderSownerUTaxrate2_ptr = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*);
        using CNotifyNotifyRaceLeaderSownerUTaxratector_CNotifyNotifyRaceLeaderSownerUTaxrate2_clbk = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, CNotifyNotifyRaceLeaderSownerUTaxratector_CNotifyNotifyRaceLeaderSownerUTaxrate2_ptr);
        using CNotifyNotifyRaceLeaderSownerUTaxrateInit4_ptr = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*);
        using CNotifyNotifyRaceLeaderSownerUTaxrateInit4_clbk = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, CNotifyNotifyRaceLeaderSownerUTaxrateInit4_ptr);
        using CNotifyNotifyRaceLeaderSownerUTaxrateNotify6_ptr = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char);
        using CNotifyNotifyRaceLeaderSownerUTaxrateNotify6_clbk = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char, CNotifyNotifyRaceLeaderSownerUTaxrateNotify6_ptr);
        using CNotifyNotifyRaceLeaderSownerUTaxrateNotify8_ptr = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char, uint16_t);
        using CNotifyNotifyRaceLeaderSownerUTaxrateNotify8_clbk = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char, uint16_t, CNotifyNotifyRaceLeaderSownerUTaxrateNotify8_ptr);
        using CNotifyNotifyRaceLeaderSownerUTaxrateUpdateRaceLeader10_ptr = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char, char, char*);
        using CNotifyNotifyRaceLeaderSownerUTaxrateUpdateRaceLeader10_clbk = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char, char, char*, CNotifyNotifyRaceLeaderSownerUTaxrateUpdateRaceLeader10_ptr);
        using CNotifyNotifyRaceLeaderSownerUTaxrateUpdateSettlementOwner12_ptr = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char, unsigned int, unsigned int);
        using CNotifyNotifyRaceLeaderSownerUTaxrateUpdateSettlementOwner12_clbk = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char, unsigned int, unsigned int, CNotifyNotifyRaceLeaderSownerUTaxrateUpdateSettlementOwner12_ptr);
        using CNotifyNotifyRaceLeaderSownerUTaxrateUpdateTaxRate14_ptr = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char, char);
        using CNotifyNotifyRaceLeaderSownerUTaxrateUpdateTaxRate14_clbk = void (WINAPIV*)(struct CNotifyNotifyRaceLeaderSownerUTaxrate*, char, char, CNotifyNotifyRaceLeaderSownerUTaxrateUpdateTaxRate14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaCommand.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CLuaCommandEx : CLuaCommand
    {
        struct _State
        {
            bool m_bExist;
        public:
            _State();
            void ctor__State();
        };
        char m_strScriptName[260];
    public:
        CLuaCommandEx();
        void ctor_CLuaCommandEx();
        char* GetScriptName();
        void SetCmd(char byType, char* strScriptName, char* strName);
        ~CLuaCommandEx();
        void dtor_CLuaCommandEx();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

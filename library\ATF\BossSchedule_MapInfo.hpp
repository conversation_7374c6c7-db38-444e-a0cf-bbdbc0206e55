// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <BossSchedule_Map.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using BossSchedule_Mapctor_BossSchedule_Map2_ptr = void (WINAPIV*)(struct BossSchedule_Map*);
        using BossSchedule_Mapctor_BossSchedule_Map2_clbk = void (WINAPIV*)(struct BossSchedule_Map*, BossSchedule_Mapctor_BossSchedule_Map2_ptr);
        using BossSchedule_MapClear4_ptr = void (WINAPIV*)(struct BossSchedule_Map*);
        using BossSchedule_MapClear4_clbk = void (WINAPIV*)(struct BossSchedule_Map*, BossSchedule_MapClear4_ptr);
        using BossSchedule_MapLoadAll6_ptr = bool (WINAPIV*)(struct BossSchedule_Map*);
        using BossSchedule_MapLoadAll6_clbk = bool (WINAPIV*)(struct BossSchedule_Map*, BossSchedule_MapLoadAll6_ptr);
        using BossSchedule_MapSaveAll8_ptr = bool (WINAPIV*)(struct BossSchedule_Map*);
        using BossSchedule_MapSaveAll8_clbk = bool (WINAPIV*)(struct BossSchedule_Map*, BossSchedule_MapSaveAll8_ptr);
        
        using BossSchedule_Mapdtor_BossSchedule_Map12_ptr = void (WINAPIV*)(struct BossSchedule_Map*);
        using BossSchedule_Mapdtor_BossSchedule_Map12_clbk = void (WINAPIV*)(struct BossSchedule_Map*, BossSchedule_Mapdtor_BossSchedule_Map12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGameObject.hpp>
#include <CGuild.hpp>
#include <CPlayer.hpp>


START_ATF_NAMESPACE
    struct  CGuildBattleController
    {
    public:
        char Add(struct CGuild* pSrcGuild, struct CGuild* pDestGuild, unsigned int dwStartTime, char by<PERSON><PERSON>ber, unsigned int dwMapInx);
        char Add(struct CGuild* pSrcGuild, struct CGuild* pDestGuild, unsigned int dwStartTime, unsigned int dwElapseTimeCnt, char byNumber, unsigned int dwMapInx);
        void AddComplete(char byR<PERSON>ult, unsigned int uiMapID, unsigned int dwID, unsigned int dwSLID);
        char AddSchedule(char* szData);
        CGuildBattleController();
        void ctor_CGuildBattleController();
        bool CheatCreateFieldObject(struct CPlayer* pkPlayer);
        bool CheatDestroyFieldObject(struct CPlayer* pkPlayer);
        bool CheatDestroyStone(struct CPlayer* pkPlayer);
        bool CheatDropStone(struct CPlayer* pkPlayer);
        bool CheatForceTakeStone(struct CPlayer* pkPlayer);
        bool CheatGetStone(struct CPlayer* pkPlayer);
        int CheatRegenStone(struct CPlayer* pkPlayer, int iRengenPos);
        bool CheatTakeStone(int iPortalInx, struct CPlayer* pkPlayer);
        void CheckGetGravityStone(uint16_t wIndex, unsigned int dwObjSerial, struct CPlayer* pkPlayer);
        void CheckGoal(struct CPlayer* pkPlayer, int iPortalInx);
        void CheckTakeGravityStone(int iPortalInx, struct CPlayer* pkPlayer);
        void CleanUp();
        void Clear();
        void CompleteClearGuildBattleRank(char byResult);
        void CompleteCreateGuildBattleRankTable(char byResult);
        void CompleteUpdateRank(char byResult, char byRace, char* pLoadData);
        void CompleteUpdateReservedSchedule(unsigned int dwMapID, char* pLoadData);
        static void Destroy();
        void DropGravityStone(struct CPlayer* pkPlayer);
        void Flip();
        struct CGameObject* GetCircleZone(int iInx);
        struct CGameObject* GetRegener(int iInx);
        struct CGameObject* GetStone(int iInx);
        bool Init();
        static struct CGuildBattleController* Instance();
        char IsAvailableSuggest(struct CGuild* pSrcGuild, unsigned int dwDestGuild, unsigned int dwStartTime, unsigned int dwNumber, unsigned int dwMapCode);
        void Join(struct CPlayer* pkPlayer);
        void JoinGuild(int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial);
        void Kill(struct CPlayer* pkSrcPlayer, struct CPlayer* pkDestPlayer);
        void LeaveGuild(struct CPlayer* pkPlayer);
        bool Load();
        bool LoadINI(unsigned int* uiMapCnt, int* iToday, int* iTodayDayID, int* iTomorrow, int* iTomorrowDayID);
        void LogIn(struct CPlayer* pkPlayer);
        void Loop();
        void NetClose(struct CPlayer* pkPlayer);
        void PushClearGuildBattleRank();
        void PushCreateGuildBattleRankTable();
        bool SaveINI();
        void SendCurrentBattleInfoRequest(int n, unsigned int uiMapID);
        void SendPossibleBattleGuildList(int n, char byRace, char byPage, unsigned int dwVer);
        void SendPossibleBattleGuildListFirst(int n, char byRace);
        void SendRankList(int n, char bySelfRace, unsigned int dwCurVer, unsigned int uiMapID, char byPage, unsigned int dwGuildSerial);
        void SendReservedScheduleList(int n, unsigned int uiMapID, unsigned int dwVer, char byDay, char byPage, unsigned int dwGuildSerial);
        char Start(struct CPlayer* pkPlayer);
        bool UpdateClearRerservedDayInfo(unsigned int dwStartSLID, unsigned int dwEndSLID, unsigned int dwStartSID, unsigned int dwEndSID);
        bool UpdateDraw(char by1PRace, unsigned int dw1PGuildSerial, char by2PRace, unsigned int dw2PGuildSerial);
        void UpdatePossibleBattleGuildList();
        bool UpdateRank(char byRace, char* byOutData);
        bool UpdateReservedGuildBattleSchedule(unsigned int dwSLID, char* byOutData);
        bool UpdateWinLose(char byWinRace, unsigned int dwWinGuildSerial, char byLoseRace, unsigned int dwLoseGuildSerial);
        ~CGuildBattleController();
        void dtor_CGuildBattleController();
    };
END_ATF_NAMESPACE

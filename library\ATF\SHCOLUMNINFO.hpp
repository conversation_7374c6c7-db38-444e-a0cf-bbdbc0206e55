// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__MIDL___MIDL_itf_shobjidl_0201_0002.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  SHCOLUMNINFO
    {
        __MIDL___MIDL_itf_shobjidl_0201_0002 scid;
        unsigned __int16 vt;
        unsigned int fmt;
        unsigned int cChars;
        unsigned int csFlags;
        wchar_t wszTitle[80];
        wchar_t wszDescription[128];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPvpCashMng.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CPvpCashMngctor_CPvpCashMng2_ptr = void (WINAPIV*)(struct CPvpCashMng*);
        using CPvpCashMngctor_CPvpCashMng2_clbk = void (WINAPIV*)(struct CPvpCashMng*, CPvpCashMngctor_CPvpCashMng2_ptr);
        using CPvpCashMngGetMaxTempPoint4_ptr = int (WINAPIV*)(struct CPvpCashMng*, char, bool);
        using CPvpCashMngGetMaxTempPoint4_clbk = int (WINAPIV*)(struct CPvpCashMng*, char, bool, CPvpCashMngGetMaxTempPoint4_ptr);
        using CPvpCashMngGetMinTempPoint6_ptr = int (WINAPIV*)(struct CPvpCashMng*, char);
        using CPvpCashMngGetMinTempPoint6_clbk = int (WINAPIV*)(struct CPvpCashMng*, char, CPvpCashMngGetMinTempPoint6_ptr);
        using CPvpCashMngGetMyClassVal8_ptr = int (WINAPIV*)(struct CPvpCashMng*, char*);
        using CPvpCashMngGetMyClassVal8_clbk = int (WINAPIV*)(struct CPvpCashMng*, char*, CPvpCashMngGetMyClassVal8_ptr);
        using CPvpCashMngGetTalikNum10_ptr = int (WINAPIV*)(struct CPvpCashMng*);
        using CPvpCashMngGetTalikNum10_clbk = int (WINAPIV*)(struct CPvpCashMng*, CPvpCashMngGetTalikNum10_ptr);
        using CPvpCashMngGetTalikRecvrPoint12_ptr = int (WINAPIV*)(struct CPvpCashMng*, int);
        using CPvpCashMngGetTalikRecvrPoint12_clbk = int (WINAPIV*)(struct CPvpCashMng*, int, CPvpCashMngGetTalikRecvrPoint12_ptr);
        using CPvpCashMngGetTalikRecvrPoint14_ptr = int (WINAPIV*)(struct CPvpCashMng*, char, unsigned int);
        using CPvpCashMngGetTalikRecvrPoint14_clbk = int (WINAPIV*)(struct CPvpCashMng*, char, unsigned int, CPvpCashMngGetTalikRecvrPoint14_ptr);
        using CPvpCashMngInstance16_ptr = struct CPvpCashMng* (WINAPIV*)();
        using CPvpCashMngInstance16_clbk = struct CPvpCashMng* (WINAPIV*)(CPvpCashMngInstance16_ptr);
        using CPvpCashMngIsTalikItem18_ptr = bool (WINAPIV*)(struct CPvpCashMng*, char*);
        using CPvpCashMngIsTalikItem18_clbk = bool (WINAPIV*)(struct CPvpCashMng*, char*, CPvpCashMngIsTalikItem18_ptr);
        using CPvpCashMngLoadData20_ptr = bool (WINAPIV*)(struct CPvpCashMng*);
        using CPvpCashMngLoadData20_clbk = bool (WINAPIV*)(struct CPvpCashMng*, CPvpCashMngLoadData20_ptr);
        using CPvpCashMngParsing22_ptr = bool (WINAPIV*)(struct CPvpCashMng*, char*, char*, char**, int, char);
        using CPvpCashMngParsing22_clbk = bool (WINAPIV*)(struct CPvpCashMng*, char*, char*, char**, int, char, CPvpCashMngParsing22_ptr);
        using CPvpCashMngRelease24_ptr = void (WINAPIV*)(struct CPvpCashMng*);
        using CPvpCashMngRelease24_clbk = void (WINAPIV*)(struct CPvpCashMng*, CPvpCashMngRelease24_ptr);
        using CPvpCashMngSetItem26_ptr = bool (WINAPIV*)(struct CPvpCashMng*, char*, int);
        using CPvpCashMngSetItem26_clbk = bool (WINAPIV*)(struct CPvpCashMng*, char*, int, CPvpCashMngSetItem26_ptr);
        
        using CPvpCashMngdtor_CPvpCashMng30_ptr = void (WINAPIV*)(struct CPvpCashMng*);
        using CPvpCashMngdtor_CPvpCashMng30_clbk = void (WINAPIV*)(struct CPvpCashMng*, CPvpCashMngdtor_CPvpCashMng30_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _MEMORYSTATUS
    {
        unsigned int dwLength;
        unsigned int dwMemoryLoad;
        unsigned __int64 dwTotalPhys;
        unsigned __int64 dwAvailPhys;
        unsigned __int64 dwTotalPageFile;
        unsigned __int64 dwAvailPageFile;
        unsigned __int64 dwTotalVirtual;
        unsigned __int64 dwAvailVirtual;
    };    
    static_assert(ATF::checkSize<_MEMORYSTATUS, 56>(), "_MEMORYSTATUS");
END_ATF_NAMESPACE

#include "stdafx.h"
#include "LootExchange.h"
#include "../../Common/ETypes.h"
#include "../../Common/Helpers/RapidHelper.hpp"

#include <bitset>
#include <fstream>
#include <sstream>
#include <regex>
#include <map>
#include <ATF/global.hpp>

namespace GameServer
{
    namespace Addon
    {
        // Static member definitions for custom mapping system
        bool CLootExchange::m_bUseCustomMappings = false;
        std::vector<CLootExchange::ItemMapping> CLootExchange::m_vItemMappings;
        std::vector<CLootExchange::TableMapping> CLootExchange::m_vTableMappings;
        std::vector<CLootExchange::CategoryMapping> CLootExchange::m_vCategoryMappings;
        std::vector<CLootExchange::ConditionalMapping> CLootExchange::m_vConditionalMappings;
        std::vector<std::string> CLootExchange::m_vExclusionList;
        std::map<std::string, std::vector<int>> CLootExchange::m_mPriorityOverrides;

        void CLootExchange::LoadItemMappings()
        {
            const std::string iniPath = "./YorozuyaGS/Configuration/LootExchange.ini";
            std::ifstream file(iniPath);
            if (!file.is_open()) return;

            std::string line, currentSection;
            bool inItemMappings = false;

            while (std::getline(file, line)) {
                // Remove whitespace and comments
                line.erase(0, line.find_first_not_of(" \t"));
                line.erase(line.find_last_not_of(" \t") + 1);
                if (line.empty() || line[0] == ';') continue;

                // Check for section
                if (line[0] == '[' && line.back() == ']') {
                    currentSection = line.substr(1, line.length() - 2);
                    inItemMappings = (currentSection == "ItemMappings");
                    continue;
                }

                if (inItemMappings) {
                    size_t pos = line.find('=');
                    if (pos != std::string::npos) {
                        std::string itemCode = line.substr(0, pos);
                        std::string mapping = line.substr(pos + 1);

                        // Remove whitespace
                        itemCode.erase(itemCode.find_last_not_of(" \t") + 1);
                        mapping.erase(0, mapping.find_first_not_of(" \t"));

                        // Parse mapping (format: CurrencyType:Value)
                        size_t colonPos = mapping.find(':');
                        if (colonPos != std::string::npos) {
                            try {
                                ItemMapping itemMapping;
                                itemMapping.itemCode = itemCode;
                                itemMapping.currencyType = std::stoi(mapping.substr(0, colonPos));
                                itemMapping.value = std::stoi(mapping.substr(colonPos + 1));
                                itemMapping.useDbValue = (itemMapping.value == 0);

                                m_vItemMappings.push_back(itemMapping);

                                if (m_bEnableLogging) {
                                    std::stringstream logMsg;
                                    logMsg << "Loaded item mapping: " << itemCode << " -> Currency " 
                                           << itemMapping.currencyType << ", Value " << itemMapping.value;
                                    LogExchange(logMsg.str());
                                }
                            }
                            catch (const std::exception& e) {
                                if (m_bEnableLogging) {
                                    LogExchange("Error parsing item mapping: " + line);
                                }
                            }
                        }
                    }
                }
            }
        }

        void CLootExchange::LoadTableMappings()
        {
            const std::string iniPath = "./YorozuyaGS/Configuration/LootExchange.ini";
            std::ifstream file(iniPath);
            if (!file.is_open()) return;

            std::string line, currentSection;
            bool inTableMappings = false;

            while (std::getline(file, line)) {
                line.erase(0, line.find_first_not_of(" \t"));
                line.erase(line.find_last_not_of(" \t") + 1);
                if (line.empty() || line[0] == ';') continue;

                if (line[0] == '[' && line.back() == ']') {
                    currentSection = line.substr(1, line.length() - 2);
                    inTableMappings = (currentSection == "TableMappings");
                    continue;
                }

                if (inTableMappings) {
                    size_t pos = line.find('=');
                    if (pos != std::string::npos) {
                        std::string tableStr = line.substr(0, pos);
                        std::string mapping = line.substr(pos + 1);

                        tableStr.erase(tableStr.find_last_not_of(" \t") + 1);
                        mapping.erase(0, mapping.find_first_not_of(" \t"));

                        // Parse mapping (format: CurrencyType:ValueMultiplier)
                        size_t colonPos = mapping.find(':');
                        if (colonPos != std::string::npos) {
                            try {
                                TableMapping tableMapping;
                                tableMapping.tableCode = std::stoi(tableStr);
                                tableMapping.currencyType = std::stoi(mapping.substr(0, colonPos));
                                tableMapping.valueMultiplier = std::stoi(mapping.substr(colonPos + 1));

                                m_vTableMappings.push_back(tableMapping);

                                if (m_bEnableLogging) {
                                    std::stringstream logMsg;
                                    logMsg << "Loaded table mapping: Table " << (int)tableMapping.tableCode 
                                           << " -> Currency " << tableMapping.currencyType 
                                           << ", Multiplier " << tableMapping.valueMultiplier << "%";
                                    LogExchange(logMsg.str());
                                }
                            }
                            catch (const std::exception& e) {
                                if (m_bEnableLogging) {
                                    LogExchange("Error parsing table mapping: " + line);
                                }
                            }
                        }
                    }
                }
            }
        }

        void CLootExchange::LoadCategoryMappings()
        {
            const std::string iniPath = "./YorozuyaGS/Configuration/LootExchange.ini";
            std::ifstream file(iniPath);
            if (!file.is_open()) return;

            std::string line, currentSection;
            bool inCategoryMappings = false;

            while (std::getline(file, line)) {
                line.erase(0, line.find_first_not_of(" \t"));
                line.erase(line.find_last_not_of(" \t") + 1);
                if (line.empty() || line[0] == ';') continue;

                if (line[0] == '[' && line.back() == ']') {
                    currentSection = line.substr(1, line.length() - 2);
                    inCategoryMappings = (currentSection == "CategoryMappings");
                    continue;
                }

                if (inCategoryMappings) {
                    size_t pos = line.find('=');
                    if (pos != std::string::npos) {
                        std::string categoryName = line.substr(0, pos);
                        std::string mapping = line.substr(pos + 1);

                        categoryName.erase(categoryName.find_last_not_of(" \t") + 1);
                        mapping.erase(0, mapping.find_first_not_of(" \t"));

                        // Parse mapping (format: CurrencyType:ValueMultiplier:TableList)
                        std::vector<std::string> parts;
                        std::stringstream ss(mapping);
                        std::string item;
                        while (std::getline(ss, item, ':')) {
                            parts.push_back(item);
                        }

                        if (parts.size() >= 3) {
                            try {
                                CategoryMapping categoryMapping;
                                categoryMapping.categoryName = categoryName;
                                categoryMapping.currencyType = std::stoi(parts[0]);
                                categoryMapping.valueMultiplier = std::stoi(parts[1]);

                                // Parse table list
                                std::stringstream tableStream(parts[2]);
                                std::string tableStr;
                                while (std::getline(tableStream, tableStr, ',')) {
                                    categoryMapping.tableList.push_back(std::stoi(tableStr));
                                }

                                m_vCategoryMappings.push_back(categoryMapping);

                                if (m_bEnableLogging) {
                                    std::stringstream logMsg;
                                    logMsg << "Loaded category mapping: " << categoryName 
                                           << " -> Currency " << categoryMapping.currencyType 
                                           << ", Multiplier " << categoryMapping.valueMultiplier << "%";
                                    LogExchange(logMsg.str());
                                }
                            }
                            catch (const std::exception& e) {
                                if (m_bEnableLogging) {
                                    LogExchange("Error parsing category mapping: " + line);
                                }
                            }
                        }
                    }
                }
            }
        }

        void CLootExchange::LoadExclusionList()
        {
            const std::string iniPath = "./YorozuyaGS/Configuration/LootExchange.ini";
            std::ifstream file(iniPath);
            if (!file.is_open()) return;

            std::string line, currentSection;
            bool inExclusionList = false;

            while (std::getline(file, line)) {
                line.erase(0, line.find_first_not_of(" \t"));
                line.erase(line.find_last_not_of(" \t") + 1);
                if (line.empty() || line[0] == ';') continue;

                if (line[0] == '[' && line.back() == ']') {
                    currentSection = line.substr(1, line.length() - 2);
                    inExclusionList = (currentSection == "ExclusionList");
                    continue;
                }

                if (inExclusionList) {
                    m_vExclusionList.push_back(line);
                    if (m_bEnableLogging) {
                        LogExchange("Added to exclusion list: " + line);
                    }
                }
            }
        }

        bool CLootExchange::MatchesPattern(const std::string& pattern, const std::string& text)
        {
            // Simple wildcard matching (* and ?)
            if (pattern.find('*') == std::string::npos && pattern.find('?') == std::string::npos) {
                return pattern == text;
            }

            // Convert wildcard pattern to regex
            std::string regexPattern = pattern;

            // Escape special regex characters except * and ?
            std::string specialChars = ".^$+{}[]|()\\";
            for (char c : specialChars) {
                size_t pos = 0;
                std::string target(1, c);
                std::string replacement = "\\" + target;
                while ((pos = regexPattern.find(target, pos)) != std::string::npos) {
                    regexPattern.replace(pos, 1, replacement);
                    pos += replacement.length();
                }
            }

            // Convert wildcards to regex
            size_t pos = 0;
            while ((pos = regexPattern.find("\\*", pos)) != std::string::npos) {
                regexPattern.replace(pos, 2, ".*");
                pos += 2;
            }
            pos = 0;
            while ((pos = regexPattern.find("\\?", pos)) != std::string::npos) {
                regexPattern.replace(pos, 2, ".");
                pos += 1;
            }

            try {
                std::regex regex(regexPattern);
                return std::regex_match(text, regex);
            }
            catch (const std::exception&) {
                return false;
            }
        }

        bool CLootExchange::IsItemExcluded(const std::string& itemCode, char byTableCode, uint16_t wItemIndex)
        {
            std::string tableIndexCode = std::to_string((int)byTableCode) + ":" + std::to_string(wItemIndex);

            for (const std::string& exclusion : m_vExclusionList) {
                if (MatchesPattern(exclusion, itemCode) || MatchesPattern(exclusion, tableIndexCode)) {
                    return true;
                }
            }
            return false;
        }

        bool CLootExchange::GetCustomMapping(ATF::CPlayer* pPlayer, ATF::_base_fld* pRecord, char byTableCode, uint16_t wItemIndex, uint32_t& outCurrencyType, int& outValue)
        {
            if (!m_bUseCustomMappings) {
                return false;
            }

            // Generate item identifiers
            std::string itemCode = "item_" + std::to_string((int)byTableCode) + "_" + std::to_string(wItemIndex);
            std::string tableIndexCode = std::to_string((int)byTableCode) + ":" + std::to_string(wItemIndex);

            // Check exclusion list first
            if (IsItemExcluded(itemCode, byTableCode, wItemIndex)) {
                return false;
            }

            // 1. Check specific item mappings first (highest priority)
            for (const ItemMapping& mapping : m_vItemMappings) {
                if (MatchesPattern(mapping.itemCode, itemCode) || MatchesPattern(mapping.itemCode, tableIndexCode)) {
                    outCurrencyType = mapping.currencyType;
                    if (mapping.useDbValue) {
                        outValue = GetMoneyValue(pRecord, byTableCode, mapping.currencyType, pPlayer->GetObjRace());
                    } else {
                        outValue = mapping.value;
                    }

                    if (m_bEnableLogging) {
                        std::stringstream logMsg;
                        logMsg << "Applied item mapping for " << itemCode << ": Currency " << outCurrencyType << ", Value " << outValue;
                        LogExchange(logMsg.str());
                    }
                    return true;
                }
            }

            // 2. Check table mappings
            for (const TableMapping& mapping : m_vTableMappings) {
                if (mapping.tableCode == byTableCode) {
                    outCurrencyType = mapping.currencyType;
                    int baseValue = GetMoneyValue(pRecord, byTableCode, mapping.currencyType, pPlayer->GetObjRace());
                    outValue = (baseValue * mapping.valueMultiplier) / 100;

                    if (m_bEnableLogging) {
                        std::stringstream logMsg;
                        logMsg << "Applied table mapping for table " << (int)byTableCode << ": Currency " << outCurrencyType << ", Value " << outValue;
                        LogExchange(logMsg.str());
                    }
                    return true;
                }
            }

            // 3. Check category mappings
            for (const CategoryMapping& mapping : m_vCategoryMappings) {
                for (uint8_t tableCode : mapping.tableList) {
                    if (tableCode == byTableCode) {
                        outCurrencyType = mapping.currencyType;
                        int baseValue = GetMoneyValue(pRecord, byTableCode, mapping.currencyType, pPlayer->GetObjRace());
                        outValue = (baseValue * mapping.valueMultiplier) / 100;

                        if (m_bEnableLogging) {
                            std::stringstream logMsg;
                            logMsg << "Applied category mapping '" << mapping.categoryName << "' for table " << (int)byTableCode
                                   << ": Currency " << outCurrencyType << ", Value " << outValue;
                            LogExchange(logMsg.str());
                        }
                        return true;
                    }
                }
            }

            return false; // No custom mapping found
        }

        std::vector<int> CLootExchange::GetItemPriority(const std::string& itemCode, char byTableCode, uint16_t wItemIndex)
        {
            std::string tableIndexCode = std::to_string((int)byTableCode) + ":" + std::to_string(wItemIndex);

            // Check for specific overrides
            for (const auto& override : m_mPriorityOverrides) {
                if (MatchesPattern(override.first, itemCode) || MatchesPattern(override.first, tableIndexCode)) {
                    return override.second;
                }
            }

            // Return default priority
            return m_vMoneyTypePriority;
        }

    }
}

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_lobby_logout
    {
        struct _reged_list
        {
            char bySlotIndex;
            unsigned int dwCharSerial;
            char szCharName[17];
            int nLevel;
            unsigned int dwDalant;
            unsigned int dwGold;
        };
        unsigned int dwAccountSerial;
        char szLobbyHistoryFileName[64];
        char byDBRet;
        int nRegeNum;
        _reged_list RegeList[3];
    public:
        _qry_case_lobby_logout();
        void ctor__qry_case_lobby_logout();
        void init();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_lobby_logout, 196>(), "_qry_case_lobby_logout");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union $FA885138A45ED3CE38760E5920CA9439
    {
        unsigned int dwMipMapCount;
        unsigned int dwRefreshRate;
        unsigned int dwSrcVBHandle;
    };    
    static_assert(ATF::checkSize<$FA885138A45ED3CE38760E5920CA9439, 4>(), "$FA885138A45ED3CE38760E5920CA9439");
END_ATF_NAMESPACE

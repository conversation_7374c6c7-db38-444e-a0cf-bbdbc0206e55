// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNoTrackObject.hpp>
#include <HINSTANCE__.hpp>


START_ATF_NAMESPACE
    struct  _AFX_SOCK_STATE : CNoTrackObject
    {
        unsigned int m_dwReserved1;
        HINSTANCE__ *m_hInstSOCK;
        void (WINAPIV *m_pfnSockTerm)();
    };
END_ATF_NAMESPACE

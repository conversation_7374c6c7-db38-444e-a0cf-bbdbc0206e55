// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SOCKET_ADDRESS.hpp>


START_ATF_NAMESPACE
    struct _INTERFACE_INFO_EX
    {
        unsigned int iiFlags;
        _SOCKET_ADDRESS iiAddress;
        _SOCKET_ADDRESS iiBroadcastAddress;
        _SOCKET_ADDRESS iiNetmask;
    };
END_ATF_NAMESPACE

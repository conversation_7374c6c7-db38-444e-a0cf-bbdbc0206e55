// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _guild_battle_pvp_inform_zocl
    {
        unsigned int dwLeftRedScore;
        unsigned int dwRightBlueScore;
        char byLeftHour;
        char byLeftMin;
        char byLeftSec;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CAsyncLogBuffer
    {
        enum ALB_STATE
        {
            ALBS_NONE = 0xFFFFFFFF,
            ALBS_WAIT = 0x0,
            ALBS_SET = 0x1,
            ALBS_COMPLETE = 0x2,
        };
        ALB_STATE m_eState;
        char m_szFileName[260];
        int m_iLen;
        int m_iMaxBuffSize;
        char *m_szBuffer;
    public:
        CAsyncLogBuffer();
        void ctor_CAsyncLogBuffer();
        char* GetFileName();
        int GetLength();
        char* GetStr();
        bool Init(int iMaxBufferSize);
        bool Log(char* pszFileName, char* szLog, int iLen);
        ~CAsyncLogBuffer();
        void dtor_CAsyncLogBuffer();
    };
END_ATF_NAMESPACE

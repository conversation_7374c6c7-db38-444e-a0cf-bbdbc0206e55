// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HRESULT.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagSOLE_AUTHENTICATION_SERVICE
    {
        unsigned int dwAuthnSvc;
        unsigned int dwAuthzSvc;
        wchar_t *pPrincipalName;
        HRESULT hr;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

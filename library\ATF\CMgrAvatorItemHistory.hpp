// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CFrameRate.hpp>
#include <CMyTimer.hpp>
#include <CNetIndexList.hpp>
#include <CPostData.hpp>
#include <CPostReturnStorage.hpp>
#include <CPostStorage.hpp>
#include <CUnmannedTraderRegistItemInfo.hpp>
#include <_AVATOR_DATA.hpp>
#include <_ITEMCOMBINE_DB_BASE.hpp>
#include <_STORAGE_LIST.hpp>
#include <_UNIT_DB_BASE.hpp>
#include <_buy_offer.hpp>
#include <_personal_amine_inven_db_load.hpp>
#include <_sell_offer.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMgrAvatorItemHistory
    {
        struct __LOG_DATA
        {
            char szFileName[64];
            int nLen;
        };
        struct  __LOG_DATA_10K : __LOG_DATA
        {
            char sData[10000];
        };
        struct  __LOG_DATA_1K : __LOG_DATA
        {
            char sData[1000];
        };
        struct  __LOG_DATA_200 : __LOG_DATA
        {
            char sData[200];
        };
        char m_szStdPath[128];
        unsigned int m_dwLastLocalDate;
        unsigned int m_dwLastLocalHour;
        CMyTimer m_tmrUpdateTime;
        char m_szCurDate[32];
        char m_szCurTime[32];
        __LOG_DATA_10K m_LogData_10K[254];
        CNetIndexList m_listLogData_10K;
        CNetIndexList m_listLogDataEmpty_10K;
        __LOG_DATA_1K m_LogData_1K[254];
        CNetIndexList m_listLogData_1K;
        CNetIndexList m_listLogDataEmpty_1K;
        __LOG_DATA_200 m_LogData_200[2532];
        CNetIndexList m_listLogData_200;
        CNetIndexList m_listLogDataEmpty_200;
        bool m_bIOThread;
        CFrameRate m_FrameRate;
    public:
        CMgrAvatorItemHistory();
        void ctor_CMgrAvatorItemHistory();
        void ClassUP(char byCurClassGrade, char byLastClassGrade, char* szOldClass, char* szCurClass, int* piOldMaxPoint, int* piAlterMaxPoint, char* pszFileName);
        void ClearLogBuffer();
        void GetNewFileName(unsigned int dwAvatorSerial, char* pszFileName);
        int GetTotalWaitSize();
        static void IOThread(void* pv);
        void InitClass(int iCostGold, unsigned int dwInitClassCnt, char byLastClassGrade, char* szOldClass, char* szCurClass, int* piOldMaxPoint, int* piAlterMaxPoint, char* pszFileName);
        void OnLoop();
        void WriteFile(char* pszFileName, char* pszLog);
        void WriteLog(char* pszFileName);
        void add_storage_fail(int n, struct _STORAGE_LIST::_db_con* pItem, char* strErrorCodePos, char* pszFileName);
        void auto_trade_buy(char* szSellerName, unsigned int dwSellerSerial, char* szSellerID, unsigned int dwRegistSerial, struct _STORAGE_LIST::_db_con* pItem, unsigned int dwPrice, unsigned int dwLeftDalant, unsigned int dwLeftGold, char* pszFileName);
        void auto_trade_login_sell(char* szBuyerName, unsigned int dwBuyerSerial, char* szBuyerID, unsigned int dwRegistSerial, struct _STORAGE_LIST::_db_con* pItem, int64_t tResultTime, unsigned int dwPrice, unsigned int dwTax, unsigned int dwLeftDalant, unsigned int dwLeftGold, char* pszFileName);
        void auto_trade_sell(char* szBuyerName, unsigned int dwBuyerSerial, char* szBuyerID, unsigned int dwRegistSerial, struct _STORAGE_LIST::_db_con* pItem, unsigned int dwPrice, unsigned int dwTax, unsigned int dwLeftDalant, unsigned int dwLeftGold, char* pszFileName);
        void back_trap_item(int n, struct _STORAGE_LIST::_db_con* pItem, char* pszFileName);
        void buy_item(int n, struct _buy_offer* pOffer, char byOfferNum, unsigned int dwCostDalant, unsigned int dwCostGold, unsigned int dwNewDalant, unsigned int dwNewGold, char* pszFileName);
        void buy_to_inven_cashitem(char byTbl, uint16_t wIndex, int nPrice, int nDis, int nNum, int nBuyPrice, int nAmount, char* pFileName, uint64_t lnUID, char byEventType);
        void buy_unit(int n, char bySlotIndex, struct _UNIT_DB_BASE::_LIST* pData, unsigned int* pdwConsumMoney, unsigned int dwNewDalant, unsigned int dwNewGold, char* pszFileName);
        void cash_item_use(int n, struct _STORAGE_LIST::_db_con* pUseItem, char* pszFileName);
        void cashitem_del_from_inven(char byTblCode, uint16_t wItemIndex, uint64_t lnUID, char* pFN);
        void char_copy(int n, char* pszDstName, unsigned int dwDstSerial, char* pszFileName);
        void cheat_add_item(int n, struct _STORAGE_LIST::_db_con* pItem, char byAddNum, char* pszFileName);
        void cheat_alter_money(int n, unsigned int dwNewDalant, unsigned int dwNewGold, char* pszFileName);
        void cheat_del_item(int n, struct _STORAGE_LIST::_db_con* pItem, char byDelNum, char* pszFileName);
        void cheat_make_item_no_material(int n, char byRetCode, struct _STORAGE_LIST::_db_con* pMakeItem, char* pszFileName);
        void close(int n, char* pCloseCode, char* pszFileName);
        void combine_ex_reward_item(int n, char byMakeNum, struct _ITEMCOMBINE_DB_BASE* pCombineDB, char* pbyRewardTypeList, uint64_t* lnUIDs, char* strFileName);
        void combine_ex_using_material(int n, unsigned int dwCheckKey, char bySlotNum, struct _STORAGE_LIST::_db_con** ppMaterial, char* pbyMtrNum, unsigned int dwFee, char* strFileName, int bSucc, unsigned int dwFailCount);
        void combine_item(int n, struct _STORAGE_LIST::_db_con* pMaterial, char* pbyMtrNum, char byMaterialNum, struct _STORAGE_LIST::_db_con* pMakeItem, unsigned int dwFee, unsigned int dwLeftDalant, char* pszFileName);
        void consume_del_item(int n, struct _STORAGE_LIST::_db_con* pItem, char* pszFileName);
        void coupon_use_buy_item(struct _STORAGE_LIST::_db_con* pCouponItem, char* ApplyItem, char* pszFileName);
        void cut_clear_item(int n, uint16_t* pwCuttingResBuffer, unsigned int dwAddGold, unsigned int dwNewGold, char* pszFileName);
        void cut_item(int n, struct _STORAGE_LIST::_db_con* pOreItem, int nOreNum, uint16_t* pwCuttingResBuffer, unsigned int dwCostDalant, unsigned int dwNewDalant, char* pszFileName);
        void delete_npc_quest_item(int n, struct _STORAGE_LIST::_db_con* pItem, char* pszFileName);
        void destroy_unit(int n, char bySlotIndex, char byFrameCode, char* pszFileName);
        void exchange_item(int n, struct _STORAGE_LIST::_db_con* pUseItem, struct _STORAGE_LIST::_db_con* pOutItem, char* pszFileName);
        void exchange_money(int n, unsigned int dwCurDalant, unsigned int dwCurGold, unsigned int dwNewDalant, unsigned int dwNewGold, char* pszFileName);
        void exchange_pvp_gold(int n, unsigned int dwPoint, unsigned int dwNewDalant, unsigned int dwNewGold, char* pszFileName);
        void exp_prof_log(int count, char* szFile);
        void grade_down_item(int n, struct _STORAGE_LIST::_db_con* pItem, struct _STORAGE_LIST::_db_con* pTalik, unsigned int dwAfterLv, char* pszFileName);
        void grade_up_item(int n, struct _STORAGE_LIST::_db_con* pItem, struct _STORAGE_LIST::_db_con* pTalik, struct _STORAGE_LIST::_db_con* pJewel, char byJewelNum, char byErrCode, unsigned int dwAfterLv, char* pszFileName);
        void guild_est_money(int n, char* pszGuildName, unsigned int dwEstDalant, unsigned int dwLeftDalant, char* pszFileName);
        void guild_est_money_rollback(int n, char* pszGuildName, unsigned int dwEstDalant, unsigned int dwLeftDalant, char* pszFileName);
        void guild_pop_money(int n, char* pszGuildName, unsigned int dwPopDalant, unsigned int dwPopGold, unsigned int dwLeftDalant, unsigned int dwLeftGold, char* pszFileName);
        void guild_pop_money_rollback(int n, char* pszGuildName, unsigned int dwPopDalant, unsigned int dwPopGold, unsigned int dwLeftDalant, unsigned int dwLeftGold, char* pszFileName);
        void guild_push_money(int n, char* pszGuildName, unsigned int dwPushDalant, unsigned int dwPushGold, unsigned int dwLeftDalant, unsigned int dwLeftGold, char* pszFileName);
        void guild_push_money_rollback(int n, char* pszGuildName, unsigned int dwPushDalant, unsigned int dwPushGold, unsigned int dwLeftDalant, unsigned int dwLeftGold, char* pszFileName);
        void guild_suggest_change_taxrate(unsigned int dwGuild, unsigned int dwMatterObj2, char* szFile);
        void have_auto_item(int n, struct CUnmannedTraderRegistItemInfo* pkInfo, char byMaxCnt);
        void have_item(int n, char* pszName, struct _AVATOR_DATA* pLoadData, struct _AVATOR_DATA* pBackupData, char* pszID, unsigned int dwIDSerial, char byDgr, unsigned int dwIP, unsigned int dwExpRate, bool bStart, char* pszFileName);
        void have_item_close(int n, char* pszName, struct _AVATOR_DATA* pLoadData, struct _AVATOR_DATA* pBackupData, char* pszID, unsigned int dwIDSerial, char byDgr, unsigned int dwIP, unsigned int dwExpRate, struct CUnmannedTraderRegistItemInfo* pkInfo, char byMaxCnt, char* pszFileName);
        void item_serial_full(int n, char* pszFileName);
        void lenditem_del_from_inven(char byTblCode, uint16_t wItemIndex, uint64_t lnUID, char* pFN);
        void login_cancel_auto_trade(int n, unsigned int dwRegistSerial, struct _STORAGE_LIST::_db_con* pRegItem, int64_t tResultTime, char* pszFileName);
        void make_item(int n, struct _STORAGE_LIST::_db_con* pMaterial, char* pbyMtrNum, char byMaterialNum, char byRetCode, bool bInsert, struct _STORAGE_LIST::_db_con* pMakeItem, char* pszFileName);
        void mastery_change_jade(int nMstCode, unsigned int dwOldCum, unsigned int dwNewCum, int nLv, float fVal, char* szFileName, int nWpType);
        void patriarch_push_money(char* pwszPatriarchName, unsigned int dwPushDalant, unsigned int dwLeftDalant, char* pszFileName);
        void pay_money(int n, char* pszClause, unsigned int dwPayDalant, unsigned int dwPayGold, unsigned int dwNewDalant, unsigned int dwNewGold, char* pszFileName);
        void personal_amine_install(char byTblCode, uint16_t wItemIndex, struct _personal_amine_inven_db_load* pCon, char* szFileName);
        void personal_amine_itemlog(char* szLogDesc, char byPos, char byTblCode, uint16_t wItemIndex, unsigned int dwDur, char* szFileName);
        void personal_amine_stop(unsigned int* pdwMineCnt, int nMaxOreNum, char byTblCode, uint16_t wItemIndex, char* szFileName);
        void personal_amine_uninstall(char byType, unsigned int* pdwMineCnt, int nMaxOreNum, struct _STORAGE_LIST::_db_con* pItem, char* szFileName);
        void post_delete(struct CPostData* pPost, char* pFileName);
        void post_getpresent(char* wszSendName, unsigned int dwPostSerial, struct _STORAGE_LIST::_db_con* Item, uint64_t dwDur, unsigned int dwGold, char* pFileName);
        void post_receive(struct CPostData* pPost, char* pFileName);
        void post_return(char* wszRecvName, unsigned int dwPostSerial, struct _STORAGE_LIST::_db_con* Item, uint64_t dwDur, unsigned int dwGold, char* pFileName);
        void post_returnreceive(struct CPostData* pPost, char* pFileName);
        void post_senditem(char* wszRecvName, struct _STORAGE_LIST::_db_con* Item, uint64_t dwDur, unsigned int dwGold, char* pFileName);
        void post_storage(struct CPostStorage* pStorage, char* pFileName);
        void price_auto_trade(int n, unsigned int dwRegistSerial, struct _STORAGE_LIST::_db_con* pRegItem, unsigned int dwTax, unsigned int dwOldPrice, unsigned int dwNewPrice, char* pszFileName);
        void raceboss_candidate(int ncost, unsigned int dwSerial, char* pszFileName);
        void raceboss_giveback(unsigned int dwSerial, unsigned int dwDalant, char* pszFileName);
        void raceboss_vote(unsigned int dwSerial, unsigned int dwAvatorSerial, char* pAvatorName, char* pszFileName);
        void re_reg_auto_trade(int n, unsigned int dwRegistSerial, struct _STORAGE_LIST::_db_con* pRegItem, unsigned int dwPrice, unsigned int dwfee, unsigned int dwLeftDalant, char* pszFileName);
        void read_cashamount(unsigned int dwAC, unsigned int dwAV, int nCash, char* pFileName);
        void reg_auto_trade(int n, unsigned int dwRegistSerial, struct _STORAGE_LIST::_db_con* pRegItem, unsigned int dwPrice, unsigned int dwfee, unsigned int dwLeftDalant, char* pszFileName);
        void return_post_storage(struct CPostReturnStorage* pReturn, char* pFileName);
        void reward_add_item(int n, char* pszClause, struct _STORAGE_LIST::_db_con* pItem, char* pszFileName);
        void reward_add_money(int n, char* pszClause, unsigned int dwAddDalant, unsigned int dwAddGold, unsigned int dwSumDalant, unsigned int dwSumGold, char* pszFileName);
        void rollback_cashitem(char* szRet, uint64_t lnUID, char* strItemCode, int nCash, char* pFileName);
        void self_cancel_auto_trade(int n, unsigned int dwRegistSerial, struct _STORAGE_LIST::_db_con* pRegItem, char* pszFileName);
        void sell_item(int n, struct _sell_offer* pOffer, char byOfferNum, unsigned int dwIncomeDalant, unsigned int dwIncomeGold, unsigned int dwNewDalant, unsigned int dwNewGold, char* pszFileName);
        void sell_unit(int n, char bySlotIndex, char byFrameCode, float fGaugeRate, unsigned int dwSellMoney, unsigned int dwPayDalant, unsigned int dwNewDalant, unsigned int dwNewGold, char* pszFileName);
        void take_ground_item(int n, char byItemBoxCode, struct _STORAGE_LIST::_db_con* pItem, char* pszThrowerName, unsigned int dwThrowerSerial, char* pszThrowerID, uint16_t wMonRecIndex, char* pMapCode, float* pfPos, char* pszFileName);
        void throw_ground_item(int n, struct _STORAGE_LIST::_db_con* pItem, char* pMapCode, float* pfPos, char* pszFileName);
        void time_jade_effect_log(char* pszItemName, struct _STORAGE_LIST::_db_con* pItem, bool bAdd, char* pszFileName);
        void time_out_cancel_auto_trade(int n, unsigned int dwRegistSerial, struct _STORAGE_LIST::_db_con* pRegItem, char* pszFileName);
        void trade(int n, struct _STORAGE_LIST::_db_con* pOutItem, int nOutItemNum, unsigned int dwOutDalant, unsigned int dwOutGold, struct _STORAGE_LIST::_db_con* pInItem, int nInItemNum, unsigned int dwInDalant, unsigned int dwInGold, char* pszDstName, unsigned int dwDstSerial, char* pszDstID, unsigned int dwSumDalant, unsigned int dwSumGold, char* pMapCode, float* pfPos, char* pszFileName);
        void trans_ground_item(struct _STORAGE_LIST::_db_con* pItem, char* pszTakerName, unsigned int dwTakerSerial, char* pszTakerID, char* pszFileName);
        void trunk_io_item(int n, struct _STORAGE_LIST::_db_con* pIOItem, bool bInput, unsigned int dwFeeDalant, unsigned int dwNewDalant, char* pszFileName);
        void trunk_io_money(int n, bool bInput, unsigned int dwIODalant, unsigned int dwIOGold, unsigned int dwPayDalant, unsigned int dwInvenDalant, unsigned int dwInvenGold, unsigned int dwTrkDalant, unsigned int dwTrkGold, char* pszFileName);
        void trunk_swap_item(int n, struct _STORAGE_LIST::_db_con* pInputItem, struct _STORAGE_LIST::_db_con* pOutputItem, unsigned int dwFeeDalant, unsigned int dwNewDalant, char* pszFileName);
        void tuning_unit(int n, char bySlotIndex, struct _UNIT_DB_BASE::_LIST* pData, int* pnPayMoney, unsigned int dwNewDalant, unsigned int dwNewGold, char* pszFileName);
        void used_cash(int nCurCash, int nUseCash, char* pFileName);
        ~CMgrAvatorItemHistory();
        void dtor_CMgrAvatorItemHistory();
    };   
    #pragma pack(pop)
    static_assert(ATF::checkSize<CMgrAvatorItemHistory, 3508344>(), "CMgrAvatorItemHistory");
END_ATF_NAMESPACE

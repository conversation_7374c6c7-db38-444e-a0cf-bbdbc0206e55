// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _radar_char_list_result_zocl
    {
        struct  _char_info
        {
            char m_byCharType;
            float m_fPos[2];
        };
        bool bEnd;
        char byListNum;
        _char_info CharInfo[50];
    public:
        _radar_char_list_result_zocl();
        void ctor__radar_char_list_result_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_radar_char_list_result_zocl, 452>(), "_radar_char_list_result_zocl");
END_ATF_NAMESPACE

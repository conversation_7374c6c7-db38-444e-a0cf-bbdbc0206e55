// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS_INDIV.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _make_item_request_clzo
    {
        _STORAGE_POS_INDIV ipMakeTool;
        unsigned __int16 wManualIndex;
        char byMaterialNum;
        _STORAGE_POS_INDIV ipMaterials[100];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

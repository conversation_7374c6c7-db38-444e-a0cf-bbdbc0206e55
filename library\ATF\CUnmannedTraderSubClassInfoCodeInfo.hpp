// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSubClassInfoCode.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderSubClassInfoCodector_CUnmannedTraderSubClassInfoCode2_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*, unsigned int);
        using CUnmannedTraderSubClassInfoCodector_CUnmannedTraderSubClassInfoCode2_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*, unsigned int, CUnmannedTraderSubClassInfoCodector_CUnmannedTraderSubClassInfoCode2_ptr);
        using CUnmannedTraderSubClassInfoCodeCreate4_ptr = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*, unsigned int);
        using CUnmannedTraderSubClassInfoCodeCreate4_clbk = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*, unsigned int, CUnmannedTraderSubClassInfoCodeCreate4_ptr);
        using CUnmannedTraderSubClassInfoCodeGetGroupID6_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*, char, uint16_t, char*);
        using CUnmannedTraderSubClassInfoCodeGetGroupID6_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*, char, uint16_t, char*, CUnmannedTraderSubClassInfoCodeGetGroupID6_ptr);
        using CUnmannedTraderSubClassInfoCodeLoadXML8_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int);
        using CUnmannedTraderSubClassInfoCodeLoadXML8_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int, CUnmannedTraderSubClassInfoCodeLoadXML8_ptr);
        
        using CUnmannedTraderSubClassInfoCodedtor_CUnmannedTraderSubClassInfoCode12_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*);
        using CUnmannedTraderSubClassInfoCodedtor_CUnmannedTraderSubClassInfoCode12_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoCode*, CUnmannedTraderSubClassInfoCodedtor_CUnmannedTraderSubClassInfoCode12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

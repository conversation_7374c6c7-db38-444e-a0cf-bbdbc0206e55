// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPoint.hpp>
#include <CSize.hpp>
#include <tagPOINT.hpp>
#include <tagRECT.hpp>
#include <tagSIZE.hpp>


START_ATF_NAMESPACE
    struct  CRect : tagRECT
    {
    public:
        struct CPoint* BottomRight();
        CRect(int l, int t, int r, int b);
        void ctor_CRect(int l, int t, int r, int b);
        CRect(struct tagPOINT topLeft, struct tagPOINT bottomRight);
        void ctor_CRect(struct tagPOINT topLeft, struct tagPOINT bottomRight);
        CRect(struct tagPOINT point, struct tagSIZE size);
        void ctor_CRect(struct tagPOINT point, struct tagSIZE size);
        CRect(struct tagRECT& srcRect);
        void ctor_CRect(struct tagRECT& srcRect);
        CRect(struct tagRECT* lpSrcRect);
        void ctor_CRect(struct tagRECT* lpSrcRect);
        CRect();
        void ctor_CRect();
        struct CPoint* CenterPoint(struct CPoint* result);
        void CopyRect(struct tagRECT* lpSrcRect);
        void DeflateRect(int x, int y);
        void DeflateRect(int l, int t, int r, int b);
        void DeflateRect(struct tagRECT* lpRect);
        void DeflateRect(struct tagSIZE size);
        int EqualRect(struct tagRECT* lpRect);
        int Height();
        void InflateRect(int x, int y);
        void InflateRect(int l, int t, int r, int b);
        void InflateRect(struct tagRECT* lpRect);
        void InflateRect(struct tagSIZE size);
        int IntersectRect(struct tagRECT* lpRect1, struct tagRECT* lpRect2);
        int IsRectEmpty();
        int IsRectNull();
        void MoveToX(int x);
        void MoveToXY(int x, int y);
        void MoveToXY(struct tagPOINT pt);
        void MoveToY(int y);
        struct CRect* MulDiv(struct CRect* result, int nMultiplier, int nDivisor);
        void NormalizeRect();
        void OffsetRect(int x, int y);
        void OffsetRect(struct tagPOINT point);
        void OffsetRect(struct tagSIZE size);
        int PtInRect(struct tagPOINT point);
        void SetRect(int x1, int y1, int x2, int y2);
        void SetRect(struct tagPOINT topLeft, struct tagPOINT bottomRight);
        void SetRectEmpty();
        struct CSize* Size(struct CSize* result);
        int SubtractRect(struct tagRECT* lpRectSrc1, struct tagRECT* lpRectSrc2);
        static void SwapLeftRight(struct tagRECT* lpRect);
        void SwapLeftRight();
        struct CPoint* TopLeft();
        int UnionRect(struct tagRECT* lpRect1, struct tagRECT* lpRect2);
        int Width();
    };    
    static_assert(ATF::checkSize<CRect, 16>(), "CRect");
END_ATF_NAMESPACE

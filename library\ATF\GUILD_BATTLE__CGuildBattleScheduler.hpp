// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CGuildBattleScheduler
        {
        public:
            CGuildBattleScheduler();
            void ctor_CGuildBattleScheduler();
            static void Destroy();
            bool Init();
            static struct CGuildBattleScheduler* Instance();
            bool UpdateClearGuildBattleScheduleDayInfo(unsigned int dwStartSLID, unsigned int dwEndSLID);
            ~CGuildBattleScheduler();
            void dtor_CGuildBattleScheduler();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

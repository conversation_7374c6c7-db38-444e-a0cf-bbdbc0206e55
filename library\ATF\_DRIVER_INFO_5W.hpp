// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _DRIVER_INFO_5W
    {
        unsigned int cVersion;
        wchar_t *pName;
        wchar_t *pEnvironment;
        wchar_t *pDriverPath;
        wchar_t *pDataFile;
        wchar_t *pConfigFile;
        unsigned int dwDriverAttributes;
        unsigned int dwConfigVersion;
        unsigned int dwDriverVersion;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

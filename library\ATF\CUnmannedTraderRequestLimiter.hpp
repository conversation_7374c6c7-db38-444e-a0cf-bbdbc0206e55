// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBuffByHolyQuestProcedure.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderRequestLimiter
    {
        typedef CRaceBuffByHolyQuestProcedure::REQUEST_TYPE REQUEST_TYPE;
        REQUEST_TYPE m_eState;
    public:
        CUnmannedTraderRequestLimiter();
        void ctor_CUnmannedTraderRequestLimiter();
        void ClearRequset();
        bool IsEmpty();
        void SetRequest(int iRequest);
        ~CUnmannedTraderRequestLimiter();
        void dtor_CUnmannedTraderRequestLimiter();
    };    
    static_assert(ATF::checkSize<CUnmannedTraderRequestLimiter, 4>(), "CUnmannedTraderRequestLimiter");
END_ATF_NAMESPACE

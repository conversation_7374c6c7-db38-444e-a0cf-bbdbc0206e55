// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_EXT_DUMMY.hpp>


START_ATF_NAMESPACE
    struct CExtDummy
    {
        unsigned int mMaxNum;
        unsigned int mNum;
        _EXT_DUMMY *mDummy;
    public:
        void DrawAllDummyBBox();
        void DrawDummyBBox(uint32_t arg_0);
        struct _EXT_DUMMY* GetDummy(uint32_t arg_0);
        void GetDummyList(uint32_t arg_0, uint32_t* arg_1, uint32_t* arg_2);
        int GetLocalFromWorld(float** arg_0, uint32_t arg_1, float* arg_2);
        unsigned int GetTotalNum();
        int GetWorldFromLocal(float** arg_0, uint32_t arg_1, float* arg_2);
        int IsInBBox(uint32_t arg_0, float* arg_1);
        int LoadExtDummy(char* arg_0);
        void ReleaseExtDummy();
        ~CExtDummy();
        int64_t dtor_CExtDummy();
    };
END_ATF_NAMESPACE

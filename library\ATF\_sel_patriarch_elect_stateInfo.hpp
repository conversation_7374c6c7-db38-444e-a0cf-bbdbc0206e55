// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_sel_patriarch_elect_state.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _sel_patriarch_elect_statector__sel_patriarch_elect_state2_ptr = void (WINAPIV*)(struct _sel_patriarch_elect_state*);
        using _sel_patriarch_elect_statector__sel_patriarch_elect_state2_clbk = void (WINAPIV*)(struct _sel_patriarch_elect_state*, _sel_patriarch_elect_statector__sel_patriarch_elect_state2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

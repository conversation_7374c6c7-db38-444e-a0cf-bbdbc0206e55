// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct RECV_DATA
    {
        bool bResult;
        unsigned int dwSeq;
        unsigned __int16 wSize;
        unsigned __int16 wType;
        unsigned __int16 wRet;
        void *pData;
    public:
        RECV_DATA();
        void ctor_RECV_DATA();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _attack_test_request_clzo
    {
        char byEffectCode;
        char byEffectIndex;
        unsigned __int16 wBulletSerial;
        char byWeaponPart;
        __int16 zTar[2];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPartyPlayer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CPartyPlayerctor_CPartyPlayer2_ptr = void (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerctor_CPartyPlayer2_clbk = void (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerctor_CPartyPlayer2_ptr);
        using CPartyPlayerDisjointParty4_ptr = bool (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerDisjointParty4_clbk = bool (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerDisjointParty4_ptr);
        using CPartyPlayerEnterWorld6_ptr = void (WINAPIV*)(struct CPartyPlayer*, struct _WA_AVATOR_CODE*, uint16_t);
        using CPartyPlayerEnterWorld6_clbk = void (WINAPIV*)(struct CPartyPlayer*, struct _WA_AVATOR_CODE*, uint16_t, CPartyPlayerEnterWorld6_ptr);
        using CPartyPlayerExitWorld8_ptr = void (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer**);
        using CPartyPlayerExitWorld8_clbk = void (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer**, CPartyPlayerExitWorld8_ptr);
        using CPartyPlayerFoundParty10_ptr = bool (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer*);
        using CPartyPlayerFoundParty10_clbk = bool (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer*, CPartyPlayerFoundParty10_ptr);
        using CPartyPlayerGetLootAuthor12_ptr = struct CPlayer* (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerGetLootAuthor12_clbk = struct CPlayer* (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerGetLootAuthor12_ptr);
        using CPartyPlayerGetPopPartyMember14_ptr = int (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerGetPopPartyMember14_clbk = int (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerGetPopPartyMember14_ptr);
        using CPartyPlayerGetPtrFromSerial16_ptr = struct CPartyPlayer* (WINAPIV*)(struct CPartyPlayer*, unsigned int);
        using CPartyPlayerGetPtrFromSerial16_clbk = struct CPartyPlayer* (WINAPIV*)(struct CPartyPlayer*, unsigned int, CPartyPlayerGetPtrFromSerial16_ptr);
        using CPartyPlayerGetPtrPartyMember18_ptr = struct CPartyPlayer** (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerGetPtrPartyMember18_clbk = struct CPartyPlayer** (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerGetPtrPartyMember18_ptr);
        using CPartyPlayerInheritBoss20_ptr = bool (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer*);
        using CPartyPlayerInheritBoss20_clbk = bool (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer*, CPartyPlayerInheritBoss20_ptr);
        using CPartyPlayerInit22_ptr = void (WINAPIV*)(struct CPartyPlayer*, uint16_t);
        using CPartyPlayerInit22_clbk = void (WINAPIV*)(struct CPartyPlayer*, uint16_t, CPartyPlayerInit22_ptr);
        using CPartyPlayerInsertPartyMember24_ptr = bool (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer*);
        using CPartyPlayerInsertPartyMember24_clbk = bool (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer*, CPartyPlayerInsertPartyMember24_ptr);
        using CPartyPlayerIsJoinPartyLevel26_ptr = bool (WINAPIV*)(struct CPartyPlayer*, int, float);
        using CPartyPlayerIsJoinPartyLevel26_clbk = bool (WINAPIV*)(struct CPartyPlayer*, int, float, CPartyPlayerIsJoinPartyLevel26_ptr);
        using CPartyPlayerIsPartyBoss28_ptr = bool (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerIsPartyBoss28_clbk = bool (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerIsPartyBoss28_ptr);
        using CPartyPlayerIsPartyLock30_ptr = bool (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerIsPartyLock30_clbk = bool (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerIsPartyLock30_ptr);
        using CPartyPlayerIsPartyMember32_ptr = bool (WINAPIV*)(struct CPartyPlayer*, struct CPlayer*);
        using CPartyPlayerIsPartyMember32_clbk = bool (WINAPIV*)(struct CPartyPlayer*, struct CPlayer*, CPartyPlayerIsPartyMember32_ptr);
        using CPartyPlayerIsPartyMode34_ptr = bool (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerIsPartyMode34_clbk = bool (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerIsPartyMode34_ptr);
        using CPartyPlayerPartyListInit36_ptr = void (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerPartyListInit36_clbk = void (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerPartyListInit36_ptr);
        using CPartyPlayerRemovePartyMember38_ptr = bool (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer*, struct CPartyPlayer**);
        using CPartyPlayerRemovePartyMember38_clbk = bool (WINAPIV*)(struct CPartyPlayer*, struct CPartyPlayer*, struct CPartyPlayer**, CPartyPlayerRemovePartyMember38_ptr);
        using CPartyPlayerSetLockMode40_ptr = bool (WINAPIV*)(struct CPartyPlayer*, bool);
        using CPartyPlayerSetLockMode40_clbk = bool (WINAPIV*)(struct CPartyPlayer*, bool, CPartyPlayerSetLockMode40_ptr);
        using CPartyPlayerSetLootShareMode42_ptr = bool (WINAPIV*)(struct CPartyPlayer*, char);
        using CPartyPlayerSetLootShareMode42_clbk = bool (WINAPIV*)(struct CPartyPlayer*, char, CPartyPlayerSetLootShareMode42_ptr);
        using CPartyPlayerSetNextLootAuthor44_ptr = void (WINAPIV*)(struct CPartyPlayer*);
        using CPartyPlayerSetNextLootAuthor44_clbk = void (WINAPIV*)(struct CPartyPlayer*, CPartyPlayerSetNextLootAuthor44_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

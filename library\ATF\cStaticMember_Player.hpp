// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct cStaticMember_Player
    {
        enum _default_value
        {
            default_max_level = 0x32,
        };
        int _nMaxLv;
        long double *_pLimExp;
    public:
        long double GetLimitExp(int lv);
        int GetMaxLv();
        bool Initialize();
        static struct cStaticMember_Player* Instance();
        static void Release();
        cStaticMember_Player();
        void ctor_cStaticMember_Player();
        bool loadLimitExpData();
        ~cStaticMember_Player();
        void dtor_cStaticMember_Player();
    };
END_ATF_NAMESPACE

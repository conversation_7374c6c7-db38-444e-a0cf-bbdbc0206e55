// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct provider_info
    {
        unsigned int (WINAPIV *pi_R0_1val)(void *, val_context *, unsigned int, void *, unsigned int *, unsigned int);
        unsigned int (WINAPIV *pi_R0_allvals)(void *, val_context *, unsigned int, void *, unsigned int *, unsigned int);
        unsigned int (WINAPIV *pi_R3_1val)(void *, val_context *, unsigned int, void *, unsigned int *, unsigned int);
        unsigned int (WINAPIV *pi_R3_allvals)(void *, val_context *, unsigned int, void *, unsigned int *, unsigned int);
        unsigned int pi_flags;
        void *pi_key_context;
    };
END_ATF_NAMESPACE

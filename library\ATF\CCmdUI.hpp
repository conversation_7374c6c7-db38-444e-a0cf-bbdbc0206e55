// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCmdUIVtbl.hpp>
#include <CMenu.hpp>
#include <CWnd.hpp>


START_ATF_NAMESPACE
    struct CCmdUI
    {
        CCmdUIVtbl *vfptr;
        unsigned int m_nID;
        unsigned int m_nIndex;
        CMenu *m_pMenu;
        CMenu *m_pSubMenu;
        CWnd *m_pOther;
        int m_bEnableChanged;
        int m_bContinueRouting;
        unsigned int m_nIndexMax;
        CMenu *m_pParentMenu;
    };
END_ATF_NAMESPACE

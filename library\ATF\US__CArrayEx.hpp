// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <US__CArray.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        template<typename _Ty, typename _TyState>
        struct CArrayEx
        {
            CArray<_Ty> m_DataAr;
            CArray<_TyState> m_StateAr;
        };
    }; // end namespace US
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__vector.hpp>
#include <tagTIMESTAMP_STRUCT.hpp>
#include <CGuildRoomInfo.hpp>


START_ATF_NAMESPACE
    struct CGuildRoomSystem
    {
        std::vector<CGuildRoomInfo> m_vecGuildRoom;
        struct CMapData *m_pRoomMap[3][2];
    public:
        CGuildRoomSystem();
        void ctor_CGuildRoomSystem();
        static struct CGuildRoomSystem* GetInstance();
        struct CMapData* GetMapData(char byRace, char byMapType);
        bool GetMapPos(unsigned int dwGuildSerial, float* pPos, struct CMapData* pMap, uint16_t* wMapLayer, char* byRoomType);
        bool GetRestTime(unsigned int dwGuildSerial, int* tt);
        int GetRoomCountByType(char byRace, char byRoomType);
        char GetRoomType(unsigned int dwGuildSerial);
        bool Init();
        bool IsGuildRoomMemberIn(unsigned int dwGuildSerial, int n, unsigned int dwCharSerial);
        bool IsRoomRented(unsigned int dwGuildSerial);
        bool Load_db();
        char RentRoom(char byRace, char byRoomType, int iGuildInx, unsigned int dwGuildSerial, struct tagTIMESTAMP_STRUCT* ts, bool bRestore);
        void RentRoomTimer();
        int RoomIn(unsigned int dwGuildSerial, int n, unsigned int dwCharSerial);
        int RoomOut(unsigned int dwGuildSerial, int n, unsigned int dwCharSerial);
        int SetPlayerOut(unsigned int dwGuildSerial, int n, unsigned int dwCharSerial);
        ~CGuildRoomSystem();
        void dtor_CGuildRoomSystem();
    };
END_ATF_NAMESPACE

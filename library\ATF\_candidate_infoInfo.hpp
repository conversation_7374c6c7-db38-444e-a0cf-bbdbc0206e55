// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_candidate_info.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _candidate_info_Init2_ptr = void (WINAPIV*)(struct _candidate_info*);
        using _candidate_info_Init2_clbk = void (WINAPIV*)(struct _candidate_info*, _candidate_info_Init2_ptr);
        
        using _candidate_infoctor__candidate_info4_ptr = void (WINAPIV*)(struct _candidate_info*);
        using _candidate_infoctor__candidate_info4_clbk = void (WINAPIV*)(struct _candidate_info*, _candidate_infoctor__candidate_info4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

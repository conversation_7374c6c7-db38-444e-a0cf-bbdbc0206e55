// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <D3DXMATRIX.hpp>


START_ATF_NAMESPACE
    struct R3Camera
    {
        float mPos[3];
        float mPoint[3];
        D3DXMATRIX mMatView;
        float mTh;
        float mPh;
        float mDist;
        float mFixXrot;
        float mTime;
        float mOldPh;
        float mPhDist;
        float mPhSign;
        float mCharYangle;
        float mCharYangleForTime;
        float mPointSmooth;
        float mRotSmooth;
        float mTarPoint[3];
        float mTarTh;
        float mTarPh;
        float mTarDist;
        void *mBsp;
    public:
        float GetDist();
        float GetPh();
        void GetPoint(float* arg_0);
        void GetPos(float* arg_0);
        void GetSmoothTarPoint(float* arg_0);
        float GetTh();
        struct D3DXMATRIX* GetViewMatrix();
        void MakeCameraAndViewMatrix();
        void MakeViewMatrix(struct D3DXMATRIX* arg_0);
        void SetBspPtr(void* arg_0);
        void SetDist(float arg_0);
        void SetMatrix(struct Matrix4* arg_0);
        void SetPoint(float arg_0, float arg_1, float arg_2);
        void SetPos(float arg_0, float arg_1, float arg_2);
        void SetSmoothDistLoop(float arg_0);
        void SetSmoothPointLoop(float arg_0, float arg_1, float arg_2);
        void SetSmoothTarPoint(float arg_0, float arg_1, float arg_2);
        void SetSmoothThPhLoop(float arg_0, float arg_1);
        void SetSmoothValue(float arg_0, float arg_1);
        void SetThPh(float arg_0, float arg_1);
        void SetViewMatrix();
        ~R3Camera();
        int64_t dtor_R3Camera();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetWorking.hpp>
#include <_MSG_HEADER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CNetworkEX : CNetWorking
    {
        unsigned int dwMonsterPosMiss;
        unsigned int dwPlayerPosMiss;
        char m_byStatus[4];
    public:
        bool AMP_DownloadRequest(int n, char* pBuf);
        bool ATradeAdjustPriceRequest(int n, char* pBuf);
        bool ATradeBuyItemRequest(int n, char* pBuf);
        bool ATradeClearItemRequest(int n, char* pBuf);
        bool ATradeReRegistRequest(int n, char* pBuf);
        bool ATradeRegItemRequest(int n, char* pBuf);
        bool ATradeRegedListRequest(int n, char* pBuf);
        bool ATradeTaxRateRequest(int n, char* pBuf);
        void AcceptClientCheck(unsigned int dwProID, unsigned int dwIndex, unsigned int dwSerial);
        bool AccountLineAnalysis(int n, struct _MSG_HEADER* pMsgHeader, char* pMsg);
        bool AddBagRequest(int n, char* pBuf);
        bool AddCharRequest(int n, char* pBuf);
        bool AliveCharRequest(int n, char* pBuf);
        bool AlterItemSlotRequest(int n, char* pBuf);
        bool AlterLinkBoardSlotRequest(int n, char* pBuf);
        bool AlterPartyLootShareRequest(int n, char* pBuf);
        bool AlterWindowInfoRequest(int n, char* pBuf);
        bool AnimusCommandRequest(int n, char* pBuf);
        bool AnimusInvenChangeRequest(int n, char* pBuf);
        bool AnimusRecallRequest(int n, char* pBuf);
        bool AnimusReturnRequest(int n, char* pBuf);
        bool AnimusTargetRequest(int n, char* pBuf);
        void AnsyncConnectComplete(unsigned int dwProID, unsigned int dwIndex, int nResult);
        bool Apex_R(int n, uint16_t wSize, char* pBuf);
        bool Apex_T(int n, uint16_t wSize, char* pBuf);
        bool AttackForceRequest(int n, char* pBuf);
        bool AttackPersonalRequest(int n, char* pBuf);
        bool AttackSiegeRequest(int n, char* pBuf);
        bool AttackSkillRequest(int n, char* pBuf);
        bool AttackTestRequest(int n, char* pBuf);
        bool AttackUnitRequest(int n, char* pBuf);
        bool AwayPartyInvitation(int n, char* pBuf);
        bool AwayPartyInvitationAnswer(int n, char* pBuf);
        bool BackTowerRequest(int n, char* pBuf);
        bool BackTrapRequest(int n, char* pBuf);
        bool BaseDownloadRequest(int n, char* pBuf);
        bool BillingChangeType(int n, char* pBuf);
        bool BillingCloseRequest(int n, char* pBuf);
        bool BillingDestroyModule(int n, char* pBuf);
        bool BillingExpireIPOverflow(int n, char* pBuf);
        bool BillingExpirePCBang(int n, char* pBuf);
        bool BillingExpirePersonal(int n, char* pBuf);
        bool BillingInfoRequest(int n, char* pBuf);
        bool BillingLineAnalysis(int n, struct _MSG_HEADER* pMsgHeader, char* pMsg);
        bool BillingRemaintimePCBang(int n, char* pBuf);
        bool BillingRemaintimePersonal(int n, char* pBuf);
        bool BossSMSMsgRequest(int n, char* pBuf);
        bool BriefPassReport(int n, char* pBuf);
        bool BuddyAddAnswer(int n, char* pBuf);
        bool BuddyAddRequest(int n, char* pBuf);
        bool BuddyDelRequest(int n, char* pBuf);
        bool BuddyDownloadRequest(int n, char* pBuf);
        bool BuyStoreRequest(int n, char* pBuf);
        CNetworkEX();
        void ctor_CNetworkEX();
        bool CanSelectClassRequest(int n, char* pBuf);
        bool CancelRaceBossSMSMsg(int n, char* pBuf);
        bool CashDBInfoRecvResult(int n, char* pBuf);
        bool CastVoteRequest(int n, char* pBuf);
        bool CharacterRenameCash(int n, char* pBuf);
        bool ChatAllRecvYesOrNo(int n, char* pBuf);
        bool ChatAllRequest(int n, char* pBuf);
        bool ChatCheatRequest(int n, char* pBuf);
        bool ChatCircleRequest(int n, char* pBuf);
        bool ChatFarRequest(int n, char* pBuf);
        bool ChatGmNoticeRequest(int n, char* pBuf);
        bool ChatGreetingMsg_GM(int n, char* pBuf);
        bool ChatGreetingMsg_GUILD(int n, char* pBuf);
        bool ChatGreetingMsg_RACE(int n, char* pBuf);
        bool ChatGuildEstSenRequest(int n, char* pBuf);
        bool ChatGuildRequest(int n, char* pBuf);
        bool ChatLockCommand(unsigned int n, char* pMsg);
        bool ChatManageRequest(int n, char* pBuf);
        bool ChatMapRecvYesOrNo(int n, char* pBuf);
        bool ChatMapRequest(int n, char* pBuf);
        bool ChatMgrWhisperRequest(int n, char* pBuf);
        bool ChatMultiFarRequest(int n, char* pBuf);
        bool ChatOperatorRequest(int n, char* pBuf);
        bool ChatPartyRequest(int n, char* pBuf);
        bool ChatRaceBossCryRequest(int n, char* pBuf);
        bool ChatRaceBossRequest(int n, char* pBuf);
        bool ChatRaceRequest(int n, char* pBuf);
        bool ChatRePresentationRequest(int n, char* pBuf);
        bool ChatTradeRequestMsg(int n, char* pBuf);
        bool CheckIsBlockIPResult(unsigned int n, char* pMsg);
        bool ChinaBillingChangePrimium(int n, char* pBuf);
        bool ClassSkillRecallTeleportRequest(int n, char* pBuf);
        bool ClassSkillRequest(int n, char* pBuf);
        bool ClientLineAnalysis(int n, struct _MSG_HEADER* pMsgHeader, char* pMsg);
        void Close(unsigned int dwProID, unsigned int dwSocketIndex, bool bSlowClose, char* pszLog);
        void CloseClientCheck(unsigned int dwProID, unsigned int dwIndex, unsigned int dwSerial);
        bool CombineExItemAcceptRequest(int n, char* pBuf);
        bool CombineExItemRequest(int n, char* pBuf);
        bool CombineItemRequest(int n, char* pBuf);
        bool ConEventTotalSalesCheck(int n, char* pBuf);
        bool ConnectionStatusRequest(int n);
        bool CumDownloadRequest(int n, char* pBuf);
        bool CuttingCompleteRequest(int n, char* pBuf);
        bool DTradeAddRequest(int n, char* pBuf);
        bool DTradeAnswerRequest(int n, char* pBuf);
        bool DTradeAskRequest(int n, char* pBuf);
        bool DTradeBetRequest(int n, char* pBuf);
        bool DTradeCancleRequest(int n, char* pBuf);
        bool DTradeDelRequest(int n, char* pBuf);
        bool DTradeLockRequest(int n, char* pBuf);
        bool DTradeOKRequest(int n, char* pBuf);
        bool DarkHoleAnswerReenterRequest(int n, char* pBuf);
        bool DarkHoleClearOutRequest(int n, char* pBuf);
        bool DarkHoleEnterRequest(int n, char* pBuf);
        bool DarkHoleGiveupOutRequest(int n, char* pBuf);
        bool DarkHoleOpenRequest(int n, char* pBuf);
        bool DataAnalysis(unsigned int dwProID, unsigned int dwClientIndex, struct _MSG_HEADER* pMsgHeader, char* pMsg);
        bool DecideRecallRequest(int n, char* pBuf);
        bool DelCharRequest(int n, char* pBuf);
        bool DisconnectGuildWarCharacterRequest(int n, char* pBuf);
        bool DownGradeItemRequest(int n, char* pBuf);
        bool EmbellishRequest(int n, char* pBuf);
        bool EnterReturnGateRequest(int n, char* pBuf);
        bool EnterWorldRequest(int n, struct _MSG_HEADER* pMsgHeader, char* pBuf);
        bool EnterWorldResult(unsigned int n, char* pMsg);
        bool EquipPartRequest(int n, char* pBuf);
        bool ExchangeDalantForGoldRequest(int n, char* pBuf);
        bool ExchangeGoldForDalantRequest(int n, char* pBuf);
        bool ExchangeGoldForPvPRequest(int n, char* pBuf);
        bool ExchangeItemRequest(int n, char* pBuf);
        bool ExitWorldRequest(int n, char* pBuf);
        bool ExpulsionSocket(unsigned int dwProID, unsigned int dwIndex, char byReason, void* pvInfo);
        bool ForceCloseCommand(unsigned int n, char* pMsg);
        bool ForceDownloadRequest(int n, char* pBuf);
        bool ForceInvenChangeRequest(int n, char* pBuf);
        bool ForceRecallTeleportRequest(int n, char* pBuf);
        bool ForceRequest(int n, char* pBuf);
        bool GotoAvatorRequest(int n, char* pBuf);
        bool GotoBasePortalRequest(int n, char* pBuf);
        bool GuildBattleBlockReport(int n, char* pBuf);
        bool GuildBattleCurrentBattleInfoRequest(int n, char* pBuf);
        bool GuildBattleGetGravityStoneRequest(int n, char* pBuf);
        bool GuildBattleGoalRequest(int n, char* pBuf);
        bool GuildBattleJoinGuildBattleRequest(int n, char* pBuf);
        bool GuildBattlePossibleGuildBattleList(int n, char* pBuf);
        bool GuildBattleRankListRequest(int n, char* pBuf);
        bool GuildBattleReservedScheduleRequest(int n, char* pBuf);
        bool GuildBattleTakeGravityStoneRequest(int n, char* pBuf);
        bool GuildCancelSuggestRequest(int n, char* pBuf);
        bool GuildDownloadRequest(int n, char* pBuf);
        bool GuildEstablishRequest(int n, char* pBuf);
        bool GuildHonorListRequest(int n, char* pBuf);
        bool GuildJoinAcceptRequest(int n, char* pBuf);
        bool GuildJoinApplyCancelRequest(int n, char* pBuf);
        bool GuildJoinApplyRequest(int n, char* pBuf);
        bool GuildListRequest(int n, char* pBuf);
        bool GuildManageRequest(int n, char* pBuf);
        bool GuildNextHonorListRequest(int n, char* pBuf);
        bool GuildOfferSuggestRequest(int n, char* pBuf);
        bool GuildPushMoneyRequest(int n, char* pBuf);
        bool GuildQueryInfoRequest(int n, char* pBuf);
        bool GuildRoomEnterRequest(int n, char* pBuf);
        bool GuildRoomOutRequest(int n, char* pBuf);
        bool GuildRoomRentRequest(int n, char* pBuf);
        bool GuildRoomRestTimeRequest(int n, char* pBuf);
        bool GuildSelfLeaveRequest(int n, char* pBuf);
        bool GuildSetHonorRequest(int n, char* pBuf);
        bool GuildVoteRequest(int n, char* pBuf);
        bool GustureRequest(int n, char* pBuf);
        bool InitClassCostRequest(int n, char* pBuf);
        bool InitClassRequest(int n, char* pBuf);
        bool InvenDownloadRequest(int n, char* pBuf);
        bool ItemboxTakeRequest(int n, char* pBuf);
        bool LimitItemNumRequest(int n, char* pBuf);
        bool LinkBoardDownloadRequest(int n, char* pBuf);
        bool LogInControllServer(int n, char* pBuf);
        bool LogInWebAgentServer(int n, char* pBuf);
        bool MacroDownLoadRequest(int n, char* pBuf);
        bool MakeItemRequest(int n, char* pBuf);
        bool MakeTowerRequest(int n, char* pBuf);
        bool MakeTrapRequest(int n, char* pBuf);
        bool ManageClientForceExitRequest();
        bool ManageClientLimitRunRequest(char* pBuf);
        bool MineCancleRequest(int n, char* pBuf);
        bool MineStartRequest(int n, char* pBuf);
        bool ModeChangeRequest(int n, char* pBuf);
        bool MoveInfoRequeset(int n, char* pBuf);
        bool MoveLobbyRequest(int n, char* pBuf);
        bool MovePortalRequest(int n, char* pBuf);
        bool MoveToOwnStoneMapRequest(int n, char* pBuf);
        bool MoveTypeChangeRequeset(int n, char* pBuf);
        bool NPCDialogRequest(int n, char* pBuf);
        bool NPCLinkCheckItemRequest(int n, char* pBuf);
        bool NPCQuestListRequest(int n, char* pBuf);
        bool NPCQuestRequest(int n, char* pBuf);
        bool NPCWatchingRequest(int n, char* pBuf);
        bool NewPosStartRequest(int n, char* pBuf);
        bool NextPoint(int n, char* pBuf);
        bool NotifyLocalTimeRequest(int n, char* pBuf);
        bool NotifyRaceBossCryMsg(int n, char* pBuf);
        bool ObjectServerPosRequest(int n, char* pBuf);
        bool OffPartRequest(int n, char* pBuf);
        bool OpenControlInform(unsigned int n, char* pMsg);
        bool OpenWorldFailureResult(unsigned int n, char* pMsg);
        bool OpenWorldSuccessResult(unsigned int n, char* pMsg);
        bool OreIntoBagRequest(int n, char* pBuf);
        bool OtherShapeRequest(int n, char* pBuf);
        bool PartyDisjointRequest(int n, char* pBuf);
        bool PartyJoinApplicatiohAnswer(int n, char* pBuf);
        bool PartyJoinApplication(int n, char* pBuf);
        bool PartyJoinInvitation(int n, char* pBuf);
        bool PartyJoinInvitationAnswer(int n, char* pBuf);
        bool PartyLeaveCompulsionRequest(int n, char* pBuf);
        bool PartyLeaveSelfRequest(int n, char* pBuf);
        bool PartyLockRequest(int n, char* pBuf);
        bool PartyReqBlockReport(int n, char* pBuf);
        bool PartySuccessionRequest(int n, char* pBuf);
        bool PcBangPrimiumCouponRequest(int n, char* pBuf);
        bool PlayerInfoResult(int n, char* pBuf);
        bool PlayerMacroUpdate(int n, char* pBuf);
        bool PostContentRequest(int n, char* pBuf);
        bool PostDeleteRequest(int n, char* pBuf);
        bool PostItemGoldRequest(int n, char* pBuf);
        bool PostListRequest(int n, char* pBuf);
        bool PostReturnConfirmRequest(int n, char* pBuf);
        bool PostSendRequest(int n, char* pBuf);
        bool PotionSocketDivisionRequest(int n, char* pBuf);
        bool PotionSocketSeparationRequest(int n, char* pBuf);
        bool ProposeVoteRequest(int n, char* pBuf);
        bool PvpCashRecorverWithTalik(int n, char* pBuf);
        bool PvpRankListRequest(int n, char* pBuf);
        bool QuestDownloadRequest(int n, char* pBuf);
        bool QuestGiveupRequest(int n, char* pBuf);
        bool QuestSelectRewardReport(int n, char* pBuf);
        bool RadarCharListRequest(int n, char* pBuf);
        bool RealMovPosRequest(int n, char* pBuf);
        bool RegedCharRequest(int n, char* pBuf);
        bool RegistBindRequest(int n, char* pBuf);
        bool ReleaseGroupTargetObjectRequest(int n, char* pBuf);
        bool ReleaseSiegeModeRequest(int n, char* pBuf);
        bool ReleaseTargetObjectRequest(int n, char* pBuf);
        bool RequestChangeTaxRate(int n, char* pBuf);
        bool RequestPatriarchPunishment(int n, char* pBuf);
        bool RequestTLLogoutTime(int n, char* pBuf);
        bool RequestTaxRate(int n, char* pBuf);
        bool RequestUILockFindPW(int n, char* pBuf);
        bool RequestUILockInit(int n, char* pBuf);
        bool RequestUILockUpdateInfo(int n, char* pBuf);
        bool RequestUILockUserCertify(int n, char* pBuf);
        bool ResCuttingRequest(int n, char* pBuf);
        bool ResDivisionRequest(int n, char* pBuf);
        bool ResSeparationRequest(int n, char* pBuf);
        bool Revival(int n, char* pBuf);
        bool SelCharRequest(int n, char* pBuf);
        bool SelectClassRequest(int n, char* pBuf);
        bool SelectPcBangRewardRequest(int n, char* pBuf);
        bool SelectWaitedQuestReport(int n, char* pBuf);
        bool SellStoreRequest(int n, char* pBuf);
        bool SendRaceBossMsgFromWebRequest(int n, char* pBuf);
        bool SetGroupMapPointRequest(int n, char* pBuf);
        bool SetGroupTargetObjectRequest(int n, char* pBuf);
        bool SetItemCheckRequest(int n, char* pBuf);
        void SetPassablePacket(unsigned int dwProID, char byHeader1, char byHeader2);
        bool SetRaceBossCryMsgRequest(int n, char* pBuf);
        bool SetTargetObjectRequest(int n, char* pBuf);
        bool SkillRecallTeleportRequest(int n, char* pBuf);
        bool SkillRequest(int n, char* pBuf);
        bool SpecialDownloadRequest(int n, char* pBuf);
        bool Stop(int n, char* pBuf);
        bool StoreListRequest(int n, char* pBuf);
        bool TaiwanBillingUserCertify(int n, char* pBuf);
        bool TalikCrystalExchangeRequest(int n, struct _MSG_HEADER* pHeader, char* pBuf);
        bool TalikRecorverList(int n, char* pBuf);
        bool ThrowSkillRequest(int n, char* pBuf);
        bool ThrowStorageRequest(int n, char* pBuf);
        bool ThrowUnitRequest(int n, char* pBuf);
        bool TotalGuildRankRequest(int n, char* pBuf);
        bool TradeBlockReport(int n, char* pBuf);
        bool TransAccountInform(unsigned int n, char* pMsg);
        bool TransShipRenewTicketRequest(int n, char* pBuf);
        bool TransformSiegeModeRequest(int n, char* pBuf);
        bool TrunkAlterItemSlotRequest(int n, char* pBuf);
        bool TrunkChangePasswdRequest(int n, char* pBuf);
        bool TrunkCreateCostIsFreeRequest(int n, char* pBuf);
        bool TrunkDownloadRequest(int n, char* pBuf);
        bool TrunkEstRequest(int n, char* pBuf);
        bool TrunkExtendRequest(int n, char* pBuf);
        bool TrunkHintAnswerRequest(int n, char* pBuf);
        bool TrunkIoMergeRequest(int n, char* pBuf);
        bool TrunkIoMoneyRequest(int n, char* pBuf);
        bool TrunkIoMoveRequest(int n, char* pBuf);
        bool TrunkIoSwapRequest(int n, char* pBuf);
        bool TrunkPotionDivisionRequest(int n, char* pBuf);
        bool TrunkPwHintIndexRequest(int n, char* pBuf);
        bool TrunkResDivisionRequest(int n, char* pBuf);
        bool TutorialProcessReport(int n, char* pBuf);
        bool UILockInitResult(unsigned int n, char* pMsg);
        bool UILockRefreshResult(unsigned int n, char* pMsg);
        bool UILockUpdateResult(unsigned int n, char* pMsg);
        bool UnitBulletFillRequest(int n, char* pBuf);
        bool UnitBulletReplaceRequest(int n, char* pBuf);
        bool UnitDeliveryRequest(int n, char* pBuf);
        bool UnitFrameBuyRequest(int n, char* pBuf);
        bool UnitFrameRepairRequest(int n, char* pBuf);
        bool UnitLeaveRequest(int n, char* pBuf);
        bool UnitPackFillRequest(int n, char* pBuf);
        bool UnitPartTuningRequest(int n, char* pBuf);
        bool UnitReturnRequest(int n, char* pBuf);
        bool UnitSellRequest(int n, char* pBuf);
        bool UnitTakeRequest(int n, char* pBuf);
        bool UpgradeItemRequest(int n, char* pBuf);
        bool UseFireCrackerItemRequest(int n, char* pBuf);
        bool UsePotionRequest(int n, char* pBuf);
        bool UseRadarItemRequest(int n, char* pBuf);
        bool UseRecallTeleportItemRequest(int n, char* pBuf);
        bool UseRecoverLossExpItemRequest(int n, char* pBuf);
        bool UseSoccerBallItemRequest(int n, char* pBuf);
        bool UserBlockResult(unsigned int n, char* pMsg);
        void UserLoop();
        bool WebAgentLineAnalysis(int n, struct _MSG_HEADER* pMsgHeader, char* pMsg);
        bool WeeklyGuildRankRequest(int n, char* pBuf);
        bool WhisperBlockReport(int n, char* pBuf);
        bool WorldExitInform(unsigned int n, char* pMsg);
        bool WorldMsgInform(unsigned int n, char* pMsg);
        bool WorldServiceInform(unsigned int n, char* pMsg);
        bool ZoneAliveCheckRequest(int n, char* pBuf);
        ~CNetworkEX();
        void dtor_CNetworkEX();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CNetworkEX, 284544>(), "CNetworkEX");
END_ATF_NAMESPACE

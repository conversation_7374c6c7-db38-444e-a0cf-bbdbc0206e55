// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$D702D035F589A3FF876782FB29EF6D4D.hpp>


START_ATF_NAMESPACE
    struct _MERGE_FILE
    {
        $D702D035F589A3FF876782FB29EF6D4D ___u0;
        unsigned int file_length;
        unsigned __int16 name_cnt;
        unsigned __int16 cnt;
        unsigned int start_index;
    };
END_ATF_NAMESPACE

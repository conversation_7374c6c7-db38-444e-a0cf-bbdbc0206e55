// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateCountDown.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateCountDownctor_CNormalGuildBattleStateCountDown2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateCountDown*);
            using GUILD_BATTLE__CNormalGuildBattleStateCountDownctor_CNormalGuildBattleStateCountDown2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateCountDown*, GUILD_BATTLE__CNormalGuildBattleStateCountDownctor_CNormalGuildBattleStateCountDown2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateCountDownEnter4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateCountDown*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateCountDownEnter4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateCountDown*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateCountDownEnter4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateCountDownGetTerm6_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateCountDown*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CNormalGuildBattleStateCountDownGetTerm6_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateCountDown*, struct ATL::CTimeSpan*, GUILD_BATTLE__CNormalGuildBattleStateCountDownGetTerm6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateCountDowndtor_CNormalGuildBattleStateCountDown8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateCountDown*);
            using GUILD_BATTLE__CNormalGuildBattleStateCountDowndtor_CNormalGuildBattleStateCountDown8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateCountDown*, GUILD_BATTLE__CNormalGuildBattleStateCountDowndtor_CNormalGuildBattleStateCountDown8_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

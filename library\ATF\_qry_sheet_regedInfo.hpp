// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_sheet_reged.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_sheet_regedctor__qry_sheet_reged2_ptr = void (WINAPIV*)(struct _qry_sheet_reged*);
        using _qry_sheet_regedctor__qry_sheet_reged2_clbk = void (WINAPIV*)(struct _qry_sheet_reged*, _qry_sheet_regedctor__qry_sheet_reged2_ptr);
        using _qry_sheet_regedsize4_ptr = int (WINAPIV*)(struct _qry_sheet_reged*);
        using _qry_sheet_regedsize4_clbk = int (WINAPIV*)(struct _qry_sheet_reged*, _qry_sheet_regedsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

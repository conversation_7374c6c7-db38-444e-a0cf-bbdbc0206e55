// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <_character_create_setdata.hpp>


START_ATF_NAMESPACE
    struct  _nuclear_create_setdata : _character_create_setdata
    {
        CPlayer *pMaster;
        int nMasterSirial;
        unsigned int m_dwWarnTime;
        unsigned int m_dwAttInformTime;
        unsigned int m_dwAttStartTime;
    public:
        _nuclear_create_setdata();
        void ctor__nuclear_create_setdata();
    };    
    static_assert(ATF::checkSize<_nuclear_create_setdata, 56>(), "_nuclear_create_setdata");
END_ATF_NAMESPACE

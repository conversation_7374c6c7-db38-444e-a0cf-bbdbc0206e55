// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <_object_create_setdata.hpp>


START_ATF_NAMESPACE
    struct  CReturnGateCreateParam : _object_create_setdata
    {
        CPlayer *m_pkOwner;
    public:
        CReturnGateCreateParam(struct CPlayer* pkOwner);
        void ctor_CReturnGateCreateParam(struct CPlayer* pkOwner);
        struct CPlayer* GetOwner();
        ~CReturnGateCreateParam();
        void dtor_CReturnGateCreateParam();
    };    
    static_assert(ATF::checkSize<CReturnGateCreateParam, 40>(), "CReturnGateCreateParam");
END_ATF_NAMESPACE

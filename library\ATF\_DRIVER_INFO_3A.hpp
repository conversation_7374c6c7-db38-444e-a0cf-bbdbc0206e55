// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _DRIVER_INFO_3A
    {
        unsigned int cVersion;
        char *pName;
        char *pEnvironment;
        char *pDriverPath;
        char *pDataFile;
        char *pConfigFile;
        char *pHelpFile;
        char *pDependentFiles;
        char *pMonitorName;
        char *pDefaultDataType;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _PVP_RANK_REFRESH_USER
    {
        unsigned int dwSerial;
        char byLv;
        char byRace;
        unsigned __int16 wPvpRate;
        unsigned int dwPvpRank;
    public:
        void Init();
        bool IsFilled();
        void SetData(unsigned int dwSerialP, char byLvP, char byRaceP);
    };
END_ATF_NAMESPACE

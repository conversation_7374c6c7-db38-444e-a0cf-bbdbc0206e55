// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_quest_history_download_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _quest_history_download_result_zoclctor__quest_history_download_result_zocl2_ptr = void (WINAPIV*)(struct _quest_history_download_result_zocl*);
        using _quest_history_download_result_zoclctor__quest_history_download_result_zocl2_clbk = void (WINAPIV*)(struct _quest_history_download_result_zocl*, _quest_history_download_result_zoclctor__quest_history_download_result_zocl2_ptr);
        using _quest_history_download_result_zoclsize4_ptr = int (WINAPIV*)(struct _quest_history_download_result_zocl*);
        using _quest_history_download_result_zoclsize4_clbk = int (WINAPIV*)(struct _quest_history_download_result_zocl*, _quest_history_download_result_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

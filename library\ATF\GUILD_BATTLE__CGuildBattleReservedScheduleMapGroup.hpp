// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleReservedSchedule.hpp>
#include <GUILD_BATTLE__CGuildBattleSchedule.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleReservedScheduleMapGroup
        {
            bool m_bDone;
            unsigned int m_uiDayInx;
            unsigned int m_uiMapCnt;
            CGuildBattleReservedSchedule **m_ppkReservedSchedule;
        public:
            char Add(unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, struct CGuildBattleSchedule** ppkSchedule, unsigned int* uiSLID);
            CGuildBattleReservedScheduleMapGroup();
            void ctor_CGuildBattleReservedScheduleMapGroup();
            bool CleanUpDanglingReservedSchedule();
            bool Clear(unsigned int uiMapID, unsigned int dwID);
            bool Clear();
            bool CopyUseTimeField(unsigned int uiMapID, bool* pbField);
            void Flip();
            unsigned int GetCurScheduleID(unsigned int uiMapID);
            unsigned int GetDayID();
            bool GetSLID(unsigned int uiMapID, unsigned int* uiSLID);
            bool Init(unsigned int uiDayInx, unsigned int uiMapCnt);
            bool IsDone();
            char IsEmptyTime(unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt);
            bool Load(bool bToday);
            bool Loop();
            void PushDQSClear();
            struct CGuildBattleSchedule* UpdateUseFlag(unsigned int uiMapID, unsigned int dwID);
            ~CGuildBattleReservedScheduleMapGroup();
            void dtor_CGuildBattleReservedScheduleMapGroup();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

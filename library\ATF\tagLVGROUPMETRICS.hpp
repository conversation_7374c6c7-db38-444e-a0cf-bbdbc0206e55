// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagLVGROUPMETRICS
    {
        unsigned int cbSize;
        unsigned int mask;
        unsigned int Left;
        unsigned int Top;
        unsigned int Right;
        unsigned int Bottom;
        unsigned int crLeft;
        unsigned int crTop;
        unsigned int crRight;
        unsigned int crBottom;
        unsigned int crHeader;
        unsigned int crFooter;
    };
END_ATF_NAMESPACE

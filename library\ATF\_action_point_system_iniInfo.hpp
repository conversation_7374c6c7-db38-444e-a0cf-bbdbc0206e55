// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_action_point_system_ini.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _action_point_system_inictor__action_point_system_ini2_ptr = void (WINAPIV*)(struct _action_point_system_ini*);
        using _action_point_system_inictor__action_point_system_ini2_clbk = void (WINAPIV*)(struct _action_point_system_ini*, _action_point_system_inictor__action_point_system_ini2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

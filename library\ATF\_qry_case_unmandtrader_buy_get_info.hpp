// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_unmandtrader_buy_get_info
    {
        struct __list
        {
            unsigned int dwRegistSerial;
            unsigned int dwPrice;
            unsigned int dwSeller;
            char byRaceSexCode;
            unsigned int dwDalant;
            unsigned int dwGuildSerial;
            char byUserGrade;
            unsigned int dwAccountSerial;
            char szAccountID[13];
            char wszName[17];
            unsigned int dwTax;
            char byProcRet;
        };
        unsigned __int16 wInx;
        unsigned int dwBuyer;
        char byRace;
        char byUserGrade;
        char byDivision;
        char byClass;
        char bySubClass;
        char byType;
        char byNum;
        __list List[10];
    };
END_ATF_NAMESPACE

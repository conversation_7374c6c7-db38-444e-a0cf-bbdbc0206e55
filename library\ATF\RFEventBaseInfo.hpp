// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RFEventBase.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using RFEventBaseDoEvent2_ptr = int (WINAPIV*)(struct RFEventBase*, struct CPlayer*);
        using RFEventBaseDoEvent2_clbk = int (WINAPIV*)(struct RFEventBase*, struct CPlayer*, RFEventBaseDoEvent2_ptr);
        using RFEventBaseGetPlayerState4_ptr = char* (WINAPIV*)(struct RFEventBase*, unsigned int, unsigned int);
        using RFEventBaseGetPlayerState4_clbk = char* (WINAPIV*)(struct RFEventBase*, unsigned int, unsigned int, RFEventBaseGetPlayerState4_ptr);
        using RFEventBaseInitialzie6_ptr = bool (WINAPIV*)(struct RFEventBase*);
        using RFEventBaseInitialzie6_clbk = bool (WINAPIV*)(struct RFEventBase*, RFEventBaseInitialzie6_ptr);
        using RFEventBaseIsDbUpdate8_ptr = bool (WINAPIV*)(struct RFEventBase*, unsigned int);
        using RFEventBaseIsDbUpdate8_clbk = bool (WINAPIV*)(struct RFEventBase*, unsigned int, RFEventBaseIsDbUpdate8_ptr);
        using RFEventBaseIsEnable10_ptr = bool (WINAPIV*)(struct RFEventBase*);
        using RFEventBaseIsEnable10_clbk = bool (WINAPIV*)(struct RFEventBase*, RFEventBaseIsEnable10_ptr);
        using RFEventBaseLoop12_ptr = void (WINAPIV*)(struct RFEventBase*);
        using RFEventBaseLoop12_clbk = void (WINAPIV*)(struct RFEventBase*, RFEventBaseLoop12_ptr);
        
        using RFEventBasector_RFEventBase14_ptr = void (WINAPIV*)(struct RFEventBase*);
        using RFEventBasector_RFEventBase14_clbk = void (WINAPIV*)(struct RFEventBase*, RFEventBasector_RFEventBase14_ptr);
        using RFEventBaseSetEvent16_ptr = bool (WINAPIV*)(struct RFEventBase*, char*, int, bool);
        using RFEventBaseSetEvent16_clbk = bool (WINAPIV*)(struct RFEventBase*, char*, int, bool, RFEventBaseSetEvent16_ptr);
        using RFEventBaseSetPlayerState18_ptr = bool (WINAPIV*)(struct RFEventBase*, void*, int);
        using RFEventBaseSetPlayerState18_clbk = bool (WINAPIV*)(struct RFEventBase*, void*, int, RFEventBaseSetPlayerState18_ptr);
        
        using RFEventBasedtor_RFEventBase23_ptr = void (WINAPIV*)(struct RFEventBase*);
        using RFEventBasedtor_RFEventBase23_clbk = void (WINAPIV*)(struct RFEventBase*, RFEventBasedtor_RFEventBase23_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <HINSTANCE__.hpp>
#include <_param_cash_rollback.hpp>
#include <_param_cash_select.hpp>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CRusiaBillingMgr
    {
        CLogFile m_logBill;
        HINSTANCE__ *m_hDll;
        char m_lpszDSN[50];
        char m_lpszServer[50];
        char m_lpszDataBase[50];
        char m_lpszPort[50];
        char m_lpszAccount[50];
        char m_lpszPassword[50];
    public:
        void ArrangeString(char* szDest, char* szSorc, char cToken);
        CRusiaBillingMgr();
        void ctor_CRusiaBillingMgr();
        int CallFunc_Item_Buy(struct _param_cash_update* rParam, int nIdx);
        int CallFunc_Item_Cancel(struct _param_cash_rollback::__list* list, char* szUserID);
        int CallFunc_RFOnline_Auth(struct _param_cash_select* rParam);
        int ConfigUserODBC(char* szDSN, char* szServer, char* szDatabase, uint16_t wPort);
        void DeleteMem();
        int Free();
        int Init();
        struct CRusiaBillingMgr* Instance();
        int LoadINIFile();
        void Release();
        char* dhExtractSubString(char* szSub, char* szFull, char cToken);
        void dhRExtractSubString(char* szSub, char* szFull, char cToken);
        ~CRusiaBillingMgr();
        void dtor_CRusiaBillingMgr();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _PARTICLE_ELEMENT
    {
        float mDirStep[3];
        float mDir[3];
        float mPos[3];
        float mTime;
        float mARGBStep[4];
        float mARGB[4];
        float mScaleStep;
        float mScale;
        float mZRotStep;
        float mZRot;
        float mYRotStep;
        float mYRot;
        char mFlag;
        char mIsLive;
        unsigned __int16 mNowTrack;
        float mNowFrame;
    public:
        void UpDate(float arg_0);
    };
END_ATF_NAMESPACE

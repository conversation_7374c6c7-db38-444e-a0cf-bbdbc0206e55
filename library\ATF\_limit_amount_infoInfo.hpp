// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_limit_amount_info.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _limit_amount_infoctor__limit_amount_info2_ptr = void (WINAPIV*)(struct _limit_amount_info*);
        using _limit_amount_infoctor__limit_amount_info2_clbk = void (WINAPIV*)(struct _limit_amount_info*, _limit_amount_infoctor__limit_amount_info2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

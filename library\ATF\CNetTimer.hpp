// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct CNetTimer
    {
        int m_nTickTerm;
        unsigned int m_dwTickOld;
        bool m_bOper;
    public:
        void BeginTimer(unsigned int dwTerm);
        CNetTimer();
        void ctor_CNetTimer();
        bool CountingTimer();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CNetTimer, 12>(), "CNetTimer");
END_ATF_NAMESPACE

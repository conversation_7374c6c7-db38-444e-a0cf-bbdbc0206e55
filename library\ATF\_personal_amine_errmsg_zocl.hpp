// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _personal_amine_errmsg_zocl
    {
        char byErrCode;
    public:
        _personal_amine_errmsg_zocl();
        void ctor__personal_amine_errmsg_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_personal_amine_errmsg_zocl, 1>(), "_personal_amine_errmsg_zocl");
END_ATF_NAMESPACE

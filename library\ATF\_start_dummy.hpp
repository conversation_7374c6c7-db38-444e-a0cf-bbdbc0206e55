// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    struct _start_dummy
    {
        _dummy_position *m_pDumPos;
    public:
        bool SetDummy(struct _dummy_position* pDumPos);
        _start_dummy();
        void ctor__start_dummy();
    };
END_ATF_NAMESPACE

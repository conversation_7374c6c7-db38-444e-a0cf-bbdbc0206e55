// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _object_list_point
    {
        struct CGameObject *m_pItem;
        struct _object_list_point *m_pNext;
        struct _object_list_point *m_pPrev;
    public:
        void InitLink();
        void SetPoint(struct CGameObject* pItem);
        _object_list_point();
        void ctor__object_list_point();
    };    
    static_assert(ATF::checkSize<_object_list_point, 24>(), "_object_list_point");
END_ATF_NAMESPACE

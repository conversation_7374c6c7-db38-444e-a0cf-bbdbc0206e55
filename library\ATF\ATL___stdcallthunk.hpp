// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        #pragma pack(push, 2)
        struct  _stdcallthunk
        {
            unsigned __int16 RcxMov;
            unsigned __int64 RcxImm;
            unsigned __int16 RaxMov;
            unsigned __int64 RaxImm;
            unsigned __int16 RaxJmp;
        };
        #pragma pack(pop)
    }; // end namespace ATL
END_ATF_NAMESPACE

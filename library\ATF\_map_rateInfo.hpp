// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_map_rate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _map_rateSetting2_ptr = void (WINAPIV*)(struct _map_rate*, int, int);
        using _map_rateSetting2_clbk = void (WINAPIV*)(struct _map_rate*, int, int, _map_rateSetting2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

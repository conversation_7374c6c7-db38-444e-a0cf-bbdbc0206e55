// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct  CCheckSumBaseConverter
    {
    public:
        long double ProcCode(char byIndex, unsigned int dwSerial, long double dValue);
        unsigned int ProcCode(char byIndex, unsigned int dwSerial, unsigned int dwValue);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_POINTL.hpp>
#include <_RECTL.hpp>


START_ATF_NAMESPACE
    struct tagEMRTEXT
    {
        _POINTL ptlReference;
        unsigned int nChars;
        unsigned int offString;
        unsigned int fOptions;
        _RECTL rcl;
        unsigned int offDx;
    };
END_ATF_NAMESPACE

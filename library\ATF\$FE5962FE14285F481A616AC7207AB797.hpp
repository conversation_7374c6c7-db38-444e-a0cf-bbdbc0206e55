// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagVARIANT.hpp>


START_ATF_NAMESPACE
    struct $FE5962FE14285F481A616AC7207AB797
    {
        BYTE gap0[8];
        tagVARIANT *pvarVal;
    };    
    static_assert(ATF::checkSize<$FE5962FE14285F481A616AC7207AB797, 16>(), "$FE5962FE14285F481A616AC7207AB797");
END_ATF_NAMESPACE

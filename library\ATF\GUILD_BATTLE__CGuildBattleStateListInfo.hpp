// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleStateList.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            using GUILD_BATTLE__CGuildBattleStateListAdvance2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, int);
            using GUILD_BATTLE__CGuildBattleStateListAdvance2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, int, GUILD_BATTLE__CGuildBattleStateListAdvance2_ptr);
            
            using GUILD_BATTLE__CGuildBattleStateListctor_CGuildBattleStateList4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, int, int, unsigned int);
            using GUILD_BATTLE__CGuildBattleStateListctor_CGuildBattleStateList4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, int, int, unsigned int, GUILD_BATTLE__CGuildBattleStateListctor_CGuildBattleStateList4_ptr);
            using GUILD_BATTLE__CGuildBattleStateListCheckLoop6_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListCheckLoop6_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListCheckLoop6_ptr);
            using GUILD_BATTLE__CGuildBattleStateListClear8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListClear8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListClear8_ptr);
            using GUILD_BATTLE__CGuildBattleStateListForceNext10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListForceNext10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListForceNext10_ptr);
            using GUILD_BATTLE__CGuildBattleStateListGetTerm12_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CGuildBattleStateListGetTerm12_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, struct ATL::CTimeSpan*, GUILD_BATTLE__CGuildBattleStateListGetTerm12_ptr);
            using GUILD_BATTLE__CGuildBattleStateListGoto14_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListGoto14_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListGoto14_ptr);
            using GUILD_BATTLE__CGuildBattleStateListGotoState16_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, int);
            using GUILD_BATTLE__CGuildBattleStateListGotoState16_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, int, GUILD_BATTLE__CGuildBattleStateListGotoState16_ptr);
            using GUILD_BATTLE__CGuildBattleStateListIsEmpty18_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListIsEmpty18_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListIsEmpty18_ptr);
            using GUILD_BATTLE__CGuildBattleStateListIsProc20_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListIsProc20_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListIsProc20_ptr);
            using GUILD_BATTLE__CGuildBattleStateListLog22_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, char*);
            using GUILD_BATTLE__CGuildBattleStateListLog22_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, char*, GUILD_BATTLE__CGuildBattleStateListLog22_ptr);
            using GUILD_BATTLE__CGuildBattleStateListNext24_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, bool);
            using GUILD_BATTLE__CGuildBattleStateListNext24_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, bool, GUILD_BATTLE__CGuildBattleStateListNext24_ptr);
            using GUILD_BATTLE__CGuildBattleStateListProcess26_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CGuildBattleStateListProcess26_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CGuildBattleStateListProcess26_ptr);
            using GUILD_BATTLE__CGuildBattleStateListSetNextState28_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListSetNextState28_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListSetNextState28_ptr);
            using GUILD_BATTLE__CGuildBattleStateListSetReady30_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListSetReady30_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListSetReady30_ptr);
            using GUILD_BATTLE__CGuildBattleStateListSetWait32_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListSetWait32_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListSetWait32_ptr);
            
            using GUILD_BATTLE__CGuildBattleStateListdtor_CGuildBattleStateList34_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleStateListdtor_CGuildBattleStateList34_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleStateListdtor_CGuildBattleStateList34_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

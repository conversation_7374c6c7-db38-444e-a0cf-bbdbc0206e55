// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _ore_cutting_result_zocl
    {
        struct  _list
        {
            unsigned __int16 wResIndex;
            char by<PERSON>dd<PERSON><PERSON>;
        };
        char byErrCode;
        char byLeftNum;
        unsigned int dwLeftDalant;
        unsigned int dwConsumDalant;
        char byCutting<PERSON>um;
        _list ResList[100];
    public:
        _ore_cutting_result_zocl();
        void ctor__ore_cutting_result_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_ore_cutting_result_zocl, 311>(), "_ore_cutting_result_zocl");
END_ATF_NAMESPACE

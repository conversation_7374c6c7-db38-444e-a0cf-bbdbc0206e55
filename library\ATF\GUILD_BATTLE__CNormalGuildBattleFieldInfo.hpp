// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleField.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, unsigned int, GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, uint16_t, unsigned int, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, uint16_t, unsigned int, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_ptr = struct CPlayer* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_clbk = struct CPlayer* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_ptr = struct CCircleZone* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_clbk = struct CCircleZone* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int, GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_ptr = struct CMapData* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_clbk = struct CMapData* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_ptr = char* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_clbk = char* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int*, int*, int*);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int*, int*, int*, GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_ptr = struct CGravityStoneRegener* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_clbk = struct CGravityStoneRegener* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int, GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_ptr = struct CGravityStone* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_clbk = struct CGravityStone* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldInit54_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleFieldInit54_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, unsigned int, GUILD_BATTLE__CNormalGuildBattleFieldInit54_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*, int);
            using GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct CPlayer*, int, GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, char*, char*, char*, unsigned int*, struct _dummy_position***);
            using GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, char*, char*, char*, unsigned int*, struct _dummy_position***, GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, char, char, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, char, char, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct _dummy_position**, int**, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, struct _dummy_position**, int**, unsigned int, GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldStart66_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, char, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldStart66_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, char, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldStart66_ptr);
            using GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int, struct CPlayer*);
            using GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, int, struct CPlayer*, GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*);
            using GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleField*, GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

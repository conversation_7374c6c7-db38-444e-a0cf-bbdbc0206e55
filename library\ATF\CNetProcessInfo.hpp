// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetProcess.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CNetProcessAcceptThread2_ptr = void (WINAPIV*)(void*);
        using CNetProcessAcceptThread2_clbk = void (WINAPIV*)(void*, CNetProcessAcceptThread2_ptr);
        
        using CNetProcessctor_CNetProcess4_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcessctor_CNetProcess4_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcessctor_CNetProcess4_ptr);
        using CNetProcessCloseAll6_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcessCloseAll6_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcessCloseAll6_ptr);
        using CNetProcessCloseSocket8_ptr = void (WINAPIV*)(struct CNetProcess*, unsigned int, bool);
        using CNetProcessCloseSocket8_clbk = void (WINAPIV*)(struct CNetProcess*, unsigned int, bool, CNetProcessCloseSocket8_ptr);
        using CNetProcessCompleteAnsyncConnect10_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcessCompleteAnsyncConnect10_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcessCompleteAnsyncConnect10_ptr);
        using CNetProcessConnectThread12_ptr = void (WINAPIV*)(void*);
        using CNetProcessConnectThread12_clbk = void (WINAPIV*)(void*, CNetProcessConnectThread12_ptr);
        using CNetProcessFindKeyFromWaitList14_ptr = bool (WINAPIV*)(struct CNetProcess*, unsigned int, unsigned int, unsigned int*, int);
        using CNetProcessFindKeyFromWaitList14_clbk = bool (WINAPIV*)(struct CNetProcess*, unsigned int, unsigned int, unsigned int*, int, CNetProcessFindKeyFromWaitList14_ptr);
        using CNetProcessGetContextHandle16_ptr = void* (WINAPIV*)(struct CNetProcess*, uint16_t);
        using CNetProcessGetContextHandle16_clbk = void* (WINAPIV*)(struct CNetProcess*, uint16_t, CNetProcessGetContextHandle16_ptr);
        using CNetProcessGetSendThreadFrame18_ptr = unsigned int (WINAPIV*)(struct CNetProcess*);
        using CNetProcessGetSendThreadFrame18_clbk = unsigned int (WINAPIV*)(struct CNetProcess*, CNetProcessGetSendThreadFrame18_ptr);
        using CNetProcessIOLogFileOperSetting20_ptr = void (WINAPIV*)(struct CNetProcess*, bool);
        using CNetProcessIOLogFileOperSetting20_clbk = void (WINAPIV*)(struct CNetProcess*, bool, CNetProcessIOLogFileOperSetting20_ptr);
        using CNetProcessLoadSendMsg22_ptr = int (WINAPIV*)(struct CNetProcess*, unsigned int, char*, char*, uint16_t);
        using CNetProcessLoadSendMsg22_clbk = int (WINAPIV*)(struct CNetProcess*, unsigned int, char*, char*, uint16_t, CNetProcessLoadSendMsg22_ptr);
        using CNetProcessLoadSendMsg24_ptr = int (WINAPIV*)(struct CNetProcess*, unsigned int, uint16_t, char*, uint16_t);
        using CNetProcessLoadSendMsg24_clbk = int (WINAPIV*)(struct CNetProcess*, unsigned int, uint16_t, char*, uint16_t, CNetProcessLoadSendMsg24_ptr);
        using CNetProcessLogFileOperSetting26_ptr = void (WINAPIV*)(struct CNetProcess*, bool, bool, bool);
        using CNetProcessLogFileOperSetting26_clbk = void (WINAPIV*)(struct CNetProcess*, bool, bool, bool, CNetProcessLogFileOperSetting26_ptr);
        using CNetProcessNetEventThread28_ptr = void (WINAPIV*)(void*);
        using CNetProcessNetEventThread28_clbk = void (WINAPIV*)(void*, CNetProcessNetEventThread28_ptr);
        using CNetProcessOnLoop30_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcessOnLoop30_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcessOnLoop30_ptr);
        using CNetProcessOnLoop_Receipt32_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcessOnLoop_Receipt32_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcessOnLoop_Receipt32_ptr);
        using CNetProcessPushAnsyncConnect34_ptr = bool (WINAPIV*)(struct CNetProcess*, unsigned int, struct sockaddr_in*);
        using CNetProcessPushAnsyncConnect34_clbk = bool (WINAPIV*)(struct CNetProcess*, unsigned int, struct sockaddr_in*, CNetProcessPushAnsyncConnect34_ptr);
        using CNetProcessPushCloseNode36_ptr = void (WINAPIV*)(struct CNetProcess*, int);
        using CNetProcessPushCloseNode36_clbk = void (WINAPIV*)(struct CNetProcess*, int, CNetProcessPushCloseNode36_ptr);
        using CNetProcessPushKeyCheckList38_ptr = bool (WINAPIV*)(struct CNetProcess*, unsigned int, unsigned int, unsigned int*, int);
        using CNetProcessPushKeyCheckList38_clbk = bool (WINAPIV*)(struct CNetProcess*, unsigned int, unsigned int, unsigned int*, int, CNetProcessPushKeyCheckList38_ptr);
        using CNetProcessRecvThread40_ptr = void (WINAPIV*)(void*);
        using CNetProcessRecvThread40_clbk = void (WINAPIV*)(void*, CNetProcessRecvThread40_ptr);
        using CNetProcessRelease42_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcessRelease42_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcessRelease42_ptr);
        using CNetProcessSendThread44_ptr = void (WINAPIV*)(void*);
        using CNetProcessSendThread44_clbk = void (WINAPIV*)(void*, CNetProcessSendThread44_ptr);
        using CNetProcessSetContextHandle46_ptr = void (WINAPIV*)(struct CNetProcess*, void*, uint16_t);
        using CNetProcessSetContextHandle46_clbk = void (WINAPIV*)(struct CNetProcess*, void*, uint16_t, CNetProcessSetContextHandle46_ptr);
        using CNetProcessSetProcess48_ptr = bool (WINAPIV*)(struct CNetProcess*, int, struct _NET_TYPE_PARAM*, struct CNetWorking*, bool);
        using CNetProcessSetProcess48_clbk = bool (WINAPIV*)(struct CNetProcess*, int, struct _NET_TYPE_PARAM*, struct CNetWorking*, bool, CNetProcessSetProcess48_ptr);
        using CNetProcessStartSpeedHackCheck50_ptr = bool (WINAPIV*)(struct CNetProcess*, unsigned int, char*);
        using CNetProcessStartSpeedHackCheck50_clbk = bool (WINAPIV*)(struct CNetProcess*, unsigned int, char*, CNetProcessStartSpeedHackCheck50_ptr);
        using CNetProcess_CheckSend52_ptr = void (WINAPIV*)(struct CNetProcess*, uint16_t);
        using CNetProcess_CheckSend52_clbk = void (WINAPIV*)(struct CNetProcess*, uint16_t, CNetProcess_CheckSend52_ptr);
        using CNetProcess_CheckWaitKey54_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcess_CheckWaitKey54_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcess_CheckWaitKey54_ptr);
        using CNetProcess_CkeckKeyCertifyDeley56_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcess_CkeckKeyCertifyDeley56_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcess_CkeckKeyCertifyDeley56_ptr);
        using CNetProcess_CkeckRecvBreak58_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcess_CkeckRecvBreak58_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcess_CkeckRecvBreak58_ptr);
        using CNetProcess_CkeckSpeedHackDeley60_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcess_CkeckSpeedHackDeley60_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcess_CkeckSpeedHackDeley60_ptr);
        using CNetProcess_ForceCloseLoop62_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcess_ForceCloseLoop62_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcess_ForceCloseLoop62_ptr);
        using CNetProcess_InternalPacketProcess64_ptr = bool (WINAPIV*)(struct CNetProcess*, unsigned int, struct _MSG_HEADER*, char*);
        using CNetProcess_InternalPacketProcess64_clbk = bool (WINAPIV*)(struct CNetProcess*, unsigned int, struct _MSG_HEADER*, char*, CNetProcess_InternalPacketProcess64_ptr);
        using CNetProcess_PopRecvMsg66_ptr = void (WINAPIV*)(struct CNetProcess*, uint16_t);
        using CNetProcess_PopRecvMsg66_clbk = void (WINAPIV*)(struct CNetProcess*, uint16_t, CNetProcess_PopRecvMsg66_ptr);
        using CNetProcess_Receipt68_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcess_Receipt68_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcess_Receipt68_ptr);
        using CNetProcess_ResponSpeedHack70_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcess_ResponSpeedHack70_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcess_ResponSpeedHack70_ptr);
        using CNetProcess_SendLoop72_ptr = void (WINAPIV*)(struct CNetProcess*, unsigned int);
        using CNetProcess_SendLoop72_clbk = void (WINAPIV*)(struct CNetProcess*, unsigned int, CNetProcess_SendLoop72_ptr);
        using CNetProcess_SendSpeedHackCheckMsg74_ptr = void (WINAPIV*)(struct CNetProcess*, int);
        using CNetProcess_SendSpeedHackCheckMsg74_clbk = void (WINAPIV*)(struct CNetProcess*, int, CNetProcess_SendSpeedHackCheckMsg74_ptr);
        using CNetProcesswt_AcceptClient79_ptr = bool (WINAPIV*)(struct CNetProcess*, unsigned int*);
        using CNetProcesswt_AcceptClient79_clbk = bool (WINAPIV*)(struct CNetProcess*, unsigned int*, CNetProcesswt_AcceptClient79_ptr);
        using CNetProcesswt_CloseClient81_ptr = bool (WINAPIV*)(struct CNetProcess*, unsigned int);
        using CNetProcesswt_CloseClient81_clbk = bool (WINAPIV*)(struct CNetProcess*, unsigned int, CNetProcesswt_CloseClient81_ptr);
        
        using CNetProcessdtor_CNetProcess83_ptr = void (WINAPIV*)(struct CNetProcess*);
        using CNetProcessdtor_CNetProcess83_clbk = void (WINAPIV*)(struct CNetProcess*, CNetProcessdtor_CNetProcess83_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

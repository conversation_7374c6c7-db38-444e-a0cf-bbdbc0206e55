// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitRightInfo.hpp>
#include <CPlayer.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CMoveMapLimitRightInfoList
    {
        std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > m_vecRight;
    public:
        CMoveMapLimitRightInfoList();
        void ctor_CMoveMapLimitRightInfoList();
        void CreateComplete(struct CPlayer* pkPlayer);
        struct CMoveMapLimitRightInfo* Get(int iInx);
        bool Init(struct std::vector<int,std::allocator<int> >* vecRightTypeList);
        void Load(struct CPlayer* pkPlayer);
        void LogIn(struct CPlayer* pkPlayer);
        void LogOut(struct CPlayer* pkPlayer);
        ~CMoveMapLimitRightInfoList();
        void dtor_CMoveMapLimitRightInfoList();
    };
END_ATF_NAMESPACE

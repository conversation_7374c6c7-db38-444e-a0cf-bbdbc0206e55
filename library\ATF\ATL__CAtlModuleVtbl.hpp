// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct CAtlModuleVtbl
        {
            void *(WINAPIV *__vecDelDtor)(CAtlModule *_this, unsigned int);
            int (WINAPIV *Lock)(CAtlModule *_this);
            int (WINAPIV *Unlock)(CAtlModule *_this);
            int (WINAPIV *GetLockCount)(CAtlModule *_this);
            HRESULT (WINAPIV *GetGITPtr)(CAtlModule *_this, IGlobalInterfaceTable **);
            HRESULT (WINAPIV *AddCommonRGSReplacements)(CAtlModule *_this, IRegistrarBase *);
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

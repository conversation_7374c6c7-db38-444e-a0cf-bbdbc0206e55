// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PVPPOINT_LIMIT_DB_BASE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _PVPPOINT_LIMIT_DB_BASEInit2_ptr = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*);
        using _PVPPOINT_LIMIT_DB_BASEInit2_clbk = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*, _PVPPOINT_LIMIT_DB_BASEInit2_ptr);
        
        using _PVPPOINT_LIMIT_DB_BASEctor__PVPPOINT_LIMIT_DB_BASE4_ptr = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*);
        using _PVPPOINT_LIMIT_DB_BASEctor__PVPPOINT_LIMIT_DB_BASE4_clbk = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*, _PVPPOINT_LIMIT_DB_BASEctor__PVPPOINT_LIMIT_DB_BASE4_ptr);
        
        using _PVPPOINT_LIMIT_DB_BASEdtor__PVPPOINT_LIMIT_DB_BASE6_ptr = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*);
        using _PVPPOINT_LIMIT_DB_BASEdtor__PVPPOINT_LIMIT_DB_BASE6_clbk = void (WINAPIV*)(struct _PVPPOINT_LIMIT_DB_BASE*, _PVPPOINT_LIMIT_DB_BASEdtor__PVPPOINT_LIMIT_DB_BASE6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

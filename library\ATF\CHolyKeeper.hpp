// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAttack.hpp>
#include <CCharacter.hpp>
#include <CMyTimer.hpp>
#include <_attack_param.hpp>
#include <_dummy_position.hpp>
#include <_keeper_create_setdata.hpp>
#include <_monster_fld.hpp>
#include <_object_id.hpp>


START_ATF_NAMESPACE
    struct  CHolyKeeper : CCharacter
    {
        int m_nHP;
        unsigned int m_dwLastDestroyTime;
        _monster_fld *m_pRec;
        _dummy_position *m_pPosCreate;
        _dummy_position *m_pPosActive;
        _dummy_position *m_pPosCenter;
        int m_nMasterRace;
        bool m_bExit;
        bool m_bChaos;
        unsigned int m_dwLastStopTime;
        int m_nDefPart[5];
        struct CPlayer *m_pLastMoveTarget;
        CAttack *m_at;
        _attack_param m_ap;
        bool m_bDamageAbleState;
        int m_nCurrLootIndex;
        int m_nEndLootIndex;
        int m_nCurrDropIndex;
        unsigned __int16 m_wMagnifications;
        unsigned __int16 m_wRange;
        unsigned __int16 m_wDropCntOnce;
        CMyTimer m_tmrDropTime;
    public:
        CHolyKeeper();
        void ctor_CHolyKeeper();
        bool CheckAttack();
        void CheckCurPos();
        void CheckExit();
        void CheckMove();
        bool Create(struct _keeper_create_setdata* pData, int nCreateType);
        bool Destroy(char byDestroyCode, struct CCharacter* pAtter);
        void DropItem();
        bool Exit();
        int GetAttackDP();
        float* GetAttackPivot();
        float GetAttackRange();
        int GetDefFC(int nAttactPart, struct CCharacter* pAttChar, int* pnConvertPart);
        float GetDefFacing(int nPart);
        float GetDefGap(int nPart);
        int GetDefSkill(bool bBackAttack);
        int GetFireTol();
        int GetGenAttackProb(struct CCharacter* pDst, int nPart, bool bBackAttack);
        int GetHP();
        int GetLevel();
        int GetMaxHP();
        char* GetObjName();
        int GetObjRace();
        int GetSoilTol();
        int GetWaterTol();
        float GetWeaponAdjust();
        int GetWeaponClass();
        float GetWidth();
        int GetWindTol();
        bool Init(struct _object_id* pID);
        bool IsBeAttackedAble(bool bFirst);
        bool IsBeDamagedAble(struct CCharacter* pAtter);
        void Loop();
        void OutOfSec();
        struct CCharacter* SearchAttackTarget();
        struct CPlayer* SearchMoveTarget();
        void SendMsg_Attack();
        void SendMsg_Create();
        void SendMsg_Destroy(char byDesType);
        void SendMsg_FixPosition(int n);
        void SendMsg_Move();
        void SendMsg_RealMovePoint(int n);
        int SetDamage(int nDam, struct CCharacter* pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn);
        void SetDamageAbleState(bool bDamageAbState);
        void SetDropItem();
        void SetHP(int nHP);
        void SetMaxHP(int nMaxHP);
        void SetStateChaos();
        ~CHolyKeeper();
        void dtor_CHolyKeeper();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_REGED.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _REGEDRelease2_ptr = bool (WINAPIV*)(struct _REGED*, char);
        using _REGEDRelease2_clbk = bool (WINAPIV*)(struct _REGED*, char, _REGEDRelease2_ptr);
        using _REGEDSet4_ptr = bool (WINAPIV*)(struct _REGED*, char, struct _STORAGE_LIST::_db_con*);
        using _REGEDSet4_clbk = bool (WINAPIV*)(struct _REGED*, char, struct _STORAGE_LIST::_db_con*, _REGEDSet4_ptr);
        using _REGEDUpdateEquipLv6_ptr = void (WINAPIV*)(struct _REGED*);
        using _REGEDUpdateEquipLv6_clbk = void (WINAPIV*)(struct _REGED*, _REGEDUpdateEquipLv6_ptr);
        
        using _REGEDctor__REGED8_ptr = void (WINAPIV*)(struct _REGED*);
        using _REGEDctor__REGED8_clbk = void (WINAPIV*)(struct _REGED*, _REGEDctor__REGED8_ptr);
        using _REGEDinit10_ptr = void (WINAPIV*)(struct _REGED*);
        using _REGEDinit10_clbk = void (WINAPIV*)(struct _REGED*, _REGEDinit10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

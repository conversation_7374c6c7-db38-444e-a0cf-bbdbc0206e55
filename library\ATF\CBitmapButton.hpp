// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBitmap.hpp>
#include <CButton.hpp>


START_ATF_NAMESPACE
    struct  CBitmapButton : CButton
    {
        CBitmap m_bitmap;
        CBitmap m_bitmapSel;
        CBitmap m_bitmapFocus;
        CBitmap m_bitmapDisabled;
    };
END_ATF_NAMESPACE

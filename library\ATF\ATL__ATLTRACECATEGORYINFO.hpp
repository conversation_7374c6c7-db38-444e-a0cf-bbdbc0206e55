// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__ATLTRACESETTINGS.hpp>



START_ATF_NAMESPACE
    namespace ATL
    {
        struct ATLTRACECATEGORYINFO
        {
            wchar_t szName[64];
            ATLTRACESETTINGS settings;
            unsigned __int64 dwCategory;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

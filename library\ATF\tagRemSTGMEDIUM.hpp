// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct tagRemSTGMEDIUM
    {
        unsigned int tymed;
        unsigned int dwHandleType;
        unsigned int pData;
        unsigned int pUnkForRelease;
        unsigned int cbData;
        char data[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

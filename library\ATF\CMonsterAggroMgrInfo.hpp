// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonsterAggroMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMonsterAggroMgrctor_CMonsterAggroMgr2_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgrctor_CMonsterAggroMgr2_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgrctor_CMonsterAggroMgr2_ptr);
        using CMonsterAggroMgrGetKingPowerDamageCharacter4_ptr = struct CCharacter* (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgrGetKingPowerDamageCharacter4_clbk = struct CCharacter* (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgrGetKingPowerDamageCharacter4_ptr);
        using CMonsterAggroMgrGetTopAggroCharacter6_ptr = struct CCharacter* (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgrGetTopAggroCharacter6_clbk = struct CCharacter* (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgrGetTopAggroCharacter6_ptr);
        using CMonsterAggroMgrGetTopDamageCharacter8_ptr = struct CCharacter* (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgrGetTopDamageCharacter8_clbk = struct CCharacter* (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgrGetTopDamageCharacter8_ptr);
        using CMonsterAggroMgrInit10_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgrInit10_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgrInit10_ptr);
        using CMonsterAggroMgrOnlyOnceInit12_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*, struct CMonster*);
        using CMonsterAggroMgrOnlyOnceInit12_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, struct CMonster*, CMonsterAggroMgrOnlyOnceInit12_ptr);
        using CMonsterAggroMgrProcess14_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgrProcess14_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgrProcess14_ptr);
        using CMonsterAggroMgrResetAggro16_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgrResetAggro16_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgrResetAggro16_ptr);
        using CMonsterAggroMgrSearchAggroNode18_ptr = struct CAggroNode* (WINAPIV*)(struct CMonsterAggroMgr*, struct CCharacter*);
        using CMonsterAggroMgrSearchAggroNode18_clbk = struct CAggroNode* (WINAPIV*)(struct CMonsterAggroMgr*, struct CCharacter*, CMonsterAggroMgrSearchAggroNode18_ptr);
        using CMonsterAggroMgrSendChangeAggroData20_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgrSendChangeAggroData20_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgrSendChangeAggroData20_ptr);
        using CMonsterAggroMgrSetAggro22_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*, struct CCharacter*, int, int, unsigned int, int, int);
        using CMonsterAggroMgrSetAggro22_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, struct CCharacter*, int, int, unsigned int, int, int, CMonsterAggroMgrSetAggro22_ptr);
        using CMonsterAggroMgrSetTopAggroCharacter24_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*, struct CCharacter*);
        using CMonsterAggroMgrSetTopAggroCharacter24_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, struct CCharacter*, CMonsterAggroMgrSetTopAggroCharacter24_ptr);
        using CMonsterAggroMgrShortRankDelay26_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*, unsigned int);
        using CMonsterAggroMgrShortRankDelay26_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, unsigned int, CMonsterAggroMgrShortRankDelay26_ptr);
        using CMonsterAggroMgr_GetBlinkNode28_ptr = struct CAggroNode* (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgr_GetBlinkNode28_clbk = struct CAggroNode* (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgr_GetBlinkNode28_ptr);
        using CMonsterAggroMgr_SearchAggroNode30_ptr = struct CAggroNode* (WINAPIV*)(struct CMonsterAggroMgr*, struct CCharacter*);
        using CMonsterAggroMgr_SearchAggroNode30_clbk = struct CAggroNode* (WINAPIV*)(struct CMonsterAggroMgr*, struct CCharacter*, CMonsterAggroMgr_SearchAggroNode30_ptr);
        using CMonsterAggroMgr_ShortRank32_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgr_ShortRank32_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgr_ShortRank32_ptr);
        
        using CMonsterAggroMgrdtor_CMonsterAggroMgr34_ptr = void (WINAPIV*)(struct CMonsterAggroMgr*);
        using CMonsterAggroMgrdtor_CMonsterAggroMgr34_clbk = void (WINAPIV*)(struct CMonsterAggroMgr*, CMonsterAggroMgrdtor_CMonsterAggroMgr34_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

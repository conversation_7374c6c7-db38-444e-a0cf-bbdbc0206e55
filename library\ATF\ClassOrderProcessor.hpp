// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <Cmd.hpp>
#include <ElectProcessor.hpp>
#include <_pt_appoint_inform_request_zocl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  ClassOrderProcessor : ElectProcessor
    {
        char _byPtType[2];
        _pt_appoint_inform_request_zocl _kSend[3];
    public:
        ClassOrderProcessor();
        void ctor_ClassOrderProcessor();
        int Doit(Cmd eCmd, struct CPlayer* pOne, char* pdata);
        bool Initialize();
        static struct ClassOrderProcessor* Instance();
        void SendMsg_PatriarchInform(struct CPlayer* pOne);
        void SendMsg_QueryAppointResult(uint16_t wIndex, char byRet, char byClassType, char* pwszAvatorName);
        void UpdatePacket(char byRace, char byClassType);
        int _CheckUserInfo(char byRace, char byClassType, struct CPlayer* pUser);
        int _QueryAppoint(struct CPlayer* pOne, char* pData);
        int _RequestAppoint(struct CPlayer* pOne, char* pData);
        int _RequestDischarge(struct CPlayer* pOne, char* pData);
        int _ResponseAppoint(struct CPlayer* pOne, char* pData);
        ~ClassOrderProcessor();
        void dtor_ClassOrderProcessor();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

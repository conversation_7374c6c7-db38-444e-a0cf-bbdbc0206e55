// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _BUDDY_DB_BASE
    {
        struct  _LIST
        {
            unsigned int dwSerial;
            char wszName[17];
        public:
            void Init();
            bool IsFilled();
            _LIST();
            void ctor__LIST();
        };
        _LIST m_List[50];
    public:
        void Init();
        _BUDDY_DB_BASE();
        void ctor__BUDDY_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_BUDDY_DB_BASE, 1050>(), "_BUDDY_DB_BASE");
END_ATF_NAMESPACE

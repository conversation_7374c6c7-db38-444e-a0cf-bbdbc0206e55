// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HBRUSH__.hpp>
#include <HPEN__.hpp>
#include <tagNMCUSTOMDRAWINFO.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _NMTBCUSTOMDRAW
    {
        tagNMCUSTOMDRAWINFO nmcd;
        HBRUSH__ *hbrMonoDither;
        HBRUSH__ *hbrLines;
        HPEN__ *hpenLines;
        unsigned int clrText;
        unsigned int clrMark;
        unsigned int clrTextHighlight;
        unsigned int clrBtnFace;
        unsigned int clrBtnHighlight;
        unsigned int clrHighlightHotTrack;
        tagRECT rcText;
        int nStringBkMode;
        int nHLStringBkMode;
        int iListGap;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

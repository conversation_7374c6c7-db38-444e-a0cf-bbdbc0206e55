// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  waveformat_tag
    {
        unsigned __int16 wFormatTag;
        unsigned __int16 nChannels;
        unsigned int nSamplesPerSec;
        unsigned int nAvgBytesPerSec;
        unsigned __int16 nBlockAlign;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

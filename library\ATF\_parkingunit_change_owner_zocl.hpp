// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _parkingunit_change_owner_zocl
    {
        unsigned __int16 wObjIndex;
        unsigned int dwObjSerial;
        unsigned int dwOldOwnerSerial;
        unsigned int dwNewOwnerSerial;
        char byNewOwnerUnitSlotIndex;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$980F6A28C67C8F3640106D01D4C85A6A.hpp>


START_ATF_NAMESPACE
    struct _IMAGE_IMPORT_DESCRIPTOR
    {
        $980F6A28C67C8F3640106D01D4C85A6A ___u0;
        unsigned int TimeDateStamp;
        unsigned int ForwarderChain;
        unsigned int Name;
        unsigned int FirstThunk;
    };
END_ATF_NAMESPACE

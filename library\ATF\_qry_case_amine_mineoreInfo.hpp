// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_amine_mineore.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_amine_mineorector__qry_case_amine_mineore2_ptr = void (WINAPIV*)(struct _qry_case_amine_mineore*);
        using _qry_case_amine_mineorector__qry_case_amine_mineore2_clbk = void (WINAPIV*)(struct _qry_case_amine_mineore*, _qry_case_amine_mineorector__qry_case_amine_mineore2_ptr);
        using _qry_case_amine_mineoresize4_ptr = int (WINAPIV*)(struct _qry_case_amine_mineore*);
        using _qry_case_amine_mineoresize4_clbk = int (WINAPIV*)(struct _qry_case_amine_mineore*, _qry_case_amine_mineoresize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $EA00B0D50EAA1933F67C45009B664198
    {
        BYTE gap0[8];
        unsigned __int64 ullVal;
    };    
    static_assert(ATF::checkSize<$EA00B0D50EAA1933F67C45009B664198, 16>(), "$EA00B0D50EAA1933F67C45009B664198");
END_ATF_NAMESPACE

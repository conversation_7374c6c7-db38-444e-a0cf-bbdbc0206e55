// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimerVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMyTimer
    {
        CMyTimerVtbl *vfptr;
        int m_nTickTerm;
        unsigned int m_dwTickOld;
        bool m_bOper;
    public:
        void BeginTimer(unsigned int dwTerm);
        void BeginTimerAddLapse(unsigned int dwTerm, unsigned int dwAddLapse);
        CMyTimer();
        void ctor_CMyTimer();
        void CountingAddTickOld(unsigned int dwAddGap);
        bool CountingTimer();
        unsigned int GetTerm();
        void NextTimeRun();
        void StopTimer();
        void TermTimeRun();
        ~CMyTimer();
        void dtor_CMyTimer();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CMyTimer, 24>(), "CMyTimer");
END_ATF_NAMESPACE

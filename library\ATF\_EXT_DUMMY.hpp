// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$5CA2DEBC757552518522447F5D1B1DF5.hpp>


START_ATF_NAMESPACE
    struct _EXT_DUMMY
    {
        char mName[32];
        unsigned int mFlag;
        float mBBmin[3];
        float mBBmax[3];
        unsigned int mID;
        $5CA2DEBC757552518522447F5D1B1DF5 ___u5;
        float mMat[4][4];
        float mInvMat[4][4];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  PROPPRG
    {
        unsigned __int16 flPrg;
        unsigned __int16 flPrgInit;
        char achTitle[30];
        char achCmd<PERSON>ine[128];
        char achWorkDir[64];
        unsigned __int16 wHotKey;
        char achIconFile[80];
        unsigned __int16 wIconIndex;
        unsigned int dwEnhModeFlags;
        unsigned int dwRealModeFlags;
        char achOtherFile[80];
        char achPIFFile[260];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

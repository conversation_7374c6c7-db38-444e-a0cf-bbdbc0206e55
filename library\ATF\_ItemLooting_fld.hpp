// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _ItemLooting_fld : _base_fld
    {
        int m_nLootRate;
        int m_nLootTime;
        int m_nOperationCount;
        int m_nLootListCount;
        char m_itmLootCodeKey[200][8];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuild.hpp>
#include <CMyTimer.hpp>
#include <CNetIndexList.hpp>
#include <_suggested_matter.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMgrGuildHistory
    {
        struct __LOG_DATA
        {
            char szFileName[128];
            int nLen;
            char sData[300];
        };
        char m_szStdPath[128];
        unsigned int m_dwLastLocalDate;
        unsigned int m_dwLastLocalHour;
        CMyTimer m_tmrUpdateTime;
        char m_szCurTime[32];
        __LOG_DATA m_LogData[2532];
        CNetIndexList m_listLogData;
        CNetIndexList m_listLogDataEmpty;
        bool m_bIOThread;
    public:
        CMgrGuildHistory();
        void ctor_CMgrGuildHistory();
        void GetNewFileName(unsigned int dwGuildSerial, char* pszFileName);
        int GetTotalWaitSize();
        static void IOThread(void* pv);
        void OnLoop();
        void WriteFile(char* pszFileName, char* pszLog);
        void change_atrade_taxrate(char* pszSugerName, unsigned int dwSugerSerial, char byCurTax, char byNextTax, char* pszFileName);
        void join_member(char* pszJoinerName, unsigned int dwJoinerSerial, char* pszOKerName, unsigned int dwOKSerial, int nMemNum, char* pszFileName);
        void leave_member(char* pszLeaverName, unsigned int dwLeaverSerial, bool bSelf, int nMemNum, char* pszFileName, bool bPunish);
        void load_guild(struct CGuild* pGuild, char* pszFileName);
        void pop_money(char* pszIOerName, unsigned int dwIOerSerial, int nPopDalant, int nPopGold, long double dTotalDalant, long double dTotalGold, char* pszFileName);
        void push_money(char* pszIOerName, unsigned int dwIOerSerial, int nPushDalant, int nPushGold, long double dTotalDalant, long double dTotalGold, char* pszFileName);
        void start_guild(struct CGuild* pGuild, char* pszFileName);
        void suggest_cancel(char* pszSugerName, unsigned int dwSugerSerial, struct _suggested_matter* pMatter, char* pszFileName);
        void suggest_complete(char* pszSugerName, unsigned int dwSugerSerial, struct _suggested_matter* pMatter, bool bPass, char* pszFileName);
        void suggest_vote(char* pszSugerName, unsigned int dwSugerSerial, struct _suggested_matter* pMatter, char* pszFileName);
        ~CMgrGuildHistory();
        void dtor_CMgrGuildHistory();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CMgrGuildHistory, 1094344>(), "CMgrGuildHistory");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBilling.hpp>
#include <CTSingleton.hpp>
#include <CUserDB.hpp>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    struct  CBillingManager : CTSingleton<CBillingManager>
    {
        CBilling *m_pBill;
    public:
        void Alive(struct CUserDB* pUserDB)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, struct CUserDB*);
            (org_ptr(0x14007c1f0L))(this, pUserDB);
        };
        void BillingClose(char* szID)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, char*);
            (org_ptr(0x1401c3f80L))(this, szID);
        };
        CBillingManager()
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*);
            (org_ptr(0x14028dce0L))(this);
        };
        void ctor_CBillingManager()
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*);
            (org_ptr(0x14028dce0L))(this);
        };
        void Change_BillingType(char* szID, char* szCMSCode, int16_t iType, int lRemainTime, struct _SYSTEMTIME* pstEndDate, char byReason)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, char*, char*, int16_t, int, struct _SYSTEMTIME*, char);
            (org_ptr(0x1401c40e0L))(this, szID, szCMSCode, iType, lRemainTime, pstEndDate, byReason);
        };
        void Change_Primium(char* szID, bool bResult)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, char*, bool);
            (org_ptr(0x1401c4310L))(this, szID, bResult);
        };
        void Expire_IPOverflow(char* szID)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, char*);
            (org_ptr(0x1401c4250L))(this, szID);
        };
        void Expire_PCBang(char* szCMS)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, char*);
            (org_ptr(0x1401c41f0L))(this, szCMS);
        };
        void Expire_Personal(char* szID)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, char*);
            (org_ptr(0x1401c4180L))(this, szID);
        };
        bool Init()
        {
            using org_ptr = bool (WINAPIV*)(struct CBillingManager*);
            return (org_ptr(0x14028dd40L))(this);
        };
        bool IsOperate()
        {
            using org_ptr = bool (WINAPIV*)(struct CBillingManager*);
            return (org_ptr(0x140207e30L))(this);
        };
        bool LoadINI()
        {
            using org_ptr = bool (WINAPIV*)(struct CBillingManager*);
            return (org_ptr(0x14028ddd0L))(this);
        };
        void Login(struct CUserDB* pUserDB)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, struct CUserDB*);
            (org_ptr(0x140079030L))(this, pUserDB);
        };
        void Logout(struct CUserDB* pUserDB)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, struct CUserDB*);
            (org_ptr(0x14007a400L))(this, pUserDB);
        };
        void Remaintime_PCBang(char* szCMSCode, int16_t iType, int lRemaintime, struct _SYSTEMTIME* pstEndDate)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, char*, int16_t, int, struct _SYSTEMTIME*);
            (org_ptr(0x1401c4060L))(this, szCMSCode, iType, lRemaintime, pstEndDate);
        };
        void Remaintime_Personal(char* szID, int16_t iType, int lRemaintime, struct _SYSTEMTIME* pstEndDate)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, char*, int16_t, int, struct _SYSTEMTIME*);
            (org_ptr(0x1401c3fe0L))(this, szID, iType, lRemaintime, pstEndDate);
        };
        void SendMsg_ZoneAliveCheck(unsigned int dwData)
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*, unsigned int);
            (org_ptr(0x1401c42c0L))(this, dwData);
        };
        void Start()
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*);
            (org_ptr(0x1401e1390L))(this);
        };
        ~CBillingManager()
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*);
            (org_ptr(0x14028df70L))(this);
        };
        void dtor_CBillingManager()
        {
            using org_ptr = void (WINAPIV*)(struct CBillingManager*);
            (org_ptr(0x14028df70L))(this);
        };
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct tagLOGFONTW
    {
        int lfHeight;
        int lfWidth;
        int lfEscapement;
        int lfOrientation;
        int lfWeight;
        char lfItalic;
        char lfUnderline;
        char lfStrikeOut;
        char lfCharSet;
        char lfOutPrecision;
        char lfClipPrecision;
        char lfQuality;
        char lfPitchAndFamily;
        wchar_t lfFaceName[32];
    };
END_ATF_NAMESPACE

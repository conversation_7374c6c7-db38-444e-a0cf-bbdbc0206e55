// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_guild_battle_suggest_matter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _guild_battle_suggest_matterCancelSuggestedMatter2_ptr = void (WINAPIV*)(struct _guild_battle_suggest_matter*);
        using _guild_battle_suggest_matterCancelSuggestedMatter2_clbk = void (WINAPIV*)(struct _guild_battle_suggest_matter*, _guild_battle_suggest_matterCancelSuggestedMatter2_ptr);
        using _guild_battle_suggest_matterClear4_ptr = void (WINAPIV*)(struct _guild_battle_suggest_matter*);
        using _guild_battle_suggest_matterClear4_clbk = void (WINAPIV*)(struct _guild_battle_suggest_matter*, _guild_battle_suggest_matterClear4_ptr);
        using _guild_battle_suggest_matterIsCompleteBattle6_ptr = bool (WINAPIV*)(struct _guild_battle_suggest_matter*);
        using _guild_battle_suggest_matterIsCompleteBattle6_clbk = bool (WINAPIV*)(struct _guild_battle_suggest_matter*, _guild_battle_suggest_matterIsCompleteBattle6_ptr);
        
        using _guild_battle_suggest_matterctor__guild_battle_suggest_matter8_ptr = void (WINAPIV*)(struct _guild_battle_suggest_matter*);
        using _guild_battle_suggest_matterctor__guild_battle_suggest_matter8_clbk = void (WINAPIV*)(struct _guild_battle_suggest_matter*, _guild_battle_suggest_matterctor__guild_battle_suggest_matter8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 8)
        template<>
        struct fpos<int>
        {
            __int64 _Myoff;
            __int64 _Fpos;
            int _Mystate;
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE

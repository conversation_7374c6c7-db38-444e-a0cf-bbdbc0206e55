// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagGUITHREADINFO
    {
        unsigned int cbSize;
        unsigned int flags;
        HWND__ *hwndActive;
        HWND__ *hwndFocus;
        HWND__ *hwndCapture;
        HWND__ *hwndMenuOwner;
        HWND__ *hwndMoveSize;
        HWND__ *hwndCaret;
        tagRECT rcCaret;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_attack_param.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _attack_paramctor__attack_param2_ptr = void (WINAPIV*)(struct _attack_param*);
        using _attack_paramctor__attack_param2_clbk = void (WINAPIV*)(struct _attack_param*, _attack_paramctor__attack_param2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

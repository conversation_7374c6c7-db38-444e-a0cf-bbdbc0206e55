// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMonsterSkill
    {
        bool m_bExit;
        int m_UseType;
        int m_nSFCode;
        unsigned __int16 m_wSFIndex;
        struct _base_fld *m_pSF_Fld;
        struct _monster_sp_fld *m_pSPConst;
        unsigned int m_BefTime;
        unsigned int m_dwDelayTime;
        unsigned int m_dwCastDelay;
        float m_fAttackDist;
        int m_nMotive;
        int m_nMotivevalue;
        int m_nCaseType;
        int m_nAccumulationCount;
        int m_nSFLv;
        int m_Element;
        int m_StdDmg;
        int m_MinDmg;
        int m_MaxDmg;
        int m_MinProb;
        int m_MaxProb;
    public:
        CMonsterSkill();
        void ctor_CMonsterSkill();
        void Copy(struct CMonsterSkill* Cls);
        int GetAccumulationCount();
        float GetAttackDist();
        unsigned int GetBeforeTime();
        int GetDstCaseType();
        int GetElement();
        int GetExceptMotive();
        int GetExceptMotiveValue();
        struct _base_fld* GetFld();
        int GetMaxDmg();
        int GetMaxProb();
        int GetMinDmg();
        int GetMinProb();
        int GetMotive();
        int GetMotiveValue();
        unsigned int GetNextActionDelayTime();
        int GetSFLv();
        int GetSPActionProbability();
        int GetSPLimitCount();
        int GetType();
        int GetUseType();
        void Init();
        bool IsAttackAble();
        bool IsExit();
        void NextPass();
        void SetAccumulationCountAdd(int nTempAccumulationCount);
        int SetForce(struct _monster_fld* pMonsterFld, struct _monster_sp_fld* pSPCont, int nSFLv, struct _force_fld* pForceFld, unsigned int dwDelayTime, float fAttackDist, unsigned int dwCastDelay, int nMotive, int nMotiveValue, int skillDestType);
        int SetGen(struct _monster_fld* pMonsterFld, int nSFLv, unsigned int dwDelayTime, float fAttackDist, unsigned int dwCastDelay);
        int SetSkill(struct _monster_fld* pMonsterFld, struct _monster_sp_fld* pSPCont, int nSFLv, int nEffectType, struct _skill_fld* pSkillFld, unsigned int dwDelayTime, float fAttackDist, unsigned int dwCastDelay, int nMotive, int nMotiveValue, int skillDestType);
        int Use(unsigned int dwUsedTime, bool bCount);
        ~CMonsterSkill();
        void dtor_CMonsterSkill();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CMonsterSkill, 96>(), "CMonsterSkill");
END_ATF_NAMESPACE

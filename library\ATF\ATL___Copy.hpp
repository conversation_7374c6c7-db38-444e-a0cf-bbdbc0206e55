// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _Copy<tagCONNECTDATA>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _Copy<tagVARIANT>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _Copy<tagOLEVERB>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _Copy<wchar_t *>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

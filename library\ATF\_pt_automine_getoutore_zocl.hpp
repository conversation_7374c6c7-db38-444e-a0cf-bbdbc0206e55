// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _pt_automine_getoutore_zocl
    {
        char byRetCode;
        unsigned __int16 wItemSerial;
    public:
        _pt_automine_getoutore_zocl();
        void ctor__pt_automine_getoutore_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_pt_automine_getoutore_zocl, 3>(), "_pt_automine_getoutore_zocl");
END_ATF_NAMESPACE

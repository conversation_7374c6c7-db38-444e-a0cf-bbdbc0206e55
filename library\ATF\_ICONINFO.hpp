// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HBITMAP__.hpp>


START_ATF_NAMESPACE
    struct _ICONINFO
    {
        int fIcon;
        unsigned int xHotspot;
        unsigned int yHotspot;
        HBITMAP__ *hbmMask;
        HBITMAP__ *hbmColor;
    };    
    static_assert(ATF::checkSize<_ICONINFO, 32>(), "_ICONINFO");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _darkhole_open_portal_by_react_inform_zocl
    {
        unsigned __int16 wPortalIndex;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_darkhole_open_portal_by_react_inform_zocl, 2>(), "_darkhole_open_portal_by_react_inform_zocl");
END_ATF_NAMESPACE

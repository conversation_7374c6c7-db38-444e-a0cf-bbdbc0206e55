// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_E_ENTRY.hpp>


START_ATF_NAMESPACE
    struct _ENTITY_FILE_HEADER
    {
        _E_ENTRY CompHeader;
        _E_ENTRY Vertex;
        _E_ENTRY VColor;
        _E_ENTRY UV;
        _E_ENTRY Face;
        _E_ENTRY FaceId;
        _E_ENTRY VertexId;
        _E_ENTRY MatGroup;
        _E_ENTRY Object;
        _E_ENTRY Track;
    };
END_ATF_NAMESPACE

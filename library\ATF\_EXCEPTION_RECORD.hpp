// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _EXCEPTION_RECORD
    {
        int ExceptionCode;
        unsigned int ExceptionFlags;
        _EXCEPTION_RECORD *ExceptionRecord;
        void *ExceptionAddress;
        unsigned int NumberParameters;
        unsigned __int64 ExceptionInformation[15];
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_EXCEPTION_RECORD, 152>(), "_EXCEPTION_RECORD");
END_ATF_NAMESPACE

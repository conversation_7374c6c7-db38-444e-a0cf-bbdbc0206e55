// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STAT_DB_BASE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _cum_download_result_zocl
    {
        char byRetCode;
        _STAT_DB_BASE Stat;
        char byLeftCuttingResNum;
        unsigned __int16 wleftResList[20];
    public:
        _cum_download_result_zocl();
        void ctor__cum_download_result_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_cum_download_result_zocl, 362>(), "_cum_download_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAggroNode.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CAggroNodector_CAggroNode2_ptr = void (WINAPIV*)(struct CAggroNode*);
        using CAggroNodector_CAggroNode2_clbk = void (WINAPIV*)(struct CAggroNode*, CAggroNodector_CAggroNode2_ptr);
        using CAggroNodeInit4_ptr = void (WINAPIV*)(struct CAggroNode*);
        using CAggroNodeInit4_clbk = void (WINAPIV*)(struct CAggroNode*, CAggroNodeInit4_ptr);
        using CAggroNodeIsLive6_ptr = int (WINAPIV*)(struct CAggroNode*);
        using CAggroNodeIsLive6_clbk = int (WINAPIV*)(struct CAggroNode*, CAggroNodeIsLive6_ptr);
        using CAggroNodeSet8_ptr = void (WINAPIV*)(struct CAggroNode*, struct CCharacter*);
        using CAggroNodeSet8_clbk = void (WINAPIV*)(struct CAggroNode*, struct CCharacter*, CAggroNodeSet8_ptr);
        using CAggroNodeSetAggro10_ptr = void (WINAPIV*)(struct CAggroNode*, int, float, int, unsigned int, int, int, int);
        using CAggroNodeSetAggro10_clbk = void (WINAPIV*)(struct CAggroNode*, int, float, int, unsigned int, int, int, int, CAggroNodeSetAggro10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $A3BAC9569453E46653D4C53B8339026E
    {
        unsigned __int32 BaseMid : 8;
        unsigned __int32 Type : 5;
        unsigned __int32 Dpl : 2;
        unsigned __int32 Pres : 1;
        unsigned __int32 LimitHi : 4;
        unsigned __int32 Sys : 1;
        unsigned __int32 Reserved_0 : 1;
        unsigned __int32 Default_Big : 1;
        unsigned __int32 Granularity : 1;
        unsigned __int32 BaseHi : 8;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_guild_member_download_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _guild_member_download_zoclClear2_ptr = void (WINAPIV*)(struct _guild_member_download_zocl*);
        using _guild_member_download_zoclClear2_clbk = void (WINAPIV*)(struct _guild_member_download_zocl*, _guild_member_download_zoclClear2_ptr);
        
        using _guild_member_download_zoclctor__guild_member_download_zocl4_ptr = void (WINAPIV*)(struct _guild_member_download_zocl*);
        using _guild_member_download_zoclctor__guild_member_download_zocl4_clbk = void (WINAPIV*)(struct _guild_member_download_zocl*, _guild_member_download_zoclctor__guild_member_download_zocl4_ptr);
        using _guild_member_download_zoclsize6_ptr = int (WINAPIV*)(struct _guild_member_download_zocl*);
        using _guild_member_download_zoclsize6_clbk = int (WINAPIV*)(struct _guild_member_download_zocl*, _guild_member_download_zoclsize6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _guild_join_other_inform_zocl
    {
        unsigned int dwAvatorSerial;
        unsigned int dwGuildSerial;
        unsigned __int16 wVisualVersion;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

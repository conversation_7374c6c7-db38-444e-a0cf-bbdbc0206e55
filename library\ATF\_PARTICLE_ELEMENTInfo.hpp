// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PARTICLE_ELEMENT.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _PARTICLE_ELEMENTUpDate1_ptr = void (WINAPIV*)(struct _PARTICLE_ELEMENT*, float);
        using _PARTICLE_ELEMENTUpDate1_clbk = void (WINAPIV*)(struct _PARTICLE_ELEMENT*, float, _PARTICLE_ELEMENTUpDate1_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

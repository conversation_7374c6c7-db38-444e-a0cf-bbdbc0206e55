// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _change_billing_type_inform_zocl
    {
        __int16 iCurrentType;
        __int16 iChangeType;
        int lRemainMin;
        _SYSTEMTIME stEndDate;
        char byReason;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

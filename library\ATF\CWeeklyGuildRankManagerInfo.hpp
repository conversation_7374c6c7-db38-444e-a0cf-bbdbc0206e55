// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CWeeklyGuildRankManager.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_ptr);
        using CWeeklyGuildRankManagerCompleteLoadeTodayRank4_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char, char*);
        using CWeeklyGuildRankManagerCompleteLoadeTodayRank4_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char, char*, CWeeklyGuildRankManagerCompleteLoadeTodayRank4_ptr);
        using CWeeklyGuildRankManagerCompleteUpdateClear6_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char);
        using CWeeklyGuildRankManagerCompleteUpdateClear6_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char, CWeeklyGuildRankManagerCompleteUpdateClear6_ptr);
        using CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char, char*);
        using CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char, char*, CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_ptr);
        using CWeeklyGuildRankManagerCreatePvpPointGuildRank10_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*);
        using CWeeklyGuildRankManagerCreatePvpPointGuildRank10_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, CWeeklyGuildRankManagerCreatePvpPointGuildRank10_ptr);
        using CWeeklyGuildRankManagerDestroy12_ptr = void (WINAPIV*)();
        using CWeeklyGuildRankManagerDestroy12_clbk = void (WINAPIV*)(CWeeklyGuildRankManagerDestroy12_ptr);
        using CWeeklyGuildRankManagerGetPrevOwnerGuild14_ptr = struct CGuild* (WINAPIV*)(struct CWeeklyGuildRankManager*, char, char);
        using CWeeklyGuildRankManagerGetPrevOwnerGuild14_clbk = struct CGuild* (WINAPIV*)(struct CWeeklyGuildRankManager*, char, char, CWeeklyGuildRankManagerGetPrevOwnerGuild14_ptr);
        using CWeeklyGuildRankManagerGetPrevRankDate16_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, int);
        using CWeeklyGuildRankManagerGetPrevRankDate16_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, int, CWeeklyGuildRankManagerGetPrevRankDate16_ptr);
        using CWeeklyGuildRankManagerGetTodayRankDate18_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, int);
        using CWeeklyGuildRankManagerGetTodayRankDate18_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, int, CWeeklyGuildRankManagerGetTodayRankDate18_ptr);
        using CWeeklyGuildRankManagerInit20_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerInit20_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerInit20_ptr);
        using CWeeklyGuildRankManagerInitNextSetOwnerDate22_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerInitNextSetOwnerDate22_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerInitNextSetOwnerDate22_ptr);
        using CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_ptr);
        using CWeeklyGuildRankManagerInsertSettlementOwner26_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, struct CRFWorldDatabase*, char*);
        using CWeeklyGuildRankManagerInsertSettlementOwner26_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, struct CRFWorldDatabase*, char*, CWeeklyGuildRankManagerInsertSettlementOwner26_ptr);
        using CWeeklyGuildRankManagerInstance28_ptr = struct CWeeklyGuildRankManager* (WINAPIV*)();
        using CWeeklyGuildRankManagerInstance28_clbk = struct CWeeklyGuildRankManager* (WINAPIV*)(CWeeklyGuildRankManagerInstance28_ptr);
        using CWeeklyGuildRankManagerIsEmptyRank30_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, struct _pvppoint_guild_rank_info*);
        using CWeeklyGuildRankManagerIsEmptyRank30_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, struct _pvppoint_guild_rank_info*, CWeeklyGuildRankManagerIsEmptyRank30_ptr);
        using CWeeklyGuildRankManagerLoad32_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerLoad32_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerLoad32_ptr);
        using CWeeklyGuildRankManagerLoadINILastRankTime34_ptr = int64_t (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerLoadINILastRankTime34_clbk = int64_t (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerLoadINILastRankTime34_ptr);
        using CWeeklyGuildRankManagerLoadPrevOwner36_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerLoadPrevOwner36_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerLoadPrevOwner36_ptr);
        using CWeeklyGuildRankManagerLoadPrevTable38_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, struct _pvppoint_guild_rank_info*);
        using CWeeklyGuildRankManagerLoadPrevTable38_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, struct _pvppoint_guild_rank_info*, CWeeklyGuildRankManagerLoadPrevTable38_ptr);
        using CWeeklyGuildRankManagerLoop40_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerLoop40_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerLoop40_ptr);
        using CWeeklyGuildRankManagerOrderRank42_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*, struct _pvppoint_guild_rank_info*);
        using CWeeklyGuildRankManagerOrderRank42_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, struct _pvppoint_guild_rank_info*, CWeeklyGuildRankManagerOrderRank42_ptr);
        using CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, unsigned int, long double);
        using CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, unsigned int, long double, CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_ptr);
        using CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char*);
        using CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_ptr);
        using CWeeklyGuildRankManagerSaveINI48_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerSaveINI48_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerSaveINI48_ptr);
        using CWeeklyGuildRankManagerSelectOwnerGuild50_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, struct _weeklyguildrank_owner_info*);
        using CWeeklyGuildRankManagerSelectOwnerGuild50_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, struct _weeklyguildrank_owner_info*, CWeeklyGuildRankManagerSelectOwnerGuild50_ptr);
        using CWeeklyGuildRankManagerSend52_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*, unsigned int, char, struct CPlayer*);
        using CWeeklyGuildRankManagerSend52_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, unsigned int, char, struct CPlayer*, CWeeklyGuildRankManagerSend52_ptr);
        using CWeeklyGuildRankManagerSetNextRankDate54_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerSetNextRankDate54_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerSetNextRankDate54_ptr);
        using CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_ptr);
        using CWeeklyGuildRankManagerUpdateOwnerGuild58_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*);
        using CWeeklyGuildRankManagerUpdateOwnerGuild58_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, CWeeklyGuildRankManagerUpdateOwnerGuild58_ptr);
        using CWeeklyGuildRankManagerUpdateRankDBRecord60_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, struct _pvppoint_guild_rank_info*);
        using CWeeklyGuildRankManagerUpdateRankDBRecord60_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, struct _pvppoint_guild_rank_info*, CWeeklyGuildRankManagerUpdateRankDBRecord60_ptr);
        using CWeeklyGuildRankManagerUpdateTodayRank62_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*);
        using CWeeklyGuildRankManagerUpdateTodayRank62_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, CWeeklyGuildRankManagerUpdateTodayRank62_ptr);
        using CWeeklyGuildRankManagerUpdateTodayTable64_ptr = int (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, struct _pvppoint_guild_rank_info*);
        using CWeeklyGuildRankManagerUpdateTodayTable64_clbk = int (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, struct _pvppoint_guild_rank_info*, CWeeklyGuildRankManagerUpdateTodayTable64_ptr);
        using CWeeklyGuildRankManagerUpdateWeeklyOwner66_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*);
        using CWeeklyGuildRankManagerUpdateWeeklyOwner66_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankManager*, char*, CWeeklyGuildRankManagerUpdateWeeklyOwner66_ptr);
        
        using CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_ptr = void (WINAPIV*)(struct CWeeklyGuildRankManager*);
        using CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_clbk = void (WINAPIV*)(struct CWeeklyGuildRankManager*, CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPathFinder.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPathFinderAddPath1_ptr = int64_t (WINAPIV*)(struct CPathFinder*, float*, int);
        using CPathFinderAddPath1_clbk = int64_t (WINAPIV*)(struct CPathFinder*, float*, int, CPathFinderAddPath1_ptr);
        using CPathFinderCompletePath2_ptr = void (WINAPIV*)(struct CPathFinder*, int);
        using CPathFinderCompletePath2_clbk = void (WINAPIV*)(struct CPathFinder*, int, CPathFinderCompletePath2_ptr);
        using CPathFinderGetBackLineId3_ptr = uint16_t (WINAPIV*)(struct CPathFinder*, int);
        using CPathFinderGetBackLineId3_clbk = uint16_t (WINAPIV*)(struct CPathFinder*, int, CPathFinderGetBackLineId3_ptr);
        using CPathFinderGetFrontLineId4_ptr = uint16_t (WINAPIV*)(struct CPathFinder*, int);
        using CPathFinderGetFrontLineId4_clbk = uint16_t (WINAPIV*)(struct CPathFinder*, int, CPathFinderGetFrontLineId4_ptr);
        using CPathFinderGetPathCnt5_ptr = int64_t (WINAPIV*)(struct CPathFinder*, int);
        using CPathFinderGetPathCnt5_clbk = int64_t (WINAPIV*)(struct CPathFinder*, int, CPathFinderGetPathCnt5_ptr);
        using CPathFinderGetPathDirection6_ptr = int64_t (WINAPIV*)(struct CPathFinder*, int);
        using CPathFinderGetPathDirection6_clbk = int64_t (WINAPIV*)(struct CPathFinder*, int, CPathFinderGetPathDirection6_ptr);
        using CPathFinderPopPathStack7_ptr = void (WINAPIV*)(struct CPathFinder*, int);
        using CPathFinderPopPathStack7_clbk = void (WINAPIV*)(struct CPathFinder*, int, CPathFinderPopPathStack7_ptr);
        using CPathFinderPushPathStack8_ptr = void (WINAPIV*)(struct CPathFinder*, int);
        using CPathFinderPushPathStack8_clbk = void (WINAPIV*)(struct CPathFinder*, int, CPathFinderPushPathStack8_ptr);
        using CPathFinderSetBackLineId9_ptr = void (WINAPIV*)(struct CPathFinder*, int, int);
        using CPathFinderSetBackLineId9_clbk = void (WINAPIV*)(struct CPathFinder*, int, int, CPathFinderSetBackLineId9_ptr);
        using CPathFinderSetFrontLineId10_ptr = void (WINAPIV*)(struct CPathFinder*, int, int);
        using CPathFinderSetFrontLineId10_clbk = void (WINAPIV*)(struct CPathFinder*, int, int, CPathFinderSetFrontLineId10_ptr);
        using CPathFinderSetPathDirection11_ptr = void (WINAPIV*)(struct CPathFinder*, int, int);
        using CPathFinderSetPathDirection11_clbk = void (WINAPIV*)(struct CPathFinder*, int, int, CPathFinderSetPathDirection11_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

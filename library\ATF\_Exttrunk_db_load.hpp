// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct  _Exttrunk_db_load : _STORAGE_LIST
    {
        char m_byItemSlotRace[40];
        _STORAGE_LIST::_db_con m_ExtList[40];
    public:
        _Exttrunk_db_load();
        void ctor__Exttrunk_db_load();
    };    
    static_assert(ATF::checkSize<_Exttrunk_db_load, 2060>(), "_Exttrunk_db_load");
END_ATF_NAMESPACE

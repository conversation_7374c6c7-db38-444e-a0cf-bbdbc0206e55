// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_post_content_get.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_post_content_getctor__qry_case_post_content_get2_ptr = void (WINAPIV*)(struct _qry_case_post_content_get*);
        using _qry_case_post_content_getctor__qry_case_post_content_get2_clbk = void (WINAPIV*)(struct _qry_case_post_content_get*, _qry_case_post_content_getctor__qry_case_post_content_get2_ptr);
        using _qry_case_post_content_getsize4_ptr = int (WINAPIV*)(struct _qry_case_post_content_get*);
        using _qry_case_post_content_getsize4_clbk = int (WINAPIV*)(struct _qry_case_post_content_get*, _qry_case_post_content_getsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagBITMAPCOREHEADER.hpp>
#include <tagRGBTRIPLE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct tagBITMAPCOREINFO
    {
        tagBITMAPCOREHEADER bmciHeader;
        tagRGBTRIPLE bmciColors[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

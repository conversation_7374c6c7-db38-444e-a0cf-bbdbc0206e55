// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct INTERNET_DIAGNOSTIC_SOCKET_INFO
    {
        unsigned __int64 Socket;
        unsigned int SourcePort;
        unsigned int DestPort;
        unsigned int Flags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

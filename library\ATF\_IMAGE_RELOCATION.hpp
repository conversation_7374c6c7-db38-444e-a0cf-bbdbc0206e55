// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$66A2FC5ED47CB5912809650DAB14E256.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _IMAGE_RELOCATION
    {
        $66A2FC5ED47CB5912809650DAB14E256 ___u0;
        unsigned int SymbolTableIndex;
        unsigned __int16 Type;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

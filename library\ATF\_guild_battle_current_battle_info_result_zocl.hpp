// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _guild_battle_current_battle_info_result_zocl
    {
        char wszLeftRedName[17];
        char wszRightBlueName[17];
        unsigned int dwLeftRedScore;
        unsigned int dwRighBluetScore;
        unsigned int dwLeftRedGoalCnt;
        unsigned int dwRighBluetGoalCnt;
        char byLeftHour;
        char byLeftMin;
        char byLeftSec;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <MonsterStateData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using MonsterStateDataGetStateChunk2_ptr = uint16_t (WINAPIV*)(struct MonsterStateData*);
        using MonsterStateDataGetStateChunk2_clbk = uint16_t (WINAPIV*)(struct MonsterStateData*, MonsterStateDataGetStateChunk2_ptr);
        
        using MonsterStateDatactor_MonsterStateData4_ptr = void (WINAPIV*)(struct MonsterStateData*);
        using MonsterStateDatactor_MonsterStateData4_clbk = void (WINAPIV*)(struct MonsterStateData*, MonsterStateDatactor_MonsterStateData4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

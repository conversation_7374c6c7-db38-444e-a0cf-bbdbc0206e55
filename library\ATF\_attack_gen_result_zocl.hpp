// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _attack_gen_result_zocl
    {
        struct  _dam_list
        {
            char byDstID;
            unsigned int dwDstSerial;
            unsigned __int16 wDamage;
            bool bActive;
            unsigned __int16 wActiveDamage;
        };
        char byAtterID;
        unsigned int dwAtterSerial;
        char byAttack<PERSON>art;
        unsigned __int16 wBulletIndex;
        bool bCritical;
        bool bWPActive;
        char byListNum;
        _dam_list DamList[32];
    public:
        _attack_gen_result_zocl();
        void ctor__attack_gen_result_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_attack_gen_result_zocl, 331>(), "_attack_gen_result_zocl");
END_ATF_NAMESPACE

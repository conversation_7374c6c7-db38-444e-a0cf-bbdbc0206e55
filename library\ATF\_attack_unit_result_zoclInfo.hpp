// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_attack_unit_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _attack_unit_result_zoclctor__attack_unit_result_zocl2_ptr = void (WINAPIV*)(struct _attack_unit_result_zocl*);
        using _attack_unit_result_zoclctor__attack_unit_result_zocl2_clbk = void (WINAPIV*)(struct _attack_unit_result_zocl*, _attack_unit_result_zoclctor__attack_unit_result_zocl2_ptr);
        using _attack_unit_result_zoclsize4_ptr = int (WINAPIV*)(struct _attack_unit_result_zocl*);
        using _attack_unit_result_zoclsize4_clbk = int (WINAPIV*)(struct _attack_unit_result_zocl*, _attack_unit_result_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

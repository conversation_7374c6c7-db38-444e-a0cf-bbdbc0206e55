// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__basic_string.hpp>


START_ATF_NAMESPACE
    struct AreaData
    {
        std::basic_string<char> strAreaName;
        unsigned int dwRGB;
        unsigned int dwX;
        unsigned int dwY;
        unsigned int dwSound;
    public:
        AreaData(struct AreaData* __that);
        void ctor_AreaData(struct AreaData* __that);
        AreaData();
        void ctor_AreaData();
        ~AreaData();
        void dtor_AreaData();
    };    
    static_assert(ATF::checkSize<AreaData, 64>(), "AreaData");
END_ATF_NAMESPACE

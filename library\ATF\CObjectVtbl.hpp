// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$102F5DD57E3755C8CCE8E6EF3935BFA3.hpp>


START_ATF_NAMESPACE
    struct CObjectVtbl
    {
        struct CRuntimeClass *(WINAPIV *GetRuntimeClass)(struct CObject *_this);
        $102F5DD57E3755C8CCE8E6EF3935BFA3 ___u1;
        void (WINAPIV *Serialize)(struct CObject *_this, struct CArchive *);
        void (WINAPIV *AssertValid)(struct CObject *_this);
        void (WINAPIV *Dump)(struct CObject *_this, struct CDumpContext *);
    };
END_ATF_NAMESPACE

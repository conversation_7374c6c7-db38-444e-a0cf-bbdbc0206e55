// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagWCRANGE.hpp>


START_ATF_NAMESPACE
    struct tagGLYPHSET
    {
        unsigned int cbThis;
        unsigned int flAccel;
        unsigned int cGlyphsSupported;
        unsigned int cRanges;
        tagWCRANGE ranges[1];
    };
END_ATF_NAMESPACE

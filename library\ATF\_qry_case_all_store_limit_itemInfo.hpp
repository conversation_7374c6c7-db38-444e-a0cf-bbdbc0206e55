// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_all_store_limit_item.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _qry_case_all_store_limit_itemDataInit2_ptr = void (WINAPIV*)(struct _qry_case_all_store_limit_item*);
        using _qry_case_all_store_limit_itemDataInit2_clbk = void (WINAPIV*)(struct _qry_case_all_store_limit_item*, _qry_case_all_store_limit_itemDataInit2_ptr);
        using _qry_case_all_store_limit_itemInit4_ptr = bool (WINAPIV*)(struct _qry_case_all_store_limit_item*, unsigned int);
        using _qry_case_all_store_limit_itemInit4_clbk = bool (WINAPIV*)(struct _qry_case_all_store_limit_item*, unsigned int, _qry_case_all_store_limit_itemInit4_ptr);
        
        using _qry_case_all_store_limit_itemctor__qry_case_all_store_limit_item6_ptr = void (WINAPIV*)(struct _qry_case_all_store_limit_item*);
        using _qry_case_all_store_limit_itemctor__qry_case_all_store_limit_item6_clbk = void (WINAPIV*)(struct _qry_case_all_store_limit_item*, _qry_case_all_store_limit_itemctor__qry_case_all_store_limit_item6_ptr);
        
        using _qry_case_all_store_limit_itemdtor__qry_case_all_store_limit_item8_ptr = void (WINAPIV*)(struct _qry_case_all_store_limit_item*);
        using _qry_case_all_store_limit_itemdtor__qry_case_all_store_limit_item8_clbk = void (WINAPIV*)(struct _qry_case_all_store_limit_item*, _qry_case_all_store_limit_itemdtor__qry_case_all_store_limit_item8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

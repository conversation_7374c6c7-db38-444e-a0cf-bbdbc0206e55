// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>
#include <CHolyScheduleData.hpp>
#include <CHolyStoneSaveData.hpp>
#include <CLogFile.hpp>
#include <CMapData.hpp>
#include <CMyTimer.hpp>
#include <CPlayer.hpp>
#include <CRecordData.hpp>
#include <_QUEST_CASH.hpp>
#include <_QUEST_CASH_OTHER.hpp>
#include <__holy_keeper_data.hpp>
#include <__holy_stone_data.hpp>
#include <_portal_dummy.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CHolyStoneSystem
    {
        enum _Change_Reason
        {
            eSchedule = 0x0,
            echeetkey = 0x1,
            eContinue = 0x2,
            eKeeper_Die = 0x3,
            eKeeper_Keep = 0x4,
            eTouchDown = 0x5,
        };
        CRecordData m_tblQuest;
        CLogFile m_logQuest;
        CLogFile m_logQuestDestroy;
        CLogFile m_logPer10Min;
        CMyTimer m_tmrHSKSystem;
        CPlayer *m_pkDestroyer;
        unsigned int m_dwNextStartTime;
        int m_nHolyStoneNum;
        __holy_keeper_data m_HolyKeeperData;
        __holy_stone_data m_HolyStoneData[3];
        unsigned int m_dwCheckTime[7];
        CHolyScheduleData m_ScheculeData;
        CHolyStoneSaveData m_SaveData;
        CMyTimer m_tmrCumPlayer;
        char m_strHolyMental[64];
        _QUEST_CASH m_cashQuest[5064];
        float m_fKeeperHPRate;
        float m_fFirstKeeperHPRate;
        int m_bScheduleCodePre;
        _QUEST_CASH_OTHER m_cashQuestOther[5064];
        _portal_dummy *m_pPortalDummy[3];
        int m_nRaceBattlePoint[3][2];
        char m_byKeeperDestroyRace;
        bool m_bConsumable;
        bool m_pMentalPass;
        bool bFreeMining;
    public:
        void AlterSchedule(char byScheduleCode, char byNumOfTime);
        bool AuthMiningTicket(unsigned int dwKey);
        CHolyStoneSystem();
        void ctor_CHolyStoneSystem();
        void CheckDestroyerIsArriveMine();
        bool CheckHolyMaster(struct CPlayer* pAtter, char byDestroyStoneRaceCode);
        void CheckKeeperPlusTime();
        bool ContinueStartSystem();
        void CreateHolyKeeper(int nCreateType);
        void CreateHolyStone();
        void DestroyHolyKeeper();
        void DestroyHolyStone();
        struct _QUEST_CASH* FindStoragedQuestCash(unsigned int dwAvatorSerial);
        int GetControlLeftTime();
        int GetDestroyStoneRace();
        unsigned int GetDestroyerGuildSerial();
        unsigned int GetDestroyerSerial();
        int GetDestroyerState();
        bool GetGoldBoxConsumable();
        int GetHolyMasterRace();
        char* GetHolyMentalString();
        char GetKeeperDestroyRace();
        struct CMapData* GetMapData();
        char GetNumOfTime();
        struct _portal_dummy* GetPortalDummy(char byRace);
        int GetSceneCode();
        unsigned int GetStartBattleTickTime();
        char GetStartDay();
        char GetStartHour();
        char GetStartMin();
        char GetStartMonth();
        uint16_t GetStartYear();
        void GiveHSKQuest();
        void HSKRespawnSystem();
        bool InitHolySystem();
        void InitQuestCash();
        void InitQuestCash_Other();
        bool IsControlScene();
        bool IsItemLootAuthority(struct CPlayer* pOne, char byCreateCode);
        bool IsMentalPass();
        bool IsMinigeTicketCheck();
        bool IsUseReturnItem(unsigned int dwObjSerial);
        void OnLoop();
        void On_HS_SCENE_BATTLE_END_WAIT_TIME();
        void On_HS_SCENE_BATTLE_TIME();
        void On_HS_SCENE_INIT();
        void On_HS_SCENE_KEEPER_ATTACKABLE_TIME();
        void On_HS_SCENE_KEEPER_CHAOS_TIME();
        void On_HS_SCENE_KEEPER_DEATTACKABLE_TIME();
        void On_HS_SCENE_KEEPER_DIE_TIME();
        void PeneltyFailRace(char byFailRace);
        void PeneltyLoseRace(char byDestroyedRace);
        struct _QUEST_CASH_OTHER* PopStoredQuestCash_Other(unsigned int dwAvatorSerial);
        void PushQuestCash_Other(unsigned int dwAvatorSerial, char byStoneMapMoveInfo);
        void PushStoreQuestCash(unsigned int dwAvatorSerial, char byQuestType, int nPvpPoint, uint16_t wKillPoint, uint16_t wDiePoint, char byCristalBattleDBInfo, char byHSKTime);
        void ReceiveDestroyKeeper(struct CCharacter* pCharacter);
        void RecoverPvpCash();
        void ReleaseLastAttBuff();
        void SendHolyStoneHP(struct CPlayer* pkPlayer);
        void SendHolyStoneHPToRaceBoss();
        void SendIsArriveDestroyer(char byArrive);
        void SendMsg_CreateHolyMaster(struct CPlayer* pkDestroyer, int nControlSec);
        void SendMsg_EndBattle(char byLoseRace);
        void SendMsg_EnterKeeper(int n);
        void SendMsg_EnterStone(int n);
        void SendMsg_ExitStone();
        void SendMsg_HolyKeeperAttackAbleState(bool bAttackAble);
        void SendMsg_HolyKeeperStateChaos();
        void SendMsg_HolyStoneSystemState(int nPlayerIndex);
        void SendMsg_NoticeNextQuest(int n, char byStoneMapMoveInfo);
        void SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeper(bool bKeepKeeper);
        void SendMsg_StartBattle();
        void SendMsg_WaitKeeper(int n, char byWaitType);
        void SendMsg_WaitStone(int n);
        void SendMsg_to_webagent_about_last_attacker_for_keeper(struct CPlayer* pPlayer, int bByAnimus);
        void SendNotifyHolyStoneDestroyedToRaceBoss();
        void SendSMS_CompleteQuest(char byDestroyedRace, char* pwszMasterName, int nControlSec, char* szMasterClass, char byMasterLv);
        void SendSMS_MineTimeExtend(int nControlSec);
        void SetDestroyStoneRace(int nRace);
        void SetEffectToDestroyerGuildMember();
        void SetGoldBoxConsumable(bool bFlag);
        void SetHolyMasterRace(int nMaster);
        void SetKeeperDestroyRace(char byRace);
        bool SetScene(char byNumOfTime, int nSceneCode, unsigned int nPassTime, int nChangeReason);
        void SetTermTimeDefault(char byNumOfTime);
        void UnAllRegisterPerAutoMine();
        void UpdateNotifyHolyStoneHPToRaceBoss();
        void WriteLogPer10Min_Combat();
        bool ct_KeeperStart(int nKeeperState, int nRace, int nPassTime);
        bool ct_State(struct CPlayer* pOne);
        bool ct_StopBattle();
        ~CHolyStoneSystem();
        void dtor_CHolyStoneSystem();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CHolyStoneSystem, 143904>(), "CHolyStoneSystem");
END_ATF_NAMESPACE

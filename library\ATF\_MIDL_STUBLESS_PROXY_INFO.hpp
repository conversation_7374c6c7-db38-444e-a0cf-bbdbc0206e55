// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_MIDL_STUB_DESC.hpp>
#include <_MIDL_SYNTAX_INFO.hpp>
#include <_RPC_SYNTAX_IDENTIFIER.hpp>


START_ATF_NAMESPACE
    struct _MIDL_STUBLESS_PROXY_INFO
    {
        _MIDL_STUB_DESC *pStubDesc;
        const char *ProcFormatString;
        const unsigned __int16 *FormatStringOffset;
        _RPC_SYNTAX_IDENTIFIER *pTransferSyntax;
        unsigned __int64 nCount;
        _MIDL_SYNTAX_INFO *pSyntaxInfo;
    };
END_ATF_NAMESPACE

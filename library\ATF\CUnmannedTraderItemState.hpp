// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$7C043B02100353E56B76EDF5C6AF069E.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderItemState
    {
        typedef $7C043B02100353E56B76EDF5C6AF069E STATE;
        STATE m_eState;
    public:
        CUnmannedTraderItemState();
        void ctor_CUnmannedTraderItemState();
        void Clear();
        static unsigned int GetMaxStateCnt();
        STATE GetState();
        static wchar_t** GetStateStrList();
        static wchar_t* GetStateStrW(unsigned int uiInx);
        static bool PushUpdateState(char byType, unsigned int dwRegistSerial, char byState, unsigned int dwOwnerSerial, uint16_t wItemSerial, char byItemTableCode, uint16_t wItemTableIndex);
        bool Set(char byState);
        ~CUnmannedTraderItemState();
        void dtor_CUnmannedTraderItemState();
    };    
    static_assert(ATF::checkSize<CUnmannedTraderItemState, 4>(), "CUnmannedTraderItemState");
END_ATF_NAMESPACE

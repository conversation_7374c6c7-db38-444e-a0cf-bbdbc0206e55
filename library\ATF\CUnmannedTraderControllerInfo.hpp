// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderController.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CUnmannedTraderControllerBuy2_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _unmannedtrader_buy_item_request_clzo*);
        using CUnmannedTraderControllerBuy2_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _unmannedtrader_buy_item_request_clzo*, CUnmannedTraderControllerBuy2_ptr);
        
        using CUnmannedTraderControllerctor_CUnmannedTraderController4_ptr = void (WINAPIV*)(struct CUnmannedTraderController*);
        using CUnmannedTraderControllerctor_CUnmannedTraderController4_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, CUnmannedTraderControllerctor_CUnmannedTraderController4_ptr);
        using CUnmannedTraderControllerCancelRegist6_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _a_trade_clear_item_request_clzo*);
        using CUnmannedTraderControllerCancelRegist6_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _a_trade_clear_item_request_clzo*, CUnmannedTraderControllerCancelRegist6_ptr);
        using CUnmannedTraderControllerCheatCancelRegist8_ptr = bool (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int, char);
        using CUnmannedTraderControllerCheatCancelRegist8_clbk = bool (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int, char, CUnmannedTraderControllerCheatCancelRegist8_ptr);
        using CUnmannedTraderControllerCheckDBItemState10_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char, unsigned int, char*, char*);
        using CUnmannedTraderControllerCheckDBItemState10_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char, unsigned int, char*, char*, CUnmannedTraderControllerCheckDBItemState10_ptr);
        using CUnmannedTraderControllerComleteLazyClean12_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerComleteLazyClean12_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerComleteLazyClean12_ptr);
        using CUnmannedTraderControllerCompleteBuy14_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*);
        using CUnmannedTraderControllerCompleteBuy14_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*, CUnmannedTraderControllerCompleteBuy14_ptr);
        using CUnmannedTraderControllerCompleteBuyComplete16_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerCompleteBuyComplete16_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerCompleteBuyComplete16_ptr);
        using CUnmannedTraderControllerCompleteBuyRollBack18_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*);
        using CUnmannedTraderControllerCompleteBuyRollBack18_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*, CUnmannedTraderControllerCompleteBuyRollBack18_ptr);
        using CUnmannedTraderControllerCompleteCancelRegist20_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*);
        using CUnmannedTraderControllerCompleteCancelRegist20_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*, CUnmannedTraderControllerCompleteCancelRegist20_ptr);
        using CUnmannedTraderControllerCompleteCreate22_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t);
        using CUnmannedTraderControllerCompleteCreate22_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, CUnmannedTraderControllerCompleteCreate22_ptr);
        using CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, uint16_t);
        using CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, uint16_t, CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_ptr);
        using CUnmannedTraderControllerCompleteLogInCompete26_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerCompleteLogInCompete26_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerCompleteLogInCompete26_ptr);
        using CUnmannedTraderControllerCompleteReRegist28_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerCompleteReRegist28_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerCompleteReRegist28_ptr);
        using CUnmannedTraderControllerCompleteReRegistRollBack30_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerCompleteReRegistRollBack30_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerCompleteReRegistRollBack30_ptr);
        using CUnmannedTraderControllerCompleteRegistItem32_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*);
        using CUnmannedTraderControllerCompleteRegistItem32_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*, CUnmannedTraderControllerCompleteRegistItem32_ptr);
        using CUnmannedTraderControllerCompleteReprice34_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*);
        using CUnmannedTraderControllerCompleteReprice34_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*, CUnmannedTraderControllerCompleteReprice34_ptr);
        using CUnmannedTraderControllerCompleteSelectBuyInfo36_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*);
        using CUnmannedTraderControllerCompleteSelectBuyInfo36_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*, CUnmannedTraderControllerCompleteSelectBuyInfo36_ptr);
        using CUnmannedTraderControllerCompleteSelectReservedSchedule38_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*);
        using CUnmannedTraderControllerCompleteSelectReservedSchedule38_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*, CUnmannedTraderControllerCompleteSelectReservedSchedule38_ptr);
        using CUnmannedTraderControllerCompleteSelectSearchList40_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char, char*);
        using CUnmannedTraderControllerCompleteSelectSearchList40_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char, char*, CUnmannedTraderControllerCompleteSelectSearchList40_ptr);
        using CUnmannedTraderControllerCompleteTimeOutCancelRegist42_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*);
        using CUnmannedTraderControllerCompleteTimeOutCancelRegist42_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*, CUnmannedTraderControllerCompleteTimeOutCancelRegist42_ptr);
        using CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_ptr);
        using CUnmannedTraderControllerCompleteUpdateState46_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*);
        using CUnmannedTraderControllerCompleteUpdateState46_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char, char*, CUnmannedTraderControllerCompleteUpdateState46_ptr);
        using CUnmannedTraderControllerDestroy48_ptr = void (WINAPIV*)();
        using CUnmannedTraderControllerDestroy48_clbk = void (WINAPIV*)(CUnmannedTraderControllerDestroy48_ptr);
        using CUnmannedTraderControllerGetEmptyRecordSerial50_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, unsigned int*, bool*);
        using CUnmannedTraderControllerGetEmptyRecordSerial50_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, unsigned int*, bool*, CUnmannedTraderControllerGetEmptyRecordSerial50_ptr);
        using CUnmannedTraderControllerGetMaxRegistCnt52_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int);
        using CUnmannedTraderControllerGetMaxRegistCnt52_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int, CUnmannedTraderControllerGetMaxRegistCnt52_ptr);
        using CUnmannedTraderControllerGetRegItemInfo54_ptr = struct CUnmannedTraderRegistItemInfo* (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int);
        using CUnmannedTraderControllerGetRegItemInfo54_clbk = struct CUnmannedTraderRegistItemInfo* (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int, CUnmannedTraderControllerGetRegItemInfo54_ptr);
        using CUnmannedTraderControllerInit56_ptr = bool (WINAPIV*)(struct CUnmannedTraderController*);
        using CUnmannedTraderControllerInit56_clbk = bool (WINAPIV*)(struct CUnmannedTraderController*, CUnmannedTraderControllerInit56_ptr);
        using CUnmannedTraderControllerInitLogger58_ptr = bool (WINAPIV*)(struct CUnmannedTraderController*);
        using CUnmannedTraderControllerInitLogger58_clbk = bool (WINAPIV*)(struct CUnmannedTraderController*, CUnmannedTraderControllerInitLogger58_ptr);
        using CUnmannedTraderControllerInsertDefalutRecord60_ptr = bool (WINAPIV*)(struct CUnmannedTraderController*);
        using CUnmannedTraderControllerInsertDefalutRecord60_clbk = bool (WINAPIV*)(struct CUnmannedTraderController*, CUnmannedTraderControllerInsertDefalutRecord60_ptr);
        using CUnmannedTraderControllerInsertStateRecord62_ptr = bool (WINAPIV*)(struct CUnmannedTraderController*);
        using CUnmannedTraderControllerInsertStateRecord62_clbk = bool (WINAPIV*)(struct CUnmannedTraderController*, CUnmannedTraderControllerInsertStateRecord62_ptr);
        using CUnmannedTraderControllerInstance64_ptr = struct CUnmannedTraderController* (WINAPIV*)();
        using CUnmannedTraderControllerInstance64_clbk = struct CUnmannedTraderController* (WINAPIV*)(CUnmannedTraderControllerInstance64_ptr);
        using CUnmannedTraderControllerLoad66_ptr = bool (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int, struct _TRADE_DB_BASE*);
        using CUnmannedTraderControllerLoad66_clbk = bool (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int, struct _TRADE_DB_BASE*, CUnmannedTraderControllerLoad66_ptr);
        using CUnmannedTraderControllerLoad68_ptr = bool (WINAPIV*)(struct CUnmannedTraderController*);
        using CUnmannedTraderControllerLoad68_clbk = bool (WINAPIV*)(struct CUnmannedTraderController*, CUnmannedTraderControllerLoad68_ptr);
        using CUnmannedTraderControllerLog70_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerLog70_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerLog70_ptr);
        using CUnmannedTraderControllerLogOut72_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int);
        using CUnmannedTraderControllerLogOut72_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, unsigned int, CUnmannedTraderControllerLogOut72_ptr);
        using CUnmannedTraderControllerLoop74_ptr = void (WINAPIV*)(struct CUnmannedTraderController*);
        using CUnmannedTraderControllerLoop74_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, CUnmannedTraderControllerLoop74_ptr);
        using CUnmannedTraderControllerModifyPrice76_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _a_trade_adjust_price_request_clzo*);
        using CUnmannedTraderControllerModifyPrice76_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _a_trade_adjust_price_request_clzo*, CUnmannedTraderControllerModifyPrice76_ptr);
        using CUnmannedTraderControllerReRegist78_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _unmannedtrader_re_regist_request_clzo*);
        using CUnmannedTraderControllerReRegist78_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _unmannedtrader_re_regist_request_clzo*, CUnmannedTraderControllerReRegist78_ptr);
        using CUnmannedTraderControllerRegist80_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _a_trade_reg_item_request_clzo*);
        using CUnmannedTraderControllerRegist80_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _a_trade_reg_item_request_clzo*, CUnmannedTraderControllerRegist80_ptr);
        using CUnmannedTraderControllerSearch82_ptr = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _unmannedtrader_search_list_request_clzo*);
        using CUnmannedTraderControllerSearch82_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, uint16_t, struct _unmannedtrader_search_list_request_clzo*, CUnmannedTraderControllerSearch82_ptr);
        using CUnmannedTraderControllerSelectBuy84_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerSelectBuy84_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerSelectBuy84_ptr);
        using CUnmannedTraderControllerSelectSearchList86_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*, struct CRFWorldDatabase*, char*);
        using CUnmannedTraderControllerSelectSearchList86_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, struct CRFWorldDatabase*, char*, CUnmannedTraderControllerSelectSearchList86_ptr);
        using CUnmannedTraderControllerUpdateBuy88_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateBuy88_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateBuy88_ptr);
        using CUnmannedTraderControllerUpdateBuyComplete90_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateBuyComplete90_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateBuyComplete90_ptr);
        using CUnmannedTraderControllerUpdateBuyRollBack92_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateBuyRollBack92_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateBuyRollBack92_ptr);
        using CUnmannedTraderControllerUpdateCancelRegist94_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateCancelRegist94_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateCancelRegist94_ptr);
        using CUnmannedTraderControllerUpdateCheatRegistTime96_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateCheatRegistTime96_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateCheatRegistTime96_ptr);
        using CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_ptr = bool (WINAPIV*)(struct CUnmannedTraderController*);
        using CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_clbk = bool (WINAPIV*)(struct CUnmannedTraderController*, CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_ptr);
        using CUnmannedTraderControllerUpdateItemState100_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateItemState100_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateItemState100_ptr);
        using CUnmannedTraderControllerUpdateLazyClean102_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateLazyClean102_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateLazyClean102_ptr);
        using CUnmannedTraderControllerUpdateLogInComplete104_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateLogInComplete104_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateLogInComplete104_ptr);
        using CUnmannedTraderControllerUpdateRePrice106_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateRePrice106_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateRePrice106_ptr);
        using CUnmannedTraderControllerUpdateReRegist108_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateReRegist108_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateReRegist108_ptr);
        using CUnmannedTraderControllerUpdateReRegistRollBack110_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateReRegistRollBack110_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateReRegistRollBack110_ptr);
        using CUnmannedTraderControllerUpdateRegistItem112_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateRegistItem112_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateRegistItem112_ptr);
        using CUnmannedTraderControllerUpdateTimeOutCancelRegist114_ptr = char (WINAPIV*)(struct CUnmannedTraderController*, char*);
        using CUnmannedTraderControllerUpdateTimeOutCancelRegist114_clbk = char (WINAPIV*)(struct CUnmannedTraderController*, char*, CUnmannedTraderControllerUpdateTimeOutCancelRegist114_ptr);
        
        using CUnmannedTraderControllerdtor_CUnmannedTraderController118_ptr = void (WINAPIV*)(struct CUnmannedTraderController*);
        using CUnmannedTraderControllerdtor_CUnmannedTraderController118_clbk = void (WINAPIV*)(struct CUnmannedTraderController*, CUnmannedTraderControllerdtor_CUnmannedTraderController118_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

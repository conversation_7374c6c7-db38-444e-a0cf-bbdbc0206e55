// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _message
    {
        unsigned int dwMessage;
        unsigned int dwKey1;
        unsigned int dwKey2;
        unsigned int dwKey3;
        _message *pNext;
        _message *pPrev;
    public:
        unsigned int GetKey1();
        unsigned int GetMessageA();
        void SetMsg(unsigned int message, unsigned int key1, unsigned int key2, unsigned int key3);
        _message();
        void ctor__message();
        ~_message();
        void dtor__message();
    };    
    static_assert(ATF::checkSize<_message, 32>(), "_message");
END_ATF_NAMESPACE

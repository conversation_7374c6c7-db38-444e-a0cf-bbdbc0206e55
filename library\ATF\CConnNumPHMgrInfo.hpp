// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CConnNumPHMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CConnNumPHMgrctor_CConnNumPHMgr2_ptr = void (WINAPIV*)(struct CConnNumPHMgr*);
        using CConnNumPHMgrctor_CConnNumPHMgr2_clbk = void (WINAPIV*)(struct CConnNumPHMgr*, CConnNumPHMgrctor_CConnNumPHMgr2_ptr);
        using CConnNumPHMgrCheck4_ptr = struct _USER_NUM_SHEET* (WINAPIV*)(struct CConnNumPHMgr*, int);
        using CConnNumPHMgrCheck4_clbk = struct _USER_NUM_SHEET* (WINAPIV*)(struct CConnNumPHMgr*, int, CConnNumPHMgrCheck4_ptr);
        using CConnNumPHMgrGetCurHour6_ptr = int (WINAPIV*)(struct CConnNumPHMgr*);
        using CConnNumPHMgrGetCurHour6_clbk = int (WINAPIV*)(struct CConnNumPHMgr*, CConnNumPHMgrGetCurHour6_ptr);
        using CConnNumPHMgrInit8_ptr = void (WINAPIV*)(struct CConnNumPHMgr*);
        using CConnNumPHMgrInit8_clbk = void (WINAPIV*)(struct CConnNumPHMgr*, CConnNumPHMgrInit8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

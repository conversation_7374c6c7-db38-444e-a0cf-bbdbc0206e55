// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTimeSpan.hpp>
#include <GUILD_BATTLE__CGuildBattleStateList.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleState.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateCountDown.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateDivide.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateFin.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateInBattle.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateNotify.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateReady.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateReturn.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CNormalGuildBattleStateList : CGuildBattleStateList
        {
            enum NGB_STATE
            {
                NGB_NOTIFY = 0x0,
                NGB_READY = 0x1,
                NGB_COUNT = 0x2,
                NGB_INBATTLE = 0x3,
                NGB_DIVIDE = 0x4,
                NGB_RETURN = 0x5,
                NGB_FIN = 0x6,
                NGB_MAX = 0x7,
            };
            CNormalGuildBattleStateNotify NOTIFY;
            CNormalGuildBattleStateReady READY;
            CNormalGuildBattleStateCountDown COUNT;
            CNormalGuildBattleStateInBattle INBATTLE;
            CNormalGuildBattleStateDivide DIVIDE;
            CNormalGuildBattleStateReturn RETURN;
            CNormalGuildBattleStateFin FIN;
            struct CNormalGuildBattleState *m_pStateList[7];
        public:
            bool AdvanceRegenState();
            CNormalGuildBattleStateList();
            void ctor_CNormalGuildBattleStateList();
            bool IsInBattle();
            bool IsInBattleRegenState();
            bool IsReadyOrCountState();
            void SetBattleTime(struct ATL::CTimeSpan kTime);
            bool SetGotoRegenState();
            void SetNextState();
            ~CNormalGuildBattleStateList();
            void dtor_CNormalGuildBattleStateList();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CNormalGuildBattleStateList, 304>(), "GUILD_BATTLE::CNormalGuildBattleStateList");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

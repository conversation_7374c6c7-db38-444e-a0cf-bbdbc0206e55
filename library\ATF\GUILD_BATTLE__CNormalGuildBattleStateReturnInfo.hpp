// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateReturn.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateReturnctor_CNormalGuildBattleStateReturn2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReturn*);
            using GUILD_BATTLE__CNormalGuildBattleStateReturnctor_CNormalGuildBattleStateReturn2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReturn*, GUILD_BATTLE__CNormalGuildBattleStateReturnctor_CNormalGuildBattleStateReturn2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateReturnFin4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReturn*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateReturnFin4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReturn*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateReturnFin4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateReturnGetTerm6_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReturn*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CNormalGuildBattleStateReturnGetTerm6_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReturn*, struct ATL::CTimeSpan*, GUILD_BATTLE__CNormalGuildBattleStateReturnGetTerm6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateReturndtor_CNormalGuildBattleStateReturn8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReturn*);
            using GUILD_BATTLE__CNormalGuildBattleStateReturndtor_CNormalGuildBattleStateReturn8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateReturn*, GUILD_BATTLE__CNormalGuildBattleStateReturndtor_CNormalGuildBattleStateReturn8_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

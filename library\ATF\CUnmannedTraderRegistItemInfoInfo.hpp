// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderRegistItemInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_ptr);
        using CUnmannedTraderRegistItemInfoClear4_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoClear4_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoClear4_ptr);
        using CUnmannedTraderRegistItemInfoClearBuyerInfo6_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoClearBuyerInfo6_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoClearBuyerInfo6_ptr);
        using CUnmannedTraderRegistItemInfoClearRegist8_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoClearRegist8_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoClearRegist8_ptr);
        using CUnmannedTraderRegistItemInfoClearToWaitState10_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoClearToWaitState10_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoClearToWaitState10_ptr);
        using CUnmannedTraderRegistItemInfoGetBuyerSerial12_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetBuyerSerial12_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetBuyerSerial12_ptr);
        using CUnmannedTraderRegistItemInfoGetD14_ptr = uint64_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetD14_clbk = uint64_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetD14_ptr);
        using CUnmannedTraderRegistItemInfoGetETSerial16_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetETSerial16_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetETSerial16_ptr);
        using CUnmannedTraderRegistItemInfoGetItemIndex18_ptr = uint16_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetItemIndex18_clbk = uint16_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetItemIndex18_ptr);
        using CUnmannedTraderRegistItemInfoGetItemSerial20_ptr = uint16_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetItemSerial20_clbk = uint16_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetItemSerial20_ptr);
        using CUnmannedTraderRegistItemInfoGetLeftSec22_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetLeftSec22_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetLeftSec22_ptr);
        using CUnmannedTraderRegistItemInfoGetPrice24_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetPrice24_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetPrice24_ptr);
        using CUnmannedTraderRegistItemInfoGetRegistSerial26_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetRegistSerial26_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetRegistSerial26_ptr);
        using CUnmannedTraderRegistItemInfoGetResultTime28_ptr = int64_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetResultTime28_clbk = int64_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetResultTime28_ptr);
        using CUnmannedTraderRegistItemInfoGetSellTurm30_ptr = char (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetSellTurm30_clbk = char (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetSellTurm30_ptr);
        using CUnmannedTraderRegistItemInfoGetStartTime32_ptr = int64_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetStartTime32_clbk = int64_t (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetStartTime32_ptr);
        using CUnmannedTraderRegistItemInfoGetStartTimePtr34_ptr = int64_t* (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetStartTimePtr34_clbk = int64_t* (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetStartTimePtr34_ptr);
        using CUnmannedTraderRegistItemInfoGetState36_ptr = CUnmannedTraderItemState::STATE (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetState36_clbk = CUnmannedTraderItemState::STATE (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetState36_ptr);
        using CUnmannedTraderRegistItemInfoGetStorageIndex38_ptr = char (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetStorageIndex38_clbk = char (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetStorageIndex38_ptr);
        using CUnmannedTraderRegistItemInfoGetTableCode40_ptr = char (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetTableCode40_clbk = char (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetTableCode40_ptr);
        using CUnmannedTraderRegistItemInfoGetTax42_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetTax42_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetTax42_ptr);
        using CUnmannedTraderRegistItemInfoGetU44_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoGetU44_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoGetU44_ptr);
        using CUnmannedTraderRegistItemInfoIsEmpty46_ptr = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoIsEmpty46_clbk = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoIsEmpty46_ptr);
        using CUnmannedTraderRegistItemInfoIsOverRegistTime48_ptr = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoIsOverRegistTime48_clbk = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoIsOverRegistTime48_ptr);
        using CUnmannedTraderRegistItemInfoIsRegist50_ptr = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoIsRegist50_clbk = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoIsRegist50_ptr);
        using CUnmannedTraderRegistItemInfoIsSellUpdateWait52_ptr = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoIsSellUpdateWait52_clbk = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoIsSellUpdateWait52_ptr);
        using CUnmannedTraderRegistItemInfoIsSellWait54_ptr = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoIsSellWait54_clbk = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoIsSellWait54_ptr);
        using CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_ptr = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_clbk = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_ptr);
        using CUnmannedTraderRegistItemInfoReRegistItem58_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, unsigned int);
        using CUnmannedTraderRegistItemInfoReRegistItem58_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, unsigned int, CUnmannedTraderRegistItemInfoReRegistItem58_ptr);
        using CUnmannedTraderRegistItemInfoRegistItem60_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, unsigned int, uint16_t, unsigned int, unsigned int, char, char, uint16_t, char, uint64_t, unsigned int, bool);
        using CUnmannedTraderRegistItemInfoRegistItem60_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, unsigned int, uint16_t, unsigned int, unsigned int, char, char, uint16_t, char, uint64_t, unsigned int, bool, CUnmannedTraderRegistItemInfoRegistItem60_ptr);
        using CUnmannedTraderRegistItemInfoRepriceItem62_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, unsigned int);
        using CUnmannedTraderRegistItemInfoRepriceItem62_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, unsigned int, CUnmannedTraderRegistItemInfoRepriceItem62_ptr);
        using CUnmannedTraderRegistItemInfoSellComplete64_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, unsigned int, unsigned int, unsigned int, int64_t, char*, char*);
        using CUnmannedTraderRegistItemInfoSellComplete64_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, unsigned int, unsigned int, unsigned int, int64_t, char*, char*, CUnmannedTraderRegistItemInfoSellComplete64_ptr);
        using CUnmannedTraderRegistItemInfoSellWaitItem66_ptr = char (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, uint16_t, struct CLogFile*, int64_t, char*);
        using CUnmannedTraderRegistItemInfoSellWaitItem66_clbk = char (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, uint16_t, struct CLogFile*, int64_t, char*, CUnmannedTraderRegistItemInfoSellWaitItem66_ptr);
        using CUnmannedTraderRegistItemInfoSet68_ptr = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, uint16_t, char, unsigned int, struct _TRADE_DB_BASE*, struct CLogFile*);
        using CUnmannedTraderRegistItemInfoSet68_clbk = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, uint16_t, char, unsigned int, struct _TRADE_DB_BASE*, struct CLogFile*, CUnmannedTraderRegistItemInfoSet68_ptr);
        using CUnmannedTraderRegistItemInfoSetOverRegistTime70_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfoSetOverRegistTime70_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfoSetOverRegistTime70_ptr);
        using CUnmannedTraderRegistItemInfoSetState72_ptr = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, char);
        using CUnmannedTraderRegistItemInfoSetState72_clbk = bool (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, char, CUnmannedTraderRegistItemInfoSetState72_ptr);
        
        using CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_ptr = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*);
        using CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_clbk = void (WINAPIV*)(struct CUnmannedTraderRegistItemInfo*, CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

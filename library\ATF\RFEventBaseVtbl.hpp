// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct RFEventBaseVtbl
    {
        void *(WINAPIV *__vecDelDtor)(struct RFEventBase *_this, unsigned int);
        BYTE gap8[8];
        bool (WINAPIV *SetEvent)(struct RFEventBase *_this, const char *, int, bool);
        int (WINAPIV *DoEvent)(struct RFEventBase *_this, struct CPlayer *);
        bool (WINAPIV *IsEnable)(struct RFEventBase *_this);
        bool (WINAPIV *IsDbUpdate)(struct RFEventBase *_this, unsigned int);
        void (WINAPIV *Loop)(struct RFEventBase *_this);
        bool (WINAPIV *SetPlayerState)(struct RFEventBase *_this, void *const , int);
        char *(WINAPIV *GetPlayerState)(struct RFEventBase *_this, unsigned int, unsigned int);
    };
END_ATF_NAMESPACE

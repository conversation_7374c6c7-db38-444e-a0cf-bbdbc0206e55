// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleLogger.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattleLoggerctor_CGuildBattleLogger2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*);
            using GUILD_BATTLE__CGuildBattleLoggerctor_CGuildBattleLogger2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*, GUILD_BATTLE__CGuildBattleLoggerctor_CGuildBattleLogger2_ptr);
            using GUILD_BATTLE__CGuildBattleLoggerCreateLogFile4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*, char*);
            using GUILD_BATTLE__CGuildBattleLoggerCreateLogFile4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*, char*, GUILD_BATTLE__CGuildBattleLoggerCreateLogFile4_ptr);
            using GUILD_BATTLE__CGuildBattleLoggerDestroy6_ptr = void (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleLoggerDestroy6_clbk = void (WINAPIV*)(GUILD_BATTLE__CGuildBattleLoggerDestroy6_ptr);
            using GUILD_BATTLE__CGuildBattleLoggerInit8_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*);
            using GUILD_BATTLE__CGuildBattleLoggerInit8_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*, GUILD_BATTLE__CGuildBattleLoggerInit8_ptr);
            using GUILD_BATTLE__CGuildBattleLoggerInstance10_ptr = struct GUILD_BATTLE::CGuildBattleLogger* (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleLoggerInstance10_clbk = struct GUILD_BATTLE::CGuildBattleLogger* (WINAPIV*)(GUILD_BATTLE__CGuildBattleLoggerInstance10_ptr);
            using GUILD_BATTLE__CGuildBattleLoggerLog12_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*, char*);
            using GUILD_BATTLE__CGuildBattleLoggerLog12_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*, char*, GUILD_BATTLE__CGuildBattleLoggerLog12_ptr);
            using GUILD_BATTLE__CGuildBattleLoggerLog14_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*, wchar_t*);
            using GUILD_BATTLE__CGuildBattleLoggerLog14_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*, wchar_t*, GUILD_BATTLE__CGuildBattleLoggerLog14_ptr);
            
            using GUILD_BATTLE__CGuildBattleLoggerdtor_CGuildBattleLogger18_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*);
            using GUILD_BATTLE__CGuildBattleLoggerdtor_CGuildBattleLogger18_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleLogger*, GUILD_BATTLE__CGuildBattleLoggerdtor_CGuildBattleLogger18_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

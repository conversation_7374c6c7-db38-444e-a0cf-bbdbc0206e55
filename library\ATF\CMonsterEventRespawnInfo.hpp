// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonsterEventRespawn.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMonsterEventRespawnctor_CMonsterEventRespawn2_ptr = void (WINAPIV*)(struct CMonsterEventRespawn*);
        using CMonsterEventRespawnctor_CMonsterEventRespawn2_clbk = void (WINAPIV*)(struct CMonsterEventRespawn*, CMonsterEventRespawnctor_CMonsterEventRespawn2_ptr);
        using CMonsterEventRespawnCheckRespawnEvent4_ptr = void (WINAPIV*)(struct CMonsterEventRespawn*);
        using CMonsterEventRespawnCheckRespawnEvent4_clbk = void (WINAPIV*)(struct CMonsterEventRespawn*, CMonsterEventRespawnCheckRespawnEvent4_ptr);
        using CMonsterEventRespawnSetEventRespawn6_ptr = bool (WINAPIV*)(struct CMonsterEventRespawn*);
        using CMonsterEventRespawnSetEventRespawn6_clbk = bool (WINAPIV*)(struct CMonsterEventRespawn*, CMonsterEventRespawnSetEventRespawn6_ptr);
        using CMonsterEventRespawnStartRespawnEvent8_ptr = bool (WINAPIV*)(struct CMonsterEventRespawn*, char*, char*);
        using CMonsterEventRespawnStartRespawnEvent8_clbk = bool (WINAPIV*)(struct CMonsterEventRespawn*, char*, char*, CMonsterEventRespawnStartRespawnEvent8_ptr);
        using CMonsterEventRespawnStopRespawnEvent10_ptr = bool (WINAPIV*)(struct CMonsterEventRespawn*, char*, char*);
        using CMonsterEventRespawnStopRespawnEvent10_clbk = bool (WINAPIV*)(struct CMonsterEventRespawn*, char*, char*, CMonsterEventRespawnStopRespawnEvent10_ptr);
        
        using CMonsterEventRespawndtor_CMonsterEventRespawn15_ptr = void (WINAPIV*)(struct CMonsterEventRespawn*);
        using CMonsterEventRespawndtor_CMonsterEventRespawn15_clbk = void (WINAPIV*)(struct CMonsterEventRespawn*, CMonsterEventRespawndtor_CMonsterEventRespawn15_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

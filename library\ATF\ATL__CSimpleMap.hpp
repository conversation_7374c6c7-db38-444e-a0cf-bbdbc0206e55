// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        #pragma pack(push, 8)
        template<typename _Key, typename _Value, typename _Helper>
        struct CSimpleMap
        {
            _Key* m_aKey;
            _Value* m_aVal;
            int m_nSize;
        };
        #pragma pack(pop)
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RPC_DISPATCH_TABLE.hpp>
#include <_RPC_PROTSEQ_ENDPOINT.hpp>
#include <_RPC_SYNTAX_IDENTIFIER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _RPC_SERVER_INTERFACE
    {
        unsigned int Length;
        _RPC_SYNTAX_IDENTIFIER InterfaceId;
        _RPC_SYNTAX_IDENTIFIER TransferSyntax;
        RPC_DISPATCH_TABLE *DispatchTable;
        unsigned int RpcProtseqEndpointCount;
        _RPC_PROTSEQ_ENDPOINT *RpcProtseqEndpoint;
        void *DefaultManagerEpv;
        const void *InterpreterInfo;
        unsigned int Flags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

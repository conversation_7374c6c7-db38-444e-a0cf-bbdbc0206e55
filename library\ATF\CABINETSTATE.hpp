// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CABINETSTATE
    {
        unsigned __int16 cLength;
        unsigned __int16 nVersion;
        __int32 fFullPathTitle : 1;
        __int32 fSaveLocalView : 1;
        __int32 fNotShell : 1;
        __int32 fSimpleDefault : 1;
        __int32 fDontShowDescBar : 1;
        __int32 fNewWindowMode : 1;
        __int32 fShowCompColor : 1;
        __int32 fDontPrettyNames : 1;
        __int32 fAdminsCreateCommonGroups : 1;
        unsigned __int32 fUnusedFlags : 7;
        unsigned int fMenuEnumFilter;
    };
END_ATF_NAMESPACE

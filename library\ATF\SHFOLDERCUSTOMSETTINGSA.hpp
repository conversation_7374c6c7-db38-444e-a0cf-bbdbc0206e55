// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct SHFOLDERCUSTOMSETTINGSA
    {
        unsigned int dwSize;
        unsigned int dwMask;
        _GUID *pvid;
        char *pszWebViewTemplate;
        unsigned int cchWebViewTemplate;
        char *pszWebViewTemplateVersion;
        char *pszInfoTip;
        unsigned int cchInfoTip;
        _GUID *pclsid;
        unsigned int dwFlags;
        char *pszIconFile;
        unsigned int cchIconFile;
        int iIconIndex;
        char *pszLogo;
        unsigned int cchLogo;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _STORAGE_LIST
    {
        struct  _storage_con
        {
            char m_bLoad;
            char m_byTableCode;
            char m_byClientIndex;
            unsigned __int16 m_wItemIndex;
            unsigned __int64 m_dwDur;
            unsigned int m_dwLv;
            unsigned __int16 m_wSerial;
            bool m_bLock;
            unsigned int m_dwETSerialNumber;
            unsigned __int64 m_lnUID;
            char m_byCsMethod;
            unsigned int m_dwT;
            unsigned int m_dwLendRegdTime;
        public:
            void Init();
            _storage_con(char byTableCode, uint16_t wItemIndex, unsigned int dwDur, unsigned int dwLv, uint16_t wSerial);
            void ctor__storage_con(char byTableCode, uint16_t wItemIndex, unsigned int dwDur, unsigned int dwLv, uint16_t wSerial);
            _storage_con();
            void ctor__storage_con();
            void empty();
            void lock(bool bLock);
        };
        struct   _db_con : _storage_con
        {
            _STORAGE_LIST *m_pInList;
            char m_byStorageIndex;
        public:
            static unsigned int CalcNewSerialNumber();
            unsigned int GetSerialNumber();
            void Init();
            void SetSerialNumber(unsigned int dwSN);
            _db_con();
            void ctor__db_con();
        };
        int m_nListNum;
        int m_nUsedNum;
        int m_nListCode;
        _db_con *m_pStorageList;
    public:
        bool AlterCurDur(int n, int nAlter, uint64_t* pdwLeftDur);
        bool EmptyCon(int n);
        int GetIndexEmptyCon();
        int GetIndexFromSerial(uint16_t wSerial);
        int GetNumEmptyCon();
        int GetNumUseCon();
        struct _db_con* GetPtrFromItemCode(char* pwszItemCode);
        struct _db_con* GetPtrFromItemInfo(char byTableCode, uint16_t ItemIndex);
        struct _db_con* GetPtrFromSerial(uint16_t wSerial);
        unsigned int GetRemainLendTime(int n, int tmCur);
        bool GradeDown(int n, unsigned int dwUptInfo);
        bool GradeUp(int n, unsigned int dwUptInfo);
        void SetAllEmpty();
        char SetClientIndexFromSerial(uint16_t wSerial, char byClientIndex, char* pbyoutOldClientIndex);
        bool SetGrade(int n, char byLv, unsigned int dwUptInfo);
        void SetLimCurDur(int n, unsigned int dwSetDur);
        void SetLock(int n, bool bLock);
        void SetMemory(struct _db_con* pList, int nListName, int nListNum, int nUsedNum);
        bool SetUseListNum(int nUsedNum);
        unsigned int TransInCon(struct _storage_con* pCon);
        bool UpdateCurDur(int n, int nUpdate);
        _STORAGE_LIST();
        void ctor__STORAGE_LIST();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_STORAGE_LIST, 20>(), "_STORAGE_LIST");
END_ATF_NAMESPACE

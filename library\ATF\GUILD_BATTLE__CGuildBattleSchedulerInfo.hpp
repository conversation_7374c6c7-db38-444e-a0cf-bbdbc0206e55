// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleScheduler.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattleSchedulerctor_CGuildBattleScheduler2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduler*);
            using GUILD_BATTLE__CGuildBattleSchedulerctor_CGuildBattleScheduler2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduler*, GUILD_BATTLE__CGuildBattleSchedulerctor_CGuildBattleScheduler2_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulerDestroy4_ptr = void (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleSchedulerDestroy4_clbk = void (WINAPIV*)(GUILD_BATTLE__CGuildBattleSchedulerDestroy4_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulerInit6_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduler*);
            using GUILD_BATTLE__CGuildBattleSchedulerInit6_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduler*, GUILD_BATTLE__CGuildBattleSchedulerInit6_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulerInstance8_ptr = struct GUILD_BATTLE::CGuildBattleScheduler* (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleSchedulerInstance8_clbk = struct GUILD_BATTLE::CGuildBattleScheduler* (WINAPIV*)(GUILD_BATTLE__CGuildBattleSchedulerInstance8_ptr);
            using GUILD_BATTLE__CGuildBattleSchedulerUpdateClearGuildBattleScheduleDayInfo10_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduler*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleSchedulerUpdateClearGuildBattleScheduleDayInfo10_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduler*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleSchedulerUpdateClearGuildBattleScheduleDayInfo10_ptr);
            
            using GUILD_BATTLE__CGuildBattleSchedulerdtor_CGuildBattleScheduler14_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduler*);
            using GUILD_BATTLE__CGuildBattleSchedulerdtor_CGuildBattleScheduler14_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleScheduler*, GUILD_BATTLE__CGuildBattleSchedulerdtor_CGuildBattleScheduler14_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

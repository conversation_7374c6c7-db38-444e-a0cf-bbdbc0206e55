#include <_not_arranged_char_inform_zocl.hpp>


START_ATF_NAMESPACE
    _not_arranged_char_inform_zocl::_not_arranged_char_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _not_arranged_char_inform_zocl*);
        (org_ptr(0x14011f750L))(this);
    };
    void _not_arranged_char_inform_zocl::ctor__not_arranged_char_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _not_arranged_char_inform_zocl*);
        (org_ptr(0x14011f750L))(this);
    };
    int _not_arranged_char_inform_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _not_arranged_char_inform_zocl*);
        return (org_ptr(0x14011f7c0L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IUnknown.hpp>


START_ATF_NAMESPACE
    struct $60E4D37D1F84587CCAA4F5EA0F9453FD
    {
        BYTE gap0[8];
        IUnknown *punkVal;
    };    
    static_assert(ATF::checkSize<$60E4D37D1F84587CCAA4F5EA0F9453FD, 16>(), "$60E4D37D1F84587CCAA4F5EA0F9453FD");
END_ATF_NAMESPACE

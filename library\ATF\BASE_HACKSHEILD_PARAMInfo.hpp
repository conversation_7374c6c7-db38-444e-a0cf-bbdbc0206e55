// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <BASE_HACKSHEILD_PARAM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using BASE_HACKSHEILD_PARAMctor_BASE_HACKSHEILD_PARAM2_ptr = void (WINAPIV*)(struct BASE_HACKSHEILD_PARAM*);
        using BASE_HACKSHEILD_PARAMctor_BASE_HACKSHEILD_PARAM2_clbk = void (WINAPIV*)(struct BASE_HACKSHEILD_PARAM*, BASE_HACKSHEILD_PARAMctor_BASE_HACKSHEILD_PARAM2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

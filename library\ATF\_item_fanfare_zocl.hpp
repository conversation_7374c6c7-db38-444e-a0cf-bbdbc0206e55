// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _item_fanfare_zocl
    {
        enum _Take_Type
        {
            eUpgrade = 0x0,
            eCombine = 0x1,
            eBox = 0x2,
            eLooting = 0x3,
            eGate = 0x4,
            eQuest = 0x5,
            eMake = 0x6,
        };
        enum _SubTypeParam
        {
            eSocket = 0x0,
            eGrade = 0x1,
        };
        struct _SubType
        {
            __int8 bitParmaType : 2;
            __int8 bitGrade : 6;
        };
        char byTakeType;
        _SubType bySubType;
        char byTableCode;
        unsigned __int16 wItemIndex;
        unsigned __int16 wMonsterIndex;
        unsigned __int16 wMapIndex;
        unsigned int dwCharacterSerial;
        char strCharacterName[17];
        bool bAllSend;
    public:
        void Init();
        _item_fanfare_zocl();
        void ctor__item_fanfare_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_item_fanfare_zocl, 31>(), "_item_fanfare_zocl");
END_ATF_NAMESPACE

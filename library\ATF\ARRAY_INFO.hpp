// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct ARRAY_INFO
    {
        int Dimension;
        unsigned int *BufferConformanceMark;
        unsigned int *BufferVarianceMark;
        unsigned int *MaxCountArray;
        unsigned int *OffsetArray;
        unsigned int *ActualCountArray;
    };
END_ATF_NAMESPACE

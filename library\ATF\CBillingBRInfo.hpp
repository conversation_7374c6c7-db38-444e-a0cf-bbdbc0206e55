// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingBR.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CBillingBRctor_CBillingBR2_ptr = void (WINAPIV*)(struct CBillingBR*);
        using CBillingBRctor_CBillingBR2_clbk = void (WINAPIV*)(struct CBillingBR*, CBillingBRctor_CBillingBR2_ptr);
        
        using CBillingBRdtor_CBillingBR7_ptr = void (WINAPIV*)(struct CBillingBR*);
        using CBillingBRdtor_CBillingBR7_clbk = void (WINAPIV*)(struct CBillingBR*, CBillingBRdtor_CBillingBR7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_result_csi_buy_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _result_csi_buy_zoclctor__result_csi_buy_zocl2_ptr = void (WINAPIV*)(struct _result_csi_buy_zocl*);
        using _result_csi_buy_zoclctor__result_csi_buy_zocl2_clbk = void (WINAPIV*)(struct _result_csi_buy_zocl*, _result_csi_buy_zoclctor__result_csi_buy_zocl2_ptr);
        using _result_csi_buy_zoclsize4_ptr = int (WINAPIV*)(struct _result_csi_buy_zocl*);
        using _result_csi_buy_zoclsize4_clbk = int (WINAPIV*)(struct _result_csi_buy_zocl*, _result_csi_buy_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ANIMUS_RETURN_DELAY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _ANIMUS_RETURN_DELAYProcess2_ptr = void (WINAPIV*)(struct _ANIMUS_RETURN_DELAY*, unsigned int, unsigned int);
        using _ANIMUS_RETURN_DELAYProcess2_clbk = void (WINAPIV*)(struct _ANIMUS_RETURN_DELAY*, unsigned int, unsigned int, _ANIMUS_RETURN_DELAYProcess2_ptr);
        
        using _ANIMUS_RETURN_DELAYctor__ANIMUS_RETURN_DELAY4_ptr = void (WINAPIV*)(struct _ANIMUS_RETURN_DELAY*);
        using _ANIMUS_RETURN_DELAYctor__ANIMUS_RETURN_DELAY4_clbk = void (WINAPIV*)(struct _ANIMUS_RETURN_DELAY*, _ANIMUS_RETURN_DELAYctor__ANIMUS_RETURN_DELAY4_ptr);
        
        using _ANIMUS_RETURN_DELAYdtor__ANIMUS_RETURN_DELAY6_ptr = void (WINAPIV*)(struct _ANIMUS_RETURN_DELAY*);
        using _ANIMUS_RETURN_DELAYdtor__ANIMUS_RETURN_DELAY6_clbk = void (WINAPIV*)(struct _ANIMUS_RETURN_DELAY*, _ANIMUS_RETURN_DELAYdtor__ANIMUS_RETURN_DELAY6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

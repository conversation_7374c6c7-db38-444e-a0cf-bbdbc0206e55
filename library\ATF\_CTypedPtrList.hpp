// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPtrList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  _CTypedPtrList<CPtrList,COleControlSiteOrWnd *> : CPtrList
    {
    };
END_ATF_NAMESPACE
#include <CPtrList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  _CTypedPtrList<CPtrList,CPtrList *> : CPtrList
    {
    };
END_ATF_NAMESPACE
#include <CObList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  _CTypedPtrList<CObList,CObList *> : CObList
    {
    };
END_ATF_NAMESPACE

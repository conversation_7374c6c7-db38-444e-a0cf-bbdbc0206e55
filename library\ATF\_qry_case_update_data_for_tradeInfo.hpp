// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_update_data_for_trade.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_update_data_for_tradector__qry_case_update_data_for_trade2_ptr = void (WINAPIV*)(struct _qry_case_update_data_for_trade*);
        using _qry_case_update_data_for_tradector__qry_case_update_data_for_trade2_clbk = void (WINAPIV*)(struct _qry_case_update_data_for_trade*, _qry_case_update_data_for_tradector__qry_case_update_data_for_trade2_ptr);
        using _qry_case_update_data_for_tradesize4_ptr = int (WINAPIV*)(struct _qry_case_update_data_for_trade*);
        using _qry_case_update_data_for_tradesize4_clbk = int (WINAPIV*)(struct _qry_case_update_data_for_trade*, _qry_case_update_data_for_tradesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

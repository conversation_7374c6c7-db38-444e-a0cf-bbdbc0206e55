// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _ItemExchange_fld : _base_fld
    {
        struct _output
        {
            char m_itmPdOutput[8];
            int m_nPdProCnt;
            unsigned int m_dwProb;
        };
        _output m_Output[61];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleRewardItem.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattleRewardItemctor_CGuildBattleRewardItem2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*);
            using GUILD_BATTLE__CGuildBattleRewardItemctor_CGuildBattleRewardItem2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, GUILD_BATTLE__CGuildBattleRewardItemctor_CGuildBattleRewardItem2_ptr);
            using GUILD_BATTLE__CGuildBattleRewardItemGetAmount4_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*);
            using GUILD_BATTLE__CGuildBattleRewardItemGetAmount4_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, GUILD_BATTLE__CGuildBattleRewardItemGetAmount4_ptr);
            using GUILD_BATTLE__CGuildBattleRewardItemGetItemCode6_ptr = char* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*);
            using GUILD_BATTLE__CGuildBattleRewardItemGetItemCode6_clbk = char* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, GUILD_BATTLE__CGuildBattleRewardItemGetItemCode6_ptr);
            using GUILD_BATTLE__CGuildBattleRewardItemGive8_ptr = struct GUILD_BATTLE::CGuildBattleRewardItem* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, struct CPlayer*);
            using GUILD_BATTLE__CGuildBattleRewardItemGive8_clbk = struct GUILD_BATTLE::CGuildBattleRewardItem* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, struct CPlayer*, GUILD_BATTLE__CGuildBattleRewardItemGive8_ptr);
            using GUILD_BATTLE__CGuildBattleRewardItemInit10_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, uint16_t);
            using GUILD_BATTLE__CGuildBattleRewardItemInit10_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, uint16_t, GUILD_BATTLE__CGuildBattleRewardItemInit10_ptr);
            using GUILD_BATTLE__CGuildBattleRewardItemIsNull12_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*);
            using GUILD_BATTLE__CGuildBattleRewardItemIsNull12_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, GUILD_BATTLE__CGuildBattleRewardItemIsNull12_ptr);
            using GUILD_BATTLE__CGuildBattleRewardItemSetItem14_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, char*);
            using GUILD_BATTLE__CGuildBattleRewardItemSetItem14_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItem*, char*, GUILD_BATTLE__CGuildBattleRewardItemSetItem14_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

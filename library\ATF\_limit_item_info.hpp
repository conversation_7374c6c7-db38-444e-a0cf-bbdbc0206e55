// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    struct _limit_item_info
    {
        bool bLoad;
        unsigned int dwStorageIndex;
        int nLimitNum;
        _INVENKEY Key;
    public:
        _limit_item_info();
        void ctor__limit_item_info();
        void init();
    };
END_ATF_NAMESPACE

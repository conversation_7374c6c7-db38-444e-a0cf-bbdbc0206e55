// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CFrameRate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CFrameRatector_CFrameRate2_ptr = void (WINAPIV*)(struct CFrameRate*);
        using CFrameRatector_CFrameRate2_clbk = void (WINAPIV*)(struct CFrameRate*, CFrameRatector_CFrameRate2_ptr);
        using CFrameRateCalcSpeedPerFrame4_ptr = void (WINAPIV*)(struct CFrameRate*);
        using CFrameRateCalcSpeedPerFrame4_clbk = void (WINAPIV*)(struct CFrameRate*, CFrameRateCalcSpeedPerFrame4_ptr);
        using CFrameRateGetFPS6_ptr = unsigned int (WINAPIV*)(struct CFrameRate*);
        using CFrameRateGetFPS6_clbk = unsigned int (WINAPIV*)(struct CFrameRate*, CFrameRateGetFPS6_ptr);
        using CFrameRateGetSpeedPerFrame8_ptr = float (WINAPIV*)(struct CFrameRate*);
        using CFrameRateGetSpeedPerFrame8_clbk = float (WINAPIV*)(struct CFrameRate*, CFrameRateGetSpeedPerFrame8_ptr);
        
        using CFrameRatedtor_CFrameRate13_ptr = void (WINAPIV*)(struct CFrameRate*);
        using CFrameRatedtor_CFrameRate13_clbk = void (WINAPIV*)(struct CFrameRate*, CFrameRatedtor_CFrameRate13_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

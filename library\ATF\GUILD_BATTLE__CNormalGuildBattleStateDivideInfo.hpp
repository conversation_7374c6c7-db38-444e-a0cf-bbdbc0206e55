// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateDivide.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateDividector_CNormalGuildBattleStateDivide2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateDivide*);
            using GUILD_BATTLE__CNormalGuildBattleStateDividector_CNormalGuildBattleStateDivide2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateDivide*, GUILD_BATTLE__CNormalGuildBattleStateDividector_CNormalGuildBattleStateDivide2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateDivideFin4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateDivide*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateDivideFin4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateDivide*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateDivideFin4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateDivideGetTerm6_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateDivide*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CNormalGuildBattleStateDivideGetTerm6_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateDivide*, struct ATL::CTimeSpan*, GUILD_BATTLE__CNormalGuildBattleStateDivideGetTerm6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateDividedtor_CNormalGuildBattleStateDivide8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateDivide*);
            using GUILD_BATTLE__CNormalGuildBattleStateDividedtor_CNormalGuildBattleStateDivide8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateDivide*, GUILD_BATTLE__CNormalGuildBattleStateDividedtor_CNormalGuildBattleStateDivide8_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union $DAED55BFC94E6097478CA68F0B175AFE
    {
        unsigned int dwRGBBitCount;
        unsigned int dwYUVBitCount;
        unsigned int dwZBufferBitDepth;
        unsigned int dwAlphaBitDepth;
        unsigned int dwLuminanceBitCount;
        unsigned int dwBumpBitCount;
        unsigned int dwPrivateFormatBitCount;
    };    
    static_assert(ATF::checkSize<$DAED55BFC94E6097478CA68F0B175AFE, 4>(), "$DAED55BFC94E6097478CA68F0B175AFE");
END_ATF_NAMESPACE

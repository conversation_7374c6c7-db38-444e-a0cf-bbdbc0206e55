// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_param_cashitem_dblog.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _param_cashitem_dblogctor__param_cashitem_dblog2_ptr = void (WINAPIV*)(struct _param_cashitem_dblog*, unsigned int);
        using _param_cashitem_dblogctor__param_cashitem_dblog2_clbk = void (WINAPIV*)(struct _param_cashitem_dblog*, unsigned int, _param_cashitem_dblogctor__param_cashitem_dblog2_ptr);
        using _param_cashitem_dblogsize4_ptr = int (WINAPIV*)(struct _param_cashitem_dblog*);
        using _param_cashitem_dblogsize4_clbk = int (WINAPIV*)(struct _param_cashitem_dblog*, _param_cashitem_dblogsize4_ptr);
        
        using _param_cashitem_dblogdtor__param_cashitem_dblog6_ptr = void (WINAPIV*)(struct _param_cashitem_dblog*);
        using _param_cashitem_dblogdtor__param_cashitem_dblog6_clbk = void (WINAPIV*)(struct _param_cashitem_dblog*, _param_cashitem_dblogdtor__param_cashitem_dblog6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _golden_box_item_ini
    {
        struct _golden_box_item_list
        {
            char m_szLimcode[64];
            unsigned __int16 m_wLimcount;
            unsigned __int16 m_wRate;
        };
        int m_bUse_event;
        int m_wYear[2];
        int m_byMonth[2];
        int m_byDay[2];
        int m_byHour[2];
        int m_byMinute[2];
        int m_EventTime[2];
        char m_szGoldenBoxcode[2][64];
        unsigned __int16 m_wGoldenBoxmax[2];
        char m_bygolden_item_num[2];
        unsigned int m_dwStarterBoxCnt;
        char m_szStarterBoxCode[2][64];
        char m_byLoopCnt;
        _golden_box_item_list m_golden_box_item_list[2][100];
    public:
        _golden_box_item_ini();
        void ctor__golden_box_item_ini();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

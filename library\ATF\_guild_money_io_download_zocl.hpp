// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _guild_money_io_download_zocl
    {
        unsigned __int16 wDataSize;
        char sData[10000];
    public:
        _guild_money_io_download_zocl();
        void ctor__guild_money_io_download_zocl();
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

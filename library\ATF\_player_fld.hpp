// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _player_fld : _base_fld
    {
        char m_strName[64];
        int m_nRaceCode;
        int m_nSexCode;
        float m_fMoveWalkRate;
        float m_fMoveRunRate;
        float m_fHeight;
        float m_fWidth;
        float m_fAttackRange;
        float m_fDefGap;
        float m_fDefFacing;
    };
END_ATF_NAMESPACE

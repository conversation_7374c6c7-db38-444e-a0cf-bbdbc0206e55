// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _PERSONALAMINE_INVEN_DB_BASE
    {
        struct _LIST
        {
            _INVENKEY Key;
            unsigned int dwDur;
        public:
            void Init();
            bool Release();
            bool Set(_STORAGE_LIST::_db_con* pItem);
            _LIST();
            void ctor__LIST();
        };
        bool bUsable;
        _LIST m_List[40];
    public:
        void Init();
        _PERSONALAMINE_INVEN_DB_BASE();
        void ctor__PERSONALAMINE_INVEN_DB_BASE();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_PERSONALAMINE_INVEN_DB_BASE, 321>(), "_PERSONALAMINE_INVEN_DB_BASE");
END_ATF_NAMESPACE

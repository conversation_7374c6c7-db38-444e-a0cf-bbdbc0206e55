// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_unmandtrader_re_registsingleitem.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_unmandtrader_re_registsingleitemctor__qry_case_unmandtrader_re_registsingleitem2_ptr = void (WINAPIV*)(struct _qry_case_unmandtrader_re_registsingleitem*);
        using _qry_case_unmandtrader_re_registsingleitemctor__qry_case_unmandtrader_re_registsingleitem2_clbk = void (WINAPIV*)(struct _qry_case_unmandtrader_re_registsingleitem*, _qry_case_unmandtrader_re_registsingleitemctor__qry_case_unmandtrader_re_registsingleitem2_ptr);
        using _qry_case_unmandtrader_re_registsingleitemsize4_ptr = int (WINAPIV*)(struct _qry_case_unmandtrader_re_registsingleitem*);
        using _qry_case_unmandtrader_re_registsingleitemsize4_clbk = int (WINAPIV*)(struct _qry_case_unmandtrader_re_registsingleitem*, _qry_case_unmandtrader_re_registsingleitemsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

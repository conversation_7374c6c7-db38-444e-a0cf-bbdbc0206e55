// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTimeSpan.hpp>
#include <GUILD_BATTLE__CGuildBattleStateListVtbl.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        #pragma pack(push, 8)
        struct CGuildBattleStateList
        {
            enum GBS_LOOP_TYPE
            {
                GBS_ONCE = 0x0,
                GBS_LOOP = 0x1,
                GBS_COUNT = 0x2,
                GBS_MAX = 0x3,
            };
            CGuildBattleStateListVtbl *vfptr;
            int m_iForceAdvance;
            int m_iState;
            struct CGuildBattleState *m_pkNextState;
            struct CGuildBattleState *m_pkCurState;
            int STATE_MAX;
            unsigned int m_uiLoopCnt;
            unsigned int m_uiCurLoopCnt;
            GBS_LOOP_TYPE m_eLoopType;
            bool m_bEnter;
        public:
            void Advance(int iAdvance);
            CGuildBattleStateList(int iStateMax, int iLoopType, unsigned int uiLoopCnt);
            void ctor_CGuildBattleStateList(int iStateMax, int iLoopType, unsigned int uiLoopCnt);
            int CheckLoop();
            void Clear();
            void ForceNext();
            struct ::ATF::ATL::CTimeSpan* GetTerm(struct ::ATF::ATL::CTimeSpan* result);
            int Goto();
            bool GotoState(int iState);
            bool IsEmpty();
            bool IsProc();
            void Log(char* szMsg);
            int Next(bool bForce);
            void Process(struct CGuildBattle* pkBattle);
            void SetNextState();
            void SetReady();
            void SetWait();
            ~CGuildBattleStateList();
            void dtor_CGuildBattleStateList();
        };
        #pragma pack(pop)    
        static_assert(ATF::checkSize<GUILD_BATTLE::CGuildBattleStateList, 56>(), "GUILD_BATTLE::CGuildBattleStateList");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

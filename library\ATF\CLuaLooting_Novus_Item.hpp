// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMapData.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct CLuaLooting_Novus_Item
    {
        struct _State
        {
            bool m_bExist;
        public:
            _State();
            void ctor__State();
        };
        _STORAGE_LIST::_db_con m_Item;
        CMapData *m_pMap;
        unsigned __int16 m_wLayerIndex;
        char m_byCreateType;
        float m_fLootPos[3];
        unsigned __int16 m_wLootRange;
        unsigned int m_dwLootCount;
    public:
        CLuaLooting_Novus_Item();
        void ctor_CLuaLooting_Novus_Item();
        ~CLuaLooting_Novus_Item();
        void dtor_CLuaLooting_Novus_Item();
    };
END_ATF_NAMESPACE

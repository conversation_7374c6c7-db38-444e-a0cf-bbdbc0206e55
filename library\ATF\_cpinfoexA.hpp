// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _cpinfoexA
    {
        unsigned int MaxCharSize;
        char Default<PERSON>har[2];
        char LeadByte[12];
        wchar_t UnicodeDefaultChar;
        unsigned int CodePage;
        char CodePageName[260];
    };
END_ATF_NAMESPACE

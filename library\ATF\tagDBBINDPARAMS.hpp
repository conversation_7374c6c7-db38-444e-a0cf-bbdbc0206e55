// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagDBBINDPARAMS
    {
        unsigned int cbMaxLen;
        unsigned int dwBinding;
        unsigned int dwDataType;
        unsigned int cbVarDataLen;
        unsigned int dwInfo;
        void *pData;
    };
END_ATF_NAMESPACE

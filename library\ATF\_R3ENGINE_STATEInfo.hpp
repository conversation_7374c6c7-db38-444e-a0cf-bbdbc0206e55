// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_R3ENGINE_STATE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _R3ENGINE_STATEctor__R3ENGINE_STATE1_ptr = int64_t (WINAPIV*)(struct _R3ENGINE_STATE*);
        using _R3ENGINE_STATEctor__R3ENGINE_STATE1_clbk = int64_t (WINAPIV*)(struct _R3ENGINE_STATE*, _R3ENGINE_STATEctor__R3ENGINE_STATE1_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

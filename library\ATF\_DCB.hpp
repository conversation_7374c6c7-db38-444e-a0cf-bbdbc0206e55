// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _DCB
    {
        unsigned int DCBlength;
        unsigned int BaudRate;
        unsigned __int32 fBinary : 1;
        unsigned __int32 fParity : 1;
        unsigned __int32 fOutxCtsFlow : 1;
        unsigned __int32 fOutxDsrFlow : 1;
        unsigned __int32 fDtrControl : 2;
        unsigned __int32 fDsrSensitivity : 1;
        unsigned __int32 fTXContinueOnXoff : 1;
        unsigned __int32 fOutX : 1;
        unsigned __int32 fInX : 1;
        unsigned __int32 fErrorChar : 1;
        unsigned __int32 fNull : 1;
        unsigned __int32 fRtsControl : 2;
        unsigned __int32 fAbortOnError : 1;
        unsigned __int32 fDummy2 : 17;
        unsigned __int16 wReserved;
        unsigned __int16 XonLim;
        unsigned __int16 XoffLim;
        char Byte<PERSON><PERSON>;
        char <PERSON><PERSON>;
        char <PERSON><PERSON><PERSON>;
        char <PERSON><PERSON><PERSON><PERSON>;
        char <PERSON><PERSON><PERSON>;
        char <PERSON><PERSON><PERSON><PERSON><PERSON>;
        char <PERSON>ofChar;
        char EvtChar;
        unsigned __int16 wReserved1;
    };
END_ATF_NAMESPACE

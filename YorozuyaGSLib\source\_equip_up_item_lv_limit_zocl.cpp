#include <_equip_up_item_lv_limit_zocl.hpp>


START_ATF_NAMESPACE
    _equip_up_item_lv_limit_zocl::_equip_up_item_lv_limit_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _equip_up_item_lv_limit_zocl*);
        (org_ptr(0x1400f06f0L))(this);
    };
    void _equip_up_item_lv_limit_zocl::ctor__equip_up_item_lv_limit_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _equip_up_item_lv_limit_zocl*);
        (org_ptr(0x1400f06f0L))(this);
    };
END_ATF_NAMESPACE

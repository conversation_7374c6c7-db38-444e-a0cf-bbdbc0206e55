// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetIndexList.hpp>
#include <_DELAY_PROCESSVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _DELAY_PROCESS
    {
        _DELAY_PROCESSVtbl *vfptr;
        unsigned int m_dwObjectNum;
        unsigned int *m_pdwPushTime;
        unsigned int *m_pdwPushSerial;
        CNetIndexList m_list;
        int m_dwTerm;
    public:
        void CheckOnLoop();
        void Delete(unsigned int dwIndex, unsigned int dwSerial);
        bool Init(unsigned int dwObjectNum, unsigned int dwTerm);
        void Process(unsigned int dwIndex, unsigned int dwSerial);
        bool Push(unsigned int dwIndex, unsigned int dwSerial);
        _DELAY_PROCESS(unsigned int dwObjectNum, unsigned int dwTerm);
        void ctor__DELAY_PROCESS(unsigned int dwObjectNum, unsigned int dwTerm);
        _DELAY_PROCESS();
        void ctor__DELAY_PROCESS();
        ~_DELAY_PROCESS();
        void dtor__DELAY_PROCESS();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_DELAY_PROCESS, 200>(), "_DELAY_PROCESS");
END_ATF_NAMESPACE

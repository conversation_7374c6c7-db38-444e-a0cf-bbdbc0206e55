// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CAtlModuleT.hpp>
#include <ATL___ATL_OBJMAP_ENTRY30.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComModule : CAtlModuleT<CComModule>
        {
            _ATL_OBJMAP_ENTRY30 *m_pObjMap;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

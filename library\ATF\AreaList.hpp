// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AreaData.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct AreaList
    {
        std::vector<AreaData,std::allocator<AreaData> > List;
        unsigned int Count;
        unsigned int Width;
        unsigned int Height;
        char *pData;
        unsigned int DataEnd;
        char *pRealData;
    public:
        AreaList(struct AreaList* __that);
        void ctor_AreaList(struct AreaList* __that);
        AreaList();
        void ctor_AreaList();
        void ExtractData();
        void Push(struct AreaData adata);
        ~AreaList();
        void dtor_AreaList();
    };    
    static_assert(ATF::checkSize<AreaList, 80>(), "AreaList");
END_ATF_NAMESPACE

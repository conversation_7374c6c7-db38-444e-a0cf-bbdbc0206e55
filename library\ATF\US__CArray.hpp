// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaScript.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        #pragma pack(push, 8)
        template<typename _Ty>
        struct CArray
        {
            struct CArrayVtbl
            {
                void *(WINAPIV *__vecDelDtor)(CArray<_Ty> *_this, unsigned int);
            };
            CArrayVtbl *vfptr;
            bool m_bAlloc;
            _Ty *m_pBuffer;
            unsigned int m_dwCount;
        };
        #pragma pack(pop)
    }; // end namespace US
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <FinalDecisionProcessor.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using FinalDecisionProcessorDoit2_ptr = int (WINAPIV*)(struct FinalDecisionProcessor*, Cmd, struct CPlayer*, char*);
        using FinalDecisionProcessorDoit2_clbk = int (WINAPIV*)(struct FinalDecisionProcessor*, Cmd, struct CPlayer*, char*, FinalDecisionProcessorDoit2_ptr);
        
        using FinalDecisionProcessorctor_FinalDecisionProcessor4_ptr = void (WINAPIV*)(struct FinalDecisionProcessor*);
        using FinalDecisionProcessorctor_FinalDecisionProcessor4_clbk = void (WINAPIV*)(struct FinalDecisionProcessor*, FinalDecisionProcessorctor_FinalDecisionProcessor4_ptr);
        using FinalDecisionProcessorInitialize6_ptr = bool (WINAPIV*)(struct FinalDecisionProcessor*);
        using FinalDecisionProcessorInitialize6_clbk = bool (WINAPIV*)(struct FinalDecisionProcessor*, FinalDecisionProcessorInitialize6_ptr);
        using FinalDecisionProcessor_FinalDecision8_ptr = void (WINAPIV*)(struct FinalDecisionProcessor*);
        using FinalDecisionProcessor_FinalDecision8_clbk = void (WINAPIV*)(struct FinalDecisionProcessor*, FinalDecisionProcessor_FinalDecision8_ptr);
        using FinalDecisionProcessor_ReqNetFinalDecision10_ptr = void (WINAPIV*)(struct FinalDecisionProcessor*, struct CPlayer*);
        using FinalDecisionProcessor_ReqNetFinalDecision10_clbk = void (WINAPIV*)(struct FinalDecisionProcessor*, struct CPlayer*, FinalDecisionProcessor_ReqNetFinalDecision10_ptr);
        using FinalDecisionProcessor_SetWinner12_ptr = void (WINAPIV*)(struct FinalDecisionProcessor*);
        using FinalDecisionProcessor_SetWinner12_clbk = void (WINAPIV*)(struct FinalDecisionProcessor*, FinalDecisionProcessor_SetWinner12_ptr);
        
        using FinalDecisionProcessordtor_FinalDecisionProcessor17_ptr = void (WINAPIV*)(struct FinalDecisionProcessor*);
        using FinalDecisionProcessordtor_FinalDecisionProcessor17_clbk = void (WINAPIV*)(struct FinalDecisionProcessor*, FinalDecisionProcessordtor_FinalDecisionProcessor17_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AutoMineMachine.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using AutoMineMachinector_AutoMineMachine2_ptr = void (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachinector_AutoMineMachine2_clbk = void (WINAPIV*)(struct AutoMineMachine*, AutoMineMachinector_AutoMineMachine2_ptr);
        using AutoMineMachineChangeOwner4_ptr = void (WINAPIV*)(struct AutoMineMachine*, struct CGuild*);
        using AutoMineMachineChangeOwner4_clbk = void (WINAPIV*)(struct AutoMineMachine*, struct CGuild*, AutoMineMachineChangeOwner4_ptr);
        using AutoMineMachineCharge6_ptr = void (WINAPIV*)(struct AutoMineMachine*, int, int, int);
        using AutoMineMachineCharge6_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, int, int, AutoMineMachineCharge6_ptr);
        using AutoMineMachineDischarge8_ptr = void (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachineDischarge8_clbk = void (WINAPIV*)(struct AutoMineMachine*, AutoMineMachineDischarge8_ptr);
        using AutoMineMachineGetMachineInfo10_ptr = void (WINAPIV*)(struct AutoMineMachine*, struct _DB_LOAD_AUTOMINE_MACHINE*);
        using AutoMineMachineGetMachineInfo10_clbk = void (WINAPIV*)(struct AutoMineMachine*, struct _DB_LOAD_AUTOMINE_MACHINE*, AutoMineMachineGetMachineInfo10_ptr);
        using AutoMineMachineGetOutOreInAutoMine12_ptr = void (WINAPIV*)(struct AutoMineMachine*, struct CPlayer*, char*);
        using AutoMineMachineGetOutOreInAutoMine12_clbk = void (WINAPIV*)(struct AutoMineMachine*, struct CPlayer*, char*, AutoMineMachineGetOutOreInAutoMine12_ptr);
        using AutoMineMachineGetOwnerGuild14_ptr = struct CGuild* (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachineGetOwnerGuild14_clbk = struct CGuild* (WINAPIV*)(struct AutoMineMachine*, AutoMineMachineGetOwnerGuild14_ptr);
        using AutoMineMachineGetState16_ptr = void (WINAPIV*)(struct AutoMineMachine*, unsigned int*);
        using AutoMineMachineGetState16_clbk = void (WINAPIV*)(struct AutoMineMachine*, unsigned int*, AutoMineMachineGetState16_ptr);
        using AutoMineMachineInitialize18_ptr = bool (WINAPIV*)(struct AutoMineMachine*, char, char);
        using AutoMineMachineInitialize18_clbk = bool (WINAPIV*)(struct AutoMineMachine*, char, char, AutoMineMachineInitialize18_ptr);
        using AutoMineMachineIsMaster20_ptr = bool (WINAPIV*)(struct AutoMineMachine*, struct CPlayer*);
        using AutoMineMachineIsMaster20_clbk = bool (WINAPIV*)(struct AutoMineMachine*, struct CPlayer*, AutoMineMachineIsMaster20_ptr);
        using AutoMineMachineLoadDatabase22_ptr = bool (WINAPIV*)(struct AutoMineMachine*, struct _DB_LOAD_AUTOMINE_MACHINE*);
        using AutoMineMachineLoadDatabase22_clbk = bool (WINAPIV*)(struct AutoMineMachine*, struct _DB_LOAD_AUTOMINE_MACHINE*, AutoMineMachineLoadDatabase22_ptr);
        using AutoMineMachineLoop24_ptr = void (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachineLoop24_clbk = void (WINAPIV*)(struct AutoMineMachine*, AutoMineMachineLoop24_ptr);
        using AutoMineMachineMoveOreInAutoMine26_ptr = void (WINAPIV*)(struct AutoMineMachine*, int, char, char, char, char);
        using AutoMineMachineMoveOreInAutoMine26_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, char, char, char, char, AutoMineMachineMoveOreInAutoMine26_ptr);
        using AutoMineMachineOreMerge28_ptr = void (WINAPIV*)(struct AutoMineMachine*, int, char*);
        using AutoMineMachineOreMerge28_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, char*, AutoMineMachineOreMerge28_ptr);
        using AutoMineMachineSelectOre30_ptr = void (WINAPIV*)(struct AutoMineMachine*, int, char);
        using AutoMineMachineSelectOre30_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, char, AutoMineMachineSelectOre30_ptr);
        using AutoMineMachineSendMsg_MachineInfo32_ptr = void (WINAPIV*)(struct AutoMineMachine*, int);
        using AutoMineMachineSendMsg_MachineInfo32_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, AutoMineMachineSendMsg_MachineInfo32_ptr);
        using AutoMineMachineSendMsg_ResultCode34_ptr = void (WINAPIV*)(struct AutoMineMachine*, int, char, char);
        using AutoMineMachineSendMsg_ResultCode34_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, char, char, AutoMineMachineSendMsg_ResultCode34_ptr);
        using AutoMineMachineSetOpenUI36_ptr = void (WINAPIV*)(struct AutoMineMachine*, bool);
        using AutoMineMachineSetOpenUI36_clbk = void (WINAPIV*)(struct AutoMineMachine*, bool, AutoMineMachineSetOpenUI36_ptr);
        using AutoMineMachineSetOwner38_ptr = bool (WINAPIV*)(struct AutoMineMachine*, char, char, struct CGuild*);
        using AutoMineMachineSetOwner38_clbk = bool (WINAPIV*)(struct AutoMineMachine*, char, char, struct CGuild*, AutoMineMachineSetOwner38_ptr);
        using AutoMineMachineStart40_ptr = void (WINAPIV*)(struct AutoMineMachine*, int);
        using AutoMineMachineStart40_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, AutoMineMachineStart40_ptr);
        using AutoMineMachineStop42_ptr = void (WINAPIV*)(struct AutoMineMachine*, int);
        using AutoMineMachineStop42_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, AutoMineMachineStop42_ptr);
        using AutoMineMachineSubChargeCost44_ptr = void (WINAPIV*)(struct AutoMineMachine*, char, char*);
        using AutoMineMachineSubChargeCost44_clbk = void (WINAPIV*)(struct AutoMineMachine*, char, char*, AutoMineMachineSubChargeCost44_ptr);
        using AutoMineMachine_Convert_GoldToGage46_ptr = int (WINAPIV*)(struct AutoMineMachine*, int);
        using AutoMineMachine_Convert_GoldToGage46_clbk = int (WINAPIV*)(struct AutoMineMachine*, int, AutoMineMachine_Convert_GoldToGage46_ptr);
        using AutoMineMachine_InitMineOre48_ptr = bool (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachine_InitMineOre48_clbk = bool (WINAPIV*)(struct AutoMineMachine*, AutoMineMachine_InitMineOre48_ptr);
        using AutoMineMachine_Mining50_ptr = void (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachine_Mining50_clbk = void (WINAPIV*)(struct AutoMineMachine*, AutoMineMachine_Mining50_ptr);
        using AutoMineMachinepush_dqs_battery_charge52_ptr = void (WINAPIV*)(struct AutoMineMachine*, uint16_t, int, int);
        using AutoMineMachinepush_dqs_battery_charge52_clbk = void (WINAPIV*)(struct AutoMineMachine*, uint16_t, int, int, AutoMineMachinepush_dqs_battery_charge52_ptr);
        using AutoMineMachinepush_dqs_battery_discharge54_ptr = void (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachinepush_dqs_battery_discharge54_clbk = void (WINAPIV*)(struct AutoMineMachine*, AutoMineMachinepush_dqs_battery_discharge54_ptr);
        using AutoMineMachinepush_dqs_getore56_ptr = void (WINAPIV*)(struct AutoMineMachine*, int, int, char);
        using AutoMineMachinepush_dqs_getore56_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, int, char, AutoMineMachinepush_dqs_getore56_ptr);
        using AutoMineMachinepush_dqs_mineore58_ptr = void (WINAPIV*)(struct AutoMineMachine*, int, int);
        using AutoMineMachinepush_dqs_mineore58_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, int, AutoMineMachinepush_dqs_mineore58_ptr);
        using AutoMineMachinepush_dqs_moveore60_ptr = void (WINAPIV*)(struct AutoMineMachine*, int, struct _INVENKEY*, char, int, struct _INVENKEY*, char);
        using AutoMineMachinepush_dqs_moveore60_clbk = void (WINAPIV*)(struct AutoMineMachine*, int, struct _INVENKEY*, char, int, struct _INVENKEY*, char, AutoMineMachinepush_dqs_moveore60_ptr);
        using AutoMineMachinepush_dqs_newowner62_ptr = void (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachinepush_dqs_newowner62_clbk = void (WINAPIV*)(struct AutoMineMachine*, AutoMineMachinepush_dqs_newowner62_ptr);
        using AutoMineMachinepush_dqs_selore64_ptr = void (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachinepush_dqs_selore64_clbk = void (WINAPIV*)(struct AutoMineMachine*, AutoMineMachinepush_dqs_selore64_ptr);
        using AutoMineMachinepush_dqs_workingstate66_ptr = void (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachinepush_dqs_workingstate66_clbk = void (WINAPIV*)(struct AutoMineMachine*, AutoMineMachinepush_dqs_workingstate66_ptr);
        
        using AutoMineMachinedtor_AutoMineMachine68_ptr = void (WINAPIV*)(struct AutoMineMachine*);
        using AutoMineMachinedtor_AutoMineMachine68_clbk = void (WINAPIV*)(struct AutoMineMachine*, AutoMineMachinedtor_AutoMineMachine68_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

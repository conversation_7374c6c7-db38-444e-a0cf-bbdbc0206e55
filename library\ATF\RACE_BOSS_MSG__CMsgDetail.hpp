// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RACE_BOSS_MSG__CMsgInfo.hpp>


START_ATF_NAMESPACE
    namespace RACE_BOSS_MSG
    {
        namespace Detail
        {
            extern ::std::array<hook_record, 17> CMsg_functions;
        }; // end namespace Detail
    }; // end namespace RACE_BOSS_MSG
END_ATF_NAMESPACE

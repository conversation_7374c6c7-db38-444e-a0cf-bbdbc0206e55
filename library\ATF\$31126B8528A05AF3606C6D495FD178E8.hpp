// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $31126B8528A05AF3606C6D495FD178E8
    {
        BYTE gap0[8];
        __int16 iVal;
    };    
    static_assert(ATF::checkSize<$31126B8528A05AF3606C6D495FD178E8, 10>(), "$31126B8528A05AF3606C6D495FD178E8");
END_ATF_NAMESPACE

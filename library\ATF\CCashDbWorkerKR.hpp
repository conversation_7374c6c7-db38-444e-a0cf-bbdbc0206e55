// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CashDbWorker.hpp>


START_ATF_NAMESPACE
    struct  CCashDbWorkerKR : CashDbWorker
    {
    public:
        CCashDbWorkerKR();
        void ctor_CCashDbWorkerKR();
        ~CCashDbWorkerKR();
        void dtor_CCashDbWorkerKR();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CNormalGuildBattleGuildMember
        {
            unsigned int m_dwSerial;
            bool m_bRestart;
            struct CMapData *m_pOldBindMapData;
            struct _dummy_position *m_pOldBindDummyData;
            char m_szOldBindMapCode[12];
            char m_szOldBindDummy[12];
            unsigned __int16 m_usGoalCnt;
            unsigned __int16 m_usKillCnt;
            long double m_dPvpPoint;
            struct _guild_member_info *m_pkMember;
        public:
            void AddGoldCnt();
            void AddKillCnt();
            CNormalGuildBattleGuildMember();
            void ctor_CNormalGuildBattleGuildMember();
            void CleanUpBattle();
            void Clear();
            long double DecPvpPoint(struct CNormalGuildBattleLogger* kLogger);
            uint16_t GetGoalCount();
            uint16_t GetIndex();
            uint16_t GetKillCount();
            CPlayer* GetPlayer();
            unsigned int GetSerial();
            long double IncPvpPoint(long double dInc, struct CNormalGuildBattleLogger* kLogger);
            bool IsCommitteeMember();
            bool IsEmpty();
            bool IsEnableStart();
            bool IsExist();
            bool IsReStart();
            void Join(struct _guild_member_info* pkMember);
            void Login();
            void NetClose();
            void PushDQSPvpPoint(unsigned int dwPvpPoint);
            void ReturnBindPos();
            void ReturnStartPos();
            void Send(char* byType, char* pSend, unsigned int uiSize);
            void SetBattleState(bool bFlag, char byColorInx);
            void SetReStartFlag();
            void StockOldInfo();
            ~CNormalGuildBattleGuildMember();
            void dtor_CNormalGuildBattleGuildMember();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <common/ATFCore.hpp>
#include <qry_case_golden_box_itemDetail.hpp>


START_ATF_NAMESPACE
    namespace Register
    {
        class qry_case_golden_box_itemRegister : public IRegister
        {
            public: 
                void Register() override
                {
                    auto& hook_core = CATFCore::get_instance();
                    for (auto& r : Detail::qry_case_golden_box_item_functions)
                        hook_core.reg_wrapper(r.pBind, r);
                }
        };
    }; // end namespace Register
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _COMMTIMEOUTS
    {
        unsigned int ReadIntervalTimeout;
        unsigned int ReadTotalTimeoutMultiplier;
        unsigned int ReadTotalTimeoutConstant;
        unsigned int WriteTotalTimeoutMultiplier;
        unsigned int WriteTotalTimeoutConstant;
    };
END_ATF_NAMESPACE

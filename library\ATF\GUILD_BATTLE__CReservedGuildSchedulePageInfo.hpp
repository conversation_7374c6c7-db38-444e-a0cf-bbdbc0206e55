// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CReservedGuildSchedulePage.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CReservedGuildSchedulePagector_CReservedGuildSchedulePage2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*);
            using GUILD_BATTLE__CReservedGuildSchedulePagector_CReservedGuildSchedulePage2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, GUILD_BATTLE__CReservedGuildSchedulePagector_CReservedGuildSchedulePage2_ptr);
            using GUILD_BATTLE__CReservedGuildSchedulePageClear4_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*);
            using GUILD_BATTLE__CReservedGuildSchedulePageClear4_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, GUILD_BATTLE__CReservedGuildSchedulePageClear4_ptr);
            using GUILD_BATTLE__CReservedGuildSchedulePageFind6_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, unsigned int);
            using GUILD_BATTLE__CReservedGuildSchedulePageFind6_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, unsigned int, GUILD_BATTLE__CReservedGuildSchedulePageFind6_ptr);
            using GUILD_BATTLE__CReservedGuildSchedulePageFlip8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*);
            using GUILD_BATTLE__CReservedGuildSchedulePageFlip8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, GUILD_BATTLE__CReservedGuildSchedulePageFlip8_ptr);
            using GUILD_BATTLE__CReservedGuildSchedulePageIncVer10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*);
            using GUILD_BATTLE__CReservedGuildSchedulePageIncVer10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, GUILD_BATTLE__CReservedGuildSchedulePageIncVer10_ptr);
            using GUILD_BATTLE__CReservedGuildSchedulePageInit12_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, char);
            using GUILD_BATTLE__CReservedGuildSchedulePageInit12_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, char, GUILD_BATTLE__CReservedGuildSchedulePageInit12_ptr);
            using GUILD_BATTLE__CReservedGuildSchedulePageSend14_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, int, unsigned int, struct GUILD_BATTLE::CReservedGuildSchedulePage*);
            using GUILD_BATTLE__CReservedGuildSchedulePageSend14_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, int, unsigned int, struct GUILD_BATTLE::CReservedGuildSchedulePage*, GUILD_BATTLE__CReservedGuildSchedulePageSend14_ptr);
            
            using GUILD_BATTLE__CReservedGuildSchedulePagedtor_CReservedGuildSchedulePage16_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*);
            using GUILD_BATTLE__CReservedGuildSchedulePagedtor_CReservedGuildSchedulePage16_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildSchedulePage*, GUILD_BATTLE__CReservedGuildSchedulePagedtor_CReservedGuildSchedulePage16_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

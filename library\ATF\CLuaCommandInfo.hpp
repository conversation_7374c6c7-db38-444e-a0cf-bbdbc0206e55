// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaCommand.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CLuaCommandctor_CLuaCommand2_ptr = void (WINAPIV*)(struct CLuaCommand*);
        using CLuaCommandctor_CLuaCommand2_clbk = void (WINAPIV*)(struct CLuaCommand*, CLuaCommandctor_CLuaCommand2_ptr);
        using CLuaCommandGetBuffer4_ptr = char* (WINAPIV*)(struct CLuaCommand*);
        using CLuaCommandGetBuffer4_clbk = char* (WINAPIV*)(struct CLuaCommand*, CLuaCommandGetBuffer4_ptr);
        using CLuaCommandGetType6_ptr = char (WINAPIV*)(struct CLuaCommand*);
        using CLuaCommandGetType6_clbk = char (WINAPIV*)(struct CLuaCommand*, CLuaCommandGetType6_ptr);
        using CLuaCommandInit8_ptr = void (WINAPIV*)(struct CLuaCommand*);
        using CLuaCommandInit8_clbk = void (WINAPIV*)(struct CLuaCommand*, CLuaCommandInit8_ptr);
        using CLuaCommandSetCmd10_ptr = void (WINAPIV*)(struct CLuaCommand*, char, char*);
        using CLuaCommandSetCmd10_clbk = void (WINAPIV*)(struct CLuaCommand*, char, char*, CLuaCommandSetCmd10_ptr);
        
        using CLuaCommanddtor_CLuaCommand15_ptr = void (WINAPIV*)(struct CLuaCommand*);
        using CLuaCommanddtor_CLuaCommand15_clbk = void (WINAPIV*)(struct CLuaCommand*, CLuaCommanddtor_CLuaCommand15_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

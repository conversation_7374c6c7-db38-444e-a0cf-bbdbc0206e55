// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetCriticalSection.hpp>


START_ATF_NAMESPACE
    struct _NET_BUFFER
    {
        unsigned int m_nMaxSize;
        unsigned int m_nEtrSize;
        int m_dwPopRot;
        int m_dwPopPnt;
        int m_dwPushRot;
        int m_dwPushPnt;
        char *m_sMainBuffer;
        CNetCriticalSection m_csPush;
        CNetCriticalSection m_csPop;
        char *m_sTempBuffer;
    public:
        void AddPopPos(unsigned int dwAddSize);
        void AddPushPos(unsigned int dwAddSize);
        bool AllocBuffer(int nMaxSize, int nEtrSize, char* pTemp);
        int GetLeftLoadSize();
        char* GetPopPoint(bool* pbMiss);
        char* GetPushPos();
        char* GetSendPoint(int* pnSendSize, bool* pMiss);
        void Init();
        _NET_BUFFER();
        void ctor__NET_BUFFER();
        ~_NET_BUFFER();
        void dtor__NET_BUFFER();
    };
END_ATF_NAMESPACE

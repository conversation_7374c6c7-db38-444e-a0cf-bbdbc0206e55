// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_in_guildbattlerewardmoney
    {
        unsigned int dwGuildIndex;
        unsigned int dwGuildSerial;
        unsigned int dwAddGold;
        unsigned int dwAddDalant;
        char byDate[4];
        long double out_totalgold;
        long double out_totaldalant;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_in_guildbattlerewardmoney, 40>(), "_qry_case_in_guildbattlerewardmoney");
END_ATF_NAMESPACE

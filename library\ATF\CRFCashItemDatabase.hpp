// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRFNewDatabase.hpp>
#include <_param_cash_rollback.hpp>
#include <_param_cash_select.hpp>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    struct  CRFCashItemDatabase : CRFNewDatabase
    {
    public:
        CRFCashItemDatabase();
        void ctor_CRFCashItemDatabase();
        bool CallProc_InsertCashItemLog(unsigned int dwSerial, char byLv, char* szItemCode, char* szItemName, char byNum, unsigned int dwCost);
        int CallProc_RFONLINE_Cancel(struct _param_cash_rollback::__list* list);
        int CallProc_RFONLINE_Cancel_Jap(struct _param_cash_rollback* list, int iIndex);
        int CallProc_RFOnlineAuth(struct _param_cash_select* rParam);
        int CallProc_RFOnlineAuth_Jap(struct _param_cash_select* rParam);
        int CallProc_RFOnlineAvg_Event(unsigned int* iAvgCashSelling);
        int CallProc_RFOnlineUse(struct _param_cash_update* rParam, int nIdx);
        int CallProc_RFOnlineUse_Jap(struct _param_cash_update* rParam, int nIdx);
        void dhRExtractSubString(char* szSub, char* szFull, int n);
        ~CRFCashItemDatabase();
        void dtor_CRFCashItemDatabase();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetIndexList.hpp>
#include <CPlayer.hpp>
#include <GMCallMgrVtbl.hpp>
#include <GMRequestData.hpp>


START_ATF_NAMESPACE
    struct GMCallMgr
    {
        enum GMAcceptErrorCode
        {
            eAcceptOk = 0x0,
            eAcceptErrorNotUser = 0x1,
            eAcceptErrorCannotMove = 0x2,
            eAcceptErrorAssert = 0x3,
        };
        GMCallMgrVtbl *vfptr;
        GMRequestData m_buffGMRequestData[2532];
        CNetIndexList m_listGMRequestDataEmpty;
        CNetIndexList m_listGMRequestDataTask;
    public:
        GMCallMgr();
        void ctor_GMCallMgr();
        struct GMRequestData* GetGMRequestDataPtr(struct CPlayer* pOne);
        void Init();
        void InitReqBuff();
        struct GMRequestData* PopReqEmptNode();
        void PushReqNode(struct GMRequestData* pInst);
        bool RequestAcceptGMCall(struct CPlayer* pOne, unsigned int dwUserSerial);
        bool RequestGMCall(struct CPlayer* pOne, int bCall);
        bool RequestGMList(struct CPlayer* pOne, int nCurrPageIndex);
        void SendResponseAcceptResult(struct CPlayer* pOneGM, struct CPlayer* pOneUser, int nErrorCode);
        bool SendResponseGMCall(struct CPlayer* pOne, int bCallState);
        bool SendResponseGMList(struct CPlayer* pOne, int nCurrPageIndex);
        ~GMCallMgr();
        void dtor_GMCallMgr();
    };
END_ATF_NAMESPACE

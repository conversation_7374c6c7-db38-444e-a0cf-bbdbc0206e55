// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _other_unit_ride_change_zocl
    {
        unsigned __int16 wIndex;
        unsigned int dwSerial;
        unsigned __int16 wEquipVer;
        bool bTake;
        unsigned int dwUnitSerial;
        __int16 zNewPos[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

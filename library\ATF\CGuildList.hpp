// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuildListVtbl.hpp>
#include <__guild_list_page.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CGuildList
    {
        CGuildListVtbl *vfptr;
        bool m_bInit;
        char m_byMaxPage[3];
        __guild_list_page *m_pGuildList[3];
    public:
        void AddList(char byRace, char byGrade, char* pwszGuildName, char* pwszMasterName);
        CGuildList();
        void ctor_CGuildList();
        bool Init();
        void SendList(uint16_t wIndex, char byRace, char byPage);
        void SetGrade(char byRace, char* pwszGuildName, char byGrade);
        void SetGuildMaster(char byRace, char* pwszGuildName, char* pwszMasterName);
        ~CGuildList();
        void dtor_CGuildList();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CGuildList, 40>(), "CGuildList");
END_ATF_NAMESPACE

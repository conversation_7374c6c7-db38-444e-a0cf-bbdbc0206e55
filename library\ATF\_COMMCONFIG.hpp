// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DCB.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _COMMCONFIG
    {
        unsigned int dwSize;
        unsigned __int16 wVersion;
        unsigned __int16 wReserved;
        _DCB dcb;
        unsigned int dwProviderSubType;
        unsigned int dwProviderOffset;
        unsigned int dwProviderSize;
        wchar_t wcProviderData[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

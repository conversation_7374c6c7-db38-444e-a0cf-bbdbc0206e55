// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _notify_not_use_premium_cashitem_zocl
    {
        unsigned __int16 wItemSerial;
    public:
        int size();
    };       
    #pragma pack(pop)
    static_assert(ATF::checkSize<_notify_not_use_premium_cashitem_zocl, 2>(), "_notify_not_use_premium_cashitem_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LTD.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _LTDset2_ptr = void (WINAPIV*)(struct _LTD*, char, char);
        using _LTDset2_clbk = void (WINAPIV*)(struct _LTD*, char, char, _LTDset2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMapData.hpp>
#include <_LAYER_SET.hpp>
#include <_dummy_position.hpp>
#include <std__vector.hpp>
#include <tagTIMESTAMP_STRUCT.hpp>
#include <RoomCharInfo.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CGuildRoomInfo
    {
        bool m_bRent;
        char m_byRoomType;
        char m_byRace;
        int m_iGuildIdx;
        unsigned int m_dwGuildSerial;
        int m_timeBegin;
        int m_timer;
        std::vector<RoomCharInfo> m_vecRoomMember;
        CMapData *m_pRoomMap;
        unsigned __int16 m_wRoomMapLayer;
        _LAYER_SET *m_pLayerSet;
        _dummy_position *m_pRoomStartDummy;
    public:
        CGuildRoomInfo(struct CGuildRoomInfo* __that);
        void ctor_CGuildRoomInfo(struct CGuildRoomInfo* __that);
        CGuildRoomInfo();
        void ctor_CGuildRoomInfo();
        unsigned int GetGuildSerial();
        struct CMapData* GetMapData();
        uint16_t GetMapLayer();
        bool GetMapPos(float* pPos);
        int GetRestTime();
        char GetRoomType();
        bool IsMemberIn(int n, unsigned int dwCharSerial);
        bool IsRent();
        void OutAllRoomMember();
        int RoomIn(int n, unsigned int dwCharSerial);
        int RoomOut(int n, unsigned int dwCharSerial);
        void RoomTimer();
        void Room_Initialize();
        void SendDQS_RoomInsert();
        void SendDQS_RoomUpdate();
        void SendMsg_RoomTimeOver();
        bool SetPlayerOut(int n, unsigned int dwCharSerial, int iMemberIdx);
        bool SetRoom(int iGuildInx, unsigned int dwGuildSerial);
        void SetRoomMapInfo(struct CMapData* pMap, uint16_t wMapLayer, char byRoomType, char byRace);
        void SetRoomTime();
        void SetRoomTime_Restore(struct tagTIMESTAMP_STRUCT ts);
        bool SetRoom_Restore(int iGuildInx, unsigned int dwGuildSerial, struct tagTIMESTAMP_STRUCT ts);
        void TimeOver();
        ~CGuildRoomInfo();
        void dtor_CGuildRoomInfo();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CGuildRoomInfo, 96>(), "CGuildRoomInfo");
END_ATF_NAMESPACE

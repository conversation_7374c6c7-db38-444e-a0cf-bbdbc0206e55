// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_server_notify_inform_zone.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _server_notify_inform_zonesize2_ptr = int (WINAPIV*)(struct _server_notify_inform_zone*);
        using _server_notify_inform_zonesize2_clbk = int (WINAPIV*)(struct _server_notify_inform_zone*, _server_notify_inform_zonesize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

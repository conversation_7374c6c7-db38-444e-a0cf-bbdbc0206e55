// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAITimer.hpp>
#include <CAttack.hpp>
#include <CCharacter.hpp>
#include <CMapData.hpp>
#include <CPlayer.hpp>
#include <SKILL.hpp>
#include <_animus_create_setdata.hpp>
#include <_animus_fld.hpp>
#include <_attack_param.hpp>
#include <_object_id.hpp>


START_ATF_NAMESPACE
    struct  CAnimus : CCharacter
    {
        char m_byClassCode;
        int m_nHP;
        int m_nFP;
        unsigned __int64 m_dwExp;
        CPlayer *m_pMaster;
        unsigned int m_dwMasterSerial;
        char m_wszMasterName[17];
        char m_aszMasterName[17];
        char m_byRoleCode;
        unsigned int m_dwLastDestroyTime;
        float m_fMoveSpeed;
        char m_byPosRaceTown;
        CMapData *m_pBeforeTownCheckMap;
        float m_fBeforeTownCheckPos[2];
        unsigned int m_dwStunTime;
        unsigned int m_dwBeAttackedTargetTime;
        CCharacter *m_pNextTarget;
        int m_nMaxAttackPnt;
        unsigned int m_tmNextEatMasterFP;
        _animus_fld *m_pRecord;
        int m_nMaxHP;
        int m_nMaxFP;
        float m_Mightiness;
        int m_DefPart[5];
        unsigned int m_dwAIMode;
        CCharacter *m_pTarget;
        CAITimer m_AITimer[3];
        SKILL m_Skill[2];
    public:
        void AIInit();
        void Action();
        void AlterExp(int64_t nAddExp);
        void AlterExp_MasterReport(int64_t nAlterExp);
        void AlterFP_MasterReport();
        void AlterHP_MasterReport();
        void AlterMode_MasterReport(char byMode);
        bool Attack(unsigned int skill);
        int AttackableHeight();
        CAnimus();
        void ctor_CAnimus();
        void CalcAttExp(struct CAttack* pAT);
        void CalcDefExp(struct CCharacter* pAttackObj, int nDamage);
        void ChangeMode(unsigned int mode);
        void ChangeMode_MasterCommand(int nMode);
        bool ChangeTarget_MasterCommand(struct CCharacter* pTarget);
        void CheckPosInTown();
        bool Create(struct _animus_create_setdata* pData);
        bool Destroy();
        int GetAttackDP();
        int GetAttackPart();
        float GetAttackRange();
        int GetDefFC(int nAttactPart, struct CCharacter* pAttChar, int* pnConvertPart);
        float GetDefFacing(int nPart);
        float GetDefGap(int nPart);
        int GetDefSkill(bool bBackAttackDamage);
        int GetFireTol();
        int GetGenAttackProb(struct CCharacter* pDst, int nPart, bool bBackAttack);
        int GetHP();
        int GetLevel();
        int GetMaxHP();
        char GetMaxLevel();
        bool GetMoveTarget(struct CCharacter* target, float fMoveSpeed, char byMoveMode);
        static unsigned int GetNewMonSerial();
        char* GetObjName();
        int GetObjRace();
        int GetSoilTol();
        void GetTarget();
        int GetWaterTol();
        float GetWeaponAdjust();
        int GetWeaponClass();
        float GetWidth();
        int GetWindTol();
        bool Heal(unsigned int skill);
        bool Init(struct _object_id* pID);
        bool IsBeAttackedAble(bool bFirst);
        bool IsInTown();
        bool IsValidTarget();
        void LifeTimeCheck();
        void Loop();
        void MasterAttack_MasterInform(struct CCharacter* pDst);
        void MasterBeAttacked_MasterInform(struct CCharacter* pDst);
        void OutOfSec();
        void Process();
        void RecvKillMessage(struct CCharacter* pDier);
        void Return_MasterRequest(char byReturnType);
        bool RobbedHP(struct CCharacter* pDst, int nDecHP);
        struct CCharacter* SearchNearEnemy();
        struct CCharacter* SearchNearPlayerAttack();
        void SendMsg_AnimusActHealInform(unsigned int dwDstSerial, int nAddHP);
        void SendMsg_Attack_Gen(struct CAttack* pAT);
        void SendMsg_Create();
        void SendMsg_Destroy();
        void SendMsg_FixPosition(int n);
        void SendMsg_LevelUp();
        void SendMsg_Move();
        void SendMsg_RealMovePoint(int n);
        int SetDamage(int nDam, struct CCharacter* pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn);
        static bool SetStaticMember();
        void TransPoToMaster();
        void _ProcComsumeMaterFP();
        void make_gen_attack_param(struct CCharacter* pDst, char byPart, struct _attack_param* pAP, int nSkillIndex);
        ~CAnimus();
        void dtor_CAnimus();
    };
END_ATF_NAMESPACE

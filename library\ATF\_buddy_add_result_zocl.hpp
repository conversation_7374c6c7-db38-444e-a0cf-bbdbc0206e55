// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _buddy_add_result_zocl
    {
        char byRetCode;
        bool bAccept;
        unsigned int dwAskerSerial;
        unsigned __int16 wAdderIndex;
        unsigned int dwAdderSerial;
        char wszAdderName[17];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <Cmd.hpp>
#include <ElectProcessor.hpp>
#include <_pt_result_fcandidacy_list_zocl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CandidateRegister : ElectProcessor
    {
        char _byPtType[2];
        _pt_result_fcandidacy_list_zocl _kSend[3];
        bool _bInitCandidate;
    public:
        CandidateRegister();
        void ctor_CandidateRegister();
        int Doit(Cmd eCmd, struct CPlayer* pOne, char* pdata);
        bool Initialize();
        bool _AddToPacket(struct CPlayer* pOne, unsigned int dwWinCnt);
        int _CheckPlayerInfo(struct CPlayer* pOne);
        void _InitCandidate();
        int _Regist(struct CPlayer* pOne, char* pdata);
        int _SendList(uint16_t wSock, char byRace);
        void _SortCandidacyByPvpPoint(char byRace);
        void _UpdatePacketWin(char byRace, char* wszName, unsigned int dwWinCnt);
        ~CandidateRegister();
        void dtor_CandidateRegister();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

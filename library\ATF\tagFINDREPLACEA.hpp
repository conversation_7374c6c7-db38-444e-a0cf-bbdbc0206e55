// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    struct tagFINDREPLACEA
    {
        unsigned int lStructSize;
        HWND__ *hwndOwner;
        HINSTANCE__ *hInstance;
        unsigned int Flags;
        char *lpstrFindWhat;
        char *lpstrReplaceWith;
        unsigned __int16 wFindWhatLen;
        unsigned __int16 wReplaceWithLen;
        __int64 lCustData;
        unsigned __int64 (WINAPIV *lpfnHook)(HWND__ *, unsigned int, unsigned __int64, __int64);
        const char *lpTemplateName;
    };
END_ATF_NAMESPACE

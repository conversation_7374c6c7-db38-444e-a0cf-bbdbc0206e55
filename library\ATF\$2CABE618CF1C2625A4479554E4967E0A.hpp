// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $2CABE618CF1C2625A4479554E4967E0A
    {
        BYTE gap0[8];
        long double *pdblVal;
    };    
    static_assert(ATF::checkSize<$2CABE618CF1C2625A4479554E4967E0A, 16>(), "$2CABE618CF1C2625A4479554E4967E0A");
END_ATF_NAMESPACE

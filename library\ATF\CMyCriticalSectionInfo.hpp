// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyCriticalSection.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMyCriticalSectionctor_CMyCriticalSection2_ptr = void (WINAPIV*)(struct CMyCriticalSection*);
        using CMyCriticalSectionctor_CMyCriticalSection2_clbk = void (WINAPIV*)(struct CMyCriticalSection*, CMyCriticalSectionctor_CMyCriticalSection2_ptr);
        using CMyCriticalSectionLock4_ptr = void (WINAPIV*)(struct CMyCriticalSection*);
        using CMyCriticalSectionLock4_clbk = void (WINAPIV*)(struct CMyCriticalSection*, CMyCriticalSectionLock4_ptr);
        using CMyCriticalSectionUnlock6_ptr = void (WINAPIV*)(struct CMyCriticalSection*);
        using CMyCriticalSectionUnlock6_clbk = void (WINAPIV*)(struct CMyCriticalSection*, CMyCriticalSectionUnlock6_ptr);
        
        using CMyCriticalSectiondtor_CMyCriticalSection11_ptr = void (WINAPIV*)(struct CMyCriticalSection*);
        using CMyCriticalSectiondtor_CMyCriticalSection11_clbk = void (WINAPIV*)(struct CMyCriticalSection*, CMyCriticalSectiondtor_CMyCriticalSection11_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

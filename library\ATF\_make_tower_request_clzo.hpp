// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _make_tower_request_clzo
    {
        struct __material
        {
            unsigned __int16 wItemSerial;
            char byMaterSlotIndex;
            char byAmount;
        };
        unsigned __int16 wSkillIndex;
        __int16 zPos[3];
        unsigned __int16 wTowerItemSerial;
        unsigned __int16 wConsumeItemSerial[3];
        char byMaterialNum;
        __material Material[30];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

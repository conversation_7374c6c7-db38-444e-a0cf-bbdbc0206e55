// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetSocket.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CNetSocketAccept_Client2_ptr = bool (WINAPIV*)(struct CNetSocket*, unsigned int);
        using CNetSocketAccept_Client2_clbk = bool (WINAPIV*)(struct CNetSocket*, unsigned int, CNetSocketAccept_Client2_ptr);
        using CNetSocketAccept_Server4_ptr = unsigned int (WINAPIV*)(struct CNetSocket*);
        using CNetSocketAccept_Server4_clbk = unsigned int (WINAPIV*)(struct CNetSocket*, CNetSocketAccept_Server4_ptr);
        
        using CNetSocketctor_CNetSocket6_ptr = void (WINAPIV*)(struct CNetSocket*);
        using CNetSocketctor_CNetSocket6_clbk = void (WINAPIV*)(struct CNetSocket*, CNetSocketctor_CNetSocket6_ptr);
        using CNetSocketCloseAll8_ptr = void (WINAPIV*)(struct CNetSocket*);
        using CNetSocketCloseAll8_clbk = void (WINAPIV*)(struct CNetSocket*, CNetSocketCloseAll8_ptr);
        using CNetSocketCloseSocket10_ptr = bool (WINAPIV*)(struct CNetSocket*, unsigned int);
        using CNetSocketCloseSocket10_clbk = bool (WINAPIV*)(struct CNetSocket*, unsigned int, CNetSocketCloseSocket10_ptr);
        using CNetSocketConnect12_ptr = int (WINAPIV*)(struct CNetSocket*, unsigned int, struct sockaddr_in*);
        using CNetSocketConnect12_clbk = int (WINAPIV*)(struct CNetSocket*, unsigned int, struct sockaddr_in*, CNetSocketConnect12_ptr);
        using CNetSocketEmptySocketBuffer14_ptr = void (WINAPIV*)(struct CNetSocket*, unsigned int);
        using CNetSocketEmptySocketBuffer14_clbk = void (WINAPIV*)(struct CNetSocket*, unsigned int, CNetSocketEmptySocketBuffer14_ptr);
        using CNetSocketFindEmptySocket16_ptr = unsigned int (WINAPIV*)(struct CNetSocket*);
        using CNetSocketFindEmptySocket16_clbk = unsigned int (WINAPIV*)(struct CNetSocket*, CNetSocketFindEmptySocket16_ptr);
        using CNetSocketGetSocket18_ptr = struct _socket* (WINAPIV*)(struct CNetSocket*, unsigned int);
        using CNetSocketGetSocket18_clbk = struct _socket* (WINAPIV*)(struct CNetSocket*, unsigned int, CNetSocketGetSocket18_ptr);
        using CNetSocketGetSocketIPAddress20_ptr = unsigned int (WINAPIV*)(struct CNetSocket*, unsigned int);
        using CNetSocketGetSocketIPAddress20_clbk = unsigned int (WINAPIV*)(struct CNetSocket*, unsigned int, CNetSocketGetSocketIPAddress20_ptr);
        using CNetSocketGetSocketType22_ptr = struct _SOCK_TYPE_PARAM* (WINAPIV*)(struct CNetSocket*);
        using CNetSocketGetSocketType22_clbk = struct _SOCK_TYPE_PARAM* (WINAPIV*)(struct CNetSocket*, CNetSocketGetSocketType22_ptr);
        using CNetSocketGetTotalCount24_ptr = struct _total_count* (WINAPIV*)(struct CNetSocket*);
        using CNetSocketGetTotalCount24_clbk = struct _total_count* (WINAPIV*)(struct CNetSocket*, CNetSocketGetTotalCount24_ptr);
        using CNetSocketInitAcceptSocket26_ptr = bool (WINAPIV*)(struct CNetSocket*, char*);
        using CNetSocketInitAcceptSocket26_clbk = bool (WINAPIV*)(struct CNetSocket*, char*, CNetSocketInitAcceptSocket26_ptr);
        using CNetSocketOnLoop28_ptr = void (WINAPIV*)(struct CNetSocket*);
        using CNetSocketOnLoop28_clbk = void (WINAPIV*)(struct CNetSocket*, CNetSocketOnLoop28_ptr);
        using CNetSocketPushIPCheckList30_ptr = bool (WINAPIV*)(struct CNetSocket*, unsigned int);
        using CNetSocketPushIPCheckList30_clbk = bool (WINAPIV*)(struct CNetSocket*, unsigned int, CNetSocketPushIPCheckList30_ptr);
        using CNetSocketRecv32_ptr = bool (WINAPIV*)(struct CNetSocket*, unsigned int, char*, int, int*);
        using CNetSocketRecv32_clbk = bool (WINAPIV*)(struct CNetSocket*, unsigned int, char*, int, int*, CNetSocketRecv32_ptr);
        using CNetSocketRelease34_ptr = void (WINAPIV*)(struct CNetSocket*);
        using CNetSocketRelease34_clbk = void (WINAPIV*)(struct CNetSocket*, CNetSocketRelease34_ptr);
        using CNetSocketSend36_ptr = bool (WINAPIV*)(struct CNetSocket*, unsigned int, char*, int, int*);
        using CNetSocketSend36_clbk = bool (WINAPIV*)(struct CNetSocket*, unsigned int, char*, int, int*, CNetSocketSend36_ptr);
        using CNetSocketSetSocket38_ptr = bool (WINAPIV*)(struct CNetSocket*, struct _SOCK_TYPE_PARAM*, char*);
        using CNetSocketSetSocket38_clbk = bool (WINAPIV*)(struct CNetSocket*, struct _SOCK_TYPE_PARAM*, char*, CNetSocketSetSocket38_ptr);
        
        using CNetSocketdtor_CNetSocket43_ptr = void (WINAPIV*)(struct CNetSocket*);
        using CNetSocketdtor_CNetSocket43_clbk = void (WINAPIV*)(struct CNetSocket*, CNetSocketdtor_CNetSocket43_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

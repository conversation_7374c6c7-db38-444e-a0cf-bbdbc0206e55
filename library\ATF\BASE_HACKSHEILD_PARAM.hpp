// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <BASE_HACKSHEILD_PARAMVtbl.hpp>


START_ATF_NAMESPACE
    struct BASE_HACKSHEILD_PARAM
    {
        BASE_HACKSHEILD_PARAMVtbl *vfptr;
    public:
        BASE_HACKSHEILD_PARAM();
        void ctor_BASE_HACKSHEILD_PARAM();
    };
END_ATF_NAMESPACE

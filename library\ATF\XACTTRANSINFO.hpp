// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <BOID.hpp>


START_ATF_NAMESPACE
    struct XACTTRANSINFO
    {
        BOID uow;
        int isoLevel;
        unsigned int isoFlags;
        unsigned int grfTCSupported;
        unsigned int grfRMSupported;
        unsigned int grfTCSupportedRetaining;
        unsigned int grfRMSupportedRetaining;
    };
END_ATF_NAMESPACE

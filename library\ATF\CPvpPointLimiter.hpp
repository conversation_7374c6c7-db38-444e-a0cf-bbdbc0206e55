// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CPvpPointLimiter
    {
        struct _PVPPOINT_LIMIT_DB_BASE *m_pkInfo;
    public:
        CPvpPointLimiter();
        void ctor_CPvpPointLimiter();
        void CheatUpdate(long double dOriginalPvpPoint);
        void Clear(int64_t tUpdateTime, long double dOriginalPvpPoint, struct CPlayer* pkSelf);
        bool Set(long double dOriginalPvpPoint, struct _PVPPOINT_LIMIT_DB_BASE* pkInfo);
        bool TakePvpPoint(long double* dPvpPoint, struct CPlayer* pkSelf, struct CPlayer* pkDest);
        void Update(int64_t tUpdateTime, long double dOriginalPvpPoint, long double dUsePoint, bool bUseUp);
        ~CPvpPointLimiter();
        void dtor_CPvpPointLimiter();
    };    
    static_assert(ATF::checkSize<CPvpPointLimiter, 8>(), "CPvpPointLimiter");
END_ATF_NAMESPACE

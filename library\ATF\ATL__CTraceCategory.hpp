// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct CTraceCategory
        {
            unsigned __int64 m_dwCategory;
        public:
            CTraceCategory(char* pszCategoryName, unsigned int nStartingLevel);
            void ctor_CTraceCategory(char* pszCategoryName, unsigned int nStartingLevel);
        };    
        static_assert(ATF::checkSize<ATL::CTraceCategory, 8>(), "ATL::CTraceCategory");
    }; // end namespace ATL
END_ATF_NAMESPACE

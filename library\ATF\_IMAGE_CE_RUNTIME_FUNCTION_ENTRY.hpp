// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_CE_RUNTIME_FUNCTION_ENTRY
    {
        unsigned int FuncStart;
        unsigned __int32 PrologLen : 8;
        unsigned __int32 FuncLen : 22;
        unsigned __int32 ThirtyTwoBit : 1;
        unsigned __int32 ExceptionFlag : 1;
    };
END_ATF_NAMESPACE

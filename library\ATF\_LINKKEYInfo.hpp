// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LINKKEY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _LINKKEYCovDBKey2_ptr = int16_t (WINAPIV*)(struct _LINKKEY*);
        using _LINKKEYCovDBKey2_clbk = int16_t (WINAPIV*)(struct _LINKKEY*, _LINKKEYCovDBKey2_ptr);
        using _LINKKEYGetCode4_ptr = uint16_t (WINAPIV*)(struct _LINKKEY*);
        using _LINKKEYGetCode4_clbk = uint16_t (WINAPIV*)(struct _LINKKEY*, _LINKKEYGetCode4_ptr);
        using _LINKKEYGetIndex6_ptr = uint16_t (WINAPIV*)(struct _LINKKEY*);
        using _LINKKEYGetIndex6_clbk = uint16_t (WINAPIV*)(struct _LINKKEY*, _LINKKEYGetIndex6_ptr);
        using _LINKKEYIsFilled8_ptr = bool (WINAPIV*)(struct _LINKKEY*);
        using _LINKKEYIsFilled8_clbk = bool (WINAPIV*)(struct _LINKKEY*, _LINKKEYIsFilled8_ptr);
        using _LINKKEYLoadDBKey10_ptr = void (WINAPIV*)(struct _LINKKEY*, int16_t);
        using _LINKKEYLoadDBKey10_clbk = void (WINAPIV*)(struct _LINKKEY*, int16_t, _LINKKEYLoadDBKey10_ptr);
        using _LINKKEYSetData12_ptr = void (WINAPIV*)(struct _LINKKEY*, uint16_t, uint16_t);
        using _LINKKEYSetData12_clbk = void (WINAPIV*)(struct _LINKKEY*, uint16_t, uint16_t, _LINKKEYSetData12_ptr);
        using _LINKKEYSetRelease14_ptr = void (WINAPIV*)(struct _LINKKEY*);
        using _LINKKEYSetRelease14_clbk = void (WINAPIV*)(struct _LINKKEY*, _LINKKEYSetRelease14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

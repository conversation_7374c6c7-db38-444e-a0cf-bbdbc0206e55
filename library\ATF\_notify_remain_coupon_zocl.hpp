// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _notify_remain_coupon_zocl
    {
        char byR<PERSON>inCoupon;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_notify_remain_coupon_zocl, 1>(), "_notify_remain_coupon_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $E898A5260D8380431CCAA47FCC291563
    {
        BYTE gap0[8];
        char cVal;
    };    
    static_assert(ATF::checkSize<$E898A5260D8380431CCAA47FCC291563, 9>(), "$E898A5260D8380431CCAA47FCC291563");
END_ATF_NAMESPACE

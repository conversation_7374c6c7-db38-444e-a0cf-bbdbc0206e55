// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_enter_lobby_report_wrac.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _enter_lobby_report_wracsize2_ptr = int (WINAPIV*)(struct _enter_lobby_report_wrac*);
        using _enter_lobby_report_wracsize2_clbk = int (WINAPIV*)(struct _enter_lobby_report_wrac*, _enter_lobby_report_wracsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

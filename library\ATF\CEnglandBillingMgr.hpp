// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <HINSTANCE__.hpp>
#include <TaskPool.hpp>
#include <_param_cash_select.hpp>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    struct CEnglandBillingMgr
    {
        CLogFile m_logBill;
        HINSTANCE__ *m_hDll;
        TaskPool *m_pkPool;
    public:
        CEnglandBillingMgr();
        void ctor_CEnglandBillingMgr();
        int CallFunc_Item_Buy(struct _param_cash_update* rParam, int n, int nIdx);
        int CallFunc_RFOnline_Auth(struct _param_cash_select* rParam, int nIdx);
        bool Free();
        bool MakeConnectionThread();
        void SetPoolPointer(struct TaskPool* lpPointer);
        ~CEnglandBillingMgr();
        void dtor_CEnglandBillingMgr();
    };
END_ATF_NAMESPACE

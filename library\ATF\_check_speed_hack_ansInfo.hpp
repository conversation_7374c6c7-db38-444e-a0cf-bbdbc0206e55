// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_check_speed_hack_ans.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _check_speed_hack_anssize2_ptr = int (WINAPIV*)(struct _check_speed_hack_ans*);
        using _check_speed_hack_anssize2_clbk = int (WINAPIV*)(struct _check_speed_hack_ans*, _check_speed_hack_anssize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

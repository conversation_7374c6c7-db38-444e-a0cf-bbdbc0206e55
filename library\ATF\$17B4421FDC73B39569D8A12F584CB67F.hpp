// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $17B4421FDC73B39569D8A12F584CB67F
    {
        BYTE gap0[8];
        void *pvRecord;
    };    
    static_assert(ATF::checkSize<$17B4421FDC73B39569D8A12F584CB67F, 16>(), "$17B4421FDC73B39569D8A12F584CB67F");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CFrameRate.hpp>
#include <CMyTimer.hpp>
#include <CNetIndexList.hpp>
#include <_QUEST_DB_BASE.hpp>


START_ATF_NAMESPACE
    struct CMgrAvatorQuestHistory
    {
        struct __LOG_DATA
        {
            char szFileName[64];
            int nLen;
            char sData[300];
        };
        char m_szStdPath[128];
        unsigned int m_dwLastLocalDate;
        unsigned int m_dwLastLocalHour;
        CMyTimer m_tmrUpdateTime;
        char m_szCurTime[32];
        __LOG_DATA m_LogData[2532];
        CNetIndexList m_listLogData;
        CNetIndexList m_listLogDataEmpty;
        bool m_bIOThread;
        CFrameRate m_FrameRate;
    public:
        CMgrAvatorQuestHistory();
        void ctor_CMgrAvatorQuestHistory();
        void GetNewFileName(unsigned int dwAvatorSerial, char* pszFileName);
        int GetTotalWaitSize();
        static void IOThread(void* pv);
        void OnLoop();
        void WriteFile(char* pszFileName, char* pszLog);
        void char_copy(char* pszDstName, unsigned int dwDstSerial, char* pszFileName);
        void complete_quest(int nSlot, char* pszQuestCode, char* pszFileName);
        void fail_quest(int nSlot, char* pszQuestCode, char* pszFailCode, char* pszFileName);
        void init_quest(char* pszAvatorName, struct _QUEST_DB_BASE* pQuestDB, char* pszFileName);
        void insert_quest(int nSlot, char* pszQuestCode, char* pszFileName);
        ~CMgrAvatorQuestHistory();
        void dtor_CMgrAvatorQuestHistory();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_COFF_SYMBOLS_HEADER
    {
        unsigned int NumberOfSymbols;
        unsigned int LvaToFirstSymbol;
        unsigned int NumberOfLinenumbers;
        unsigned int LvaToFirstLinenumber;
        unsigned int RvaToFirstByteOfCode;
        unsigned int RvaToLastByteOfCode;
        unsigned int RvaToFirstByteOfData;
        unsigned int RvaToLastByteOfData;
    };
END_ATF_NAMESPACE

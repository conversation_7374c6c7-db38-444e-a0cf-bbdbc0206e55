// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CHRID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _itembox_create_zocl
    {
        char byItemTableCode;
        unsigned __int16 wItemRecIndex;
        char byAmount;
        unsigned __int16 wBoxIndex;
        unsigned int dwOwerSerial;
        _CHRID idDumber;
        char byState;
        __int16 zPos[3];
        char byThrowerRace;
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_itembox_create_zocl, 0x19>(), "_itembox_create_zocl");
END_ATF_NAMESPACE

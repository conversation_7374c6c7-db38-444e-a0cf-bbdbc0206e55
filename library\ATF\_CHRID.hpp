// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _CHRID
    {
        char byID;
        unsigned __int16 wIndex;
        unsigned int dwSerial;
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_CHRID, 7>(), "_CHRID");
END_ATF_NAMESPACE

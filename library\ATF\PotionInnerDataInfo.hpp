// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <PotionInnerData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using PotionInnerDataInit2_ptr = void (WINAPIV*)(struct PotionInnerData*);
        using PotionInnerDataInit2_clbk = void (WINAPIV*)(struct PotionInnerData*, PotionInnerDataInit2_ptr);
        
        using PotionInnerDatactor_PotionInnerData4_ptr = void (WINAPIV*)(struct PotionInnerData*);
        using PotionInnerDatactor_PotionInnerData4_clbk = void (WINAPIV*)(struct PotionInnerData*, PotionInnerDatactor_PotionInnerData4_ptr);
        
        using PotionInnerDatadtor_PotionInnerData6_ptr = void (WINAPIV*)(struct PotionInnerData*);
        using PotionInnerDatadtor_PotionInnerData6_clbk = void (WINAPIV*)(struct PotionInnerData*, PotionInnerDatadtor_PotionInnerData6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

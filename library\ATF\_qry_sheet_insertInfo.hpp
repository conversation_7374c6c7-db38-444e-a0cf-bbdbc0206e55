// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_sheet_insert.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_sheet_insertctor__qry_sheet_insert2_ptr = void (WINAPIV*)(struct _qry_sheet_insert*);
        using _qry_sheet_insertctor__qry_sheet_insert2_clbk = void (WINAPIV*)(struct _qry_sheet_insert*, _qry_sheet_insertctor__qry_sheet_insert2_ptr);
        using _qry_sheet_insertsize4_ptr = int (WINAPIV*)(struct _qry_sheet_insert*);
        using _qry_sheet_insertsize4_clbk = int (WINAPIV*)(struct _qry_sheet_insert*, _qry_sheet_insertsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

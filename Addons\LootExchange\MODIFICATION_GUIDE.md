# Loot Exchange Addon Modifications

## Overview
The modified loot exchange addon now supports exchanging items for **all money types** instead of just processing points. This provides much more flexibility in how loot is converted to currency.

## Key Changes Made

### 1. Enhanced Money Type Support
The original addon only exchanged items that had the "processing_point" flag (bit 4) set. The modified version:

- **When `exchange_all = false`**: Works exactly like the original (only processing points)
- **When `exchange_all = true`**: Exchanges items for ANY valid money type they support

### 2. Priority-Based Money Type Selection
When `exchange_all = true`, the addon tries money types in this priority order:
1. **CP (Dalant)** - Main game currency
2. **Gold** - Secondary currency  
3. **PvP Point** - PvP currency
4. **PvP Point 2** - PvP cash bag
5. **Processing Point** - Crafting currency
6. **Hunter Point** - Hunting currency
7. **Gold Point** - Special currency

The first valid money type found for an item will be used.

### 3. Improved Logic Flow
```cpp
// NEW: Check all money types when exchange_all is enabled
if (CLootExchange::m_bExchangeAll)
{
    // Try all money types in priority order
    for (int i = 0; i < static_cast<int>(e_money_type::num); ++i)
    {
        if (bMoneyType.test(i))
        {
            nSelectedMoneyType = i;
            bFoundValidMoneyType = true;
            break; // Use the first valid money type found
        }
    }
}
else
{
    // Original behavior: only processing_point
    if (bMoneyType.test(static_cast<int>(e_money_type::processing_point)))
    {
        nSelectedMoneyType = static_cast<int>(e_money_type::processing_point);
        bFoundValidMoneyType = true;
    }
}
```

## Configuration Options

### Current Configuration (Disabled)
```json
{
  "name": "addon.loot_exchange",
  "config": {
    "activated": false,
    "exchange_all": false
  }
}
```

### Recommended Configurations

#### Option 1: Conservative (Processing Points Only)
```json
{
  "name": "addon.loot_exchange", 
  "config": {
    "activated": true,
    "exchange_all": false
  }
}
```
- Only exchanges items that have processing point values
- Safest option, maintains original behavior

#### Option 2: Full Exchange (All Money Types)
```json
{
  "name": "addon.loot_exchange",
  "config": {
    "activated": true, 
    "exchange_all": true
  }
}
```
- Exchanges items for any currency they support
- Maximum convenience for players
- Uses priority system (CP > Gold > PvP > etc.)

## Benefits of the Modification

### 1. **Flexibility**
- Players can now exchange items for the most appropriate currency type
- No longer limited to just processing points

### 2. **Better Economy Integration**
- Items exchange for their intended currency types
- Maintains game balance by using original item values

### 3. **Backward Compatibility**
- Original behavior preserved when `exchange_all = false`
- No breaking changes to existing configurations

### 4. **Smart Priority System**
- Prioritizes main currencies (CP, Gold) over specialized ones
- Ensures consistent behavior across different item types

## Implementation Steps

### 1. Replace the Original File
```bash
# Backup original
cp Addons/LootExchange/LootExchange.cpp Addons/LootExchange/LootExchange.cpp.backup

# Replace with modified version
cp Addons/LootExchange/LootExchange_Modified.cpp Addons/LootExchange/LootExchange.cpp
```

### 2. Update Configuration
Edit `YorozuyaGS/Configuration/global.json`:
```json
{
  "name": "addon.loot_exchange",
  "config": {
    "activated": true,
    "exchange_all": true  // Set to true for all money types
  }
}
```

### 3. Rebuild and Test
1. Rebuild the LootExchange project
2. Test with different item types
3. Verify currency is added correctly

## Testing Recommendations

### Test Cases
1. **Equipment Items**: Should exchange for CP/Gold
2. **Resources**: Should exchange for appropriate race-specific currency
3. **Special Items**: Should exchange for their designated currency type
4. **Stackable Items**: Should multiply value by quantity

### Verification
- Check player currency increases match item values
- Ensure no items are lost during exchange
- Verify different item types use appropriate currencies
- Test both `exchange_all = true` and `exchange_all = false` modes

## Troubleshooting

### Common Issues
1. **Items not exchanging**: Check if `activated = true` in config
2. **Wrong currency type**: Verify item has valid money type flags
3. **Zero value items**: Items with no monetary value won't exchange

### Debug Tips
- Enable logging to track which money types are detected
- Check item database for money type flags
- Verify player has space for currency (if applicable)

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _force_download_result_zocl
    {
        struct  _list
        {
            unsigned __int16 wItemIndex;
            unsigned int dwCum;
            char byCsMethod;
            unsigned int dwT;
        };
        char byRetCode;
        char bySlotNum;
        _list ItemSlotInfo[88];
    public:
        _force_download_result_zocl();
        void ctor__force_download_result_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_force_download_result_zocl, 970>(), "_force_download_result_zocl");
END_ATF_NAMESPACE

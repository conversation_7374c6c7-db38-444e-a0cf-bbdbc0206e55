// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    struct tagHELPINFO
    {
        unsigned int cbSize;
        int iContextType;
        int iCtrlId;
        void *hItemHandle;
        unsigned __int64 dwContextId;
        tagPOINT MousePos;
    };
END_ATF_NAMESPACE

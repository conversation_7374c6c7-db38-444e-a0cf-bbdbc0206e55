// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleRewardItemManager.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattleRewardItemManagerctor_CGuildBattleRewardItemManager2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItemManager*);
            using GUILD_BATTLE__CGuildBattleRewardItemManagerctor_CGuildBattleRewardItemManager2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItemManager*, GUILD_BATTLE__CGuildBattleRewardItemManagerctor_CGuildBattleRewardItemManager2_ptr);
            using GUILD_BATTLE__CGuildBattleRewardItemManagerGive4_ptr = struct GUILD_BATTLE::CGuildBattleRewardItem* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItemManager*, struct CPlayer*);
            using GUILD_BATTLE__CGuildBattleRewardItemManagerGive4_clbk = struct GUILD_BATTLE::CGuildBattleRewardItem* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItemManager*, struct CPlayer*, GUILD_BATTLE__CGuildBattleRewardItemManagerGive4_ptr);
            using GUILD_BATTLE__CGuildBattleRewardItemManagerInit6_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItemManager*);
            using GUILD_BATTLE__CGuildBattleRewardItemManagerInit6_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItemManager*, GUILD_BATTLE__CGuildBattleRewardItemManagerInit6_ptr);
            using GUILD_BATTLE__CGuildBattleRewardItemManagerInstance8_ptr = struct GUILD_BATTLE::CGuildBattleRewardItemManager* (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleRewardItemManagerInstance8_clbk = struct GUILD_BATTLE::CGuildBattleRewardItemManager* (WINAPIV*)(GUILD_BATTLE__CGuildBattleRewardItemManagerInstance8_ptr);
            
            using GUILD_BATTLE__CGuildBattleRewardItemManagerdtor_CGuildBattleRewardItemManager10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItemManager*);
            using GUILD_BATTLE__CGuildBattleRewardItemManagerdtor_CGuildBattleRewardItemManager10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRewardItemManager*, GUILD_BATTLE__CGuildBattleRewardItemManagerdtor_CGuildBattleRewardItemManager10_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

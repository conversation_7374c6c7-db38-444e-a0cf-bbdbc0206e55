// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CSetItemType.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CSetItemTypector_CSetItemType2_ptr = void (WINAPIV*)(struct CSetItemType*);
        using CSetItemTypector_CSetItemType2_clbk = void (WINAPIV*)(struct CSetItemType*, CSetItemTypector_CSetItemType2_ptr);
        using CSetItemTypeClass_Init4_ptr = void (WINAPIV*)(struct CSetItemType*);
        using CSetItemTypeClass_Init4_clbk = void (WINAPIV*)(struct CSetItemType*, CSetItemTypeClass_Init4_ptr);
        using CSetItemTypeGetEffectTypeCount6_ptr = int (WINAPIV*)(struct CSetItemType*);
        using CSetItemTypeGetEffectTypeCount6_clbk = int (WINAPIV*)(struct CSetItemType*, CSetItemTypeGetEffectTypeCount6_ptr);
        using CSetItemTypeGetsi_interpret8_ptr = struct si_interpret* (WINAPIV*)(struct CSetItemType*, int);
        using CSetItemTypeGetsi_interpret8_clbk = struct si_interpret* (WINAPIV*)(struct CSetItemType*, int, CSetItemTypeGetsi_interpret8_ptr);
        using CSetItemTypeSetItemType_Init10_ptr = bool (WINAPIV*)(struct CSetItemType*, struct CRecordData*);
        using CSetItemTypeSetItemType_Init10_clbk = bool (WINAPIV*)(struct CSetItemType*, struct CRecordData*, CSetItemTypeSetItemType_Init10_ptr);
        using CSetItemTypeSetItemType_UnInit12_ptr = bool (WINAPIV*)(struct CSetItemType*);
        using CSetItemTypeSetItemType_UnInit12_clbk = bool (WINAPIV*)(struct CSetItemType*, CSetItemTypeSetItemType_UnInit12_ptr);
        
        using CSetItemTypedtor_CSetItemType14_ptr = void (WINAPIV*)(struct CSetItemType*);
        using CSetItemTypedtor_CSetItemType14_clbk = void (WINAPIV*)(struct CSetItemType*, CSetItemTypedtor_CSetItemType14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

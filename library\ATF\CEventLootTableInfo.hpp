// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CEventLootTable.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CEventLootTableAddRecord2_ptr = void (WINAPIV*)(struct CEventLootTable*, struct CEventLootTable::_event_drop*);
        using CEventLootTableAddRecord2_clbk = void (WINAPIV*)(struct CEventLootTable*, struct CEventLootTable::_event_drop*, CEventLootTableAddRecord2_ptr);
        
        using CEventLootTablector_CEventLootTable4_ptr = void (WINAPIV*)(struct CEventLootTable*);
        using CEventLootTablector_CEventLootTable4_clbk = void (WINAPIV*)(struct CEventLootTable*, CEventLootTablector_CEventLootTable4_ptr);
        using CEventLootTableGetRecord6_ptr = struct CEventLootTable::_event_drop* (WINAPIV*)(struct CEventLootTable*, char*);
        using CEventLootTableGetRecord6_clbk = struct CEventLootTable::_event_drop* (WINAPIV*)(struct CEventLootTable*, char*, CEventLootTableGetRecord6_ptr);
        using CEventLootTableReadRecord8_ptr = bool (WINAPIV*)(struct CEventLootTable*);
        using CEventLootTableReadRecord8_clbk = bool (WINAPIV*)(struct CEventLootTable*, CEventLootTableReadRecord8_ptr);
        
        using CEventLootTabledtor_CEventLootTable13_ptr = void (WINAPIV*)(struct CEventLootTable*);
        using CEventLootTabledtor_CEventLootTable13_clbk = void (WINAPIV*)(struct CEventLootTable*, CEventLootTabledtor_CEventLootTable13_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CPartyModeKillMonsterExpNotify
    {
        struct CExpInfo
        {
            float m_fExp;
            struct CPlayer *m_pkMember;
        public:
            CExpInfo();
            void ctor_CExpInfo();
            void Notify();
            void SetGetExp(struct CPlayer* pkMember, float fExp);
            ~CExpInfo();
            void dtor_CExpInfo();
        };
        bool m_bKillMonster;
        char m_byMemberCnt;
        CExpInfo m_kInfo[8];
    public:
        bool Add(struct CPlayer* pkMember, float fExp);
        CPartyModeKillMonsterExpNotify();
        void ctor_CPartyModeKillMonsterExpNotify();
        void Notify();
        void ctor_Notify();
        void SetKillMonsterFlag();
        ~CPartyModeKillMonsterExpNotify();
        void dtor_CPartyModeKillMonsterExpNotify();
    };    
    static_assert(ATF::checkSize<CPartyModeKillMonsterExpNotify, 136>(), "CPartyModeKillMonsterExpNotify");
END_ATF_NAMESPACE

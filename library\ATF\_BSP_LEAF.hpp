// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _BSP_LEAF
    {
        char type;
        unsigned __int16 face_num;
        unsigned int face_start_id;
        unsigned __int16 m_group_num;
        unsigned int m_group_start_id;
        __int16 bb_min[3];
        __int16 bb_max[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

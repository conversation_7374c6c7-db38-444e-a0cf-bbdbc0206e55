// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CIndexBuffer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CIndexBufferInitIndexBuffer1_ptr = void (WINAPIV*)(struct CIndexBuffer*, int, int);
        using CIndexBufferInitIndexBuffer1_clbk = void (WINAPIV*)(struct CIndexBuffer*, int, int, CIndexBufferInitIndexBuffer1_ptr);
        using CIndexBufferReleaseIndexBuffer2_ptr = void (WINAPIV*)(struct CIndexBuffer*);
        using CIndexBufferReleaseIndexBuffer2_clbk = void (WINAPIV*)(struct CIndexBuffer*, CIndexBufferReleaseIndexBuffer2_ptr);
        using CIndexBufferVPLock3_ptr = uint8_t* (WINAPIV*)(struct CIndexBuffer*, int, int, uint32_t);
        using CIndexBufferVPLock3_clbk = uint8_t* (WINAPIV*)(struct CIndexBuffer*, int, int, uint32_t, CIndexBufferVPLock3_ptr);
        using CIndexBufferVPUnLock4_ptr = void (WINAPIV*)(struct CIndexBuffer*);
        using CIndexBufferVPUnLock4_clbk = void (WINAPIV*)(struct CIndexBuffer*, CIndexBufferVPUnLock4_ptr);
        
        using CIndexBufferdtor_CIndexBuffer5_ptr = int64_t (WINAPIV*)(struct CIndexBuffer*);
        using CIndexBufferdtor_CIndexBuffer5_clbk = int64_t (WINAPIV*)(struct CIndexBuffer*, CIndexBufferdtor_CIndexBuffer5_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct DLGITEMTEMPLATEEX
    {
        unsigned int helpID;
        unsigned int exStyle;
        unsigned int style;
        __int16 x;
        __int16 y;
        __int16 cx;
        __int16 cy;
        unsigned int id;
    };
END_ATF_NAMESPACE

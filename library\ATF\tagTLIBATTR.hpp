// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct tagTLIBATTR
    {
        _GUID guid;
        unsigned int lcid;
        tagSYSKIND syskind;
        unsigned __int16 wMajorVerNum;
        unsigned __int16 wMinorVerNum;
        unsigned __int16 wLibFlags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

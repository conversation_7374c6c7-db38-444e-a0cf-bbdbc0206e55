// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSubClassInfoLevel.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderSubClassInfoLevelctor_CUnmannedTraderSubClassInfoLevel2_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*, unsigned int);
        using CUnmannedTraderSubClassInfoLevelctor_CUnmannedTraderSubClassInfoLevel2_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*, unsigned int, CUnmannedTraderSubClassInfoLevelctor_CUnmannedTraderSubClassInfoLevel2_ptr);
        using CUnmannedTraderSubClassInfoLevelCreate4_ptr = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*, unsigned int);
        using CUnmannedTraderSubClassInfoLevelCreate4_clbk = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*, unsigned int, CUnmannedTraderSubClassInfoLevelCreate4_ptr);
        using CUnmannedTraderSubClassInfoLevelGetGroupID6_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*, char, uint16_t, char*);
        using CUnmannedTraderSubClassInfoLevelGetGroupID6_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*, char, uint16_t, char*, CUnmannedTraderSubClassInfoLevelGetGroupID6_ptr);
        using CUnmannedTraderSubClassInfoLevelLoadXML8_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int);
        using CUnmannedTraderSubClassInfoLevelLoadXML8_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int, CUnmannedTraderSubClassInfoLevelLoadXML8_ptr);
        
        using CUnmannedTraderSubClassInfoLeveldtor_CUnmannedTraderSubClassInfoLevel12_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*);
        using CUnmannedTraderSubClassInfoLeveldtor_CUnmannedTraderSubClassInfoLevel12_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfoLevel*, CUnmannedTraderSubClassInfoLeveldtor_CUnmannedTraderSubClassInfoLevel12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

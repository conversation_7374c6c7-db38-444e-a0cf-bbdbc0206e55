// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_fireguard_block_request_wrac.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _fireguard_block_request_wracsize2_ptr = int (WINAPIV*)(struct _fireguard_block_request_wrac*);
        using _fireguard_block_request_wracsize2_clbk = int (WINAPIV*)(struct _fireguard_block_request_wrac*, _fireguard_block_request_wracsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

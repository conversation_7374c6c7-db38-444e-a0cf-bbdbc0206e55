// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CCRTAllocator.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            using ATL__CCRTAllocatorAllocate2_ptr = void* (WINAPIV*)(uint64_t);
            using ATL__CCRTAllocatorAllocate2_clbk = void* (WINAPIV*)(uint64_t, ATL__CCRTAllocatorAllocate2_ptr);
            using ATL__CCRTAllocatorFree4_ptr = void (WINAPIV*)(void*);
            using ATL__CCRTAllocatorFree4_clbk = void (WINAPIV*)(void*, ATL__CCRTAllocatorFree4_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FORCEKEY.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _FORCE_DB_BASE
    {
        struct  _LIST
        {
            _FORCEKEY Key;
            unsigned int dwItemETSerial;
            unsigned __int64 lnUID;
            char byCsMethod;
            unsigned int dwT;
            unsigned int m_dwLendRegdTime;
        public:
            void Init();
            bool Release();
            bool Set(_STORAGE_LIST::_db_con* pItem);
            _LIST();
            void ctor__LIST();
        };
        _LIST m_List[88];
    public:
        void Init();
        _FORCE_DB_BASE();
        void ctor__FORCE_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_FORCE_DB_BASE, 2200>(), "_FORCE_DB_BASE");
END_ATF_NAMESPACE

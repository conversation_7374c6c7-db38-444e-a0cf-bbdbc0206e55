// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGravityStone.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CGravityStonector_CGravityStone2_ptr = void (WINAPIV*)(struct CGravityStone*, uint16_t);
        using CGravityStonector_CGravityStone2_clbk = void (WINAPIV*)(struct CGravityStone*, uint16_t, CGravityStonector_CGravityStone2_ptr);
        using CGravityStoneCheatGet4_ptr = char (WINAPIV*)(struct CGravityStone*, struct CPlayer*);
        using CGravityStoneCheatGet4_clbk = char (WINAPIV*)(struct CGravityStone*, struct CPlayer*, CGravityStoneCheatGet4_ptr);
        using CGravityStoneCheckTakeTimeLimit6_ptr = bool (WINAPIV*)(struct CGravityStone*);
        using CGravityStoneCheckTakeTimeLimit6_clbk = bool (WINAPIV*)(struct CGravityStone*, CGravityStoneCheckTakeTimeLimit6_ptr);
        using CGravityStoneClear8_ptr = void (WINAPIV*)(struct CGravityStone*);
        using CGravityStoneClear8_clbk = void (WINAPIV*)(struct CGravityStone*, CGravityStoneClear8_ptr);
        using CGravityStoneDestroy10_ptr = void (WINAPIV*)(struct CGravityStone*);
        using CGravityStoneDestroy10_clbk = void (WINAPIV*)(struct CGravityStone*, CGravityStoneDestroy10_ptr);
        using CGravityStoneDrop12_ptr = char (WINAPIV*)(struct CGravityStone*, struct CPlayer*);
        using CGravityStoneDrop12_clbk = char (WINAPIV*)(struct CGravityStone*, struct CPlayer*, CGravityStoneDrop12_ptr);
        using CGravityStoneGet14_ptr = char (WINAPIV*)(struct CGravityStone*, uint16_t, unsigned int, struct CPlayer*);
        using CGravityStoneGet14_clbk = char (WINAPIV*)(struct CGravityStone*, uint16_t, unsigned int, struct CPlayer*, CGravityStoneGet14_ptr);
        using CGravityStoneGetOwner16_ptr = struct CPlayer* (WINAPIV*)(struct CGravityStone*);
        using CGravityStoneGetOwner16_clbk = struct CPlayer* (WINAPIV*)(struct CGravityStone*, CGravityStoneGetOwner16_ptr);
        using CGravityStoneGetOwnerCurPos18_ptr = float* (WINAPIV*)(struct CGravityStone*);
        using CGravityStoneGetOwnerCurPos18_clbk = float* (WINAPIV*)(struct CGravityStone*, CGravityStoneGetOwnerCurPos18_ptr);
        using CGravityStoneIsInTown20_ptr = bool (WINAPIV*)(struct CGravityStone*);
        using CGravityStoneIsInTown20_clbk = bool (WINAPIV*)(struct CGravityStone*, CGravityStoneIsInTown20_ptr);
        using CGravityStoneIsNearPosition22_ptr = bool (WINAPIV*)(struct CGravityStone*, float*);
        using CGravityStoneIsNearPosition22_clbk = bool (WINAPIV*)(struct CGravityStone*, float*, CGravityStoneIsNearPosition22_ptr);
        using CGravityStoneIsValidOwner24_ptr = bool (WINAPIV*)(struct CGravityStone*, struct CPlayer*);
        using CGravityStoneIsValidOwner24_clbk = bool (WINAPIV*)(struct CGravityStone*, struct CPlayer*, CGravityStoneIsValidOwner24_ptr);
        using CGravityStoneRegen26_ptr = bool (WINAPIV*)(struct CGravityStone*, struct _object_create_setdata*);
        using CGravityStoneRegen26_clbk = bool (WINAPIV*)(struct CGravityStone*, struct _object_create_setdata*, CGravityStoneRegen26_ptr);
        using CGravityStoneSendMsg_Create28_ptr = void (WINAPIV*)(struct CGravityStone*);
        using CGravityStoneSendMsg_Create28_clbk = void (WINAPIV*)(struct CGravityStone*, CGravityStoneSendMsg_Create28_ptr);
        using CGravityStoneSendMsg_Destroy30_ptr = void (WINAPIV*)(struct CGravityStone*);
        using CGravityStoneSendMsg_Destroy30_clbk = void (WINAPIV*)(struct CGravityStone*, CGravityStoneSendMsg_Destroy30_ptr);
        using CGravityStoneSendMsg_FixPosition32_ptr = void (WINAPIV*)(struct CGravityStone*, int);
        using CGravityStoneSendMsg_FixPosition32_clbk = void (WINAPIV*)(struct CGravityStone*, int, CGravityStoneSendMsg_FixPosition32_ptr);
        using CGravityStoneSetOwner34_ptr = void (WINAPIV*)(struct CGravityStone*, struct CPlayer*);
        using CGravityStoneSetOwner34_clbk = void (WINAPIV*)(struct CGravityStone*, struct CPlayer*, CGravityStoneSetOwner34_ptr);
        
        using CGravityStonedtor_CGravityStone39_ptr = void (WINAPIV*)(struct CGravityStone*);
        using CGravityStonedtor_CGravityStone39_clbk = void (WINAPIV*)(struct CGravityStone*, CGravityStonedtor_CGravityStone39_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

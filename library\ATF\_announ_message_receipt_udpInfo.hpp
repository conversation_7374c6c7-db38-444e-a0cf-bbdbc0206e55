// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_announ_message_receipt_udp.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _announ_message_receipt_udpctor__announ_message_receipt_udp2_ptr = void (WINAPIV*)(struct _announ_message_receipt_udp*);
        using _announ_message_receipt_udpctor__announ_message_receipt_udp2_clbk = void (WINAPIV*)(struct _announ_message_receipt_udp*, _announ_message_receipt_udpctor__announ_message_receipt_udp2_ptr);
        using _announ_message_receipt_udpsize4_ptr = int (WINAPIV*)(struct _announ_message_receipt_udp*);
        using _announ_message_receipt_udpsize4_clbk = int (WINAPIV*)(struct _announ_message_receipt_udp*, _announ_message_receipt_udpsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

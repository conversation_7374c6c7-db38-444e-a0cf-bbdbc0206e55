// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _d_trade_unit_add_inform_zocl
    {
        unsigned __int16 wUnitKeySerial;
        char bySlotIndex;
        char byFrame;
        unsigned int dwGauge;
        char byPart[6];
        unsigned int dwBullet[2];
        unsigned int dwSpare[8];
        int nPullingFee;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

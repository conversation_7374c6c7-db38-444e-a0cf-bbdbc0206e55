// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _COMSTAT
    {
        unsigned __int32 fCtsHold : 1;
        unsigned __int32 fDsrHold : 1;
        unsigned __int32 fRlsdHold : 1;
        unsigned __int32 fXoffHold : 1;
        unsigned __int32 fXoffSent : 1;
        unsigned __int32 fEof : 1;
        unsigned __int32 fTxim : 1;
        unsigned __int32 fReserved : 25;
        unsigned int cbInQue;
        unsigned int cbOutQue;
    };
END_ATF_NAMESPACE

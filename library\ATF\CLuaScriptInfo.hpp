// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaScript.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CLuaScriptctor_CLuaScript2_ptr = void (WINAPIV*)(struct CLuaScript*);
        using CLuaScriptctor_CLuaScript2_clbk = void (WINAPIV*)(struct CLuaScript*, CLuaScriptctor_CLuaScript2_ptr);
        using CLuaScriptGetLuaState4_ptr = lua_State* (WINAPIV*)(struct CLuaScript*);
        using CLuaScriptGetLuaState4_clbk = lua_State* (WINAPIV*)(struct CLuaScript*, CLuaScriptGetLuaState4_ptr);
        using CLuaScriptGetName6_ptr = char* (WINAPIV*)(struct CLuaScript*);
        using CLuaScriptGetName6_clbk = char* (WINAPIV*)(struct CLuaScript*, CLuaScriptGetName6_ptr);
        using CLuaScriptRunCommand8_ptr = bool (WINAPIV*)(struct CLuaScript*, struct CLuaCommand*);
        using CLuaScriptRunCommand8_clbk = bool (WINAPIV*)(struct CLuaScript*, struct CLuaCommand*, CLuaScriptRunCommand8_ptr);
        using CLuaScriptSetName10_ptr = void (WINAPIV*)(struct CLuaScript*, char*);
        using CLuaScriptSetName10_clbk = void (WINAPIV*)(struct CLuaScript*, char*, CLuaScriptSetName10_ptr);
        
        using CLuaScriptdtor_CLuaScript14_ptr = void (WINAPIV*)(struct CLuaScript*);
        using CLuaScriptdtor_CLuaScript14_clbk = void (WINAPIV*)(struct CLuaScript*, CLuaScriptdtor_CLuaScript14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

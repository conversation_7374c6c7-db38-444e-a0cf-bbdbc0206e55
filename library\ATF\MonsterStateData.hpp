// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$F4056BB7EECE38A81B2AD80CF7F3D177.hpp>


START_ATF_NAMESPACE
    struct MonsterStateData
    {
        $F4056BB7EECE38A81B2AD80CF7F3D177 ___u0;
    public:
        uint16_t GetStateChunk();
        MonsterStateData();
        void ctor_MonsterStateData();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IDirectDrawSurface.hpp>
#include <_DDBLTFX.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct _DDBLTBATCH
    {
        tagRECT *lprDest;
        IDirectDrawSurface *lpDDSSrc;
        tagRECT *lprSrc;
        unsigned int dwFlags;
        _DDBLTFX *lpDDBltFx;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_detected_char_list.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _detected_char_listAddCharInfo2_ptr = int (WINAPIV*)(struct _detected_char_list*, char, float*);
        using _detected_char_listAddCharInfo2_clbk = int (WINAPIV*)(struct _detected_char_list*, char, float*, _detected_char_listAddCharInfo2_ptr);
        
        using _detected_char_listctor__detected_char_list4_ptr = void (WINAPIV*)(struct _detected_char_list*);
        using _detected_char_listctor__detected_char_list4_clbk = void (WINAPIV*)(struct _detected_char_list*, _detected_char_listctor__detected_char_list4_ptr);
        using _detected_char_listinit6_ptr = void (WINAPIV*)(struct _detected_char_list*);
        using _detected_char_listinit6_clbk = void (WINAPIV*)(struct _detected_char_list*, _detected_char_listinit6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

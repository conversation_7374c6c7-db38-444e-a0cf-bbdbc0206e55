// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogTypeDBTaskPool.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CLogTypeDBTaskPoolctor_CLogTypeDBTaskPool2_ptr = void (WINAPIV*)(struct CLogTypeDBTaskPool*);
        using CLogTypeDBTaskPoolctor_CLogTypeDBTaskPool2_clbk = void (WINAPIV*)(struct CLogTypeDBTaskPool*, CLogTypeDBTaskPoolctor_CLogTypeDBTaskPool2_ptr);
        using CLogTypeDBTaskPoolDestroy4_ptr = void (WINAPIV*)(struct CLogTypeDBTaskPool*);
        using CLogTypeDBTaskPoolDestroy4_clbk = void (WINAPIV*)(struct CLogTypeDBTaskPool*, CLogTypeDBTaskPoolD<PERSON>roy4_ptr);
        using CLogTypeDBTaskPoolGetComplete6_ptr = struct CLogTypeDBTask* (WINAPIV*)(struct CLogTypeDBTaskPool*);
        using CLogTypeDBTaskPoolGetComplete6_clbk = struct CLogTypeDBTask* (WINAPIV*)(struct CLogTypeDBTaskPool*, CLogTypeDBTaskPoolGetComplete6_ptr);
        using CLogTypeDBTaskPoolGetEmpty8_ptr = struct CLogTypeDBTask* (WINAPIV*)(struct CLogTypeDBTaskPool*);
        using CLogTypeDBTaskPoolGetEmpty8_clbk = struct CLogTypeDBTask* (WINAPIV*)(struct CLogTypeDBTaskPool*, CLogTypeDBTaskPoolGetEmpty8_ptr);
        using CLogTypeDBTaskPoolGetProc10_ptr = struct CLogTypeDBTask* (WINAPIV*)(struct CLogTypeDBTaskPool*);
        using CLogTypeDBTaskPoolGetProc10_clbk = struct CLogTypeDBTask* (WINAPIV*)(struct CLogTypeDBTaskPool*, CLogTypeDBTaskPoolGetProc10_ptr);
        using CLogTypeDBTaskPoolInit12_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskPool*, unsigned int, unsigned int, struct CLogFile*);
        using CLogTypeDBTaskPoolInit12_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskPool*, unsigned int, unsigned int, struct CLogFile*, CLogTypeDBTaskPoolInit12_ptr);
        using CLogTypeDBTaskPoolSetComplete14_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskPool*, struct CLogTypeDBTask*, struct CLogFile*);
        using CLogTypeDBTaskPoolSetComplete14_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskPool*, struct CLogTypeDBTask*, struct CLogFile*, CLogTypeDBTaskPoolSetComplete14_ptr);
        using CLogTypeDBTaskPoolSetEmpty16_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskPool*, struct CLogTypeDBTask*, struct CLogFile*);
        using CLogTypeDBTaskPoolSetEmpty16_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskPool*, struct CLogTypeDBTask*, struct CLogFile*, CLogTypeDBTaskPoolSetEmpty16_ptr);
        using CLogTypeDBTaskPoolSetProc18_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskPool*, struct CLogTypeDBTask*, struct CLogFile*);
        using CLogTypeDBTaskPoolSetProc18_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskPool*, struct CLogTypeDBTask*, struct CLogFile*, CLogTypeDBTaskPoolSetProc18_ptr);
        
        using CLogTypeDBTaskPooldtor_CLogTypeDBTaskPool20_ptr = void (WINAPIV*)(struct CLogTypeDBTaskPool*);
        using CLogTypeDBTaskPooldtor_CLogTypeDBTaskPool20_clbk = void (WINAPIV*)(struct CLogTypeDBTaskPool*, CLogTypeDBTaskPooldtor_CLogTypeDBTaskPool20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonsterHelper.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CMonsterHelperCheckPreAttackRangeTargetAbleCharacter2_ptr = int (WINAPIV*)(struct CMonster*, struct CGameObject*);
        using CMonsterHelperCheckPreAttackRangeTargetAbleCharacter2_clbk = int (WINAPIV*)(struct CMonster*, struct CGameObject*, CMonsterHelperCheckPreAttackRangeTargetAbleCharacter2_ptr);
        using CMonsterHelperGetAngle4_ptr = float (WINAPIV*)(float*, float*);
        using CMonsterHelperGetAngle4_clbk = float (WINAPIV*)(float*, float*, CMonsterHelperGetAngle4_ptr);
        using CMonsterHelperGetDirection6_ptr = void (WINAPIV*)(float**, float**, float**, float);
        using CMonsterHelperGetDirection6_clbk = void (WINAPIV*)(float**, float**, float**, float, CMonsterHelperGetDirection6_ptr);
        using CMonsterHelperHierarcyHelpCast8_ptr = void (WINAPIV*)(struct CMonster*);
        using CMonsterHelperHierarcyHelpCast8_clbk = void (WINAPIV*)(struct CMonster*, CMonsterHelperHierarcyHelpCast8_ptr);
        using CMonsterHelperIsInSector10_ptr = int (WINAPIV*)(float*, float*, float*, float, float, float*);
        using CMonsterHelperIsInSector10_clbk = int (WINAPIV*)(float*, float*, float*, float, float, float*, CMonsterHelperIsInSector10_ptr);
        using CMonsterHelperSearchNearMonster12_ptr = unsigned int (WINAPIV*)(struct CMonster*, struct _NEAR_DATA*, unsigned int, int);
        using CMonsterHelperSearchNearMonster12_clbk = unsigned int (WINAPIV*)(struct CMonster*, struct _NEAR_DATA*, unsigned int, int, CMonsterHelperSearchNearMonster12_ptr);
        using CMonsterHelperSearchNearMonsterByDistance14_ptr = struct CMonster* (WINAPIV*)(struct CMonster*, unsigned int);
        using CMonsterHelperSearchNearMonsterByDistance14_clbk = struct CMonster* (WINAPIV*)(struct CMonster*, unsigned int, CMonsterHelperSearchNearMonsterByDistance14_ptr);
        using CMonsterHelperSearchNearPlayer16_ptr = struct CPlayer* (WINAPIV*)(struct CMonster*, int);
        using CMonsterHelperSearchNearPlayer16_clbk = struct CPlayer* (WINAPIV*)(struct CMonster*, int, CMonsterHelperSearchNearPlayer16_ptr);
        using CMonsterHelperSearchPatrolMovePos18_ptr = int (WINAPIV*)(struct CMonster*, float**);
        using CMonsterHelperSearchPatrolMovePos18_clbk = int (WINAPIV*)(struct CMonster*, float**, CMonsterHelperSearchPatrolMovePos18_ptr);
        using CMonsterHelperSearchTargetMovePos_MovingTarget20_ptr = int (WINAPIV*)(struct CMonster*, struct CCharacter*, float**);
        using CMonsterHelperSearchTargetMovePos_MovingTarget20_clbk = int (WINAPIV*)(struct CMonster*, struct CCharacter*, float**, CMonsterHelperSearchTargetMovePos_MovingTarget20_ptr);
        using CMonsterHelperSearchTargetMovePos_StopTarget22_ptr = int (WINAPIV*)(struct CMonster*, struct CCharacter*, float**);
        using CMonsterHelperSearchTargetMovePos_StopTarget22_clbk = int (WINAPIV*)(struct CMonster*, struct CCharacter*, float**, CMonsterHelperSearchTargetMovePos_StopTarget22_ptr);
        using CMonsterHelperTransPort24_ptr = void (WINAPIV*)(struct CMonster*, float*);
        using CMonsterHelperTransPort24_clbk = void (WINAPIV*)(struct CMonster*, float*, CMonsterHelperTransPort24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

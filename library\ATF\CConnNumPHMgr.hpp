// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_USER_NUM_SHEET.hpp>


START_ATF_NAMESPACE
    struct CConnNumPHMgr
    {
        struct __cnt_per_h
        {
            int nUserCumPerMin;
            unsigned int dwCounting;
            int nMaxUserNum;
        public:
            int GetAverageUserNumPerHour();
            void Init();
            __cnt_per_h();
            void ctor___cnt_per_h();
        };
        __cnt_per_h m_cph;
        _USER_NUM_SHEET m_LastResult;
        int m_nLastHour;
        unsigned int m_dwCheckLastTime;
    public:
        CConnNumPHMgr();
        void ctor_CConnNumPHMgr();
        struct _USER_NUM_SHEET* Check(int nLoginNum);
        int GetCurHour();
        void Init();
    };    
    static_assert(ATF::checkSize<CConnNumPHMgr, 32>(), "CConnNumPHMgr");
END_ATF_NAMESPACE

#include <_qry_case_amine_selore.hpp>


START_ATF_NAMESPACE
    _qry_case_amine_selore::_qry_case_amine_selore()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_amine_selore*);
        (org_ptr(0x1402d43b0L))(this);
    };
    void _qry_case_amine_selore::ctor__qry_case_amine_selore()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_amine_selore*);
        (org_ptr(0x1402d43b0L))(this);
    };
    int _qry_case_amine_selore::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_amine_selore*);
        return (org_ptr(0x1402d43d0L))(this);
    };
END_ATF_NAMESPACE

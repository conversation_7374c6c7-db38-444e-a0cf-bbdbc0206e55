// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RECTL.hpp>
#include <tagEMR.hpp>
#include <tagEMRTEXT.hpp>


START_ATF_NAMESPACE
    struct tagEMREXTTEXTOUTA
    {
        tagEMR emr;
        _RECTL rclBounds;
        unsigned int iGraphicsMode;
        float exScale;
        float eyScale;
        tagEMRTEXT emrtext;
    };
END_ATF_NAMESPACE

#include <_nuclear_bomb_drop_result_zocl.hpp>


START_ATF_NAMESPACE
    _nuclear_bomb_drop_result_zocl::_nuclear_bomb_drop_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_bomb_drop_result_zocl*);
        (org_ptr(0x14013e630L))(this);
    };
    void _nuclear_bomb_drop_result_zocl::ctor__nuclear_bomb_drop_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_bomb_drop_result_zocl*);
        (org_ptr(0x14013e630L))(this);
    };
END_ATF_NAMESPACE

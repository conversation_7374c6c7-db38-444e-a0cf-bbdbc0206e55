// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_NPCQuestIndexTempData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _NPCQuestIndexTempDataInit2_ptr = void (WINAPIV*)(struct _NPCQuestIndexTempData*);
        using _NPCQuestIndexTempDataInit2_clbk = void (WINAPIV*)(struct _NPCQuestIndexTempData*, _NPCQuestIndexTempDataInit2_ptr);
        
        using _NPCQuestIndexTempDatactor__NPCQuestIndexTempData4_ptr = void (WINAPIV*)(struct _NPCQuestIndexTempData*);
        using _NPCQuestIndexTempDatactor__NPCQuestIndexTempData4_clbk = void (WINAPIV*)(struct _NPCQuestIndexTempData*, _NPCQuestIndexTempDatactor__NPCQuestIndexTempData4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

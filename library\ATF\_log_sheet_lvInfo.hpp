// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_log_sheet_lv.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _log_sheet_lvsize2_ptr = int (WINAPIV*)(struct _log_sheet_lv*);
        using _log_sheet_lvsize2_clbk = int (WINAPIV*)(struct _log_sheet_lv*, _log_sheet_lvsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

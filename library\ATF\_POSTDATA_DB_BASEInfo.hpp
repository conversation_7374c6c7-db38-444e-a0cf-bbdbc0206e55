// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_POSTDATA_DB_BASE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _POSTDATA_DB_BASEInit2_ptr = void (WINAPIV*)(struct _POSTDATA_DB_BASE*);
        using _POSTDATA_DB_BASEInit2_clbk = void (WINAPIV*)(struct _POSTDATA_DB_BASE*, _POSTDATA_DB_BASEInit2_ptr);
        using _POSTDATA_DB_BASEUpdateInit4_ptr = void (WINAPIV*)(struct _POSTDATA_DB_BASE*);
        using _POSTDATA_DB_BASEUpdateInit4_clbk = void (WINAPIV*)(struct _POSTDATA_DB_BASE*, _POSTDATA_DB_BASEUpdateInit4_ptr);
        
        using _POSTDATA_DB_BASEctor__POSTDATA_DB_BASE6_ptr = void (WINAPIV*)(struct _POSTDATA_DB_BASE*);
        using _POSTDATA_DB_BASEctor__POSTDATA_DB_BASE6_clbk = void (WINAPIV*)(struct _POSTDATA_DB_BASE*, _POSTDATA_DB_BASEctor__POSTDATA_DB_BASE6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

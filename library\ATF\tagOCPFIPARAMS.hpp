// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <IUnknown.hpp>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    struct tagOCPFIPARAMS
    {
        unsigned int cbStructSize;
        HWND__ *hWndOwner;
        int x;
        int y;
        const wchar_t *lpszCaption;
        unsigned int cObjects;
        IUnknown **lplpUnk;
        unsigned int cPages;
        _GUID *lpPages;
        unsigned int lcid;
        int dispidInitialProperty;
    };
END_ATF_NAMESPACE

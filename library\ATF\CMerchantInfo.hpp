// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMerchant.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMerchantctor_CMerchant2_ptr = void (WINAPIV*)(struct CMerchant*);
        using CMerchantctor_CMerchant2_clbk = void (WINAPIV*)(struct CMerchant*, CMerchantctor_CMerchant2_ptr);
        using CMerchantCreate4_ptr = bool (WINAPIV*)(struct CMerchant*, struct _npc_create_setdata*);
        using CMerchantCreate4_clbk = bool (WINAPIV*)(struct CMerchant*, struct _npc_create_setdata*, CMerchantCreate4_ptr);
        using CMerchantDestroy6_ptr = bool (WINAPIV*)(struct CMerchant*, struct CGameObject*);
        using CMerchantDestroy6_clbk = bool (WINAPIV*)(struct CMerchant*, struct CGameObject*, CMerchantDestroy6_ptr);
        using CMerchantGetFireTol8_ptr = int (WINAPIV*)(struct CMerchant*);
        using CMerchantGetFireTol8_clbk = int (WINAPIV*)(struct CMerchant*, CMerchantGetFireTol8_ptr);
        using CMerchantGetNewMonSerial10_ptr = unsigned int (WINAPIV*)();
        using CMerchantGetNewMonSerial10_clbk = unsigned int (WINAPIV*)(CMerchantGetNewMonSerial10_ptr);
        using CMerchantGetObjName12_ptr = char* (WINAPIV*)(struct CMerchant*);
        using CMerchantGetObjName12_clbk = char* (WINAPIV*)(struct CMerchant*, CMerchantGetObjName12_ptr);
        using CMerchantGetObjRace14_ptr = int (WINAPIV*)(struct CMerchant*);
        using CMerchantGetObjRace14_clbk = int (WINAPIV*)(struct CMerchant*, CMerchantGetObjRace14_ptr);
        using CMerchantGetSoilTol16_ptr = int (WINAPIV*)(struct CMerchant*);
        using CMerchantGetSoilTol16_clbk = int (WINAPIV*)(struct CMerchant*, CMerchantGetSoilTol16_ptr);
        using CMerchantGetStoreDummyName18_ptr = char* (WINAPIV*)(struct CMerchant*);
        using CMerchantGetStoreDummyName18_clbk = char* (WINAPIV*)(struct CMerchant*, CMerchantGetStoreDummyName18_ptr);
        using CMerchantGetWaterTol20_ptr = int (WINAPIV*)(struct CMerchant*);
        using CMerchantGetWaterTol20_clbk = int (WINAPIV*)(struct CMerchant*, CMerchantGetWaterTol20_ptr);
        using CMerchantGetWindTol22_ptr = int (WINAPIV*)(struct CMerchant*);
        using CMerchantGetWindTol22_clbk = int (WINAPIV*)(struct CMerchant*, CMerchantGetWindTol22_ptr);
        using CMerchantInit24_ptr = bool (WINAPIV*)(struct CMerchant*, struct _object_id*);
        using CMerchantInit24_clbk = bool (WINAPIV*)(struct CMerchant*, struct _object_id*, CMerchantInit24_ptr);
        using CMerchantLoop26_ptr = void (WINAPIV*)(struct CMerchant*);
        using CMerchantLoop26_clbk = void (WINAPIV*)(struct CMerchant*, CMerchantLoop26_ptr);
        using CMerchantOutOfSec28_ptr = void (WINAPIV*)(struct CMerchant*);
        using CMerchantOutOfSec28_clbk = void (WINAPIV*)(struct CMerchant*, CMerchantOutOfSec28_ptr);
        using CMerchantSendMsg_Create30_ptr = void (WINAPIV*)(struct CMerchant*);
        using CMerchantSendMsg_Create30_clbk = void (WINAPIV*)(struct CMerchant*, CMerchantSendMsg_Create30_ptr);
        using CMerchantSendMsg_Destroy32_ptr = void (WINAPIV*)(struct CMerchant*);
        using CMerchantSendMsg_Destroy32_clbk = void (WINAPIV*)(struct CMerchant*, CMerchantSendMsg_Destroy32_ptr);
        using CMerchantSendMsg_FixPosition34_ptr = void (WINAPIV*)(struct CMerchant*, int);
        using CMerchantSendMsg_FixPosition34_clbk = void (WINAPIV*)(struct CMerchant*, int, CMerchantSendMsg_FixPosition34_ptr);
        using CMerchantSendMsg_Move36_ptr = void (WINAPIV*)(struct CMerchant*);
        using CMerchantSendMsg_Move36_clbk = void (WINAPIV*)(struct CMerchant*, CMerchantSendMsg_Move36_ptr);
        using CMerchantSendMsg_RealFixPosition38_ptr = void (WINAPIV*)(struct CMerchant*, bool);
        using CMerchantSendMsg_RealFixPosition38_clbk = void (WINAPIV*)(struct CMerchant*, bool, CMerchantSendMsg_RealFixPosition38_ptr);
        using CMerchantSendMsg_RealMovePoint40_ptr = void (WINAPIV*)(struct CMerchant*, int);
        using CMerchantSendMsg_RealMovePoint40_clbk = void (WINAPIV*)(struct CMerchant*, int, CMerchantSendMsg_RealMovePoint40_ptr);
        using CMerchantSendMsg_TransShipTicketNumInform42_ptr = void (WINAPIV*)(struct CMerchant*, int);
        using CMerchantSendMsg_TransShipTicketNumInform42_clbk = void (WINAPIV*)(struct CMerchant*, int, CMerchantSendMsg_TransShipTicketNumInform42_ptr);
        
        using CMerchantdtor_CMerchant48_ptr = void (WINAPIV*)(struct CMerchant*);
        using CMerchantdtor_CMerchant48_clbk = void (WINAPIV*)(struct CMerchant*, CMerchantdtor_CMerchant48_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

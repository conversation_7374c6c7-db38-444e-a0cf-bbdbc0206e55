#include <_personal_amine_infoui_open_zocl.hpp>


START_ATF_NAMESPACE
    _personal_amine_infoui_open_zocl::_personal_amine_infoui_open_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_infoui_open_zocl*);
        (org_ptr(0x1402de1d0L))(this);
    };
    void _personal_amine_infoui_open_zocl::ctor__personal_amine_infoui_open_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_infoui_open_zocl*);
        (org_ptr(0x1402de1d0L))(this);
    };
    int _personal_amine_infoui_open_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_amine_infoui_open_zocl*);
        return (org_ptr(0x1402de220L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CTimer
    {
        float mLoopTime;
        float mTime;
        float mRealTime;
        float mMinFPS;
        float mfLoopHop;
        unsigned int mOldTime;
        unsigned int mLoopCnt;
        unsigned int mLoopHop;
        int m_bUsingQPF;
        int m_bTimerInitialized;
        float mFPS;
        unsigned int mLoopFPSCnt;
        float mFPSTime;
        float m_fTicksPerSec;
        float m_fFramesPerSec;
        float m_fAverageFramesPerSec;
        float m_fSecsPerFrame;
        float m_fLamTime;
        unsigned __int64 m_qwTicks;
        unsigned __int64 m_qwStartTicks;
        unsigned __int64 m_qwTicksPerSec;
        unsigned __int64 m_qwTicksPerFrame;
    public:
        void CalculateTime();
        float GetDuration();
        float GetLoopTime();
        uint64_t GetTicks();
        float GetTime();
        void SetMinFPS(float arg_0);
        void SetTime(float arg_0);
    };
END_ATF_NAMESPACE

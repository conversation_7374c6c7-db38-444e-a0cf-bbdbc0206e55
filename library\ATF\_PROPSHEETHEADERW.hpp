// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$239F6525EBEE5A59CD6D6A36644BDE3E.hpp>
#include <$4C4583A6196236F56BC12590E4B2EFC2.hpp>
#include <$83C8E2C911F5E2587ECBCD922D9E85F5.hpp>
#include <$B0947E08C6202895FB055323AE645C10.hpp>
#include <$BDF50BE7362FD6198E9BB19DF1C2D17C.hpp>
#include <HINSTANCE__.hpp>
#include <HPALETTE__.hpp>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    struct _PROPSHEETHEADERW
    {
        unsigned int dwSize;
        unsigned int dwFlags;
        HWND__ *hwndParent;
        HINSTANCE__ *hInstance;
        $BDF50BE7362FD6198E9BB19DF1C2D17C ___u4;
        const wchar_t *pszCaption;
        unsigned int nPages;
        $83C8E2C911F5E2587ECBCD922D9E85F5 ___u7;
        $4C4583A6196236F56BC12590E4B2EFC2 ___u8;
        int (WINAPIV *pfnCallback)(HWND__ *, unsigned int, __int64);
        $B0947E08C6202895FB055323AE645C10 ___u10;
        HPALETTE__ *hplWatermark;
        $239F6525EBEE5A59CD6D6A36644BDE3E ___u12;
    };
END_ATF_NAMESPACE

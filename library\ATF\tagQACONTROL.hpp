// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagQACONTROL
    {
        unsigned int cbSize;
        unsigned int dwMiscStatus;
        unsigned int dwViewStatus;
        unsigned int dwEventCookie;
        unsigned int dwPropNotifyCookie;
        unsigned int dwPointerActivationPolicy;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringData.hpp>
#include <ATL__IAtlStringMgr.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct  CFixedStringMgr : IAtlStringMgr
        {
            IAtlStringMgr *m_pMgr;
            CStringData *m_pData;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

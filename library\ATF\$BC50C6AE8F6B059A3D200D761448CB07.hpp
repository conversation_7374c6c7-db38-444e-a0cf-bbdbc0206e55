// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$6A435823DE2D5F9D1069AA43A5DA44DD.hpp>
#include <$6DC2C7E74167B71B77D6DD11ECD04365.hpp>
#include <$9D94810479450F1C808DE70F2901A3F1.hpp>
#include <$F1A9B145873FF1611D30A5027CBCDEC0.hpp>


START_ATF_NAMESPACE
    union $BC50C6AE8F6B059A3D200D761448CB07
    {
        $F1A9B145873FF1611D30A5027CBCDEC0 WindowMessage;
        $6DC2C7E74167B71B77D6DD11ECD04365 Event;
        $6A435823DE2D5F9D1069AA43A5DA44DD Apc;
        $9D94810479450F1C808DE70F2901A3F1 Port;
    };
END_ATF_NAMESPACE

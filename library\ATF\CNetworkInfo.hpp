// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetwork.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNetworkctor_CNetwork2_ptr = void (WINAPIV*)(struct CNetwork*);
        using CNetworkctor_CNetwork2_clbk = void (WINAPIV*)(struct CNetwork*, CNetworkctor_CNetwork2_ptr);
        using CNetworkFreeDLL4_ptr = void (WINAPIV*)(struct CNetwork*);
        using CNetworkFreeDLL4_clbk = void (WINAPIV*)(struct CNetwork*, CNetworkFreeDLL4_ptr);
        using CNetworkInitNetwork6_ptr = void (WINAPIV*)(struct CNetwork*);
        using CNetworkInitNetwork6_clbk = void (WINAPIV*)(struct CNetwork*, CNetworkInitNetwork6_ptr);
        using CNetworkLoadDll8_ptr = bool (WINAPIV*)(struct CNetwork*, char*);
        using CNetworkLoadDll8_clbk = bool (WINAPIV*)(struct CNetwork*, char*, CNetworkLoadDll8_ptr);
        
        using CNetworkdtor_CNetwork13_ptr = void (WINAPIV*)(struct CNetwork*);
        using CNetworkdtor_CNetwork13_clbk = void (WINAPIV*)(struct CNetwork*, CNetworkdtor_CNetwork13_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

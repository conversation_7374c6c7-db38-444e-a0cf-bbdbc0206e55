// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CashDbWorker.hpp>
#include <Task.hpp>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    struct  CCashDbWorkerJP : CashDbWorker
    {
    public:
        CCashDbWorkerJP();
        void ctor_CCashDbWorkerJP();
        int ConvertErrorCode(char state);
        void GetUseCashQueryStr(struct _param_cash_update* rParam, int nIdx, char* wszQuery, uint64_t tBufferSize);
        void _all_rollback(struct _param_cash_update* psheet);
        int _wait_tsk_cash_rollback(struct Task* pkTsk);
        int _wait_tsk_cash_select(struct Task* pkTsk);
        int _wait_tsk_cash_update(struct Task* pkTsk);
        ~CCashDbWorkerJP();
        void dtor_CCashDbWorkerJP();
    };
END_ATF_NAMESPACE

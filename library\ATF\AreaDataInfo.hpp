// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AreaData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using AreaDatactor_AreaData2_ptr = void (WINAPIV*)(struct AreaData*, struct AreaData*);
        using AreaDatactor_AreaData2_clbk = void (WINAPIV*)(struct AreaData*, struct AreaData*, AreaDatactor_AreaData2_ptr);
        
        using AreaDatactor_AreaData4_ptr = void (WINAPIV*)(struct AreaData*);
        using AreaDatactor_AreaData4_clbk = void (WINAPIV*)(struct AreaData*, AreaDatactor_AreaData4_ptr);
        
        using AreaDatadtor_AreaData10_ptr = void (WINAPIV*)(struct AreaData*);
        using AreaDatadtor_AreaData10_clbk = void (WINAPIV*)(struct AreaData*, AreaDatadtor_AreaData10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

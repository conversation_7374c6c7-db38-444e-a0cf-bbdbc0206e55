// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <WheatyExceptionReport.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using WheatyExceptionReportDumpTypeIndex2_ptr = char* (WINAPIV*)(char*, uint64_t, unsigned int, unsigned int, uint64_t, bool*);
        using WheatyExceptionReportDumpTypeIndex2_clbk = char* (WINAPIV*)(char*, uint64_t, unsigned int, unsigned int, uint64_t, bool*, WheatyExceptionReportDumpTypeIndex2_ptr);
        using WheatyExceptionReportEnumerateSymbolsCallback4_ptr = int (WINAPIV*)(struct _SYMBOL_INFO*, unsigned int, void*);
        using WheatyExceptionReportEnumerateSymbolsCallback4_clbk = int (WINAPIV*)(struct _SYMBOL_INFO*, unsigned int, void*, WheatyExceptionReportEnumerateSymbolsCallback4_ptr);
        using WheatyExceptionReportFormatOutputValue6_ptr = char* (WINAPIV*)(char*, BasicType, uint64_t, void*);
        using WheatyExceptionReportFormatOutputValue6_clbk = char* (WINAPIV*)(char*, BasicType, uint64_t, void*, WheatyExceptionReportFormatOutputValue6_ptr);
        using WheatyExceptionReportFormatSymbolValue8_ptr = bool (WINAPIV*)(struct _SYMBOL_INFO*, struct _tagSTACKFRAME64*, char*, unsigned int);
        using WheatyExceptionReportFormatSymbolValue8_clbk = bool (WINAPIV*)(struct _SYMBOL_INFO*, struct _tagSTACKFRAME64*, char*, unsigned int, WheatyExceptionReportFormatSymbolValue8_ptr);
        using WheatyExceptionReportGenerateExceptionReport10_ptr = void (WINAPIV*)(struct _EXCEPTION_POINTERS*);
        using WheatyExceptionReportGenerateExceptionReport10_clbk = void (WINAPIV*)(struct _EXCEPTION_POINTERS*, WheatyExceptionReportGenerateExceptionReport10_ptr);
        using WheatyExceptionReportGetBasicType12_ptr = BasicType (WINAPIV*)(unsigned int, uint64_t);
        using WheatyExceptionReportGetBasicType12_clbk = BasicType (WINAPIV*)(unsigned int, uint64_t, WheatyExceptionReportGetBasicType12_ptr);
        using WheatyExceptionReportGetDisplayInfo14_ptr = int (WINAPIV*)(int, char*, char*);
        using WheatyExceptionReportGetDisplayInfo14_clbk = int (WINAPIV*)(int, char*, char*, WheatyExceptionReportGetDisplayInfo14_ptr);
        using WheatyExceptionReportGetExceptionString16_ptr = char* (WINAPIV*)(unsigned int);
        using WheatyExceptionReportGetExceptionString16_clbk = char* (WINAPIV*)(unsigned int, WheatyExceptionReportGetExceptionString16_ptr);
        using WheatyExceptionReportGetLogicalAddress18_ptr = int (WINAPIV*)(void*, char*, unsigned int, unsigned int*, unsigned int*);
        using WheatyExceptionReportGetLogicalAddress18_clbk = int (WINAPIV*)(void*, char*, unsigned int, unsigned int*, unsigned int*, WheatyExceptionReportGetLogicalAddress18_ptr);
        using WheatyExceptionReportGetOsName20_ptr = char* (WINAPIV*)(unsigned int, unsigned int, unsigned int);
        using WheatyExceptionReportGetOsName20_clbk = char* (WINAPIV*)(unsigned int, unsigned int, unsigned int, WheatyExceptionReportGetOsName20_ptr);
        using WheatyExceptionReportGetOsVersion22_ptr = char* (WINAPIV*)();
        using WheatyExceptionReportGetOsVersion22_clbk = char* (WINAPIV*)(WheatyExceptionReportGetOsVersion22_ptr);
        using WheatyExceptionReportSetDescription24_ptr = void (WINAPIV*)(struct WheatyExceptionReport*, char*);
        using WheatyExceptionReportSetDescription24_clbk = void (WINAPIV*)(struct WheatyExceptionReport*, char*, WheatyExceptionReportSetDescription24_ptr);
        using WheatyExceptionReportSetFtpConnection26_ptr = void (WINAPIV*)(struct WheatyExceptionReport*, char*, unsigned int, char*, char*, char*);
        using WheatyExceptionReportSetFtpConnection26_clbk = void (WINAPIV*)(struct WheatyExceptionReport*, char*, unsigned int, char*, char*, char*, WheatyExceptionReportSetFtpConnection26_ptr);
        using WheatyExceptionReportSetLogName28_ptr = void (WINAPIV*)(struct WheatyExceptionReport*, char*);
        using WheatyExceptionReportSetLogName28_clbk = void (WINAPIV*)(struct WheatyExceptionReport*, char*, WheatyExceptionReportSetLogName28_ptr);
        using WheatyExceptionReportSetRunDialog30_ptr = void (WINAPIV*)(struct WheatyExceptionReport*, int);
        using WheatyExceptionReportSetRunDialog30_clbk = void (WINAPIV*)(struct WheatyExceptionReport*, int, WheatyExceptionReportSetRunDialog30_ptr);
        
        using WheatyExceptionReportctor_WheatyExceptionReport32_ptr = void (WINAPIV*)(struct WheatyExceptionReport*);
        using WheatyExceptionReportctor_WheatyExceptionReport32_clbk = void (WINAPIV*)(struct WheatyExceptionReport*, WheatyExceptionReportctor_WheatyExceptionReport32_ptr);
        using WheatyExceptionReportWheatyUnhandledExceptionFilter34_ptr = int (WINAPIV*)(struct _EXCEPTION_POINTERS*);
        using WheatyExceptionReportWheatyUnhandledExceptionFilter34_clbk = int (WINAPIV*)(struct _EXCEPTION_POINTERS*, WheatyExceptionReportWheatyUnhandledExceptionFilter34_ptr);
        using WheatyExceptionReportWriteStackDetails36_ptr = void (WINAPIV*)(struct _CONTEXT*, bool);
        using WheatyExceptionReportWriteStackDetails36_clbk = void (WINAPIV*)(struct _CONTEXT*, bool, WheatyExceptionReportWriteStackDetails36_ptr);
        using WheatyExceptionReport_tprintfh38_ptr = int (WINAPIV*)(void*, char*);
        using WheatyExceptionReport_tprintfh38_clbk = int (WINAPIV*)(void*, char*, WheatyExceptionReport_tprintfh38_ptr);
        using WheatyExceptionReportprintf40_ptr = int (WINAPIV*)(char*);
        using WheatyExceptionReportprintf40_clbk = int (WINAPIV*)(char*, WheatyExceptionReportprintf40_ptr);
        
        using WheatyExceptionReportdtor_WheatyExceptionReport42_ptr = void (WINAPIV*)(struct WheatyExceptionReport*);
        using WheatyExceptionReportdtor_WheatyExceptionReport42_clbk = void (WINAPIV*)(struct WheatyExceptionReport*, WheatyExceptionReportdtor_WheatyExceptionReport42_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

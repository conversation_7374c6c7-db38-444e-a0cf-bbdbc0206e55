// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _log_sheet_economy
    {
        unsigned int dwDate;
        long double dTradeGold[3];
        long double dTradeDalant[3];
        int nMgrValue;
        long double dMineOre[3][3];
        long double dCutOre[3][3];
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_log_sheet_economy, 208>(), "_log_sheet_economy");
END_ATF_NAMESPACE

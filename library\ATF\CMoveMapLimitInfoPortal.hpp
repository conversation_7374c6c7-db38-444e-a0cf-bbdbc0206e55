// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitInfo.hpp>
#include <CMoveMapLimitRightInfo.hpp>
#include <CMyTimer.hpp>
#include <CPlayer.hpp>
#include <_dummy_position.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CMoveMapLimitInfoPortal : CMoveMapLimitInfo
    {
        enum REQUEST_TYPE
        {
            RT_FORCE_MOVE_HQ = 0x0,
            RT_USE_MOVE_SCROLL = 0x1,
            RT_GOTO_LIMIT_ZONE = 0x2,
            RT_MAX = 0x3,
        };
        enum FORCE_MOVE_HQ_STATE
        {
            FMHS_WAIT = 0x0,
            FMHS_WAIT_NOTIFY_FORCE_MOVE = 0x1,
            FMHS_NOTIFY_FORCE_MOVE = 0x2,
            <PERSON><PERSON>_WAIT_FORCE_MOVE = 0x3,
            FMHS_FORCE_MOVE = 0x4,
        };
        _dummy_position *m_pkSrcDummy;
        _dummy_position *m_pkDestDummy;
        _dummy_position *m_pkRegenDummy;
        std::vector<char *,std::allocator<char *> > m_vecAllowDummyCode;
        FORCE_MOVE_HQ_STATE m_eNotifyForceMoveHQState;
        CMyTimer *m_pkNotifyForceMoveHQTimer;
        unsigned int m_uiProcNotifyInx;
    public:
        CMoveMapLimitInfoPortal(unsigned int uiInx, int iType);
        void ctor_CMoveMapLimitInfoPortal(unsigned int uiInx, int iType);
        bool Init();
        void Load(struct CPlayer* pkPlayer, struct CMoveMapLimitRightInfo* pkRight);
        bool LoadINI();
        void Loop();
        char ProcForceMoveHQ(int iUserInx, char* pRequest, struct CMoveMapLimitRightInfo* pkRight);
        char ProcGotoLimitZone(int iUserInx, char* pRequest, struct CMoveMapLimitRightInfo* pkRight);
        char ProcUseMoveScroll(int iUserInx, char* pRequest, struct CMoveMapLimitRightInfo* pkRight);
        char Request(int iUserInx, int iRequetType, char* pRequest, struct CMoveMapLimitRightInfo* pkRight);
        void SubProcForceMoveHQ();
        char SubProcGotoLimitZone(int iUserInx, char* pRequest, struct CMoveMapLimitRightInfo* pkRight);
        void SubProcNotifyForceMoveHQ();
        ~CMoveMapLimitInfoPortal();
        void dtor_CMoveMapLimitInfoPortal();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <UsRefObject.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using UsRefObjectDecRefCount2_ptr = void (WINAPIV*)(struct UsRefObject*);
        using UsRefObjectDecRefCount2_clbk = void (WINAPIV*)(struct UsRefObject*, UsRefObjectDecRefCount2_ptr);
        using UsRefObjectIncRefCount4_ptr = void (WINAPIV*)(struct UsRefObject*);
        using UsRefObjectIncRefCount4_clbk = void (WINAPIV*)(struct UsRefObject*, UsRefObjectIncRefCount4_ptr);
        
        using UsRefObjectctor_UsRefObject6_ptr = void (WINAPIV*)(struct UsRefObject*);
        using UsRefObjectctor_UsRefObject6_clbk = void (WINAPIV*)(struct UsRefObject*, UsRefObjectctor_UsRefObject6_ptr);
        
        using UsRefObjectdtor_UsRefObject11_ptr = void (WINAPIV*)(struct UsRefObject*);
        using UsRefObjectdtor_UsRefObject11_clbk = void (WINAPIV*)(struct UsRefObject*, UsRefObjectdtor_UsRefObject11_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LINKKEY.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _LINK_DB_BASE
    {
        struct _LIST
        {
            _LINKKEY Key;
        public:
            void Init();
            _LIST();
            void ctor__LIST();
        };
        _LIST m_LinkList[50];
        char m_byLinkBoardLock;
        unsigned int m_dwSkill[2];
        unsigned int m_dwForce[2];
        unsigned int m_dwCharacter[2];
        unsigned int m_dwAnimus[2];
        unsigned int m_dwInven;
        unsigned int m_dwInvenBag[5];
    public:
        void Init();
        _LINK_DB_BASE();
        void ctor__LINK_DB_BASE();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_LINK_DB_BASE, 157>(), "_LINK_DB_BASE");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AreaList.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using AreaListctor_AreaList2_ptr = void (WINAPIV*)(struct AreaList*, struct AreaList*);
        using AreaListctor_AreaList2_clbk = void (WINAPIV*)(struct AreaList*, struct AreaList*, AreaListctor_AreaList2_ptr);
        
        using AreaListctor_AreaList4_ptr = void (WINAPIV*)(struct AreaList*);
        using AreaListctor_AreaList4_clbk = void (WINAPIV*)(struct AreaList*, AreaListctor_AreaList4_ptr);
        using AreaListExtractData6_ptr = void (WINAPIV*)(struct AreaList*);
        using AreaListExtractData6_clbk = void (WINAPIV*)(struct AreaList*, AreaListExtractData6_ptr);
        using AreaListPush8_ptr = void (WINAPIV*)(struct AreaList*, struct AreaData);
        using AreaListPush8_clbk = void (WINAPIV*)(struct AreaList*, struct AreaData, AreaListPush8_ptr);
        
        using AreaListdtor_AreaList12_ptr = void (WINAPIV*)(struct AreaList*);
        using AreaListdtor_AreaList12_clbk = void (WINAPIV*)(struct AreaList*, AreaListdtor_AreaList12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

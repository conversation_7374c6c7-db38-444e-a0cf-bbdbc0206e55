// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBsp.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CBspctor_CBsp1_ptr = int64_t (WINAPIV*)(struct CBsp*);
        using CBspctor_CBsp1_clbk = int64_t (WINAPIV*)(struct CBsp*, CBspctor_CBsp1_ptr);
        using CBspCalcEntitiesMainColor2_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspCalcEntitiesMainColor2_clbk = void (WINAPIV*)(struct CBsp*, CBspCalcEntitiesMainColor2_ptr);
        using CBspCalcObjectLoop3_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspCalcObjectLoop3_clbk = void (WINAPIV*)(struct CBsp*, CBspCalcObjectLoop3_ptr);
        using CBspCanYouGoThere4_ptr = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**);
        using CBspCanYouGoThere4_clbk = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, CBspCanYouGoThere4_ptr);
        using CBspClearVariable5_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspClearVariable5_clbk = void (WINAPIV*)(struct CBsp*, CBspClearVariable5_ptr);
        using CBspDrawAlphaRender6_ptr = void (WINAPIV*)(struct CBsp*, float*);
        using CBspDrawAlphaRender6_clbk = void (WINAPIV*)(struct CBsp*, float*, CBspDrawAlphaRender6_ptr);
        using CBspDrawBspRender7_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspDrawBspRender7_clbk = void (WINAPIV*)(struct CBsp*, CBspDrawBspRender7_ptr);
        using CBspDrawCollisionPoly8_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspDrawCollisionPoly8_clbk = void (WINAPIV*)(struct CBsp*, CBspDrawCollisionPoly8_ptr);
        using CBspDrawDynamicLightSub9_ptr = void (WINAPIV*)(struct CBsp*, float*, float*);
        using CBspDrawDynamicLightSub9_clbk = void (WINAPIV*)(struct CBsp*, float*, float*, CBspDrawDynamicLightSub9_ptr);
        using CBspDrawDynamicLights10_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspDrawDynamicLights10_clbk = void (WINAPIV*)(struct CBsp*, CBspDrawDynamicLights10_ptr);
        using CBspDrawLeafBBox11_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspDrawLeafBBox11_clbk = void (WINAPIV*)(struct CBsp*, CBspDrawLeafBBox11_ptr);
        using CBspDrawLeafCollisionPoly12_ptr = void (WINAPIV*)(struct CBsp*, int16_t);
        using CBspDrawLeafCollisionPoly12_clbk = void (WINAPIV*)(struct CBsp*, int16_t, CBspDrawLeafCollisionPoly12_ptr);
        using CBspDrawMagicLightSub13_ptr = void (WINAPIV*)(struct CBsp*, float*, float*);
        using CBspDrawMagicLightSub13_clbk = void (WINAPIV*)(struct CBsp*, float*, float*, CBspDrawMagicLightSub13_ptr);
        using CBspDrawMapEntitiesRender14_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspDrawMapEntitiesRender14_clbk = void (WINAPIV*)(struct CBsp*, CBspDrawMapEntitiesRender14_ptr);
        using CBspDrawMatBBox15_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspDrawMatBBox15_clbk = void (WINAPIV*)(struct CBsp*, CBspDrawMatBBox15_ptr);
        using CBspDrawShadowRender16_ptr = void (WINAPIV*)(struct CBsp*, float*, float*, float*);
        using CBspDrawShadowRender16_clbk = void (WINAPIV*)(struct CBsp*, float*, float*, float*, CBspDrawShadowRender16_ptr);
        using CBspEdgeTest17_ptr = int (WINAPIV*)(struct CBsp*, float*, int);
        using CBspEdgeTest17_clbk = int (WINAPIV*)(struct CBsp*, float*, int, CBspEdgeTest17_ptr);
        using CBspFastWalkNodeForLeafListFromBBox18_ptr = void (WINAPIV*)(struct CBsp*, int8_t, int16_t, int16_t, float**);
        using CBspFastWalkNodeForLeafListFromBBox18_clbk = void (WINAPIV*)(struct CBsp*, int8_t, int16_t, int16_t, float**, CBspFastWalkNodeForLeafListFromBBox18_ptr);
        using CBspFrameMoveEnvironment19_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspFrameMoveEnvironment19_clbk = void (WINAPIV*)(struct CBsp*, CBspFrameMoveEnvironment19_ptr);
        using CBspFrameMoveMapEntities20_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspFrameMoveMapEntities20_clbk = void (WINAPIV*)(struct CBsp*, CBspFrameMoveMapEntities20_ptr);
        using CBspGetBestYposInLeaf21_ptr = float (WINAPIV*)(struct CBsp*, float*, float*, float, float, int);
        using CBspGetBestYposInLeaf21_clbk = float (WINAPIV*)(struct CBsp*, float*, float*, float, float, int, CBspGetBestYposInLeaf21_ptr);
        using CBspGetBspObjectMatrix22_ptr = void (WINAPIV*)(struct CBsp*, float**, uint16_t);
        using CBspGetBspObjectMatrix22_clbk = void (WINAPIV*)(struct CBsp*, float**, uint16_t, CBspGetBspObjectMatrix22_ptr);
        using CBspGetColorFromPoint23_ptr = uint32_t (WINAPIV*)(struct CBsp*, int, float*);
        using CBspGetColorFromPoint23_clbk = uint32_t (WINAPIV*)(struct CBsp*, int, float*, CBspGetColorFromPoint23_ptr);
        using CBspGetDynamicVertexBuffer24_ptr = void* (WINAPIV*)(struct CBsp*);
        using CBspGetDynamicVertexBuffer24_clbk = void* (WINAPIV*)(struct CBsp*, CBspGetDynamicVertexBuffer24_ptr);
        using CBspGetEventAnimationState25_ptr = uint32_t (WINAPIV*)(struct CBsp*, uint16_t);
        using CBspGetEventAnimationState25_clbk = uint32_t (WINAPIV*)(struct CBsp*, uint16_t, CBspGetEventAnimationState25_ptr);
        using CBspGetFaceFrontPoint26_ptr = void (WINAPIV*)(struct CBsp*, float**, int);
        using CBspGetFaceFrontPoint26_clbk = void (WINAPIV*)(struct CBsp*, float**, int, CBspGetFaceFrontPoint26_ptr);
        using CBspGetFastLeafListFromBBox27_ptr = void (WINAPIV*)(struct CBsp*, float*, float*, int32_t*, int16_t*, uint32_t);
        using CBspGetFastLeafListFromBBox27_clbk = void (WINAPIV*)(struct CBsp*, float*, float*, int32_t*, int16_t*, uint32_t, CBspGetFastLeafListFromBBox27_ptr);
        using CBspGetFinalPath28_ptr = int64_t (WINAPIV*)(struct CBsp*, void*, float*, float**);
        using CBspGetFinalPath28_clbk = int64_t (WINAPIV*)(struct CBsp*, void*, float*, float**, CBspGetFinalPath28_ptr);
        using CBspGetFirstYpos29_ptr = float (WINAPIV*)(struct CBsp*, float*, float*, float*);
        using CBspGetFirstYpos29_clbk = float (WINAPIV*)(struct CBsp*, float*, float*, float*, CBspGetFirstYpos29_ptr);
        using CBspGetFirstYpos30_ptr = float (WINAPIV*)(struct CBsp*, float*, int);
        using CBspGetFirstYpos30_clbk = float (WINAPIV*)(struct CBsp*, float*, int, CBspGetFirstYpos30_ptr);
        using CBspGetFirstYpos31_ptr = float (WINAPIV*)(struct CBsp*, float*, int16_t*, int16_t*);
        using CBspGetFirstYpos31_clbk = float (WINAPIV*)(struct CBsp*, float*, int16_t*, int16_t*, CBspGetFirstYpos31_ptr);
        using CBspGetHeight32_ptr = void (WINAPIV*)(struct CBsp*, float*);
        using CBspGetHeight32_clbk = void (WINAPIV*)(struct CBsp*, float*, CBspGetHeight32_ptr);
        using CBspGetLeafList33_ptr = void (WINAPIV*)(struct CBsp*, float*, float*, int32_t*, int16_t*, uint32_t);
        using CBspGetLeafList33_clbk = void (WINAPIV*)(struct CBsp*, float*, float*, int32_t*, int16_t*, uint32_t, CBspGetLeafList33_ptr);
        using CBspGetLeafNum34_ptr = int16_t (WINAPIV*)(struct CBsp*, float*);
        using CBspGetLeafNum34_clbk = int16_t (WINAPIV*)(struct CBsp*, float*, CBspGetLeafNum34_ptr);
        using CBspGetLightFromPoint35_ptr = uint32_t (WINAPIV*)(struct CBsp*, float*, uint32_t);
        using CBspGetLightFromPoint35_clbk = uint32_t (WINAPIV*)(struct CBsp*, float*, uint32_t, CBspGetLightFromPoint35_ptr);
        using CBspGetLightMapUVFromPoint36_ptr = void (WINAPIV*)(struct CBsp*, float*, int, float*);
        using CBspGetLightMapUVFromPoint36_clbk = void (WINAPIV*)(struct CBsp*, float*, int, float*, CBspGetLightMapUVFromPoint36_ptr);
        using CBspGetMatGroup37_ptr = struct _BSP_MAT_GROUP* (WINAPIV*)(struct CBsp*);
        using CBspGetMatGroup37_clbk = struct _BSP_MAT_GROUP* (WINAPIV*)(struct CBsp*, CBspGetMatGroup37_ptr);
        using CBspGetMatGroupPoint38_ptr = float (WINAPIV*)(struct CBsp*, uint16_t, float*);
        using CBspGetMatGroupPoint38_clbk = float (WINAPIV*)(struct CBsp*, uint16_t, float*, CBspGetMatGroupPoint38_ptr);
        using CBspGetPath39_ptr = void (WINAPIV*)(struct CBsp*, float*, float*);
        using CBspGetPath39_clbk = void (WINAPIV*)(struct CBsp*, float*, float*, CBspGetPath39_ptr);
        using CBspGetPathCrossPoint40_ptr = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, int, int);
        using CBspGetPathCrossPoint40_clbk = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, int, int, CBspGetPathCrossPoint40_ptr);
        using CBspGetPathFind41_ptr = uint32_t (WINAPIV*)(struct CBsp*, float*, float*, float**, uint32_t*, int);
        using CBspGetPathFind41_clbk = uint32_t (WINAPIV*)(struct CBsp*, float*, float*, float**, uint32_t*, int, CBspGetPathFind41_ptr);
        using CBspGetPointFromScreenRay42_ptr = int64_t (WINAPIV*)(struct CBsp*, float, float, float*, float*);
        using CBspGetPointFromScreenRay42_clbk = int64_t (WINAPIV*)(struct CBsp*, float, float, float*, float*, CBspGetPointFromScreenRay42_ptr);
        using CBspGetPointFromScreenRayFar43_ptr = int64_t (WINAPIV*)(struct CBsp*, float, float, float*, float*);
        using CBspGetPointFromScreenRayFar43_clbk = int64_t (WINAPIV*)(struct CBsp*, float, float, float*, float*, CBspGetPointFromScreenRayFar43_ptr);
        using CBspGetVertexNormal44_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspGetVertexNormal44_clbk = void (WINAPIV*)(struct CBsp*, CBspGetVertexNormal44_ptr);
        using CBspGetYposInLeaf45_ptr = float (WINAPIV*)(struct CBsp*, float*, float*, float, float, int);
        using CBspGetYposInLeaf45_clbk = float (WINAPIV*)(struct CBsp*, float*, float*, float, float, int, CBspGetYposInLeaf45_ptr);
        using CBspGetYposInLeafNoAttr46_ptr = float (WINAPIV*)(struct CBsp*, float*, float*, float, float, int);
        using CBspGetYposInLeafNoAttr46_clbk = float (WINAPIV*)(struct CBsp*, float*, float*, float, float, int, CBspGetYposInLeafNoAttr46_ptr);
        using CBspGetYposInLeafUseEdgeNormal47_ptr = float (WINAPIV*)(struct CBsp*, float*, float*, float, float, int);
        using CBspGetYposInLeafUseEdgeNormal47_clbk = float (WINAPIV*)(struct CBsp*, float*, float*, float, float, int, CBspGetYposInLeafUseEdgeNormal47_ptr);
        using CBspHearMapSound48_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspHearMapSound48_clbk = void (WINAPIV*)(struct CBsp*, CBspHearMapSound48_ptr);
        using CBspIsCollisionFace49_ptr = int64_t (WINAPIV*)(struct CBsp*, float*, float*);
        using CBspIsCollisionFace49_clbk = int64_t (WINAPIV*)(struct CBsp*, float*, float*, CBspIsCollisionFace49_ptr);
        using CBspIsCollisionFace50_ptr = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, float**);
        using CBspIsCollisionFace50_clbk = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, float**, CBspIsCollisionFace50_ptr);
        using CBspIsCollisionFace51_ptr = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, float);
        using CBspIsCollisionFace51_clbk = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, float, CBspIsCollisionFace51_ptr);
        using CBspIsCollisionFaceForServer52_ptr = int64_t (WINAPIV*)(struct CBsp*, float*, float*);
        using CBspIsCollisionFaceForServer52_clbk = int64_t (WINAPIV*)(struct CBsp*, float*, float*, CBspIsCollisionFaceForServer52_ptr);
        using CBspIsCollisionFaceForServer53_ptr = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, float);
        using CBspIsCollisionFaceForServer53_clbk = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, float, CBspIsCollisionFaceForServer53_ptr);
        using CBspIsCollisionFromPath54_ptr = int64_t (WINAPIV*)(struct CBsp*, float*, float*);
        using CBspIsCollisionFromPath54_clbk = int64_t (WINAPIV*)(struct CBsp*, float*, float*, CBspIsCollisionFromPath54_ptr);
        using CBspIsExistSelfPoint55_ptr = int64_t (WINAPIV*)(struct CBsp*, int, int);
        using CBspIsExistSelfPoint55_clbk = int64_t (WINAPIV*)(struct CBsp*, int, int, CBspIsExistSelfPoint55_ptr);
        using CBspIsInViewFrustum56_ptr = int64_t (WINAPIV*)(struct CBsp*, uint16_t);
        using CBspIsInViewFrustum56_clbk = int64_t (WINAPIV*)(struct CBsp*, uint16_t, CBspIsInViewFrustum56_ptr);
        using CBspIsInWater57_ptr = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, float);
        using CBspIsInWater57_clbk = int64_t (WINAPIV*)(struct CBsp*, float*, float*, float**, float, CBspIsInWater57_ptr);
        using CBspIsLoaded58_ptr = int64_t (WINAPIV*)(struct CBsp*);
        using CBspIsLoaded58_clbk = int64_t (WINAPIV*)(struct CBsp*, CBspIsLoaded58_ptr);
        using CBspLoadBsp59_ptr = void (WINAPIV*)(struct CBsp*, char*);
        using CBspLoadBsp59_clbk = void (WINAPIV*)(struct CBsp*, char*, CBspLoadBsp59_ptr);
        using CBspLoadEntities60_ptr = void (WINAPIV*)(struct CBsp*, struct _READ_MAP_ENTITIES_LIST*);
        using CBspLoadEntities60_clbk = void (WINAPIV*)(struct CBsp*, struct _READ_MAP_ENTITIES_LIST*, CBspLoadEntities60_ptr);
        using CBspLoadEnvironment61_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspLoadEnvironment61_clbk = void (WINAPIV*)(struct CBsp*, CBspLoadEnvironment61_ptr);
        using CBspLoadExtBsp62_ptr = void (WINAPIV*)(struct CBsp*, char*);
        using CBspLoadExtBsp62_clbk = void (WINAPIV*)(struct CBsp*, char*, CBspLoadExtBsp62_ptr);
        using CBspLoadSoundEntities63_ptr = void (WINAPIV*)(struct CBsp*, struct _READ_SOUND_ENTITY_LIST*, struct _READ_SOUND_ENTITIES_LIST*);
        using CBspLoadSoundEntities63_clbk = void (WINAPIV*)(struct CBsp*, struct _READ_SOUND_ENTITY_LIST*, struct _READ_SOUND_ENTITIES_LIST*, CBspLoadSoundEntities63_ptr);
        using CBspLoopInitRenderedMatGroup64_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspLoopInitRenderedMatGroup64_clbk = void (WINAPIV*)(struct CBsp*, CBspLoopInitRenderedMatGroup64_ptr);
        using CBspMakeEdgeNormal65_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspMakeEdgeNormal65_clbk = void (WINAPIV*)(struct CBsp*, CBspMakeEdgeNormal65_ptr);
        using CBspOnlyStoreCollisionStructure66_ptr = void (WINAPIV*)(struct CBsp*, struct _BSP_READ_M_GROUP*, char**, int16_t**, float**, uint32_t*, struct _BSP_READ_FACE*, uint32_t*);
        using CBspOnlyStoreCollisionStructure66_clbk = void (WINAPIV*)(struct CBsp*, struct _BSP_READ_M_GROUP*, char**, int16_t**, float**, uint32_t*, struct _BSP_READ_FACE*, uint32_t*, CBspOnlyStoreCollisionStructure66_ptr);
        using CBspPrepareAnimation67_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspPrepareAnimation67_clbk = void (WINAPIV*)(struct CBsp*, CBspPrepareAnimation67_ptr);
        using CBspPrepareShadowRender68_ptr = void (WINAPIV*)(struct CBsp*, float*, void*, float, uint32_t, float, float);
        using CBspPrepareShadowRender68_clbk = void (WINAPIV*)(struct CBsp*, float*, void*, float, uint32_t, float, float, CBspPrepareShadowRender68_ptr);
        using CBspReadDynamicDataExtBsp69_ptr = void (WINAPIV*)(struct CBsp*, FILE*);
        using CBspReadDynamicDataExtBsp69_clbk = void (WINAPIV*)(struct CBsp*, FILE*, CBspReadDynamicDataExtBsp69_ptr);
        using CBspReadDynamicDataFillVertexBuffer70_ptr = void (WINAPIV*)(struct CBsp*, FILE*);
        using CBspReadDynamicDataFillVertexBuffer70_clbk = void (WINAPIV*)(struct CBsp*, FILE*, CBspReadDynamicDataFillVertexBuffer70_ptr);
        using CBspReadyBspRender71_ptr = void (WINAPIV*)(struct CBsp*, float*);
        using CBspReadyBspRender71_clbk = void (WINAPIV*)(struct CBsp*, float*, CBspReadyBspRender71_ptr);
        using CBspReleaseBsp72_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspReleaseBsp72_clbk = void (WINAPIV*)(struct CBsp*, CBspReleaseBsp72_ptr);
        using CBspReleaseEntities73_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspReleaseEntities73_clbk = void (WINAPIV*)(struct CBsp*, CBspReleaseEntities73_ptr);
        using CBspReleaseEnvironment74_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspReleaseEnvironment74_clbk = void (WINAPIV*)(struct CBsp*, CBspReleaseEnvironment74_ptr);
        using CBspReleaseSoundEntities75_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspReleaseSoundEntities75_clbk = void (WINAPIV*)(struct CBsp*, CBspReleaseSoundEntities75_ptr);
        using CBspRenderCollisionLeaf76_ptr = void (WINAPIV*)(struct CBsp*, int16_t);
        using CBspRenderCollisionLeaf76_clbk = void (WINAPIV*)(struct CBsp*, int16_t, CBspRenderCollisionLeaf76_ptr);
        using CBspRenderEnvironment77_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspRenderEnvironment77_clbk = void (WINAPIV*)(struct CBsp*, CBspRenderEnvironment77_ptr);
        using CBspRenderIndepentMatGroup78_ptr = void (WINAPIV*)(struct CBsp*, uint16_t);
        using CBspRenderIndepentMatGroup78_clbk = void (WINAPIV*)(struct CBsp*, uint16_t, CBspRenderIndepentMatGroup78_ptr);
        using CBspRenderLeaf79_ptr = void (WINAPIV*)(struct CBsp*, int16_t);
        using CBspRenderLeaf79_clbk = void (WINAPIV*)(struct CBsp*, int16_t, CBspRenderLeaf79_ptr);
        using CBspRenderMatGroup80_ptr = void (WINAPIV*)(struct CBsp*, uint16_t);
        using CBspRenderMatGroup80_clbk = void (WINAPIV*)(struct CBsp*, uint16_t, CBspRenderMatGroup80_ptr);
        using CBspRenderOneEntityRender81_ptr = void (WINAPIV*)(struct CBsp*, uint16_t);
        using CBspRenderOneEntityRender81_clbk = void (WINAPIV*)(struct CBsp*, uint16_t, CBspRenderOneEntityRender81_ptr);
        using CBspRenderReflectionMatGroup82_ptr = void (WINAPIV*)(struct CBsp*, uint16_t);
        using CBspRenderReflectionMatGroup82_clbk = void (WINAPIV*)(struct CBsp*, uint16_t, CBspRenderReflectionMatGroup82_ptr);
        using CBspRenderShadowMatGroup83_ptr = void (WINAPIV*)(struct CBsp*, uint16_t);
        using CBspRenderShadowMatGroup83_clbk = void (WINAPIV*)(struct CBsp*, uint16_t, CBspRenderShadowMatGroup83_ptr);
        using CBspSaveExtBsp84_ptr = void (WINAPIV*)(struct CBsp*, char*, struct _ADD_BSP_SAVE*);
        using CBspSaveExtBsp84_clbk = void (WINAPIV*)(struct CBsp*, char*, struct _ADD_BSP_SAVE*, CBspSaveExtBsp84_ptr);
        using CBspSearchNode85_ptr = void (WINAPIV*)(struct CBsp*, int16_t);
        using CBspSearchNode85_clbk = void (WINAPIV*)(struct CBsp*, int16_t, CBspSearchNode85_ptr);
        using CBspSetAllAnimationState86_ptr = int64_t (WINAPIV*)(struct CBsp*, uint32_t);
        using CBspSetAllAnimationState86_clbk = int64_t (WINAPIV*)(struct CBsp*, uint32_t, CBspSetAllAnimationState86_ptr);
        using CBspSetCFNormal87_ptr = void (WINAPIV*)(struct CBsp*);
        using CBspSetCFNormal87_clbk = void (WINAPIV*)(struct CBsp*, CBspSetCFNormal87_ptr);
        using CBspSetEventAnimationState88_ptr = int64_t (WINAPIV*)(struct CBsp*, uint16_t, uint32_t);
        using CBspSetEventAnimationState88_clbk = int64_t (WINAPIV*)(struct CBsp*, uint16_t, uint32_t, CBspSetEventAnimationState88_ptr);
        using CBspSetIsLoaded89_ptr = void (WINAPIV*)(struct CBsp*, int);
        using CBspSetIsLoaded89_clbk = void (WINAPIV*)(struct CBsp*, int, CBspSetIsLoaded89_ptr);
        using CBspSubLeafList90_ptr = void (WINAPIV*)(struct CBsp*, float, struct _BSP_NODE*, float*, float*, int16_t*, int*);
        using CBspSubLeafList90_clbk = void (WINAPIV*)(struct CBsp*, float, struct _BSP_NODE*, float*, float*, int16_t*, int*, CBspSubLeafList90_ptr);
        using CBspSubLeafListFromBBox91_ptr = void (WINAPIV*)(struct CBsp*, float, struct _BSP_NODE*, float**, int16_t*, int*);
        using CBspSubLeafListFromBBox91_clbk = void (WINAPIV*)(struct CBsp*, float, struct _BSP_NODE*, float**, int16_t*, int*, CBspSubLeafListFromBBox91_ptr);
        using CBspWalkLeaf92_ptr = void (WINAPIV*)(struct CBsp*, int16_t);
        using CBspWalkLeaf92_clbk = void (WINAPIV*)(struct CBsp*, int16_t, CBspWalkLeaf92_ptr);
        using CBspWalkNode93_ptr = void (WINAPIV*)(struct CBsp*, int16_t);
        using CBspWalkNode93_clbk = void (WINAPIV*)(struct CBsp*, int16_t, CBspWalkNode93_ptr);
        using CBspWalkNodeForLeafList94_ptr = void (WINAPIV*)(struct CBsp*, int8_t, int16_t, int16_t, float*, float);
        using CBspWalkNodeForLeafList94_clbk = void (WINAPIV*)(struct CBsp*, int8_t, int16_t, int16_t, float*, float, CBspWalkNodeForLeafList94_ptr);
        
        using CBspdtor_CBsp96_ptr = int64_t (WINAPIV*)(struct CBsp*);
        using CBspdtor_CBsp96_clbk = int64_t (WINAPIV*)(struct CBsp*, CBspdtor_CBsp96_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

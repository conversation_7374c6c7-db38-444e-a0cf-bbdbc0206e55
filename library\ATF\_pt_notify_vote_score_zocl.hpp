// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pt_notify_vote_score_zocl
    {
        struct __body
        {
            char byRank;
            char wszAvator<PERSON>ame[17];
            char byScoreRate;
        };
        char byRace;
        char byVoteRate;
        char byNonvoteRate;
        char byCnt;
        __body body[8];
    public:
        _pt_notify_vote_score_zocl();
        void ctor__pt_notify_vote_score_zocl();
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

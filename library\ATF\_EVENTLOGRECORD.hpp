// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _EVENTLOGRECORD
    {
        unsigned int Length;
        unsigned int Reserved;
        unsigned int RecordNumber;
        unsigned int TimeGenerated;
        unsigned int TimeWritten;
        unsigned int EventID;
        unsigned __int16 EventType;
        unsigned __int16 NumStrings;
        unsigned __int16 EventCategory;
        unsigned __int16 ReservedFlags;
        unsigned int ClosingRecordNumber;
        unsigned int StringOffset;
        unsigned int UserSidLength;
        unsigned int UserSidOffset;
        unsigned int DataLength;
        unsigned int DataOffset;
    };
END_ATF_NAMESPACE

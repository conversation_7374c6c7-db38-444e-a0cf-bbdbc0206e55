// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _guildroom_out_result_zocl
    {
        char byRetCode;
        char byMapIndex;
        unsigned __int16 wMapLayerIndex;
        __int16 sNewPos[3];
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_guildroom_out_result_zocl, 10>(), "_guildroom_out_result_zocl");
END_ATF_NAMESPACE

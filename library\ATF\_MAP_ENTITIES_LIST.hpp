// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CParticle.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _MAP_ENTITIES_LIST
    {
        unsigned __int16 ID;
        float Scale;
        float Pos[3];
        float RotX;
        float RotY;
        __int16 BBMin[3];
        __int16 BBMax[3];
        float AddFrame;
        CParticle *Particle;
        unsigned int Color;
        unsigned int mMapColor;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <DelayLoadProc.hpp>
#include <HINSTANCE__.hpp>
#include <ImgDelayDescr.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct DelayLoadInfo
    {
        unsigned int cb;
        ImgDelayDescr *pidd;
        __int64 (WINAPIV **ppfn)();
        const char *szDll;
        DelayLoadProc dlp;
        HINSTANCE__ *hmodCur;
        __int64 (WINAPIV *pfnCur)();
        unsigned int dwLastError;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

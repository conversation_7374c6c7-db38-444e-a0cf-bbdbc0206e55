// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _EFF_RATE
    {
        enum
        {
            GE_AttFc_ = 0,
            GA1 = 1,
            SK_AttFc_ = 2,
            SA1 = 3,
            FC_AttFc = 4,
            GE_Stun = 5,
            DefFc = 6,
            FP_Consum = 7,
            FC_CtDm_Tm = 8,
            HP_Mx = 9,
            FP_Mx = 10,
            SP_Mx = 11,
            HP_Absorb = 12,
            FP_Absorb = 13,
            HP_Rev = 14,
            FP_Rev = 15,
            SP_Rev = 16,
            Shield_Def = 17,
            HP_Potion_Rev = 18,
            FP_Potion_Rev = 19,
            SP_Potion_Rev = 20,
            Fg_Unit = 21,
            Fg_Animus = 22,
            FC_SiegeMode = 23,
            DEF_SiegeMode = 24,
            Part_Tol_ = 25,
            PT1 = 26,
            PT2 = 27,
            PT3 = 28,
            Fg_Lcr = 29,
            Fg_Def = 30,
            Fg_Rev = 31,
            Potion_Inc_Fc = 32,
            Potion_Dec_Def = 33,
            Potion_Exp_Rate = 34,
            Potion_Mastery_Rate = 35,
            Potion_Item_Drop_Rate = 36,
            Potion_Mine_Speed_Rate = 37,
            Skill_Exp_Rate = 38,
            SP_Absorb = 40,
            Unit_AttFc_byMst_Close_Fc = 41,
            Unit_AttFc_byMst_Long_Fc = 42,
            Unit_AttFc_byMst_FC = 43,
            Unit_AttFc_byMst_Def_Fc = 44,
            Animus_AttFc_ByMst_FC = 57,
            Animus_AttFc_ByMst_GE = 58,
            Animus_AttFc_ByMst_SK = 59,
            Animus_AttFc_ByMst_GA1 = 60,
            Animus_AttFc_ByMst_SA1 = 61,
            __END = 62,
        };
    };

    struct _EFF_HAVE
    {
        enum
        {
            Gamble_Prof = 0,
            Trade_Prof = 1,
            Exp_Prof = 2,
            Chat_All_Race = 3,
            Equip_Lv_Up = 4,
            Exp_Prof_Event_Item1 = 5,
            Mastery_Prof = 6,
            Mining_Speed_Prof = 7,
            Item_Drop_Prof = 8,
            Exp_Prof_Event_Item2 = 9,
            Trade_Buy_Prof = 10,
            Trade_Sell_Prof = 11,
            HP_Auto_Recover = 12,
            FP_Auto_Recover = 13,
            SP_Auto_Recover = 14,
            HP_Max_Inc = 15,
            FP_Max_Inc = 16,
            SP_Max_Inc = 17,
            All_Fc_Inc = 18,
            Near_GE_Fc = 19,
            Near_SK_Fc = 20,
            Far_GE_Fc = 21,
            Far_SK_Fc = 22,
            FC_Att_Fc = 23,
            All_Def_Fc = 24,
            Att_Dam_To_HP = 25,
            Att_Dam_To_FP = 26,
            Att_Dam_To_SP = 27,
            AP_Rate = 28,
            Near_AttFc_Add_Uint = 29,
            Far_AttFc_Add_Unit = 30,
            FC_AttFc_Add_Unit = 31,
            Def_Fc_Add_Unit = 32,
            Animus_AttFc = 33,
            Animus_Near_GE_AttFc = 34,
            Animus_Near_SK_AttFc = 35,
            Animus_Far_AttFc = 36,
            Animus_Far_SK_AttFc = 37,
            GE_AttFc_Inc_Cont = 38,
            SK_Dist_Inc_Cont = 39,
            Anti_CtDm_Cont = 40,
            Move_Spd_Inc_Cont = 41,
            Avd_Att_Ret_Dam = 42,
            Near_Hit_Rate = 43,
            Far_Hit_Rate = 44,
            FC_Hit_Rate = 45,
            SK_Hit_Rate = 46,
            Extend_Crt_Zone = 47,
            All_Hit_Rate = 48,
            All_Avd_Rate = 49,
            Name_Hide = 50,
            Resurrect_Of_Recvr = 51,
            Mob_Lv_Lmt_Extend = 52,
            Party_Mk_Lv_Extend = 53,
            Return_Dam_Cont = 54,
            Scroll_Lmt_Cancel = 55,
            All_Att_Avd = 56,
            Def_Block_Inc = 57,
            No_Delay_Potion = 58,
            Close_Mast_Inc = 59,
            Long_Mast_Inc = 60,
            FC_Mast_Inc = 61,
            Def_Mast_Inc = 62,
            Shld_Mast_Inc = 63,
            Animus_Mast_Inc = 64,
            Unit_Mast_Inc = 65,
            Exp_Prof_Event_Item3 = 71,
            Exp_Prof_Event_Item4 = 72,
            Drop_Prof_Item1 = 73,
            Drop_Prof_Item2 = 74,
            Drop_Prof_Item3 = 75,
            Detect_Jade = 76,
            Cont_Dem_Time_Dec = 77,
            Dst_No_Shd_Rate = 78,
            Mugi_Talic_Eff = 79,
            Grace_Talic_Eff = 80,
            Mastery_Prof_OverLap = 82,
            __END = 83,
        };
    };

    struct _EFF_STATE
    {
        enum
        {
            SK_Att_Lck = 0,
            FC_Att_Lck = 1,
            SK_CtHp_Lck = 2,
            FC_CtHp_Lck = 3,
            FC_CtDm_Lck = 4,
            Stealth = 5,
            Move_Lck = 6,
            Run_Lck = 7,
            Abs_Avd = 8,
            Abs_Crt = 9,
            Rev_Lck = 10,
            Dst_No_Shd = 11,
            Dst_No_Def = 12,
            Dst_Make_Stun = 13,
            Res_Att = 14,
            HP_Rcv_Lck = 15,
            FP_Rcv_Lck = 16,
            SP_Rcv_Lck = 17,
            View_Lck = 18,
            Tol_Minus = 19,
            Stone_Lck = 20,
            Suspend_Lck = 21,
            Force_Shield = 22,
            Find_Trap = 23,
            Solitude = 24,
            Remove_CtDm_Lck = 25,
            Invisible = 26,
            Invincible = 28,
            __END = 29,
        };
    };

    struct _EFF_PLUS
    {
        enum
        {
            GE_Hit_ = 0,
            GH1 = 1,
            Lcr_Hit = 2,
            GE_Avd = 3,
            GE_Att_Dist_ = 4,
            GAD1 = 5,
            SK_Att_Dist_ = 6,
            SAD1 = 7,
            FC_Att_Dist = 8,
            GE_Att_Spd_ = 9,
            GAS1 = 10,
            Lcr_Att_Spd = 11,
            SK_Spd = 12,
            FC_Spd = 13,
            GE_CrtExt = 14,
            Part_Tol_ = 15,
            PT1 = 16,
            PT2 = 17,
            PT3 = 18,
            SK_LvUp = 19,
            Move_Run_Spd = 20,
            Transparency = 21,
            Detect = 22,
            Anti_CtDm = 23,
            Know_WeakPoint = 24,
            FP_Rev_Add = 25,
            View_Circle = 26,
            Avoid_Con = 27,
            Dst_Shd = 28,
            DEF_ShdDefRate = 29,
            SK_HitRate = 30,
            FC_HitRate = 31,
            HP_Per2Sec = 32,
            FP_Per2Sec = 33,
            SP_Per2Sec = 34,
            DP_Max = 35,
            Lcr_Att_Dist = 36,
            All_AvdCrt = 37,
            Con_Eff_CancelRate = 38,
            Fg_Crt = 39,
            Potion_Inc_All_Hit = 40,
            Potion_Inc_Ignore_Sheild = 41,
            __END = 42,
        };
    };

    #pragma pack(push, 8)
    struct _effect_parameter
    {
        struct __param_data
        {
            float m_fEff_Rate[62];
            float m_fEff_Plus[42];
            char m_bEff_State[29];
            float m_fEff_Have[83];
        };
        __param_data *m_pDataParam;
        bool m_bLock;
    public:
        void AllocEffParam();
        float GetEff_Have(int nParamIndex);
        float GetEff_Plus(int nParamIndex);
        float GetEff_Rate(int nParamIndex);
        bool GetEff_State(int nParamIndex);
        void InitEffHave();
        void InitEffParam();
        bool SetEff_Plus(int nParamIndex, float fVar, bool bAdd);
        bool SetEff_Rate(int nParamIndex, float fVar, bool bAdd);
        bool SetEff_State(int nParamIndex, bool bVar);
        void SetLock(bool bLock);
        _effect_parameter();
        void ctor__effect_parameter();
        ~_effect_parameter();
        void dtor__effect_parameter();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_effect_parameter, 16>(), "_effect_parameter");
END_ATF_NAMESPACE

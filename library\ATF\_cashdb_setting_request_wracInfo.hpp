// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_cashdb_setting_request_wrac.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _cashdb_setting_request_wracsize2_ptr = int (WINAPIV*)(struct _cashdb_setting_request_wrac*);
        using _cashdb_setting_request_wracsize2_clbk = int (WINAPIV*)(struct _cashdb_setting_request_wrac*, _cashdb_setting_request_wracsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _CRYMSG_DB_BASE
    {
        struct _LIST
        {
            char wszCryMsg[65];
        public:
            void Init();
            _LIST();
            void ctor__LIST();
        };
        _LIST m_List[10];
    public:
        void Init();
        _CRYMSG_DB_BASE();
        void ctor__CRYMSG_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_CRYMSG_DB_BASE, 650>(), "_CRYMSG_DB_BASE");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_con_event_.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _con_event_ctor__con_event_2_ptr = void (WINAPIV*)(struct _con_event_*);
        using _con_event_ctor__con_event_2_clbk = void (WINAPIV*)(struct _con_event_*, _con_event_ctor__con_event_2_ptr);
        
        using _con_event_dtor__con_event_4_ptr = void (WINAPIV*)(struct _con_event_*);
        using _con_event_dtor__con_event_4_clbk = void (WINAPIV*)(struct _con_event_*, _con_event_dtor__con_event_4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBossMsgController.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRaceBossMsgControllerctor_CRaceBossMsgController2_ptr = void (WINAPIV*)(struct CRaceBossMsgController*);
        using CRaceBossMsgControllerctor_CRaceBossMsgController2_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, CRaceBossMsgControllerctor_CRaceBossMsgController2_ptr);
        using CRaceBossMsgControllerCancel4_ptr = bool (WINAPIV*)(struct CRaceBossMsgController*, char, unsigned int);
        using CRaceBossMsgControllerCancel4_clbk = bool (WINAPIV*)(struct CRaceBossMsgController*, char, unsigned int, CRaceBossMsgControllerCancel4_ptr);
        using CRaceBossMsgControllerCancel6_ptr = bool (WINAPIV*)(struct CRaceBossMsgController*, char, unsigned int, struct CPlayer*);
        using CRaceBossMsgControllerCancel6_clbk = bool (WINAPIV*)(struct CRaceBossMsgController*, char, unsigned int, struct CPlayer*, CRaceBossMsgControllerCancel6_ptr);
        using CRaceBossMsgControllerCleanUp8_ptr = void (WINAPIV*)(struct CRaceBossMsgController*);
        using CRaceBossMsgControllerCleanUp8_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, CRaceBossMsgControllerCleanUp8_ptr);
        using CRaceBossMsgControllerDestroy10_ptr = void (WINAPIV*)();
        using CRaceBossMsgControllerDestroy10_clbk = void (WINAPIV*)(CRaceBossMsgControllerDestroy10_ptr);
        using CRaceBossMsgControllerGetCurDay12_ptr = int (WINAPIV*)(struct CRaceBossMsgController*);
        using CRaceBossMsgControllerGetCurDay12_clbk = int (WINAPIV*)(struct CRaceBossMsgController*, CRaceBossMsgControllerGetCurDay12_ptr);
        using CRaceBossMsgControllerInit14_ptr = bool (WINAPIV*)(struct CRaceBossMsgController*);
        using CRaceBossMsgControllerInit14_clbk = bool (WINAPIV*)(struct CRaceBossMsgController*, CRaceBossMsgControllerInit14_ptr);
        using CRaceBossMsgControllerInstance16_ptr = struct CRaceBossMsgController* (WINAPIV*)();
        using CRaceBossMsgControllerInstance16_clbk = struct CRaceBossMsgController* (WINAPIV*)(CRaceBossMsgControllerInstance16_ptr);
        using CRaceBossMsgControllerIsDayChanged18_ptr = bool (WINAPIV*)(struct CRaceBossMsgController*);
        using CRaceBossMsgControllerIsDayChanged18_clbk = bool (WINAPIV*)(struct CRaceBossMsgController*, CRaceBossMsgControllerIsDayChanged18_ptr);
        using CRaceBossMsgControllerLoadCurTime20_ptr = bool (WINAPIV*)(struct CRaceBossMsgController*, unsigned int*);
        using CRaceBossMsgControllerLoadCurTime20_clbk = bool (WINAPIV*)(struct CRaceBossMsgController*, unsigned int*, CRaceBossMsgControllerLoadCurTime20_ptr);
        using CRaceBossMsgControllerOnLoop22_ptr = void (WINAPIV*)(struct CRaceBossMsgController*);
        using CRaceBossMsgControllerOnLoop22_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, CRaceBossMsgControllerOnLoop22_ptr);
        using CRaceBossMsgControllerSaveCurTime24_ptr = void (WINAPIV*)(struct CRaceBossMsgController*);
        using CRaceBossMsgControllerSaveCurTime24_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, CRaceBossMsgControllerSaveCurTime24_ptr);
        using CRaceBossMsgControllerSend26_ptr = bool (WINAPIV*)(struct CRaceBossMsgController*, struct CPlayer*, char*);
        using CRaceBossMsgControllerSend26_clbk = bool (WINAPIV*)(struct CRaceBossMsgController*, struct CPlayer*, char*, CRaceBossMsgControllerSend26_ptr);
        using CRaceBossMsgControllerSend28_ptr = bool (WINAPIV*)(struct CRaceBossMsgController*, char, unsigned int, char*, char*, unsigned int);
        using CRaceBossMsgControllerSend28_clbk = bool (WINAPIV*)(struct CRaceBossMsgController*, char, unsigned int, char*, char*, unsigned int, CRaceBossMsgControllerSend28_ptr);
        using CRaceBossMsgControllerSendCancelWeb30_ptr = void (WINAPIV*)(struct CRaceBossMsgController*, char, struct RACE_BOSS_MSG::CMsg*);
        using CRaceBossMsgControllerSendCancelWeb30_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, char, struct RACE_BOSS_MSG::CMsg*, CRaceBossMsgControllerSendCancelWeb30_ptr);
        using CRaceBossMsgControllerSendCancleInfomManager32_ptr = void (WINAPIV*)(struct CRaceBossMsgController*, uint16_t, char, unsigned int, char*);
        using CRaceBossMsgControllerSendCancleInfomManager32_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, uint16_t, char, unsigned int, char*, CRaceBossMsgControllerSendCancleInfomManager32_ptr);
        using CRaceBossMsgControllerSendCancleInfomSender34_ptr = void (WINAPIV*)(struct CRaceBossMsgController*, unsigned int);
        using CRaceBossMsgControllerSendCancleInfomSender34_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, unsigned int, CRaceBossMsgControllerSendCancleInfomSender34_ptr);
        using CRaceBossMsgControllerSendComfirmWeb36_ptr = void (WINAPIV*)(struct CRaceBossMsgController*, char, struct RACE_BOSS_MSG::CMsg*);
        using CRaceBossMsgControllerSendComfirmWeb36_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, char, struct RACE_BOSS_MSG::CMsg*, CRaceBossMsgControllerSendComfirmWeb36_ptr);
        using CRaceBossMsgControllerSendConfirmCtrl38_ptr = void (WINAPIV*)(struct CRaceBossMsgController*, char, struct RACE_BOSS_MSG::CMsg*);
        using CRaceBossMsgControllerSendConfirmCtrl38_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, char, struct RACE_BOSS_MSG::CMsg*, CRaceBossMsgControllerSendConfirmCtrl38_ptr);
        using CRaceBossMsgControllerSendInfomSender40_ptr = void (WINAPIV*)(struct CRaceBossMsgController*, unsigned int, char);
        using CRaceBossMsgControllerSendInfomSender40_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, unsigned int, char, CRaceBossMsgControllerSendInfomSender40_ptr);
        using CRaceBossMsgControllerSendMsgRequestResult42_ptr = void (WINAPIV*)(struct CRaceBossMsgController*, uint16_t, char);
        using CRaceBossMsgControllerSendMsgRequestResult42_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, uint16_t, char, CRaceBossMsgControllerSendMsgRequestResult42_ptr);
        using CRaceBossMsgControllerSendRequestWeb44_ptr = void (WINAPIV*)(struct CRaceBossMsgController*, char, struct RACE_BOSS_MSG::CMsg*);
        using CRaceBossMsgControllerSendRequestWeb44_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, char, struct RACE_BOSS_MSG::CMsg*, CRaceBossMsgControllerSendRequestWeb44_ptr);
        using CRaceBossMsgControllerSendWebRaceBossSMSErrorResult46_ptr = void (WINAPIV*)(struct CRaceBossMsgController*, int, unsigned int);
        using CRaceBossMsgControllerSendWebRaceBossSMSErrorResult46_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, int, unsigned int, CRaceBossMsgControllerSendWebRaceBossSMSErrorResult46_ptr);
        using CRaceBossMsgControllerUpdateSend48_ptr = void (WINAPIV*)(struct CRaceBossMsgController*);
        using CRaceBossMsgControllerUpdateSend48_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, CRaceBossMsgControllerUpdateSend48_ptr);
        
        using CRaceBossMsgControllerdtor_CRaceBossMsgController52_ptr = void (WINAPIV*)(struct CRaceBossMsgController*);
        using CRaceBossMsgControllerdtor_CRaceBossMsgController52_clbk = void (WINAPIV*)(struct CRaceBossMsgController*, CRaceBossMsgControllerdtor_CRaceBossMsgController52_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RPC_SYNTAX_IDENTIFIER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _RPC_MESSAGE
    {
        void *Handle;
        unsigned int DataRepresentation;
        void *Buffer;
        unsigned int BufferLength;
        unsigned int ProcNum;
        _RPC_SYNTAX_IDENTIFIER *TransferSyntax;
        void *RpcInterfaceInformation;
        void *ReservedForRuntime;
        void *ManagerEpv;
        void *ImportContext;
        unsigned int RpcFlags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

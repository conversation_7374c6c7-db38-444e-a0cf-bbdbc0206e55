// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleReservedScheduleMapGroup.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int, unsigned int, struct GUILD_BATTLE::CGuildBattleSchedule**, unsigned int*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int, unsigned int, struct GUILD_BATTLE::C<PERSON><PERSON>BattleSchedule**, unsigned int*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_ptr);
            
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, bool*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, bool*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, bool);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, bool, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_ptr);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_ptr = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_clbk = struct GUILD_BATTLE::CGuildBattleSchedule* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_ptr);
            
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*);
            using GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup*, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

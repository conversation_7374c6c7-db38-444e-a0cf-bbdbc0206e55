// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGEHLP_STACK_FRAME
    {
        unsigned __int64 InstructionOffset;
        unsigned __int64 ReturnOffset;
        unsigned __int64 FrameOffset;
        unsigned __int64 StackOffset;
        unsigned __int64 BackingStoreOffset;
        unsigned __int64 FuncTableEntry;
        unsigned __int64 Params[4];
        unsigned __int64 Reserved[5];
        int Virtual;
        unsigned int Reserved2;
    };    
    static_assert(ATF::checkSize<_IMAGEHLP_STACK_FRAME, 128>(), "_IMAGEHLP_STACK_FRAME");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTraceFileAndLineInfo.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            
            using ATL__CTraceFileAndLineInfoctor_CTraceFileAndLineInfo2_ptr = void (WINAPIV*)(struct ATL::CTraceFileAndLineInfo*, char*, int);
            using ATL__CTraceFileAndLineInfoctor_CTraceFileAndLineInfo2_clbk = void (WINAPIV*)(struct ATL::CTraceFileAndLineInfo*, char*, int, ATL__CTraceFileAndLineInfoctor_CTraceFileAndLineInfo2_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

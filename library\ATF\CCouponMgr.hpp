// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <CouponInfo.hpp>
#include <_PCBANG_PLAY_TIME.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct CCouponMgr
    {
        CouponInfo m_Coupon[5];
        char m_byRemainTime;
        char m_byReceiveCoupon;
        unsigned int m_dwContTime;
        char m_byInitTime;
        bool m_bTimeReset;
        CMyTimer m_tmrCheckConnMin;
        CMyTimer m_tmrCouponEnableTime;
        _PCBANG_PLAY_TIME *m_pkInfo;
    public:
        CCouponMgr();
        void ctor_CCouponMgr();
        struct CouponInfo* GetCouponInfo(char byCouponTime);
        void Init(uint16_t wIdx);
        void InitCuponInfo();
        bool IsEnableGetCoupon();
        void LoadData(unsigned int dwAccSerial, struct _PCBANG_PLAY_TIME* pkInfo);
        void LogOut(bool bForceClose);
        void Loop(uint16_t wIdx);
        void ReceivePrimiumCoupon(uint16_t wIdx);
        void SendMsg_CouponEnsure(uint16_t wIdx, char byCouponTime);
        void SendMsg_CouponError(uint16_t wIdx, char byRet);
        void SendMsg_CouponLendResult(uint16_t wIndx, struct _STORAGE_LIST::_db_con* pCoupon);
        void SendMsg_InPcBangTime(uint16_t wIdx);
        void SendMsg_RemainCouponInform(uint16_t wIdx, char byRemainCoupon);
        bool SetCheetContTime(uint16_t wIdx, int nMin);
        ~CCouponMgr();
        void dtor_CCouponMgr();
    };    
    static_assert(ATF::checkSize<CCouponMgr, 112>(), "CCouponMgr");
END_ATF_NAMESPACE

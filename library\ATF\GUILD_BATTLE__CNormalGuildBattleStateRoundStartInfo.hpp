// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateRoundStart.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartctor_CNormalGuildBattleStateRoundStart2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartctor_CNormalGuildBattleStateRoundStart2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*, GUILD_BATTLE__CNormalGuildBattleStateRoundStartctor_CNormalGuildBattleStateRoundStart2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartEnter4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartEnter4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundStartEnter4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartFin6_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartFin6_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundStartFin6_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartLoop8_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartLoop8_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundStartLoop8_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartdtor_CNormalGuildBattleStateRoundStart10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundStartdtor_CNormalGuildBattleStateRoundStart10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundStart*, GUILD_BATTLE__CNormalGuildBattleStateRoundStartdtor_CNormalGuildBattleStateRoundStart10_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _record_bin_header
    {
        int m_nRecordNum;
        int m_nFieldNum;
        int m_nRecordSize;
    public:
        _record_bin_header();
        void ctor__record_bin_header();
    };    
    static_assert(ATF::checkSize<_record_bin_header, 12>(), "_record_bin_header");
END_ATF_NAMESPACE

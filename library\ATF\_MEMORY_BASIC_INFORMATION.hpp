// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _MEMORY_BASIC_INFORMATION
    {
        void *BaseAddress;
        void *AllocationBase;
        unsigned int AllocationProtect;
        unsigned __int64 RegionSize;
        unsigned int State;
        unsigned int Protect;
        unsigned int Type;
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_MEMORY_BASIC_INFORMATION, 48>(), "_MEMORY_BASIC_INFORMATION");
END_ATF_NAMESPACE

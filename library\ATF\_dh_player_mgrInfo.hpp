// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dh_player_mgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _dh_player_mgrInit2_ptr = void (WINAPIV*)(struct _dh_player_mgr*);
        using _dh_player_mgrInit2_clbk = void (WINAPIV*)(struct _dh_player_mgr*, _dh_player_mgrInit2_ptr);
        using _dh_player_mgrIsFill4_ptr = bool (WINAPIV*)(struct _dh_player_mgr*);
        using _dh_player_mgrIsFill4_clbk = bool (WINAPIV*)(struct _dh_player_mgr*, _dh_player_mgrIsFill4_ptr);
        
        using _dh_player_mgrctor__dh_player_mgr6_ptr = void (WINAPIV*)(struct _dh_player_mgr*);
        using _dh_player_mgrctor__dh_player_mgr6_clbk = void (WINAPIV*)(struct _dh_player_mgr*, _dh_player_mgrctor__dh_player_mgr6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

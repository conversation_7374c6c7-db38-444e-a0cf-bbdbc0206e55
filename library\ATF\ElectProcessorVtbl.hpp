// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Cmd.hpp>


START_ATF_NAMESPACE
    struct ElectProcessorVtbl
    {
        void *(WINAPIV *__vecDelDtor)(struct ElectProcessor *_this, unsigned int);
        bool (WINAPIV *Initialize)(struct ElectProcessor *_this);
        int (WINAPIV *Doit)(struct ElectProcessor *_this, Cmd, struct CPlayer *, char *);
    };
END_ATF_NAMESPACE

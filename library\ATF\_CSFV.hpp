// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IShellFolder.hpp>
#include <IShellView.hpp>
#include <_ITEMIDLIST.hpp>
#include <__MIDL___MIDL_itf_shobjidl_0202_0002.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _CSFV
    {
        unsigned int cbSize;
        IShellFolder *pshf;
        IShellView *psvOuter;
        _ITEMIDLIST *pidl;
        int lEvents;
        HRESULT (WINAPIV *pfnCallback)(IShellView *, IShellFolder *, HWND__ *, unsigned int, unsigned __int64, __int64);
        __MIDL___MIDL_itf_shobjidl_0202_0002 fvm;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

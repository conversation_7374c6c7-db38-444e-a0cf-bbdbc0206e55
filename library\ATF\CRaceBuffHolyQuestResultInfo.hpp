// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CRaceBuffHolyQuestResultInfo
    {
        bool m_bSetBuff;
        char m_byCurWinRace;
        char m_byCurFailRace;
        char m_byCurLoseRace;
        char m_byOldWinRace;
        char m_byOldFailRace;
        char m_byOldLoseRace;
        unsigned int m_uiContinueWinCnt;
        unsigned int m_uiContinueFailCnt;
        unsigned int m_uiContinueLoseCnt;
    public:
        CRaceBuffHolyQuestResultInfo();
        void ctor_CRaceBuffHolyQuestResultInfo();
        void ClearResult();
        bool FindFailRace(char byWinRace, char byLoseRace, char* pbyFailRace);
        unsigned int GetContinueCnt(int iType);
        int GetResultType(char byRace, bool bGetScanner);
        bool IsChaos();
        bool IsValidResult();
        bool Load();
        bool LoadINI();
        bool Save();
        bool SaveINI();
        bool SaveINISubProcSaveNum(char* szItem, char byNum);
        void SetBuffFlag();
        void SetResult(char byWinRace, char byLoseRace, unsigned int uiThMax);
        bool SetResultSubProcSetRace(char byWinRace, char byLoseRace, unsigned int uiThMax);
        ~CRaceBuffHolyQuestResultInfo();
        void dtor_CRaceBuffHolyQuestResultInfo();
    };
END_ATF_NAMESPACE

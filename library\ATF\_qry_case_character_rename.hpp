// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS_INDIV.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_character_rename
    {
        unsigned int dwCharSerial;
        unsigned int dwAlreadySerial;
        _STORAGE_POS_INDIV ItemInfo;
        char wszCharName[17];
        char wszOldName[17];
    public:
        _qry_case_character_rename();
        void ctor__qry_case_character_rename();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_character_rename, 48>(), "_qry_case_character_rename");
END_ATF_NAMESPACE

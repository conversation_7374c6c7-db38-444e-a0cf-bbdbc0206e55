// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _raceboss_msg_confirm_zowb
    {
        int nCountIndex;
        int nWorldCode;
        char byRaceCode;
        char wszMasterName[17];
        char wszMsg[255];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

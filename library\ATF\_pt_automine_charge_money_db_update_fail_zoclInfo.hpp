// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_pt_automine_charge_money_db_update_fail_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _pt_automine_charge_money_db_update_fail_zoclsize2_ptr = int (WINAPIV*)(struct _pt_automine_charge_money_db_update_fail_zocl*);
        using _pt_automine_charge_money_db_update_fail_zoclsize2_clbk = int (WINAPIV*)(struct _pt_automine_charge_money_db_update_fail_zocl*, _pt_automine_charge_money_db_update_fail_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

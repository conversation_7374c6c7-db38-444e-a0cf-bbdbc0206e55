// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <tagIDLDESC.hpp>
#include <tagTYPEDESC.hpp>



START_ATF_NAMESPACE
    struct tagTYPEATTR
    {
        _GUID guid;
        unsigned int lcid;
        unsigned int dwReserved;
        int memidConstructor;
        int memidDestructor;
        wchar_t *lpstrSchema;
        unsigned int cbSizeInstance;
        tagTYPEKIND typekind;
        unsigned __int16 cFuncs;
        unsigned __int16 cVars;
        unsigned __int16 cImplTypes;
        unsigned __int16 cbSizeVft;
        unsigned __int16 cbAlignment;
        unsigned __int16 wTypeFlags;
        unsigned __int16 wMajorVerNum;
        unsigned __int16 wMinorVerNum;
        tagTYPEDESC tdescAlias;
        tagIDLDESC idldescType;
    };
END_ATF_NAMESPACE

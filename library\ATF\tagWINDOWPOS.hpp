// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagWINDOWPOS
    {
        HWND__ *hwnd;
        HWND__ *hwndInsertAfter;
        int x;
        int y;
        int cx;
        int cy;
        unsigned int flags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

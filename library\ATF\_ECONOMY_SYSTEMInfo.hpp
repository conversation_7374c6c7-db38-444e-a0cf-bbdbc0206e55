// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ECONOMY_SYSTEM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _ECONOMY_SYSTEMCurTradeMoneyInit2_ptr = void (WINAPIV*)(struct _ECONOMY_SYSTEM*);
        using _ECONOMY_SYSTEMCurTradeMoneyInit2_clbk = void (WINAPIV*)(struct _ECONOMY_SYSTEM*, _ECONOMY_SYSTEMCurTradeMoneyInit2_ptr);
        using _ECONOMY_SYSTEMInit4_ptr = void (WINAPIV*)(struct _ECONOMY_SYSTEM*);
        using _ECONOMY_SYSTEMInit4_clbk = void (WINAPIV*)(struct _ECONOMY_SYSTEM*, _ECONOMY_SYSTEMInit4_ptr);
        
        using _ECONOMY_SYSTEMctor__ECONOMY_SYSTEM6_ptr = void (WIN<PERSON>IV*)(struct _ECONOMY_SYSTEM*);
        using _ECONOMY_SYSTEMctor__ECONOMY_SYSTEM6_clbk = void (WINAPIV*)(struct _ECONOMY_SYSTEM*, _ECONOMY_SYSTEMctor__ECONOMY_SYSTEM6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

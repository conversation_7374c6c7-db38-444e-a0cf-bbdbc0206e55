// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataTW.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataTWctor_CNationSettingDataTW2_ptr = void (WINAPIV*)(struct CNationSettingDataTW*);
        using CNationSettingDataTWctor_CNationSettingDataTW2_clbk = void (WINAPIV*)(struct CNationSettingDataTW*, CNationSettingDataTWctor_CNationSettingDataTW2_ptr);
        using CNationSettingDataTWCreateWorker4_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataTW*);
        using CNationSettingDataTWCreateWorker4_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataTW*, CNationSettingDataTWCreateWorker4_ptr);
        using CNationSettingDataTWGetCashItemPrice6_ptr = int (WINAPIV*)(struct CNationSettingDataTW*, struct _CashShop_str_fld*);
        using CNationSettingDataTWGetCashItemPrice6_clbk = int (WINAPIV*)(struct CNationSettingDataTW*, struct _CashShop_str_fld*, CNationSettingDataTWGetCashItemPrice6_ptr);
        using CNationSettingDataTWGetItemName8_ptr = char* (WINAPIV*)(struct CNationSettingDataTW*, struct _NameTxt_fld*);
        using CNationSettingDataTWGetItemName8_clbk = char* (WINAPIV*)(struct CNationSettingDataTW*, struct _NameTxt_fld*, CNationSettingDataTWGetItemName8_ptr);
        using CNationSettingDataTWInit10_ptr = int (WINAPIV*)(struct CNationSettingDataTW*);
        using CNationSettingDataTWInit10_clbk = int (WINAPIV*)(struct CNationSettingDataTW*, CNationSettingDataTWInit10_ptr);
        using CNationSettingDataTWIsPersonalFreeFixedAmountBillingType12_ptr = bool (WINAPIV*)(struct CNationSettingDataTW*, int16_t*, int16_t*);
        using CNationSettingDataTWIsPersonalFreeFixedAmountBillingType12_clbk = bool (WINAPIV*)(struct CNationSettingDataTW*, int16_t*, int16_t*, CNationSettingDataTWIsPersonalFreeFixedAmountBillingType12_ptr);
        using CNationSettingDataTWReadSystemPass14_ptr = bool (WINAPIV*)(struct CNationSettingDataTW*);
        using CNationSettingDataTWReadSystemPass14_clbk = bool (WINAPIV*)(struct CNationSettingDataTW*, CNationSettingDataTWReadSystemPass14_ptr);
        using CNationSettingDataTWValidMacAddress16_ptr = bool (WINAPIV*)(struct CNationSettingDataTW*);
        using CNationSettingDataTWValidMacAddress16_clbk = bool (WINAPIV*)(struct CNationSettingDataTW*, CNationSettingDataTWValidMacAddress16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

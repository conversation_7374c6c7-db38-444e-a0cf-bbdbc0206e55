// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_TRUSTEE_W.hpp>



START_ATF_NAMESPACE
    struct _ACTRL_ACCESS_ENTRYW
    {
        _TRUSTEE_W Trustee;
        unsigned int fAccessFlags;
        unsigned int Access;
        unsigned int ProvSpecificAccess;
        unsigned int Inheritance;
        wchar_t *lpInheritProperty;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CStdioFile.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CInternetFile : CStdioFile
    {
        void *m_hFile;
        int m_bReadMode;
        unsigned __int64 m_dwContext;
        void *m_hConnection;
        ATL::CStringT<char> m_strServerName;
        unsigned int m_nWriteBufferSize;
        unsigned int m_nWriteBufferPos;
        char *m_pbWriteBuffer;
        unsigned int m_nReadBufferSize;
        unsigned int m_nReadBufferPos;
        char *m_pbReadBuffer;
        unsigned int m_nReadBufferBytes;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

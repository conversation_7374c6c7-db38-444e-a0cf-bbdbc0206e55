// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$E5EC784184F61E2D1DB730C15D65FB8F.hpp>
#include <HINSTANCE__.hpp>
#include <HKEY__.hpp>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    struct _SHELLEXECUTEINFOW
    {
        unsigned int cbSize;
        unsigned int fMask;
        HWND__ *hwnd;
        const wchar_t *lpVerb;
        const wchar_t *lpFile;
        const wchar_t *lpParameters;
        const wchar_t *lpDirectory;
        int nShow;
        HINSTANCE__ *hInstApp;
        void *lpIDList;
        const wchar_t *lpClass;
        HKEY__ *hkeyClass;
        unsigned int dwHotKey;
        $E5EC784184F61E2D1DB730C15D65FB8F ___u13;
        void *hProcess;
    };
END_ATF_NAMESPACE

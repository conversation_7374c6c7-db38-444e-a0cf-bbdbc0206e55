// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ONE_LAYER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _R3MATERIAL
    {
        int m_iMatNum;
        char m_name[128];
        unsigned int m_dwFlag;
        int m_iDetailSurface;
        float m_fDetailScale;
        unsigned int m_dwLayerNum;
        _ONE_LAYER m_Layer[7];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

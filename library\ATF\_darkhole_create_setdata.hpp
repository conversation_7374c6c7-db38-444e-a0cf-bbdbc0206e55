// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <_object_create_setdata.hpp>


START_ATF_NAMESPACE
    struct  _darkhole_create_setdata : _object_create_setdata
    {
        CPlayer *pOpener;
    public:
        _darkhole_create_setdata();
        void ctor__darkhole_create_setdata();
    };    
    static_assert(ATF::checkSize<_darkhole_create_setdata, 40>(), "_darkhole_create_setdata");
END_ATF_NAMESPACE

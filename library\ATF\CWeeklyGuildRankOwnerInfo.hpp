// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CWeeklyGuildRankOwnerInfo
    {
        unsigned int m_dwSerial;
        char m_wszGuildName[17];
        char m_byRace;
        unsigned __int16 m_wRank;
        char m_byGrade;
        long double m_dKillPvpPoint;
        long double m_dGuildBattlePvpPoint;
        unsigned int m_dwSumLv;
    public:
        CWeeklyGuildRankOwnerInfo();
        void ctor_CWeeklyGuildRankOwnerInfo();
        void Clear();
        bool IsEmpty();
        ~CWeeklyGuildRankOwnerInfo();
        void dtor_CWeeklyGuildRankOwnerInfo();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

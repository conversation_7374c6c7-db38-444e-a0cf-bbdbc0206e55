// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _darkhole_open_result_zocl
    {
        char byRetCode;
        unsigned __int16 wHoleIndex;
        unsigned int dwHoleSerial;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_darkhole_open_result_zocl, 7>(), "_darkhole_open_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_unmandtrader_re_registsingleitem
    {
        struct __list
        {
            char byProcRet;
            bool bRegist;
            unsigned __int16 wItemSerial;
            unsigned int dwTax;
            unsigned int dwListIndex;
            char byClass1;
            char byClass2;
            char byClass3;
            unsigned int dwPrice;
            unsigned int dwRegistSerial;
            char byUpdateState;
        };
        char byType;
        unsigned __int16 wInx;
        char byNum;
        unsigned int dwOwnerSerial;
        __list List[10];
    public:
        _qry_case_unmandtrader_re_registsingleitem();
        void ctor__qry_case_unmandtrader_re_registsingleitem();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_unmandtrader_re_registsingleitem, 292>(), "_qry_case_unmandtrader_re_registsingleitem");
END_ATF_NAMESPACE

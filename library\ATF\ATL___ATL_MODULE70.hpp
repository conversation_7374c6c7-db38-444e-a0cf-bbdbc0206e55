// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CComCriticalSection.hpp>
#include <ATL___ATL_TERMFUNC_ELEM.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct _ATL_MODULE70
        {
            unsigned int cbSize;
            int m_nLockCnt;
            _ATL_TERMFUNC_ELEM *m_pTermFuncs;
            CComCriticalSection m_csStaticDataInitAndTypeInfo;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <US__AbstractThread.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        #pragma pack(push, 8)
        template<typename _Interface>
        struct  CWinThread : AbstractThread
        {
            volatile bool m_bRunning;
            _Interface m_ThreadParam;
            void *m_hThread;
            void *m_hStartupEvent;
            void *m_hDestroyEvent;
            unsigned int m_dwThreadID;
        };
        #pragma pack(pop)
    }; // end namespace US
END_ATF_NAMESPACE

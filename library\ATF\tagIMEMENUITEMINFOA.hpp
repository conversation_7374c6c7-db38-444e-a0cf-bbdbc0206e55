// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HBITMAP__.hpp>


START_ATF_NAMESPACE
    struct tagIMEMENUITEMINFOA
    {
        unsigned int cbSize;
        unsigned int fType;
        unsigned int fState;
        unsigned int wID;
        HBITMAP__ *hbmpChecked;
        HBITMAP__ *hbmpUnchecked;
        unsigned int dwItemData;
        char szString[80];
        HBITMAP__ *hbmpItem;
    };
END_ATF_NAMESPACE

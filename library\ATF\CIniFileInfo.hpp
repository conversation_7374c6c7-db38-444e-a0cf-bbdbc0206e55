// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CIniFile.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CIniFilector_CIniFile2_ptr = void (WINAPIV*)(struct CIniFile*, char*);
        using CIniFilector_CIniFile2_clbk = void (WINAPIV*)(struct CIniFile*, char*, CIniFilector_CIniFile2_ptr);
        
        using CIniFilector_CIniFile4_ptr = void (WINAPIV*)(struct CIniFile*);
        using CIniFilector_CIniFile4_clbk = void (WINAPIV*)(struct CIniFile*, CIniFilector_CIniFile4_ptr);
        using CIniFileClear6_ptr = void (WINAPIV*)(struct CIniFile*);
        using CIniFileClear6_clbk = void (WINAPIV*)(struct CIniFile*, CIniFileClear6_ptr);
        using CIniFileDeleteKey8_ptr = bool (WINAPIV*)(struct CIniFile*, struct INI_Key*);
        using CIniFileDeleteKey8_clbk = bool (WINAPIV*)(struct CIniFile*, struct INI_Key*, CIniFileDeleteKey8_ptr);
        using CIniFileDeleteKey10_ptr = int (WINAPIV*)(struct CIniFile*, char*, char*);
        using CIniFileDeleteKey10_clbk = int (WINAPIV*)(struct CIniFile*, char*, char*, CIniFileDeleteKey10_ptr);
        using CIniFileDeleteSection12_ptr = bool (WINAPIV*)(struct CIniFile*, struct INI_Section*);
        using CIniFileDeleteSection12_clbk = bool (WINAPIV*)(struct CIniFile*, struct INI_Section*, CIniFileDeleteSection12_ptr);
        using CIniFileDeleteSection14_ptr = int (WINAPIV*)(struct CIniFile*, char*);
        using CIniFileDeleteSection14_clbk = int (WINAPIV*)(struct CIniFile*, char*, CIniFileDeleteSection14_ptr);
        using CIniFileGetKey16_ptr = struct INI_Key* (WINAPIV*)(struct CIniFile*, char*, char*);
        using CIniFileGetKey16_clbk = struct INI_Key* (WINAPIV*)(struct CIniFile*, char*, char*, CIniFileGetKey16_ptr);
        using CIniFileGetSection18_ptr = struct INI_Section* (WINAPIV*)(struct CIniFile*, char*);
        using CIniFileGetSection18_clbk = struct INI_Section* (WINAPIV*)(struct CIniFile*, char*, CIniFileGetSection18_ptr);
        using CIniFileGetSection20_ptr = struct INI_Section* (WINAPIV*)(struct CIniFile*, unsigned int);
        using CIniFileGetSection20_clbk = struct INI_Section* (WINAPIV*)(struct CIniFile*, unsigned int, CIniFileGetSection20_ptr);
        using CIniFileGetSectionSize22_ptr = unsigned int (WINAPIV*)(struct CIniFile*);
        using CIniFileGetSectionSize22_clbk = unsigned int (WINAPIV*)(struct CIniFile*, CIniFileGetSectionSize22_ptr);
        using CIniFileLoad24_ptr = bool (WINAPIV*)(struct CIniFile*);
        using CIniFileLoad24_clbk = bool (WINAPIV*)(struct CIniFile*, CIniFileLoad24_ptr);
        using CIniFileLoadKey26_ptr = struct INI_Key* (WINAPIV*)(struct CIniFile*, char*, char*);
        using CIniFileLoadKey26_clbk = struct INI_Key* (WINAPIV*)(struct CIniFile*, char*, char*, CIniFileLoadKey26_ptr);
        using CIniFileLoadSection28_ptr = struct INI_Section* (WINAPIV*)(struct CIniFile*, char*);
        using CIniFileLoadSection28_clbk = struct INI_Section* (WINAPIV*)(struct CIniFile*, char*, CIniFileLoadSection28_ptr);
        using CIniFileMerge_Intersection30_ptr = bool (WINAPIV*)(struct CIniFile*, struct CIniFile*);
        using CIniFileMerge_Intersection30_clbk = bool (WINAPIV*)(struct CIniFile*, struct CIniFile*, CIniFileMerge_Intersection30_ptr);
        using CIniFileMerge_SumOfSets32_ptr = bool (WINAPIV*)(struct CIniFile*, struct CIniFile*);
        using CIniFileMerge_SumOfSets32_clbk = bool (WINAPIV*)(struct CIniFile*, struct CIniFile*, CIniFileMerge_SumOfSets32_ptr);
        using CIniFileSave34_ptr = void (WINAPIV*)(struct CIniFile*);
        using CIniFileSave34_clbk = void (WINAPIV*)(struct CIniFile*, CIniFileSave34_ptr);
        using CIniFileSaveKey36_ptr = bool (WINAPIV*)(struct CIniFile*, struct INI_Key*);
        using CIniFileSaveKey36_clbk = bool (WINAPIV*)(struct CIniFile*, struct INI_Key*, CIniFileSaveKey36_ptr);
        using CIniFileSaveSection38_ptr = bool (WINAPIV*)(struct CIniFile*, struct INI_Section*);
        using CIniFileSaveSection38_clbk = bool (WINAPIV*)(struct CIniFile*, struct INI_Section*, CIniFileSaveSection38_ptr);
        using CIniFileSetIniFilename40_ptr = void (WINAPIV*)(struct CIniFile*, char*);
        using CIniFileSetIniFilename40_clbk = void (WINAPIV*)(struct CIniFile*, char*, CIniFileSetIniFilename40_ptr);
        using CIniFileSplitKey42_ptr = bool (WINAPIV*)(struct CIniFile*, char*, char*, int);
        using CIniFileSplitKey42_clbk = bool (WINAPIV*)(struct CIniFile*, char*, char*, int, CIniFileSplitKey42_ptr);
        using CIniFileWriteString44_ptr = bool (WINAPIV*)(struct CIniFile*, char*, char*, char*);
        using CIniFileWriteString44_clbk = bool (WINAPIV*)(struct CIniFile*, char*, char*, char*, CIniFileWriteString44_ptr);
        
        using CIniFiledtor_CIniFile49_ptr = void (WINAPIV*)(struct CIniFile*);
        using CIniFiledtor_CIniFile49_clbk = void (WINAPIV*)(struct CIniFile*, CIniFiledtor_CIniFile49_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ITEMCOMBINE_DB_BASE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _ITEMCOMBINE_DB_BASEInit2_ptr = void (WINAPIV*)(struct _ITEMCOMBINE_DB_BASE*);
        using _ITEMCOMBINE_DB_BASEInit2_clbk = void (WINAPIV*)(struct _ITEMCOMBINE_DB_BASE*, _ITEMCOMBINE_DB_BASEInit2_ptr);
        using _ITEMCOMBINE_DB_BASEIsCombineData4_ptr = bool (WINAPIV*)(struct _ITEMCOMBINE_DB_BASE*);
        using _ITEMCOMBINE_DB_BASEIsCombineData4_clbk = bool (WINAPIV*)(struct _ITEMCOMBINE_DB_BASE*, _ITEMCOMBINE_DB_BASEIsCombineData4_ptr);
        
        using _ITEMCOMBINE_DB_BASEctor__ITEMCOMBINE_DB_BASE6_ptr = void (WINAPIV*)(struct _ITEMCOMBINE_DB_BASE*);
        using _ITEMCOMBINE_DB_BASEctor__ITEMCOMBINE_DB_BASE6_clbk = void (WINAPIV*)(struct _ITEMCOMBINE_DB_BASE*, _ITEMCOMBINE_DB_BASEctor__ITEMCOMBINE_DB_BASE6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

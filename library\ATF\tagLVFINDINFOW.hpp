// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagLVFINDINFOW
    {
        unsigned int flags;
        const wchar_t *psz;
        __int64 lParam;
        tagPOINT pt;
        unsigned int vkDirection;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

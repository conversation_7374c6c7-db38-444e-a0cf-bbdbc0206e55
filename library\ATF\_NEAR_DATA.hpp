// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _NEAR_DATA
    {
        float fLen;
        CCharacter *pChar;
        int bCanYouGoThere;
    public:
        void Init();
        _NEAR_DATA();
        void ctor__NEAR_DATA();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_NEAR_DATA, 24>(), "_NEAR_DATA");
END_ATF_NAMESPACE

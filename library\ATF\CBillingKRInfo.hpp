// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingKR.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CBillingKRctor_CBillingKR2_ptr = void (WINAPIV*)(struct CBillingKR*);
        using CBillingKRctor_CBillingKR2_clbk = void (WINAPIV*)(struct CBillingKR*, CBillingKRctor_CBillingKR2_ptr);
        
        using CBillingKRdtor_CBillingKR7_ptr = void (WINAPIV*)(struct CBillingKR*);
        using CBillingKRdtor_CBillingKR7_clbk = void (WINAPIV*)(struct CBillingKR*, CBillingKRdtor_CBillingKR7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

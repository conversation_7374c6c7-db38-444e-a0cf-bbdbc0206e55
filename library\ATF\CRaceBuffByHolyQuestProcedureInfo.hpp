// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBuffByHolyQuestProcedure.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRaceBuffByHolyQuestProcedurector_CRaceBuffByHolyQuestProcedure2_ptr = void (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*);
        using CRaceBuffByHolyQuestProcedurector_CRaceBuffByHolyQuestProcedure2_clbk = void (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, CRaceBuffByHolyQuestProcedurector_CRaceBuffByHolyQuestProcedure2_ptr);
        using CRaceBuffByHolyQuestProcedureCancelPlayerRaceBuff4_ptr = int (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, struct CPlayer*, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE, unsigned int);
        using CRaceBuffByHolyQuestProcedureCancelPlayerRaceBuff4_clbk = int (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, struct CPlayer*, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE, unsigned int, CRaceBuffByHolyQuestProcedureCancelPlayerRaceBuff4_ptr);
        using CRaceBuffByHolyQuestProcedureCreateComplete6_ptr = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, struct CPlayer*);
        using CRaceBuffByHolyQuestProcedureCreateComplete6_clbk = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, struct CPlayer*, CRaceBuffByHolyQuestProcedureCreateComplete6_ptr);
        using CRaceBuffByHolyQuestProcedureGetRaceBuffLevel8_ptr = int (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, struct CPlayer*);
        using CRaceBuffByHolyQuestProcedureGetRaceBuffLevel8_clbk = int (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, struct CPlayer*, CRaceBuffByHolyQuestProcedureGetRaceBuffLevel8_ptr);
        using CRaceBuffByHolyQuestProcedureInit10_ptr = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*);
        using CRaceBuffByHolyQuestProcedureInit10_clbk = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, CRaceBuffByHolyQuestProcedureInit10_ptr);
        using CRaceBuffByHolyQuestProcedureLoop12_ptr = void (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*);
        using CRaceBuffByHolyQuestProcedureLoop12_clbk = void (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, CRaceBuffByHolyQuestProcedureLoop12_ptr);
        using CRaceBuffByHolyQuestProcedureLoopSubProcSetRaceBuff14_ptr = void (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*);
        using CRaceBuffByHolyQuestProcedureLoopSubProcSetRaceBuff14_clbk = void (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, CRaceBuffByHolyQuestProcedureLoopSubProcSetRaceBuff14_ptr);
        using CRaceBuffByHolyQuestProcedureRequest16_ptr = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, int);
        using CRaceBuffByHolyQuestProcedureRequest16_clbk = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, int, CRaceBuffByHolyQuestProcedureRequest16_ptr);
        using CRaceBuffByHolyQuestProcedureRequestSubProcCancelRaceBuff18_ptr = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*);
        using CRaceBuffByHolyQuestProcedureRequestSubProcCancelRaceBuff18_clbk = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, CRaceBuffByHolyQuestProcedureRequestSubProcCancelRaceBuff18_ptr);
        using CRaceBuffByHolyQuestProcedureRequestSubProcSetRaceBattleResult20_ptr = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*);
        using CRaceBuffByHolyQuestProcedureRequestSubProcSetRaceBattleResult20_clbk = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, CRaceBuffByHolyQuestProcedureRequestSubProcSetRaceBattleResult20_ptr);
        using CRaceBuffByHolyQuestProcedureRequestSubProcSetRaceBuff22_ptr = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*);
        using CRaceBuffByHolyQuestProcedureRequestSubProcSetRaceBuff22_clbk = bool (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, CRaceBuffByHolyQuestProcedureRequestSubProcSetRaceBuff22_ptr);
        
        using CRaceBuffByHolyQuestProceduredtor_CRaceBuffByHolyQuestProcedure24_ptr = void (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*);
        using CRaceBuffByHolyQuestProceduredtor_CRaceBuffByHolyQuestProcedure24_clbk = void (WINAPIV*)(struct CRaceBuffByHolyQuestProcedure*, CRaceBuffByHolyQuestProceduredtor_CRaceBuffByHolyQuestProcedure24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

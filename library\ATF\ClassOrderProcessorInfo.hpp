// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ClassOrderProcessor.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using ClassOrderProcessorctor_ClassOrderProcessor2_ptr = void (WINAPIV*)(struct ClassOrderProcessor*);
        using ClassOrderProcessorctor_ClassOrderProcessor2_clbk = void (WINAPIV*)(struct ClassOrderProcessor*, ClassOrderProcessorctor_ClassOrderProcessor2_ptr);
        using ClassOrderProcessorDoit4_ptr = int (WINAPIV*)(struct ClassOrderProcessor*, Cmd, struct CPlayer*, char*);
        using ClassOrderProcessorDoit4_clbk = int (WINAPIV*)(struct ClassOrderProcessor*, Cmd, struct CPlayer*, char*, ClassOrderProcessorDoit4_ptr);
        using ClassOrderProcessorInitialize6_ptr = bool (WINAPIV*)(struct ClassOrderProcessor*);
        using ClassOrderProcessorInitialize6_clbk = bool (WINAPIV*)(struct ClassOrderProcessor*, ClassOrderProcessorInitialize6_ptr);
        using ClassOrderProcessorInstance8_ptr = struct ClassOrderProcessor* (WINAPIV*)();
        using ClassOrderProcessorInstance8_clbk = struct ClassOrderProcessor* (WINAPIV*)(ClassOrderProcessorInstance8_ptr);
        using ClassOrderProcessorSendMsg_PatriarchInform10_ptr = void (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*);
        using ClassOrderProcessorSendMsg_PatriarchInform10_clbk = void (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*, ClassOrderProcessorSendMsg_PatriarchInform10_ptr);
        using ClassOrderProcessorSendMsg_QueryAppointResult12_ptr = void (WINAPIV*)(struct ClassOrderProcessor*, uint16_t, char, char, char*);
        using ClassOrderProcessorSendMsg_QueryAppointResult12_clbk = void (WINAPIV*)(struct ClassOrderProcessor*, uint16_t, char, char, char*, ClassOrderProcessorSendMsg_QueryAppointResult12_ptr);
        using ClassOrderProcessorUpdatePacket14_ptr = void (WINAPIV*)(struct ClassOrderProcessor*, char, char);
        using ClassOrderProcessorUpdatePacket14_clbk = void (WINAPIV*)(struct ClassOrderProcessor*, char, char, ClassOrderProcessorUpdatePacket14_ptr);
        using ClassOrderProcessor_CheckUserInfo16_ptr = int (WINAPIV*)(struct ClassOrderProcessor*, char, char, struct CPlayer*);
        using ClassOrderProcessor_CheckUserInfo16_clbk = int (WINAPIV*)(struct ClassOrderProcessor*, char, char, struct CPlayer*, ClassOrderProcessor_CheckUserInfo16_ptr);
        using ClassOrderProcessor_QueryAppoint18_ptr = int (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*, char*);
        using ClassOrderProcessor_QueryAppoint18_clbk = int (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*, char*, ClassOrderProcessor_QueryAppoint18_ptr);
        using ClassOrderProcessor_RequestAppoint20_ptr = int (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*, char*);
        using ClassOrderProcessor_RequestAppoint20_clbk = int (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*, char*, ClassOrderProcessor_RequestAppoint20_ptr);
        using ClassOrderProcessor_RequestDischarge22_ptr = int (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*, char*);
        using ClassOrderProcessor_RequestDischarge22_clbk = int (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*, char*, ClassOrderProcessor_RequestDischarge22_ptr);
        using ClassOrderProcessor_ResponseAppoint24_ptr = int (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*, char*);
        using ClassOrderProcessor_ResponseAppoint24_clbk = int (WINAPIV*)(struct ClassOrderProcessor*, struct CPlayer*, char*, ClassOrderProcessor_ResponseAppoint24_ptr);
        
        using ClassOrderProcessordtor_ClassOrderProcessor29_ptr = void (WINAPIV*)(struct ClassOrderProcessor*);
        using ClassOrderProcessordtor_ClassOrderProcessor29_clbk = void (WINAPIV*)(struct ClassOrderProcessor*, ClassOrderProcessordtor_ClassOrderProcessor29_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

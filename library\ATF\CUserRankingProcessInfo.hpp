// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUserRankingProcess.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CUserRankingProcessAllocObject2_ptr = bool (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessAllocObject2_clbk = bool (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessAllocObject2_ptr);
        
        using CUserRankingProcessctor_CUserRankingProcess4_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessctor_CUserRankingProcess4_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessctor_CUserRankingProcess4_ptr);
        using CUserRankingProcessCheckAndCreateTodayPvpRankTable6_ptr = bool (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessCheckAndCreateTodayPvpRankTable6_clbk = bool (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessCheckAndCreateTodayPvpRankTable6_ptr);
        using CUserRankingProcessCheckTomorrowPvpRankDate8_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessCheckTomorrowPvpRankDate8_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessCheckTomorrowPvpRankDate8_ptr);
        using CUserRankingProcessCompleteGuildRankStep110_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteGuildRankStep110_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteGuildRankStep110_ptr);
        using CUserRankingProcessCompleteGuildRankStep212_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteGuildRankStep212_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteGuildRankStep212_ptr);
        using CUserRankingProcessCompleteGuildRankStep314_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteGuildRankStep314_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteGuildRankStep314_ptr);
        using CUserRankingProcessCompleteGuildRankStep416_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteGuildRankStep416_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteGuildRankStep416_ptr);
        using CUserRankingProcessCompleteRaceRankStep118_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep118_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep118_ptr);
        using CUserRankingProcessCompleteRaceRankStep1020_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep1020_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep1020_ptr);
        using CUserRankingProcessCompleteRaceRankStep1122_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep1122_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep1122_ptr);
        using CUserRankingProcessCompleteRaceRankStep224_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep224_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep224_ptr);
        using CUserRankingProcessCompleteRaceRankStep326_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep326_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep326_ptr);
        using CUserRankingProcessCompleteRaceRankStep428_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep428_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep428_ptr);
        using CUserRankingProcessCompleteRaceRankStep530_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep530_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep530_ptr);
        using CUserRankingProcessCompleteRaceRankStep632_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep632_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep632_ptr);
        using CUserRankingProcessCompleteRaceRankStep734_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep734_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep734_ptr);
        using CUserRankingProcessCompleteRaceRankStep836_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep836_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep836_ptr);
        using CUserRankingProcessCompleteRaceRankStep938_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRaceRankStep938_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRaceRankStep938_ptr);
        using CUserRankingProcessCompleteRankInGuildStep140_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRankInGuildStep140_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRankInGuildStep140_ptr);
        using CUserRankingProcessCompleteRankInGuildStep242_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRankInGuildStep242_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRankInGuildStep242_ptr);
        using CUserRankingProcessCompleteRankInGuildStep344_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRankInGuildStep344_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRankInGuildStep344_ptr);
        using CUserRankingProcessCompleteRankInGuildStep446_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRankInGuildStep446_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRankInGuildStep446_ptr);
        using CUserRankingProcessCompleteRankInGuildStep548_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRankInGuildStep548_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRankInGuildStep548_ptr);
        using CUserRankingProcessCompleteRankInGuildStep650_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRankInGuildStep650_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRankInGuildStep650_ptr);
        using CUserRankingProcessCompleteRankUpdateAndSelectGarde52_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char*);
        using CUserRankingProcessCompleteRankUpdateAndSelectGarde52_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char*, CUserRankingProcessCompleteRankUpdateAndSelectGarde52_ptr);
        using CUserRankingProcessFindRank54_ptr = unsigned int (WINAPIV*)(struct CUserRankingProcess*, char, unsigned int);
        using CUserRankingProcessFindRank54_clbk = unsigned int (WINAPIV*)(struct CUserRankingProcess*, char, unsigned int, CUserRankingProcessFindRank54_ptr);
        using CUserRankingProcessFlipPvPRankTop56_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessFlipPvPRankTop56_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessFlipPvPRankTop56_ptr);
        using CUserRankingProcessGetBossType58_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char, unsigned int);
        using CUserRankingProcessGetBossType58_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char, unsigned int, CUserRankingProcessGetBossType58_ptr);
        using CUserRankingProcessGetCurrentPvpRankData60_ptr = struct _PVP_RANK_DATA* (WINAPIV*)(struct CUserRankingProcess*, char, char);
        using CUserRankingProcessGetCurrentPvpRankData60_clbk = struct _PVP_RANK_DATA* (WINAPIV*)(struct CUserRankingProcess*, char, char, CUserRankingProcessGetCurrentPvpRankData60_ptr);
        using CUserRankingProcessGetCurrentRaceBossSerial62_ptr = unsigned int (WINAPIV*)(struct CUserRankingProcess*, char, char);
        using CUserRankingProcessGetCurrentRaceBossSerial62_clbk = unsigned int (WINAPIV*)(struct CUserRankingProcess*, char, char, CUserRankingProcessGetCurrentRaceBossSerial62_ptr);
        using CUserRankingProcessGetRankDateStr64_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char*, uint64_t);
        using CUserRankingProcessGetRankDateStr64_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char*, uint64_t, CUserRankingProcessGetRankDateStr64_ptr);
        using CUserRankingProcessGetTommorrowStr66_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessGetTommorrowStr66_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessGetTommorrowStr66_ptr);
        using CUserRankingProcessIncreaseVesion68_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessIncreaseVesion68_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessIncreaseVesion68_ptr);
        using CUserRankingProcessInit70_ptr = bool (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessInit70_clbk = bool (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessInit70_ptr);
        using CUserRankingProcessInitProcFunc72_ptr = bool (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessInitProcFunc72_clbk = bool (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessInitProcFunc72_ptr);
        using CUserRankingProcessIsCurrentRaceBossGroup74_ptr = bool (WINAPIV*)(struct CUserRankingProcess*, char, unsigned int);
        using CUserRankingProcessIsCurrentRaceBossGroup74_clbk = bool (WINAPIV*)(struct CUserRankingProcess*, char, unsigned int, CUserRankingProcessIsCurrentRaceBossGroup74_ptr);
        using CUserRankingProcessIsRaceViceBoss76_ptr = bool (WINAPIV*)(struct CUserRankingProcess*, char, unsigned int);
        using CUserRankingProcessIsRaceViceBoss76_clbk = bool (WINAPIV*)(struct CUserRankingProcess*, char, unsigned int, CUserRankingProcessIsRaceViceBoss76_ptr);
        using CUserRankingProcessLoad78_ptr = bool (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessLoad78_clbk = bool (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessLoad78_ptr);
        using CUserRankingProcessLoadINI80_ptr = void (WINAPIV*)(struct CUserRankingProcess*, int*, int*);
        using CUserRankingProcessLoadINI80_clbk = void (WINAPIV*)(struct CUserRankingProcess*, int*, int*, CUserRankingProcessLoadINI80_ptr);
        using CUserRankingProcessLoop82_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessLoop82_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessLoop82_ptr);
        using CUserRankingProcessProcApplyGuildGrade84_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcApplyGuildGrade84_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcApplyGuildGrade84_ptr);
        using CUserRankingProcessProcApplyRankInGuild86_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcApplyRankInGuild86_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcApplyRankInGuild86_ptr);
        using CUserRankingProcessProcFailedWait88_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcFailedWait88_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcFailedWait88_ptr);
        using CUserRankingProcessProcNotifyVersionUp90_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcNotifyVersionUp90_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcNotifyVersionUp90_ptr);
        using CUserRankingProcessProcRankComplete92_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcRankComplete92_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcRankComplete92_ptr);
        using CUserRankingProcessProcRankStart94_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcRankStart94_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcRankStart94_ptr);
        using CUserRankingProcessProcRankSuccess96_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcRankSuccess96_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcRankSuccess96_ptr);
        using CUserRankingProcessProcSaveTargetList98_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcSaveTargetList98_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcSaveTargetList98_ptr);
        using CUserRankingProcessProcWait100_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcWait100_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcWait100_ptr);
        using CUserRankingProcessProcWaitDayChanged102_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessProcWaitDayChanged102_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessProcWaitDayChanged102_ptr);
        using CUserRankingProcessPvpRankDataPacking104_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessPvpRankDataPacking104_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessPvpRankDataPacking104_ptr);
        using CUserRankingProcessPvpRankListRequest106_ptr = void (WINAPIV*)(struct CUserRankingProcess*, uint16_t, char, char, char);
        using CUserRankingProcessPvpRankListRequest106_clbk = void (WINAPIV*)(struct CUserRankingProcess*, uint16_t, char, char, char, CUserRankingProcessPvpRankListRequest106_ptr);
        using CUserRankingProcessSetCurrentRaceBossSerial108_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char, unsigned int);
        using CUserRankingProcessSetCurrentRaceBossSerial108_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char, unsigned int, CUserRankingProcessSetCurrentRaceBossSerial108_ptr);
        using CUserRankingProcessSetLogger110_ptr = void (WINAPIV*)(struct CUserRankingProcess*, struct CLogFile*);
        using CUserRankingProcessSetLogger110_clbk = void (WINAPIV*)(struct CUserRankingProcess*, struct CLogFile*, CUserRankingProcessSetLogger110_ptr);
        using CUserRankingProcessSetRankingStartTime112_ptr = bool (WINAPIV*)(struct CUserRankingProcess*, int, int);
        using CUserRankingProcessSetRankingStartTime112_clbk = bool (WINAPIV*)(struct CUserRankingProcess*, int, int, CUserRankingProcessSetRankingStartTime112_ptr);
        using CUserRankingProcessSetUpdateRaceBossSerial114_ptr = void (WINAPIV*)(struct CUserRankingProcess*, char, char, unsigned int);
        using CUserRankingProcessSetUpdateRaceBossSerial114_clbk = void (WINAPIV*)(struct CUserRankingProcess*, char, char, unsigned int, CUserRankingProcessSetUpdateRaceBossSerial114_ptr);
        using CUserRankingProcessUpdateAndSelectGuildGrade116_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateAndSelectGuildGrade116_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateAndSelectGuildGrade116_ptr);
        using CUserRankingProcessUpdateGuildRankStep1118_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateGuildRankStep1118_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateGuildRankStep1118_ptr);
        using CUserRankingProcessUpdateGuildRankStep2120_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateGuildRankStep2120_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateGuildRankStep2120_ptr);
        using CUserRankingProcessUpdateGuildRankStep3122_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateGuildRankStep3122_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateGuildRankStep3122_ptr);
        using CUserRankingProcessUpdateGuildRankStep4124_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateGuildRankStep4124_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateGuildRankStep4124_ptr);
        using CUserRankingProcessUpdateNextRankingStartTime126_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessUpdateNextRankingStartTime126_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessUpdateNextRankingStartTime126_ptr);
        using CUserRankingProcessUpdateRaceRankStep1128_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep1128_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep1128_ptr);
        using CUserRankingProcessUpdateRaceRankStep10130_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep10130_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep10130_ptr);
        using CUserRankingProcessUpdateRaceRankStep11132_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep11132_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep11132_ptr);
        using CUserRankingProcessUpdateRaceRankStep2134_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep2134_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep2134_ptr);
        using CUserRankingProcessUpdateRaceRankStep3136_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep3136_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep3136_ptr);
        using CUserRankingProcessUpdateRaceRankStep4138_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep4138_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep4138_ptr);
        using CUserRankingProcessUpdateRaceRankStep5140_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep5140_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep5140_ptr);
        using CUserRankingProcessUpdateRaceRankStep6142_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep6142_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep6142_ptr);
        using CUserRankingProcessUpdateRaceRankStep7144_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep7144_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep7144_ptr);
        using CUserRankingProcessUpdateRaceRankStep8146_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep8146_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep8146_ptr);
        using CUserRankingProcessUpdateRaceRankStep9148_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRaceRankStep9148_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRaceRankStep9148_ptr);
        using CUserRankingProcessUpdateRankinGuildStep1150_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRankinGuildStep1150_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRankinGuildStep1150_ptr);
        using CUserRankingProcessUpdateRankinGuildStep2152_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRankinGuildStep2152_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRankinGuildStep2152_ptr);
        using CUserRankingProcessUpdateRankinGuildStep3154_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRankinGuildStep3154_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRankinGuildStep3154_ptr);
        using CUserRankingProcessUpdateRankinGuildStep4156_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRankinGuildStep4156_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRankinGuildStep4156_ptr);
        using CUserRankingProcessUpdateRankinGuildStep5158_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRankinGuildStep5158_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRankinGuildStep5158_ptr);
        using CUserRankingProcessUpdateRankinGuildStep6160_ptr = char (WINAPIV*)(struct CUserRankingProcess*, char*);
        using CUserRankingProcessUpdateRankinGuildStep6160_clbk = char (WINAPIV*)(struct CUserRankingProcess*, char*, CUserRankingProcessUpdateRankinGuildStep6160_ptr);
        
        using CUserRankingProcessdtor_CUserRankingProcess162_ptr = void (WINAPIV*)(struct CUserRankingProcess*);
        using CUserRankingProcessdtor_CUserRankingProcess162_clbk = void (WINAPIV*)(struct CUserRankingProcess*, CUserRankingProcessdtor_CUserRankingProcess162_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

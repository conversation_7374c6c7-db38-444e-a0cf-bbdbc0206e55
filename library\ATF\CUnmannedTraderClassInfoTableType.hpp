// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CUnmannedTraderClassInfo.hpp>
#include <CUnmannedTraderSubClassInfo.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct  CUnmannedTraderClassInfoTableType : CUnmannedTraderClassInfo
    {
        char m_byTableCode;
        std::vector<CUnmannedTraderSubClassInfo *> m_vecSubClass;
    public:
        CUnmannedTraderClassInfoTableType(unsigned int dwID);
        void ctor_CUnmannedTraderClassInfoTableType(unsigned int dwID);
        void CleanUp();
        struct CUnmannedTraderClassInfo* Create(unsigned int dwID);
        bool GetGroupID(char byTableCode, uint16_t wItemTableIndex, char* byClass);
        bool GetGroupID(char byTableCode, uint16_t wItemTableIndex, char* byClass, char* bySubClass);
        bool IsExistGroupID(char byClass, char bySubClass);
        bool IsValidID(unsigned int dwID);
        bool LoadXML(struct TiXmlElement* elemClass, struct CLogFile* kLogger, unsigned int dwDivisionID);
        ~CUnmannedTraderClassInfoTableType();
        void dtor_CUnmannedTraderClassInfoTableType();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _nuclear_position_result_zocl
    {
        unsigned int dwMasterSerial;
        float zPos[3];
        char byUseClass;
    public:
        _nuclear_position_result_zocl();
        void ctor__nuclear_position_result_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_nuclear_position_result_zocl, 17>(), "_nuclear_position_result_zocl");
END_ATF_NAMESPACE

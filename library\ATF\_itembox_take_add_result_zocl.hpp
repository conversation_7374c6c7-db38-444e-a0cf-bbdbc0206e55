// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _itembox_take_add_result_zocl
    {
        char sErrorCode;
         unsigned __int16 wItemSerial;
        char byAmount;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_itembox_take_add_result_zocl, 4>(), "_itembox_take_add_result_zocl");
END_ATF_NAMESPACE

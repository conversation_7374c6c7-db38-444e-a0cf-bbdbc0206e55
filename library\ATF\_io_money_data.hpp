// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _io_money_data
    {
        char wszIOerName[17];
        unsigned int dwIOerSerial;
        long double dIODalant;
        long double dIOGold;
        long double dLeftDalant;
        long double dLeftGold;
        char byDate[4];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMHDR.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagNMCHAR
    {
        tagNMHDR hdr;
        unsigned int ch;
        unsigned int dwItemPrev;
        unsigned int dwItemNext;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

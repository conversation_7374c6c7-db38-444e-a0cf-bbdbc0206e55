// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPaintDC.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CPaintDCdtor_CPaintDC1_ptr = int64_t (WINAPIV*)(struct CPaintDC*);
        using CPaintDCdtor_CPaintDC1_clbk = int64_t (WINAPIV*)(struct CPaintDC*, CPaintDCdtor_CPaintDC1_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HBITMAP__.hpp>
#include <HWND__.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagREBARBANDINFOW
    {
        unsigned int cbSize;
        unsigned int fMask;
        unsigned int fStyle;
        unsigned int clrFore;
        unsigned int clrBack;
        wchar_t *lpText;
        unsigned int cch;
        int iImage;
        HWND__ *hwndChild;
        unsigned int cxMinChild;
        unsigned int cyMinChild;
        unsigned int cx;
        HBITMAP__ *hbmBack;
        unsigned int wID;
        unsigned int cyChild;
        unsigned int cyMaxChild;
        unsigned int cyIntegral;
        unsigned int cxIdeal;
        __int64 lParam;
        unsigned int cxHeader;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

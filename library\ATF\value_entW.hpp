// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct value_entW
    {
        wchar_t *ve_valuename;
        unsigned int ve_valuelen;
        unsigned __int64 ve_valueptr;
        unsigned int ve_type;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/ModuleHook.hpp"

#include <ATF/CPlayerInfo.hpp>

namespace GameServer
{
    namespace Addon
    {
        class CLootExchange
            : public Yorozuya::Module::IModule
            , CModuleHook
        {
        public:
            CLootExchange() { };

            virtual void load() override;

            virtual void unload() override;

            virtual Yorozuya::Module::ModuleName_t get_name() override;

            virtual void configure(const rapidjson::Value& nodeConfig) override;

        private:
            static int GetMoneyType(
                ATF::_base_fld* pRec, char byTableCode);

            static int GetMoneyValue(
                ATF::_base_fld * pRec,
                char byTableCode,
                uint32_t nMoneyType,
                int nRace);

            static bool AddMoney(ATF::CPlayer* pObj, uint32_t nMoneyType, int nMoneyValue);

        private:
            static bool m_bActivated;
            static bool m_bExchangeAll;
            static int m_nMinimumValue;
            static float m_fMaxPickupDistance;
            static bool m_bEnableLogging;
            static std::string m_sLogFilePath;
            static std::vector<int> m_vMoneyTypePriority;
            static bool m_bItemTypeSettings[7]; // Equipment, Consumables, Resources, Special, Force, Accessories
            static bool m_bCurrencySettings[7]; // CP, Gold, PvP, PvP2, Processing, Hunter, Gold
            static bool m_bUseRaceSpecificPricing;
            static int m_nDefaultRace;
            static bool m_bMultiplyByDurability;
            static int m_nExchangeRateModifier;
            static bool m_bPremiumPlayersOnly;
            static int m_nMinimumPlayerLevel;
            static int m_nDailyExchangeLimit;
            static std::string m_sDailyResetTime;

        private:
            static void LoadConfiguration();
            static bool IsItemTypeAllowed(char byTableCode);
            static bool IsCurrencyTypeAllowed(uint32_t nMoneyType);
            static void LogExchange(const std::string& message);

        private:
            static void WINAPIV pc_TakeGroundingItem(
                ATF::CPlayer* pObj,
                ATF::CItemBox* pBox,
                uint16_t wAddSerial,
                ATF::Info::CPlayerpc_TakeGroundingItem1947_ptr next);
        };
    };
};

#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/ModuleHook.hpp"

#include <ATF/CPlayerInfo.hpp>

namespace GameServer
{
    namespace Addon
    {
        class CLootExchange
            : public Yorozuya::Module::IModule
            , CModuleHook
        {
        public:
            CLootExchange() { };

            virtual void load() override;

            virtual void unload() override;

            virtual Yorozuya::Module::ModuleName_t get_name() override;

            virtual void configure(const rapidjson::Value& nodeConfig) override;

        private:
            static int GetMoneyType(
                ATF::_base_fld* pRec, char byTableCode);

            static int GetMoneyValue(
                ATF::_base_fld * pRec,
                char byTableCode,
                uint32_t nMoneyType,
                int nRace);

            static bool AddMoney(ATF::CPlayer* pObj, uint32_t nMoneyType, int nMoneyValue);

        private:
            // Basic configuration
            static bool m_bActivated;
            static bool m_bExchangeAll;
            static int m_nMinimumValue;
            static float m_fMaxPickupDistance;
            static bool m_bEnableLogging;
            static std::string m_sLogFilePath;
            static std::vector<int> m_vMoneyTypePriority;
            static bool m_bItemTypeSettings[7];
            static bool m_bCurrencySettings[7];
            static bool m_bUseRaceSpecificPricing;
            static int m_nDefaultRace;
            static bool m_bMultiplyByDurability;
            static int m_nExchangeRateModifier;
            static bool m_bPremiumPlayersOnly;
            static int m_nMinimumPlayerLevel;
            static int m_nDailyExchangeLimit;
            static std::string m_sDailyResetTime;

            // Custom mapping system
            static bool m_bUseCustomMappings;

            // Item mapping structures
            struct ItemMapping {
                std::string itemCode;
                uint32_t currencyType;
                int value;
                bool useDbValue;
            };

            struct TableMapping {
                uint8_t tableCode;
                uint32_t currencyType;
                int valueMultiplier;
            };

            struct CategoryMapping {
                std::string categoryName;
                uint32_t currencyType;
                int valueMultiplier;
                std::vector<uint8_t> tableList;
            };

            struct ConditionalMapping {
                std::string condition;
                uint32_t currencyType;
                int value;
                bool useDbValue;
            };

            static std::vector<ItemMapping> m_vItemMappings;
            static std::vector<TableMapping> m_vTableMappings;
            static std::vector<CategoryMapping> m_vCategoryMappings;
            static std::vector<ConditionalMapping> m_vConditionalMappings;
            static std::vector<std::string> m_vExclusionList;
            static std::map<std::string, std::vector<int>> m_mPriorityOverrides;

        private:
            static void LoadConfiguration();
            static void LoadItemMappings();
            static void LoadTableMappings();
            static void LoadCategoryMappings();
            static void LoadConditionalMappings();
            static void LoadExclusionList();
            static void LoadPriorityOverrides();

            static bool IsItemTypeAllowed(char byTableCode);
            static bool IsCurrencyTypeAllowed(uint32_t nMoneyType);
            static bool IsItemExcluded(const std::string& itemCode, char byTableCode, uint16_t wItemIndex);
            static bool MatchesPattern(const std::string& pattern, const std::string& text);
            static bool EvaluateCondition(const std::string& condition, ATF::CPlayer* pPlayer, ATF::_base_fld* pRecord, char byTableCode);

            static bool GetCustomMapping(ATF::CPlayer* pPlayer, ATF::_base_fld* pRecord, char byTableCode, uint16_t wItemIndex, uint32_t& outCurrencyType, int& outValue);
            static std::vector<int> GetItemPriority(const std::string& itemCode, char byTableCode, uint16_t wItemIndex);

            static void LogExchange(const std::string& message);

        private:
            static void WINAPIV pc_TakeGroundingItem(
                ATF::CPlayer* pObj,
                ATF::CItemBox* pBox,
                uint16_t wAddSerial,
                ATF::Info::CPlayerpc_TakeGroundingItem1947_ptr next);
        };
    };
};

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_possible_battle_guild_list_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CPossibleBattleGuildListManager
        {
            bool m_bInit;
            unsigned int *m_pdwVer;
            char *m_pMaxPage;
            _possible_battle_guild_list_result_zocl **m_ppkList;
        public:
            CPossibleBattleGuildListManager();
            void ctor_CPossibleBattleGuildListManager();
            void Clear();
            static void Destroy();
            void DoDayChangedWork();
            bool Init();
            static struct CPossibleBattleGuildListManager* Instance();
            bool Load();
            bool MakePage(char byRace, char ucPage, uint16_t* wLastGuildInx);
            void Send(int n, char byRace, char byPage, unsigned int dwVer);
            void SendErrorResult(int n, char byRet);
            void SendFirst(int n, char byRace);
            char SendInfo(int n, char byRace, char byPage, unsigned int dwVer);
            void UpdateGuildList();
            ~CPossibleBattleGuildListManager();
            void dtor_CPossibleBattleGuildListManager();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

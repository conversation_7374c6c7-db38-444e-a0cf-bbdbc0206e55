// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union $83FB4F737B5EDC31066F75BACF4F71E0
    {
        unsigned int dwCaps4;
        unsigned int dwVolumeDepth;
    };    
    static_assert(ATF::checkSize<$83FB4F737B5EDC31066F75BACF4F71E0, 4>(), "$83FB4F737B5EDC31066F75BACF4F71E0");
END_ATF_NAMESPACE

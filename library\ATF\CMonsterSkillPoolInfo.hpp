// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonsterSkillPool.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMonsterSkillPoolctor_CMonsterSkillPool2_ptr = void (WINAPIV*)(struct CMonsterSkillPool*);
        using CMonsterSkillPoolctor_CMonsterSkillPool2_clbk = void (WINAPIV*)(struct CMonsterSkillPool*, CMonsterSkillPoolctor_CMonsterSkillPool2_ptr);
        using CMonsterSkillPoolGetMonSkill4_ptr = struct CMonsterSkill* (WINAPIV*)(struct CMonsterSkillPool*, int);
        using CMonsterSkillPoolGetMonSkill4_clbk = struct CMonsterSkill* (WINAPIV*)(struct CMonsterSkillPool*, int, CMonsterSkillPoolGetMonSkill4_ptr);
        using CMonsterSkillPoolGetMonSkillKind6_ptr = struct CMonsterSkill* (WINAPIV*)(struct CMonsterSkillPool*, int);
        using CMonsterSkillPoolGetMonSkillKind6_clbk = struct CMonsterSkill* (WINAPIV*)(struct CMonsterSkillPool*, int, CMonsterSkillPoolGetMonSkillKind6_ptr);
        using CMonsterSkillPoolInit8_ptr = void (WINAPIV*)(struct CMonsterSkillPool*);
        using CMonsterSkillPoolInit8_clbk = void (WINAPIV*)(struct CMonsterSkillPool*, CMonsterSkillPoolInit8_ptr);
        using CMonsterSkillPoolInsertSkill10_ptr = int (WINAPIV*)(struct CMonsterSkillPool*, struct CMonsterSkill*);
        using CMonsterSkillPoolInsertSkill10_clbk = int (WINAPIV*)(struct CMonsterSkillPool*, struct CMonsterSkill*, CMonsterSkillPoolInsertSkill10_ptr);
        using CMonsterSkillPoolSet12_ptr = int (WINAPIV*)(struct CMonsterSkillPool*, struct CMonster*);
        using CMonsterSkillPoolSet12_clbk = int (WINAPIV*)(struct CMonsterSkillPool*, struct CMonster*, CMonsterSkillPoolSet12_ptr);
        
        using CMonsterSkillPooldtor_CMonsterSkillPool14_ptr = void (WINAPIV*)(struct CMonsterSkillPool*);
        using CMonsterSkillPooldtor_CMonsterSkillPool14_clbk = void (WINAPIV*)(struct CMonsterSkillPool*, CMonsterSkillPooldtor_CMonsterSkillPool14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

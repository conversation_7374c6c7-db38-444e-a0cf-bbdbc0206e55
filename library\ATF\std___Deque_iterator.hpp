// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RECV_DATA.hpp>
#include <std___Container_base.hpp>
#include <std___Deque_const_iterator.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0> : _Deque_const_iterator<RECV_DATA,allocator<RECV_DATA>,0>
        {
        public:
            _Deque_iterator(struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>* __that)
            {
                using org_ptr = void (WINAPIV*)(struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>*, struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>*);
                (org_ptr(0x14031d540L))(this, __that);
            };
            void ctor__Deque_iterator(struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>* __that)
            {
                using org_ptr = void (WINAPIV*)(struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>*, struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>*);
                (org_ptr(0x14031d540L))(this, __that);
            };
            _Deque_iterator(uint64_t _Off, struct _Container_base* _Pdeque)
            {
                using org_ptr = void (WINAPIV*)(struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>*, uint64_t, struct _Container_base*);
                (org_ptr(0x14031e370L))(this, _Off, _Pdeque);
            };
            void ctor__Deque_iterator(uint64_t _Off, struct _Container_base* _Pdeque)
            {
                using org_ptr = void (WINAPIV*)(struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>*, uint64_t, struct _Container_base*);
                (org_ptr(0x14031e370L))(this, _Off, _Pdeque);
            };
            ~_Deque_iterator()
            {
                using org_ptr = void (WINAPIV*)(struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>*);
                (org_ptr(0x14031d480L))(this);
            };
            void dtor__Deque_iterator()
            {
                using org_ptr = void (WINAPIV*)(struct _Deque_iterator<RECV_DATA,allocator<RECV_DATA>,0>*);
                (org_ptr(0x14031d480L))(this);
            };
        };    
        static_assert(ATF::checkSize<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>, 32>(), "std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>");
    }; // end namespace std
END_ATF_NAMESPACE

#include <_limit_item_num_info_zoclDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        
        Info::_limit_item_num_info_zoclctor__limit_item_num_info_zocl2_ptr _limit_item_num_info_zoclctor__limit_item_num_info_zocl2_next(nullptr);
        Info::_limit_item_num_info_zoclctor__limit_item_num_info_zocl2_clbk _limit_item_num_info_zoclctor__limit_item_num_info_zocl2_user(nullptr);
        
        Info::_limit_item_num_info_zoclsize4_ptr _limit_item_num_info_zoclsize4_next(nullptr);
        Info::_limit_item_num_info_zoclsize4_clbk _limit_item_num_info_zoclsize4_user(nullptr);
        
        
        void _limit_item_num_info_zoclctor__limit_item_num_info_zocl2_wrapper(struct _limit_item_num_info_zocl* _this)
        {
           _limit_item_num_info_zoclctor__limit_item_num_info_zocl2_user(_this, _limit_item_num_info_zoclctor__limit_item_num_info_zocl2_next);
        };
        int _limit_item_num_info_zoclsize4_wrapper(struct _limit_item_num_info_zocl* _this)
        {
           return _limit_item_num_info_zoclsize4_user(_this, _limit_item_num_info_zoclsize4_next);
        };
        
        ::std::array<hook_record, 2> _limit_item_num_info_zocl_functions = 
        {
            _hook_record {
                (LPVOID)0x1400ef1b0L,
                (LPVOID *)&_limit_item_num_info_zoclctor__limit_item_num_info_zocl2_user,
                (LPVOID *)&_limit_item_num_info_zoclctor__limit_item_num_info_zocl2_next,
                (LPVOID)cast_pointer_function(_limit_item_num_info_zoclctor__limit_item_num_info_zocl2_wrapper),
                (LPVOID)cast_pointer_function((void(_limit_item_num_info_zocl::*)())&_limit_item_num_info_zocl::ctor__limit_item_num_info_zocl)
            },
            _hook_record {
                (LPVOID)0x1400ef200L,
                (LPVOID *)&_limit_item_num_info_zoclsize4_user,
                (LPVOID *)&_limit_item_num_info_zoclsize4_next,
                (LPVOID)cast_pointer_function(_limit_item_num_info_zoclsize4_wrapper),
                (LPVOID)cast_pointer_function((int(_limit_item_num_info_zocl::*)())&_limit_item_num_info_zocl::size)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE

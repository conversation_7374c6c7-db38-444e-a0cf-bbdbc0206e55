// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagMODULEENTRY32
    {
        unsigned int dwSize;
        unsigned int th32ModuleID;
        unsigned int th32ProcessID;
        unsigned int GlblcntUsage;
        unsigned int ProccntUsage;
        char *modBaseAddr;
        unsigned int modBaseSize;
        HINSTANCE__ *hModule;
        char szModule[256];
        char szExePath[260];
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<tagMODULEENTRY32, 568>(), "tagMODULEENTRY32");
END_ATF_NAMESPACE

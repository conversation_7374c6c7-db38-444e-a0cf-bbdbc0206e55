// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_mastery_up_data.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _mastery_up_datactor__mastery_up_data2_ptr = void (WINAPIV*)(struct _mastery_up_data*);
        using _mastery_up_datactor__mastery_up_data2_clbk = void (WINAPIV*)(struct _mastery_up_data*, _mastery_up_datactor__mastery_up_data2_ptr);
        using _mastery_up_datainit4_ptr = void (WINAPIV*)(struct _mastery_up_data*);
        using _mastery_up_datainit4_clbk = void (WINAPIV*)(struct _mastery_up_data*, _mastery_up_datainit4_ptr);
        using _mastery_up_dataset6_ptr = void (WINAPIV*)(struct _mastery_up_data*, char, char, char);
        using _mastery_up_dataset6_clbk = void (WINAPIV*)(struct _mastery_up_data*, char, char, char, _mastery_up_dataset6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum EVENT_STATE
    {
      Gold_Event_disable = 0x0,
      Gold_Event_wait = 0x1,
      Gold_Event_start = 0x2,
      Gold_Event_end = 0x3,
      Gold_Event_all = 0x4,
    };
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    enum Event_State
    {
      state_disable = 0x0,
      state_wait = 0x1,
      state_start = 0x2,
      state_end = 0x3,
      state_all = 0x4,
    };
END_ATF_NAMESPACE

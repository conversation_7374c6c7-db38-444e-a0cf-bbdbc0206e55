// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _OSVERSIONINFOA
    {
        unsigned int dwOSVersionInfoSize;
        unsigned int dwMajorVersion;
        unsigned int dwMinorVersion;
        unsigned int dwBuildNumber;
        unsigned int dwPlatformId;
        char szCSDVersion[128];
    };    
    static_assert(ATF::checkSize<_OSVERSIONINFOA, 148>(), "_OSVERSIONINFOA");
END_ATF_NAMESPACE

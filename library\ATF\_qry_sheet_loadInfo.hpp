// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_sheet_load.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_sheet_loadctor__qry_sheet_load2_ptr = void (WINAPIV*)(struct _qry_sheet_load*);
        using _qry_sheet_loadctor__qry_sheet_load2_clbk = void (WINAPIV*)(struct _qry_sheet_load*, _qry_sheet_loadctor__qry_sheet_load2_ptr);
        using _qry_sheet_loadsize4_ptr = int (WINAPIV*)(struct _qry_sheet_load*);
        using _qry_sheet_loadsize4_clbk = int (WINAPIV*)(struct _qry_sheet_load*, _qry_sheet_loadsize4_ptr);
        
        using _qry_sheet_loaddtor__qry_sheet_load6_ptr = void (WINAPIV*)(struct _qry_sheet_load*);
        using _qry_sheet_loaddtor__qry_sheet_load6_clbk = void (WINAPIV*)(struct _qry_sheet_load*, _qry_sheet_loaddtor__qry_sheet_load6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

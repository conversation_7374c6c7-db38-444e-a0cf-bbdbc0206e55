// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <DLGITEMTEMPLATE.hpp>
#include <DLGTEMPLATE.hpp>


START_ATF_NAMESPACE
    struct _AFX_OCC_DIALOG_INFO
    {
        struct ItemInfo
        {
            unsigned int nId;
            int bAutoRadioButton;
        };
        DLGTEMPLATE *m_pNewTemplate;
        DLGITEMTEMPLATE **m_ppOleDlgItems;
        unsigned int m_cItems;
        ItemInfo *m_pItemInfo;
    };
END_ATF_NAMESPACE

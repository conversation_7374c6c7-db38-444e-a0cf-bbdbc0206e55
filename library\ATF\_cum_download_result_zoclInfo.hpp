// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_cum_download_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _cum_download_result_zoclctor__cum_download_result_zocl2_ptr = void (WINAPIV*)(struct _cum_download_result_zocl*);
        using _cum_download_result_zoclctor__cum_download_result_zocl2_clbk = void (WINAPIV*)(struct _cum_download_result_zocl*, _cum_download_result_zoclctor__cum_download_result_zocl2_ptr);
        using _cum_download_result_zoclsize4_ptr = int (WINAPIV*)(struct _cum_download_result_zocl*);
        using _cum_download_result_zoclsize4_clbk = int (WINAPIV*)(struct _cum_download_result_zocl*, _cum_download_result_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

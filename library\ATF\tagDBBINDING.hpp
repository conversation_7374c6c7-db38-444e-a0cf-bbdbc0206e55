// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ITypeInfo.hpp>
#include <tagDBBINDEXT.hpp>
#include <tagDBOBJECT.hpp>


START_ATF_NAMESPACE
    struct tagDBBINDING
    {
        unsigned __int64 iOrdinal;
        unsigned __int64 obValue;
        unsigned __int64 obLength;
        unsigned __int64 obStatus;
        ITypeInfo *pTypeInfo;
        tagDBOBJECT *pObject;
        tagDBBINDEXT *pBindExt;
        unsigned int dwPart;
        unsigned int dwMemOwner;
        unsigned int eParamIO;
        unsigned __int64 cbMaxLen;
        unsigned int dwFlags;
        unsigned __int16 wType;
        char bPrecision;
        char bScale;
    };
END_ATF_NAMESPACE

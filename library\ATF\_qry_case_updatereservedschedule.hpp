// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_updatereservedschedule
    {
        unsigned int dwMapID;
        unsigned int dwSLID;
        char byLoadDataStartPosition;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_updatereservedschedule, 12>(), "_qry_case_updatereservedschedule");
END_ATF_NAMESPACE

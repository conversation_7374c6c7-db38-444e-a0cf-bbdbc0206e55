// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUserDB.hpp>
#include <_LTD_ARRAY_ITEM.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _LTD_PARAM
    {
        CUserDB *m_pUserDB;
        _LTD_ARRAY_ITEM m_DstItem;
        _LTD_ARRAY_ITEM m_MtrItem1;
        _LTD_ARRAY_ITEM m_MtrItem2;
        char m_wszEtc[128];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

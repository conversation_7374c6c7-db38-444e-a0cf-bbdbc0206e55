// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CReservedGuildScheduleMapGroup.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupctor_CReservedGuildScheduleMapGroup2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupctor_CReservedGuildScheduleMapGroup2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, GUILD_BATTLE__CReservedGuildScheduleMapGroupctor_CReservedGuildScheduleMapGroup2_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupClear4_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupClear4_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, GUILD_BATTLE__CReservedGuildScheduleMapGroupClear4_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupFind6_ptr = struct GUILD_BATTLE::CReservedGuildSchedulePage* (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, unsigned int);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupFind6_clbk = struct GUILD_BATTLE::CReservedGuildSchedulePage* (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, unsigned int, GUILD_BATTLE__CReservedGuildScheduleMapGroupFind6_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupFlip8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupFlip8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, GUILD_BATTLE__CReservedGuildScheduleMapGroupFlip8_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupGetMaxPage10_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupGetMaxPage10_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, GUILD_BATTLE__CReservedGuildScheduleMapGroupGetMaxPage10_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupInit12_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, unsigned int);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupInit12_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, unsigned int, GUILD_BATTLE__CReservedGuildScheduleMapGroupInit12_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupLoad14_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, char, struct _worlddb_guild_battle_reserved_schedule_info*);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupLoad14_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, char, struct _worlddb_guild_battle_reserved_schedule_info*, GUILD_BATTLE__CReservedGuildScheduleMapGroupLoad14_ptr);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupSend16_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, int, unsigned int, char, struct GUILD_BATTLE::CReservedGuildSchedulePage*);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupSend16_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, int, unsigned int, char, struct GUILD_BATTLE::CReservedGuildSchedulePage*, GUILD_BATTLE__CReservedGuildScheduleMapGroupSend16_ptr);
            
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupdtor_CReservedGuildScheduleMapGroup20_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*);
            using GUILD_BATTLE__CReservedGuildScheduleMapGroupdtor_CReservedGuildScheduleMapGroup20_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CReservedGuildScheduleMapGroup*, GUILD_BATTLE__CReservedGuildScheduleMapGroupdtor_CReservedGuildScheduleMapGroup20_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

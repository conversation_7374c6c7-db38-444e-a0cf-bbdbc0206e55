// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _log_sheet_usernum
    {
        int nAveragePerHour;
        int nMaxPerHour;
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_log_sheet_usernum, 8>(), "_log_sheet_usernum");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$7DCE818CF0033D97DE575F75A488E5F7.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _IMAGE_SYMBOL
    {
        $7DCE818CF0033D97DE575F75A488E5F7 N;
        unsigned int Value;
        __int16 SectionNumber;
        unsigned __int16 Type;
        char StorageClass;
        char NumberOfAuxSymbols;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HACCEL__.hpp>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagOIFI
    {
        unsigned int cb;
        int fMDIApp;
        HWND__ *hwndFrame;
        HACCEL__ *haccel;
        unsigned int cAccelEntries;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

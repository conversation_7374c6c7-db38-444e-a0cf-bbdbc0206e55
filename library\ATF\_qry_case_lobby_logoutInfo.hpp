// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_lobby_logout.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_lobby_logoutctor__qry_case_lobby_logout2_ptr = void (WINAPIV*)(struct _qry_case_lobby_logout*);
        using _qry_case_lobby_logoutctor__qry_case_lobby_logout2_clbk = void (WINAPIV*)(struct _qry_case_lobby_logout*, _qry_case_lobby_logoutctor__qry_case_lobby_logout2_ptr);
        using _qry_case_lobby_logoutinit4_ptr = void (WINAPIV*)(struct _qry_case_lobby_logout*);
        using _qry_case_lobby_logoutinit4_clbk = void (WINAPIV*)(struct _qry_case_lobby_logout*, _qry_case_lobby_logoutinit4_ptr);
        using _qry_case_lobby_logoutsize6_ptr = int (WINAPIV*)(struct _qry_case_lobby_logout*);
        using _qry_case_lobby_logoutsize6_clbk = int (WINAPIV*)(struct _qry_case_lobby_logout*, _qry_case_lobby_logoutsize6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

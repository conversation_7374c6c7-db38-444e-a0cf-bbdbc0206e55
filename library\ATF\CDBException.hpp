// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CException.hpp>


START_ATF_NAMESPACE
    struct  CDBException : CException
    {
        __int16 m_nRetCode;
        ATL::CStringT<char> m_strError;
        ATL::CStringT<char> m_strStateNativeOrigin;
    };
END_ATF_NAMESPACE

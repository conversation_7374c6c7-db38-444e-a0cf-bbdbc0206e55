// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryTH.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryTHctor_CNationSettingFactoryTH2_ptr = void (WINAPIV*)(struct CNationSettingFactoryTH*);
        using CNationSettingFactoryTHctor_CNationSettingFactoryTH2_clbk = void (WINAPIV*)(struct CNationSettingFactoryTH*, CNationSettingFactoryTHctor_CNationSettingFactoryTH2_ptr);
        using CNationSettingFactoryTHCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryTH*, int, char*, bool);
        using CNationSettingFactoryTHCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryTH*, int, char*, bool, CNationSettingFactoryTHCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

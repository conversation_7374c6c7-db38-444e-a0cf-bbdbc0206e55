// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_s__RTTIBaseClassDescriptor2.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  $_s__RTTIBaseClassArray$_extraBytes_24
    {
        _s__RTTIBaseClassDescriptor2 *arrayOfBaseClassDescriptors[3];
        BYTE gap18[4];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

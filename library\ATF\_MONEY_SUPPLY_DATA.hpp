// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _MONEY_SUPPLY_DATA
    {
        struct _money_supply
        {
            int nLv[4];
            int nRace[3];
            int nClass[60];
        };
        __int64 dwAmount[9];
        _money_supply ms_data[4];
        int nFeeLv[4];
        int nFeeRace[3];
        int nHonorGuildRace[2][3];
        int nUnitRepairLv[4];
        int nBuyUnitLv[4];
    public:
        void init();
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

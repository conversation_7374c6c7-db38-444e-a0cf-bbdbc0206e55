// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__ATLTRACEPROCESSSETTINGS.hpp>



START_ATF_NAMESPACE
    namespace ATL
    {
        struct ATLTRACEPROCESSINFO
        {
            wchar_t szName[64];
            wchar_t szPath[260];
            unsigned int dwId;
            ATLTRACEPROCESSSETTINGS settings;
            int nModules;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

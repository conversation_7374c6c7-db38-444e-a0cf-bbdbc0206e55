// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ITypeInfo.hpp>
#include <tagDBID.hpp>



START_ATF_NAMESPACE
    struct tagDBCOLUMNINFO
    {
        wchar_t *pwszName;
        ITypeInfo *pTypeInfo;
        unsigned __int64 iOrdinal;
        unsigned int dwFlags;
        unsigned __int64 ulColumnSize;
        unsigned __int16 wType;
        char bPrecision;
        char bScale;
        tagDBID columnid;
    };
END_ATF_NAMESPACE

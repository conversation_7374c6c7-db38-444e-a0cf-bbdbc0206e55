// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <_PROCESS_INFORMATION.hpp>
#include <_SECURITY_ATTRIBUTES.hpp>
#include <_STARTUPINFOW.hpp>


START_ATF_NAMESPACE
    struct _SHCREATEPROCESSINFOW
    {
        unsigned int cbSize;
        unsigned int fMask;
        HWND__ *hwnd;
        const wchar_t *pszFile;
        const wchar_t *pszParameters;
        const wchar_t *pszCurrentDirectory;
        void *hUserToken;
        _SECURITY_ATTRIBUTES *lpProcessAttributes;
        _SECURITY_ATTRIBUTES *lpThreadAttributes;
        int bInheritHandles;
        unsigned int dwCreationFlags;
        _STARTUPINFOW *lpStartupInfo;
        _PROCESS_INFORMATION *lpProcessInformation;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pvp_order_view_inform_zocl
    {
        int nKillCnt;
        int nDeahtCnt;
        long double dTodayPvpPoint;
        long double dOriginalPvpPoint;
    public:
        _pvp_order_view_inform_zocl();
        void ctor__pvp_order_view_inform_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_pvp_order_view_inform_zocl, 24>(), "_pvp_order_view_inform_zocl");
END_ATF_NAMESPACE

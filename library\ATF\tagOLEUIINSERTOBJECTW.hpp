// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <HRSRC__.hpp>
#include <HWND__.hpp>
#include <IOleClientSite.hpp>
#include <IStorage.hpp>
#include <_GUID.hpp>
#include <tagFORMATETC.hpp>



START_ATF_NAMESPACE
    struct tagOLEUIINSERTOBJECTW
    {
        unsigned int cbStruct;
        unsigned int dwFlags;
        HWND__ *hWndOwner;
        const wchar_t *lpszCaption;
        unsigned int (WINAPIV *lpfnHook)(HWND__ *, unsigned int, unsigned __int64, __int64);
        __int64 lCustData;
        HINSTANCE__ *hInstance;
        const wchar_t *lpszTemplate;
        HRSRC__ *hResource;
        _GUID clsid;
        wchar_t *lpszFile;
        unsigned int cchFile;
        unsigned int cClsidExclude;
        _GUID *lpClsidExclude;
        _GUID iid;
        unsigned int oleRender;
        tagFORMATETC *lpFormatEtc;
        IOleClientSite *lpIOleClientSite;
        IStorage *lpIStorage;
        void **ppvObj;
        int sc;
        void *hMetaPict;
    };
END_ATF_NAMESPACE

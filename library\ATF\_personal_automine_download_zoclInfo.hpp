// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_personal_automine_download_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _personal_automine_download_zoclsize2_ptr = int (WINAPIV*)(struct _personal_automine_download_zocl*);
        using _personal_automine_download_zoclsize2_clbk = int (WINAPIV*)(struct _personal_automine_download_zocl*, _personal_automine_download_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

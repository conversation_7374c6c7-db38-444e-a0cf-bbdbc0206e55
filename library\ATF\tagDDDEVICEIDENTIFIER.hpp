// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    struct tagDDDEVICEIDENTIFIER
    {
        char szDriver[512];
        char szDescription[512];
        _LARGE_INTEGER liDriverVersion;
        unsigned int dwVendorId;
        unsigned int dwDeviceId;
        unsigned int dwSubSysId;
        unsigned int dwRevision;
        _GUID guidDeviceIdentifier;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGameObject.hpp>
#include <CMapData.hpp>
#include <CMapDataTable.hpp>
#include <CMapOperationVtbl.hpp>
#include <CMyTimer.hpp>
#include <_region_data.hpp>
#include <std__vector.hpp>
#include <std__pair.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMapOperation
    {
        CMapOperationVtbl *vfptr;
        int m_nLoopStartPoint;
        unsigned int m_dwSpeedHackStdTime;
        CMapDataTable m_tblMapData;
        int m_nMapNum;
        int m_nStdMapNum;
        std::vector<std::pair<int,int>> m_vecStandardMapCodeTable;
        CMapData *m_Map;
        int m_nRegionNum;
        _region_data m_RegionData[100];
        bool m_bReSpawnMonster;
        CMyTimer m_tmrObjTerm;
        CMyTimer m_tmrSystem;
        CMyTimer m_tmrRecover;
        unsigned int m_dwLastObjLoopTime;
        CMapData *m_SettlementMapData[3][2];
    public:
        CMapOperation();
        void ctor_CMapOperation();
        void CheckMapPortalLink();
        int GetMap(struct CMapData* pMap);
        struct CMapData* GetMap(char* szMapCode);
        struct CMapData* GetMap(int nIndex);
        struct CMapData* GetPosStartMap(char byRaceCode, bool bRand, float* pfoutPos);
        struct CMapData* GetSettlementMapData(int iRace, int iTh);
        struct CMapData* GetStartMap(char byRaceCode);
        bool Init();
        bool IsExistStdMapID(int iMapID);
        bool IsInRegion(char* pszRegionCode, struct CGameObject* pObj);
        bool LoadMaps();
        bool LoadRegion();
        void OnLoop();
        void RespawnMonster();
        ~CMapOperation();
        void dtor_CMapOperation();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CMapOperation, 5040>(), "CMapOperation");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CExchangeEvent.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CExchangeEventctor_CExchangeEvent2_ptr = void (WINAPIV*)(struct CExchangeEvent*);
        using CExchangeEventctor_CExchangeEvent2_clbk = void (WINAPIV*)(struct CExchangeEvent*, CExchangeEventctor_CExchangeEvent2_ptr);
        using CExchangeEventChangeData4_ptr = void (WINAPIV*)(struct CExchangeEvent*);
        using CExchangeEventChangeData4_clbk = void (WINAPIV*)(struct CExchangeEvent*, CExchangeEventChangeData4_ptr);
        using CExchangeEventCheckBuddhaEventData6_ptr = bool (WINAPIV*)(struct CExchangeEvent*, bool*);
        using CExchangeEventCheckBuddhaEventData6_clbk = bool (WINAPIV*)(struct CExchangeEvent*, bool*, CExchangeEventCheckBuddhaEventData6_ptr);
        using CExchangeEventDeleteExchangeEventItem8_ptr = void (WINAPIV*)(struct CExchangeEvent*, struct CPlayer*);
        using CExchangeEventDeleteExchangeEventItem8_clbk = void (WINAPIV*)(struct CExchangeEvent*, struct CPlayer*, CExchangeEventDeleteExchangeEventItem8_ptr);
        using CExchangeEventDestroy10_ptr = void (WINAPIV*)();
        using CExchangeEventDestroy10_clbk = void (WINAPIV*)(CExchangeEventDestroy10_ptr);
        using CExchangeEventGetEventItemInfo12_ptr = struct EventItemInfo* (WINAPIV*)(struct CExchangeEvent*, int);
        using CExchangeEventGetEventItemInfo12_clbk = struct EventItemInfo* (WINAPIV*)(struct CExchangeEvent*, int, CExchangeEventGetEventItemInfo12_ptr);
        using CExchangeEventGiveEventItem14_ptr = void (WINAPIV*)(struct CExchangeEvent*, struct CPlayer*);
        using CExchangeEventGiveEventItem14_clbk = void (WINAPIV*)(struct CExchangeEvent*, struct CPlayer*, CExchangeEventGiveEventItem14_ptr);
        using CExchangeEventInitialzie16_ptr = bool (WINAPIV*)(struct CExchangeEvent*);
        using CExchangeEventInitialzie16_clbk = bool (WINAPIV*)(struct CExchangeEvent*, CExchangeEventInitialzie16_ptr);
        using CExchangeEventInstance18_ptr = struct CExchangeEvent* (WINAPIV*)();
        using CExchangeEventInstance18_clbk = struct CExchangeEvent* (WINAPIV*)(CExchangeEventInstance18_ptr);
        using CExchangeEventIsDelete20_ptr = bool (WINAPIV*)(struct CExchangeEvent*);
        using CExchangeEventIsDelete20_clbk = bool (WINAPIV*)(struct CExchangeEvent*, CExchangeEventIsDelete20_ptr);
        using CExchangeEventIsEnable22_ptr = bool (WINAPIV*)(struct CExchangeEvent*);
        using CExchangeEventIsEnable22_clbk = bool (WINAPIV*)(struct CExchangeEvent*, CExchangeEventIsEnable22_ptr);
        using CExchangeEventIsWait24_ptr = bool (WINAPIV*)(struct CExchangeEvent*);
        using CExchangeEventIsWait24_clbk = bool (WINAPIV*)(struct CExchangeEvent*, CExchangeEventIsWait24_ptr);
        using CExchangeEventLoop26_ptr = void (WINAPIV*)(struct CExchangeEvent*);
        using CExchangeEventLoop26_clbk = void (WINAPIV*)(struct CExchangeEvent*, CExchangeEventLoop26_ptr);
        using CExchangeEventReadBuddhaEventInfo28_ptr = void (WINAPIV*)(struct CExchangeEvent*);
        using CExchangeEventReadBuddhaEventInfo28_clbk = void (WINAPIV*)(struct CExchangeEvent*, CExchangeEventReadBuddhaEventInfo28_ptr);
        
        using CExchangeEventdtor_CExchangeEvent33_ptr = void (WINAPIV*)(struct CExchangeEvent*);
        using CExchangeEventdtor_CExchangeEvent33_clbk = void (WINAPIV*)(struct CExchangeEvent*, CExchangeEventdtor_CExchangeEvent33_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

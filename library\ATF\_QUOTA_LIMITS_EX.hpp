// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    struct _QUOTA_LIMITS_EX
    {
        unsigned __int64 PagedPoolLimit;
        unsigned __int64 NonPagedPoolLimit;
        unsigned __int64 MinimumWorkingSetSize;
        unsigned __int64 MaximumWorkingSetSize;
        unsigned __int64 PagefileLimit;
        _LARGE_INTEGER TimeLimit;
        unsigned __int64 Reserved1;
        unsigned __int64 Reserved2;
        unsigned __int64 Reserved3;
        unsigned __int64 Reserved4;
        unsigned int Flags;
        unsigned int Reserved5;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMainThread.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CMainThreadAccountServerLogin2_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadAccountServerLogin2_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadAccountServerLogin2_ptr);
        using CMainThreadAddGuildBattleSchdule4_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadAddGuildBattleSchdule4_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadAddGuildBattleSchdule4_ptr);
        using CMainThreadAddPassablePacket6_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadAddPassablePacket6_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadAddPassablePacket6_ptr);
        using CMainThreadAlive_Char_Complete8_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadAlive_Char_Complete8_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadAlive_Char_Complete8_ptr);
        
        using CMainThreadctor_CMainThread10_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadctor_CMainThread10_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadctor_CMainThread10_ptr);
        using CMainThreadCashDBInit12_ptr = bool (WINAPIV*)(struct CMainThread*, char*, char*, char*, char*, unsigned int);
        using CMainThreadCashDBInit12_clbk = bool (WINAPIV*)(struct CMainThread*, char*, char*, char*, char*, unsigned int, CMainThreadCashDBInit12_ptr);
        using CMainThreadCheckAccountLineState14_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadCheckAccountLineState14_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadCheckAccountLineState14_ptr);
        using CMainThreadCheckAvatorState16_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadCheckAvatorState16_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadCheckAvatorState16_ptr);
        using CMainThreadCheckConnNumLog18_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadCheckConnNumLog18_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadCheckConnNumLog18_ptr);
        using CMainThreadCheckDayChangedPvpPointClear20_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadCheckDayChangedPvpPointClear20_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadCheckDayChangedPvpPointClear20_ptr);
        using CMainThreadCheckDefine22_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadCheckDefine22_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadCheckDefine22_ptr);
        using CMainThreadCheckForceClose24_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadCheckForceClose24_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadCheckForceClose24_ptr);
        using CMainThreadCheckRadarItemDelay26_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadCheckRadarItemDelay26_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadCheckRadarItemDelay26_ptr);
        using CMainThreadCheckServerRateINIFile28_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadCheckServerRateINIFile28_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadCheckServerRateINIFile28_ptr);
        using CMainThreadCheckServiceableTime30_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadCheckServiceableTime30_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadCheckServiceableTime30_ptr);
        using CMainThreadCompleteLoadGuildBattleTotalRecord32_ptr = void (WINAPIV*)(struct CMainThread*, char, char*);
        using CMainThreadCompleteLoadGuildBattleTotalRecord32_clbk = void (WINAPIV*)(struct CMainThread*, char, char*, CMainThreadCompleteLoadGuildBattleTotalRecord32_ptr);
        using CMainThreadCompleteUpdatePlayerVoteInfo34_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadCompleteUpdatePlayerVoteInfo34_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadCompleteUpdatePlayerVoteInfo34_ptr);
        using CMainThreadCompleteUpdateServerToken36_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadCompleteUpdateServerToken36_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadCompleteUpdateServerToken36_ptr);
        using CMainThreadCompleteUpdateSetLimitRun38_ptr = void (WINAPIV*)(struct CMainThread*, char, char*);
        using CMainThreadCompleteUpdateSetLimitRun38_clbk = void (WINAPIV*)(struct CMainThread*, char, char*, CMainThreadCompleteUpdateSetLimitRun38_ptr);
        using CMainThreadCompleteUpdateVoteAvailable40_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadCompleteUpdateVoteAvailable40_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadCompleteUpdateVoteAvailable40_ptr);
        using CMainThreadComplete_Select_RegeAvator_For_Lobby_Logout42_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadComplete_Select_RegeAvator_For_Lobby_Logout42_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadComplete_Select_RegeAvator_For_Lobby_Logout42_ptr);
        using CMainThreadComplete_db_Update_Data_For_Post_Send44_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadComplete_db_Update_Data_For_Post_Send44_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadComplete_db_Update_Data_For_Post_Send44_ptr);
        using CMainThreadComplete_db_Update_Data_For_Trade46_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadComplete_db_Update_Data_For_Trade46_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadComplete_db_Update_Data_For_Trade46_ptr);
        using CMainThreadContUserSaveJobCheck48_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadContUserSaveJobCheck48_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadContUserSaveJobCheck48_ptr);
        using CMainThreadCont_UserSave_Complete50_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadCont_UserSave_Complete50_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadCont_UserSave_Complete50_ptr);
        using CMainThreadCreateDataResetToken52_ptr = unsigned int (WINAPIV*)(struct CMainThread*, struct _SYSTEMTIME*);
        using CMainThreadCreateDataResetToken52_clbk = unsigned int (WINAPIV*)(struct CMainThread*, struct _SYSTEMTIME*, CMainThreadCreateDataResetToken52_ptr);
        using CMainThreadCreateSelectCharacterLogTable54_ptr = void (WINAPIV*)(struct CMainThread*, char);
        using CMainThreadCreateSelectCharacterLogTable54_clbk = void (WINAPIV*)(struct CMainThread*, char, CMainThreadCreateSelectCharacterLogTable54_ptr);
        using CMainThreadDQSCompleteProcess56_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadDQSCompleteProcess56_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadDQSCompleteProcess56_ptr);
        using CMainThreadDQSThread58_ptr = void (WINAPIV*)(void*);
        using CMainThreadDQSThread58_clbk = void (WINAPIV*)(void*, CMainThreadDQSThread58_ptr);
        using CMainThreadDataFileInit60_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadDataFileInit60_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadDataFileInit60_ptr);
        using CMainThreadDatabaseInit62_ptr = bool (WINAPIV*)(struct CMainThread*, char*, char*);
        using CMainThreadDatabaseInit62_clbk = bool (WINAPIV*)(struct CMainThread*, char*, char*, CMainThreadDatabaseInit62_ptr);
        using CMainThreadDelete_Avator_Complete64_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadDelete_Avator_Complete64_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadDelete_Avator_Complete64_ptr);
        using CMainThreadEndServer66_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadEndServer66_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadEndServer66_ptr);
        using CMainThreadForceCloseUserInTiming68_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadForceCloseUserInTiming68_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadForceCloseUserInTiming68_ptr);
        using CMainThreadGetChar70_ptr = struct CGameObject* (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadGetChar70_clbk = struct CGameObject* (WINAPIV*)(struct CMainThread*, char*, CMainThreadGetChar70_ptr);
        using CMainThreadGetCharW72_ptr = struct CGameObject* (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadGetCharW72_clbk = struct CGameObject* (WINAPIV*)(struct CMainThread*, char*, CMainThreadGetCharW72_ptr);
        using CMainThreadGetObjectA74_ptr = struct CGameObject* (WINAPIV*)(struct CMainThread*, struct _object_id*);
        using CMainThreadGetObjectA74_clbk = struct CGameObject* (WINAPIV*)(struct CMainThread*, struct _object_id*, CMainThreadGetObjectA74_ptr);
        using CMainThreadGetObjectA76_ptr = struct CGameObject* (WINAPIV*)(struct CMainThread*, int, int, int);
        using CMainThreadGetObjectA76_clbk = struct CGameObject* (WINAPIV*)(struct CMainThread*, int, int, int, CMainThreadGetObjectA76_ptr);
        using CMainThreadGetObjectExpand78_ptr = struct CGameObject* (WINAPIV*)(struct CMainThread*, struct _object_id*, char*, uint16_t);
        using CMainThreadGetObjectExpand78_clbk = struct CGameObject* (WINAPIV*)(struct CMainThread*, struct _object_id*, char*, uint16_t, CMainThreadGetObjectExpand78_ptr);
        using CMainThreadGetTommorrowStr80_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadGetTommorrowStr80_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadGetTommorrowStr80_ptr);
        using CMainThreadInAtradTaxMoney82_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadInAtradTaxMoney82_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadInAtradTaxMoney82_ptr);
        using CMainThreadInGuildbattleCost84_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadInGuildbattleCost84_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadInGuildbattleCost84_ptr);
        using CMainThreadInGuildbattleRewardMoney86_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadInGuildbattleRewardMoney86_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadInGuildbattleRewardMoney86_ptr);
        using CMainThreadInit88_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadInit88_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadInit88_ptr);
        using CMainThreadInsert_Avator_Complete90_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadInsert_Avator_Complete90_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadInsert_Avator_Complete90_ptr);
        using CMainThreadIsExcuteService92_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadIsExcuteService92_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadIsExcuteService92_ptr);
        using CMainThreadIsReleaseServiceMode94_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadIsReleaseServiceMode94_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadIsReleaseServiceMode94_ptr);
        using CMainThreadIsTestServer96_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadIsTestServer96_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadIsTestServer96_ptr);
        using CMainThreadLoadINI98_ptr = int (WINAPIV*)(struct CMainThread*);
        using CMainThreadLoadINI98_clbk = int (WINAPIV*)(struct CMainThread*, CMainThreadLoadINI98_ptr);
        using CMainThreadLoadItemConsumeINI100_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadLoadItemConsumeINI100_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadLoadItemConsumeINI100_ptr);
        using CMainThreadLoadLimitInfo102_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadLoadLimitInfo102_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadLoadLimitInfo102_ptr);
        using CMainThreadLoadServerRateINIFile104_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadLoadServerRateINIFile104_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadLoadServerRateINIFile104_ptr);
        using CMainThreadLoadWorldInfoINI106_ptr = int (WINAPIV*)(struct CMainThread*);
        using CMainThreadLoadWorldInfoINI106_clbk = int (WINAPIV*)(struct CMainThread*, CMainThreadLoadWorldInfoINI106_ptr);
        using CMainThreadLoadWorldSystemINI108_ptr = int (WINAPIV*)(struct CMainThread*);
        using CMainThreadLoadWorldSystemINI108_clbk = int (WINAPIV*)(struct CMainThread*, CMainThreadLoadWorldSystemINI108_ptr);
        using CMainThreadLoad_Content_Complete110_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadLoad_Content_Complete110_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadLoad_Content_Complete110_ptr);
        using CMainThreadLoad_PostStorage_Complete112_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadLoad_PostStorage_Complete112_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadLoad_PostStorage_Complete112_ptr);
        using CMainThreadLoad_ReturnPost_Complete114_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadLoad_ReturnPost_Complete114_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadLoad_ReturnPost_Complete114_ptr);
        using CMainThreadLobby_Account_Complete116_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadLobby_Account_Complete116_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadLobby_Account_Complete116_ptr);
        using CMainThreadLogout_Account_Complete118_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadLogout_Account_Complete118_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadLogout_Account_Complete118_ptr);
        using CMainThreadMakeSystemTower120_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadMakeSystemTower120_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadMakeSystemTower120_ptr);
        using CMainThreadManageClientLimitRunRequest122_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadManageClientLimitRunRequest122_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadManageClientLimitRunRequest122_ptr);
        using CMainThreadNetworkInit124_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadNetworkInit124_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadNetworkInit124_ptr);
        using CMainThreadObjectInit126_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadObjectInit126_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadObjectInit126_ptr);
        using CMainThreadOnDQSRun128_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadOnDQSRun128_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadOnDQSRun128_ptr);
        using CMainThreadOnRun130_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadOnRun130_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadOnRun130_ptr);
        using CMainThreadOutDestGuildbattleCost132_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadOutDestGuildbattleCost132_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadOutDestGuildbattleCost132_ptr);
        using CMainThreadOutSrcGuildbattleCost134_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadOutSrcGuildbattleCost134_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadOutSrcGuildbattleCost134_ptr);
        using CMainThreadPingToAccount136_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadPingToAccount136_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadPingToAccount136_ptr);
        using CMainThreadPushDQSData138_ptr = struct _DB_QRY_SYN_DATA* (WINAPIV*)(struct CMainThread*, unsigned int, struct _CLID*, char, char*, int);
        using CMainThreadPushDQSData138_clbk = struct _DB_QRY_SYN_DATA* (WINAPIV*)(struct CMainThread*, unsigned int, struct _CLID*, char, char*, int, CMainThreadPushDQSData138_ptr);
        using CMainThreadPushResetServerToken140_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadPushResetServerToken140_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadPushResetServerToken140_ptr);
        using CMainThreadPush_ChargeItem142_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, unsigned int, char);
        using CMainThreadPush_ChargeItem142_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, unsigned int, char, CMainThreadPush_ChargeItem142_ptr);
        using CMainThreadQryCaseAddpvppoint144_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadQryCaseAddpvppoint144_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadQryCaseAddpvppoint144_ptr);
        using CMainThreadReged_Avator_Complete146_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadReged_Avator_Complete146_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadReged_Avator_Complete146_ptr);
        using CMainThreadRelease148_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadRelease148_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadRelease148_ptr);
        using CMainThreadRuleThread150_ptr = void (WINAPIV*)(void*);
        using CMainThreadRuleThread150_clbk = void (WINAPIV*)(void*, CMainThreadRuleThread150_ptr);
        using CMainThreadSelect_Avator_Complete152_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadSelect_Avator_Complete152_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadSelect_Avator_Complete152_ptr);
        using CMainThreadSendWebRaceBossSMS154_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadSendWebRaceBossSMS154_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadSendWebRaceBossSMS154_ptr);
        using CMainThreadSerivceForceSet156_ptr = void (WINAPIV*)(struct CMainThread*, bool);
        using CMainThreadSerivceForceSet156_clbk = void (WINAPIV*)(struct CMainThread*, bool, CMainThreadSerivceForceSet156_ptr);
        using CMainThreadSerivceSelfStart158_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadSerivceSelfStart158_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadSerivceSelfStart158_ptr);
        using CMainThreadSerivceSelfStop160_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadSerivceSelfStop160_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadSerivceSelfStop160_ptr);
        using CMainThreadServerStateMsgGotoWebAgent162_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadServerStateMsgGotoWebAgent162_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadServerStateMsgGotoWebAgent162_ptr);
        using CMainThreadSetGlobalDataName164_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadSetGlobalDataName164_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadSetGlobalDataName164_ptr);
        using CMainThreadSetServerRate166_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadSetServerRate166_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadSetServerRate166_ptr);
        using CMainThreadUpdateGuildBattleDrawRankInfo168_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadUpdateGuildBattleDrawRankInfo168_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadUpdateGuildBattleDrawRankInfo168_ptr);
        using CMainThreadUpdateGuildBattleWinLoseRankInfo170_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadUpdateGuildBattleWinLoseRankInfo170_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadUpdateGuildBattleWinLoseRankInfo170_ptr);
        using CMainThreadUpdateLoadGuildBattleRank172_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadUpdateLoadGuildBattleRank172_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadUpdateLoadGuildBattleRank172_ptr);
        using CMainThreadUpdateReservedGuildBattleSchedule174_ptr = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*);
        using CMainThreadUpdateReservedGuildBattleSchedule174_clbk = void (WINAPIV*)(struct CMainThread*, struct _DB_QRY_SYN_DATA*, CMainThreadUpdateReservedGuildBattleSchedule174_ptr);
        using CMainThreadValidMacAddress176_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadValidMacAddress176_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadValidMacAddress176_ptr);
        using CMainThread_CheckGuildCheckSum178_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, char*, long double*, long double*);
        using CMainThread_CheckGuildCheckSum178_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, char*, long double*, long double*, CMainThread_CheckGuildCheckSum178_ptr);
        using CMainThread_CheckTotalSales180_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThread_CheckTotalSales180_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThread_CheckTotalSales180_ptr);
        using CMainThread_GameDataBaseInit182_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThread_GameDataBaseInit182_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThread_GameDataBaseInit182_ptr);
        using CMainThread_db_Check_NpcData184_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*);
        using CMainThread_db_Check_NpcData184_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, CMainThread_db_Check_NpcData184_ptr);
        using CMainThread_db_GuildRoom_Insert186_ptr = char (WINAPIV*)(struct CMainThread*, struct _qry_case_guildroom_insert*);
        using CMainThread_db_GuildRoom_Insert186_clbk = char (WINAPIV*)(struct CMainThread*, struct _qry_case_guildroom_insert*, CMainThread_db_GuildRoom_Insert186_ptr);
        using CMainThread_db_GuildRoom_Update188_ptr = bool (WINAPIV*)(struct CMainThread*, struct _qry_case_guildroom_update*);
        using CMainThread_db_GuildRoom_Update188_clbk = bool (WINAPIV*)(struct CMainThread*, struct _qry_case_guildroom_update*, CMainThread_db_GuildRoom_Update188_ptr);
        using CMainThread_db_Load_Base190_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*);
        using CMainThread_db_Load_Base190_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, CMainThread_db_Load_Base190_ptr);
        using CMainThread_db_Load_BattleTournamentInfo192_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThread_db_Load_BattleTournamentInfo192_clbk = void (WINAPIV*)(struct CMainThread*, CMainThread_db_Load_BattleTournamentInfo192_ptr);
        using CMainThread_db_Load_Buddy194_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _BUDDY_DB_BASE*);
        using CMainThread_db_Load_Buddy194_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _BUDDY_DB_BASE*, CMainThread_db_Load_Buddy194_ptr);
        using CMainThread_db_Load_Cash_LimSale196_ptr = char (WINAPIV*)(struct CMainThread*, struct qry_case_cash_limsale*);
        using CMainThread_db_Load_Cash_LimSale196_clbk = char (WINAPIV*)(struct CMainThread*, struct qry_case_cash_limsale*, CMainThread_db_Load_Cash_LimSale196_ptr);
        using CMainThread_db_Load_CryMsg198_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _CRYMSG_DB_BASE*);
        using CMainThread_db_Load_CryMsg198_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _CRYMSG_DB_BASE*, CMainThread_db_Load_CryMsg198_ptr);
        using CMainThread_db_Load_General200_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, char, struct _AVATOR_DATA*);
        using CMainThread_db_Load_General200_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, char, struct _AVATOR_DATA*, CMainThread_db_Load_General200_ptr);
        using CMainThread_db_Load_GoldBoxItem202_ptr = char (WINAPIV*)(struct CMainThread*, struct qry_case_select_golden_box_item*, int*);
        using CMainThread_db_Load_GoldBoxItem202_clbk = char (WINAPIV*)(struct CMainThread*, struct qry_case_select_golden_box_item*, int*, CMainThread_db_Load_GoldBoxItem202_ptr);
        using CMainThread_db_Load_Inven204_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, int, struct _INVEN_DB_BASE*);
        using CMainThread_db_Load_Inven204_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, int, struct _INVEN_DB_BASE*, CMainThread_db_Load_Inven204_ptr);
        using CMainThread_db_Load_ItemCombineEx206_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _ITEMCOMBINE_DB_BASE*);
        using CMainThread_db_Load_ItemCombineEx206_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _ITEMCOMBINE_DB_BASE*, CMainThread_db_Load_ItemCombineEx206_ptr);
        using CMainThread_db_Load_MacroData208_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AIOC_A_MACRODATA*);
        using CMainThread_db_Load_MacroData208_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AIOC_A_MACRODATA*, CMainThread_db_Load_MacroData208_ptr);
        using CMainThread_db_Load_NpcQuest_History210_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _QUEST_DB_BASE*);
        using CMainThread_db_Load_NpcQuest_History210_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _QUEST_DB_BASE*, CMainThread_db_Load_NpcQuest_History210_ptr);
        using CMainThread_db_Load_OreCutting212_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _CUTTING_DB_BASE*);
        using CMainThread_db_Load_OreCutting212_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _CUTTING_DB_BASE*, CMainThread_db_Load_OreCutting212_ptr);
        using CMainThread_db_Load_PatriarchComm214_ptr = int (WINAPIV*)(struct CMainThread*, char*);
        using CMainThread_db_Load_PatriarchComm214_clbk = int (WINAPIV*)(struct CMainThread*, char*, CMainThread_db_Load_PatriarchComm214_ptr);
        using CMainThread_db_Load_PcBangFavor216_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _PCBANG_FAVOR_ITEM_DB_BASE*);
        using CMainThread_db_Load_PcBangFavor216_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _PCBANG_FAVOR_ITEM_DB_BASE*, CMainThread_db_Load_PcBangFavor216_ptr);
        using CMainThread_db_Load_PotionDelay218_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _POTION_NEXT_USE_TIME_DB_BASE*);
        using CMainThread_db_Load_PotionDelay218_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _POTION_NEXT_USE_TIME_DB_BASE*, CMainThread_db_Load_PotionDelay218_ptr);
        using CMainThread_db_Load_PrimiumPlayTime220_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _PCBANG_PLAY_TIME*);
        using CMainThread_db_Load_PrimiumPlayTime220_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _PCBANG_PLAY_TIME*, CMainThread_db_Load_PrimiumPlayTime220_ptr);
        using CMainThread_db_Load_PvpOrderView222_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _PVP_ORDER_VIEW_DB_BASE*);
        using CMainThread_db_Load_PvpOrderView222_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _PVP_ORDER_VIEW_DB_BASE*, CMainThread_db_Load_PvpOrderView222_ptr);
        using CMainThread_db_Load_PvpPointLimitData224_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _PVPPOINT_LIMIT_DB_BASE*);
        using CMainThread_db_Load_PvpPointLimitData224_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _PVPPOINT_LIMIT_DB_BASE*, CMainThread_db_Load_PvpPointLimitData224_ptr);
        using CMainThread_db_Load_Quest226_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _QUEST_DB_BASE*);
        using CMainThread_db_Load_Quest226_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _QUEST_DB_BASE*, CMainThread_db_Load_Quest226_ptr);
        using CMainThread_db_Load_SFDelayData228_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _worlddb_sf_delay_info*);
        using CMainThread_db_Load_SFDelayData228_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _worlddb_sf_delay_info*, CMainThread_db_Load_SFDelayData228_ptr);
        using CMainThread_db_Load_Start_NpcQuest_History230_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, char, struct _QUEST_DB_BASE*);
        using CMainThread_db_Load_Start_NpcQuest_History230_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, char, struct _QUEST_DB_BASE*, CMainThread_db_Load_Start_NpcQuest_History230_ptr);
        using CMainThread_db_Load_Supplement232_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _SUPPLEMENT_DB_BASE*);
        using CMainThread_db_Load_Supplement232_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _SUPPLEMENT_DB_BASE*, CMainThread_db_Load_Supplement232_ptr);
        using CMainThread_db_Load_TimeLimitInfo234_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _TIMELIMITINFO_DB_BASE*);
        using CMainThread_db_Load_TimeLimitInfo234_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _TIMELIMITINFO_DB_BASE*, CMainThread_db_Load_TimeLimitInfo234_ptr);
        using CMainThread_db_Load_Trade236_ptr = char (WINAPIV*)(struct CMainThread*, char, unsigned int, struct _TRADE_DB_BASE*);
        using CMainThread_db_Load_Trade236_clbk = char (WINAPIV*)(struct CMainThread*, char, unsigned int, struct _TRADE_DB_BASE*, CMainThread_db_Load_Trade236_ptr);
        using CMainThread_db_Load_Trunk238_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, char, struct _TRUNK_DB_BASE*);
        using CMainThread_db_Load_Trunk238_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, char, struct _TRUNK_DB_BASE*, CMainThread_db_Load_Trunk238_ptr);
        using CMainThread_db_Load_UI240_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _LINK_DB_BASE*, struct _SFCONT_DB_BASE*);
        using CMainThread_db_Load_UI240_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _LINK_DB_BASE*, struct _SFCONT_DB_BASE*, CMainThread_db_Load_UI240_ptr);
        using CMainThread_db_Load_Unit242_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _UNIT_DB_BASE*);
        using CMainThread_db_Load_Unit242_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _UNIT_DB_BASE*, CMainThread_db_Load_Unit242_ptr);
        using CMainThread_db_Select_RegeAvator_For_Lobby_Logout244_ptr = char (WINAPIV*)(struct CMainThread*, char*);
        using CMainThread_db_Select_RegeAvator_For_Lobby_Logout244_clbk = char (WINAPIV*)(struct CMainThread*, char*, CMainThread_db_Select_RegeAvator_For_Lobby_Logout244_ptr);
        using CMainThread_db_Update_Base246_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, bool);
        using CMainThread_db_Update_Base246_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, bool, CMainThread_db_Update_Base246_ptr);
        using CMainThread_db_Update_Buddy248_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_Buddy248_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_Buddy248_ptr);
        using CMainThread_db_Update_Cash_LimSale250_ptr = char (WINAPIV*)(struct CMainThread*, struct _db_cash_limited_sale*, struct _db_cash_limited_sale*);
        using CMainThread_db_Update_Cash_LimSale250_clbk = char (WINAPIV*)(struct CMainThread*, struct _db_cash_limited_sale*, struct _db_cash_limited_sale*, CMainThread_db_Update_Cash_LimSale250_ptr);
        using CMainThread_db_Update_CryMsg252_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_CryMsg252_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_CryMsg252_ptr);
        using CMainThread_db_Update_Data_For_Post_Send254_ptr = char (WINAPIV*)(struct CMainThread*, char*);
        using CMainThread_db_Update_Data_For_Post_Send254_clbk = char (WINAPIV*)(struct CMainThread*, char*, CMainThread_db_Update_Data_For_Post_Send254_ptr);
        using CMainThread_db_Update_Data_For_Trade256_ptr = char (WINAPIV*)(struct CMainThread*, char*);
        using CMainThread_db_Update_Data_For_Trade256_clbk = char (WINAPIV*)(struct CMainThread*, char*, CMainThread_db_Update_Data_For_Trade256_ptr);
        using CMainThread_db_Update_General258_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, bool);
        using CMainThread_db_Update_General258_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, bool, CMainThread_db_Update_General258_ptr);
        using CMainThread_db_Update_GoldBoxItem260_ptr = char (WINAPIV*)(struct CMainThread*, int, struct _db_golden_box_item*, struct _db_golden_box_item*);
        using CMainThread_db_Update_GoldBoxItem260_clbk = char (WINAPIV*)(struct CMainThread*, int, struct _db_golden_box_item*, struct _db_golden_box_item*, CMainThread_db_Update_GoldBoxItem260_ptr);
        using CMainThread_db_Update_Inven262_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_Inven262_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_Inven262_ptr);
        using CMainThread_db_Update_ItemCombineEx264_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_ItemCombineEx264_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_ItemCombineEx264_ptr);
        using CMainThread_db_Update_MacroData266_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AIOC_A_MACRODATA*, struct _AIOC_A_MACRODATA*);
        using CMainThread_db_Update_MacroData266_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AIOC_A_MACRODATA*, struct _AIOC_A_MACRODATA*, CMainThread_db_Update_MacroData266_ptr);
        using CMainThread_db_Update_NpcData268_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_NpcData268_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, char*, CMainThread_db_Update_NpcData268_ptr);
        using CMainThread_db_Update_NpcQuest_History270_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_NpcQuest_History270_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_NpcQuest_History270_ptr);
        using CMainThread_db_Update_OreCutting272_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int);
        using CMainThread_db_Update_OreCutting272_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int, CMainThread_db_Update_OreCutting272_ptr);
        using CMainThread_db_Update_PcBangFavor274_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int);
        using CMainThread_db_Update_PcBangFavor274_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int, CMainThread_db_Update_PcBangFavor274_ptr);
        using CMainThread_db_Update_PotionDelay276_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int);
        using CMainThread_db_Update_PotionDelay276_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int, CMainThread_db_Update_PotionDelay276_ptr);
        using CMainThread_db_Update_PrimiumPlayTime278_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, char*);
        using CMainThread_db_Update_PrimiumPlayTime278_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, char*, CMainThread_db_Update_PrimiumPlayTime278_ptr);
        using CMainThread_db_Update_PvpOrderView280_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, char*);
        using CMainThread_db_Update_PvpOrderView280_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, char*, CMainThread_db_Update_PvpOrderView280_ptr);
        using CMainThread_db_Update_PvpPointLimit282_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, char*);
        using CMainThread_db_Update_PvpPointLimit282_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, char*, CMainThread_db_Update_PvpPointLimit282_ptr);
        using CMainThread_db_Update_Quest284_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_Quest284_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_Quest284_ptr);
        using CMainThread_db_Update_SFDelayData286_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*);
        using CMainThread_db_Update_SFDelayData286_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, CMainThread_db_Update_SFDelayData286_ptr);
        using CMainThread_db_Update_Set_Limit_Run288_ptr = char (WINAPIV*)(struct CMainThread*);
        using CMainThread_db_Update_Set_Limit_Run288_clbk = char (WINAPIV*)(struct CMainThread*, CMainThread_db_Update_Set_Limit_Run288_ptr);
        using CMainThread_db_Update_Start_NpcQuest_History290_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*);
        using CMainThread_db_Update_Start_NpcQuest_History290_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, CMainThread_db_Update_Start_NpcQuest_History290_ptr);
        using CMainThread_db_Update_Supplement292_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int);
        using CMainThread_db_Update_Supplement292_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int, CMainThread_db_Update_Supplement292_ptr);
        using CMainThread_db_Update_TimeLimitInfo294_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int);
        using CMainThread_db_Update_TimeLimitInfo294_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, int, CMainThread_db_Update_TimeLimitInfo294_ptr);
        using CMainThread_db_Update_Trunk296_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_Trunk296_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_Trunk296_ptr);
        using CMainThread_db_Update_Trunk_Extend298_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_Trunk_Extend298_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_Trunk_Extend298_ptr);
        using CMainThread_db_Update_UI300_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_UI300_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_UI300_ptr);
        using CMainThread_db_Update_Unit302_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_Update_Unit302_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_Update_Unit302_ptr);
        using CMainThread_db_complete_event_classrefine304_ptr = void (WINAPIV*)(struct CMainThread*, uint16_t, unsigned int, char, unsigned int);
        using CMainThread_db_complete_event_classrefine304_clbk = void (WINAPIV*)(struct CMainThread*, uint16_t, unsigned int, char, unsigned int, CMainThread_db_complete_event_classrefine304_ptr);
        using CMainThread_db_complete_update_event_classrefine306_ptr = void (WINAPIV*)(struct CMainThread*, uint16_t, unsigned int);
        using CMainThread_db_complete_update_event_classrefine306_clbk = void (WINAPIV*)(struct CMainThread*, uint16_t, unsigned int, CMainThread_db_complete_update_event_classrefine306_ptr);
        using CMainThread_db_init_classrefine_count308_ptr = int (WINAPIV*)(struct CMainThread*);
        using CMainThread_db_init_classrefine_count308_clbk = int (WINAPIV*)(struct CMainThread*, CMainThread_db_init_classrefine_count308_ptr);
        using CMainThread_db_load_event_classrefine310_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, char*, unsigned int*);
        using CMainThread_db_load_event_classrefine310_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, char*, unsigned int*, CMainThread_db_load_event_classrefine310_ptr);
        using CMainThread_db_load_losebattlecount312_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*);
        using CMainThread_db_load_losebattlecount312_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, CMainThread_db_load_losebattlecount312_ptr);
        using CMainThread_db_load_punishment314_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*);
        using CMainThread_db_load_punishment314_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, CMainThread_db_load_punishment314_ptr);
        using CMainThread_db_load_raceboss316_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*);
        using CMainThread_db_load_raceboss316_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, CMainThread_db_load_raceboss316_ptr);
        using CMainThread_db_update_event_classrefine318_ptr = char (WINAPIV*)(struct CMainThread*, uint16_t, unsigned int, char, unsigned int);
        using CMainThread_db_update_event_classrefine318_clbk = char (WINAPIV*)(struct CMainThread*, uint16_t, unsigned int, char, unsigned int, CMainThread_db_update_event_classrefine318_ptr);
        using CMainThread_db_update_inven_AMP320_ptr = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*);
        using CMainThread_db_update_inven_AMP320_clbk = bool (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, char*, CMainThread_db_update_inven_AMP320_ptr);
        using CMainThreadcheck_dbsyn_data_size325_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadcheck_dbsyn_data_size325_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadcheck_dbsyn_data_size325_ptr);
        using CMainThreadcheck_item_code_index327_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadcheck_item_code_index327_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadcheck_item_code_index327_ptr);
        using CMainThreadcheck_loaded_data329_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreadcheck_loaded_data329_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreadcheck_loaded_data329_ptr);
        using CMainThreadcheck_min_max_guild_money331_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, long double*, long double*);
        using CMainThreadcheck_min_max_guild_money331_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, long double*, long double*, CMainThreadcheck_min_max_guild_money331_ptr);
        using CMainThreaddb_Add_PvpPoint333_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int);
        using CMainThreaddb_Add_PvpPoint333_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, CMainThreaddb_Add_PvpPoint333_ptr);
        using CMainThreaddb_Delete_Avator335_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, char);
        using CMainThreaddb_Delete_Avator335_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, char, CMainThreaddb_Delete_Avator335_ptr);
        using CMainThreaddb_GM_GreetingMsg337_ptr = char (WINAPIV*)(struct CMainThread*, struct _qry_case_gm_greetingmsg*);
        using CMainThreaddb_GM_GreetingMsg337_clbk = char (WINAPIV*)(struct CMainThread*, struct _qry_case_gm_greetingmsg*, CMainThreaddb_GM_GreetingMsg337_ptr);
        using CMainThreaddb_GUILD_GreetingMsg339_ptr = char (WINAPIV*)(struct CMainThread*, struct _qry_case_guild_greetingmsg*);
        using CMainThreaddb_GUILD_GreetingMsg339_clbk = char (WINAPIV*)(struct CMainThread*, struct _qry_case_guild_greetingmsg*, CMainThreaddb_GUILD_GreetingMsg339_ptr);
        using CMainThreaddb_Insert_Avator341_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, char*, struct _REGED_AVATOR_DB*, unsigned int*);
        using CMainThreaddb_Insert_Avator341_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, char*, struct _REGED_AVATOR_DB*, unsigned int*, CMainThreaddb_Insert_Avator341_ptr);
        using CMainThreaddb_Insert_ChangeClass_AfterInitClass343_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, char, char*, char*, int, char, uint16_t, char, char, char, char, char);
        using CMainThreaddb_Insert_ChangeClass_AfterInitClass343_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, char, char*, char*, int, char, uint16_t, char, char, char, char, char, CMainThreaddb_Insert_ChangeClass_AfterInitClass343_ptr);
        using CMainThreaddb_Insert_CharacSelect_Log345_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, char*, unsigned int, char*, uint16_t, char, char, char, char, char);
        using CMainThreaddb_Insert_CharacSelect_Log345_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, char*, unsigned int, char*, uint16_t, char, char, char, char, char, CMainThreaddb_Insert_CharacSelect_Log345_ptr);
        using CMainThreaddb_Insert_Economy_History347_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _worlddb_economy_history_info_array::_worlddb_economy_history_info*);
        using CMainThreaddb_Insert_Economy_History347_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _worlddb_economy_history_info_array::_worlddb_economy_history_info*, CMainThreaddb_Insert_Economy_History347_ptr);
        using CMainThreaddb_Insert_Item349_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, unsigned int, char);
        using CMainThreaddb_Insert_Item349_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, unsigned int, char, CMainThreaddb_Insert_Item349_ptr);
        using CMainThreaddb_Insert_guild351_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int*, char*, char, unsigned int*);
        using CMainThreaddb_Insert_guild351_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int*, char*, char, unsigned int*, CMainThreaddb_Insert_guild351_ptr);
        using CMainThreaddb_LoadGreetingMsg353_ptr = bool (WINAPIV*)(struct CMainThread*);
        using CMainThreaddb_LoadGreetingMsg353_clbk = bool (WINAPIV*)(struct CMainThread*, CMainThreaddb_LoadGreetingMsg353_ptr);
        using CMainThreaddb_Load_Avator355_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, struct _AVATOR_DATA*, bool*, unsigned int*, unsigned int*, bool*, char*, long double*, long double*, bool*, bool*, char*, bool, unsigned int*);
        using CMainThreaddb_Load_Avator355_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, struct _AVATOR_DATA*, bool*, unsigned int*, unsigned int*, bool*, char*, long double*, long double*, bool*, bool*, char*, bool, unsigned int*, CMainThreaddb_Load_Avator355_ptr);
        using CMainThreaddb_Load_Content357_ptr = char (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreaddb_Load_Content357_clbk = char (WINAPIV*)(struct CMainThread*, char*, CMainThreaddb_Load_Content357_ptr);
        using CMainThreaddb_Load_PostStorage359_ptr = char (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreaddb_Load_PostStorage359_clbk = char (WINAPIV*)(struct CMainThread*, char*, CMainThreaddb_Load_PostStorage359_ptr);
        using CMainThreaddb_Load_ReturnPost361_ptr = char (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreaddb_Load_ReturnPost361_clbk = char (WINAPIV*)(struct CMainThread*, char*, CMainThreaddb_Load_ReturnPost361_ptr);
        using CMainThreaddb_Log_AvatorLevel363_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, char);
        using CMainThreaddb_Log_AvatorLevel363_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, char, CMainThreaddb_Log_AvatorLevel363_ptr);
        using CMainThreaddb_Log_UserNum365_ptr = char (WINAPIV*)(struct CMainThread*, int, int);
        using CMainThreaddb_Log_UserNum365_clbk = char (WINAPIV*)(struct CMainThread*, int, int, CMainThreaddb_Log_UserNum365_ptr);
        using CMainThreaddb_RACE_GreetingMsg367_ptr = char (WINAPIV*)(struct CMainThread*, struct _qry_case_race_greetingmsg*);
        using CMainThreaddb_RACE_GreetingMsg367_clbk = char (WINAPIV*)(struct CMainThread*, struct _qry_case_race_greetingmsg*, CMainThreaddb_RACE_GreetingMsg367_ptr);
        using CMainThreaddb_Reged_Avator369_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _REGED*, struct _NOT_ARRANGED_AVATOR_DB*, char*);
        using CMainThreaddb_Reged_Avator369_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _REGED*, struct _NOT_ARRANGED_AVATOR_DB*, char*, CMainThreaddb_Reged_Avator369_ptr);
        using CMainThreaddb_Select_Economy_History371_ptr = char (WINAPIV*)(struct CMainThread*, struct _economy_history_data*, int*, int*, struct _economy_history_data*, int*, unsigned int);
        using CMainThreaddb_Select_Economy_History371_clbk = char (WINAPIV*)(struct CMainThread*, struct _economy_history_data*, int*, int*, struct _economy_history_data*, int*, unsigned int, CMainThreaddb_Select_Economy_History371_ptr);
        using CMainThreaddb_Update_Avator373_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, bool);
        using CMainThreaddb_Update_Avator373_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, bool, CMainThreaddb_Update_Avator373_ptr);
        using CMainThreaddb_Update_PostStorage375_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*);
        using CMainThreaddb_Update_PostStorage375_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, struct _AVATOR_DATA*, struct _AVATOR_DATA*, CMainThreaddb_Update_PostStorage375_ptr);
        using CMainThreaddb_Update_PvpInfo377_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, char, int16_t*, long double);
        using CMainThreaddb_Update_PvpInfo377_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, char, int16_t*, long double, CMainThreaddb_Update_PvpInfo377_ptr);
        using CMainThreaddb_buy_emblem379_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, int, unsigned int, unsigned int, unsigned int, long double*, long double*, char*, char*, char*);
        using CMainThreaddb_buy_emblem379_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, int, unsigned int, unsigned int, unsigned int, long double*, long double*, char*, char*, char*, CMainThreaddb_buy_emblem379_ptr);
        using CMainThreaddb_char_set_alive381_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, char, unsigned int, char*, char, struct _REGED*);
        using CMainThreaddb_char_set_alive381_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, char, unsigned int, char*, char, struct _REGED*, CMainThreaddb_char_set_alive381_ptr);
        using CMainThreaddb_disjoint_guild383_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int);
        using CMainThreaddb_disjoint_guild383_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, CMainThreaddb_disjoint_guild383_ptr);
        using CMainThreaddb_input_guild_money385_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, unsigned int, long double*, long double*, char*, char*);
        using CMainThreaddb_input_guild_money385_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, unsigned int, long double*, long double*, char*, char*, CMainThreaddb_input_guild_money385_ptr);
        using CMainThreaddb_input_guild_money_atradetax387_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, long double*, long double*, char*);
        using CMainThreaddb_input_guild_money_atradetax387_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, long double*, long double*, char*, CMainThreaddb_input_guild_money_atradetax387_ptr);
        using CMainThreaddb_output_guild_money389_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, unsigned int, long double*, long double*, char*, char*, char*);
        using CMainThreaddb_output_guild_money389_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, unsigned int, unsigned int, long double*, long double*, char*, char*, char*, CMainThreaddb_output_guild_money389_ptr);
        using CMainThreaddb_sendwebracebosssms391_ptr = char (WINAPIV*)(struct CMainThread*, struct _qry_case_sendwebracebosssms*);
        using CMainThreaddb_sendwebracebosssms391_clbk = char (WINAPIV*)(struct CMainThread*, struct _qry_case_sendwebracebosssms*, CMainThreaddb_sendwebracebosssms391_ptr);
        using CMainThreaddb_update_guildmaster393_ptr = char (WINAPIV*)(struct CMainThread*, struct _qry_case_update_guildmaster*);
        using CMainThreaddb_update_guildmaster393_clbk = char (WINAPIV*)(struct CMainThread*, struct _qry_case_update_guildmaster*, CMainThreaddb_update_guildmaster393_ptr);
        using CMainThreaddb_update_guildmember_add395_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, char, int);
        using CMainThreaddb_update_guildmember_add395_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, char, int, CMainThreaddb_update_guildmember_add395_ptr);
        using CMainThreaddb_update_guildmember_del397_ptr = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, int);
        using CMainThreaddb_update_guildmember_del397_clbk = char (WINAPIV*)(struct CMainThread*, unsigned int, unsigned int, int, CMainThreaddb_update_guildmember_del397_ptr);
        using CMainThreadgm_DisplayAll399_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_DisplayAll399_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_DisplayAll399_ptr);
        using CMainThreadgm_DisplaymodeChange401_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_DisplaymodeChange401_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_DisplaymodeChange401_ptr);
        using CMainThreadgm_DungeonLoad403_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_DungeonLoad403_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_DungeonLoad403_ptr);
        using CMainThreadgm_MainThreadControl405_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_MainThreadControl405_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_MainThreadControl405_ptr);
        using CMainThreadgm_MapChange407_ptr = void (WINAPIV*)(struct CMainThread*, struct CMapData*);
        using CMainThreadgm_MapChange407_clbk = void (WINAPIV*)(struct CMainThread*, struct CMapData*, CMainThreadgm_MapChange407_ptr);
        using CMainThreadgm_MonsterInit409_ptr = bool (WINAPIV*)(struct CMainThread*, struct CCharacter*);
        using CMainThreadgm_MonsterInit409_clbk = bool (WINAPIV*)(struct CMainThread*, struct CCharacter*, CMainThreadgm_MonsterInit409_ptr);
        using CMainThreadgm_ObjectSelect411_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_ObjectSelect411_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_ObjectSelect411_ptr);
        using CMainThreadgm_PreCloseAnn413_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_PreCloseAnn413_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_PreCloseAnn413_ptr);
        using CMainThreadgm_ServerClose415_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_ServerClose415_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_ServerClose415_ptr);
        using CMainThreadgm_UpdateMap417_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_UpdateMap417_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_UpdateMap417_ptr);
        using CMainThreadgm_UpdateObject419_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_UpdateObject419_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_UpdateObject419_ptr);
        using CMainThreadgm_UpdateServer421_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_UpdateServer421_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_UpdateServer421_ptr);
        using CMainThreadgm_UserExit423_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadgm_UserExit423_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadgm_UserExit423_ptr);
        using CMainThreadpc_AllUserGMNoticeInform425_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadpc_AllUserGMNoticeInform425_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadpc_AllUserGMNoticeInform425_ptr);
        using CMainThreadpc_AllUserKickInform427_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreadpc_AllUserKickInform427_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreadpc_AllUserKickInform427_ptr);
        using CMainThreadpc_AllUserMsgInform429_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadpc_AllUserMsgInform429_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadpc_AllUserMsgInform429_ptr);
        using CMainThreadpc_AlterWorldService431_ptr = void (WINAPIV*)(struct CMainThread*, bool);
        using CMainThreadpc_AlterWorldService431_clbk = void (WINAPIV*)(struct CMainThread*, bool, CMainThreadpc_AlterWorldService431_ptr);
        using CMainThreadpc_CashDBInfoRecvResult433_ptr = void (WINAPIV*)(struct CMainThread*, char*, char*, char*, char*, unsigned int);
        using CMainThreadpc_CashDBInfoRecvResult433_clbk = void (WINAPIV*)(struct CMainThread*, char*, char*, char*, char*, unsigned int, CMainThreadpc_CashDBInfoRecvResult433_ptr);
        using CMainThreadpc_ChatLockCommand435_ptr = void (WINAPIV*)(struct CMainThread*, struct _CLID*, uint16_t);
        using CMainThreadpc_ChatLockCommand435_clbk = void (WINAPIV*)(struct CMainThread*, struct _CLID*, uint16_t, CMainThreadpc_ChatLockCommand435_ptr);
        using CMainThreadpc_EnterWorldResult437_ptr = void (WINAPIV*)(struct CMainThread*, char, struct _CLID*);
        using CMainThreadpc_EnterWorldResult437_clbk = void (WINAPIV*)(struct CMainThread*, char, struct _CLID*, CMainThreadpc_EnterWorldResult437_ptr);
        using CMainThreadpc_ForceCloseCommand439_ptr = void (WINAPIV*)(struct CMainThread*, struct _CLID*, bool, char, unsigned int);
        using CMainThreadpc_ForceCloseCommand439_clbk = void (WINAPIV*)(struct CMainThread*, struct _CLID*, bool, char, unsigned int, CMainThreadpc_ForceCloseCommand439_ptr);
        using CMainThreadpc_OpenWorldFailureResult441_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadpc_OpenWorldFailureResult441_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadpc_OpenWorldFailureResult441_ptr);
        using CMainThreadpc_OpenWorldSuccessResult443_ptr = void (WINAPIV*)(struct CMainThread*, char, char*, char*);
        using CMainThreadpc_OpenWorldSuccessResult443_clbk = void (WINAPIV*)(struct CMainThread*, char, char*, char*, CMainThreadpc_OpenWorldSuccessResult443_ptr);
        using CMainThreadpc_SetMainGreetingMsg445_ptr = void (WINAPIV*)(struct CMainThread*, char*, char*);
        using CMainThreadpc_SetMainGreetingMsg445_clbk = void (WINAPIV*)(struct CMainThread*, char*, char*, CMainThreadpc_SetMainGreetingMsg445_ptr);
        using CMainThreadpc_SetRaceGreetingMsg447_ptr = void (WINAPIV*)(struct CMainThread*, int, char*, char*);
        using CMainThreadpc_SetRaceGreetingMsg447_clbk = void (WINAPIV*)(struct CMainThread*, int, char*, char*, CMainThreadpc_SetRaceGreetingMsg447_ptr);
        using CMainThreadpc_TaiwanBillingUserCertify449_ptr = void (WINAPIV*)(struct CMainThread*, char*, char);
        using CMainThreadpc_TaiwanBillingUserCertify449_clbk = void (WINAPIV*)(struct CMainThread*, char*, char, CMainThreadpc_TaiwanBillingUserCertify449_ptr);
        using CMainThreadpc_TransIPKeyInform451_ptr = void (WINAPIV*)(struct CMainThread*, unsigned int, char*, char, char, unsigned int*, struct _GLBID*, unsigned int, bool, int16_t, char*, struct _SYSTEMTIME*, int, char, char*, char, char*, char, char*, char, bool, int, bool, unsigned int*, unsigned int*);
        using CMainThreadpc_TransIPKeyInform451_clbk = void (WINAPIV*)(struct CMainThread*, unsigned int, char*, char, char, unsigned int*, struct _GLBID*, unsigned int, bool, int16_t, char*, struct _SYSTEMTIME*, int, char, char*, char, char*, char, char*, char, bool, int, bool, unsigned int*, unsigned int*, CMainThreadpc_TransIPKeyInform451_ptr);
        using CMainThreadpc_UILockInitResult453_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadpc_UILockInitResult453_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadpc_UILockInitResult453_ptr);
        using CMainThreadpc_UILockUpdateResult455_ptr = void (WINAPIV*)(struct CMainThread*, char*);
        using CMainThreadpc_UILockUpdateResult455_clbk = void (WINAPIV*)(struct CMainThread*, char*, CMainThreadpc_UILockUpdateResult455_ptr);
        using CMainThreadpc_UserChatBlockResult457_ptr = void (WINAPIV*)(struct CMainThread*, char, struct _CLID*, struct _CLID*, int);
        using CMainThreadpc_UserChatBlockResult457_clbk = void (WINAPIV*)(struct CMainThread*, char, struct _CLID*, struct _CLID*, int, CMainThreadpc_UserChatBlockResult457_ptr);
        
        using CMainThreaddtor_CMainThread459_ptr = void (WINAPIV*)(struct CMainThread*);
        using CMainThreaddtor_CMainThread459_clbk = void (WINAPIV*)(struct CMainThread*, CMainThreaddtor_CMainThread459_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

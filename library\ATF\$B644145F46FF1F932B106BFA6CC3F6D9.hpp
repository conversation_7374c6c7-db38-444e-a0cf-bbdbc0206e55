// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $B644145F46FF1F932B106BFA6CC3F6D9
    {
        BYTE gap0[8];
        unsigned int ulVal;
    };    
    static_assert(ATF::checkSize<$B644145F46FF1F932B106BFA6CC3F6D9, 12>(), "$B644145F46FF1F932B106BFA6CC3F6D9");
END_ATF_NAMESPACE

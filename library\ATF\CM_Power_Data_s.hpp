// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CM_Power_Data_s
    {
        unsigned int PD_Size;
        _DEVICE_POWER_STATE PD_MostRecentPowerState;
        unsigned int PD_Capabilities;
        unsigned int PD_D1Latency;
        unsigned int PD_D2Latency;
        unsigned int PD_D3Latency;
        _DEVICE_POWER_STATE PD_PowerStateMapping[7];
        _SYSTEM_POWER_STATE PD_DeepestSystemWake;
    };
END_ATF_NAMESPACE

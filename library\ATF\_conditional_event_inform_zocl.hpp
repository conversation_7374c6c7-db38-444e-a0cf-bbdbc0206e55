// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _conditional_event_inform_zocl
    {
        char byEventType;
        char byEventStatus;
        char szMsgCode[8];
        unsigned __int16 wCsDiscount;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_conditional_event_inform_zocl, 12>(), "_conditional_event_inform_zocl");
END_ATF_NAMESPACE

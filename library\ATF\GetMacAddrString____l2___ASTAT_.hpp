// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ADAPTER_STATUS.hpp>
#include <_NAME_BUFFER.hpp>


START_ATF_NAMESPACE
    namespace GetMacAddrString
    {
        namespace __l2
        {
            struct _ASTAT_
            {
                _ADAPTER_STATUS adapt;
                _NAME_BUFFER NameBuff[30];
            };
        }; // end namespace __l2
    }; // end namespace GetMacAddrString
END_ATF_NAMESPACE

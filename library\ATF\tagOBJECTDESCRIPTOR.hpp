// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <_POINTL.hpp>
#include <tagSIZE.hpp>


START_ATF_NAMESPACE
    struct tagOBJECTDESCRIPTOR
    {
        unsigned int cbSize;
        _GUID clsid;
        unsigned int dwDrawAspect;
        tagSIZE sizel;
        _POINTL pointl;
        unsigned int dwStatus;
        unsigned int dwFullUserTypeName;
        unsigned int dwSrcOfCopy;
    };
END_ATF_NAMESPACE

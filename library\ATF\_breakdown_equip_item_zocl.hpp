// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _breakdown_equip_item_zocl
    {
        unsigned __int16 wPlayerIndex;
        unsigned int dwPlayerSerial;
        unsigned __int16 dwEquipVer;
        char byPartIndex;
        unsigned __int16 wItemSerial;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

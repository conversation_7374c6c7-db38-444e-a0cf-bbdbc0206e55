// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _MapItem_fld : _base_fld
    {
        int m_bExist;
        char m_strModel[64];
        int m_nIconIDX;
        char m_strName[64];
        int m_nKindClt;
        int m_nFixPart;
        char m_strCivil[64];
        char m_strMapCode[64];
        int m_nMoney;
        int m_nStdPrice;
        int m_nStdPoint;
        int m_nGoldPoint;
        int m_nKillPoint;
        int m_nProcPoint;
        int m_nStoragePrice;
        int m_bSell;
        int m_bExchange;
        int m_bGround;
        int m_bStoragePossible;
        int m_bUseableNormalAcc;
        char m_strTooltipIndex[64];
        int m_bIsTime;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _NETRESOURCEW
    {
        unsigned int dwScope;
        unsigned int dwType;
        unsigned int dwDisplayType;
        unsigned int dwUsage;
        wchar_t *lpLocalName;
        wchar_t *lpRemoteName;
        wchar_t *lpComment;
        wchar_t *lpProvider;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetCriticalSection.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CLogFile
    {
        char m_szFileName[128];
        unsigned int m_dwLogCount;
        int m_bWriteAble;
        CNetCriticalSection m_cs;
        bool m_bAddCount;
        bool m_bDate;
        bool m_bTrace;
        bool m_bInit;
    public:
        CLogFile();
        void ctor_CLogFile();
        void SetWriteAble(bool bAble);
        void SetWriteLogFile(char* szFileName, int bWriteAble, bool bTrace, bool bDate, bool bAddCount);
        void Write(char* fmt);
        void WriteFromArg(char* fmt, char* arg);
        void WriteFromArg(wchar_t* lpcwFmt, char* arg);
        void WriteString(char* fmt);
        ~CLogFile();
        void dtor_CLogFile();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CLogFile, 184>(), "CLogFile");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_notify_race_leader_s_owner_u_taxrate.hpp>


START_ATF_NAMESPACE
    struct CNotifyNotifyRaceLeaderSownerUTaxrate
    {
        _notify_race_leader_s_owner_u_taxrate m_Send[3];
    public:
        CNotifyNotifyRaceLeaderSownerUTaxrate();
        void ctor_CNotifyNotifyRaceLeaderSownerUTaxrate();
        void Init();
        void Notify(char byRace);
        void Notify(char byRace, uint16_t wIndex);
        void UpdateRaceLeader(char byRace, char byNth, char* wszLeaderName);
        void UpdateSettlementOwner(char byRace, unsigned int dw1ThGuildSerial, unsigned int dw2ThGuildSerial);
        void UpdateTaxRate(char byRace, char byTaxRate);
    };    
    static_assert(ATF::checkSize<CNotifyNotifyRaceLeaderSownerUTaxrate, 666>(), "CNotifyNotifyRaceLeaderSownerUTaxrate");
END_ATF_NAMESPACE

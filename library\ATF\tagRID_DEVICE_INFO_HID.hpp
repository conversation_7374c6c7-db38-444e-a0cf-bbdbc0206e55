// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagRID_DEVICE_INFO_HID
    {
        unsigned int dwVendorId;
        unsigned int dwProductId;
        unsigned int dwVersionNumber;
        unsigned __int16 usUsagePage;
        unsigned __int16 usUsage;
    };
END_ATF_NAMESPACE

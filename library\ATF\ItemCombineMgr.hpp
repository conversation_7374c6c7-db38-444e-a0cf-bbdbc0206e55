// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_combine_ex_item_request_clzo.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct ItemCombineMgr
    {
        enum tbl_kind
        {
            eCOMMON = 0x0,
            eItemCombMax = 0x1,
        };
        enum RESULT_EFFECT_TYPE
        {
            RET_TYPE_NONE = 0xFFFFFFFF,
            RET_NOT_EXIST = 0x0,
            RET_EXIST = 0x1,
        };
        struct CPlayer *m_pMaster;
    public:
        static bool CheckLoadData();
        char ClearDB_CombineResult();
        char ConsumeMeterial_And_CalculateNewItems(_STORAGE_LIST::_db_con** pMt_Sv_Inv, char byMtSlotNum, _combine_ex_item_request_clzo::_list* pipMaterials, struct _combine_ex_item_result_zocl* pSaveData, struct _ItemCombine_exp_fld* pfld, char byLinkTableIndex, int nType);
        void InitMgr(struct CPlayer* pOne);
        ItemCombineMgr();
        void ctor_ItemCombineMgr();
        char LoadDB_CombineResult(struct _combine_ex_item_result_zocl* pLoadData);
        static bool LoadData();
        char MakeNewItems(struct _ITEMCOMBINE_DB_BASE* pPlayerItemDB, struct _combine_ex_item_accept_request_clzo* pRecv, struct _combine_ex_item_accept_result_zocl* pSend);
        void OnPlayerCreateCompleteProc();
        char RequestCombineAcceptProcess(struct _combine_ex_item_accept_request_clzo* pRecv, struct _combine_ex_item_accept_result_zocl* pSend);
        char RequestCombineProcess(_combine_ex_item_request_clzo* pRecv, struct _combine_ex_item_result_zocl* pSend);
        char UpdateDB_CombineResult(struct _combine_ex_item_result_zocl* pSaveData);
        ~ItemCombineMgr();
        void dtor_ItemCombineMgr();
    };    
    static_assert(ATF::checkSize<ItemCombineMgr, 8>(), "ItemCombineMgr");
END_ATF_NAMESPACE

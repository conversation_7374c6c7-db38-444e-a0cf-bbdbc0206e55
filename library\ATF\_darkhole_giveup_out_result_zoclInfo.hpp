// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_darkhole_giveup_out_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _darkhole_giveup_out_result_zoclsize2_ptr = int (WINAPIV*)(struct _darkhole_giveup_out_result_zocl*);
        using _darkhole_giveup_out_result_zoclsize2_clbk = int (WINAPIV*)(struct _darkhole_giveup_out_result_zocl*, _darkhole_giveup_out_result_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

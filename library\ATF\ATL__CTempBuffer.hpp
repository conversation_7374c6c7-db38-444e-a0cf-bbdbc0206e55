// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct CTempBuffer<char,128,CCRTAllocator>
        {
            char *m_p;
            char m_abFixedBuffer[128];
        public:
            char* Allocate(uint64_t nElements)
            {
                using org_ptr = char* (WINAPIV*)(struct CTempBuffer<char,128,CCRTAllocator>*, uint64_t);
                return (org_ptr(0x140026e90L))(this, nElements);
            };
            char* AllocateBytes(uint64_t nBytes)
            {
                using org_ptr = char* (WINAPIV*)(struct CTempBuffer<char,128,CCRTAllocator>*, uint64_t);
                return (org_ptr(0x140026ff0L))(this, nBytes);
            };
            void AllocateHeap(uint64_t nBytes)
            {
                using org_ptr = void (WINAPIV*)(struct CTempBuffer<char,128,CCRTAllocator>*, uint64_t);
                (org_ptr(0x1400273f0L))(this, nBytes);
            };
            CTempBuffer()
            {
                using org_ptr = void (WINAPIV*)(struct CTempBuffer<char,128,CCRTAllocator>*);
                (org_ptr(0x140026df0L))(this);
            };
            void ctor_CTempBuffer()
            {
                using org_ptr = void (WINAPIV*)(struct CTempBuffer<char,128,CCRTAllocator>*);
                (org_ptr(0x140026df0L))(this);
            };
            void FreeHeap()
            {
                using org_ptr = void (WINAPIV*)(struct CTempBuffer<char,128,CCRTAllocator>*);
                (org_ptr(0x1400270c0L))(this);
            };
            ~CTempBuffer()
            {
                using org_ptr = void (WINAPIV*)(struct CTempBuffer<char,128,CCRTAllocator>*);
                (org_ptr(0x140026e10L))(this);
            };
            void dtor_CTempBuffer()
            {
                using org_ptr = void (WINAPIV*)(struct CTempBuffer<char,128,CCRTAllocator>*);
                (org_ptr(0x140026e10L))(this);
            };
        };    
        static_assert(ATF::checkSize<ATL::CTempBuffer<char,128,ATL::CCRTAllocator>, 136>(), "ATL::CTempBuffer<char,128,ATL::CCRTAllocator>");
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct CTempBuffer<char,256,CCRTAllocator>
        {
            char *m_p;
            char m_abFixedBuffer[256];
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct CTempBuffer<char,1024,CCRTAllocator>
        {
            char *m_p;
            char m_abFixedBuffer[1024];
        };
    }; // end namespace ATL
END_ATF_NAMESPACE



START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct CTempBuffer<wchar_t,128,CCRTAllocator>
        {
            wchar_t *m_p;
            char m_abFixedBuffer[128];
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct CTempBuffer<unsigned char,256,CCRTAllocator>
        {
            char *m_p;
            char m_abFixedBuffer[256];
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ScheduleMSG.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using ScheduleMSGInit2_ptr = void (WINAPIV*)(struct ScheduleMSG*);
        using ScheduleMSGInit2_clbk = void (WINAPIV*)(struct ScheduleMSG*, ScheduleMSGInit2_ptr);
        
        using ScheduleMSGctor_ScheduleMSG4_ptr = void (WINAPIV*)(struct ScheduleMSG*);
        using ScheduleMSGctor_ScheduleMSG4_clbk = void (WINAPIV*)(struct ScheduleMSG*, ScheduleMSGctor_ScheduleMSG4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

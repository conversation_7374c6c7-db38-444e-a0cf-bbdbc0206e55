// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _personal_automine_battery_insert_zocl
    {
        unsigned int dwObjSerial;
        unsigned int dwOwnerSerial;
        unsigned __int16 wItemSerial;
    public:
        _personal_automine_battery_insert_zocl();
        void ctor__personal_automine_battery_insert_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_personal_automine_battery_insert_zocl, 10>(), "_personal_automine_battery_insert_zocl");
END_ATF_NAMESPACE

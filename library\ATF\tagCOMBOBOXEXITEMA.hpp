// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagCOMBOBOXEXITEMA
    {
        unsigned int mask;
        __int64 iItem;
        char *pszText;
        int cchTextMax;
        int iImage;
        int iSelectedImage;
        int iOverlay;
        int iIndent;
        __int64 lParam;
    };
END_ATF_NAMESPACE

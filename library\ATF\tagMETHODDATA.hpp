// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagPARAMDATA.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagMETHODDATA
    {
        wchar_t *szName;
        tagPARAMDATA *ppdata;
        int dispid;
        unsigned int iMeth;
        tagCALLCONV cc;
        unsigned int cArgs;
        unsigned __int16 wFlags;
        unsigned __int16 vtReturn;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

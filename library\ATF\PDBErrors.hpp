// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum PDBErrors
    {
      EC_OK = 0x0,
      EC_USAGE = 0x1,
      EC_OUT_OF_MEMORY = 0x2,
      EC_FILE_SYSTEM = 0x3,
      EC_NOT_FOUND = 0x4,
      EC_INVALID_SIG = 0x5,
      EC_INVALID_AGE = 0x6,
      EC_PRECOMP_REQUIRED = 0x7,
      EC_OUT_OF_TI = 0x8,
      EC_NOT_IMPLEMENTED = 0x9,
      EC_V1_PDB = 0xA,
      EC_UNKNOWN_FORMAT = 0xA,
      EC_FORMAT = 0xB,
      EC_LIMIT = 0xC,
      EC_CORRUPT = 0xD,
      EC_TI16 = 0xE,
      EC_ACCESS_DENIED = 0xF,
      EC_ILLEGAL_TYPE_EDIT = 0x10,
      EC_INVALID_EXECUTABLE = 0x11,
      EC_DBG_NOT_FOUND = 0x12,
      EC_NO_DEBUG_INFO = 0x13,
      EC_INVALID_EXE_TIMESTAMP = 0x14,
      EC_CORRUPT_TYPEPOOL = 0x15,
      EC_DEBUG_INFO_NOT_IN_PDB = 0x16,
      EC_RPC = 0x17,
      EC_UNKNOWN = 0x18,
      EC_BAD_CACHE_PATH = 0x19,
      EC_CACHE_FULL = 0x1A,
      EC_TOO_MANY_MOD_ADDTYPE = 0x1B,
      EC_MAX = 0x1C,
    };
END_ATF_NAMESPACE

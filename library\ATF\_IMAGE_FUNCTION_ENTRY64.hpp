// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$E0927F6BE2F1645595F211B37EFD8766.hpp>


START_ATF_NAMESPACE
    struct _IMAGE_FUNCTION_ENTRY64
    {
        unsigned __int64 StartingAddress;
        unsigned __int64 EndingAddress;
        $E0927F6BE2F1645595F211B37EFD8766 ___u2;
    };
END_ATF_NAMESPACE

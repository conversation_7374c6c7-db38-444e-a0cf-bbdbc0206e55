// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_unmandtrader_time_out_cancelitem
    {
        char byType;
        unsigned int dwRegistSerial;
        char byState;
        unsigned int dwOwnerSerial;
        char byItemTableCode;
        unsigned __int16 wItemTableIndex;
        char szAccount[13];
        char wszName[17];
        char byProcRet;
        unsigned int dwK;
        unsigned int dwD;
        unsigned int dwU;
        unsigned int dwT;
        unsigned __int64 lnUID;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagLVITEMA
    {
        unsigned int mask;
        int iItem;
        int iSubItem;
        unsigned int state;
        unsigned int stateMask;
        char *pszText;
        int cchTextMax;
        int iImage;
        __int64 lParam;
        int iIndent;
        int iGroupId;
        unsigned int cColumns;
        unsigned int *puColumns;
    };
END_ATF_NAMESPACE

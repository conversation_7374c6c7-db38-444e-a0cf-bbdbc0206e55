// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CObject.hpp>
#include <Event_Time.hpp>
#include <$FDD8F4F487B302A1323F7A672D081758.hpp>


START_ATF_NAMESPACE
    struct  CFile : CObject
    {
        enum OpenFlags
        {
            modeRead = 0x0,
            modeWrite = 0x1,
            modeReadWrite = 0x2,
            shareCompat = 0x0,
            shareExclusive = 0x10,
            shareDenyWrite = 0x20,
            shareDenyRead = 0x30,
            shareDenyNone = 0x40,
            modeNoInherit = 0x80,
            modeCreate = 0x1000,
            modeNoTruncate = 0x2000,
            typeText = 0x4000,
            typeBinary = 0x8000,
            osNoBuffer = 0x10000,
            osWriteThrough = 0x20000,
            osRandomAccess = 0x40000,
            osSequentialScan = 0x80000,
        };
        typedef $FDD8F4F487B302A1323F7A672D081758 Attribute;
        typedef Event_Time SeekPosition;
        enum BufferCommand
        {
            bufferRead = 0x0,
            bufferWrite = 0x1,
            bufferCommit = 0x2,
            bufferCheck = 0x3,
        };
        enum BufferFlags
        {
            bufferDirect = 0x1,
            bufferBlocking = 0x2,
        };
        void *m_hFile;
        int m_bCloseOnDelete;
        ATL::CStringT<char> m_strFileName;
    };
END_ATF_NAMESPACE

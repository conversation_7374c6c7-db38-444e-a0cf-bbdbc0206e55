// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AINetFile.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using AINetFilector_AINetFile2_ptr = void (WINAPIV*)(struct AINetFile*, void*, unsigned int);
        using AINetFilector_AINetFile2_clbk = void (WINAPIV*)(struct AINetFile*, void*, unsigned int, AINetFilector_AINetFile2_ptr);
        using AINetFileClose4_ptr = void (WINAPIV*)(struct AINetFile*);
        using AINetFileClose4_clbk = void (WINAPIV*)(struct AINetFile*, AINetFileClose4_ptr);
        using AINetFileRead6_ptr = unsigned int (WINAPIV*)(struct AINetFile*, void*, unsigned int);
        using AINetFileRead6_clbk = unsigned int (WINAPIV*)(struct AINetFile*, void*, unsigned int, AINetFileRead6_ptr);
        using AINetFileWrite8_ptr = unsigned int (WINAPIV*)(struct AINetFile*, void*, unsigned int);
        using AINetFileWrite8_clbk = unsigned int (WINAPIV*)(struct AINetFile*, void*, unsigned int, AINetFileWrite8_ptr);
        using AINetFileWriteString10_ptr = int (WINAPIV*)(struct AINetFile*, char*);
        using AINetFileWriteString10_clbk = int (WINAPIV*)(struct AINetFile*, char*, AINetFileWriteString10_ptr);
        
        using AINetFiledtor_AINetFile15_ptr = void (WINAPIV*)(struct AINetFile*);
        using AINetFiledtor_AINetFile15_clbk = void (WINAPIV*)(struct AINetFile*, AINetFiledtor_AINetFile15_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

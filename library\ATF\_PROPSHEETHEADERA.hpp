// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$********************************.hpp>
#include <$8534B7AC4FE3D038219CB6DFE55F5B7D.hpp>
#include <$B97DE914A4AB227A3855161D5080717C.hpp>
#include <$E3CAD8411F77256CCB020CE77D197F57.hpp>
#include <$F11E48D1ADA518B8C9A94CE476B00947.hpp>
#include <HINSTANCE__.hpp>
#include <HPALETTE__.hpp>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    const struct _PROPSHEETHEADERA
    {
        unsigned int dwSize;
        unsigned int dwFlags;
        HWND__ *hwndParent;
        HINSTANCE__ *hInstance;
        $F11E48D1ADA518B8C9A94CE476B00947 ___u4;
        const char *pszCaption;
        unsigned int nPages;
        $E3CAD8411F77256CCB020CE77D197F57 ___u7;
        $8534B7AC4FE3D038219CB6DFE55F5B7D ___u8;
        int (WINAPIV *pfnCallback)(HWND__ *, unsigned int, __int64);
        $******************************** ___u10;
        HPALETTE__ *hplWatermark;
        $B97DE914A4AB227A3855161D5080717C ___u12;
    };
END_ATF_NAMESPACE

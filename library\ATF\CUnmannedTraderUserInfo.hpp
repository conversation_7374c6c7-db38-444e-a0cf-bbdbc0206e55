// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CPlayer.hpp>
#include <CUnmannedTraderItemState.hpp>
#include <CUnmannedTraderRegistItemInfo.hpp>
#include <CUnmannedTraderRequestLimiter.hpp>
#include <_TRADE_DB_BASE.hpp>
#include <_a_trade_adjust_price_request_clzo.hpp>
#include <_a_trade_clear_item_request_clzo.hpp>
#include <_a_trade_reg_item_request_clzo.hpp>
#include <_qry_case_unmandtrader_log_in_proc_update_complete.hpp>
#include <_unmannedtrader_buy_item_request_clzo.hpp>
#include <_unmannedtrader_re_regist_request_clzo.hpp>
#include <_unmannedtrader_search_list_request_clzo.hpp>
#include <std___Vector_iterator.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CUnmannedTraderUserInfo
    {
        enum LOG_IN_STATE
        {
            UTUI_NONE = 0xFFFFFFFF,
            UTUI_EMPTY = 0x0,
            UTUI_LOGIN = 0x1,
        };
        LOG_IN_STATE m_eState;
        unsigned __int16 m_wInx;
        unsigned int m_dwUserSerial;
        char m_byRegistCnt;
        char m_byMaxRegistCnt;
        CUnmannedTraderRequestLimiter m_kRequestState;
        std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > m_vecRegistItemInfo;
        std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > m_vecLoadItemInfo;
    public:
        void Buy(char byType, struct _unmannedtrader_buy_item_request_clzo* pRequest, struct CLogFile* pkLogger);
        char BuyComplete(struct CPlayer* pkBuyer, unsigned int dwSellerSerial, char* wszSellerName, char* szSellerAccountName, unsigned int dwRegistSerial, unsigned int dwK, uint64_t dwD, unsigned int dwU, unsigned int dwPrice, uint64_t lnUID, struct CLogFile* pkLogger, uint16_t* wAddItemSerial);
        CUnmannedTraderUserInfo(struct CUnmannedTraderUserInfo* __that);
        void ctor_CUnmannedTraderUserInfo(struct CUnmannedTraderUserInfo* __that);
        CUnmannedTraderUserInfo();
        void ctor_CUnmannedTraderUserInfo();
        void CancelRegist(char byType, struct _a_trade_clear_item_request_clzo* pRequest, struct CLogFile* pkLogger);
        bool CheatCancelRegist(char byNth);
        bool CheatCancelRegistAll();
        bool CheatCancelRegistSingle(char byNth);
        char CheckBuy(char byType, struct _unmannedtrader_buy_item_request_clzo* pRequest, struct CPlayer** pkBuyer, struct CLogFile* pkLogger);
        char CheckBuyComplete(struct CPlayer* pkBuyer, unsigned int dwPrice);
        char CheckCancelRegist(char byType, struct _a_trade_clear_item_request_clzo* pRequest, struct CLogFile* pkLogger);
        bool CheckIsUpdatedTaxRate(char byTax, struct CLogFile* pkLogger);
        char CheckModifyPrice(char byType, struct _a_trade_adjust_price_request_clzo* pRequest, unsigned int* dwOldPrice, struct CLogFile* pkLogger, unsigned int* pdwTax);
        char CheckReRegist(char byType, struct CLogFile* pkLogger, uint16_t wItemSerial, char byAmount, char byItemTableCode, uint16_t wItemIndex, unsigned int dwRegistSerial, unsigned int dwPrice, char* pbyDivision, char* pbyClass, char* pbySubClass, unsigned int* pdwTax, unsigned int* pdwListIndex);
        char CheckRegist(char byType, struct _a_trade_reg_item_request_clzo* pRequest, struct CLogFile* pkLogger, char* byTempSlotIndex, char* byDivision, char* byClass, char* bySubClass, unsigned int* dwListIndex, unsigned int* dwTax);
        char CheckSearch(char byType, struct _unmannedtrader_search_list_request_clzo* pRequest, unsigned int* dwListIndex, unsigned int* dwCurVer, struct CLogFile* pkLogger);
        char CheckSellComplete(struct CPlayer* pkSellPlayer, struct CPlayer* pkBuyer, unsigned int dwRegistSerial, unsigned int dwRealPrice, struct CLogFile* pkLogger);
        void Clear();
        void ClearLoadItemInfo();
        void ClearRequest();
        void CompleteCancelRegist(char byRet, char* pLoadData, struct CLogFile* pkLogger);
        bool CompleteCancelRegistItem(unsigned int dwRegistSerial, uint16_t dwItemSerial, struct CLogFile* pkLogger);
        void CompleteCreate(struct CLogFile* pkLogger);
        void CompleteReRegist(char* pLoadData, struct CLogFile* pkLogger);
        bool CompleteReRegistItem(unsigned int dwRegistSerial, uint16_t dwItemSerial, unsigned int dwPrice, struct CLogFile* pkLogger, char* pbyProcRet);
        void CompleteReRegistRollBack(char* pData, struct CLogFile* pkLogger);
        void CompleteRegist(char byRet, char* pLoadData, struct CLogFile* pkLogger);
        bool CompleteRegistItem(unsigned int dwRegistSerial, uint16_t dwItemSerial, unsigned int dwETSerialNumber, unsigned int dwPrice, char bySellTurm, char byTableCode, uint16_t wItemIndex, char byStorageIndex, uint64_t dwD, unsigned int dwU, bool bInserted);
        void CompleteReprice(char byRet, char* pLoadData, struct CLogFile* pkLogger);
        bool CompleteRepriceItem(unsigned int dwRegistSerial, uint16_t dwItemSerial, unsigned int dwPrice);
        void CompleteTimeOutClear(unsigned int dwRegistSerial, struct CLogFile* pkLogger);
        void CompleteUpdateCheatRegistTime(char* pLoadData);
        bool CompleteUpdateState(unsigned int dwRegistSerial, char byState, bool bReCountRegist);
        void CountRegistItem();
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo>* Find(std::_Vector_iterator<CUnmannedTraderRegistItemInfo>* result, unsigned int dwRegistSerial);
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo>* FindEmpty(std::_Vector_iterator<CUnmannedTraderRegistItemInfo>* result);
        struct CPlayer* FindOwner();
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo>* FindRegist(std::_Vector_iterator<CUnmannedTraderRegistItemInfo>* result, std::_Vector_iterator<CUnmannedTraderRegistItemInfo> iterStart);
        CUnmannedTraderItemState::STATE GetCloseItemForPassTimeUpdateInfo(unsigned int dwRegistSerial, struct CPlayer** pkOwner);
        void GetCurrentRegItemStateStr(char* szStateStr, int iBuffSize);
        uint16_t GetIndex();
        char GetMaxRegistCnt();
        struct CUnmannedTraderRegistItemInfo* GetRegItemInfo();
        unsigned int GetSerial();
        bool Init(uint16_t wInx);
        bool IsLogInState();
        bool IsNull();
        bool Load(char byType, uint16_t wInx, unsigned int dwSerial, struct _TRADE_DB_BASE* kInfo, struct CLogFile* pkLogger);
        void LogOut(unsigned int dwSerial, struct CLogFile* pkLogger);
        void ModifyPrice(char byType, struct _a_trade_adjust_price_request_clzo* pRequest, struct CLogFile* pkLogger);
        void NotifyCloseItem(struct _qry_case_unmandtrader_log_in_proc_update_complete* pkResult, struct CLogFile* pkLogger);
        void NotifyRegistItem();
        void PrcoSellUpdateWaitItem(struct _qry_case_unmandtrader_log_in_proc_update_complete* pkResult, char byGroupType, struct CLogFile* pkLogger);
        void ProcSellWaitItem(struct _qry_case_unmandtrader_log_in_proc_update_complete* pkResult, char byGroupType, struct CLogFile* pkLogger);
        void ReRegist(char byType, struct _unmannedtrader_re_regist_request_clzo* pRequest, struct CLogFile* pkLogger);
        void Regist(char byType, struct _a_trade_reg_item_request_clzo* pRequest, struct CLogFile* pkLogger);
        char RegistItem(char byType, struct _a_trade_reg_item_request_clzo* pRequest, char byTempSlotIndex, char byDivision, char byClass, char bySubClass, unsigned int dwListIndex, unsigned int dwTax);
        void Search(char byType, struct _unmannedtrader_search_list_request_clzo* pRequest, struct CLogFile* pkLogger);
        char SellComplete(struct CPlayer* pkSellPlayer, struct CPlayer* pkBuyer, unsigned int dwOriPrice, unsigned int dwRealPrice, unsigned int dwTax, unsigned int dwRegistSerial, int64_t tResultTime, struct CLogFile* pkLogger);
        void SendBuyErrorResult(uint16_t wInx, char byRet);
        void SendCancelRegistErrorResult(uint16_t wInx, char byRet);
        void SendCancelRegistSuccessResult(uint16_t wInx, uint16_t wItemSerial, unsigned int dwRegistSerial);
        void SendNotifyCloseItem(uint16_t wInx, uint16_t wItemSerial, unsigned int dwRegistSerial, unsigned int dwPrice, char byTax);
        void SendRegistItemErrorResult(uint16_t wInx, char byRet, uint16_t wItemSerial, unsigned int dwRetParam1);
        void SendRegistItemSuccessResult(unsigned int dwLeftDalant, uint16_t wInx, char* pLoadData);
        void SendRepriceErrorResult(struct CPlayer* pReceiver, char byRet);
        void SendRepriceSuccessResult(struct CPlayer* pReceiver, uint16_t wItemSerial, unsigned int dwNewPrice, unsigned int dwRegistSerial, unsigned int dwTax);
        void SendSearchErrorResult(uint16_t wInx, char byRet);
        void SendSearchResult(uint16_t wInx, char* pLoadData);
        void SendSellInfom(uint16_t wInx, uint16_t wItemSerial, unsigned int dwAddDalant, unsigned int dwTaxDalant, unsigned int dwTotalDalant);
        void SetAllItemState(char byState, char byMaxCnt);
        void SetCompleteInfo(struct CLogFile* pkLogger);
        bool SetLoadInfo(char byType, unsigned int dwSerial, struct _TRADE_DB_BASE* kInfo, struct CLogFile* pkLogger);
        ~CUnmannedTraderUserInfo();
        void dtor_CUnmannedTraderUserInfo();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CUnmannedTraderUserInfo, 104>(), "CUnmannedTraderUserInfo");
END_ATF_NAMESPACE

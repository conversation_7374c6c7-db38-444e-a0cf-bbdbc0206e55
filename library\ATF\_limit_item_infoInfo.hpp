// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_limit_item_info.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _limit_item_infoctor__limit_item_info2_ptr = void (WINAPIV*)(struct _limit_item_info*);
        using _limit_item_infoctor__limit_item_info2_clbk = void (WINAPIV*)(struct _limit_item_info*, _limit_item_infoctor__limit_item_info2_ptr);
        using _limit_item_infoinit4_ptr = void (WINAPIV*)(struct _limit_item_info*);
        using _limit_item_infoinit4_clbk = void (WINAPIV*)(struct _limit_item_info*, _limit_item_infoinit4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    struct tagMINMAXINFO
    {
        tagPOINT ptReserved;
        tagPOINT ptMaxSize;
        tagPOINT ptMaxPosition;
        tagPOINT ptMinTrackSize;
        tagPOINT ptMaxTrackSize;
    };
END_ATF_NAMESPACE

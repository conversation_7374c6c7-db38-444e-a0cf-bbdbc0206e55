// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _region_data
    {
        char szRegionData[32];
        struct CMapData *pMap;
        unsigned __int16 wDummyLineIndex;
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_region_data, 48>(), "_region_data");
END_ATF_NAMESPACE

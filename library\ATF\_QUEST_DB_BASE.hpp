// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _QUEST_DB_BASE
    {
        struct  _LIST
        {
            char byQuestType;
            unsigned __int16 wIndex;
            unsigned __int16 wNum[3];
            unsigned int dwPassSec;
        public:
            void Init();
            _LIST();
            void ctor__LIST();
        };
        struct  _NPC_QUEST_HISTORY
        {
            char szQuestCode[8];
            int nEventNo;
            char byLevel;
            unsigned int dwEventEndTime;
        public:
            void Init();
            _NPC_QUEST_HISTORY();
            void ctor__NPC_QUEST_HISTORY();
        };
        struct  _START_NPC_QUEST_HISTORY
        {
            char szQuestCode[64];
            char byLevel;
            _SYSTEMTIME tmStartTime;
            __int64 nEndTime;
        public:
            void Init();
            _START_NPC_QUEST_HISTORY();
            void ctor__START_NPC_QUEST_HISTORY();
        };
        _LIST m_List[30];
        _NPC_QUEST_HISTORY m_History[70];
        unsigned int dwListCnt;
        _START_NPC_QUEST_HISTORY *m_StartHistory;
    public:
        void Init();
        _QUEST_DB_BASE();
        void ctor__QUEST_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_QUEST_DB_BASE, 1592>(), "_QUEST_DB_BASE");
END_ATF_NAMESPACE

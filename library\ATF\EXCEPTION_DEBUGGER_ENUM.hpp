// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum EXCEPTION_DEBUGGER_ENUM
    {
      EXCEPTION_DEBUGGER_NAME_THREAD = 0x1000,
      EXCEPTION_DEBUGGER_PROBE = 0x1001,
      EXCEPTION_DEBUGGER_RUNTIMECHECK = 0x1002,
      EXCEPTION_DEBUGGER_FIBER = 0x1003,
      EXCEPTION_DEBUGGER_HANDLECHECK = 0x1004,
      EXCEPTION_DEBUGGER_MAX = 0x1004,
    };
END_ATF_NAMESPACE

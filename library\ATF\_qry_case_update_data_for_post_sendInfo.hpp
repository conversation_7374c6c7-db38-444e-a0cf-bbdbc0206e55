// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_update_data_for_post_send.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_update_data_for_post_sendctor__qry_case_update_data_for_post_send2_ptr = void (WINAPIV*)(struct _qry_case_update_data_for_post_send*);
        using _qry_case_update_data_for_post_sendctor__qry_case_update_data_for_post_send2_clbk = void (WINAPIV*)(struct _qry_case_update_data_for_post_send*, _qry_case_update_data_for_post_sendctor__qry_case_update_data_for_post_send2_ptr);
        using _qry_case_update_data_for_post_sendsize4_ptr = int (WINAPIV*)(struct _qry_case_update_data_for_post_send*);
        using _qry_case_update_data_for_post_sendsize4_clbk = int (WINAPIV*)(struct _qry_case_update_data_for_post_send*, _qry_case_update_data_for_post_sendsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

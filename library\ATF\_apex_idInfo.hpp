// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_apex_id.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _apex_idctor__apex_id2_ptr = void (WINAPIV*)(struct _apex_id*, char);
        using _apex_idctor__apex_id2_clbk = void (WINAPIV*)(struct _apex_id*, char, _apex_idctor__apex_id2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

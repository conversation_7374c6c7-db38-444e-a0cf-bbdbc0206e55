// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingID.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CBillingIDAlive2_ptr = void (WINAPIV*)(struct CBillingID*, struct CUserDB*);
        using CBillingIDAlive2_clbk = void (WINAPIV*)(struct CBillingID*, struct CUserDB*, CBillingIDAlive2_ptr);
        using CBillingIDBillingClose4_ptr = void (WINAPIV*)(struct CBillingID*, char*);
        using CBillingIDBillingClose4_clbk = void (WINAPIV*)(struct CBillingID*, char*, CBillingIDBillingClose4_ptr);
        
        using CBillingIDctor_CBillingID6_ptr = void (WINAPIV*)(struct CBillingID*);
        using CBillingIDctor_CBillingID6_clbk = void (WINAPIV*)(struct CBillingID*, CBillingIDctor_CBillingID6_ptr);
        using CBillingIDLogin8_ptr = void (WINAPIV*)(struct CBillingID*, struct CUserDB*);
        using CBillingIDLogin8_clbk = void (WINAPIV*)(struct CBillingID*, struct CUserDB*, CBillingIDLogin8_ptr);
        using CBillingIDLogout10_ptr = void (WINAPIV*)(struct CBillingID*, struct CUserDB*);
        using CBillingIDLogout10_clbk = void (WINAPIV*)(struct CBillingID*, struct CUserDB*, CBillingIDLogout10_ptr);
        using CBillingIDSendMsg_Login12_ptr = bool (WINAPIV*)(struct CBillingID*, char*, char*, char*, int16_t, struct _SYSTEMTIME*, int);
        using CBillingIDSendMsg_Login12_clbk = bool (WINAPIV*)(struct CBillingID*, char*, char*, char*, int16_t, struct _SYSTEMTIME*, int, CBillingIDSendMsg_Login12_ptr);
        
        using CBillingIDdtor_CBillingID17_ptr = void (WINAPIV*)(struct CBillingID*);
        using CBillingIDdtor_CBillingID17_clbk = void (WINAPIV*)(struct CBillingID*, CBillingIDdtor_CBillingID17_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  $5DF3780F58367B9F2537DF425A8A812E
    {
        unsigned __int16 vt;
        unsigned __int16 wReserved1;
        unsigned __int16 wReserved2;
        unsigned __int16 wReserved3;
        char cVal;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$684CBA0077E04B5E942B3E8824B58094.hpp>
#include <_M128.hpp>


START_ATF_NAMESPACE
    struct _CONTEXT
    {
        unsigned __int64 P1Home;
        unsigned __int64 P2Home;
        unsigned __int64 P3Home;
        unsigned __int64 P4Home;
        unsigned __int64 P5Home;
        unsigned __int64 P6Home;
        unsigned int ContextFlags;
        unsigned int MxCsr;
        unsigned __int16 SegCs;
        unsigned __int16 SegDs;
        unsigned __int16 SegEs;
        unsigned __int16 SegFs;
        unsigned __int16 SegGs;
        unsigned __int16 SegSs;
        unsigned int EFlags;
        unsigned __int64 Dr0;
        unsigned __int64 Dr1;
        unsigned __int64 Dr2;
        unsigned __int64 Dr3;
        unsigned __int64 Dr6;
        unsigned __int64 Dr7;
        unsigned __int64 Rax;
        unsigned __int64 Rcx;
        unsigned __int64 Rdx;
        unsigned __int64 Rbx;
        unsigned __int64 Rsp;
        unsigned __int64 Rbp;
        unsigned __int64 Rsi;
        unsigned __int64 Rdi;
        unsigned __int64 R8;
        unsigned __int64 R9;
        unsigned __int64 R10;
        unsigned __int64 R11;
        unsigned __int64 R12;
        unsigned __int64 R13;
        unsigned __int64 R14;
        unsigned __int64 R15;
        unsigned __int64 Rip;
        $684CBA0077E04B5E942B3E8824B58094 ___u38;
        _M128 VectorRegister[26];
        unsigned __int64 VectorControl;
        unsigned __int64 DebugControl;
        unsigned __int64 LastBranchToRip;
        unsigned __int64 LastBranchFromRip;
        unsigned __int64 LastExceptionToRip;
        unsigned __int64 LastExceptionFromRip;
    };    
    static_assert(ATF::checkSize<_CONTEXT, 1232>(), "_CONTEXT");
END_ATF_NAMESPACE

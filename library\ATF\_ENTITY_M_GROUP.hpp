// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _ENTITY_M_GROUP
    {
        unsigned __int16 Type;
        unsigned __int16 TriNum;
        __int16 MtlId;
        float BBMin[3];
        float BBMax[3];
        unsigned int VBMinIndex;
        unsigned int IBMinIndex;
        unsigned int VCnt;
        unsigned __int16 ObjectId;
        void *MultiSourceUV;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

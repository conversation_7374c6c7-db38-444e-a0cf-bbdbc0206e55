// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CRecentFileListVtbl.hpp>


START_ATF_NAMESPACE
    struct CRecentFileList
    {
        CRecentFileListVtbl *vfptr;
        int m_nSize;
        ATL::CStringT<char> *m_arrNames;
        ATL::CStringT<char> m_strSectionName;
        ATL::CStringT<char> m_strEntryFormat;
        unsigned int m_nStart;
        int m_nMaxDisplayLength;
        ATL::CStringT<char> m_strOriginal;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AutominePersonal.hpp>
#include <CLogFile.hpp>
#include <CMapData.hpp>
#include <CPlayer.hpp>
#include <_PERSONALAMINE_INVEN_DB_BASE.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct AutominePersonalMgr
    {
        CLogFile m_logService;
        CLogFile m_logError;
        unsigned int m_dwObjSerial;
        AutominePersonal *m_pMachine;
        unsigned __int16 m_wOreIndex[5];
    public:
        AutominePersonalMgr();
        void ctor_AutominePersonalMgr();
        bool CreateDBTable();
        bool Is_MineRun(int n);
        bool Open_InfoUI(int n, char* pmsg);
        bool Open_InvenUI(int n, char* pmsg);
        bool check_dummy(struct CMapData* pMap, char byCurDummyIndex, float* pfCurPos);
        bool check_machine(int n, unsigned int dwSerial, struct AutominePersonal* pMachine, struct _STORAGE_LIST::_db_con* pcitem);
        bool db_load_inven(unsigned int dwSerial, struct _PERSONALAMINE_INVEN_DB_BASE* pCon);
        bool extract_battery(int n);
        bool extract_battery(int n, char* pmsg);
        struct _STORAGE_LIST::_db_con* get_localitem(struct CPlayer* pOne, uint16_t wItemSerial);
        struct AutominePersonal* get_machine(int nIdx);
        bool init_objects();
        bool initialize();
        bool insert_battery(int n, char* pmsg);
        bool install(int n, char* pmsg);
        static struct AutominePersonalMgr* instance();
        bool make_storagebox(int n, char* pmsg);
        void pop_dqs_makestorage(char byRet, char* pdata);
        bool pop_ore(int n, char* pmsg);
        void push_dqs_makestorage(int n, unsigned int dwAvatorSerial, unsigned int dwTotGold);
        static void release();
        int request_query(char* pdata);
        void result_query(char byRet, char* pdata);
        bool selectore(int n, char* pmsg);
        void send_ecode(int n, char byCode);
        bool uninstall(int n);
        bool uninstall(int n, char* pmsg);
        ~AutominePersonalMgr();
        void dtor_AutominePersonalMgr();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerTW.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerTWctor_CCashDbWorkerTW2_ptr = void (WINAPIV*)(struct CCashDbWorkerTW*);
        using CCashDbWorkerTWctor_CCashDbWorkerTW2_clbk = void (WINAPIV*)(struct CCashDbWorkerTW*, CCashDbWorkerTWctor_CCashDbWorkerTW2_ptr);
        using CCashDbWorkerTWGetUseCashQueryStr4_ptr = void (WINAPIV*)(struct CCashDbWorkerTW*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDbWorkerTWGetUseCashQueryStr4_clbk = void (WINAPIV*)(struct CCashDbWorkerTW*, struct _param_cash_update*, int, char*, uint64_t, CCashDbWorkerTWGetUseCashQueryStr4_ptr);
        
        using CCashDbWorkerTWdtor_CCashDbWorkerTW9_ptr = void (WINAPIV*)(struct CCashDbWorkerTW*);
        using CCashDbWorkerTWdtor_CCashDbWorkerTW9_clbk = void (WINAPIV*)(struct CCashDbWorkerTW*, CCashDbWorkerTWdtor_CCashDbWorkerTW9_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

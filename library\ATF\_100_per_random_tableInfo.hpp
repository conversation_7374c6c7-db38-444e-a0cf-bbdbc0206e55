// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_100_per_random_table.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _100_per_random_tableGetRand2_ptr = uint16_t (WINAPIV*)(struct _100_per_random_table*);
        using _100_per_random_tableGetRand2_clbk = uint16_t (WINAPIV*)(struct _100_per_random_table*, _100_per_random_tableGetRand2_ptr);
        
        using _100_per_random_tablector__100_per_random_table4_ptr = void (WINAPIV*)(struct _100_per_random_table*);
        using _100_per_random_tablector__100_per_random_table4_clbk = void (WINAPIV*)(struct _100_per_random_table*, _100_per_random_tablector__100_per_random_table4_ptr);
        using _100_per_random_tablereset6_ptr = void (WINAPIV*)(struct _100_per_random_table*);
        using _100_per_random_tablereset6_clbk = void (WINAPIV*)(struct _100_per_random_table*, _100_per_random_tablereset6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

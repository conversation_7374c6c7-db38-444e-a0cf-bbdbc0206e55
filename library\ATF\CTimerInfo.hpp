// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTimer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CTimerCalculateTime1_ptr = void (WINAPIV*)(struct CTimer*);
        using CTimerCalculateTime1_clbk = void (WINAPIV*)(struct CTimer*, CTimerCalculateTime1_ptr);
        using CTimerGetDuration2_ptr = float (WINAPIV*)(struct CTimer*);
        using CTimerGetDuration2_clbk = float (WINAPIV*)(struct CTimer*, CTimerGetDuration2_ptr);
        using CTimerGetLoopTime3_ptr = float (WINAPIV*)(struct CTimer*);
        using CTimerGetLoopTime3_clbk = float (WINAPIV*)(struct CTimer*, CTimerGetLoopTime3_ptr);
        using CTimerGetTicks4_ptr = uint64_t (WINAPIV*)(struct CTimer*);
        using CTimerGetTicks4_clbk = uint64_t (WINAPIV*)(struct CTimer*, CTimerGetTicks4_ptr);
        using CTimerGetTime5_ptr = float (WINAPIV*)(struct CTimer*);
        using CTimerGetTime5_clbk = float (WINAPIV*)(struct CTimer*, CTimerGetTime5_ptr);
        using CTimerSetMinFPS6_ptr = void (WINAPIV*)(struct CTimer*, float);
        using CTimerSetMinFPS6_clbk = void (WINAPIV*)(struct CTimer*, float, CTimerSetMinFPS6_ptr);
        using CTimerSetTime7_ptr = void (WINAPIV*)(struct CTimer*, float);
        using CTimerSetTime7_clbk = void (WINAPIV*)(struct CTimer*, float, CTimerSetTime7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

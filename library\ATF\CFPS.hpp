// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CFPSVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CFPS
    {
        CFPSVtbl *vfptr;
        unsigned int m_dwFrames;
        unsigned int m_dwFrameTime;
        unsigned int m_dwFrameCount;
    public:
        CFPS();
        void ctor_CFPS();
        void CalcFPS();
        unsigned int GetFPS();
        ~CFPS();
        void dtor_CFPS();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

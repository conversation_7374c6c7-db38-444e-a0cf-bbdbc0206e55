// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_personal_amine_mineore_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _personal_amine_mineore_zoclctor__personal_amine_mineore_zocl2_ptr = void (WINAPIV*)(struct _personal_amine_mineore_zocl*);
        using _personal_amine_mineore_zoclctor__personal_amine_mineore_zocl2_clbk = void (WINAPIV*)(struct _personal_amine_mineore_zocl*, _personal_amine_mineore_zoclctor__personal_amine_mineore_zocl2_ptr);
        using _personal_amine_mineore_zoclclear4_ptr = void (WINAPIV*)(struct _personal_amine_mineore_zocl*);
        using _personal_amine_mineore_zoclclear4_clbk = void (WINAPIV*)(struct _personal_amine_mineore_zocl*, _personal_amine_mineore_zoclclear4_ptr);
        using _personal_amine_mineore_zoclsize6_ptr = int (WINAPIV*)(struct _personal_amine_mineore_zocl*);
        using _personal_amine_mineore_zoclsize6_clbk = int (WINAPIV*)(struct _personal_amine_mineore_zocl*, _personal_amine_mineore_zoclsize6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

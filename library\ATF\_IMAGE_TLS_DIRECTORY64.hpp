// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_TLS_DIRECTORY64
    {
        unsigned __int64 StartAddressOfRawData;
        unsigned __int64 EndAddressOfRawData;
        unsigned __int64 AddressOfIndex;
        unsigned __int64 AddressOfCallBacks;
        unsigned int SizeOfZeroFill;
        unsigned int Characteristics;
    };
END_ATF_NAMESPACE

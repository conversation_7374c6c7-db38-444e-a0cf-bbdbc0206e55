// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPoint.hpp>
#include <CRect.hpp>
#include <tagPOINT.hpp>
#include <tagRECT.hpp>
#include <tagSIZE.hpp>


START_ATF_NAMESPACE
    struct  CSize : tagSIZE
    {
    public:
        CSize(int initCX, int initCY);
        void ctor_CSize(int initCX, int initCY);
        CSize(struct tagPOINT initPt);
        void ctor_CSize(struct tagPOINT initPt);
        CSize(struct tagSIZE initSize);
        void ctor_CSize(struct tagSIZE initSize);
        CSize(unsigned int dwSize);
        void ctor_CSize(unsigned int dwSize);
        CSize();
        void ctor_CSize();
        void SetSize(int CX, int CY);
    };    
    static_assert(ATF::checkSize<CSize, 8>(), "CSize");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct TPIVtbl
    {
        unsigned int (WINAPIV *QueryInterfaceVersion)(TPI *_this);
        BYTE gap8[16];
        int (WINAPIV *QueryCVRecordForTi16)(TPI *_this, unsigned __int16, char *, int *);
        int (WINAPIV *QueryPbCVRecordForTi16)(TPI *_this, unsigned __int16, char **);
        unsigned __int16 (WINAPIV *QueryTi16Min)(TPI *_this);
        unsigned __int16 (WINAPIV *QueryTi16Mac)(TPI *_this);
        int (WINAPIV *QueryCb)(TPI *_this);
        int (WINAPIV *Close)(TPI *_this);
        int (WINAPIV *Commit)(TPI *_this);
        int (WINAPIV *QueryTi16ForUDT)(TPI *_this, const char *, int, unsigned __int16 *);
        int (WINAPIV *SupportQueryTiForUDT)(TPI *_this);
        int (WINAPIV *fIs16bitTypePool)(TPI *_this);
        int (WINAPIV *QueryTiForUDT)(TPI *_this, const char *, int, unsigned int *);
        int (WINAPIV *QueryTiForCVRecord)(TPI *_this, char *, unsigned int *);
        int (WINAPIV *QueryCVRecordForTi)(TPI *_this, unsigned int, char *, int *);
        int (WINAPIV *QueryPbCVRecordForTi)(TPI *_this, unsigned int, char **);
        unsigned int (WINAPIV *QueryTiMin)(TPI *_this);
        unsigned int (WINAPIV *QueryTiMac)(TPI *_this);
        int (WINAPIV *AreTypesEqual)(TPI *_this, unsigned int, unsigned int);
        int (WINAPIV *IsTypeServed)(TPI *_this, unsigned int);
        int (WINAPIV *QueryTiForUDTW)(TPI *_this, const wchar_t *, int, unsigned int *);
    };
END_ATF_NAMESPACE

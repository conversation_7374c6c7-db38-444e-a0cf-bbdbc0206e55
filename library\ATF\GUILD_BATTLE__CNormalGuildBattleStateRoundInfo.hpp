// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateRound.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateRoundctor_CNormalGuildBattleStateRound2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundctor_CNormalGuildBattleStateRound2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, GUILD_BATTLE__CNormalGuildBattleStateRoundctor_CNormalGuildBattleStateRound2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundEnter4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundEnter4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundEnter4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundEnter6_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundEnter6_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundEnter6_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundFin8_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundFin8_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundFin8_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundFin10_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundFin10_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundFin10_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundLog12_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CNormalGuildBattle*, char*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundLog12_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CNormalGuildBattle*, char*, GUILD_BATTLE__CNormalGuildBattleStateRoundLog12_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundLoop14_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundLoop14_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundLoop14_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundLoop16_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundLoop16_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundLoop16_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateRounddtor_CNormalGuildBattleStateRound18_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*);
            using GUILD_BATTLE__CNormalGuildBattleStateRounddtor_CNormalGuildBattleStateRound18_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRound*, GUILD_BATTLE__CNormalGuildBattleStateRounddtor_CNormalGuildBattleStateRound18_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

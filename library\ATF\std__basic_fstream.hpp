// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__basic_iostream.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 8)
        template<>
        struct  basic_fstream<wchar_t,char_traits<wchar_t> > : basic_iostream<wchar_t,char_traits<wchar_t> >
        {
            BYTE _Filebuffer[48];
            BYTE gapA8[96];
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE
#include <std__basic_iostream.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 8)
        template<>
        struct  basic_fstream<char,char_traits<char> > : basic_iostream<char,char_traits<char> >
        {
            BYTE _Filebuffer[48];
            BYTE gapA8[96];
        };
        #pragma pack(pop)
    }; // end namespace std
E<PERSON>_ATF_NAMESPACE

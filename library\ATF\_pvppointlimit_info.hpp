// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _pvppointlimit_info
    {
        __int64 tUpdatedate;
        bool bUseUp;
        char byLimitRate;
        long double dOriginalPoint;
        long double dLimitPoint;
        long double dUsePoint;
    };
END_ATF_NAMESPACE

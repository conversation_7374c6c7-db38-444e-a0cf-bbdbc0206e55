// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _add_char_result_zone
    {
        char byRetCode;
        char byAddSlotIndex;
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_add_char_result_zone, 2>(), "_add_char_result_zone");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <CRaceBuffHolyQuestResultInfo.hpp>
#include <CRaceBuffInfoByHolyQuestList.hpp>
#include <CRaceBuffInfoByHolyQuestfGroup.hpp>


START_ATF_NAMESPACE
    struct CRaceBuffByHolyQuestProcedure
    {
        enum REQUEST_TYPE
        {
            RT_WAIT = 0x0,
            RT_CANCEL_RACE_BUFF = 0x1,
            RT_SET_RACE_BATTLE_RESULT = 0x2,
            RT_SET_RACE_BUFF = 0x3,
            RT_TYPE_MAX = 0x4,
        };
        REQUEST_TYPE m_eState;
        unsigned int m_uiProccessIndex;
        CRaceBuffHolyQuestResultInfo m_kBuffHolyQestResultInfo;
        CRaceBuffInfoByHolyQuestList m_kBuffInfo;
    public:
        CRaceBuffByHolyQuestProcedure();
        void ctor_CRaceBuffByHolyQuestProcedure();
        int CancelPlayerRaceBuff(struct CPlayer* pkPlayer, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE eReleaseType, unsigned int uiReleaseLv);
        bool CreateComplete(struct CPlayer* pkPlayer);
        int GetRaceBuffLevel(struct CPlayer* pOne);
        bool Init();
        void Loop();
        void LoopSubProcSetRaceBuff();
        bool Request(int iType);
        bool RequestSubProcCancelRaceBuff();
        bool RequestSubProcSetRaceBattleResult();
        bool RequestSubProcSetRaceBuff();
        ~CRaceBuffByHolyQuestProcedure();
        void dtor_CRaceBuffByHolyQuestProcedure();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CLID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _d_trade_start_inform_zocl
    {
        _CLID idAsker;
        char byAskerEmptyNum;
        _CLID idAnswer;
        char byAnswerEmptyNum;
        unsigned int dwKey[4];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagVS_FIXEDFILEINFO
    {
        unsigned int dwSignature;
        unsigned int dwStrucVersion;
        unsigned int dwFileVersionMS;
        unsigned int dwFileVersionLS;
        unsigned int dwProductVersionMS;
        unsigned int dwProductVersionLS;
        unsigned int dwFileFlagsMask;
        unsigned int dwFileFlags;
        unsigned int dwFileOS;
        unsigned int dwFileType;
        unsigned int dwFileSubtype;
        unsigned int dwFileDateMS;
        unsigned int dwFileDateLS;
    };
END_ATF_NAMESPACE

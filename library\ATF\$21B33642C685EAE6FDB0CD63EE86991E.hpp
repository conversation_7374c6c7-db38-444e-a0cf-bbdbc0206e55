// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $21B33642C685EAE6FDB0CD63EE86991E
    {
      TIXML_NO_ERROR = 0x0,
      TIXML_ERROR = 0x1,
      TIXML_ERROR_OPENING_FILE = 0x2,
      TIXML_ERROR_OUT_OF_MEMORY = 0x3,
      TIXML_ERROR_PARSING_ELEMENT = 0x4,
      TIXML_ERROR_FAILED_TO_READ_ELEMENT_NAME = 0x5,
      TIXML_ERROR_READING_ELEMENT_VALUE = 0x6,
      TIXML_ERROR_READING_ATTRIBUTES = 0x7,
      TIXML_ERROR_PARSING_EMPTY = 0x8,
      TIXML_ERROR_READING_END_TAG = 0x9,
      TIXML_ERROR_PARSING_UNKNOWN = 0xA,
      TIXML_ERROR_PARSING_COMMENT = 0xB,
      TIXML_ERROR_PARSING_DECLARATION = 0xC,
      TIXML_ERROR_DOCUMENT_EMPTY = 0xD,
      TIXML_ERROR_EMBEDDED_NULL = 0xE,
      TIXML_ERROR_PARSING_CDATA = 0xF,
      TIXML_ERROR_STRING_COUNT = 0x10,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_equip_db_load.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _equip_db_loadctor__equip_db_load2_ptr = void (WINAPIV*)(struct _equip_db_load*);
        using _equip_db_loadctor__equip_db_load2_clbk = void (WINAPIV*)(struct _equip_db_load*, _equip_db_loadctor__equip_db_load2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

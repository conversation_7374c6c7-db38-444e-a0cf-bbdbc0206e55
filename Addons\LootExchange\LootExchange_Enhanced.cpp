#include "stdafx.h"
#include "LootExchange.h"
#include "../../Common/ETypes.h"
#include "../../Common/Helpers/RapidHelper.hpp"

#include <bitset>
#include <fstream>
#include <sstream>
#include <ATF/global.hpp>

namespace GameServer
{
    namespace Addon
    {
        // Static member definitions (basic configuration)
        bool CLootExchange::m_bActivated = false;
        bool CLootExchange::m_bExchangeAll = false;
        int CLootExchange::m_nMinimumValue = 0;
        float CLootExchange::m_fMaxPickupDistance = 100.0f;
        bool CLootExchange::m_bEnableLogging = false;
        std::string CLootExchange::m_sLogFilePath = "./YorozuyaGS/Logs/LootExchange.log";
        std::vector<int> CLootExchange::m_vMoneyTypePriority = {0, 1, 2, 3, 4, 5, 6};
        bool CLootExchange::m_bItemTypeSettings[7] = {true, true, true, true, true, true, true};
        bool CLootExchange::m_bCurrencySettings[7] = {true, true, true, true, true, true, true};
        bool CLootExchange::m_bUseRaceSpecificPricing = true;
        int CLootExchange::m_nDefaultRace = 0;
        bool CLootExchange::m_bMultiplyByDurability = true;
        int CLootExchange::m_nExchangeRateModifier = 100;
        bool CLootExchange::m_bPremiumPlayersOnly = false;
        int CLootExchange::m_nMinimumPlayerLevel = 0;
        int CLootExchange::m_nDailyExchangeLimit = 0;
        std::string CLootExchange::m_sDailyResetTime = "00:00";

        void CLootExchange::load()
        {
            LoadConfiguration();
            enable_hook(&ATF::CPlayer::pc_TakeGroundingItem, &CLootExchange::pc_TakeGroundingItem);
        }

        void CLootExchange::unload()
        {
            cleanup_all_hook();
        }

        Yorozuya::Module::ModuleName_t CLootExchange::get_name()
        {
            static const Yorozuya::Module::ModuleName_t name = "addon.loot_exchange";
            return name;
        }

        void CLootExchange::configure(const rapidjson::Value& nodeConfig)
        {
            // This method is now empty since we load from INI file
            // Keep it for interface compatibility
        }

        void CLootExchange::LoadConfiguration()
        {
            const std::string iniPath = "./YorozuyaGS/Configuration/LootExchange.ini";
            
            // Helper function to read INI values
            auto GetPrivateProfileIntA = [](const char* section, const char* key, int defaultValue, const char* fileName) -> int {
                std::ifstream file(fileName);
                if (!file.is_open()) return defaultValue;
                
                std::string line, currentSection;
                bool inTargetSection = false;
                
                while (std::getline(file, line)) {
                    line.erase(0, line.find_first_not_of(" \t"));
                    line.erase(line.find_last_not_of(" \t") + 1);
                    
                    if (line.empty() || line[0] == ';') continue;
                    
                    if (line[0] == '[' && line.back() == ']') {
                        currentSection = line.substr(1, line.length() - 2);
                        inTargetSection = (currentSection == section);
                        continue;
                    }
                    
                    if (inTargetSection) {
                        size_t pos = line.find('=');
                        if (pos != std::string::npos) {
                            std::string keyName = line.substr(0, pos);
                            std::string value = line.substr(pos + 1);
                            
                            keyName.erase(keyName.find_last_not_of(" \t") + 1);
                            value.erase(0, value.find_first_not_of(" \t"));
                            
                            if (keyName == key) {
                                return std::stoi(value);
                            }
                        }
                    }
                }
                return defaultValue;
            };

            // Load main settings
            m_bActivated = GetPrivateProfileIntA("LootExchange", "Activated", 0, iniPath.c_str()) != 0;
            m_bExchangeAll = GetPrivateProfileIntA("LootExchange", "ExchangeAll", 0, iniPath.c_str()) != 0;
            m_bUseCustomMappings = GetPrivateProfileIntA("AdvancedSettings", "UseCustomMappings", 0, iniPath.c_str()) != 0;
            m_bEnableLogging = GetPrivateProfileIntA("LootExchange", "EnableLogging", 0, iniPath.c_str()) != 0;

            // Load custom mappings if enabled
            if (m_bUseCustomMappings) {
                LoadItemMappings();
                LoadTableMappings();
                LoadCategoryMappings();
                LoadExclusionList();
                LoadPriorityOverrides();
            }

            if (m_bEnableLogging) {
                LogExchange("LootExchange configuration loaded from INI file");
                if (m_bUseCustomMappings) {
                    LogExchange("Custom mapping system enabled");
                }
            }
        }

        void WINAPIV CLootExchange::pc_TakeGroundingItem(
            ATF::CPlayer* pObj,
            ATF::CItemBox* pBox,
            uint16_t wAddSerial,
            ATF::Info::CPlayerpc_TakeGroundingItem1947_ptr next)
        {
            bool bApplyModule = false;

            do
            {
                if (!CLootExchange::m_bActivated)
                {
                    break;
                }

                // Check premium player requirement
                if (m_bPremiumPlayersOnly && !pObj->IsPremiumUser())
                {
                    break;
                }

                // Check minimum player level
                if (pObj->GetLevel() < m_nMinimumPlayerLevel)
                {
                    break;
                }

                if (!pBox->m_bLive)
                {
                    break;
                }
                
                if (!pBox->IsTakeRight(pObj))
                {
                    break;
                }

                if (ATF::Global::Get3DSqrt(pBox->m_fCurPos, pObj->m_fCurPos) > m_fMaxPickupDistance)
                {
                    break;
                }

                if (pBox->m_Item.m_byTableCode >= _countof(ATF::Global::g_MainThread->m_tblItemData))
                {
                    break;
                }

                // Check if this item type is allowed for exchange
                if (!IsItemTypeAllowed(pBox->m_Item.m_byTableCode))
                {
                    break;
                }

                auto& ItemRecords = ATF::Global::g_MainThread->m_tblItemData[pBox->m_Item.m_byTableCode];
                auto pRecord = ItemRecords.GetRecord(pBox->m_Item.m_wItemIndex);
                if (pRecord == nullptr)
                {
                    break;
                }

                uint32_t nSelectedMoneyType = 0;
                int nMoneyValue = 0;
                bool bFoundValidMapping = false;

                // Try custom mapping system first
                if (m_bUseCustomMappings) {
                    bFoundValidMapping = GetCustomMapping(pObj, pRecord, pBox->m_Item.m_byTableCode, pBox->m_Item.m_wItemIndex, nSelectedMoneyType, nMoneyValue);
                }

                // Fall back to original system if no custom mapping found
                if (!bFoundValidMapping) {
                    // Get all available money types for this item
                    ::std::bitset<32> bMoneyType(GetMoneyType(pRecord, pBox->m_Item.m_byTableCode));
                    
                    // Get priority list (may be overridden for specific items)
                    std::string itemCode = "item_" + std::to_string((int)pBox->m_Item.m_byTableCode) + "_" + std::to_string(pBox->m_Item.m_wItemIndex);
                    std::vector<int> priorityList = GetItemPriority(itemCode, pBox->m_Item.m_byTableCode, pBox->m_Item.m_wItemIndex);
                    
                    if (CLootExchange::m_bExchangeAll) {
                        // Try money types in configured priority order
                        for (int priorityType : priorityList) {
                            if (priorityType >= 0 && priorityType < static_cast<int>(e_money_type::num) &&
                                bMoneyType.test(priorityType) && IsCurrencyTypeAllowed(priorityType)) {
                                nSelectedMoneyType = priorityType;
                                bFoundValidMapping = true;
                                break;
                            }
                        }
                    } else {
                        // Original behavior: only processing_point
                        if (bMoneyType.test(static_cast<int>(e_money_type::processing_point)) &&
                            IsCurrencyTypeAllowed(static_cast<int>(e_money_type::processing_point))) {
                            nSelectedMoneyType = static_cast<int>(e_money_type::processing_point);
                            bFoundValidMapping = true;
                        }
                    }

                    if (bFoundValidMapping) {
                        nMoneyValue = GetMoneyValue(pRecord, pBox->m_Item.m_byTableCode, nSelectedMoneyType, pObj->GetObjRace());
                    }
                }

                if (!bFoundValidMapping || nMoneyValue < m_nMinimumValue) {
                    break;
                }

                // Apply exchange rate modifier (only if not already applied in custom mapping)
                if (!m_bUseCustomMappings || nMoneyValue == GetMoneyValue(pRecord, pBox->m_Item.m_byTableCode, nSelectedMoneyType, pObj->GetObjRace())) {
                    nMoneyValue = (nMoneyValue * m_nExchangeRateModifier) / 100;
                }

                // Multiply by durability if enabled
                if (m_bMultiplyByDurability) {
                    nMoneyValue *= pBox->m_Item.m_dwDur ? pBox->m_Item.m_dwDur : 1;
                }

                if (nMoneyValue <= 0) {
                    break;
                }

                bool bAddMoney = AddMoney(pObj, nSelectedMoneyType, nMoneyValue);
                if (!bAddMoney) {
                    break;
                }

                // Log the exchange if enabled
                if (m_bEnableLogging) {
                    std::stringstream logMsg;
                    logMsg << "Player " << pObj->GetObjName() << " exchanged item (Table:" 
                           << (int)pBox->m_Item.m_byTableCode << ", Index:" << pBox->m_Item.m_wItemIndex 
                           << ") for " << nMoneyValue << " of currency type " << nSelectedMoneyType;
                    if (m_bUseCustomMappings) {
                        logMsg << " [Custom Mapping]";
                    }
                    LogExchange(logMsg.str());
                }

                ATF::_STORAGE_LIST::_db_con BoxItem;
                memcpy(&BoxItem, &pBox->m_Item, sizeof(BoxItem));
                pBox->Destroy();
                pObj->SendMsg_TakeAddResult(0, &BoxItem);

                bApplyModule = true;
            } while (false);

            if (!bApplyModule)
                return next(pObj, pBox, wAddSerial);
        }

    }
}

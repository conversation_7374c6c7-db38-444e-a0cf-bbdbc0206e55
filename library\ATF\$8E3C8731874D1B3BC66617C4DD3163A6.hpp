// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $8E3C8731874D1B3BC66617C4DD3163A6
    {
        BYTE gap0[8];
        int scode;
    };    
    static_assert(ATF::checkSize<$8E3C8731874D1B3BC66617C4DD3163A6, 12>(), "$8E3C8731874D1B3BC66617C4DD3163A6");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    struct _CMInvokeCommandInfoEx
    {
        unsigned int cbSize;
        unsigned int fMask;
        HWND__ *hwnd;
        const char *lpVerb;
        const char *lpParameters;
        const char *lpDirectory;
        int nShow;
        unsigned int dwHotKey;
        void *hIcon;
        const char *lpTitle;
        const wchar_t *lpVerbW;
        const wchar_t *lpParametersW;
        const wchar_t *lpDirectoryW;
        const wchar_t *lpTitleW;
        tagPOINT ptInvoke;
    };
END_ATF_NAMESPACE

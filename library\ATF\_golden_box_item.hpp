// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _golden_box_item
    {
        struct _golden_box_item_info
        {
            char m_byTableCode;
            unsigned int m_dwIndex;
            unsigned __int16 m_wNum;
            unsigned __int16 m_wRate;
        };
        char m_bydck;
        unsigned int m_dwStarterBoxCnt;
        char m_byBoxTableCode[2];
        unsigned int m_dwBoxIndex[2];
        unsigned __int16 m_wBoxMax[2];
        char m_bygolden_item_num[2];
        _golden_box_item_info m_golden_box_item_info[2][100];
    public:
        _golden_box_item();
        void ctor__golden_box_item();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <MyTimer.hpp>


START_ATF_NAMESPACE
    struct Task
    {
        bool _bFill;
        int _nRetCode;
        int _nTaskCode;
        MyTimer::TIME _tmReg;
        char *_pBuf;
        unsigned __int64 _nBufSize;
        unsigned __int64 _nMaxBufSize;
    public:
        int GetRetCode();
        char* GetTaskBuf();
        int GetTaskCode();
        bool Initialize(uint64_t nMaxBufSize);
        void SetRetCode(int nCode);
        void SetTask(int nTaskCode, char* p, uint64_t size);
        Task();
        void ctor_Task();
        ~Task();
        void dtor_Task();
    };
END_ATF_NAMESPACE

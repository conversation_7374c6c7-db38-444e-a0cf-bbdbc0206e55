// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CNationSettingDataVtbl
    {
        int (WINAPIV *Init)(struct CNationSettingData *_this);
        BYTE gap8[16];
        struct CBilling *(WINAPIV *CreateBilling)(struct CNationSettingData *_this);
        const char *(WINAPIV *GetItemName)(struct CNationSettingData *_this, struct _NameTxt_fld *);
        bool (WINAPIV *ValidMacAddress)(struct CNationSettingData *_this);
        bool (WINAPIV *IsNormalStringW)(struct CNationSettingData *_this, const wchar_t *);
        bool (WINAPIV *IsNormalStringA)(struct CNationSettingData *_this, const char *);
        bool (WINAPIV *IsNormalChar)(struct CNationSettingData *_this, const wchar_t);
        void (WINAPIV *SetUnitPassiveValue)(struct CNationSettingData *_this, float *);
        bool (WINAPIV *IsPersonalFreeFixedAmountBillingType)(struct CNationSettingData *_this, __int16 *, __int16 *);
        bool (WINAPIV *CheckEnterWorldRequest)(struct CNationSettingData *_this, int, char *);
        void (WINAPIV *CreateComplete)(struct CNationSettingData *_this, struct CPlayer *);
        void (WINAPIV *NetClose)(struct CNationSettingData *_this, struct CPlayer *);
        int (WINAPIV *GetCashItemPrice)(struct CNationSettingData *_this, struct _CashShop_str_fld *);
        bool (WINAPIV *IsApplyPcbangPrimium)(struct CNationSettingData *_this, struct CPlayer *const );
        void (WINAPIV *SendCashDBDSNRequest)(struct CNationSettingData *_this);
        bool (WINAPIV *ReadSystemPass)(struct CNationSettingData *_this);
    };
END_ATF_NAMESPACE

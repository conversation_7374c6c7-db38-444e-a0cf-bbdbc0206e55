// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _combine_ex_item_accept_result_zocl
    {
        char byErrCode;
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_combine_ex_item_accept_result_zocl, 1>(), "_combine_ex_item_accept_result_zocl");
END_ATF_NAMESPACE

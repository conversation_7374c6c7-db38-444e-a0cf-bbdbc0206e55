// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SRAND.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _SRANDctor__SRAND2_ptr = void (WINAPIV*)(struct _SRAND*);
        using _SRANDctor__SRAND2_clbk = void (WINAPIV*)(struct _SRAND*, _SRANDctor__SRAND2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

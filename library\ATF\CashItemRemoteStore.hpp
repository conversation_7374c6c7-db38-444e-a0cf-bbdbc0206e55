// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CMyTimer.hpp>
#include <CPlayer.hpp>
#include <CRecordData.hpp>
#include <CS_RCODE.hpp>
#include <_CashShop_fld.hpp>
#include <_FILETIME.hpp>
#include <_STORAGE_LIST.hpp>
#include <_STORAGE_POS_INDIV.hpp>
#include <_cash_discount_.hpp>
#include <_cash_discount_ini_.hpp>
#include <_cash_event.hpp>
#include <_cash_event_ini.hpp>
#include <_cash_event_time.hpp>
#include <_cash_lim_sale.hpp>
#include <_con_event_.hpp>
#include <_con_event_ini.hpp>
#include <_db_cash_limited_sale.hpp>
#include <_param_cash_update.hpp>
#include <_param_cashitem_dblog.hpp>
#include <_request_csi_buy_clzo.hpp>
#include <_result_csi_buy_zocl.hpp>
#include <SetDiscount.hpp>


START_ATF_NAMESPACE
    struct CashItemRemoteStore
    {
        typedef SetDiscount e_race_code;
        typedef int __cdecl logger(const char *);
        struct _remain_num_of_good
        {
            char strCode[8];
            int nMaxNum;
            int nRemainNum;
        public:
            _remain_num_of_good();
            void ctor__remain_num_of_good();
        };
        CLogFile _kLoggers[2];
        CRecordData _kRecGoods;
        CRecordData _kRecConEventMSG;
        _remain_num_of_good *_pkRemainInfo;
        _cash_discount_ m_cde;
        _cash_event m_cash_event[3];
        _con_event_ m_con_event;
        CMyTimer m_TotalEventTimer;
        _cash_lim_sale m_lim_event;
        _cash_lim_sale m_lim_event_New;
        _cash_lim_sale m_lim_event_Old;
        bool _bIsBuyCashItemByGold;
        CLogFile _kSysLog;
    public:
        bool Buy(uint16_t wSock, char* pPacket);
        bool BuyByCash(uint16_t wSock, char* pPacket);
        bool BuyByGold(uint16_t wSock, char* pPacket);
        uint16_t BuyLimSale(char byTableCode, unsigned int dwIndex);
        CashItemRemoteStore();
        void ctor_CashItemRemoteStore();
        bool ChangeDiscountEventTime();
        bool ChangeEventTime(char byEventType);
        void Change_Conditional_Event_Status();
        bool CheatBuy(uint16_t wSock, char* szItemCode, int nNum);
        bool CheatLoadCashAmount(uint16_t wSock, int nNum);
        int CheckCouponType(struct _STORAGE_POS_INDIV* pCoupon, struct CPlayer* pOne, char byCouponNum);
        bool Check_CashEvent_INI(char byEventType);
        void Check_CashEvent_Status(char byEventType);
        void Check_Conditional_Event_INI();
        void Check_Conditional_Event_Status();
        void Check_Grosssales(unsigned int dwTotalSellCash);
        void Check_Loaded_Event_Status(char byEventType);
        void Check_Total_Selling();
        static struct _CashShop_fld* FindCashRec(int nTbl, int nIdx);
        void GetEvnetTime(struct _cash_event_time* pETime, int itime);
        char GetLimDiscout();
        int GetRemainNumOfGood(char* strCode);
        int GetRemainNumOfGood(uint16_t wStoreIndex);
        char GetSetDiscout(char bySetKind);
        char Get_CashEvent_Status(char byEventType);
        void Get_Conditional_Event_Name(char byEventType, char* szEventName);
        char Get_Conditional_Event_Status();
        bool GoodsList(uint16_t wSock, char* pPacket);
        bool GoodsListBuyByCash(uint16_t wSock, char* pPacket);
        bool GoodsListBuyByGold(uint16_t wSock, char* pPacket);
        void Inform_CashEvent(uint16_t wIndex);
        void Inform_CashEvent_Status_All(char byEventType, char byStatus, struct _cash_event_ini* pIni);
        void Inform_ConditionalEvent(uint16_t wIndex);
        void Inform_ConditionalEvent_Status_All(char byEventType, char byStatus, char* pszMsg);
        bool Initialize();
        static struct CashItemRemoteStore* Instance();
        bool IsBuyCashItemByGold();
        bool IsEventTime(char byEventType);
        bool IsUsableCoupon(struct _request_csi_buy_clzo* pBuyList, struct _STORAGE_POS_INDIV pCoupon, struct CPlayer* pOne, bool* bCheck);
        bool LimitedSale_check_count(char byTableCode, unsigned int dwIndex);
        bool LoadBuyCashMode();
        bool LoadNationalPrice(struct CRecordData* krecPrice);
        void Load_Cash_Event();
        void Load_Conditional_Event();
        void Load_Event_INI(struct _cash_event_ini* pIni, struct _FILETIME* pft, char* pEventType);
        void Load_LimitedSale_Event_INI(struct _cash_event_ini* pIni, struct _FILETIME* pft, char* pEventType);
        void Loop_Cash_Event();
        void Loop_Check_Total_Selling();
        void Loop_ContEvent();
        void Loop_TatalCashEvent();
        bool Sell(uint16_t wSock, char* pPacket);
        bool SetNextDiscountEventTime();
        bool SetNextEventTime(char byEventType);
        void Set_CashEvent_Status(char byEventType, char byStatus);
        void Set_Conditional_Evnet_Status(char byStatus);
        void Set_DB_LimitedSale_Event();
        void Set_FROMDB_LimitedSale_Event(struct _db_cash_limited_sale* Sheet);
        void Set_LimitedSale_DCK(char byEventType, char byDCK);
        void Set_LimitedSale_Event();
        void Set_LimitedSale_Event_Ini(struct _cash_event_ini* pIni);
        void Set_LimitedSale_count(char byTableCode, unsigned int dwIndex);
        void Update_INI(struct _cash_event_ini* pNewIni, char byEventType);
        bool UseDiscountCoupon(struct _param_cash_update* pBuyList, struct _STORAGE_POS_INDIV pCoupon, struct CPlayer* pOne);
        bool _InitLoggers();
        bool _MakeLinkTable(char* szMsg, int nSize);
        bool _ReadGoods();
        bool __CheckGoods(struct CRecordData* krecPrice);
        CS_RCODE _buybygold_buy_single_item(struct CPlayer* pOne, struct _request_csi_buy_clzo* pRecv, struct _request_csi_buy_clzo::__item* pSrc, struct _param_cashitem_dblog* pSheet, bool* bCouponUse, struct _result_csi_buy_zocl* Send);
        CS_RCODE _buybygold_buy_single_item_additional_process(struct CPlayer* pOne, struct _request_csi_buy_clzo::__item* pSrc, struct _param_cashitem_dblog* pSheet, struct _result_csi_buy_zocl* Send);
        unsigned int _buybygold_buy_single_item_calc_price(struct CPlayer* pOne, struct _request_csi_buy_clzo* pRecv, struct _request_csi_buy_clzo::__item* pSrc, struct _param_cashitem_dblog* pSheet, struct _CashShop_fld* pCsFld, bool* bCouponUseCheck, struct _result_csi_buy_zocl* Send, unsigned int* dwDiscount);
        unsigned int _buybygold_buy_single_item_calc_price_coupon(struct CPlayer* pOne, struct _request_csi_buy_clzo* pRecv, char byOverlapNum, int nCsPrice, bool* bCouponUseCheck, unsigned int* dwDiscount);
        unsigned int _buybygold_buy_single_item_calc_price_discount(struct _CashShop_fld* pCsFld, char byOverlapNum);
        unsigned int _buybygold_buy_single_item_calc_price_limitsale(int nCsPrice, char byOverlapNum);
        unsigned int _buybygold_buy_single_item_calc_price_one_n_one(char bySetKind, int nCsPrice, char byOverlapNum);
        CS_RCODE _buybygold_buy_single_item_check_item(struct CPlayer* pOne, struct _request_csi_buy_clzo::__item* pSrc, struct _param_cashitem_dblog* pSheet, struct _CashShop_fld** pCsFld);
        CS_RCODE _buybygold_buy_single_item_give_item(struct CPlayer* pOne, struct _request_csi_buy_clzo::__item* pSrc, struct _STORAGE_LIST::_db_con* GiveItem);
        void _buybygold_buy_single_item_proc_complete(struct CPlayer* pOne, struct _request_csi_buy_clzo::__item* pSrc, struct _param_cashitem_dblog* pSheet, struct _CashShop_fld* pCsFld, struct _STORAGE_LIST::_db_con* GiveItem, struct _result_csi_buy_zocl* Send, unsigned int dwPrice, unsigned int dwDiscountRate, bool* bCouponUseCheck, bool* bCouponUse);
        CS_RCODE _buybygold_buy_single_item_proc_price(struct CPlayer* pOne, struct _request_csi_buy_clzo* pRecv, struct _request_csi_buy_clzo::__item* pSrc, struct _param_cashitem_dblog* pSheet, struct _CashShop_fld* pCsFld, bool* bCouponUseCheck, struct _result_csi_buy_zocl* Send, unsigned int* dwPrice, unsigned int* dwDiscountRate);
        void _buybygold_buy_single_item_setbuydblog(struct _param_cashitem_dblog* pSheet, struct _STORAGE_LIST::_db_con* GiveItem, unsigned int dwPrice, unsigned int dwDiscountRate);
        void _buybygold_buy_single_item_setsenddata(struct _STORAGE_LIST::_db_con* GiveItem, struct _result_csi_buy_zocl* Send);
        CS_RCODE _buybygold_check_coupon(struct CPlayer* pOne, struct _request_csi_buy_clzo* pRecv, struct _param_cashitem_dblog* pSheet);
        CS_RCODE _buybygold_check_valid(struct CPlayer* pOne, struct _request_csi_buy_clzo* pRecv, struct _param_cashitem_dblog* pSheet);
        void _buybygold_complete(struct CPlayer* pOne, struct _result_csi_buy_zocl* Send, struct _request_csi_buy_clzo* pRecv, struct _request_csi_buy_clzo::__item* pSrc, struct _param_cashitem_dblog* pSheet, bool bCouponUse);
        void _buybygold_set_cashitem_dblog_sheet(struct CPlayer* pOne, struct _param_cashitem_dblog* pSheet);
        CS_RCODE _check_buyitem(char byRaceSex, struct _request_csi_buy_clzo::__item* pCsItem, struct _CashShop_fld* pFld);
        void check_cash_discount_ini();
        void check_cash_discount_status();
        void check_loaded_cde_status();
        void force_endup_cash_discount_event();
        char get_cde_status();
        void inform_cashdiscount_event(uint16_t widx);
        void inform_cashdiscount_status_all(char byType, struct _cash_discount_ini_* pIni);
        bool isConEventTime();
        bool is_cde_time();
        void load_cash_discount_event();
        void load_cde_ini(struct _cash_discount_ini_* pIni, struct _FILETIME* pft);
        void load_con_event_ini(struct _con_event_ini* pIni, struct _FILETIME* pft);
        void log_about_cash_event(char* szLoadInfo, struct _cash_discount_ini_* pIni);
        void loop_cash_discount_event();
        void set_cde_status(char byStatus);
        bool start_cashevent(int iBegin_TT, int iB30_TT, int iB5_TT, int iEnd_TT, char byEventType);
        bool start_cde(int iBegin_TT, int iB30_TT, int iB5_TT, int iEnd_TT);
        bool start_conevent(int iBegin_TT, int iEnd_TT, char byEventType);
        void update_ini(struct _cash_discount_ini_* pNewIni);
        ~CashItemRemoteStore();
        void dtor_CashItemRemoteStore();
    };
END_ATF_NAMESPACE

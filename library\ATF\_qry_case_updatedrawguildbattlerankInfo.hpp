// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_updatedrawguildbattlerank.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _qry_case_updatedrawguildbattleranksize2_ptr = int (WINAPIV*)(struct _qry_case_updatedrawguildbattlerank*);
        using _qry_case_updatedrawguildbattleranksize2_clbk = int (WINAPIV*)(struct _qry_case_updatedrawguildbattlerank*, _qry_case_updatedrawguildbattleranksize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

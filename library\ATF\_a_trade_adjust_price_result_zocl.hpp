// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _a_trade_adjust_price_result_zocl
    {
        char byRetCode;
        char byTaxRate;
         unsigned int dwLeftDalant;
         unsigned int dwTax;
        unsigned __int16 wItemSerial;
        unsigned int dwNewPrice;
        unsigned int dwRegistSerial;
    public:
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_a_trade_adjust_price_result_zocl, 20>(), "_a_trade_adjust_price_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAsyncLogBuffer.hpp>
#include <CLogFile.hpp>
#include <CNetIndexList.hpp>


START_ATF_NAMESPACE
    struct CAsyncLogBufferList
    {
        unsigned int m_uiMaxBufferCnt;
        unsigned int m_uiMaxBufferSize;
        CAsyncLogBuffer *m_pLogBuffer;
        CNetIndexList m_klistEmpty;
        CNetIndexList m_klistProc;
    public:
        CAsyncLogBufferList();
        void ctor_CAsyncLogBufferList();
        unsigned int GetBufferSize();
        int GetEmptySize();
        int GetProcCount();
        bool Init(unsigned int uiMaxBufferCnt, unsigned int uiMaxBufferSize, struct CLogFile* logLoading);
        bool Log(char* pszFileName, char* szLog, int iLen);
        void ProcWrite();
        static void WriteFile(char* pszFileName, int nLen, char* pszData);
        ~CAsyncLogBufferList();
        void dtor_CAsyncLogBufferList();
    };
END_ATF_NAMESPACE

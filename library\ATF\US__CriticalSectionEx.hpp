// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RTL_CRITICAL_SECTION.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        #pragma pack(push, 8)
        struct CriticalSectionEx
        {
            _RTL_CRITICAL_SECTION m_CS_;
            bool m_bLock;
        };
        #pragma pack(pop)
    }; // end namespace US
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $4C0EC2258454B893CE739DAE89D8DB7B
    {
        BYTE gap0[8];
        long double dblVal;
    };    
    static_assert(ATF::checkSize<$4C0EC2258454B893CE739DAE89D8DB7B, 16>(), "$4C0EC2258454B893CE739DAE89D8DB7B");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryRU.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryRUctor_CNationSettingFactoryRU2_ptr = void (WINAPIV*)(struct CNationSettingFactoryRU*);
        using CNationSettingFactoryRUctor_CNationSettingFactoryRU2_clbk = void (WINAPIV*)(struct CNationSettingFactoryRU*, CNationSettingFactoryRUctor_CNationSettingFactoryRU2_ptr);
        using CNationSettingFactoryRUCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryRU*, int, char*, bool);
        using CNationSettingFactoryRUCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryRU*, int, char*, bool, CNationSettingFactoryRUCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

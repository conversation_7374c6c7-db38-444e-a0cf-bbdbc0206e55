// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CHolyScheduleData
    {
        enum HS_SCHEDULE_SET_DATA
        {
            HSS_SCENE_CHANGE_TERM = 0x0,
            HSS_SCENE_BATTLE_TERM = 0x1,
            HSS_SCENE_ATTACKABLE_KEEPER_TERM = 0x2,
            HSS_SCENE_DESATTACKABLE_KEEPER_TERM = 0x3,
            HSS_SCENE_CHAOS_KEEPER_TERM = 0x4,
            HSS_SCENE_BASE_MINIE_TERM = 0x5,
            HSS_SCENE_TOUCH_DOWN_ADD_TERM = 0x6,
            HSS_TERM_MAX = 0x7,
        };
        struct __HolyScheduleNode
        {
            int m_nSceneTime[7];
        };
        bool m_bSet;
        __HolyScheduleNode *m_pSchedule;
        int m_nTotalSchedule;
    public:
        CHolyScheduleData();
        void ctor_CHolyScheduleData();
        struct __HolyScheduleNode* GetIndex(int nNumOfTime);
        unsigned int GetTotalSceduleTerm(int nNumOfTime);
        void Init();
        ~CHolyScheduleData();
        void dtor_CHolyScheduleData();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CHolyScheduleData, 24>(), "CHolyScheduleData");
END_ATF_NAMESPACE

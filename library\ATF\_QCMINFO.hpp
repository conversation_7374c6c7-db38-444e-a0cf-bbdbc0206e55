// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HMENU__.hpp>
#include <_QCMINFO_IDMAP.hpp>


START_ATF_NAMESPACE
    struct _QCMINFO
    {
        HMENU__ *hmenu;
        unsigned int indexMenu;
        unsigned int idCmdFirst;
        unsigned int idCmdLast;
        _QCMINFO_IDMAP *pIdMap;
    };
END_ATF_NAMESPACE

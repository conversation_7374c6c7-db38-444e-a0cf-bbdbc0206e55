// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMHDR.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagNMREBARCHEVRON
    {
        tagNMHDR hdr;
        unsigned int uBand;
        unsigned int wID;
        __int64 lParam;
        tagRECT rc;
        __int64 lParamNM;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMapData.hpp>
#include <US__CArrayEx.hpp>
#include <CLuaLooting_Novus_Item.hpp>


START_ATF_NAMESPACE
    struct CLuaLootingMgr
    {
        US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> m_Loot_Novus_ItemArEx;
        unsigned int m_dwLoopLootingCount;
        unsigned int m_dwLoopLootingTerm;
        unsigned int m_dwAddNodeCount;
        unsigned int m_dwNextLootingTime;
    public:
        bool AddNovusItem(char* strItemCode, struct CMapData* pMap, uint16_t wLayerIndex, float* fPos, uint16_t wLootRange, unsigned int dwOverlapCnt, unsigned int dwItemNum, char byCreateType);
        CLuaLootingMgr();
        void ctor_CLuaLootingMgr();
        static void Destroy();
        bool InitSDM(unsigned int dwLoopLootingCount, unsigned int dwLoopLootingTerm);
        static struct CLuaLootingMgr* Instance();
        void Loop();
        ~CLuaLootingMgr();
        void dtor_CLuaLootingMgr();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _D3DDEVICE_CREATION_PARAMETERS
    {
        unsigned int AdapterOrdinal;
        _D3DDEVTYPE DeviceType;
        HWND__ *hFocusWindow;
        unsigned int BehaviorFlags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

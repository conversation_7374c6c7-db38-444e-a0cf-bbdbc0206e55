// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PROCESSOR_POWER_POLICY_INFO.hpp>


START_ATF_NAMESPACE
    struct _PROCESSOR_POWER_POLICY
    {
        unsigned int Revision;
        char DynamicThrottle;
        char Spare[3];
        unsigned __int32 DisableCStates : 1;
        unsigned __int32 Reserved : 31;
        unsigned int PolicyCount;
        _PROCESSOR_POWER_POLICY_INFO Policy[3];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_money_supply_gatering_inform_zowb.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _money_supply_gatering_inform_zowbinit2_ptr = void (WINAPIV*)(struct _money_supply_gatering_inform_zowb*);
        using _money_supply_gatering_inform_zowbinit2_clbk = void (WINAPIV*)(struct _money_supply_gatering_inform_zowb*, _money_supply_gatering_inform_zowbinit2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

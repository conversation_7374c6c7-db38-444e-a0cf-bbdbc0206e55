// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$4142B449B169FE94E5953B2879308043.hpp>


START_ATF_NAMESPACE
    struct _PRINTER_NOTIFY_INFO_DATA
    {
        unsigned __int16 Type;
        unsigned __int16 Field;
        unsigned int Reserved;
        unsigned int Id;
        $4142B449B169FE94E5953B2879308043 NotifyData;
    };
END_ATF_NAMESPACE

#include <_qry_case_post_list_regi.hpp>


START_ATF_NAMESPACE
    _qry_case_post_list_regi::_qry_case_post_list_regi()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_post_list_regi*);
        (org_ptr(0x140328150L))(this);
    };
    void _qry_case_post_list_regi::ctor__qry_case_post_list_regi()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_post_list_regi*);
        (org_ptr(0x140328150L))(this);
    };
    int _qry_case_post_list_regi::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_post_list_regi*);
        return (org_ptr(0x1403281d0L))(this);
    };
    _qry_case_post_list_regi::__list::__list()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_post_list_regi::__list*);
        (org_ptr(0x1403281e0L))(this);
    };
    void _qry_case_post_list_regi::__list::ctor___list()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_post_list_regi::__list*);
        (org_ptr(0x1403281e0L))(this);
    };
END_ATF_NAMESPACE

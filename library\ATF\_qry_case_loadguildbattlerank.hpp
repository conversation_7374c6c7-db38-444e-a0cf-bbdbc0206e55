// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_loadguildbattlerank
    {
        char byRace;
        char byLoadDataStartPosition;
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_qry_case_loadguildbattlerank, 2>(), "_qry_case_loadguildbattlerank");
END_ATF_NAMESPACE

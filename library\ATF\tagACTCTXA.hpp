// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>


START_ATF_NAMESPACE
    struct tagACTCTXA
    {
        unsigned int cbSize;
        unsigned int dwFlags;
        const char *lpSource;
        unsigned __int16 wProcessorArchitecture;
        unsigned __int16 wLangId;
        const char *lpAssemblyDirectory;
        const char *lpResourceName;
        const char *lpApplicationName;
        HINSTANCE__ *hModule;
    };
END_ATF_NAMESPACE

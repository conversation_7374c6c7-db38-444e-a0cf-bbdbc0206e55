// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_nuclear_bomb_current_state_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _nuclear_bomb_current_state_zoclsize2_ptr = int (WINAPIV*)(struct _nuclear_bomb_current_state_zocl*);
        using _nuclear_bomb_current_state_zoclsize2_clbk = int (WINAPIV*)(struct _nuclear_bomb_current_state_zocl*, _nuclear_bomb_current_state_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

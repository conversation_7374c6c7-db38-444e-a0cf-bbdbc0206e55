// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerKR.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerKRctor_CCashDbWorkerKR2_ptr = void (WINAPIV*)(struct CCashDbWorkerKR*);
        using CCashDbWorkerKRctor_CCashDbWorkerKR2_clbk = void (WINAPIV*)(struct CCashDbWorkerKR*, CCashDbWorkerKRctor_CCashDbWorkerKR2_ptr);
        
        using CCashDbWorkerKRdtor_CCashDbWorkerKR7_ptr = void (WINAPIV*)(struct CCashDbWorkerKR*);
        using CCashDbWorkerKRdtor_CCashDbWorkerKR7_clbk = void (WINAPIV*)(struct CCashDbWorkerKR*, CCashDbWorkerKRdtor_CCashDbWorkerKR7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

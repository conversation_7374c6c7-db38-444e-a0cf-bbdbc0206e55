// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _sell_store_result_zocl
    {
        char bSucc;
        unsigned int dwLeftDalant;
        unsigned int dwLeftGold;
        unsigned int dwProfitDanlant;
        unsigned int dwProfitGold;
        char byDiscountRate;
        char byRetCode;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _NDR_CS_SIZE_CONVERT_ROUTINES
    {
        void (WINAPIV *pfnNetSize)(void *, unsigned int, unsigned int, _IDL_CS_CONVERT *, unsigned int *, unsigned int *);
        void (WINAPIV *pfnToNetCs)(void *, unsigned int, void *, unsigned int, char *, unsigned int *, unsigned int *);
        void (WINAPIV *pfnLocalSize)(void *, unsigned int, unsigned int, _IDL_CS_CONVERT *, unsigned int *, unsigned int *);
        void (WINAPIV *pfnFromNetCs)(void *, unsigned int, char *, unsigned int, unsigned int, void *, unsigned int *, unsigned int *);
    };
END_ATF_NAMESPACE

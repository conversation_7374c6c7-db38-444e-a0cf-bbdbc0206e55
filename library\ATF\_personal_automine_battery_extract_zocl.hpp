// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _personal_automine_battery_extract_zocl
    {
        unsigned int dwObjSerial;
        unsigned __int16 wSerial;
        unsigned int dwDur;
        char byRetCode;
    public:
        _personal_automine_battery_extract_zocl();
        void ctor__personal_automine_battery_extract_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_personal_automine_battery_extract_zocl, 11>(), "_personal_automine_battery_extract_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleLogger
        {
            CLogFile *m_pkLogger;
        public:
            CGuildBattleLogger();
            void ctor_CGuildBattleLogger();
            void CreateLogFile(char* szLogName);
            static void Destroy();
            bool Init();
            static struct CGuildBattleLogger* Instance();
            void Log(char* fmt);
            void Log(wchar_t* fmt);
            ~CGuildBattleLogger();
            void dtor_CGuildBattleLogger();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

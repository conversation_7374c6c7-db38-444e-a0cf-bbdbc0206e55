// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PRINTER_NOTIFY_INFO_DATA.hpp>


START_ATF_NAMESPACE
    struct _PRINTER_NOTIFY_INFO
    {
        unsigned int Version;
        unsigned int Flags;
        unsigned int Count;
        _PRINTER_NOTIFY_INFO_DATA aData[1];
    };
END_ATF_NAMESPACE

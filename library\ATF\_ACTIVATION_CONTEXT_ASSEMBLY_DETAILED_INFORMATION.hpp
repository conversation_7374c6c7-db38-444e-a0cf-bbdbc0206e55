// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _ACTIVATION_CONTEXT_ASSEMBLY_DETAILED_INFORMATION
    {
        unsigned int ulFlags;
        unsigned int ulEncodedAssemblyIdentityLength;
        unsigned int ulManifestPathType;
        unsigned int ulManifestPathLength;
        _LARGE_INTEGER liManifestLastWriteTime;
        unsigned int ulPolicyPathType;
        unsigned int ulPolicyPathLength;
        _LARGE_INTEGER liPolicyLastWriteTime;
        unsigned int ulMetadataSatelliteRosterIndex;
        unsigned int ulManifestVersionMajor;
        unsigned int ulManifestVersionMinor;
        unsigned int ulPolicyVersionMajor;
        unsigned int ulPolicyVersionMinor;
        unsigned int ulAssemblyDirectoryNameLength;
        const wchar_t *lpAssemblyEncodedAssemblyIdentity;
        const wchar_t *lpAssemblyManifestPath;
        const wchar_t *lpAssemblyPolicyPath;
        const wchar_t *lpAssemblyDirectoryName;
        unsigned int ulFileCount;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

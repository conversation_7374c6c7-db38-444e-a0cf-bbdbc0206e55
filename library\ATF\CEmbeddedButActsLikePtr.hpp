// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPtrList.hpp>


START_ATF_NAMESPACE
    template<>
    struct CEmbeddedButActsLikePtr<CPtrList>
    {
        CPtrList m_data;
    };
END_ATF_NAMESPACE
#include <CMapPtrToPtr.hpp>


START_ATF_NAMESPACE
    template<>
    struct CEmbeddedButActsLikePtr<CMapPtrToPtr>
    {
        CMapPtrToPtr m_data;
    };
END_ATF_NAMESPACE

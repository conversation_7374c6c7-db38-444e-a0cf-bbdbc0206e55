// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <LtdWriter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using LtdWriterInitLogDB2_ptr = bool (WINAPIV*)(struct LtdWriter*, char*, char*);
        using LtdWriterInitLogDB2_clbk = bool (WINAPIV*)(struct LtdWriter*, char*, char*, LtdWriterInitLogDB2_ptr);
        
        using LtdWriterctor_LtdWriter4_ptr = void (WINAPIV*)(struct LtdWriter*);
        using LtdWriterctor_LtdWriter4_clbk = void (WINAPIV*)(struct LtdWriter*, LtdWriterctor_LtdWriter4_ptr);
        using LtdWriterPtrInstance6_ptr = struct LtdWriter* (WINAPIV*)();
        using LtdWriterPtrInstance6_clbk = struct LtdWriter* (WINAPIV*)(LtdWriterPtrInstance6_ptr);
        using LtdWriterPushLog8_ptr = void (WINAPIV*)(struct LtdWriter*, char*, struct _LTD_PARAM*);
        using LtdWriterPushLog8_clbk = void (WINAPIV*)(struct LtdWriter*, char*, struct _LTD_PARAM*, LtdWriterPushLog8_ptr);
        using LtdWriterRelease10_ptr = void (WINAPIV*)(struct LtdWriter*);
        using LtdWriterRelease10_clbk = void (WINAPIV*)(struct LtdWriter*, LtdWriterRelease10_ptr);
        using LtdWriter_CreateTable12_ptr = void (WINAPIV*)(struct LtdWriter*);
        using LtdWriter_CreateTable12_clbk = void (WINAPIV*)(struct LtdWriter*, LtdWriter_CreateTable12_ptr);
        using LtdWriter_GetLocalDate14_ptr = int (WINAPIV*)(struct LtdWriter*);
        using LtdWriter_GetLocalDate14_clbk = int (WINAPIV*)(struct LtdWriter*, LtdWriter_GetLocalDate14_ptr);
        using LtdWriter_PushItemCut16_ptr = void (WINAPIV*)(struct LtdWriter*, char, struct _LTD_PARAM*, struct _LTD*);
        using LtdWriter_PushItemCut16_clbk = void (WINAPIV*)(struct LtdWriter*, char, struct _LTD_PARAM*, struct _LTD*, LtdWriter_PushItemCut16_ptr);
        using LtdWriter_PushItemMove18_ptr = void (WINAPIV*)(struct LtdWriter*, char, struct _LTD_PARAM*, struct _LTD*);
        using LtdWriter_PushItemMove18_clbk = void (WINAPIV*)(struct LtdWriter*, char, struct _LTD_PARAM*, struct _LTD*, LtdWriter_PushItemMove18_ptr);
        using LtdWriter_SetExpend20_ptr = void (WINAPIV*)(struct LtdWriter*, char*, struct _LTD_EXPEND*);
        using LtdWriter_SetExpend20_clbk = void (WINAPIV*)(struct LtdWriter*, char*, struct _LTD_EXPEND*, LtdWriter_SetExpend20_ptr);
        using LtdWriter_SetItemInfo22_ptr = void (WINAPIV*)(struct LtdWriter*, char, struct _STORAGE_LIST::_db_con*, char, struct _LTD_ITEMINFO*, int);
        using LtdWriter_SetItemInfo22_clbk = void (WINAPIV*)(struct LtdWriter*, char, struct _STORAGE_LIST::_db_con*, char, struct _LTD_ITEMINFO*, int, LtdWriter_SetItemInfo22_ptr);
        using LtdWriter_SetLtd24_ptr = void (WINAPIV*)(struct LtdWriter*, struct CUserDB*, struct _LTD*, bool, bool);
        using LtdWriter_SetLtd24_clbk = void (WINAPIV*)(struct LtdWriter*, struct CUserDB*, struct _LTD*, bool, bool, LtdWriter_SetLtd24_ptr);
        using LtdWriter_WriteDB26_ptr = void (WINAPIV*)(struct LtdWriter*, unsigned int);
        using LtdWriter_WriteDB26_clbk = void (WINAPIV*)(struct LtdWriter*, unsigned int, LtdWriter_WriteDB26_ptr);
        using LtdWriters_Working30_ptr = void (WINAPIV*)(void*);
        using LtdWriters_Working30_clbk = void (WINAPIV*)(void*, LtdWriters_Working30_ptr);
        using LtdWriterstart32_ptr = bool (WINAPIV*)(struct LtdWriter*);
        using LtdWriterstart32_clbk = bool (WINAPIV*)(struct LtdWriter*, LtdWriterstart32_ptr);
        using LtdWriterstop34_ptr = void (WINAPIV*)(struct LtdWriter*);
        using LtdWriterstop34_clbk = void (WINAPIV*)(struct LtdWriter*, LtdWriterstop34_ptr);
        
        using LtdWriterdtor_LtdWriter36_ptr = void (WINAPIV*)(struct LtdWriter*);
        using LtdWriterdtor_LtdWriter36_clbk = void (WINAPIV*)(struct LtdWriter*, LtdWriterdtor_LtdWriter36_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

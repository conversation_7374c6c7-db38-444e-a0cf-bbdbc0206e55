// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _guild_battle_suggest_matter
    {
        enum STATE
        {
            WAIT_BATTLE_REQUEST = 0x0,
            BATTLE_REQUEST_SUGGEST = 0x1,
            COMPLETE_REQUEST = 0x2,
            APPLY_BATTLE_REQUEST_SUGGEST = 0x3,
            COMPLETE_BATTLE_REQUEST = 0x4,
            STATE_MAX = 0x5,
        };
        STATE eState;
        struct CGuild *pkSrc;
        struct CGuild *pkDest;
        unsigned int dwStartTime;
        unsigned int dwNumber;
        unsigned int dwMapIdx;
    public:
        void CancelSuggestedMatter();
        void Clear();
        bool IsCompleteBattle();
        _guild_battle_suggest_matter();
        void ctor__guild_battle_suggest_matter();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

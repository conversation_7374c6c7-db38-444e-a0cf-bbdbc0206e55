// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_guild_battle_reserved_schedule_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CReservedGuildSchedulePage
        {
            unsigned int m_dw1PGuildSerial[5];
            unsigned int m_dw2PGuildSerial[5];
            _guild_battle_reserved_schedule_result_zocl *m_pkList;
            char m_ucPageInx;
            unsigned int m_dwVer;
        public:
            CReservedGuildSchedulePage();
            void ctor_CReservedGuildSchedulePage();
            bool Clear();
            bool Find(unsigned int dwGuildSerial);
            void Flip();
            void IncVer();
            bool Init(char ucPageInx);
            void Send(int n, unsigned int dwVer, struct CReservedGuildSchedulePage* pkSelfInfoPage);
            ~CReservedGuildSchedulePage();
            void dtor_CReservedGuildSchedulePage();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

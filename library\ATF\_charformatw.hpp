// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _charformatw
    {
        unsigned int cbSize;
        unsigned int dwMask;
        unsigned int dwEffects;
        int yHeight;
        int yOffset;
        unsigned int crTextColor;
        char bCharSet;
        char bPitchAndFamily;
        wchar_t szFaceName[32];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    struct _JOBOBJECT_BASIC_LIMIT_INFORMATION
    {
        _LARGE_INTEGER PerProcessUserTimeLimit;
        _LARGE_INTEGER PerJobUserTimeLimit;
        unsigned int LimitFlags;
        unsigned __int64 MinimumWorkingSetSize;
        unsigned __int64 MaximumWorkingSetSize;
        unsigned int ActiveProcessLimit;
        unsigned __int64 Affinity;
        unsigned int PriorityClass;
        unsigned int SchedulingClass;
    };
END_ATF_NAMESPACE

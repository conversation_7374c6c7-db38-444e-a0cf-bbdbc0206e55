// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderClassInfoTableType.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderClassInfoTableTypector_CUnmannedTraderClassInfoTableType2_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, unsigned int);
        using CUnmannedTraderClassInfoTableTypector_CUnmannedTraderClassInfoTableType2_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, unsigned int, CUnmannedTraderClassInfoTableTypector_CUnmannedTraderClassInfoTableType2_ptr);
        using CUnmannedTraderClassInfoTableTypeCleanUp4_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*);
        using CUnmannedTraderClassInfoTableTypeCleanUp4_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, CUnmannedTraderClassInfoTableTypeCleanUp4_ptr);
        using CUnmannedTraderClassInfoTableTypeCreate6_ptr = struct CUnmannedTraderClassInfo* (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, unsigned int);
        using CUnmannedTraderClassInfoTableTypeCreate6_clbk = struct CUnmannedTraderClassInfo* (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, unsigned int, CUnmannedTraderClassInfoTableTypeCreate6_ptr);
        using CUnmannedTraderClassInfoTableTypeGetGroupID8_ptr = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, char, uint16_t, char*);
        using CUnmannedTraderClassInfoTableTypeGetGroupID8_clbk = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, char, uint16_t, char*, CUnmannedTraderClassInfoTableTypeGetGroupID8_ptr);
        using CUnmannedTraderClassInfoTableTypeGetGroupID10_ptr = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, char, uint16_t, char*, char*);
        using CUnmannedTraderClassInfoTableTypeGetGroupID10_clbk = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, char, uint16_t, char*, char*, CUnmannedTraderClassInfoTableTypeGetGroupID10_ptr);
        using CUnmannedTraderClassInfoTableTypeIsExistGroupID12_ptr = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, char, char);
        using CUnmannedTraderClassInfoTableTypeIsExistGroupID12_clbk = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, char, char, CUnmannedTraderClassInfoTableTypeIsExistGroupID12_ptr);
        using CUnmannedTraderClassInfoTableTypeIsValidID14_ptr = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, unsigned int);
        using CUnmannedTraderClassInfoTableTypeIsValidID14_clbk = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, unsigned int, CUnmannedTraderClassInfoTableTypeIsValidID14_ptr);
        using CUnmannedTraderClassInfoTableTypeLoadXML16_ptr = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, struct TiXmlElement*, struct CLogFile*, unsigned int);
        using CUnmannedTraderClassInfoTableTypeLoadXML16_clbk = bool (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, struct TiXmlElement*, struct CLogFile*, unsigned int, CUnmannedTraderClassInfoTableTypeLoadXML16_ptr);
        
        using CUnmannedTraderClassInfoTableTypedtor_CUnmannedTraderClassInfoTableType20_ptr = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*);
        using CUnmannedTraderClassInfoTableTypedtor_CUnmannedTraderClassInfoTableType20_clbk = void (WINAPIV*)(struct CUnmannedTraderClassInfoTableType*, CUnmannedTraderClassInfoTableTypedtor_CUnmannedTraderClassInfoTableType20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

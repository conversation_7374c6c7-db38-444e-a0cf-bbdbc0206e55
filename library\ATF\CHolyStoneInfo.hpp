// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHolyStone.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CHolyStoneAutoRecover2_ptr = void (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneAutoRecover2_clbk = void (WINAPIV*)(struct CHolyStone*, CHolyStoneAutoRecover2_ptr);
        
        using CHolyStonector_CHolyStone4_ptr = void (WINAPIV*)(struct CHolyStone*);
        using CHolyStonector_CHolyStone4_clbk = void (WINAPIV*)(struct CHolyStone*, CHolyStonector_CHolyStone4_ptr);
        using CHolyStoneCalcCurHPRate6_ptr = uint16_t (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneCalcCurHPRate6_clbk = uint16_t (WINAPIV*)(struct CHolyStone*, CHolyStoneCalcCurHPRate6_ptr);
        using CHolyStoneCreate8_ptr = bool (WINAPIV*)(struct CHolyStone*, struct _stone_create_setdata*);
        using CHolyStoneCreate8_clbk = bool (WINAPIV*)(struct CHolyStone*, struct _stone_create_setdata*, CHolyStoneCreate8_ptr);
        using CHolyStoneDestroy10_ptr = bool (WINAPIV*)(struct CHolyStone*, char, struct CCharacter*);
        using CHolyStoneDestroy10_clbk = bool (WINAPIV*)(struct CHolyStone*, char, struct CCharacter*, CHolyStoneDestroy10_ptr);
        using CHolyStoneDropItem12_ptr = void (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneDropItem12_clbk = void (WINAPIV*)(struct CHolyStone*, CHolyStoneDropItem12_ptr);
        using CHolyStoneGetAddCountWithPlayer14_ptr = uint16_t (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetAddCountWithPlayer14_clbk = uint16_t (WINAPIV*)(struct CHolyStone*, CHolyStoneGetAddCountWithPlayer14_ptr);
        using CHolyStoneGetAttackDP16_ptr = int (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetAttackDP16_clbk = int (WINAPIV*)(struct CHolyStone*, CHolyStoneGetAttackDP16_ptr);
        using CHolyStoneGetDefFC18_ptr = int (WINAPIV*)(struct CHolyStone*, int, struct CCharacter*, int*);
        using CHolyStoneGetDefFC18_clbk = int (WINAPIV*)(struct CHolyStone*, int, struct CCharacter*, int*, CHolyStoneGetDefFC18_ptr);
        using CHolyStoneGetDefFacing20_ptr = float (WINAPIV*)(struct CHolyStone*, int);
        using CHolyStoneGetDefFacing20_clbk = float (WINAPIV*)(struct CHolyStone*, int, CHolyStoneGetDefFacing20_ptr);
        using CHolyStoneGetDefGap22_ptr = float (WINAPIV*)(struct CHolyStone*, int);
        using CHolyStoneGetDefGap22_clbk = float (WINAPIV*)(struct CHolyStone*, int, CHolyStoneGetDefGap22_ptr);
        using CHolyStoneGetDefSkill24_ptr = int (WINAPIV*)(struct CHolyStone*, bool);
        using CHolyStoneGetDefSkill24_clbk = int (WINAPIV*)(struct CHolyStone*, bool, CHolyStoneGetDefSkill24_ptr);
        using CHolyStoneGetFireTol26_ptr = int (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetFireTol26_clbk = int (WINAPIV*)(struct CHolyStone*, CHolyStoneGetFireTol26_ptr);
        using CHolyStoneGetHP28_ptr = int (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetHP28_clbk = int (WINAPIV*)(struct CHolyStone*, CHolyStoneGetHP28_ptr);
        using CHolyStoneGetLevel30_ptr = int (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetLevel30_clbk = int (WINAPIV*)(struct CHolyStone*, CHolyStoneGetLevel30_ptr);
        using CHolyStoneGetMaxHP32_ptr = int (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetMaxHP32_clbk = int (WINAPIV*)(struct CHolyStone*, CHolyStoneGetMaxHP32_ptr);
        using CHolyStoneGetNewStoneSerial34_ptr = unsigned int (WINAPIV*)();
        using CHolyStoneGetNewStoneSerial34_clbk = unsigned int (WINAPIV*)(CHolyStoneGetNewStoneSerial34_ptr);
        using CHolyStoneGetObjName36_ptr = char* (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetObjName36_clbk = char* (WINAPIV*)(struct CHolyStone*, CHolyStoneGetObjName36_ptr);
        using CHolyStoneGetObjRace38_ptr = int (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetObjRace38_clbk = int (WINAPIV*)(struct CHolyStone*, CHolyStoneGetObjRace38_ptr);
        using CHolyStoneGetSoilTol40_ptr = int (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetSoilTol40_clbk = int (WINAPIV*)(struct CHolyStone*, CHolyStoneGetSoilTol40_ptr);
        using CHolyStoneGetWaterTol42_ptr = int (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetWaterTol42_clbk = int (WINAPIV*)(struct CHolyStone*, CHolyStoneGetWaterTol42_ptr);
        using CHolyStoneGetWeaponAdjust44_ptr = float (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetWeaponAdjust44_clbk = float (WINAPIV*)(struct CHolyStone*, CHolyStoneGetWeaponAdjust44_ptr);
        using CHolyStoneGetWidth46_ptr = float (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetWidth46_clbk = float (WINAPIV*)(struct CHolyStone*, CHolyStoneGetWidth46_ptr);
        using CHolyStoneGetWindTol48_ptr = int (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneGetWindTol48_clbk = int (WINAPIV*)(struct CHolyStone*, CHolyStoneGetWindTol48_ptr);
        using CHolyStoneInit50_ptr = bool (WINAPIV*)(struct CHolyStone*, struct _object_id*);
        using CHolyStoneInit50_clbk = bool (WINAPIV*)(struct CHolyStone*, struct _object_id*, CHolyStoneInit50_ptr);
        using CHolyStoneIsBeAttackedAble52_ptr = bool (WINAPIV*)(struct CHolyStone*, bool);
        using CHolyStoneIsBeAttackedAble52_clbk = bool (WINAPIV*)(struct CHolyStone*, bool, CHolyStoneIsBeAttackedAble52_ptr);
        using CHolyStoneIsBeDamagedAble54_ptr = bool (WINAPIV*)(struct CHolyStone*, struct CCharacter*);
        using CHolyStoneIsBeDamagedAble54_clbk = bool (WINAPIV*)(struct CHolyStone*, struct CCharacter*, CHolyStoneIsBeDamagedAble54_ptr);
        using CHolyStoneIsChangedHP56_ptr = bool (WINAPIV*)(struct CHolyStone*, uint16_t);
        using CHolyStoneIsChangedHP56_clbk = bool (WINAPIV*)(struct CHolyStone*, uint16_t, CHolyStoneIsChangedHP56_ptr);
        using CHolyStoneLoop58_ptr = void (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneLoop58_clbk = void (WINAPIV*)(struct CHolyStone*, CHolyStoneLoop58_ptr);
        using CHolyStoneOutOfSec60_ptr = void (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneOutOfSec60_clbk = void (WINAPIV*)(struct CHolyStone*, CHolyStoneOutOfSec60_ptr);
        using CHolyStoneSendMsg_Create62_ptr = void (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneSendMsg_Create62_clbk = void (WINAPIV*)(struct CHolyStone*, CHolyStoneSendMsg_Create62_ptr);
        using CHolyStoneSendMsg_Destroy64_ptr = void (WINAPIV*)(struct CHolyStone*, char, unsigned int);
        using CHolyStoneSendMsg_Destroy64_clbk = void (WINAPIV*)(struct CHolyStone*, char, unsigned int, CHolyStoneSendMsg_Destroy64_ptr);
        using CHolyStoneSendMsg_FixPosition66_ptr = void (WINAPIV*)(struct CHolyStone*, int);
        using CHolyStoneSendMsg_FixPosition66_clbk = void (WINAPIV*)(struct CHolyStone*, int, CHolyStoneSendMsg_FixPosition66_ptr);
        using CHolyStoneSendMsg_StoneAlterOper68_ptr = void (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneSendMsg_StoneAlterOper68_clbk = void (WINAPIV*)(struct CHolyStone*, CHolyStoneSendMsg_StoneAlterOper68_ptr);
        using CHolyStoneSetDamage70_ptr = int (WINAPIV*)(struct CHolyStone*, int, struct CCharacter*, int, bool, int, unsigned int, bool);
        using CHolyStoneSetDamage70_clbk = int (WINAPIV*)(struct CHolyStone*, int, struct CCharacter*, int, bool, int, unsigned int, bool, CHolyStoneSetDamage70_ptr);
        using CHolyStoneSetDropItem72_ptr = void (WINAPIV*)(struct CHolyStone*);
        using CHolyStoneSetDropItem72_clbk = void (WINAPIV*)(struct CHolyStone*, CHolyStoneSetDropItem72_ptr);
        using CHolyStoneSetHP74_ptr = bool (WINAPIV*)(struct CHolyStone*, int, bool);
        using CHolyStoneSetHP74_clbk = bool (WINAPIV*)(struct CHolyStone*, int, bool, CHolyStoneSetHP74_ptr);
        using CHolyStoneSetOper76_ptr = void (WINAPIV*)(struct CHolyStone*, bool, float);
        using CHolyStoneSetOper76_clbk = void (WINAPIV*)(struct CHolyStone*, bool, float, CHolyStoneSetOper76_ptr);
        
        using CHolyStonedtor_CHolyStone82_ptr = void (WINAPIV*)(struct CHolyStone*);
        using CHolyStonedtor_CHolyStone82_clbk = void (WINAPIV*)(struct CHolyStone*, CHolyStonedtor_CHolyStone82_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

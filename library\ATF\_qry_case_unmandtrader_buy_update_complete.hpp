// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_unmandtrader_buy_update_complete
    {
        struct __list
        {
            char byProcRet;
            char byProcUpdate;
            unsigned int dwSeller;
            unsigned int dwRegistSerial;
            char byUpdateState;
        };
        unsigned __int16 wInx;
        unsigned int dwBuyer;
        char byRace;
        bool bAllSuccess;
        char byType;
        char byNum;
        __list List[10];
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_unmandtrader_buy_update_complete, 172>(), "_qry_case_unmandtrader_buy_update_complete");
END_ATF_NAMESPACE

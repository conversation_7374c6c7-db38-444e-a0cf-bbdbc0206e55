// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <type_info.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using type_info_name_internal_method1_ptr = char* (WINAPIV*)(struct type_info*, struct __type_info_node*);
        using type_info_name_internal_method1_clbk = char* (WINAPIV*)(struct type_info*, struct __type_info_node*, type_info_name_internal_method1_ptr);
        
        using type_infoctor_type_info4_ptr = void (WINAPIV*)(struct type_info*, struct type_info*);
        using type_infoctor_type_info4_clbk = void (WINAPIV*)(struct type_info*, struct type_info*, type_infoctor_type_info4_ptr);
        
        using type_infodtor_type_info5_ptr = void (WINAPIV*)(void*);
        using type_infodtor_type_info5_clbk = void (WINAPIV*)(void*, type_infodtor_type_info5_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

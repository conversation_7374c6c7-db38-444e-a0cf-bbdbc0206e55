// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HDC__.hpp>
#include <tagNMHDR.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagNMCUSTOMDRAWINFO
    {
        tagNMHDR hdr;
        unsigned int dwDrawStage;
        HDC__ *hdc;
        tagRECT rc;
        unsigned __int64 dwItemSpec;
        unsigned int uItemState;
        __int64 lItemlParam;
    };
END_ATF_NAMESPACE

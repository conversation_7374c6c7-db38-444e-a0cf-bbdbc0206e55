// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagSOleTlsDataPublic
    {
        void *pvReserved0[2];
        unsigned int dwReserved0[3];
        void *pvReserved1[1];
        unsigned int dwReserved1[3];
        void *pvReserved2[4];
        unsigned int dwReserved2[1];
        void *pCurrentCtx;
    };
END_ATF_NAMESPACE

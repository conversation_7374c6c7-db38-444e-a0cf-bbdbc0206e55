// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _guild_add_join_applier_inform_zocl
    {
        unsigned int dwApplierSerial;
        char wszName[17];
        char byLv;
        unsigned int dwPvpPoint;
        unsigned int dwApplyTime;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

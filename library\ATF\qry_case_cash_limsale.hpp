// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_db_cash_limited_sale.hpp>


START_ATF_NAMESPACE
    struct qry_case_cash_limsale
    {
        _db_cash_limited_sale NewSale;
        _db_cash_limited_sale OldSale;
    public:
        qry_case_cash_limsale();
        void ctor_qry_case_cash_limsale();
        int size();
    };    
    static_assert(ATF::checkSize<qry_case_cash_limsale, 328>(), "qry_case_cash_limsale");
END_ATF_NAMESPACE

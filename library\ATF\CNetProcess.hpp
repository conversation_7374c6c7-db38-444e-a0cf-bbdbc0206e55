// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CNetCriticalSection.hpp>
#include <CNetFrameRate.hpp>
#include <CNetIndexList.hpp>
#include <CNetProcessVtbl.hpp>
#include <CNetSocket.hpp>
#include <CNetTimer.hpp>
#include <_ANSYNC_CONNECT_DATA.hpp>
#include <_FORCE_CLOSE.hpp>
#include <_KEY_CHECK_NODE.hpp>
#include <_MSG_HEADER.hpp>
#include <_NET_BUFFER.hpp>
#include <_NET_TYPE_PARAM.hpp>
#include <_thread_parameter.hpp>
#include <sockaddr_in.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CNetProcess
    {
        CNetProcessVtbl *vfptr;
        CNetSocket m_NetSocket;
        _FORCE_CLOSE m_FC;
        _NET_TYPE_PARAM m_Type;
        int m_nIndex;
        int m_nOddMsgNum;
        unsigned int m_nTryConnectCount;
        bool m_bPassablePacket[256][256];
        bool m_bUseFG;
        bool m_bSetProcess;
        int m_nEventThreadNum;
        struct CNetWorking *m_pNetwork;
        CLogFile m_LogFile[3];
        CLogFile m_LogHack;
        CNetCriticalSection *m_csRecv;
        _NET_BUFFER *m_pRecvBuffer;
        _NET_BUFFER *m_pSendBuffer;
        _thread_parameter m_pmAcceptThread;
        _thread_parameter m_pmEventThread[81];
        _thread_parameter m_pmRecvThread[8];
        _thread_parameter m_pmSendThread;
        _thread_parameter m_pmConnectThread;
        CNetIndexList m_listAcceptEvent;
        CNetIndexList m_listCloseEvent;
        CNetIndexList m_listRecvEvent;
        CNetIndexList m_listAcceptReady;
        _ANSYNC_CONNECT_DATA *m_AnsyncConnectData;
        CNetIndexList m_listAnsyncConnect;
        CNetIndexList m_listAnsyncConnectComplete;
        CNetFrameRate m_fpsSendThread;
        int m_nKeyCheckNodeNum;
        _KEY_CHECK_NODE *m_ndKeyCheck;
        unsigned int *m_dwKeyCheckBufferList;
        CNetIndexList m_listKeyCheck;
        CNetIndexList m_listKeyCheck_Empty;
        CNetTimer m_tmrListCheckerKeyCheck;
        CNetTimer m_tmrCheckResSH;
        CNetTimer m_tmrCheckKeyCT;
        CNetTimer m_tmrCheckRecvBreak;
        char *m_sTempSendBuffer;
        char *m_sTempRecvBuffer;
        unsigned int m_dwCurTime;
    public:
        static void AcceptThread(void* pv);
        CNetProcess();
        void ctor_CNetProcess();
        void CloseAll();
        void CloseSocket(unsigned int dwSocketIndex, bool bSlowClose);
        void CompleteAnsyncConnect();
        static void ConnectThread(void* pv);
        bool FindKeyFromWaitList(unsigned int dwSocketIndex, unsigned int dwSerial, unsigned int* pdwKey, int nUseKeyNum);
        void* GetContextHandle(uint16_t wIndex);
        unsigned int GetSendThreadFrame();
        void IOLogFileOperSetting(bool bOper);
        int LoadSendMsg(unsigned int dwClientIndex, char* pbyType, char* szMsg, uint16_t nLen);
        int LoadSendMsg(unsigned int dwClientIndex, uint16_t wType, char* szMsg, uint16_t nLen);
        void LogFileOperSetting(bool bRecv, bool bSend, bool bSystem);
        static void NetEventThread(void* pv);
        void OnLoop();
        void OnLoop_Receipt();
        bool PushAnsyncConnect(unsigned int dwSocketIndex, struct sockaddr_in* pAddr);
        void PushCloseNode(int nIndex);
        bool PushKeyCheckList(unsigned int dwSerial, unsigned int dwIP, unsigned int* pdwKey, int nUseKeyNum);
        static void RecvThread(void* pv);
        void Release();
        static void SendThread(void* pv);
        void SetContextHandle(void* hContextHandle, uint16_t wIndex);
        bool SetProcess(int nIndex, struct _NET_TYPE_PARAM* pType, struct CNetWorking* pNetwork, bool bUseFG);
        bool StartSpeedHackCheck(unsigned int dwClientIndex, char* pszID);
        void _CheckSend(uint16_t wSocketIndex);
        void _CheckWaitKey();
        void _CkeckKeyCertifyDeley();
        void _CkeckRecvBreak();
        void _CkeckSpeedHackDeley();
        void _ForceCloseLoop();
        bool _InternalPacketProcess(unsigned int dwSocketIndex, struct _MSG_HEADER* pMsgHeader, char* pMsg);
        void _PopRecvMsg(uint16_t wSocketIndex);
        void _Receipt();
        void _ResponSpeedHack();
        void _SendLoop(unsigned int n);
        void _SendSpeedHackCheckMsg(int n);
        bool wt_AcceptClient(unsigned int* pdwClientIndex);
        bool wt_CloseClient(unsigned int dwClientIndex);
        ~CNetProcess();
        void dtor_CNetProcess();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CNetProcess, 71000>(), "CNetProcess");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CPlayer.hpp>
#include <Cmd.hpp>
#include <ElectProcessorVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct ElectProcessor
    {
        enum ProcessorType
        {
            _eCandidateRegister = 0x0,
            _eSecondCandidateCrystallizer = 0x1,
            _eVoter = 0x2,
            _eFinalDecisionProcessor = 0x3,
            _eFinalDecisionApplyer = 0x4,
            _eClassOrderProcessor = 0x5,
            _eProcessorNum = 0x6,
            _eNonProcessor = 0xFF,
        };
        ElectProcessorVtbl *vfptr;
        bool _bEnable;
        ProcessorType _nProcesor;
        CLogFile _kSysLog;
    public:
        int Doit(Cmd eCmd, struct CPlayer* pOne, char* pdata);
        ElectProcessor(ProcessorType nProcessorType);
        void ctor_ElectProcessor(ProcessorType nProcessorType);
        ProcessorType GetProcessorType();
        bool Initialize();
        ~ElectProcessor();
        void dtor_ElectProcessor();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<ElectProcessor, 200>(), "ElectProcessor");
END_ATF_NAMESPACE

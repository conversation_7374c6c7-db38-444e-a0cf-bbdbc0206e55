// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Locbase.hpp>
#include <std___DebugHeapString.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct  locale : _Locbase<int>
        {
            struct facet
            {
                struct facetVtbl *vfptr;
                unsigned __int64 _Refs;
            };
            struct facetVtbl
            {
                void *(WINAPIV *__vecDelDtor)(struct facet *_this, unsigned int);
            };
            struct  _Locimp : facet
            {
                struct facet **_Facetvec;
                unsigned __int64 _Facetcount;
                int _Catmask;
                bool _Xparent;
                _DebugHeapString _Name;
            };
            struct id
            {
                unsigned __int64 _Id;
            };
            struct _Locimp *_Ptr;
        };
    }; // end namespace std
END_ATF_NAMESPACE

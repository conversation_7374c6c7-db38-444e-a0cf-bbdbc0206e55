// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $60BF2A49B34D8DB288EFF40F5F901799
    {
      error_get_ground_item_success = 0x0,
      error_get_ground_item_no_empty_place = 0x1,
      error_get_ground_item_not_exist_item = 0x2,
      error_get_ground_item_exist_item_max = 0x3,
      error_get_ground_item_not_exist_box_item = 0x4,
      error_get_ground_item_not_owner = 0x5,
      error_get_ground_item_distance = 0x6,
      error_get_ground_item_not_duplicate_item = 0x7,
      error_get_ground_item_unit_key = 0x8,
      error_get_ground_item_delay = 0x9,
      error_get_ground_item_stun = 0xA,
      error_get_ground_item_max_dalant = 0xB,
      error_get_ground_item_locked_item = 0xC,
      error_get_ground_item_stone_state = 0xD,
      error_get_ground_item_no_match_time_limit = 0xE,
      error_get_ground_item_system_error = 0xF,
      error_get_ground_item_timelimit_penalty_error = 0x10,
    };
END_ATF_NAMESPACE

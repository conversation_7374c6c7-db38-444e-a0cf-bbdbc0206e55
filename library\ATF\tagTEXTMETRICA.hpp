// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct tagTEXTMETRICA
    {
        int tmHeight;
        int tmAscent;
        int tmDescent;
        int tmInternalLeading;
        int tmExternalLeading;
        int tmAveCharWidth;
        int tmMaxCharWidth;
        int tmWeight;
        int tmOverhang;
        int tmDigitizedAspectX;
        int tmDigitizedAspectY;
        char tmFirstChar;
        char tmLastChar;
        char tmDefaultChar;
        char tmBreakChar;
        char tmItalic;
        char tmUnderlined;
        char tmStruckOut;
        char tmPitchAndFamily;
        char tmCharSet;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

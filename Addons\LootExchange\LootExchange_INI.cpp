#include "stdafx.h"

#include "LootExchange.h"
#include "../../Common/ETypes.h"
#include "../../Common/Helpers/RapidHelper.hpp"

#include <bitset>
#include <fstream>
#include <sstream>
#include <ATF/global.hpp>

namespace GameServer
{
    namespace Addon
    {
        // Static member definitions
        bool CLootExchange::m_bActivated = false;
        bool CLootExchange::m_bExchangeAll = false;
        int CLootExchange::m_nMinimumValue = 0;
        float CLootExchange::m_fMaxPickupDistance = 100.0f;
        bool CLootExchange::m_bEnableLogging = false;
        std::string CLootExchange::m_sLogFilePath = "./YorozuyaGS/Logs/LootExchange.log";
        std::vector<int> CLootExchange::m_vMoneyTypePriority = {0, 1, 2, 3, 4, 5, 6};
        bool CLootExchange::m_bItemTypeSettings[7] = {true, true, true, true, true, true, true};
        bool CLootExchange::m_bCurrencySettings[7] = {true, true, true, true, true, true, true};
        bool CLootExchange::m_bUseRaceSpecificPricing = true;
        int CLootExchange::m_nDefaultRace = 0;
        bool CLootExchange::m_bMultiplyByDurability = true;
        int CLootExchange::m_nExchangeRateModifier = 100;
        bool CLootExchange::m_bPremiumPlayersOnly = false;
        int CLootExchange::m_nMinimumPlayerLevel = 0;
        int CLootExchange::m_nDailyExchangeLimit = 0;
        std::string CLootExchange::m_sDailyResetTime = "00:00";

        void CLootExchange::load()
        {
            LoadConfiguration();
            enable_hook(&ATF::CPlayer::pc_TakeGroundingItem, &CLootExchange::pc_TakeGroundingItem);
        }

        void CLootExchange::unload()
        {
            cleanup_all_hook();
        }

        Yorozuya::Module::ModuleName_t CLootExchange::get_name()
        {
            static const Yorozuya::Module::ModuleName_t name = "addon.loot_exchange";
            return name;
        }

        void CLootExchange::configure(const rapidjson::Value& nodeConfig)
        {
            // This method is now empty since we load from INI file
            // Keep it for interface compatibility
        }

        void CLootExchange::LoadConfiguration()
        {
            const std::string iniPath = "./YorozuyaGS/Configuration/LootExchange.ini";
            
            // Helper function to read INI values
            auto GetPrivateProfileIntA = [](const char* section, const char* key, int defaultValue, const char* fileName) -> int {
                std::ifstream file(fileName);
                if (!file.is_open()) return defaultValue;
                
                std::string line, currentSection;
                bool inTargetSection = false;
                
                while (std::getline(file, line)) {
                    // Remove whitespace
                    line.erase(0, line.find_first_not_of(" \t"));
                    line.erase(line.find_last_not_of(" \t") + 1);
                    
                    if (line.empty() || line[0] == ';') continue;
                    
                    // Check for section
                    if (line[0] == '[' && line.back() == ']') {
                        currentSection = line.substr(1, line.length() - 2);
                        inTargetSection = (currentSection == section);
                        continue;
                    }
                    
                    if (inTargetSection) {
                        size_t pos = line.find('=');
                        if (pos != std::string::npos) {
                            std::string keyName = line.substr(0, pos);
                            std::string value = line.substr(pos + 1);
                            
                            // Remove whitespace
                            keyName.erase(keyName.find_last_not_of(" \t") + 1);
                            value.erase(0, value.find_first_not_of(" \t"));
                            
                            if (keyName == key) {
                                return std::stoi(value);
                            }
                        }
                    }
                }
                return defaultValue;
            };

            auto GetPrivateProfileStringA = [](const char* section, const char* key, const char* defaultValue, const char* fileName) -> std::string {
                std::ifstream file(fileName);
                if (!file.is_open()) return defaultValue;
                
                std::string line, currentSection;
                bool inTargetSection = false;
                
                while (std::getline(file, line)) {
                    // Remove whitespace
                    line.erase(0, line.find_first_not_of(" \t"));
                    line.erase(line.find_last_not_of(" \t") + 1);
                    
                    if (line.empty() || line[0] == ';') continue;
                    
                    // Check for section
                    if (line[0] == '[' && line.back() == ']') {
                        currentSection = line.substr(1, line.length() - 2);
                        inTargetSection = (currentSection == section);
                        continue;
                    }
                    
                    if (inTargetSection) {
                        size_t pos = line.find('=');
                        if (pos != std::string::npos) {
                            std::string keyName = line.substr(0, pos);
                            std::string value = line.substr(pos + 1);
                            
                            // Remove whitespace
                            keyName.erase(keyName.find_last_not_of(" \t") + 1);
                            value.erase(0, value.find_first_not_of(" \t"));
                            
                            if (keyName == key) {
                                return value;
                            }
                        }
                    }
                }
                return defaultValue;
            };

            // Load main settings
            m_bActivated = GetPrivateProfileIntA("LootExchange", "Activated", 0, iniPath.c_str()) != 0;
            m_bExchangeAll = GetPrivateProfileIntA("LootExchange", "ExchangeAll", 0, iniPath.c_str()) != 0;
            m_nMinimumValue = GetPrivateProfileIntA("LootExchange", "MinimumValue", 0, iniPath.c_str());
            m_fMaxPickupDistance = static_cast<float>(GetPrivateProfileIntA("LootExchange", "MaxPickupDistance", 100, iniPath.c_str()));
            m_bEnableLogging = GetPrivateProfileIntA("LootExchange", "EnableLogging", 0, iniPath.c_str()) != 0;
            m_sLogFilePath = GetPrivateProfileStringA("LootExchange", "LogFilePath", "./YorozuyaGS/Logs/LootExchange.log", iniPath.c_str());

            // Load money type priority
            std::string priorityStr = GetPrivateProfileStringA("LootExchange", "MoneyTypePriority", "0,1,2,3,4,5,6", iniPath.c_str());
            m_vMoneyTypePriority.clear();
            std::stringstream ss(priorityStr);
            std::string item;
            while (std::getline(ss, item, ',')) {
                m_vMoneyTypePriority.push_back(std::stoi(item));
            }

            // Load item type settings
            m_bItemTypeSettings[0] = GetPrivateProfileIntA("ItemTypeSettings", "ExchangeEquipment", 1, iniPath.c_str()) != 0;
            m_bItemTypeSettings[1] = GetPrivateProfileIntA("ItemTypeSettings", "ExchangeConsumables", 1, iniPath.c_str()) != 0;
            m_bItemTypeSettings[2] = GetPrivateProfileIntA("ItemTypeSettings", "ExchangeResources", 1, iniPath.c_str()) != 0;
            m_bItemTypeSettings[3] = GetPrivateProfileIntA("ItemTypeSettings", "ExchangeSpecialItems", 1, iniPath.c_str()) != 0;
            m_bItemTypeSettings[4] = GetPrivateProfileIntA("ItemTypeSettings", "ExchangeForceItems", 1, iniPath.c_str()) != 0;
            m_bItemTypeSettings[5] = GetPrivateProfileIntA("ItemTypeSettings", "ExchangeAccessories", 1, iniPath.c_str()) != 0;

            // Load currency settings
            m_bCurrencySettings[0] = GetPrivateProfileIntA("CurrencySettings", "EnableCP", 1, iniPath.c_str()) != 0;
            m_bCurrencySettings[1] = GetPrivateProfileIntA("CurrencySettings", "EnableGold", 1, iniPath.c_str()) != 0;
            m_bCurrencySettings[2] = GetPrivateProfileIntA("CurrencySettings", "EnablePvPPoint", 1, iniPath.c_str()) != 0;
            m_bCurrencySettings[3] = GetPrivateProfileIntA("CurrencySettings", "EnablePvPCashBag", 1, iniPath.c_str()) != 0;
            m_bCurrencySettings[4] = GetPrivateProfileIntA("CurrencySettings", "EnableProcessingPoint", 1, iniPath.c_str()) != 0;
            m_bCurrencySettings[5] = GetPrivateProfileIntA("CurrencySettings", "EnableHunterPoint", 1, iniPath.c_str()) != 0;
            m_bCurrencySettings[6] = GetPrivateProfileIntA("CurrencySettings", "EnableGoldPoint", 1, iniPath.c_str()) != 0;

            // Load race-specific settings
            m_bUseRaceSpecificPricing = GetPrivateProfileIntA("RaceSpecificSettings", "UseRaceSpecificPricing", 1, iniPath.c_str()) != 0;
            m_nDefaultRace = GetPrivateProfileIntA("RaceSpecificSettings", "DefaultRace", 0, iniPath.c_str());

            // Load advanced settings
            m_bMultiplyByDurability = GetPrivateProfileIntA("AdvancedSettings", "MultiplyByDurability", 1, iniPath.c_str()) != 0;
            m_nExchangeRateModifier = GetPrivateProfileIntA("AdvancedSettings", "ExchangeRateModifier", 100, iniPath.c_str());
            m_bPremiumPlayersOnly = GetPrivateProfileIntA("AdvancedSettings", "PremiumPlayersOnly", 0, iniPath.c_str()) != 0;
            m_nMinimumPlayerLevel = GetPrivateProfileIntA("AdvancedSettings", "MinimumPlayerLevel", 0, iniPath.c_str());
            m_nDailyExchangeLimit = GetPrivateProfileIntA("AdvancedSettings", "DailyExchangeLimit", 0, iniPath.c_str());
            m_sDailyResetTime = GetPrivateProfileStringA("AdvancedSettings", "DailyResetTime", "00:00", iniPath.c_str());

            if (m_bEnableLogging) {
                LogExchange("LootExchange configuration loaded from INI file");
            }
        }

        bool CLootExchange::IsItemTypeAllowed(char byTableCode)
        {
            switch ((e_code_item_table)byTableCode)
            {
            case e_code_item_table::tbl_code_upper:
            case e_code_item_table::tbl_code_lower:
            case e_code_item_table::tbl_code_gauntlet:
            case e_code_item_table::tbl_code_shoe:
            case e_code_item_table::tbl_code_shield:
            case e_code_item_table::tbl_code_helmet:
            case e_code_item_table::tbl_code_cloak:
            case e_code_item_table::tbl_code_weapon:
                return m_bItemTypeSettings[0]; // Equipment
            case e_code_item_table::tbl_code_potion:
            case e_code_item_table::tbl_code_bullet:
                return m_bItemTypeSettings[1]; // Consumables
            case e_code_item_table::tbl_code_res:
                return m_bItemTypeSettings[2]; // Resources
            case e_code_item_table::tbl_code_map:
            case e_code_item_table::tbl_code_town:
            case e_code_item_table::tbl_code_bdungeon:
                return m_bItemTypeSettings[3]; // Special Items
            case e_code_item_table::tbl_code_fcitem:
                return m_bItemTypeSettings[4]; // Force Items
            case e_code_item_table::tbl_code_ring:
            case e_code_item_table::tbl_code_amulet:
                return m_bItemTypeSettings[5]; // Accessories
            default:
                return true; // Allow other types by default
            }
        }

        bool CLootExchange::IsCurrencyTypeAllowed(uint32_t nMoneyType)
        {
            if (nMoneyType >= 7) return false;
            return m_bCurrencySettings[nMoneyType];
        }

        void CLootExchange::LogExchange(const std::string& message)
        {
            if (!m_bEnableLogging) return;

            std::ofstream logFile(m_sLogFilePath, std::ios::app);
            if (logFile.is_open()) {
                // Get current time
                time_t now = time(0);
                char* timeStr = ctime(&now);
                timeStr[strlen(timeStr) - 1] = '\0'; // Remove newline

                logFile << "[" << timeStr << "] " << message << std::endl;
                logFile.close();
            }
        }

        void WINAPIV CLootExchange::pc_TakeGroundingItem(
            ATF::CPlayer* pObj,
            ATF::CItemBox* pBox,
            uint16_t wAddSerial,
            ATF::Info::CPlayerpc_TakeGroundingItem1947_ptr next)
        {
            bool bApplyModule = false;

            do
            {
                if (!CLootExchange::m_bActivated)
                {
                    break;
                }

                // Check premium player requirement
                if (m_bPremiumPlayersOnly && !pObj->IsPremiumUser())
                {
                    break;
                }

                // Check minimum player level
                if (pObj->GetLevel() < m_nMinimumPlayerLevel)
                {
                    break;
                }

                if (!pBox->m_bLive)
                {
                    break;
                }

                if (!pBox->IsTakeRight(pObj))
                {
                    break;
                }

                if (ATF::Global::Get3DSqrt(pBox->m_fCurPos, pObj->m_fCurPos) > m_fMaxPickupDistance)
                {
                    break;
                }

                if (pBox->m_Item.m_byTableCode >= _countof(ATF::Global::g_MainThread->m_tblItemData))
                {
                    break;
                }

                // Check if this item type is allowed for exchange
                if (!IsItemTypeAllowed(pBox->m_Item.m_byTableCode))
                {
                    break;
                }

                auto& ItemRecords = ATF::Global::g_MainThread->m_tblItemData[pBox->m_Item.m_byTableCode];
                auto pRecord = ItemRecords.GetRecord(pBox->m_Item.m_wItemIndex);
                if (pRecord == nullptr)
                {
                    break;
                }

                // Get all available money types for this item
                ::std::bitset<32> bMoneyType(GetMoneyType(pRecord, pBox->m_Item.m_byTableCode));

                // Enhanced logic to support all money types with priority
                uint32_t nSelectedMoneyType = 0;
                bool bFoundValidMoneyType = false;

                if (CLootExchange::m_bExchangeAll)
                {
                    // Try money types in configured priority order
                    for (int priorityType : m_vMoneyTypePriority)
                    {
                        if (priorityType >= 0 && priorityType < static_cast<int>(e_money_type::num) &&
                            bMoneyType.test(priorityType) && IsCurrencyTypeAllowed(priorityType))
                        {
                            nSelectedMoneyType = priorityType;
                            bFoundValidMoneyType = true;
                            break;
                        }
                    }
                }
                else
                {
                    // Original behavior: only processing_point
                    if (bMoneyType.test(static_cast<int>(e_money_type::processing_point)) &&
                        IsCurrencyTypeAllowed(static_cast<int>(e_money_type::processing_point)))
                    {
                        nSelectedMoneyType = static_cast<int>(e_money_type::processing_point);
                        bFoundValidMoneyType = true;
                    }
                }

                if (!bFoundValidMoneyType)
                {
                    break;
                }

                int nMoneyValue = GetMoneyValue(pRecord, pBox->m_Item.m_byTableCode, nSelectedMoneyType, pObj->GetObjRace());
                if (nMoneyValue < m_nMinimumValue)
                {
                    break;
                }

                // Apply exchange rate modifier
                nMoneyValue = (nMoneyValue * m_nExchangeRateModifier) / 100;

                // Multiply by durability if enabled
                if (m_bMultiplyByDurability)
                {
                    nMoneyValue *= pBox->m_Item.m_dwDur ? pBox->m_Item.m_dwDur : 1;
                }

                if (nMoneyValue <= 0)
                {
                    break;
                }

                bool bAddMoney = AddMoney(pObj, nSelectedMoneyType, nMoneyValue);
                if (!bAddMoney)
                {
                    break;
                }

                // Log the exchange if enabled
                if (m_bEnableLogging)
                {
                    std::stringstream logMsg;
                    logMsg << "Player " << pObj->GetObjName() << " exchanged item (Table:"
                           << (int)pBox->m_Item.m_byTableCode << ", Index:" << pBox->m_Item.m_wItemIndex
                           << ") for " << nMoneyValue << " of currency type " << nSelectedMoneyType;
                    LogExchange(logMsg.str());
                }

                ATF::_STORAGE_LIST::_db_con BoxItem;
                memcpy(&BoxItem, &pBox->m_Item, sizeof(BoxItem));
                pBox->Destroy();
                pObj->SendMsg_TakeAddResult(0, &BoxItem);

                bApplyModule = true;
            } while (false);

            if (!bApplyModule)
                return next(pObj, pBox, wAddSerial);
        }

        int CLootExchange::GetMoneyType(ATF::_base_fld* pRec, char byTableCode)
        {
            int nMoneyType = 0;
            switch ((e_code_item_table)byTableCode)
            {
            case e_code_item_table::tbl_code_upper:
            case e_code_item_table::tbl_code_lower:
            case e_code_item_table::tbl_code_gauntlet:
            case e_code_item_table::tbl_code_shoe:
            case e_code_item_table::tbl_code_shield:
            case e_code_item_table::tbl_code_helmet:
            case e_code_item_table::tbl_code_cloak:
                {
                    ATF::_DfnEquipItem_fld* pItemRec = (ATF::_DfnEquipItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_weapon: {
                    ATF::_WeaponItem_fld* pItemRec = (ATF::_WeaponItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_maketool: {
                    ATF::_MakeToolItem_fld* pItemRec = (ATF::_MakeToolItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_bag: {
                    ATF::_BagItem_fld* pItemRec = (ATF::_BagItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_potion: {
                    ATF::_PotionItem_fld* pItemRec = (ATF::_PotionItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_bullet: {
                    ATF::_BulletItem_fld* pItemRec = (ATF::_BulletItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_res: {
                    ATF::_ResourceItem_fld* pItemRec = (ATF::_ResourceItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_booty: {
                    ATF::_BootyItem_fld* pItemRec = (ATF::_BootyItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_battery: {
                    ATF::_BatteryItem_fld* pItemRec = (ATF::_BatteryItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_fcitem: {
                    ATF::_ForceItem_fld* pItemRec = (ATF::_ForceItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_ring: {
                    ATF::_RingItem_fld* pItemRec = (ATF::_RingItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_amulet: {
                    ATF::_AmuletItem_fld* pItemRec = (ATF::_AmuletItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_map: {
                    ATF::_MapItem_fld* pItemRec = (ATF::_MapItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_town: {
                    ATF::_TOWNItem_fld* pItemRec = (ATF::_TOWNItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_bdungeon: {
                    ATF::_BattleDungeonItem_fld* pItemRec = (ATF::_BattleDungeonItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_animus: {
                    ATF::_AnimusItem_fld* pItemRec = (ATF::_AnimusItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            case e_code_item_table::tbl_code_tower: {
                    ATF::_GuardTowerItem_fld* pItemRec = (ATF::_GuardTowerItem_fld*)pRec;
                    nMoneyType = pItemRec->m_nMoney;
                } break;
            default:
                break;
            }
            return nMoneyType;
        }

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderGroupItemInfoTable.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderGroupItemInfoTablector_CUnmannedTraderGroupItemInfoTable2_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*);
        using CUnmannedTraderGroupItemInfoTablector_CUnmannedTraderGroupItemInfoTable2_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, CUnmannedTraderGroupItemInfoTablector_CUnmannedTraderGroupItemInfoTable2_ptr);
        using CUnmannedTraderGroupItemInfoTableDestroy4_ptr = void (WINAPIV*)();
        using CUnmannedTraderGroupItemInfoTableDestroy4_clbk = void (WINAPIV*)(CUnmannedTraderGroupItemInfoTableDestroy4_ptr);
        using CUnmannedTraderGroupItemInfoTableGetGroupID6_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, uint16_t, char*, char*, char*, unsigned int*);
        using CUnmannedTraderGroupItemInfoTableGetGroupID6_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, uint16_t, char*, char*, char*, unsigned int*, CUnmannedTraderGroupItemInfoTableGetGroupID6_ptr);
        using CUnmannedTraderGroupItemInfoTableGetSortType8_ptr = struct CUnmannedTraderSortType* (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, char);
        using CUnmannedTraderGroupItemInfoTableGetSortType8_clbk = struct CUnmannedTraderSortType* (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, char, CUnmannedTraderGroupItemInfoTableGetSortType8_ptr);
        using CUnmannedTraderGroupItemInfoTableGetVersion10_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, char, unsigned int*);
        using CUnmannedTraderGroupItemInfoTableGetVersion10_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, char, unsigned int*, CUnmannedTraderGroupItemInfoTableGetVersion10_ptr);
        using CUnmannedTraderGroupItemInfoTableIncreaseVersion12_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, char);
        using CUnmannedTraderGroupItemInfoTableIncreaseVersion12_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, char, CUnmannedTraderGroupItemInfoTableIncreaseVersion12_ptr);
        using CUnmannedTraderGroupItemInfoTableIncreaseVersion14_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, uint16_t);
        using CUnmannedTraderGroupItemInfoTableIncreaseVersion14_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, uint16_t, CUnmannedTraderGroupItemInfoTableIncreaseVersion14_ptr);
        using CUnmannedTraderGroupItemInfoTableIncreaseVersion16_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, uint16_t, char, char);
        using CUnmannedTraderGroupItemInfoTableIncreaseVersion16_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, uint16_t, char, char, CUnmannedTraderGroupItemInfoTableIncreaseVersion16_ptr);
        using CUnmannedTraderGroupItemInfoTableInit18_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*);
        using CUnmannedTraderGroupItemInfoTableInit18_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, CUnmannedTraderGroupItemInfoTableInit18_ptr);
        using CUnmannedTraderGroupItemInfoTableInstance20_ptr = struct CUnmannedTraderGroupItemInfoTable* (WINAPIV*)();
        using CUnmannedTraderGroupItemInfoTableInstance20_clbk = struct CUnmannedTraderGroupItemInfoTable* (WINAPIV*)(CUnmannedTraderGroupItemInfoTableInstance20_ptr);
        using CUnmannedTraderGroupItemInfoTableIsExistGroupID22_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, char, char, char, unsigned int*);
        using CUnmannedTraderGroupItemInfoTableIsExistGroupID22_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char, char, char, char, unsigned int*, CUnmannedTraderGroupItemInfoTableIsExistGroupID22_ptr);
        using CUnmannedTraderGroupItemInfoTableLog24_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char*);
        using CUnmannedTraderGroupItemInfoTableLog24_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, char*, CUnmannedTraderGroupItemInfoTableLog24_ptr);
        using CUnmannedTraderGroupItemInfoTableSetLogger26_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, struct CLogFile*);
        using CUnmannedTraderGroupItemInfoTableSetLogger26_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, struct CLogFile*, CUnmannedTraderGroupItemInfoTableSetLogger26_ptr);
        
        using CUnmannedTraderGroupItemInfoTabledtor_CUnmannedTraderGroupItemInfoTable30_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*);
        using CUnmannedTraderGroupItemInfoTabledtor_CUnmannedTraderGroupItemInfoTable30_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupItemInfoTable*, CUnmannedTraderGroupItemInfoTabledtor_CUnmannedTraderGroupItemInfoTable30_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

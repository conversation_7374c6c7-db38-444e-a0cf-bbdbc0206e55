// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGameObject.hpp>
#include <CMyTimer.hpp>
#include <_character_create_setdata.hpp>
#include <_effect_parameter.hpp>
#include <_force_fld.hpp>
#include <_object_id.hpp>
#include <_sf_continous.hpp>
#include <_skill_fld.hpp>


START_ATF_NAMESPACE
    struct  CCharacter : CGameObject
    {
        float m_fTarPos[3];
        int m_AroundNum;
        CCharacter *m_AroundSlot[5];
        unsigned int m_dwNextGenAttackTime;
        _sf_continous m_SFCont[2][8];
        _sf_continous m_SFContAura[2][8];
        unsigned int m_dwEffSerialCounter;
        bool m_bLastContEffectUpdate;
        unsigned __int16 m_wLastContEffect;
        _effect_parameter m_EP;
        unsigned __int16 m_wEffectTempValue;
        unsigned int m_dwPlayerSerial;
        char m_wszPlayerName[17];
        int m_nContEffectSec;
        CMyTimer m_tmrSFCont;
    public:
        void AlterContDurSec(char byContCode, uint16_t wListIndex, unsigned int dwStartSec, uint16_t wNewDur);
        bool AssistForce(struct CCharacter* pDstChar, struct _force_fld* pForceFld, int nForceLv, char* pbyErrorCode, bool* pbUpMty);
        bool AssistForceToOne(struct CCharacter* pDst, struct _force_fld* pForceFld, int nForceLv);
        bool AssistSkill(struct CCharacter* pDstChar, int nEffectCode, struct _skill_fld* pSkillFld, int nSkillLv, char* pbyErrorCode, bool* pbUpMty);
        bool AssistSkillToOne(struct CCharacter* pDst, int nEffectCode, struct _skill_fld* pSkillFld, int nSkillLv);
        void BreakStealth();
        CCharacter();
        void ctor_CCharacter();
        float CalcDistForSec(float fSec, float fSpeed);
        uint16_t CalcEffectBit(uint16_t wEffectCode, uint16_t wEffectIndex);
        bool Create(struct _character_create_setdata* pData);
        bool Destroy();
        int FindEffectDst(int nEffectCode, int nAreaType, int nLv, bool bBenefit, struct CCharacter* pOriDst, char* psActableDst, struct CCharacter** ppDsts);
        int FindPotionEffectDst(int nAreaType, int nEffectAreaVal, bool bBenefit, struct CCharacter* pOriDst, char* psActableDst, struct CCharacter** ppDsts, bool* pbPath);
        int GetAttackDamPoint(int nAttPnt, int nAttPart, int nTolType, struct CCharacter* pDst, bool bBackAttack);
        int GetAttackRandomPart();
        bool GetInvisible();
        int GetNearEmptySlot(int pos, float dist, float* cur, float* target);
        unsigned int GetNextGenAttTime();
        int GetSlot(struct CCharacter* p);
        bool GetStealth(bool bInvisible);
        int GetTotalTol(char byAttTolType, int nDamPoint);
        void Go();
        void Init(struct _object_id* pID);
        char InsertSFContEffect(char byContCode, char byEffectCode, unsigned int dwEffectIndex, uint16_t wDurSec, char byLv, bool* pbUpMty, struct CCharacter* pActChar);
        int InsertSlot(struct CCharacter* p, int pos);
        bool IsDamageEffect(unsigned int uiEffectCodeType, uint16_t wEffectIndex);
        bool IsEffectableDst(char* psActableDst, struct CCharacter* pDst);
        bool IsPotionEffectableDst(char* psActableDst, struct CCharacter* pDst);
        void Move(float fSpeed);
        void MoveBreak(float fSpeed);
        bool RemoveAllContinousEffect();
        bool RemoveAllContinousEffectGroup(unsigned int uiEffectCodeType);
        void RemoveSFContEffect(char byContCode, uint16_t wListIndex, bool bInit, bool bAura);
        void RemoveSFContHelpByEffect(int nContParamCode, int nContParamIndex);
        int RemoveSlot(struct CCharacter* p);
        void ResetSlot();
        void SFContInit();
        void SendMsg_AttackActEffect(char byActEffect, struct CCharacter* pDamer);
        void SendMsg_LastEffectChangeInform();
        void SendMsg_RobedHP(struct CCharacter* pkPerform, uint16_t wRobedHP);
        void SendMsg_StunInform();
        void SetNextGenAttTime(unsigned int dwNextTime);
        bool SetTarPos(float* fTarPos, bool bColl);
        void Stop();
        void UpdateSFCont();
        int _GetAreaEffectMember(struct CCharacter* pOriDst, bool bBenefit, int nLimitRadius, float* pTar, char* psActableDst, struct CCharacter** ppDsts);
        int _GetFlashEffectMember(struct CCharacter* pOriDst, bool bBenefit, int nLimitRadius, int nLimitAngle, struct CCharacter* pOriTar, char* psActableDst, struct CCharacter** ppDsts);
        int _GetPartyEffectMember(struct CCharacter* pOriDst, bool bCircle, struct CCharacter** ppDsts);
        void _set_sf_cont(struct _sf_continous* pCont, char byEffectCode, uint16_t wEffectIndex, char byLv, unsigned int dwStartSec, uint16_t wDurSec, int nCumulCount);
        ~CCharacter();
        void dtor_CCharacter();
    };    
    static_assert(ATF::checkSize<CCharacter, 1872>(), "CCharacter");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_pvp_order_view_end_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _pvp_order_view_end_zoclsize2_ptr = int (WINAPIV*)(struct _pvp_order_view_end_zocl*);
        using _pvp_order_view_end_zoclsize2_clbk = int (WINAPIV*)(struct _pvp_order_view_end_zocl*, _pvp_order_view_end_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _D3DADAPTER_IDENTIFIER8
    {
        char Driver[512];
        char Description[512];
        _LARGE_INTEGER DriverVersion;
        unsigned int VendorId;
        unsigned int DeviceId;
        unsigned int SubSysId;
        unsigned int Revision;
        _GUID DeviceIdentifier;
        unsigned int WHQLLevel;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

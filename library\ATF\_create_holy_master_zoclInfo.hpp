// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_create_holy_master_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _create_holy_master_zoclctor__create_holy_master_zocl2_ptr = void (WINAPIV*)(struct _create_holy_master_zocl*);
        using _create_holy_master_zoclctor__create_holy_master_zocl2_clbk = void (WINAPIV*)(struct _create_holy_master_zocl*, _create_holy_master_zoclctor__create_holy_master_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerJP.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerJPctor_CCashDbWorkerJP2_ptr = void (WINAPIV*)(struct CCashDbWorkerJP*);
        using CCashDbWorkerJPctor_CCashDbWorkerJP2_clbk = void (WINAPIV*)(struct CCashDbWorkerJP*, CCashDbWorkerJPctor_CCashDbWorkerJP2_ptr);
        using CCashDbWorkerJPConvertErrorCode4_ptr = int (WINAPIV*)(struct CCashDbWorkerJP*, char);
        using CCashDbWorkerJPConvertErrorCode4_clbk = int (WINAPIV*)(struct CCashDbWorkerJP*, char, CCashDbWorkerJPConvertErrorCode4_ptr);
        using CCashDbWorkerJPGetUseCashQueryStr6_ptr = void (WINAPIV*)(struct CCashDbWorkerJP*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDbWorkerJPGetUseCashQueryStr6_clbk = void (WINAPIV*)(struct CCashDbWorkerJP*, struct _param_cash_update*, int, char*, uint64_t, CCashDbWorkerJPGetUseCashQueryStr6_ptr);
        using CCashDbWorkerJP_all_rollback8_ptr = void (WINAPIV*)(struct CCashDbWorkerJP*, struct _param_cash_update*);
        using CCashDbWorkerJP_all_rollback8_clbk = void (WINAPIV*)(struct CCashDbWorkerJP*, struct _param_cash_update*, CCashDbWorkerJP_all_rollback8_ptr);
        using CCashDbWorkerJP_wait_tsk_cash_rollback10_ptr = int (WINAPIV*)(struct CCashDbWorkerJP*, struct Task*);
        using CCashDbWorkerJP_wait_tsk_cash_rollback10_clbk = int (WINAPIV*)(struct CCashDbWorkerJP*, struct Task*, CCashDbWorkerJP_wait_tsk_cash_rollback10_ptr);
        using CCashDbWorkerJP_wait_tsk_cash_select12_ptr = int (WINAPIV*)(struct CCashDbWorkerJP*, struct Task*);
        using CCashDbWorkerJP_wait_tsk_cash_select12_clbk = int (WINAPIV*)(struct CCashDbWorkerJP*, struct Task*, CCashDbWorkerJP_wait_tsk_cash_select12_ptr);
        using CCashDbWorkerJP_wait_tsk_cash_update14_ptr = int (WINAPIV*)(struct CCashDbWorkerJP*, struct Task*);
        using CCashDbWorkerJP_wait_tsk_cash_update14_clbk = int (WINAPIV*)(struct CCashDbWorkerJP*, struct Task*, CCashDbWorkerJP_wait_tsk_cash_update14_ptr);
        
        using CCashDbWorkerJPdtor_CCashDbWorkerJP19_ptr = void (WINAPIV*)(struct CCashDbWorkerJP*);
        using CCashDbWorkerJPdtor_CCashDbWorkerJP19_clbk = void (WINAPIV*)(struct CCashDbWorkerJP*, CCashDbWorkerJPdtor_CCashDbWorkerJP19_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

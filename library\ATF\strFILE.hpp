// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <strFILEVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct strFILE
    {
        strFILEVtbl *vfptr;
        char *m_pLoadStr;
        char *m_pReadStr;
        unsigned int m_dwLoadSize;
    public:
        bool load(char* pszFileName);
        int read_line_count();
        strFILE();
        void ctor_strFILE();
        bool word(char* poutszWord);
        bool word(long double* pdoutVal);
        bool word(float* pfoutVal);
        bool word(int* pnoutVal);
        ~strFILE();
        void dtor_strFILE();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<strFILE, 32>(), "strFILE");
END_ATF_NAMESPACE

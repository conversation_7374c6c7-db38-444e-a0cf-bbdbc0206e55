// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_unmandtrader_updateitemstate
    {
        char byType;
        unsigned int dwRegistSerial;
        char byState;
        unsigned __int16 wItemSerial;
        unsigned int dwOwnerSerial;
        char byItemTableCode;
        unsigned __int16 wItemTableIndex;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_unmandtrader_updateitemstate, 20>(), "_qry_case_unmandtrader_updateitemstate");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_limit_item_db_data.hpp>


START_ATF_NAMESPACE
    struct _qry_case_all_store_limit_item
    {
        struct __list
        {
            unsigned int dwDBSerial;
            bool bNewSerial;
            char byType;
            int nTypeSerial;
            unsigned int dwStoreIndex;
            unsigned __int64 dwLimitInitTime;
            _limit_item_db_data ItemData[16];
            char byRet;
        public:
            __list();
            void ctor___list();
            void init();
        };
        unsigned int dwMax;
        unsigned int dwCount;
        __list *pStoreList;
    public:
        void DataInit();
        bool Init(unsigned int dwStoreNum);
        _qry_case_all_store_limit_item();
        void ctor__qry_case_all_store_limit_item();
        ~_qry_case_all_store_limit_item();
        void dtor__qry_case_all_store_limit_item();
    };
    static_assert(ATF::checkSize<_qry_case_all_store_limit_item, 0x10>(), "_qry_case_all_store_limit_item");
END_ATF_NAMESPACE

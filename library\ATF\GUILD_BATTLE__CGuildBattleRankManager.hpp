// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_guild_battle_rank_list_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleRankManager
        {
            unsigned int m_dwVer[3];
            unsigned int m_dwGuildSerial[3][30][10];
            _guild_battle_rank_list_result_zocl **m_ppkList;
        public:
            CGuildBattleRankManager();
            void ctor_CGuildBattleRankManager();
            bool CheckRecord(unsigned int dwGuildSerial);
            void CleanUp();
            void Clear(char byRace);
            void Clear();
            static void Destroy();
            bool Find(char byRace, unsigned int dwGuildSerial, int* iFindInx, char* byFindPage);
            bool Init();
            static struct CGuildBattleRankManager* Instance();
            bool Load(char byRace);
            bool Load();
            void PushClearGuildBattleRank();
            void PushCreateGuildBattleRankTable();
            bool SelectGuildBattleRankList(char byRace, char* pOutData);
            void Send(int n, char bySelfRace, unsigned int dwCurVer, char byTabRace, char byPage, unsigned int dwGuildSerial);
            bool Update(char byRace, char* pLoadData);
            bool UpdateDraw(char by1PRace, unsigned int dw1PGuildSerial, char by2PRace, unsigned int dw2PGuildSerial);
            bool UpdateWinLose(char byWinRace, unsigned int dwWinGuildSerial, char byLoseRace, unsigned int dwLoseGuildSerial);
            ~CGuildBattleRankManager();
            void dtor_CGuildBattleRankManager();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

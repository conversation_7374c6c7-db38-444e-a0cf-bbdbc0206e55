// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagLAYERPLANEDESCRIPTOR
    {
        unsigned __int16 nSize;
        unsigned __int16 nVersion;
        unsigned int dwFlags;
        char iPixelType;
        char cColorBits;
        char cRedBits;
        char cRedShift;
        char cGreenBits;
        char cGreenShift;
        char cBlueBits;
        char cBlueShift;
        char cAlphaBits;
        char cAlphaShift;
        char cAccumBits;
        char cAccumRedBits;
        char cAccumGreenBits;
        char cAccumBlueBits;
        char cAccumAlphaBits;
        char cDepthBits;
        char cStencilBits;
        char cAuxBuffers;
        char iLayerPlane;
        char bReserved;
        unsigned int crTransparent;
    };
END_ATF_NAMESPACE

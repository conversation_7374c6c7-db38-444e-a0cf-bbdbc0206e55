// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagTCITEMA
    {
        unsigned int mask;
        unsigned int dwState;
        unsigned int dwStateMask;
        char *pszText;
        int cchTextMax;
        int iImage;
        __int64 lParam;
    };
END_ATF_NAMESPACE

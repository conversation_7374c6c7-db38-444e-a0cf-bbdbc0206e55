// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CObject.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CGopherLocator : CObject
    {
        ATL::CStringT<char> m_Locator;
        unsigned int m_dwBufferLength;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

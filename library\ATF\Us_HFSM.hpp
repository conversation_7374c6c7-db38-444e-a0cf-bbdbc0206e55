// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <UsPoint.hpp>
#include <Us_FSM_Node.hpp>
#include <Us_HFSMVtbl.hpp>


START_ATF_NAMESPACE
    struct Us_HFSM
    {
        Us_HFSMVtbl *vfptr;
        int m_bSet;
        void *m_pObject;
        unsigned int m_dwUsedCount;
        Us_FSM_Node m_ArNode[10];
        UsPoint m_spShareStateTBLPtr;
    public:
        void AddLoopDelayTime(int nIndex, unsigned int dwAddDelay);
        void CleanUp();
        unsigned int GetIndex(struct Us_FSM_Node* pNode);
        struct Us_FSM_Node* GetNode(unsigned int dwIndex);
        void* GetObjectA();
        unsigned int GetState(unsigned int dwIndex);
        void Init();
        int Link(struct Us_FSM_Node* pParent, struct Us_FSM_Node* pChild);
        void OnProcess(unsigned int dwLastTime);
        static void SendExternMsg(struct Us_HFSM* pHFS, unsigned int dwMSG, void* lpParam, int nParam);
        static void SendMsg(struct Us_HFSM* pHFS, unsigned int dwFSMIndex, unsigned int dwMSG, void* lpParam);
        void SetLoopTime(int nIndex, unsigned int dwLoopTIme);
        int SetMyData(struct UsStateTBL* pStateTBL, void* pObject);
        Us_HFSM();
        void ctor_Us_HFSM();
        ~Us_HFSM();
        void dtor_Us_HFSM();
    };
END_ATF_NAMESPACE

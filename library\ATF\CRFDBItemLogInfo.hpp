// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRFDBItemLog.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRFDBItemLogctor_CRFDBItemLog2_ptr = void (WINAPIV*)(struct CRFDBItemLog*, unsigned int);
        using CRFDBItemLogctor_CRFDBItemLog2_clbk = void (WINAPIV*)(struct CRFDBItemLog*, unsigned int, CRFDBItemLogctor_CRFDBItemLog2_ptr);
        using CRFDBItemLogCreateTblLtd4_ptr = bool (WINAPIV*)(struct CRFDBItemLog*, int);
        using CRFDBItemLogCreateTblLtd4_clbk = bool (WINAPIV*)(struct CRFDBItemLog*, int, CRFDBItemLogCreateTblLtd4_ptr);
        using CRFDBItemLogCreateTblLtd_Expend6_ptr = bool (WINAPIV*)(struct CRFDBItemLog*, int);
        using CRFDBItemLogCreateTblLtd_Expend6_clbk = bool (WINAPIV*)(struct CRFDBItemLog*, int, CRFDBItemLogCreateTblLtd_Expend6_ptr);
        using CRFDBItemLogCreateTblLtd_ItemInfo8_ptr = bool (WINAPIV*)(struct CRFDBItemLog*, int);
        using CRFDBItemLogCreateTblLtd_ItemInfo8_clbk = bool (WINAPIV*)(struct CRFDBItemLog*, int, CRFDBItemLogCreateTblLtd_ItemInfo8_ptr);
        using CRFDBItemLogSetKorTime10_ptr = void (WINAPIV*)(struct CRFDBItemLog*, unsigned int);
        using CRFDBItemLogSetKorTime10_clbk = void (WINAPIV*)(struct CRFDBItemLog*, unsigned int, CRFDBItemLogSetKorTime10_ptr);
        using CRFDBItemLoginsert_expend15_ptr = bool (WINAPIV*)(struct CRFDBItemLog*, struct _LTD_EXPEND*);
        using CRFDBItemLoginsert_expend15_clbk = bool (WINAPIV*)(struct CRFDBItemLog*, struct _LTD_EXPEND*, CRFDBItemLoginsert_expend15_ptr);
        using CRFDBItemLoginsert_iteminfo17_ptr = bool (WINAPIV*)(struct CRFDBItemLog*, struct _LTD_ITEMINFO*, char);
        using CRFDBItemLoginsert_iteminfo17_clbk = bool (WINAPIV*)(struct CRFDBItemLog*, struct _LTD_ITEMINFO*, char, CRFDBItemLoginsert_iteminfo17_ptr);
        using CRFDBItemLoginsert_ltd19_ptr = bool (WINAPIV*)(struct CRFDBItemLog*, struct _LTD*);
        using CRFDBItemLoginsert_ltd19_clbk = bool (WINAPIV*)(struct CRFDBItemLog*, struct _LTD*, CRFDBItemLoginsert_ltd19_ptr);
        
        using CRFDBItemLogdtor_CRFDBItemLog21_ptr = void (WINAPIV*)(struct CRFDBItemLog*);
        using CRFDBItemLogdtor_CRFDBItemLog21_clbk = void (WINAPIV*)(struct CRFDBItemLog*, CRFDBItemLogdtor_CRFDBItemLog21_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

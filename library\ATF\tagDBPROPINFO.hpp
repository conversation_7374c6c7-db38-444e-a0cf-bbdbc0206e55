// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagVARIANT.hpp>



START_ATF_NAMESPACE
    struct tagDBPROPINFO
    {
        wchar_t *pwszDescription;
        unsigned int dwPropertyID;
        unsigned int dwFlags;
        unsigned __int16 vtType;
        tagVARIANT vValues;
    };
END_ATF_NAMESPACE

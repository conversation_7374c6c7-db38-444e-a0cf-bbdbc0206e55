// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <_candidate_info.hpp>


START_ATF_NAMESPACE
    struct CandidateMgr
    {
        int _nMaxNum;
        _candidate_info *_kCandidate[3];
        _candidate_info *_kCandidate_old[3];
        _candidate_info *_kPatriarchGroup[3];
        int _nCandidateCnt_1st[3];
        _candidate_info *_pkCandidateLink_1st[3][500];
        int _nCandidateCnt_2st[3];
        _candidate_info *_pkCandidateLink_2st[3][8];
        _candidate_info *_pkLeader[3][9];
        CLogFile _kSysLog;
        CLogFile _kVoteResultLog;
    public:
        void AddScore(char byRace, char* wszName, char byScore);
        void ApplyPatriarchGroup();
        bool AppointPatriarchGroup(struct CPlayer* pOne, _candidate_info::ClassType eClassType);
        CandidateMgr();
        void ctor_CandidateMgr();
        void ChangeState_1to2();
        int CheckDBValidCharacter(char byProc);
        void CompleteInsertCandidate(char byRet, char* p);
        bool DischargePatriarchGroup(char byRace, _candidate_info::ClassType eClassType);
        void FinalDecision();
        struct _candidate_info* GetCandidate(char byRace, unsigned int dwIdx);
        struct _candidate_info* GetCandidateBySerial(char byRace, unsigned int dwASerial);
        int GetCandidateCnt_1st(char byRace);
        int GetCandidateCnt_2st(char byRace);
        struct _candidate_info* GetCandidate_2st(char byRace, int nIdx);
        struct _candidate_info* GetEmpty(char byRace);
        struct _candidate_info* GetEmptyPatriarchGroup(char byRace);
        struct _candidate_info* GetLeader(char byRace, int nIdx);
        int GetMaxNum();
        struct _candidate_info* GetPatriarchGroup(char byRace, _candidate_info::ClassType eType);
        struct _candidate_info* GetPatriarchGroupBySerial(char byRace, unsigned int dwASerial);
        unsigned int GetWinCnt(char byRace, unsigned int dwAvatorSerial);
        void InitCandidate();
        bool Initialize(int nMaxNum);
        int Insert_Candidate(char* p);
        static struct CandidateMgr* Instance();
        bool IsRegistedAvator_1(char byRace, unsigned int dwAvatorSerial);
        bool IsRegistedAvator_2(char byRace, char* wszName);
        bool IsRegistedAvator_2(char byRace, unsigned int dwAvatorSerial);
        bool LoadDatabase();
        bool LoadLeaderPreVersion(char byRace);
        bool LoadPatriarchGroup();
        bool Regist(struct CPlayer* pOne);
        bool Regist(char byRace, struct _PVP_RANK_DATA* pData);
        void Release();
        int Update_ClassType();
        int Update_DischargePatriarch(char* p);
        int Update_Refund();
        int Update_RegistCandidate_2st();
        int Update_Score();
        int Update_VoteTime(unsigned int dwSerial);
        void __AddWinner(char byRace, char byNum);
        int __SortByPvpPoint();
        int __SortByRank();
        int __SortByScore();
        ~CandidateMgr();
        void dtor_CandidateMgr();
    };
END_ATF_NAMESPACE

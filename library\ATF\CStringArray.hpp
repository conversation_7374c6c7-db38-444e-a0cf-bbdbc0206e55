// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CObject.hpp>


START_ATF_NAMESPACE
    struct  CStringArray : CObject
    {
        ATL::CStringT<char> *m_pData;
        __int64 m_nSize;
        __int64 m_nMaxSize;
        __int64 m_nGrowBy;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingManager.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CBillingManagerAlive2_ptr = void (WINAPIV*)(struct CBillingManager*, struct CUserDB*);
        using CBillingManagerAlive2_clbk = void (WINAPIV*)(struct CBillingManager*, struct CUserDB*, CBillingManagerAlive2_ptr);
        using CBillingManagerBillingClose4_ptr = void (WINAPIV*)(struct CBillingManager*, char*);
        using CBillingManagerBillingClose4_clbk = void (WINAPIV*)(struct CBillingManager*, char*, CBillingManagerBillingClose4_ptr);
        
        using CBillingManagerctor_CBillingManager6_ptr = void (WINAPIV*)(struct CBillingManager*);
        using CBillingManagerctor_CBillingManager6_clbk = void (WINAPIV*)(struct CBillingManager*, CBillingManagerctor_CBillingManager6_ptr);
        using CBillingManagerChange_BillingType8_ptr = void (WINAPIV*)(struct CBillingManager*, char*, char*, int16_t, int, struct _SYSTEMTIME*, char);
        using CBillingManagerChange_BillingType8_clbk = void (WINAPIV*)(struct CBillingManager*, char*, char*, int16_t, int, struct _SYSTEMTIME*, char, CBillingManagerChange_BillingType8_ptr);
        using CBillingManagerChange_Primium10_ptr = void (WINAPIV*)(struct CBillingManager*, char*, bool);
        using CBillingManagerChange_Primium10_clbk = void (WINAPIV*)(struct CBillingManager*, char*, bool, CBillingManagerChange_Primium10_ptr);
        using CBillingManagerExpire_IPOverflow12_ptr = void (WINAPIV*)(struct CBillingManager*, char*);
        using CBillingManagerExpire_IPOverflow12_clbk = void (WINAPIV*)(struct CBillingManager*, char*, CBillingManagerExpire_IPOverflow12_ptr);
        using CBillingManagerExpire_PCBang14_ptr = void (WINAPIV*)(struct CBillingManager*, char*);
        using CBillingManagerExpire_PCBang14_clbk = void (WINAPIV*)(struct CBillingManager*, char*, CBillingManagerExpire_PCBang14_ptr);
        using CBillingManagerExpire_Personal16_ptr = void (WINAPIV*)(struct CBillingManager*, char*);
        using CBillingManagerExpire_Personal16_clbk = void (WINAPIV*)(struct CBillingManager*, char*, CBillingManagerExpire_Personal16_ptr);
        using CBillingManagerInit18_ptr = bool (WINAPIV*)(struct CBillingManager*);
        using CBillingManagerInit18_clbk = bool (WINAPIV*)(struct CBillingManager*, CBillingManagerInit18_ptr);
        using CBillingManagerIsOperate20_ptr = bool (WINAPIV*)(struct CBillingManager*);
        using CBillingManagerIsOperate20_clbk = bool (WINAPIV*)(struct CBillingManager*, CBillingManagerIsOperate20_ptr);
        using CBillingManagerLoadINI22_ptr = bool (WINAPIV*)(struct CBillingManager*);
        using CBillingManagerLoadINI22_clbk = bool (WINAPIV*)(struct CBillingManager*, CBillingManagerLoadINI22_ptr);
        using CBillingManagerLogin24_ptr = void (WINAPIV*)(struct CBillingManager*, struct CUserDB*);
        using CBillingManagerLogin24_clbk = void (WINAPIV*)(struct CBillingManager*, struct CUserDB*, CBillingManagerLogin24_ptr);
        using CBillingManagerLogout26_ptr = void (WINAPIV*)(struct CBillingManager*, struct CUserDB*);
        using CBillingManagerLogout26_clbk = void (WINAPIV*)(struct CBillingManager*, struct CUserDB*, CBillingManagerLogout26_ptr);
        using CBillingManagerRemaintime_PCBang28_ptr = void (WINAPIV*)(struct CBillingManager*, char*, int16_t, int, struct _SYSTEMTIME*);
        using CBillingManagerRemaintime_PCBang28_clbk = void (WINAPIV*)(struct CBillingManager*, char*, int16_t, int, struct _SYSTEMTIME*, CBillingManagerRemaintime_PCBang28_ptr);
        using CBillingManagerRemaintime_Personal30_ptr = void (WINAPIV*)(struct CBillingManager*, char*, int16_t, int, struct _SYSTEMTIME*);
        using CBillingManagerRemaintime_Personal30_clbk = void (WINAPIV*)(struct CBillingManager*, char*, int16_t, int, struct _SYSTEMTIME*, CBillingManagerRemaintime_Personal30_ptr);
        using CBillingManagerSendMsg_ZoneAliveCheck32_ptr = void (WINAPIV*)(struct CBillingManager*, unsigned int);
        using CBillingManagerSendMsg_ZoneAliveCheck32_clbk = void (WINAPIV*)(struct CBillingManager*, unsigned int, CBillingManagerSendMsg_ZoneAliveCheck32_ptr);
        using CBillingManagerStart34_ptr = void (WINAPIV*)(struct CBillingManager*);
        using CBillingManagerStart34_clbk = void (WINAPIV*)(struct CBillingManager*, CBillingManagerStart34_ptr);
        
        using CBillingManagerdtor_CBillingManager39_ptr = void (WINAPIV*)(struct CBillingManager*);
        using CBillingManagerdtor_CBillingManager39_clbk = void (WINAPIV*)(struct CBillingManager*, CBillingManagerdtor_CBillingManager39_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

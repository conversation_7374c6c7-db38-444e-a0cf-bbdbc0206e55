// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $D30FF6FEF44BAD43CC2D1F3BB74B6471
    {
      NATION_SCRIPT_KR = 0x0,
      NATION_SCRIPT_BR = 0x1,
      NATION_SCRIPT_CN = 0x2,
      NATION_SCRIPT_GB = 0x3,
      NATION_SCRIPT_ID = 0x4,
      NATION_SCRIPT_JP = 0x5,
      NATION_SCRIPT_PH = 0x6,
      NATION_SCRIPT_RU = 0x7,
      NATION_SCRIPT_TW = 0x8,
      NATION_SCRIPT_ES = 0x9,
      NATION_SCRIPT_TH = 0xA,
      NATION_SCRIPT_NUM = 0xB,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMgrAvatorLvHistory.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMgrAvatorLvHistoryctor_CMgrAvatorLvHistory2_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*);
        using CMgrAvatorLvHistoryctor_CMgrAvatorLvHistory2_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, CMgrAvatorLvHistoryctor_CMgrAvatorLvHistory2_ptr);
        using CMgrAvatorLvHistoryGetNewFileName4_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, unsigned int, char*);
        using CMgrAvatorLvHistoryGetNewFileName4_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, unsigned int, char*, CMgrAvatorLvHistoryGetNewFileName4_ptr);
        using CMgrAvatorLvHistoryGetTotalWaitSize6_ptr = int (WINAPIV*)(struct CMgrAvatorLvHistory*);
        using CMgrAvatorLvHistoryGetTotalWaitSize6_clbk = int (WINAPIV*)(struct CMgrAvatorLvHistory*, CMgrAvatorLvHistoryGetTotalWaitSize6_ptr);
        using CMgrAvatorLvHistoryIOThread8_ptr = void (WINAPIV*)(void*);
        using CMgrAvatorLvHistoryIOThread8_clbk = void (WINAPIV*)(void*, CMgrAvatorLvHistoryIOThread8_ptr);
        using CMgrAvatorLvHistoryOnLoop10_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*);
        using CMgrAvatorLvHistoryOnLoop10_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, CMgrAvatorLvHistoryOnLoop10_ptr);
        using CMgrAvatorLvHistoryWriteFile12_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, char*, char*);
        using CMgrAvatorLvHistoryWriteFile12_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, char*, char*, CMgrAvatorLvHistoryWriteFile12_ptr);
        using CMgrAvatorLvHistoryadjust_pvpcash14_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, bool, long double, long double, char*);
        using CMgrAvatorLvHistoryadjust_pvpcash14_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, bool, long double, long double, char*, CMgrAvatorLvHistoryadjust_pvpcash14_ptr);
        using CMgrAvatorLvHistoryalter_pvp16_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, long double, struct CPartyPlayer*, char*);
        using CMgrAvatorLvHistoryalter_pvp16_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, long double, struct CPartyPlayer*, char*, CMgrAvatorLvHistoryalter_pvp16_ptr);
        using CMgrAvatorLvHistorychar_copy18_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char*, unsigned int, char*);
        using CMgrAvatorLvHistorychar_copy18_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char*, unsigned int, char*, CMgrAvatorLvHistorychar_copy18_ptr);
        using CMgrAvatorLvHistoryclose20_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char*, char*);
        using CMgrAvatorLvHistoryclose20_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char*, char*, CMgrAvatorLvHistoryclose20_ptr);
        using CMgrAvatorLvHistorydie22_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char*, char*, char*);
        using CMgrAvatorLvHistorydie22_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char*, char*, char*, CMgrAvatorLvHistorydie22_ptr);
        using CMgrAvatorLvHistorydown_animus_exp24_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, uint64_t, uint64_t, int64_t, char*);
        using CMgrAvatorLvHistorydown_animus_exp24_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, uint64_t, uint64_t, int64_t, char*, CMgrAvatorLvHistorydown_animus_exp24_ptr);
        using CMgrAvatorLvHistorydown_exp26_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, long double, uint16_t, long double, uint16_t, char*, char*);
        using CMgrAvatorLvHistorydown_exp26_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, long double, uint16_t, long double, uint16_t, char*, char*, CMgrAvatorLvHistorydown_exp26_ptr);
        using CMgrAvatorLvHistorydowngrade_lv28_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, unsigned int, int, int*, char*);
        using CMgrAvatorLvHistorydowngrade_lv28_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, unsigned int, int, int*, char*, CMgrAvatorLvHistorydowngrade_lv28_ptr);
        using CMgrAvatorLvHistoryrecovery_exp30_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, long double, uint16_t, long double, uint16_t, long double, int, char*, char*);
        using CMgrAvatorLvHistoryrecovery_exp30_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, long double, uint16_t, long double, uint16_t, long double, int, char*, char*, CMgrAvatorLvHistoryrecovery_exp30_ptr);
        using CMgrAvatorLvHistorystart_mastery32_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char*, unsigned int, long double, unsigned int, int, int*, struct _MASTERY_PARAM*, char*);
        using CMgrAvatorLvHistorystart_mastery32_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char*, unsigned int, long double, unsigned int, int, int*, struct _MASTERY_PARAM*, char*, CMgrAvatorLvHistorystart_mastery32_ptr);
        using CMgrAvatorLvHistoryupdate_mastery34_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char, unsigned int, long double, unsigned int, int, int*, struct _MASTERY_PARAM*, unsigned int*, char*, char, char*);
        using CMgrAvatorLvHistoryupdate_mastery34_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, char, unsigned int, long double, unsigned int, int, int*, struct _MASTERY_PARAM*, unsigned int*, char*, char, char*, CMgrAvatorLvHistoryupdate_mastery34_ptr);
        using CMgrAvatorLvHistoryupgrade_lv36_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, unsigned int, int, int*, char*);
        using CMgrAvatorLvHistoryupgrade_lv36_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, int, unsigned int, int, int*, char*, CMgrAvatorLvHistoryupgrade_lv36_ptr);
        
        using CMgrAvatorLvHistorydtor_CMgrAvatorLvHistory38_ptr = void (WINAPIV*)(struct CMgrAvatorLvHistory*);
        using CMgrAvatorLvHistorydtor_CMgrAvatorLvHistory38_clbk = void (WINAPIV*)(struct CMgrAvatorLvHistory*, CMgrAvatorLvHistorydtor_CMgrAvatorLvHistory38_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

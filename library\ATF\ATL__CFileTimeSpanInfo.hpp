// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CFileTimeSpan.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            
            using ATL__CFileTimeSpanctor_CFileTimeSpan1_ptr = void (WINAPIV*)(struct ATL::CFileTimeSpan*, struct ATL::CFileTimeSpan*);
            using ATL__CFileTimeSpanctor_CFileTimeSpan1_clbk = void (WINAPIV*)(struct ATL::CFileTimeSpan*, struct ATL::CFileTimeSpan*, ATL__CFileTimeSpanctor_CFileTimeSpan1_ptr);
            
            using ATL__CFileTimeSpanctor_CFileTimeSpan2_ptr = void (WINAPIV*)(struct ATL::CFileTimeSpan*, int64_t);
            using ATL__CFileTimeSpanctor_CFileTimeSpan2_clbk = void (WINAPIV*)(struct ATL::CFileTimeSpan*, int64_t, ATL__CFileTimeSpanctor_CFileTimeSpan2_ptr);
            
            using ATL__CFileTimeSpanctor_CFileTimeSpan3_ptr = void (WINAPIV*)(struct ATL::CFileTimeSpan*);
            using ATL__CFileTimeSpanctor_CFileTimeSpan3_clbk = void (WINAPIV*)(struct ATL::CFileTimeSpan*, ATL__CFileTimeSpanctor_CFileTimeSpan3_ptr);
            using ATL__CFileTimeSpanGetTimeSpan4_ptr = int64_t (WINAPIV*)(struct ATL::CFileTimeSpan*);
            using ATL__CFileTimeSpanGetTimeSpan4_clbk = int64_t (WINAPIV*)(struct ATL::CFileTimeSpan*, ATL__CFileTimeSpanGetTimeSpan4_ptr);
            using ATL__CFileTimeSpanSetTimeSpan5_ptr = void (WINAPIV*)(struct ATL::CFileTimeSpan*, int64_t);
            using ATL__CFileTimeSpanSetTimeSpan5_clbk = void (WINAPIV*)(struct ATL::CFileTimeSpan*, int64_t, ATL__CFileTimeSpanSetTimeSpan5_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

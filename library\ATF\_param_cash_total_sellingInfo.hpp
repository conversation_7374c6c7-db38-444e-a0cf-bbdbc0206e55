// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_param_cash_total_selling.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _param_cash_total_sellingsize2_ptr = int (WINAPIV*)(struct _param_cash_total_selling*);
        using _param_cash_total_sellingsize2_clbk = int (WINAPIV*)(struct _param_cash_total_selling*, _param_cash_total_sellingsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

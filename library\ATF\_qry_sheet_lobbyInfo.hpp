// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_sheet_lobby.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_sheet_lobbyctor__qry_sheet_lobby2_ptr = void (WINAPIV*)(struct _qry_sheet_lobby*);
        using _qry_sheet_lobbyctor__qry_sheet_lobby2_clbk = void (WINAPIV*)(struct _qry_sheet_lobby*, _qry_sheet_lobbyctor__qry_sheet_lobby2_ptr);
        using _qry_sheet_lobbysize4_ptr = int (WINAPIV*)(struct _qry_sheet_lobby*);
        using _qry_sheet_lobbysize4_clbk = int (WINAPIV*)(struct _qry_sheet_lobby*, _qry_sheet_lobbysize4_ptr);
        
        using _qry_sheet_lobbydtor__qry_sheet_lobby6_ptr = void (WINAPIV*)(struct _qry_sheet_lobby*);
        using _qry_sheet_lobbydtor__qry_sheet_lobby6_clbk = void (WINAPIV*)(struct _qry_sheet_lobby*, _qry_sheet_lobbydtor__qry_sheet_lobby6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

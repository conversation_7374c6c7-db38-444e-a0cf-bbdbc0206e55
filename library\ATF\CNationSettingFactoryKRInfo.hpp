// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryKR.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryKRctor_CNationSettingFactoryKR2_ptr = void (WINAPIV*)(struct CNationSettingFactoryKR*);
        using CNationSettingFactoryKRctor_CNationSettingFactoryKR2_clbk = void (WINAPIV*)(struct CNationSettingFactoryKR*, CNationSettingFactoryKRctor_CNationSettingFactoryKR2_ptr);
        using CNationSettingFactoryKRCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryKR*, int, char*, bool);
        using CNationSettingFactoryKRCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryKR*, int, char*, bool, CNationSettingFactoryKRCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

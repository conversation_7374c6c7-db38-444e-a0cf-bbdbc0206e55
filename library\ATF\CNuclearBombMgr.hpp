// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNuclearBomb.hpp>
#include <CNuclearBombMgrVtbl.hpp>
#include <CPlayer.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CNuclearBombMgr
    {
        CNuclearBombMgrVtbl *vfptr;
        char m_szStickCode[64];
        CNuclearBomb m_Missile[3][3];
        unsigned int m_dwWarnTime;
        unsigned int m_dwAttInformTime;
        unsigned int m_dwAttStartTime;
    public:
        CNuclearBombMgr();
        void ctor_CNuclearBombMgr();
        void CheckNuclearState(struct CPlayer* pOne);
        bool CreateMissile(struct CPlayer* pMaster, float* fPos, unsigned int WarnTime, unsigned int InformTime, unsigned int StartTime);
        void Destroy();
        char GetBossType(char byRace, unsigned int dwSerial);
        static struct CNuclearBombMgr* Instance();
        bool IsPatriarch(struct CPlayer* pOne);
        bool LoadIni();
        void Loop();
        bool MissileInit();
        bool Request_EnableNuclearControl(int n, char* pMsg);
        bool Request_SelectDropPosition(int n, char* pMsg);
        void SendMsg_Result(int n, char byCode);
        ~CNuclearBombMgr();
        void dtor_CNuclearBombMgr();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

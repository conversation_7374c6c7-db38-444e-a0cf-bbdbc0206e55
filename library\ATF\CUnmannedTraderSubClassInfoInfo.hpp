// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSubClassInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderSubClassInfoctor_CUnmannedTraderSubClassInfo2_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, struct CUnmannedTraderSubClassInfo*);
        using CUnmannedTraderSubClassInfoctor_CUnmannedTraderSubClassInfo2_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, struct CUnmannedTraderSubClassInfo*, CUnmannedTraderSubClassInfoctor_CUnmannedTraderSubClassInfo2_ptr);
        
        using CUnmannedTraderSubClassInfoctor_CUnmannedTraderSubClassInfo4_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, unsigned int);
        using CUnmannedTraderSubClassInfoctor_CUnmannedTraderSubClassInfo4_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, unsigned int, CUnmannedTraderSubClassInfoctor_CUnmannedTraderSubClassInfo4_ptr);
        using CUnmannedTraderSubClassInfoCopy6_ptr = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, struct CUnmannedTraderSubClassInfo*);
        using CUnmannedTraderSubClassInfoCopy6_clbk = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, struct CUnmannedTraderSubClassInfo*, CUnmannedTraderSubClassInfoCopy6_ptr);
        using CUnmannedTraderSubClassInfoGetGroupID8_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, char, uint16_t, char*);
        using CUnmannedTraderSubClassInfoGetGroupID8_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, char, uint16_t, char*, CUnmannedTraderSubClassInfoGetGroupID8_ptr);
        using CUnmannedTraderSubClassInfoGetID10_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderSubClassInfo*);
        using CUnmannedTraderSubClassInfoGetID10_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, CUnmannedTraderSubClassInfoGetID10_ptr);
        using CUnmannedTraderSubClassInfoGetTypeName12_ptr = char* (WINAPIV*)(struct CUnmannedTraderSubClassInfo*);
        using CUnmannedTraderSubClassInfoGetTypeName12_clbk = char* (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, CUnmannedTraderSubClassInfoGetTypeName12_ptr);
        using CUnmannedTraderSubClassInfoLoadXML14_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int);
        using CUnmannedTraderSubClassInfoLoadXML14_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, struct TiXmlElement*, struct CLogFile*, unsigned int, unsigned int, CUnmannedTraderSubClassInfoLoadXML14_ptr);
        
        using CUnmannedTraderSubClassInfodtor_CUnmannedTraderSubClassInfo18_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassInfo*);
        using CUnmannedTraderSubClassInfodtor_CUnmannedTraderSubClassInfo18_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassInfo*, CUnmannedTraderSubClassInfodtor_CUnmannedTraderSubClassInfo18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

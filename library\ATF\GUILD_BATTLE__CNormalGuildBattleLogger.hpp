// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CNormalGuildBattleLogger
        {
            CLogFile *m_pkLogger;
        public:
            CNormalGuildBattleLogger();
            void ctor_CNormalGuildBattleLogger();
            void CreateLogFile(char* szLogName);
            bool Init();
            void Log(char* fmt);
            void Log(wchar_t* fmt);
            ~CNormalGuildBattleLogger();
            void dtor_CNormalGuildBattleLogger();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

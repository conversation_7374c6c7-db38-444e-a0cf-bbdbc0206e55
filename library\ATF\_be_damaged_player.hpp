// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>


START_ATF_NAMESPACE
    struct _be_damaged_player
    {
        CCharacter *m_pChar;
        unsigned int m_dwDamCharSerial;
        int m_nDamage;
    public:
        _be_damaged_player();
        void ctor__be_damaged_player();
    };
END_ATF_NAMESPACE

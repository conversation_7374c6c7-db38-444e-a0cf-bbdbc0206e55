// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingPH.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CBillingPHctor_CBillingPH2_ptr = void (WINAPIV*)(struct CBillingPH*);
        using CBillingPHctor_CBillingPH2_clbk = void (WINAPIV*)(struct CBillingPH*, CBillingPHctor_CBillingPH2_ptr);
        
        using CBillingPHdtor_CBillingPH7_ptr = void (WINAPIV*)(struct CBillingPH*);
        using CBillingPHdtor_CBillingPH7_clbk = void (WINAPIV*)(struct CBillingPH*, CBillingPHdtor_CBillingPH7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

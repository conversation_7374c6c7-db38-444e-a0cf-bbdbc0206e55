// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _holy_quest_report_wrac
    {
        char byRaceCode;
        char wszCharName[17];
        char byDestroyedRaceCode;
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_holy_quest_report_wrac, 19>(), "_holy_quest_report_wrac");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct $7B1C2AA558A72DB3909F7F0B6C8C78B2
    {
        BYTE gap0[8];
        wchar_t *bstrVal;
    };    
    static_assert(ATF::checkSize<$7B1C2AA558A72DB3909F7F0B6C8C78B2, 16>(), "$7B1C2AA558A72DB3909F7F0B6C8C78B2");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DELAY_PROCESS.hpp>


START_ATF_NAMESPACE
    struct  _ANIMUS_RETURN_DELAY : _DELAY_PROCESS
    {
    public:
        void Process(unsigned int dwIndex, unsigned int dwSerial);
        _ANIMUS_RETURN_DELAY();
        void ctor__ANIMUS_RETURN_DELAY();
        ~_ANIMUS_RETURN_DELAY();
        void dtor__ANIMUS_RETURN_DELAY();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_MONEY_SUPPLY_DATA.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _MONEY_SUPPLY_DATAinit2_ptr = void (WINAPIV*)(struct _MONEY_SUPPLY_DATA*);
        using _MONEY_SUPPLY_DATAinit2_clbk = void (WINAPIV*)(struct _MONEY_SUPPLY_DATA*, _MONEY_SUPPLY_DATAinit2_ptr);
        using _MONEY_SUPPLY_DATAsize4_ptr = int (WINAPIV*)(struct _MONEY_SUPPLY_DATA*);
        using _MONEY_SUPPLY_DATAsize4_clbk = int (WINAPIV*)(struct _MONEY_SUPPLY_DATA*, _MONEY_SUPPLY_DATAsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

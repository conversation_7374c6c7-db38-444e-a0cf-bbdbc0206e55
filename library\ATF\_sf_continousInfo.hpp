// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_sf_continous.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _sf_continousGetSFContCurTime2_ptr = unsigned int (WINAPIV*)();
        using _sf_continousGetSFContCurTime2_clbk = unsigned int (WINAPIV*)(_sf_continousGetSFContCurTime2_ptr);
        
        using _sf_continousctor__sf_continous4_ptr = void (WINAPIV*)(struct _sf_continous*);
        using _sf_continousctor__sf_continous4_clbk = void (WINAPIV*)(struct _sf_continous*, _sf_continousctor__sf_continous4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

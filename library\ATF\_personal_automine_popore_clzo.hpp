// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _personal_automine_popore_clzo
    {
        unsigned __int16 wItemSerial;
        char byNum;
        char byStorageIndex_L;
        char byStorageIndex_R;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

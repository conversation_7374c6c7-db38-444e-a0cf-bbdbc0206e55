// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  DLGTEMPLATEEX
    {
        unsigned __int16 dlgVer;
        unsigned __int16 signature;
        unsigned int helpID;
        unsigned int exStyle;
        unsigned int style;
        unsigned __int16 cDlgItems;
        __int16 x;
        __int16 y;
        __int16 cx;
        __int16 cy;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

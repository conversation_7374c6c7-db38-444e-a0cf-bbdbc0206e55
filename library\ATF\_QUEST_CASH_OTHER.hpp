// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _QUEST_CASH_OTHER
    {
        unsigned int dwAvatorSerial;
        char byStoneMapMoveInfo;
    public:
        _QUEST_CASH_OTHER();
        void ctor__QUEST_CASH_OTHER();
        void init();
        bool isLoaded();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_QUEST_CASH_OTHER, 8>(), "_QUEST_CASH_OTHER");
END_ATF_NAMESPACE

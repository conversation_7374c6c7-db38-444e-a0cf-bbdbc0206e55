// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _object_create_setdata
    {
        struct _base_fld *m_pRecordSet;
        struct CMapData *m_pMap;
        int m_nLayerIndex;
        float m_fStartPos[3];
    public:
        _object_create_setdata();
        void ctor__object_create_setdata();
    };    
    static_assert(ATF::checkSize<_object_create_setdata, 32>(), "_object_create_setdata");
END_ATF_NAMESPACE

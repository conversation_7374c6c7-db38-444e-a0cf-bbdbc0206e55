#include <_qry_case_in_atrade_tax.hpp>


START_ATF_NAMESPACE
    _qry_case_in_atrade_tax::_qry_case_in_atrade_tax()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_in_atrade_tax*);
        (org_ptr(0x1402605a0L))(this);
    };
    void _qry_case_in_atrade_tax::ctor__qry_case_in_atrade_tax()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_in_atrade_tax*);
        (org_ptr(0x1402605a0L))(this);
    };
    int _qry_case_in_atrade_tax::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_in_atrade_tax*);
        return (org_ptr(0x140260620L))(this);
    };
END_ATF_NAMESPACE

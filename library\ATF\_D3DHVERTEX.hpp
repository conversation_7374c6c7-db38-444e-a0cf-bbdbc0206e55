// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$3DBF066EA467C2B6A6BD27A3606A3EEF.hpp>
#include <$A69B8A50A378323794EDBEA675A5AFBC.hpp>
#include <$B2204CA36E1A956822577B1F8F731D20.hpp>


START_ATF_NAMESPACE
    struct _D3DHVERTEX
    {
        unsigned int dwFlags;
        $B2204CA36E1A956822577B1F8F731D20 ___u1;
        $A69B8A50A378323794EDBEA675A5AFBC ___u2;
        $3DBF066EA467C2B6A6BD27A3606A3EEF ___u3;
    };
END_ATF_NAMESPACE

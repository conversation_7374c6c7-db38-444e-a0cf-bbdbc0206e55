// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <_ChatStealTargetInfo.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CChatStealSystem
    {
        CPlayer *m_pGM;
        _ChatStealTargetInfo m_TargetInfo;
    public:
        CChatStealSystem();
        void ctor_CChatStealSystem();
        static struct CChatStealSystem* Instance();
        void SendStealMsg(struct CPlayer* pPlayer, char byChatType, unsigned int dwSenderSerial, char* pwszSender, char byRaceCode, char* pwszMessage);
        bool SetGm(struct CPlayer* pGM);
        bool SetTargetInfoFromBoss(char byType, char byRaceCode);
        bool SetTargetInfoFromCharacter(char byType, char* szCharName);
        bool SetTargetInfoFromRace(char byType, char byRaceCode);
        void StealChatMsg(struct CPlayer* pPlayer, char byChatType, char* szChatMsg);
        ~CChatStealSystem();
        void dtor_CChatStealSystem();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

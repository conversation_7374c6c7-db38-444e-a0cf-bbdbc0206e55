// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct OLE_DATA
    {
        unsigned int cfNative;
        unsigned int cfOwnerLink;
        unsigned int cfObjectLink;
        unsigned int cfEmbeddedObject;
        unsigned int cfEmbedSource;
        unsigned int cfLinkSource;
        unsigned int cfObjectDescriptor;
        unsigned int cfLinkSourceDescriptor;
        unsigned int cfFileName;
        unsigned int cfFileNameW;
        unsigned int cfRichTextFormat;
        unsigned int cfRichTextAndObjects;
    };
END_ATF_NAMESPACE

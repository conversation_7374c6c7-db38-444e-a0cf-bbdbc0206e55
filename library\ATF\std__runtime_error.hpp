// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__basic_string.hpp>
#include <std__exception.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct  runtime_error : exception
        {
            basic_string<char,char_traits<char>,allocator<char> > _Str;
        };
    }; // end namespace std
END_ATF_NAMESPACE

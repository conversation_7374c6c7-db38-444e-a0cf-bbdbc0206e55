// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_NOT_ARRANGED_AVATOR_DB.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _NOT_ARRANGED_AVATOR_DBInit2_ptr = void (WINAPIV*)(struct _NOT_ARRANGED_AVATOR_DB*);
        using _NOT_ARRANGED_AVATOR_DBInit2_clbk = void (WINAPIV*)(struct _NOT_ARRANGED_AVATOR_DB*, _NOT_ARRANGED_AVATOR_DBInit2_ptr);
        
        using _NOT_ARRANGED_AVATOR_DBctor__NOT_ARRANGED_AVATOR_DB4_ptr = void (WINAPIV*)(struct _NOT_ARRANGED_AVATOR_DB*);
        using _NOT_ARRANGED_AVATOR_DBctor__NOT_ARRANGED_AVATOR_DB4_clbk = void (WINAPIV*)(struct _NOT_ARRANGED_AVATOR_DB*, _NOT_ARRANGED_AVATOR_DBctor__NOT_ARRANGED_AVATOR_DB4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

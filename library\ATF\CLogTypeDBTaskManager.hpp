// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CLogTypeDBTaskPool.hpp>
#include <CRFWorldDatabase.hpp>


START_ATF_NAMESPACE
    struct CLogTypeDBTaskManager
    {
        enum INIT_STATE
        {
            LTDTM_NONE = 0xFFFFFFFF,
            LTDTM_INIT = 0x0,
            LTDTM_DB_INIT = 0x1,
        };
        INIT_STATE m_eState;
        bool m_bDBProc;
        CRFWorldDatabase *m_pkWorldDB;
        CLogTypeDBTaskPool m_kPool;
        CLogFile *m_pkLogger;
    public:
        CLogTypeDBTaskManager();
        void ctor_CLogTypeDBTaskManager();
        void CleanUp();
        void DBProcess();
        static void Destroy();
        bool GetDBProc();
        bool GetDBTaskConnectionStatus();
        bool GetDBTaskDataStatus();
        bool Init();
        bool InitDB(char* szDBName, char* szDBIP);
        bool InitLogger();
        static struct CLogTypeDBTaskManager* Instance();
        bool IsInitialized();
        void Log(char* fmt);
        void Loop();
        void ProcComplete();
        static void ProcThread(void* pParam);
        bool Push(char byQueryType, char* pcData, uint16_t wSize);
        ~CLogTypeDBTaskManager();
        void dtor_CLogTypeDBTaskManager();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$39C8E6A87FC5B645976D1A9AEBC0CA14.hpp>
#include <$BDF50BE7362FD6198E9BB19DF1C2D17C.hpp>
#include <HINSTANCE__.hpp>


START_ATF_NAMESPACE
    struct _PROPSHEETPAGEW_V2
    {
        unsigned int dwSize;
        unsigned int dwFlags;
        HINSTANCE__ *hInstance;
        $39C8E6A87FC5B645976D1A9AEBC0CA14 ___u3;
        $BDF50BE7362FD6198E9BB19DF1C2D17C ___u4;
        const wchar_t *pszTitle;
        __int64 (WINAPIV *pfnDlgProc)(HWND__ *, unsigned int, unsigned __int64, __int64);
        __int64 lParam;
        unsigned int (WINAPIV *pfnCallback)(HWND__ *, unsigned int, _PROPSHEETPAGEW *);
        unsigned int *pcRefParent;
        const wchar_t *pszHeaderTitle;
        const wchar_t *pszHeaderSubTitle;
    };
END_ATF_NAMESPACE

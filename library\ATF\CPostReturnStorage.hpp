// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPostData.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CPostReturnStorage
    {
        CPostData m_PostData[10];
        int m_nSize;
    public:
        struct CPostData* AddReturnPost(char byErrCode, unsigned int dwPostSerial, char byState, char* wszRecvName, char* wszTitle, char* wszContent, struct _INVENKEY Key, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, uint64_t lnUID);
        CPostReturnStorage();
        void ctor_CPostReturnStorage();
        void DelPostData(unsigned int dwPostSerial);
        struct CPostData* GetPostDataFromInx(int nIndex);
        struct CPostData* GetPostDataFromSerial(unsigned int dwPostSerial);
        int GetReturnPostInx();
        int GetSize();
        void Init();
        ~CPostReturnStorage();
        void dtor_CPostReturnStorage();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CPostReturnStorage, 3128>(), "CPostReturnStorage");
END_ATF_NAMESPACE

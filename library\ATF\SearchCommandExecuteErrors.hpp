// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum SearchCommandExecuteErrors
    {
      SCEE_PATHNOTFOUND = 0x1,
      SCEE_MAXFILESFOUND = 0x2,
      SCEE_INDEXSEARCH = 0x3,
      SCEE_CONSTRAINT = 0x4,
      SCEE_SCOPEMISMATCH = 0x5,
      SCEE_CASESENINDEX = 0x6,
      SCEE_INDEXNOTCOMPLETE = 0x7,
    };
END_ATF_NAMESPACE

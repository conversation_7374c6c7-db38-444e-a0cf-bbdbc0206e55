// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CIP.hpp>
#include <CMonikerFile.hpp>
#include <tagFORMATETC.hpp>


START_ATF_NAMESPACE
    struct  CAsyncMonikerFile : CMonikerFile
    {
        struct _AfxBindStatusCallback *m_pAfxBSCCurrent;
        int m_bStopBindingReceived;
        CIP<IBinding,&IID_IBinding> m_Binding;
        tagFORMATETC *m_pFormatEtc;
    };
END_ATF_NAMESPACE

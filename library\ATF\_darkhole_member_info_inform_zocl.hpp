// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _darkhole_member_info_inform_zocl
    {
        struct  __list
        {
            char wszName[17];
            unsigned int dwSerial;
        };
        unsigned int dwLeaderSerial;
        unsigned __int16 wMemberNum;
        __list List[32];
    public:
        _darkhole_member_info_inform_zocl();
        void ctor__darkhole_member_info_inform_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_darkhole_member_info_inform_zocl, 678>(), "_darkhole_member_info_inform_zocl");
END_ATF_NAMESPACE

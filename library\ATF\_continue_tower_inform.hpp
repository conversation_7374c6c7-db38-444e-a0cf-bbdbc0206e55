// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _continue_tower_inform
    {
        unsigned __int16 wItemSerial;
        unsigned __int16 wTwrRecIndex;
        unsigned __int16 wTwrIndex;
        unsigned int dwTwrSerial;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

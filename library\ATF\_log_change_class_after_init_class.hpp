// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _log_change_class_after_init_class
    {
        unsigned int dwCharacSerial;
        char byType;
        char szPrevClassCode[5];
        char szNextClassCode[5];
        int nClassInitCnt;
        char byLastClassGrade;
        unsigned __int16 dwYear;
        char byMonth;
        char byDay;
        char byHour;
        char byMin;
        char bySec;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_log_change_class_after_init_class, 32>(), "_log_change_class_after_init_class");
END_ATF_NAMESPACE

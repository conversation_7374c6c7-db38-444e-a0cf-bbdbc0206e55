// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CMyTimer.hpp>
#include <CNetIndexList.hpp>
#include <CPlayer.hpp>
#include <CPostData.hpp>
#include <_STORAGE_LIST.hpp>
#include <_STORAGE_POS_INDIV.hpp>


START_ATF_NAMESPACE
    struct CPostSystemManager
    {
        char m_byRegiNum;
        char m_byProcNum;
        CMyTimer m_tmrRegiTime;
        CMyTimer m_tmrProcTime;
        CNetIndexList m_listEmpty;
        CNetIndexList m_listRegist;
        CNetIndexList m_listProc;
        CLogFile *m_pkLogger;
        CPostData *m_PostData;
        __int64 m_tNextWrite;
        int m_nPostProcCountPerDay;
        int m_nPostReturnCountPerDay;
    public:
        CPostSystemManager();
        void ctor_CPostSystemManager();
        char CheckRegister(struct CPlayer* pOne, struct _STORAGE_POS_INDIV* pItemInfo, unsigned int dwGold, struct _STORAGE_LIST::_db_con** pItem);
        void CompletePostReceiverCheck(char* pData);
        void CompleteRegist(char* pData);
        void CompleteSend(char* pData);
        static void Destroy();
        bool Init();
        bool InitLogger();
        bool InsertDefaultPSRecord();
        static struct CPostSystemManager* Instace();
        bool Load();
        void Log(char* fmt);
        void Log(wchar_t* fmt);
        void Loop();
        char PostReceiverCheck(char* pData);
        bool PostRegistryLoad();
        char PostSend(char* pData);
        bool PostSendRequest(struct CPlayer* pOne, char* wszRecvName, char* wszTitle, char* wszContent, struct _STORAGE_POS_INDIV* pItemInfo, unsigned int dwGold, char byRace);
        void SetNextWriteTime();
        bool UpdateDisappearOwnerRecord();
        char UpdateRegist(char* pData);
        ~CPostSystemManager();
        void dtor_CPostSystemManager();
    };
END_ATF_NAMESPACE

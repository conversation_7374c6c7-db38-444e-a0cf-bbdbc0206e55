// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _MEMORY_BASIC_INFORMATION32
    {
        unsigned int BaseAddress;
        unsigned int AllocationBase;
        unsigned int AllocationProtect;
        unsigned int RegionSize;
        unsigned int State;
        unsigned int Protect;
        unsigned int Type;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _d_trade_add_inform_zocl
    {
        char bySlotIndex;
        char byTableCode;
        unsigned __int16 wItemIndex;
        unsigned __int64 dwDurPoint;
        unsigned int dwUdtInfo;
        char byAmount;
        char byEmptyInvenNum;
        char byCsMethod;
        unsigned int dwT;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CThreadLocalObject.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CThreadLocal<AFX_MODULE_THREAD_STATE> : CThreadLocalObject
    {
    };
END_ATF_NAMESPACE
#include <CThreadLocalObject.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CThreadLocal<_AFXCTL_AMBIENT_CACHE> : CThreadLocalObject
    {
    };
END_ATF_NAMESPACE
#include <CThreadLocalObject.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CThreadLocal<_AFX_THREAD_STATE> : CThreadLocalObject
    {
    };
END_ATF_NAMESPACE

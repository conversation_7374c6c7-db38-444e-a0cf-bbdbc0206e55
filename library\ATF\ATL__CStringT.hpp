// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <StrTraitMFC_DLL.hpp>
#include <ATL__CSimpleStringT.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<typename _Ty, typename _TraitMfc = StrTraitMFC_DLL<_Ty>>
        struct  CStringT : CSimpleStringT<_Ty, true>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

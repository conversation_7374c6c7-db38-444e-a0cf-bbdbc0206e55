// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$925CE51DAA86685189BE0A25AD2E1B85.hpp>
#include <SQLINTERVAL.hpp>


START_ATF_NAMESPACE
    struct tagSQL_INTERVAL_STRUCT
    {
        SQLINTERVAL interval_type;
        __int16 interval_sign;
        $925CE51DAA86685189BE0A25AD2E1B85 intval;
    };
END_ATF_NAMESPACE

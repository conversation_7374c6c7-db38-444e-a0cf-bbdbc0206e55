// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $E2F13C0D8C7A5E44CBEA2DDC2BADB614
    {
        unsigned __int16 vt;
        unsigned __int16 wReserved1;
        unsigned __int16 wReserved2;
        unsigned __int16 wReserved3;
        __int64 llVal;
    };    
    static_assert(ATF::checkSize<$E2F13C0D8C7A5E44CBEA2DDC2BADB614, 16>(), "$E2F13C0D8C7A5E44CBEA2DDC2BADB614");
END_ATF_NAMESPACE

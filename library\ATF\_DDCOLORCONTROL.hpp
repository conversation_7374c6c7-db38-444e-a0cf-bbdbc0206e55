// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _DDCOLORCONTROL
    {
        unsigned int dwSize;
        unsigned int dwFlags;
        int lBrightness;
        int lContrast;
        int lHue;
        int lSaturation;
        int lSharpness;
        int lGamma;
        int lColorEnable;
        unsigned int dwReserved1;
    };
END_ATF_NAMESPACE

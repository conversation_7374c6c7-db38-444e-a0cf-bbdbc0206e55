// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CFPS.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CFPSctor_CFPS2_ptr = void (WINAPIV*)(struct CFPS*);
        using CFPSctor_CFPS2_clbk = void (WINAPIV*)(struct CFPS*, CFPSctor_CFPS2_ptr);
        using CFPSCalcFPS4_ptr = void (WINAPIV*)(struct CFPS*);
        using CFPSCalcFPS4_clbk = void (WINAPIV*)(struct CFPS*, CFPSCalcFPS4_ptr);
        using CFPSGetFPS6_ptr = unsigned int (WINAPIV*)(struct CFPS*);
        using CFPSGetFPS6_clbk = unsigned int (WINAPIV*)(struct CFPS*, CFPSGetFPS6_ptr);
        
        using CFPSdtor_CFPS11_ptr = void (WINAPIV*)(struct CFPS*);
        using CFPSdtor_CFPS11_clbk = void (WINAPIV*)(struct CFPS*, CFPSdtor_CFPS11_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

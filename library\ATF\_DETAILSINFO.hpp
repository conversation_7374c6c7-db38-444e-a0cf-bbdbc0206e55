// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ITEMIDLIST.hpp>
#include <_STRRET.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _DETAILSINFO
    {
        _ITEMIDLIST *pidl;
        int fmt;
        int cxChar;
        _STRRET str;
        int iImage;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

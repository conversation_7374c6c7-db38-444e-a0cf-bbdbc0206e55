// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _lt_qry_case_insert_settlementowner_log
    {
        struct _list
        {
            unsigned int dwSerial;
            char wszGuildName[17];
            char byRace;
            unsigned __int16 wRank;
            char byGrade;
            long double dKillPvpPoint;
            long double dGuildBattlePvpPoint;
            unsigned int dwSumLv;
        };
        _list list[6];
    };
END_ATF_NAMESPACE

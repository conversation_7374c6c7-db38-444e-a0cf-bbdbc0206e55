// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagCSPLATFORM.hpp>


START_ATF_NAMESPACE
    struct tagQUERYCONTEXT
    {
        unsigned int dwContext;
        tagCSPLATFORM Platform;
        unsigned int Locale;
        unsigned int dwVersionHi;
        unsigned int dwVersionLo;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_contsave.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_contsavector__qry_case_contsave2_ptr = void (WINAPIV*)(struct _qry_case_contsave*);
        using _qry_case_contsavector__qry_case_contsave2_clbk = void (WINAPIV*)(struct _qry_case_contsave*, _qry_case_contsavector__qry_case_contsave2_ptr);
        using _qry_case_contsavesize4_ptr = int (WINAPIV*)(struct _qry_case_contsave*);
        using _qry_case_contsavesize4_clbk = int (WINAPIV*)(struct _qry_case_contsave*, _qry_case_contsavesize4_ptr);
        
        using _qry_case_contsavedtor__qry_case_contsave6_ptr = void (WINAPIV*)(struct _qry_case_contsave*);
        using _qry_case_contsavedtor__qry_case_contsave6_clbk = void (WINAPIV*)(struct _qry_case_contsave*, _qry_case_contsavedtor__qry_case_contsave6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _OSVERSIONINFOW
    {
        unsigned int dwOSVersionInfoSize;
        unsigned int dwMajorVersion;
        unsigned int dwMinorVersion;
        unsigned int dwBuildNumber;
        unsigned int dwPlatformId;
        wchar_t szCSDVersion[128];
    };
END_ATF_NAMESPACE

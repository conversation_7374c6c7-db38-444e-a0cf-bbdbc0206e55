// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CSyncCSVtbl.hpp>
#include <_RTL_CRITICAL_SECTION.hpp>


START_ATF_NAMESPACE
    struct CSyncCS
    {
        CSyncCSVtbl *vfptr;
        bool m_bUse;
        _RTL_CRITICAL_SECTION m_cs;
    public:
        CSyncCS();
        void ctor_CSyncCS();
        bool IsUse();
        void Lock();
        void SetUse(bool bUse);
        void Unlock();
        ~CSyncCS();
        void dtor_CSyncCS();
    };    
    static_assert(ATF::checkSize<CSyncCS, 56>(), "CSyncCS");
END_ATF_NAMESPACE

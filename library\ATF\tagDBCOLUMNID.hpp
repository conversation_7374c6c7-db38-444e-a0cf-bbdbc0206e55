// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$9B67567B398DC4A2C8B9E4A7785BA05C.hpp>
#include <_GUID.hpp>
#include <tagDBCOLKIND.hpp>


START_ATF_NAMESPACE
    struct tagDBCOLUMNID
    {
        _GUID guid;
        tagDBCOLKIND dwKind;
        $9B67567B398DC4A2C8B9E4A7785BA05C ___u2;
    };
END_ATF_NAMESPACE

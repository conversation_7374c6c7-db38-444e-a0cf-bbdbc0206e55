// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>
#include <CMonster.hpp>
#include <CMonsterAI.hpp>
#include <CMonsterSkill.hpp>
#include <UsStateTBL.hpp>
#include <Us_HFSM.hpp>


START_ATF_NAMESPACE
    struct  DfAIMgr : UsStateTBL
    {
    public:
        static void Action_Attack_OnLoop(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Action_Change_Handler(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Action_Patrol_OnLoop(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Action_Runaway_OnLoop(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Action_Wait_OnLoop(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Assist_OnChange(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Assist_OnLoop(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Atp_Lost_Handler(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Atp_SearchStart_OnLoop(struct Us_HFSM* p, unsigned int dwEvent, void* lpParam);
        static void Atp_Searched_Handler(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void ChangeTargetPos(struct CMonster* pMon, float* pTarPos);
        static int CheckAlienation(struct CMonster* pMon);
        static int CheckEmotionBad(struct CMonster* pMon, struct CMonsterAI* pAI, int nDamage);
        static int CheckGen(struct CMonsterAI* pAI, struct CMonster* pMon);
        static int CheckMonArea_N_ChangeState(struct CMonsterAI* pAI, struct CMonster* pMon, int bAttackState);
        static int CheckSPF(struct CMonsterAI* pAI, struct CMonster* pMon);
        static int CheckSPFDelayTime(struct CMonsterAI* pAI, int nAttackType, unsigned int dwLoopTime);
        static int CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGE(struct CMonsterSkill* pSkill, int nMotiveValue, struct CMonsterAI* pAI, struct CMonster* pMon, struct CCharacter** ppTar);
        static int CheckSPF_MON_MOTIVE_DF(struct CMonsterSkill* pSkill, int nMotiveValue, struct CMonsterAI* pAI, struct CMonster* pMon, struct CCharacter** ppTar);
        static int CheckSPF_MON_MOTIVE_MY_HP_DOWN(struct CMonsterSkill* pSkill, int nMotiveValue, struct CMonsterAI* pAI, struct CMonster* pMon, struct CCharacter** ppTar);
        static int CheckSPF_MON_MOTIVE_OTHER_HP_DOWN(struct CMonsterSkill* pSkill, int nMotiveValue, struct CMonsterAI* pAI, struct CMonster* pMon, struct CCharacter** ppTar);
        static void Condition_OnChange(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Condition_OnLoop(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        DfAIMgr();
        void ctor_DfAIMgr();
        static void Emotion_OnChange(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Emotion_OnLoop(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static struct CCharacter* GetWisdomTarget(int nDstCaseType, struct CMonsterAI* pAI, struct CMonster* pMon);
        static void Mon_SearchStart_OnLoop(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Mon_Searched_Handler(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Mv_Go_OnLoop(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static void Mv_Stop_Handler(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam);
        static int OnDFInitHFSM(struct UsStateTBL* pStateTBL, struct Us_HFSM* pHFSM);
        static void OnDfExternCallFun(struct Us_HFSM* pHFS, unsigned int dwEvent, void* lpParam, int nParam);
        static int OnUsStateTBLInit();
        static bool SearchCharacterPath(struct CMonsterAI* pAI, struct CMonster* pMon, struct CCharacter* pTarget);
        static void SearchPatrollPath(struct CMonsterAI* pAI, struct CMonster* pMon);
        static int UseSkill_Target(struct CMonster* pMon, struct CCharacter* pTarget, struct CMonsterSkill* pSkill);
        ~DfAIMgr();
        void dtor_DfAIMgr();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <FONT2DVERTEX.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using FONT2DVERTEXctor_FONT2DVERTEX1_ptr = int64_t (WINAPIV*)(struct FONT2DVERTEX*);
        using FONT2DVERTEXctor_FONT2DVERTEX1_clbk = int64_t (WINAPIV*)(struct FONT2DVERTEX*, FONT2DVERTEXctor_FONT2DVERTEX1_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

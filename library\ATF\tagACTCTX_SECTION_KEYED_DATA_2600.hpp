// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagACTCTX_SECTION_KEYED_DATA_2600
    {
        unsigned int cbSize;
        unsigned int ulDataFormatVersion;
        void *lpData;
        unsigned int ulLength;
        void *lpSectionGlobalData;
        unsigned int ulSectionGlobalDataLength;
        void *lpSectionBase;
        unsigned int ulSectionTotalLength;
        void *hActCtx;
        unsigned int ulAssemblyRosterIndex;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$166B1F81F6EA96F97683A65F38FB1A59.hpp>
#include <$17B4421FDC73B39569D8A12F584CB67F.hpp>
#include <$2516E53E690D8CC5659AAB7EDC49E664.hpp>
#include <$2CABE618CF1C2625A4479554E4967E0A.hpp>
#include <$31126B8528A05AF3606C6D495FD178E8.hpp>
#include <$48802A31D3D0701BC13CEF9CEA041E7B.hpp>
#include <$4C0EC2258454B893CE739DAE89D8DB7B.hpp>
#include <$50230A970D9734D4E9774CFC619DF0F6.hpp>
#include <$5669F5A4AD19EF2CB42A5602080CB0A6.hpp>
#include <$5CA90CF57237397281FB12BFD52C1905.hpp>
#include <$60E4D37D1F84587CCAA4F5EA0F9453FD.hpp>
#include <$6810C0431CE7B8C44277107BC1E4A02A.hpp>
#include <$6857AE69DB7C952EEFD665431A7B503A.hpp>
#include <$6C39A09D97B880131B2C5BB4943CBB45.hpp>
#include <$6D336DA143C556260FF80C12817B08DB.hpp>
#include <$6DB36323059316E675433BB10D285009.hpp>
#include <$76732A64405C0E287FCB302687644550.hpp>
#include <$7B1C2AA558A72DB3909F7F0B6C8C78B2.hpp>
#include <$886F8AE9D7C8B1B861B6CA58D67B682A.hpp>
#include <$8E3C8731874D1B3BC66617C4DD3163A6.hpp>
#include <$A24CCB9C650A9CC3453F37B8C46067B8.hpp>
#include <$AAA92DA1D6E1B0E8462F69216B62027C.hpp>
#include <$AD8630CBC7A55BCFB532806489E2D4B2.hpp>
#include <$ADBB94A21537A8461C6168A5B871A740.hpp>
#include <$B644145F46FF1F932B106BFA6CC3F6D9.hpp>
#include <$BE56ADCB97A5F80C4E840DC3FCEE04D4.hpp>
#include <$BF8CC88CDF129BD6E4FA4ABA13F521AD.hpp>
#include <$C8872C9E496A5F106B62833CDD5C9B19.hpp>
#include <$CDA73A427DD2A30CDA26CA3394C33CF0.hpp>
#include <$DF102B31B2A9296EC5F201FD7C6F7BC9.hpp>
#include <$E1D71D20C3D01115E20D79FAE9629CE8.hpp>
#include <$E2F13C0D8C7A5E44CBEA2DDC2BADB614.hpp>
#include <$E5FC128E66C26C95254032BA69D36E74.hpp>
#include <$E7F48E2F4EAA3149E57D87E5D1B2C6B8.hpp>
#include <$E898A5260D8380431CCAA47FCC291563.hpp>
#include <$E94952165EEA9EEA990FEB33603E1B19.hpp>
#include <$E9CDCC48CD0BB58E8F361AA17BFDA76E.hpp>
#include <$EA00B0D50EAA1933F67C45009B664198.hpp>
#include <$F28A51B4B40B0D33C72915D739D42B4B.hpp>
#include <$F46D9090672738781A752404F4626ABF.hpp>
#include <$F7167AE7A8ABA03C094C204FB1564A28.hpp>
#include <$F7AD3C731BAE9FE9EC401882837B8DB3.hpp>
#include <$F843BCFCB8B642AED9B77EEE39B126D8.hpp>
#include <$FE5962FE14285F481A616AC7207AB797.hpp>
#include <tagDEC.hpp>


START_ATF_NAMESPACE
    union $185862B522C7C389F20147548A6885AE
    {
        $E2F13C0D8C7A5E44CBEA2DDC2BADB614 __s0;
        tagDEC decVal;
        $6D336DA143C556260FF80C12817B08DB __s2;
        $F28A51B4B40B0D33C72915D739D42B4B __s3;
        $31126B8528A05AF3606C6D495FD178E8 __s4;
        $50230A970D9734D4E9774CFC619DF0F6 __s5;
        $4C0EC2258454B893CE739DAE89D8DB7B __s6;
        $6DB36323059316E675433BB10D285009 __s7;
        $8E3C8731874D1B3BC66617C4DD3163A6 __s8;
        $A24CCB9C650A9CC3453F37B8C46067B8 __s9;
        $E94952165EEA9EEA990FEB33603E1B19 __s10;
        $7B1C2AA558A72DB3909F7F0B6C8C78B2 __s11;
        $60E4D37D1F84587CCAA4F5EA0F9453FD __s12;
        $C8872C9E496A5F106B62833CDD5C9B19 __s13;
        $6810C0431CE7B8C44277107BC1E4A02A __s14;
        $166B1F81F6EA96F97683A65F38FB1A59 __s15;
        $76732A64405C0E287FCB302687644550 __s16;
        $E5FC128E66C26C95254032BA69D36E74 __s17;
        $E9CDCC48CD0BB58E8F361AA17BFDA76E __s18;
        $BF8CC88CDF129BD6E4FA4ABA13F521AD __s19;
        $2CABE618CF1C2625A4479554E4967E0A __s20;
        $AAA92DA1D6E1B0E8462F69216B62027C __s21;
        $F7167AE7A8ABA03C094C204FB1564A28 __s22;
        $AD8630CBC7A55BCFB532806489E2D4B2 __s23;
        $6857AE69DB7C952EEFD665431A7B503A __s24;
        $BE56ADCB97A5F80C4E840DC3FCEE04D4 __s25;
        $DF102B31B2A9296EC5F201FD7C6F7BC9 __s26;
        $F7AD3C731BAE9FE9EC401882837B8DB3 __s27;
        $F843BCFCB8B642AED9B77EEE39B126D8 __s28;
        $FE5962FE14285F481A616AC7207AB797 __s29;
        $6C39A09D97B880131B2C5BB4943CBB45 __s30;
        $E898A5260D8380431CCAA47FCC291563 __s31;
        $48802A31D3D0701BC13CEF9CEA041E7B __s32;
        $B644145F46FF1F932B106BFA6CC3F6D9 __s33;
        $EA00B0D50EAA1933F67C45009B664198 __s34;
        $5CA90CF57237397281FB12BFD52C1905 __s35;
        $E7F48E2F4EAA3149E57D87E5D1B2C6B8 __s36;
        $F46D9090672738781A752404F4626ABF __s37;
        $E1D71D20C3D01115E20D79FAE9629CE8 __s38;
        $5669F5A4AD19EF2CB42A5602080CB0A6 __s39;
        $886F8AE9D7C8B1B861B6CA58D67B682A __s40;
        $ADBB94A21537A8461C6168A5B871A740 __s41;
        $CDA73A427DD2A30CDA26CA3394C33CF0 __s42;
        $2516E53E690D8CC5659AAB7EDC49E664 __s43;
        $17B4421FDC73B39569D8A12F584CB67F __s44;
    };    
    static_assert(ATF::checkSize<$185862B522C7C389F20147548A6885AE, 16>(), "$185862B522C7C389F20147548A6885AE");
END_ATF_NAMESPACE

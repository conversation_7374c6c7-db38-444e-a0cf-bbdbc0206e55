// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSubClassFactory.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderSubClassFactoryctor_CUnmannedTraderSubClassFactory2_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassFactory*);
        using CUnmannedTraderSubClassFactoryctor_CUnmannedTraderSubClassFactory2_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassFactory*, CUnmannedTraderSubClassFactoryctor_CUnmannedTraderSubClassFactory2_ptr);
        using CUnmannedTraderSubClassFactoryCreate4_ptr = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassFactory*, char*, unsigned int);
        using CUnmannedTraderSubClassFactoryCreate4_clbk = struct CUnmannedTraderSubClassInfo* (WINAPIV*)(struct CUnmannedTraderSubClassFactory*, char*, unsigned int, CUnmannedTraderSubClassFactoryCreate4_ptr);
        using CUnmannedTraderSubClassFactoryDestroy6_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassFactory*);
        using CUnmannedTraderSubClassFactoryDestroy6_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassFactory*, CUnmannedTraderSubClassFactoryDestroy6_ptr);
        using CUnmannedTraderSubClassFactoryRegist8_ptr = bool (WINAPIV*)(struct CUnmannedTraderSubClassFactory*, struct CUnmannedTraderSubClassInfo*);
        using CUnmannedTraderSubClassFactoryRegist8_clbk = bool (WINAPIV*)(struct CUnmannedTraderSubClassFactory*, struct CUnmannedTraderSubClassInfo*, CUnmannedTraderSubClassFactoryRegist8_ptr);
        
        using CUnmannedTraderSubClassFactorydtor_CUnmannedTraderSubClassFactory10_ptr = void (WINAPIV*)(struct CUnmannedTraderSubClassFactory*);
        using CUnmannedTraderSubClassFactorydtor_CUnmannedTraderSubClassFactory10_clbk = void (WINAPIV*)(struct CUnmannedTraderSubClassFactory*, CUnmannedTraderSubClassFactorydtor_CUnmannedTraderSubClassFactory10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBilling.hpp>
#include <CNationSettingData.hpp>
#include <CashDbWorker.hpp>
#include <_CashShop_str_fld.hpp>
#include <_NameTxt_fld.hpp>


START_ATF_NAMESPACE
    struct  CNationSettingDataPH : CNationSettingData
    {
    public:
        CNationSettingDataPH();
        void ctor_CNationSettingDataPH();
        bool CheckEnterWorldRequest(int n, char* pBuf);
        struct CBilling* CreateBilling();
        struct CashDbWorker* CreateWorker();
        int GetCashItemPrice(struct _CashShop_str_fld* pFld);
        char* GetItemName(struct _NameTxt_fld* pFld);
        int Init();
        bool IsNormalString(char* szString);
        bool IsNormalString(wchar_t* wszString);
        void Loop();
        bool ReadSystemPass();
    };
END_ATF_NAMESPACE

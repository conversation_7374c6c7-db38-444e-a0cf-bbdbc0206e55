// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _buy_store_request_clzo
    {
        struct  _list
        {
            char byStorageCode;
            unsigned int dwGoodSerial;
            char byAmount;
        };
        unsigned int dwStoreIndex;
        char byBuyNum;
        int bUseNPCLinkIntem;
        _list OfferList[100];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

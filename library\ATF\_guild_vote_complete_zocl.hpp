// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _guild_vote_complete_zocl
    {
        unsigned int dwMatterVoteSynKey;
        char byApprPoint;
        char byOppoPoint;
        bool bPassed;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_result_csi_error_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _result_csi_error_zoclsize2_ptr = int (WINAPIV*)(struct _result_csi_error_zocl*);
        using _result_csi_error_zoclsize2_clbk = int (WINAPIV*)(struct _result_csi_error_zocl*, _result_csi_error_zoclsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $8F2D8B2C55DC009A855120B139156A5D
    {
      _HTTP_VERB_MIN = 0x0,
      HTTP_VERB_POST = 0x0,
      HTTP_VERB_GET = 0x1,
      HTTP_VERB_HEAD = 0x2,
      HTTP_VERB_PUT = 0x3,
      HTTP_VERB_LINK = 0x4,
      HTTP_VERB_DELETE = 0x5,
      HTTP_VERB_UNLINK = 0x6,
      _HTTP_VERB_MAX = 0x6,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMerchant.hpp>
#include <CMoveMapLimitInfoVtbl.hpp>
#include <CMoveMapLimitRightInfo.hpp>
#include <CPlayer.hpp>


START_ATF_NAMESPACE
    struct CMoveMapLimitInfo
    {
        enum OBJ_TYPE
        {
            OT_NONE = 0xFFFFFFFF,
            OT_PORTAL = 0x0,
            MAX_TYPE_NUM = 0x1,
        };
        CMoveMapLimitInfoVtbl *vfptr;
        OBJ_TYPE m_eType;
        unsigned int m_uiInx;
        int m_iMapInx;
        CMerchant *m_pStoreNPC;
    public:
        CMoveMapLimitInfo(unsigned int uiInx, int iType);
        void ctor_CMoveMapLimitInfo(unsigned int uiInx, int iType);
        static struct CMoveMapLimitInfo* Create(unsigned int uiInx, int iType);
        unsigned int GetInx();
        int GetType();
        bool IsEqualLimit(int iType, int iMapInx, unsigned int dwStoreRecordIndex);
        void Load(struct CPlayer* pkPlayer, struct CMoveMapLimitRightInfo* pkRight);
        void LogIn(struct CPlayer* pkPlayer, struct CMoveMapLimitRightInfo* pkRight);
        void LogOut(struct CPlayer* pkPlayer, struct CMoveMapLimitRightInfo* pkRight);
        void Loop();
        ~CMoveMapLimitInfo();
        void dtor_CMoveMapLimitInfo();
    };
END_ATF_NAMESPACE

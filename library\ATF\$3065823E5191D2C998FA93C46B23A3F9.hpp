// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $3065823E5191D2C998FA93C46B23A3F9
    {
      alnum = 0x107,
      alpha = 0x103,
      cntrl = 0x20,
      digit = 0x4,
      graph = 0x117,
      lower = 0x2,
      print = 0x1D7,
      punct = 0x10,
      space = 0x48,
      upper = 0x1,
      xdigit = 0x80,
    };
END_ATF_NAMESPACE

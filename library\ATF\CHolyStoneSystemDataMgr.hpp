// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHolyScheduleData.hpp>
#include <CHolyStoneSaveData.hpp>
#include <CHolyStoneSystem.hpp>


START_ATF_NAMESPACE
    struct  CHolyStoneSystemDataMgr
    {
    public:
        static bool LoadIni(struct CHolyStoneSystem* clsHolyStoneSystem);
        static bool LoadSceduleData(struct CHolyScheduleData* clsDummy);
        static bool LoadStateData(struct CHolyStoneSaveData* clsSaveDummy);
        static bool SaveStateData(struct CHolyStoneSaveData* clsSaveDummy);
    };
END_ATF_NAMESPACE

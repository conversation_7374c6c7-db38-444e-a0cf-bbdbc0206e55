// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CGuild.hpp>
#include <CPlayer.hpp>
#include <GUILD_BATTLE__CGuildBattleSchedule.hpp>
#include <GUILD_BATTLE__CNormalGuildBattle.hpp>
#include <_worlddb_guild_battle_info.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CNormalGuildBattleManager
        {
            bool m_bLoad;
            bool m_bDone;
            unsigned int m_uiMapCnt;
            unsigned int m_uiMaxBattleCnt;
            CNormalGuildBattle **m_ppkNormalBattle;
            CNormalGuildBattle **m_ppkTodayBattle;
            CNormalGuildBattle **m_ppkTomorrowBattle;
        public:
            char Add(struct CGuild* pSrcGuild, struct CGuild* pDestGuild, unsigned int dwStartTime, unsigned int dwElapseTimeCnt, char byNumber, unsigned int dwMapCode);
            void AddComplete(char byRet, unsigned int dwBattleID);
            bool AddDefaultDBRecord();
            CNormalGuildBattleManager();
            void ctor_CNormalGuildBattleManager();
            void CheckGetGravityStone(uint16_t wIndex, unsigned int dwObjSerial, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            void CheckGoal(int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial, int iPortalInx);
            void CheckTakeGravityStone(int iPortalInx, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            void Clear(struct CNormalGuildBattle** ppkStart);
            void Clear();
            static void Destroy();
            void DoDayChangedWork();
            char DropGravityStone(unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            void Flip();
            struct CNormalGuildBattle* GetBattle(unsigned int dwID);
            struct CNormalGuildBattle* GetBattleByGuildSerial(unsigned int dwGuildSerial);
            bool Init();
            static struct CNormalGuildBattleManager* Instance();
            void Join(int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            void JoinGuild(int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            int Kill(unsigned int dwGuildSerial, unsigned int dwSrcCharacSerial, unsigned int dwDestCharacSerial);
            char LeaveGuild(struct CPlayer* pkPlayer);
            bool Load(bool bToday, unsigned int uiDayID, struct CNormalGuildBattle** ppkStart);
            bool Load(int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTodayDayID, int iTomorrow, int iTomorrowDayID);
            bool LoadDBGuildBattleInfo(unsigned int dwStartID, struct _worlddb_guild_battle_info* kInfo);
            void LogIn(int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            void Loop();
            char NetClose(unsigned int dwGuildSerial, unsigned int dwCharacSerial, struct CPlayer* pkPlayer);
            char ProcCheckGetGravityStone(uint16_t wIndex, unsigned int dwObjSerial, unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            char ProcCheckGoal(unsigned int dwGuildSerial, unsigned int dwCharacSerial, int iPortalInx);
            char ProcCheckTakeGravityStone(int iPortalInx, unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            int ProcJoin(unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            bool PushDQSData(unsigned int uiFieldInx, unsigned int uiSLID, struct CNormalGuildBattle* pkBattle, struct CGuildBattleSchedule* pkSchedule);
            bool Save(unsigned int dwID);
            void SendMemberPosition(unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            void SetNextEvent();
            void SetReadyState(struct CNormalGuildBattle** ppkStart);
            char Start(struct CPlayer* pkPlayer, unsigned int dwGuildSerial, unsigned int dwCharacSerial);
            bool UpdateClearGuildBattleDayInfo(unsigned int dwStartSID, unsigned int dwEndSID);
            ~CNormalGuildBattleManager();
            void dtor_CNormalGuildBattleManager();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

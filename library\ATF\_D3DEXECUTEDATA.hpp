// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_D3DSTATUS.hpp>


START_ATF_NAMESPACE
    struct _D3DEXECUTEDATA
    {
        unsigned int dwSize;
        unsigned int dwVertexOffset;
        unsigned int dwVertexCount;
        unsigned int dwInstructionOffset;
        unsigned int dwInstructionLength;
        unsigned int dwHVertexOffset;
        _D3DSTATUS dsStatus;
    };
END_ATF_NAMESPACE

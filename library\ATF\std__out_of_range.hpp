// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__basic_string.hpp>
#include <std__logic_error.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct  out_of_range : logic_error
        {
        public:
            out_of_range(struct basic_string<char,char_traits<char>,allocator<char> >* _Message);
            void ctor_out_of_range(struct basic_string<char,char_traits<char>,allocator<char> >* _Message);
            out_of_range(struct out_of_range* __that);
            void ctor_out_of_range(struct out_of_range* __that);
            ~out_of_range();
            void dtor_out_of_range();
        };    
        static_assert(ATF::checkSize<std::out_of_range, 72>(), "std::out_of_range");
    }; // end namespace std
END_ATF_NAMESPACE

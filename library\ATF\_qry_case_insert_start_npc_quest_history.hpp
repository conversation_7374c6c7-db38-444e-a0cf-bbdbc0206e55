// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_insert_start_npc_quest_history
    {
        unsigned int dwSerial;
        char szQuestCode[64];
        char byLevel;
        __int64 nEndTime;
        unsigned int dwIndex;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

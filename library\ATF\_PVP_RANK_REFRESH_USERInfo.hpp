// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PVP_RANK_REFRESH_USER.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _PVP_RANK_REFRESH_USERInit2_ptr = void (WINAPIV*)(struct _PVP_RANK_REFRESH_USER*);
        using _PVP_RANK_REFRESH_USERInit2_clbk = void (WINAPIV*)(struct _PVP_RANK_REFRESH_USER*, _PVP_RANK_REFRESH_USERInit2_ptr);
        using _PVP_RANK_REFRESH_USERIsFilled4_ptr = bool (WINAPIV*)(struct _PVP_RANK_REFRESH_USER*);
        using _PVP_RANK_REFRESH_USERIsFilled4_clbk = bool (WINAPIV*)(struct _PVP_RANK_REFRESH_USER*, _PVP_RANK_REFRESH_USERIsFilled4_ptr);
        using _PVP_RANK_REFRESH_USERSetData6_ptr = void (WINAPIV*)(struct _PVP_RANK_REFRESH_USER*, unsigned int, char, char);
        using _PVP_RANK_REFRESH_USERSetData6_clbk = void (WINAPIV*)(struct _PVP_RANK_REFRESH_USER*, unsigned int, char, char, _PVP_RANK_REFRESH_USERSetData6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

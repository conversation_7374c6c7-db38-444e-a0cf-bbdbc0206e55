// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCheckSumGuildConverter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CCheckSumGuildConverterConvert2_ptr = void (WINAPIV*)(struct CCheckSumGuildConverter*, long double, long double, struct CCheckSumGuildData*);
        using CCheckSumGuildConverterConvert2_clbk = void (WINAPIV*)(struct CCheckSumGuildConverter*, long double, long double, struct CCheckSumGuildData*, CCheckSumGuildConverterConvert2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

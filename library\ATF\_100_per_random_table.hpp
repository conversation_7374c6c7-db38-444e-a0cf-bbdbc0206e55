// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _100_per_random_table
    {
        unsigned __int16 m_wCurTable;
        unsigned __int16 m_wCurPoint;
    public:
        uint16_t GetRand();
        _100_per_random_table();
        void ctor__100_per_random_table();
        void reset();
    };    
    static_assert(ATF::checkSize<_100_per_random_table, 4>(), "_100_per_random_table");
END_ATF_NAMESPACE

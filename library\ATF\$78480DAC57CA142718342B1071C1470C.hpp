// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_M128.hpp>


START_ATF_NAMESPACE
    struct $78480DAC57CA142718342B1071C1470C
    {
        _M128 Header[2];
        _M128 Legacy[8];
        _M128 Xmm0;
        _M128 Xmm1;
        _M128 Xmm2;
        _M128 Xmm3;
        _M128 Xmm4;
        _M128 Xmm5;
        _M128 Xmm6;
        _M128 Xmm7;
        _M128 Xmm8;
        _M128 Xmm9;
        _M128 Xmm10;
        _M128 Xmm11;
        _M128 Xmm12;
        _M128 Xmm13;
        _M128 Xmm14;
        _M128 Xmm15;
    };    
    static_assert(ATF::checkSize<$78480DAC57CA142718342B1071C1470C, 416>(), "$78480DAC57CA142718342B1071C1470C");
END_ATF_NAMESPACE

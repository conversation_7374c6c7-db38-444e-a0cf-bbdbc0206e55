// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AP_BatterySlot.hpp>
#include <CCharacter.hpp>
#include <CLogFile.hpp>
#include <_STORAGE_LIST.hpp>
#include <_qry_case_update_mineore.hpp>
#include <_personal_amine_mineore_zocl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  AutominePersonal : CCharacter
    {
        bool m_bDBLoad;
        bool m_bOpenUI_Inven;
        bool m_bOpenUI_Battery;
        bool m_bInstalled;
        bool m_bInvenFull;
        bool m_bStart;
        char m_bySelOre;
        unsigned __int16 m_wItemSerial;
        char m_byFilledSlotCnt;
        unsigned int m_dwNextSendTime_CurState;
        char m_dwDelaySec;
        unsigned int m_dwDelay;
        unsigned int m_dwNextMineTime;
        unsigned int m_dwChangeSendTime;
        int m_nMaxHP;
        _STORAGE_LIST::_db_con *m_pItem;
        struct CPlayer *m_pOwner;
        char m_byUseBattery;
        AP_BatterySlot *m_pBatterySlot;
        bool m_bChanged;
        _personal_amine_mineore_zocl m_changed_packet;
        unsigned int m_dwMineCount[15];
        _qry_case_update_mineore m_update_mineore_old;
        _qry_case_update_mineore m_update_mineore_new;
        CLogFile m_logProcess;
        CLogFile m_logSysErr;
    public:
        AutominePersonal();
        void ctor_AutominePersonal();
        int GetDefFC(int nAttactPart, struct CCharacter* pAttChar, int* pnConvertPart);
        float GetDefFacing(int nPart);
        float GetDefGap(int nPart);
        int GetHP();
        int GetMaxHP();
        int GetObjRace();
        struct CPlayer* GetOwner();
        bool IsBeAttackedAble(bool bFirst);
        bool IsBeDamagedAble(struct CCharacter* pAtter);
        void LoadDBComplete();
        void Loop();
        void SendMsg_FixPosition(int n);
        int SetDamage(int nDam, struct CCharacter* pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn);
        bool do_automine(unsigned int dwTime);
        bool extract_battery(char bySlotIdx, struct _STORAGE_LIST::_db_con* pBattery);
        unsigned int get_battery(int n);
        unsigned int get_battery();
        struct _STORAGE_LIST::_db_con* get_item();
        uint16_t get_itemserial();
        unsigned int get_objserial();
        struct CPlayer* get_owner();
        unsigned int get_ownerserial();
        bool initialize(uint16_t wIndex);
        bool insert_battery(char bySlotIdx, uint16_t wItemSerial);
        bool is_installed();
        bool is_run();
        void make_minepacket(uint16_t wItemIndex, uint16_t wItemSerial, char byStorageIndex, uint16_t nNewOre, unsigned int dwDur);
        bool regist_to_map(struct CPlayer* pOne, struct _STORAGE_LIST::_db_con* pDstItem, char byDummyIndex, unsigned int dwObjSerial, float fDelayProf);
        void send_attacked();
        void send_changed_packet(int n);
        void send_current_state();
        void send_ecode(char byCode);
        void send_installed();
        void set_delay(unsigned int dwDelay);
        void set_delaysec(unsigned int dwDS);
        void set_openUI_Inven(bool bFlag);
        void set_openUI_battery(bool bFlag);
        void set_selore(char bySelOre);
        void set_work(bool bWork);
        char sub_battery(unsigned int dwUsed);
        void sub_filledslot();
        bool unregist_from_map(char byDestroyType);
        ~AutominePersonal();
        void dtor_AutominePersonal();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<AutominePersonal, 0xe70>(), "AutominePersonal");
END_ATF_NAMESPACE

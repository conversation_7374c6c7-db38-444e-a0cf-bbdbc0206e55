// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTime.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct CFileStatus
    {
        ATL::CTime m_ctime;
        ATL::CTime m_mtime;
        ATL::CTime m_atime;
        unsigned __int64 m_size;
        char m_attribute;
        char _m_padding;
        char m_szFullName[260];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

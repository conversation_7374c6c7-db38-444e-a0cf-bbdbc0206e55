// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_MULTI_BLOCK.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _MULTI_BLOCKctor__MULTI_BLOCK2_ptr = void (WINAPIV*)(struct _MULTI_BLOCK*);
        using _MULTI_BLOCKctor__MULTI_BLOCK2_clbk = void (WINAPIV*)(struct _MULTI_BLOCK*, _MULTI_BLOCKctor__MULTI_BLOCK2_ptr);
        
        using _MULTI_BLOCKdtor__MULTI_BLOCK6_ptr = void (WINAPIV*)(struct _MULTI_BLOCK*);
        using _MULTI_BLOCKdtor__MULTI_BLOCK6_clbk = void (WINAPIV*)(struct _MULTI_BLOCK*, _MULTI_BLOCKdtor__MULTI_BLOCK6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryGB.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryGBctor_CNationSettingFactoryGB2_ptr = void (WINAPIV*)(struct CNationSettingFactoryGB*);
        using CNationSettingFactoryGBctor_CNationSettingFactoryGB2_clbk = void (WINAPIV*)(struct CNationSettingFactoryGB*, CNationSettingFactoryGBctor_CNationSettingFactoryGB2_ptr);
        using CNationSettingFactoryGBCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryGB*, int, char*, bool);
        using CNationSettingFactoryGBCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryGB*, int, char*, bool, CNationSettingFactoryGBCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

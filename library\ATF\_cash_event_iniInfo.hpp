// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_cash_event_ini.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _cash_event_inictor__cash_event_ini2_ptr = void (WINAPIV*)(struct _cash_event_ini*);
        using _cash_event_inictor__cash_event_ini2_clbk = void (WINAPIV*)(struct _cash_event_ini*, _cash_event_inictor__cash_event_ini2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum BasicType
    {
      btNoType = 0x0,
      btVoid = 0x1,
      btChar = 0x2,
      btWChar = 0x3,
      btInt = 0x6,
      btUInt = 0x7,
      btFloat = 0x8,
      btBCD = 0x9,
      btBool = 0xA,
      btLong = 0xD,
      btULong = 0xE,
      btCurrency = 0x19,
      btDate = 0x1A,
      btVariant = 0x1B,
      btComplex = 0x1C,
      btBit = 0x1D,
      btBSTR = 0x1E,
      btHresult = 0x1F,
    };
END_ATF_NAMESPACE

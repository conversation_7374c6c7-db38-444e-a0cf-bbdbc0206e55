// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    struct tagDELETEITEMSTRUCT
    {
        unsigned int CtlType;
        unsigned int CtlID;
        unsigned int itemID;
        HWND__ *hwndItem;
        unsigned __int64 itemData;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_monster_sp_group.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _monster_sp_groupctor__monster_sp_group2_ptr = void (WINAPIV*)(struct _monster_sp_group*);
        using _monster_sp_groupctor__monster_sp_group2_clbk = void (WINAPIV*)(struct _monster_sp_group*, _monster_sp_groupctor__monster_sp_group2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

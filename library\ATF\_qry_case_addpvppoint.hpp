// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_addpvppoint
    {
        unsigned int dwSerial;
        unsigned int dwPoint;
        unsigned int dwCashBag;
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_qry_case_addpvppoint, 12>(), "_qry_case_addpvppoint");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HACKSHEILD_PARAM_ANTICP.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using HACKSHEILD_PARAM_ANTICPCheckClient2_ptr = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*);
        using HACKSHEILD_PARAM_ANTICPCheckClient2_clbk = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, HACKSHEILD_PARAM_ANTICPCheckClient2_ptr);
        
        using HACKSHEILD_PARAM_ANTICPctor_HACKSHEILD_PARAM_ANTICP4_ptr = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*);
        using HACKSHEILD_PARAM_ANTICPctor_HACKSHEILD_PARAM_ANTICP4_clbk = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, H<PERSON><PERSON>HEILD_PARAM_ANTICPctor_HACKSHEILD_PARAM_ANTICP4_ptr);
        using HACKSHEILD_PARAM_ANTICPInit6_ptr = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*);
        using HACKSHEILD_PARAM_ANTICPInit6_clbk = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, HACKSHEILD_PARAM_ANTICPInit6_ptr);
        using HACKSHEILD_PARAM_ANTICPIsLogPass8_ptr = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*);
        using HACKSHEILD_PARAM_ANTICPIsLogPass8_clbk = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, HACKSHEILD_PARAM_ANTICPIsLogPass8_ptr);
        using HACKSHEILD_PARAM_ANTICPKick10_ptr = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, char, unsigned int);
        using HACKSHEILD_PARAM_ANTICPKick10_clbk = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, char, unsigned int, HACKSHEILD_PARAM_ANTICPKick10_ptr);
        using HACKSHEILD_PARAM_ANTICPOnCheckSession_FirstVerify12_ptr = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, int);
        using HACKSHEILD_PARAM_ANTICPOnCheckSession_FirstVerify12_clbk = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, int, HACKSHEILD_PARAM_ANTICPOnCheckSession_FirstVerify12_ptr);
        using HACKSHEILD_PARAM_ANTICPOnConnect14_ptr = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, int);
        using HACKSHEILD_PARAM_ANTICPOnConnect14_clbk = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, int, HACKSHEILD_PARAM_ANTICPOnConnect14_ptr);
        using HACKSHEILD_PARAM_ANTICPOnDisConnect16_ptr = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*);
        using HACKSHEILD_PARAM_ANTICPOnDisConnect16_clbk = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, HACKSHEILD_PARAM_ANTICPOnDisConnect16_ptr);
        using HACKSHEILD_PARAM_ANTICPOnLoop18_ptr = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*);
        using HACKSHEILD_PARAM_ANTICPOnLoop18_clbk = void (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, HACKSHEILD_PARAM_ANTICPOnLoop18_ptr);
        using HACKSHEILD_PARAM_ANTICPOnRecvSession20_ptr = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, struct CHackShieldExSystem*, int, char, uint64_t, char*);
        using HACKSHEILD_PARAM_ANTICPOnRecvSession20_clbk = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, struct CHackShieldExSystem*, int, char, uint64_t, char*, HACKSHEILD_PARAM_ANTICPOnRecvSession20_ptr);
        using HACKSHEILD_PARAM_ANTICPOnRecvSession_ClientCheckSum_Response22_ptr = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, uint64_t, char*);
        using HACKSHEILD_PARAM_ANTICPOnRecvSession_ClientCheckSum_Response22_clbk = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, uint64_t, char*, HACKSHEILD_PARAM_ANTICPOnRecvSession_ClientCheckSum_Response22_ptr);
        using HACKSHEILD_PARAM_ANTICPOnRecvSession_ClientCrc_Response24_ptr = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, uint64_t, char*);
        using HACKSHEILD_PARAM_ANTICPOnRecvSession_ClientCrc_Response24_clbk = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, uint64_t, char*, HACKSHEILD_PARAM_ANTICPOnRecvSession_ClientCrc_Response24_ptr);
        using HACKSHEILD_PARAM_ANTICPOnRecvSession_ServerCheckSum_Request26_ptr = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, int);
        using HACKSHEILD_PARAM_ANTICPOnRecvSession_ServerCheckSum_Request26_clbk = bool (WINAPIV*)(struct HACKSHEILD_PARAM_ANTICP*, int, HACKSHEILD_PARAM_ANTICPOnRecvSession_ServerCheckSum_Request26_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

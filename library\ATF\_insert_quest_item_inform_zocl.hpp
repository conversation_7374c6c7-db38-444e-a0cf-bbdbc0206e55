// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _insert_quest_item_inform_zocl
    {
        char byTableCode;
        unsigned __int16 wItemIndex;
        unsigned int dwDurPoint;
        unsigned int dwLv;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _mastery_up_data
    {
        bool bUpdate;
        char byCode;
        char byIndex;
        char byMastery;
    public:
        _mastery_up_data();
        void ctor__mastery_up_data();
        void init();
        void set(char code, char index, char mastery);
    };    
    static_assert(ATF::checkSize<_mastery_up_data, 4>(), "_mastery_up_data");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HDC__.hpp>
#include <HINSTANCE__.hpp>
#include <HWND__.hpp>
#include <IUnknown.hpp>
#include <tagPRINTPAGERANGE.hpp>


START_ATF_NAMESPACE
    struct tagPDEXW
    {
        unsigned int lStructSize;
        HWND__ *hwndOwner;
        void *hDevMode;
        void *hDevNames;
        HDC__ *hDC;
        unsigned int Flags;
        unsigned int Flags2;
        unsigned int ExclusionFlags;
        unsigned int nPageRanges;
        unsigned int nMaxPageRanges;
        tagPRINTPAGERANGE *lpPageRanges;
        unsigned int nMinPage;
        unsigned int nMaxPage;
        unsigned int nCopies;
        HINSTANCE__ *hInstance;
        const wchar_t *lpPrintTemplateName;
        IUnknown *lpCallback;
        unsigned int nPropertyPages;
        struct _PSP **lphPropertyPages;
        unsigned int nStartPage;
        unsigned int dwResultAction;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_post_serial_check.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_post_serial_checkctor__qry_case_post_serial_check2_ptr = void (WINAPIV*)(struct _qry_case_post_serial_check*);
        using _qry_case_post_serial_checkctor__qry_case_post_serial_check2_clbk = void (WINAPIV*)(struct _qry_case_post_serial_check*, _qry_case_post_serial_checkctor__qry_case_post_serial_check2_ptr);
        using _qry_case_post_serial_checksize4_ptr = int (WINAPIV*)(struct _qry_case_post_serial_check*);
        using _qry_case_post_serial_checksize4_clbk = int (WINAPIV*)(struct _qry_case_post_serial_check*, _qry_case_post_serial_checksize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

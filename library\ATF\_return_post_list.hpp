// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _return_post_list
    {
        struct __list
        {
            unsigned int dwSerial;
            char byState;
            char wszRecvName[17];
            char wszTitle[21];
            char wszContent[201];
            int nK;
            unsigned __int64 dwDur;
            unsigned int dwUpt;
            unsigned __int64 lnUID;
            unsigned int dwGold;
            char byErr;
        };
        bool bContinue;
        unsigned int dwCount;
        __list List[10];
    };
END_ATF_NAMESPACE

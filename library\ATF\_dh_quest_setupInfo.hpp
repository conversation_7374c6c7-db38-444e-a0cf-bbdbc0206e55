// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dh_quest_setup.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _dh_quest_setupSearchMissionFromTitle2_ptr = struct _dh_mission_setup* (WINAPIV*)(struct _dh_quest_setup*, char*);
        using _dh_quest_setupSearchMissionFromTitle2_clbk = struct _dh_mission_setup* (WINAPIV*)(struct _dh_quest_setup*, char*, _dh_quest_setupSearchMissionFromTitle2_ptr);
        using _dh_quest_setupSetRealBoss4_ptr = void (WINAPIV*)(struct _dh_quest_setup*, bool);
        using _dh_quest_setupSetRealBoss4_clbk = void (WINAPIV*)(struct _dh_quest_setup*, bool, _dh_quest_setupSetRealBoss4_ptr);
        
        using _dh_quest_setupctor__dh_quest_setup6_ptr = void (WINAPIV*)(struct _dh_quest_setup*);
        using _dh_quest_setupctor__dh_quest_setup6_clbk = void (WINAPIV*)(struct _dh_quest_setup*, _dh_quest_setupctor__dh_quest_setup6_ptr);
        
        using _dh_quest_setupdtor__dh_quest_setup11_ptr = void (WINAPIV*)(struct _dh_quest_setup*);
        using _dh_quest_setupdtor__dh_quest_setup11_clbk = void (WINAPIV*)(struct _dh_quest_setup*, _dh_quest_setupdtor__dh_quest_setup11_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

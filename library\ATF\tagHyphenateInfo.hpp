// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  tagHyphenateInfo
    {
        __int16 cbSize;
        __int16 dxHyphenateZone;
        void (WINAPIV *pfnHyphenate)(wchar_t *, unsigned __int16, int, hyphresult *);
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

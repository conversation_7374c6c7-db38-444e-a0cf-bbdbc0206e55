// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _notify_race_boss_winrate_zocl
    {
        char byTotalCnt;
        char byCurWinCnt;
        unsigned int dwAccTotalCnt;
        unsigned int dwAccWinCnt;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_notify_race_boss_winrate_zocl, 10>(), "_notify_race_boss_winrate_zocl");
END_ATF_NAMESPACE

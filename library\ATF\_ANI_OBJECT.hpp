// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_POS_TRACK.hpp>
#include <_ROT_TRACK.hpp>
#include <_SCALE_TRACK.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _ANI_OBJECT
    {
        char ObjectName[64];
        char ParentName[64];
        unsigned __int16 flag;
        unsigned __int16 parent;
        int frames;
        int Pos_cnt;
        int Rot_cnt;
        int Scale_cnt;
        float scale[3];
        float scale_quat[4];
        float pos[3];
        float quat[4];
        _SCALE_TRACK *Scale;
        _POS_TRACK *Pos;
        _ROT_TRACK *Rot;
        char AniFrameCache;
        float s_matrix[4][4];
        float now_frame;
        float f_matrix[4][4];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

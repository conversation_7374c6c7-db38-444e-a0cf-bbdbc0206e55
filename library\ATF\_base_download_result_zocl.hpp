// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _base_download_result_zocl
    {
        struct _list_equip
        {
            char sTableCode;
             unsigned __int16 wItemIndex;
             unsigned int dwUptInfo;
            char byCsMethod;
            unsigned int dwT;
        };
        struct  _list_embellish
        {
            char sTableCode;
            unsigned __int16 wItemIndex;
            unsigned __int16 wNum;
            char sClientIndex;
            char byCsMethod;
            unsigned int dwT;
        };
        char byRetCode;
        unsigned __int16 wZoneIndex;
        unsigned int dwExpRate;
        unsigned __int16 wClassHistory[3];
        unsigned __int16 wHP;
        unsigned __int16 wSP;
        unsigned __int16 wFP;
        long double dPvpPoint;
        long double dPvpCashBag;
        long double dPvpTempCash;
        unsigned int dwPvpRank;
        char byPvpClass;
        char by<PERSON><PERSON><PERSON><PERSON>;
        unsigned __int16 wXorKey;
        unsigned __int16 wMaxHP;
        unsigned __int16 wMaxSP;
        unsigned __int16 wMaxFP;
        unsigned __int16 wMapIndex;
        float fPos[3];
        _list_equip EquipList[8];
        _list_embellish EmbellishList[7];
        char byHolyMasterState;
        unsigned int dwGuildSerial;
        char byGuildGrade;
        char byEffectValue[2];
        char byUseTrunkSlotNum;
        unsigned __int16 wMaxDP;
        unsigned __int16 wDP;
        char byHonorGuildRank;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_base_download_result_zocl, 258>(), "_base_download_result_zocl");
END_ATF_NAMESPACE

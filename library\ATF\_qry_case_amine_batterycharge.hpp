// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _qry_case_amine_batterycharge
    {
        char bySubQryCase;
        char byCollisionType;
        char byRace;
        unsigned int dwGuildSerial;
        unsigned int dwBattery;
        unsigned __int16 wIndex;
        unsigned int in_master;
        int in_charge;
        int in_gold;
        long double out_totaldalant;
        long double out_totalgold;
        char byDate[4];
        char byProcRet;
    public:
        _qry_case_amine_batterycharge();
        void ctor__qry_case_amine_batterycharge();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_amine_batterycharge, 46>(), "_qry_case_amine_batterycharge");
END_ATF_NAMESPACE

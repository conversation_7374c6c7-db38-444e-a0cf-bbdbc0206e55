// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetIndexList.hpp>
#include <Task.hpp>


START_ATF_NAMESPACE
    struct TaskPool
    {
        enum RCODE
        {
            succeed = 0x0,
            list_is_empty = 0xFFFFFFFF,
            failed_popnode = 0xFFFFFFFE,
            failed_pushnode = 0xFFFFFFFD,
            failed_create_taskarry = 0xFFFFFFFC,
            failed_set_emptylist = 0xFFFFFFFB,
            failed_set_waitlist = 0xFFFFFFFA,
            failed_set_completelist = 0xFFFFFFF9,
            failed_init_indexlist = 0xFFFFFFFA,
        };
        bool _bInit;
        CNetIndexList _listEmptyIdx;
        CNetIndexList _listRegedIdx;
        CNetIndexList _listCompleteIdx;
        int _nMaxTskNum;
        Task *_pTsks;
    public:
        RCODE Initialize(int nTskMaxNum, int nMaxTskSize);
        bool IsEmpty();
        struct Task* PopCompleteTsk(unsigned int* nIdx);
        struct Task* PopWaitTsk(unsigned int* nIdx);
        RCODE PushCompleteTsk(unsigned int nIdx);
        RCODE PushEmptyTsk(unsigned int nIdx);
        RCODE PushWaitTsk(int nTaskCode, char* p, uint64_t size);
        void Release();
        TaskPool();
        void ctor_TaskPool();
        bool _create_task(int nMaxTskSize);
        bool _init_index_lists();
    };
END_ATF_NAMESPACE

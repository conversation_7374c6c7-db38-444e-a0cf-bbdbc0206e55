// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_event_respawn.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _event_respawnctor__event_respawn2_ptr = void (WINAPIV*)(struct _event_respawn*);
        using _event_respawnctor__event_respawn2_clbk = void (WINAPIV*)(struct _event_respawn*, _event_respawnctor__event_respawn2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

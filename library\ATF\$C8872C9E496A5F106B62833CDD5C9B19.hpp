// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IDispatch.hpp>


START_ATF_NAMESPACE
    struct $C8872C9E496A5F106B62833CDD5C9B19
    {
        BYTE gap0[8];
        IDispatch *pdispVal;
    };    
    static_assert(ATF::checkSize<$C8872C9E496A5F106B62833CDD5C9B19, 16>(), "$C8872C9E496A5F106B62833CDD5C9B19");
END_ATF_NAMESPACE

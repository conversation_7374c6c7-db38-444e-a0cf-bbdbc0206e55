// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union $25C8602110B22A2EB54BB3EE6752756B
    {
        float nx;
        float normal[1];
    };    
    static_assert(ATF::checkSize<$25C8602110B22A2EB54BB3EE6752756B, 4>(), "$25C8602110B22A2EB54BB3EE6752756B");
END_ATF_NAMESPACE

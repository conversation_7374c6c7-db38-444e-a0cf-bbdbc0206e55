// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DDSCAPS.hpp>
#include <_DDSCAPS2.hpp>


START_ATF_NAMESPACE
    struct _DDCAPS_DX7
    {
        unsigned int dwSize;
        unsigned int dwCaps;
        unsigned int dwCaps2;
        unsigned int dwCKeyCaps;
        unsigned int dwFXCaps;
        unsigned int dwFXAlphaCaps;
        unsigned int dwPalCaps;
        unsigned int dwSVCaps;
        unsigned int dwAlphaBltConstBitDepths;
        unsigned int dwAlphaBltPixelBitDepths;
        unsigned int dwAlphaBltSurfaceBitDepths;
        unsigned int dwAlphaOverlayConstBitDepths;
        unsigned int dwAlphaOverlayPixelBitDepths;
        unsigned int dwAlphaOverlaySurfaceBitDepths;
        unsigned int dwZBufferBitDepths;
        unsigned int dwVidMemTotal;
        unsigned int dwVidMemFree;
        unsigned int dwMaxVisibleOverlays;
        unsigned int dwCurrVisibleOverlays;
        unsigned int dwNumFourCCCodes;
        unsigned int dwAlignBoundarySrc;
        unsigned int dwAlignSizeSrc;
        unsigned int dwAlignBoundaryDest;
        unsigned int dwAlignSizeDest;
        unsigned int dwAlignStrideAlign;
        unsigned int dwRops[8];
        _DDSCAPS ddsOldCaps;
        unsigned int dwMinOverlayStretch;
        unsigned int dwMaxOverlayStretch;
        unsigned int dwMinLiveVideoStretch;
        unsigned int dwMaxLiveVideoStretch;
        unsigned int dwMinHwCodecStretch;
        unsigned int dwMaxHwCodecStretch;
        unsigned int dwReserved1;
        unsigned int dwReserved2;
        unsigned int dwReserved3;
        unsigned int dwSVBCaps;
        unsigned int dwSVBCKeyCaps;
        unsigned int dwSVBFXCaps;
        unsigned int dwSVBRops[8];
        unsigned int dwVSBCaps;
        unsigned int dwVSBCKeyCaps;
        unsigned int dwVSBFXCaps;
        unsigned int dwVSBRops[8];
        unsigned int dwSSBCaps;
        unsigned int dwSSBCKeyCaps;
        unsigned int dwSSBFXCaps;
        unsigned int dwSSBRops[8];
        unsigned int dwMaxVideoPorts;
        unsigned int dwCurrVideoPorts;
        unsigned int dwSVBCaps2;
        unsigned int dwNLVBCaps;
        unsigned int dwNLVBCaps2;
        unsigned int dwNLVBCKeyCaps;
        unsigned int dwNLVBFXCaps;
        unsigned int dwNLVBRops[8];
        _DDSCAPS2 ddsCaps;
    };
END_ATF_NAMESPACE

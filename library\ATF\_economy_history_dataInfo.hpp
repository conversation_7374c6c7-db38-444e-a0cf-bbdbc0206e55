// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_economy_history_data.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _economy_history_dataInit2_ptr = void (WINAPIV*)(struct _economy_history_data*);
        using _economy_history_dataInit2_clbk = void (WINAPIV*)(struct _economy_history_data*, _economy_history_dataInit2_ptr);
        
        using _economy_history_datactor__economy_history_data4_ptr = void (WINAPIV*)(struct _economy_history_data*);
        using _economy_history_datactor__economy_history_data4_clbk = void (WINAPIV*)(struct _economy_history_data*, _economy_history_datactor__economy_history_data4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

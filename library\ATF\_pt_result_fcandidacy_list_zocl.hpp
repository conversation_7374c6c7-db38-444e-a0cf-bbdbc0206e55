// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pt_result_fcandidacy_list_zocl
    {
        struct  __candi_info
        {
            char byGrade;
            long double dPvpPoint;
            unsigned int dwWinCnt;
            char wszAvatorName[17];
            char wszGuildName[17];
        };
        char byCnt;
        __candi_info Candidacy[500];
    public:
        _pt_result_fcandidacy_list_zocl();
        void ctor__pt_result_fcandidacy_list_zocl();
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

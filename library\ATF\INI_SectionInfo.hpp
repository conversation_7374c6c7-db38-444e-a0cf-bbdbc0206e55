// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <INI_Section.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using INI_SectionGetKey2_ptr = struct INI_Key* (WINAPIV*)(struct INI_Section*, char*);
        using INI_SectionGetKey2_clbk = struct INI_Key* (WINAPIV*)(struct INI_Section*, char*, INI_SectionGetKey2_ptr);
        
        using INI_Sectionctor_INI_Section4_ptr = void (WINAPIV*)(struct INI_Section*);
        using INI_Sectionctor_INI_Section4_clbk = void (WINAPIV*)(struct INI_Section*, INI_Sectionctor_INI_Section4_ptr);
        
        using INI_Sectiondtor_INI_Section8_ptr = void (WINAPIV*)(struct INI_Section*);
        using INI_Sectiondtor_INI_Section8_clbk = void (WINAPIV*)(struct INI_Section*, INI_Sectiondtor_INI_Section8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

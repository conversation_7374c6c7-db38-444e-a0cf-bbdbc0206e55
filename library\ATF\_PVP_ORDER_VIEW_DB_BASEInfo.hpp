// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PVP_ORDER_VIEW_DB_BASE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _PVP_ORDER_VIEW_DB_BASEInit2_ptr = void (WINAPIV*)(struct _PVP_ORDER_VIEW_DB_BASE*);
        using _PVP_ORDER_VIEW_DB_BASEInit2_clbk = void (WINAPIV*)(struct _PVP_ORDER_VIEW_DB_BASE*, _PVP_ORDER_VIEW_DB_BASEInit2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

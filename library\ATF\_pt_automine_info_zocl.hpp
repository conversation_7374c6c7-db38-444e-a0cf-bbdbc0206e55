// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DB_LOAD_AUTOMINE_MACHINE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pt_automine_info_zocl
    {
        _DB_LOAD_AUTOMINE_MACHINE INFO;
    public:
        _pt_automine_info_zocl();
        void ctor__pt_automine_info_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_pt_automine_info_zocl, 977>(), "_pt_automine_info_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _result_csi_buy_zocl
    {
        struct __item
        {
            char byTblCode;
             unsigned __int16 wItemIdx;
             unsigned int dwDur;
             unsigned int dwUp;
             unsigned int dwItemSerial;
            char byCsMethod;
            unsigned int dwT;
        };
        int nCashAmount;
        char nNum;
        bool bAdjustDiscount;
        __item item[40];
    public:
        _result_csi_buy_zocl();
        void ctor__result_csi_buy_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_result_csi_buy_zocl, 806>(), "_result_csi_buy_zocl");
END_ATF_NAMESPACE

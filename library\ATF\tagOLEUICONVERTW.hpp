// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <HRSRC__.hpp>
#include <HWND__.hpp>
#include <_GUID.hpp>



START_ATF_NAMESPACE
    struct tagOLEUICONVERTW
    {
        unsigned int cbStruct;
        unsigned int dwFlags;
        HWND__ *hWndOwner;
        const wchar_t *lpszCaption;
        unsigned int (WINAPIV *lpfnHook)(HWND__ *, unsigned int, unsigned __int64, __int64);
        __int64 lCustData;
        HINSTANCE__ *hInstance;
        const wchar_t *lpszTemplate;
        HRSRC__ *hResource;
        _GUID clsid;
        _GUID clsidConvertDefault;
        _GUID clsidActivateDefault;
        _GUID clsidNew;
        unsigned int dvAspect;
        unsigned __int16 wFormat;
        int fIsLinkedObject;
        void *hMetaPict;
        wchar_t *lpszUserType;
        int fObjectsIconChanged;
        wchar_t *lpszDefLabel;
        unsigned int cClsidExclude;
        _GUID *lpClsidExclude;
    };
END_ATF_NAMESPACE

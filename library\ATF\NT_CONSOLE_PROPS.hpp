// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_COORD.hpp>
#include <tagDATABLOCKHEADER.hpp>



START_ATF_NAMESPACE
    struct NT_CONSOLE_PROPS
    {
        tagDATABLOCKHEADER dbh;
        unsigned __int16 wFillAttribute;
        unsigned __int16 wPopupFillAttribute;
        _COORD dwScreenBufferSize;
        _COORD dwWindowSize;
        _COORD dwWindowOrigin;
        unsigned int nFont;
        unsigned int nInputBufferSize;
        _COORD dwFontSize;
        unsigned int uFontFamily;
        unsigned int uFontWeight;
        wchar_t FaceName[32];
        unsigned int uCursorSize;
        int bFullScreen;
        int bQuickEdit;
        int bInsertMode;
        int bAutoPosition;
        unsigned int uHistoryBufferSize;
        unsigned int uNumberOfHistoryBuffers;
        int bHistoryNoDup;
        unsigned int ColorTable[16];
    };
END_ATF_NAMESPACE

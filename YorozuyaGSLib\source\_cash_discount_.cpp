#include <_cash_discount_.hpp>


START_ATF_NAMESPACE
    _cash_discount_::_cash_discount_()
    {
        using org_ptr = void (WINAPIV*)(struct _cash_discount_*);
        (org_ptr(0x1403043f0L))(this);
    };
    void _cash_discount_::ctor__cash_discount_()
    {
        using org_ptr = void (WINAPIV*)(struct _cash_discount_*);
        (org_ptr(0x1403043f0L))(this);
    };
    _cash_discount_::~_cash_discount_()
    {
        using org_ptr = void (WINAPIV*)(struct _cash_discount_*);
        (org_ptr(0x1403046d0L))(this);
    };
    void _cash_discount_::dtor__cash_discount_()
    {
        using org_ptr = void (WINAPIV*)(struct _cash_discount_*);
        (org_ptr(0x1403046d0L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerCN.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerCNctor_CCashDbWorkerCN2_ptr = void (WINAPIV*)(struct CCashDbWorkerCN*);
        using CCashDbWorkerCNctor_CCashDbWorkerCN2_clbk = void (WINAPIV*)(struct CCashDbWorkerCN*, CCashDbWorkerCNctor_CCashDbWorkerCN2_ptr);
        
        using CCashDbWorkerCNdtor_CCashDbWorkerCN7_ptr = void (WINAPIV*)(struct CCashDbWorkerCN*);
        using CCashDbWorkerCNdtor_CCashDbWorkerCN7_clbk = void (WINAPIV*)(struct CCashDbWorkerCN*, CCashDbWorkerC<PERSON>dtor_CCashDbWorkerCN7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CRaceBuffInfoByHolyQuestfGroup
    {
        enum RESULT_TYPE
        {
            RT_WINGETSCANER = 0x0,
            RT_WINEMPTYSCANER = 0x1,
            RT_FAIL = 0x2,
            RT_LOSE = 0x3,
            RT_MAX_TYPE_NUM = 0x4,
        };
        unsigned int m_uiNTh;
        std::vector<struct CRaceBuffInfoByHolyQuest *> m_vecInfo;
    public:
        bool Apply(int iResultType, struct CPlayer* pkDest);
        CRaceBuffInfoByHolyQuestfGroup(unsigned int uiNTh);
        void ctor_CRaceBuffInfoByHolyQuestfGroup(unsigned int uiNTh);
        bool CreateComplete(int iResultType, struct CPlayer* pkDest);
        bool Init();
        bool Release(int iResultType, struct CPlayer* pkDest);
        ~CRaceBuffInfoByHolyQuestfGroup();
        void dtor_CRaceBuffInfoByHolyQuestfGroup();
    };
END_ATF_NAMESPACE

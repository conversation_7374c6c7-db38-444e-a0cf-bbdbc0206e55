// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMergeFileManager.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CMergeFileManagerGetFileSize1_ptr = uint32_t (WINAPIV*)(struct CMergeFileManager*, char*);
        using CMergeFileManagerGetFileSize1_clbk = uint32_t (WINAPIV*)(struct CMergeFileManager*, char*, CMergeFileManagerGetFileSize1_ptr);
        using CMergeFileManagerInitMergeFile2_ptr = void (WINAPIV*)(struct CMergeFileManager*, char*);
        using CMergeFileManagerInitMergeFile2_clbk = void (WINAPIV*)(struct CMergeFileManager*, char*, CMergeFileManagerInitMergeFile2_ptr);
        using CMergeFileManagerIsExistFile3_ptr = int (WINAPIV*)(struct CMergeFileManager*, char*);
        using CMergeFileManagerIsExistFile3_clbk = int (WINAPIV*)(struct CMergeFileManager*, char*, CMergeFileManagerIsExistFile3_ptr);
        using CMergeFileManagerLoadFileOffset4_ptr = struct _iobuf* (WINAPIV*)(struct CMergeFileManager*, char*, char*);
        using CMergeFileManagerLoadFileOffset4_clbk = struct _iobuf* (WINAPIV*)(struct CMergeFileManager*, char*, char*, CMergeFileManagerLoadFileOffset4_ptr);
        using CMergeFileManagerReleaseMergeFile5_ptr = void (WINAPIV*)(struct CMergeFileManager*);
        using CMergeFileManagerReleaseMergeFile5_clbk = void (WINAPIV*)(struct CMergeFileManager*, CMergeFileManagerReleaseMergeFile5_ptr);
        
        using CMergeFileManagerdtor_CMergeFileManager6_ptr = int64_t (WINAPIV*)(struct CMergeFileManager*);
        using CMergeFileManagerdtor_CMergeFileManager6_clbk = int64_t (WINAPIV*)(struct CMergeFileManager*, CMergeFileManagerdtor_CMergeFileManager6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ControllerTaxRate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using ControllerTaxRatector_ControllerTaxRate2_ptr = void (WINAPIV*)(struct ControllerTaxRate*);
        using ControllerTaxRatector_ControllerTaxRate2_clbk = void (WINAPIV*)(struct ControllerTaxRate*, ControllerTaxRatector_ControllerTaxRate2_ptr);
        using ControllerTaxRatecalcTaxRate7_ptr = unsigned int (WINAPIV*)(struct ControllerTaxRate*, unsigned int);
        using ControllerTaxRatecalcTaxRate7_clbk = unsigned int (WINAPIV*)(struct ControllerTaxRate*, unsigned int, ControllerTaxRatecalcTaxRate7_ptr);
        using ControllerTaxRatecheckLimitTaxRate9_ptr = bool (WINAPIV*)(struct ControllerTaxRate*, float);
        using ControllerTaxRatecheckLimitTaxRate9_clbk = bool (WINAPIV*)(struct ControllerTaxRate*, float, ControllerTaxRatecheckLimitTaxRate9_ptr);
        using ControllerTaxRategetCurTaxRate11_ptr = float (WINAPIV*)(struct ControllerTaxRate*);
        using ControllerTaxRategetCurTaxRate11_clbk = float (WINAPIV*)(struct ControllerTaxRate*, ControllerTaxRategetCurTaxRate11_ptr);
        using ControllerTaxRatesetCurTaxRate13_ptr = void (WINAPIV*)(struct ControllerTaxRate*, float);
        using ControllerTaxRatesetCurTaxRate13_clbk = void (WINAPIV*)(struct ControllerTaxRate*, float, ControllerTaxRatesetCurTaxRate13_ptr);
        using ControllerTaxRatesetLimitTaxRate15_ptr = void (WINAPIV*)(struct ControllerTaxRate*, float, float);
        using ControllerTaxRatesetLimitTaxRate15_clbk = void (WINAPIV*)(struct ControllerTaxRate*, float, float, ControllerTaxRatesetLimitTaxRate15_ptr);
        
        using ControllerTaxRatedtor_ControllerTaxRate17_ptr = void (WINAPIV*)(struct ControllerTaxRate*);
        using ControllerTaxRatedtor_ControllerTaxRate17_clbk = void (WINAPIV*)(struct ControllerTaxRate*, ControllerTaxRatedtor_ControllerTaxRate17_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

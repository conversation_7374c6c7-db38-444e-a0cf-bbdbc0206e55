// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$185862B522C7C389F20147548A6885AE.hpp>
#include <IRecordInfo.hpp>


START_ATF_NAMESPACE
    struct tagVARIANT
    {
        $185862B522C7C389F20147548A6885AE ___u0;
        IRecordInfo *pRecInfo;
    };    
    static_assert(ATF::checkSize<tagVARIANT, 24>(), "tagVARIANT");
END_ATF_NAMESPACE

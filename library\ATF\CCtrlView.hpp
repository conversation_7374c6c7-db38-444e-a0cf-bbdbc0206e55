// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CView.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CCtrlView : CView
    {
        ATL::CStringT<char> m_strClass;
        unsigned int m_dwDefaultStyle;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

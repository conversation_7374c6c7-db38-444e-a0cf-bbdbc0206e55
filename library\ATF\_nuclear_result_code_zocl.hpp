// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _nuclear_result_code_zocl
    {
        char byRetCode;
    public:
        _nuclear_result_code_zocl();
        void ctor__nuclear_result_code_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_nuclear_result_code_zocl, 1>(), "_nuclear_result_code_zocl");
END_ATF_NAMESPACE

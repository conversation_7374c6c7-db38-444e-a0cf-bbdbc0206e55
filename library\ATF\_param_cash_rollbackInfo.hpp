// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_param_cash_rollback.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _param_cash_rollbackctor__param_cash_rollback2_ptr = void (WINAPIV*)(struct _param_cash_rollback*, unsigned int, unsigned int, uint16_t);
        using _param_cash_rollbackctor__param_cash_rollback2_clbk = void (WINAPIV*)(struct _param_cash_rollback*, unsigned int, unsigned int, uint16_t, _param_cash_rollbackctor__param_cash_rollback2_ptr);
        using _param_cash_rollbacksize4_ptr = int (WINAPIV*)(struct _param_cash_rollback*);
        using _param_cash_rollbacksize4_clbk = int (WINAPIV*)(struct _param_cash_rollback*, _param_cash_rollbacksize4_ptr);
        
        using _param_cash_rollbackdtor__param_cash_rollback6_ptr = void (WINAPIV*)(struct _param_cash_rollback*);
        using _param_cash_rollbackdtor__param_cash_rollback6_clbk = void (WINAPIV*)(struct _param_cash_rollback*, _param_cash_rollbackdtor__param_cash_rollback6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct RoomCharInfo
    {
        bool bIn;
        int iCharIdx;
        unsigned int dwCharSerial;
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<RoomCharInfo, 12>(), "RoomCharInfo");
END_ATF_NAMESPACE

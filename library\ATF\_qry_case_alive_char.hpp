// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_REGED.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_alive_char
    {
        char in_byCase;
        unsigned int in_dwSerial;
        char in_w_szName[17];
        char in_bySlot;
        _REGED out_AliveAvator;
    public:
        _qry_case_alive_char();
        void ctor__qry_case_alive_char();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_alive_char, 296>(), "_qry_case_alive_char");
END_ATF_NAMESPACE

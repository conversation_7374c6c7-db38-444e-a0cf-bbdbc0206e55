// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>
#include <CPlayer.hpp>
#include <_be_damaged_char.hpp>
#include <_be_damaged_player.hpp>
#include <_nuclear_create_setdata.hpp>
#include <_object_id.hpp>


START_ATF_NAMESPACE
    struct  CNuclearBomb : CCharacter
    {
        unsigned __int16 m_wItemIndex;
        unsigned __int16 m_wControlSerial;
        bool m_bUse;
        bool m_bIsLive;
        float m_fDropPos[3];
        unsigned int m_dwStartTime;
        unsigned int m_dwDurTime;
        unsigned int m_dwDelayTime;
        unsigned int m_dwWarnTime;
        unsigned int m_dwAttInformTime;
        unsigned int m_dwAttStartTime;
        char m_byBombState;
        _be_damaged_player m_DamList[300];
        _be_damaged_char m_EffList[400];
        int m_nDamagedObjNum;
        int m_nEffObjNum;
        int m_nStartDmLoop;
        CPlayer *m_pMaster;
    public:
        void Attack(int StartNum, int Obj_num);
        CNuclearBomb();
        void ctor_CNuclearBomb();
        bool Create(struct _nuclear_create_setdata* pData);
        bool Destroy();
        char GetBombStatus();
        uint16_t GetControlSerial();
        int GetDamagedObjNum();
        int GetGenAttackProb(struct CCharacter* pDst, int nPart);
        uint16_t GetItemIndex();
        char GetMasterClass();
        char GetMasterRace();
        float* GetMissilePos();
        static unsigned int GetNewSerial();
        void GetShowEffectList();
        bool GetUse();
        bool Init(struct _object_id* pID);
        void Loop();
        void NuclearDamege();
        void RecvKillMessage(struct CCharacter* pDier);
        void SendMsg_AddEffect();
        void SendMsg_Attack(int StartNum, int Obj_Num);
        void SendMsg_DropMissile();
        void SendMsg_InformAttack();
        void SendMsg_InformDropPos();
        void SendMsg_MasterDie();
        void SendMsg_NuclearFind(int n, char race);
        void SendMsg_Result(int n, char byCode);
        void SetBombStatus();
        void SetControlSerial(uint16_t wControlSerial);
        void SetNuclearIndex(uint16_t wItemIndex);
        void WarningToAll(char byRaceCode);
        ~CNuclearBomb();
        void dtor_CNuclearBomb();
    };
END_ATF_NAMESPACE

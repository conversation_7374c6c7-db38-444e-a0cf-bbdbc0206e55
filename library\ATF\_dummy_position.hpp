// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _dummy_position
    {
        char m_szCode[64];
        unsigned __int16 m_wLineIndex;
        bool m_bPosAble;
        __int16 m_zLocalMin[3];
        __int16 m_zLocalMax[3];
        float m_fMin[3];
        float m_fMax[3];
        float m_fRT[3];
        float m_fLB[3];
        float m_fCenterPos[3];
        float m_fDirection[3];
        unsigned __int16 m_wActiveMon;
    public:
        void SetActiveMonNum(int nAlter);
        _dummy_position();
        void ctor__dummy_position();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_dummy_position, 156>(), "_dummy_position");
END_ATF_NAMESPACE

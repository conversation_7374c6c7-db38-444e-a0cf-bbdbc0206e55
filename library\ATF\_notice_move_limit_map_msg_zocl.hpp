// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _notice_move_limit_map_msg_zocl
    {
        enum NOTIFY_TYPE
        {
            NT_NONE = 0xFF,
            NT_NOTIFY_FORCE_MOVE_HQ_FROM_LIMIT_MAP_BY_HOLY_QUEST = 0x0,
            NT_FORCE_MOVE_HQ_FROM_LIMIT_MAP_BY_HOLY_QUEST = 0x1,
            NT_FORCE_START_POS_FROM_LIMIT_MAP = 0x2,
        };
        char byType;
    public:
        _notice_move_limit_map_msg_zocl();
        void ctor__notice_move_limit_map_msg_zocl();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_notice_move_limit_map_msg_zocl, 1>(), "_notice_move_limit_map_msg_zocl");
END_ATF_NAMESPACE

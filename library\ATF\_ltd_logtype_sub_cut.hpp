// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum _ltd_logtype_sub_cut
    {
      make_success = 0x0,
      make_fail = 0xA,
      mix_success = 0x14,
      max_fail = 0x1E,
      upgrade_success = 0x28,
      upgrade_fail_random = 0x32,
      upgrade_fail_deltalik = 0x3C,
      upgrade_fail_delitem = 0x46,
      downgrade_success = 0x50,
      ore = 0x5A,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct PotionInnerData
    {
        bool m_bPotionDelayIndexList[38];
    public:
        void Init();
        PotionInnerData();
        void ctor_PotionInnerData();
        ~PotionInnerData();
        void dtor_PotionInnerData();
    };    
    static_assert(ATF::checkSize<PotionInnerData, 38>(), "PotionInnerData");
END_ATF_NAMESPACE

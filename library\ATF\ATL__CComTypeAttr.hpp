// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CComPtr.hpp>
#include <tagTYPEATTR.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct CComTypeAttr
        {
            tagTYPEATTR *m_pTypeAttr;
            CComPtr<ITypeInfo> m_pTypeInfo;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

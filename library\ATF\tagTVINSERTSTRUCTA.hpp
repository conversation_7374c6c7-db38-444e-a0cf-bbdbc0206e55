// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$BCFD166F8F7472A6E5CBDFFAA598D6FD.hpp>


START_ATF_NAMESPACE
    struct tagTVINSERTSTRUCTA
    {
        struct _TREEITEM *hParent;
        struct _TREEITEM *hInsertAfter;
        $BCFD166F8F7472A6E5CBDFFAA598D6FD ___u2;
    };
END_ATF_NAMESPACE

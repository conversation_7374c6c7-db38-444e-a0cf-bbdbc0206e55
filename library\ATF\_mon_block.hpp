// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMapData.hpp>
#include <_dummy_position.hpp>
#include <_mon_block_fld.hpp>


START_ATF_NAMESPACE
    struct _mon_block
    {
        _mon_block_fld *m_pBlkRec;
        _dummy_position *m_pDumPos[20];
        bool m_bBossBlock;
        bool m_bRotate;
        CMapData *m_pMap;
    public:
        int SelectDummyIndex();
        bool SetBlock(struct _mon_block_fld* pBlkRec, struct CMapData* pMap, struct _dummy_position** ppDumPos);
        void SetRotateBlock(bool rhs);
    };
END_ATF_NAMESPACE

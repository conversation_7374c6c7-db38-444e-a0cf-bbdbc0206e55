// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _guild_master_advantage_info
    {
        float attack_value;
        float defence_value;
    };    
    static_assert(ATF::checkSize<_guild_master_advantage_info, 8>(), "_guild_master_advantage_info");
END_ATF_NAMESPACE

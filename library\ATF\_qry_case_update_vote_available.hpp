// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_update_vote_available
    {
        char byVoteEnable;
        unsigned int dwAccountSerial;
        unsigned int dwCharSerial;
        char wszCharName[17];
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_update_vote_available, 32>(), "_qry_case_update_vote_available");
END_ATF_NAMESPACE

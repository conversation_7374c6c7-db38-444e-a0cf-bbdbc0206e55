// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_D3DLIGHTINGCAPS.hpp>
#include <_D3DPrimCaps.hpp>
#include <_D3DTRANSFORMCAPS.hpp>


START_ATF_NAMESPACE
    struct _D3DDeviceDesc
    {
        unsigned int dwSize;
        unsigned int dwFlags;
        unsigned int dcmColorModel;
        unsigned int dwDevCaps;
        _D3DTRANSFORMCAPS dtcTransformCaps;
        int bClipping;
        _D3DLIGHTINGCAPS dlcLightingCaps;
        _D3DPrimCaps dpcLineCaps;
        _D3DPrimCaps dpcTriCaps;
        unsigned int dwDeviceRenderBitDepth;
        unsigned int dwDeviceZBufferBitDepth;
        unsigned int dwMaxBufferSize;
        unsigned int dwMaxVertexCount;
        unsigned int dwMinTextureWidth;
        unsigned int dwMinTextureHeight;
        unsigned int dwMaxTextureWidth;
        unsigned int dwMaxTextureHeight;
        unsigned int dwMinStippleWidth;
        unsigned int dwMaxStippleWidth;
        unsigned int dwMinStippleHeight;
        unsigned int dwMaxStippleHeight;
        unsigned int dwMaxTextureRepeat;
        unsigned int dwMaxTextureAspectRatio;
        unsigned int dwMaxAnisotropy;
        float dvGuardBandLeft;
        float dvGuardBandTop;
        float dvGuardBandRight;
        float dvGuardBandBottom;
        float dvExtentsAdjust;
        unsigned int dwStencilCaps;
        unsigned int dwFVFCaps;
        unsigned int dwTextureOpCaps;
        unsigned __int16 wMaxTextureBlendStages;
        unsigned __int16 wMaxSimultaneousTextures;
    };
END_ATF_NAMESPACE

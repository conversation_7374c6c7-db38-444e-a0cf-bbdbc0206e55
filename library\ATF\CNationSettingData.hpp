// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__vector.hpp>
#include <CHEAT_COMMAND.hpp>
#include <INationGameGuardSystem.hpp>
#include <CNationSettingDataVtbl.hpp>

START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CNationSettingData
    {
        enum CASH_DB_PROC_FLAG
        {
            CDPS_NOT_REF = 0xFFFFFFFF,
            CDPS_USE_REF = 0x0,
            CDPS_SET = 0x1,
            CDPS_INIT = 0x2,
        };
        CNationSettingDataVtbl *vfptr;
        bool m_bServiceMode;
        int m_iNationCode;
        char m_szNationCodeStr[3];
        int m_iANSICodePage;
        char m_szNoneString[8];
        char m_szCashDBName[64];
        char m_szCashDBIP[16];
        char m_szCashDBID[64];
        char m_szCashDBPW[64];
        unsigned __int16 m_wCashDBPort;
        CASH_DB_PROC_FLAG m_eCashDBFlag;
        char m_szWorldDBID[64];
        char m_szWorldDBPW[64];
        unsigned __int16 m_wBillingForceCloseDelay;
        char m_szVaildKey[17];
        std::vector<CHEAT_COMMAND> m_vecCheatData;
        INationGameGuardSystem *m_pGameGuardSystem;
    public:
        CNationSettingData();
        void ctor_CNationSettingData();
        bool CheckDBCSCompleteString(int nCodePage, char* strData, uint64_t* pCharacterCount);
        bool CheckEnterWorldRequest(int n, char* pBuf);
        struct CBilling* CreateBilling();
        void CreateComplete(struct CPlayer* pOne);
        struct CashDbWorker* CreateWorker();
        int GetCashItemPrice(struct _CashShop_str_fld* pFld);
        bool GetFireGuardEnableSetting();
        struct INationGameGuardSystem* GetGameGuardSystem();
        char* GetItemName(struct _NameTxt_fld* pFld);
        char* GetNoneString();
        bool GetTimeLimitEnableSetting();
        int Init();
        bool IsApplyPcbangPrimium(struct CPlayer* pUser);
        bool IsCashDBDSNSetted();
        bool IsCashDBInit();
        bool IsCashDBUseExtRef();
        bool IsNormalChar(wchar_t wcChar);
        bool IsNormalString(char* szString);
        bool IsNormalString(wchar_t* wszString);
        bool IsNormalStringDefProc(char* szString, char* szException);
        bool IsNormalStringDefProc(wchar_t* wszString, wchar_t* wszException);
        bool IsPersonalFreeFixedAmountBillingType(int16_t* pDest1, int16_t* pDest2);
        void Loop();
        void NetClose(struct CPlayer* pOne);
        bool ReadSystemPass();
        void SendCashDBDSNRequest();
        void SetCahsDBUseExtRefFlag();
        void SetCashDBDSN(char* szIP, char* szDBName, char* szAccount, char* szPassword, unsigned int dwPort);
        void SetCashDBDSNSetFlag();
        void SetCashDBInitFlag();
        void SetUnitPassiveValue(float* fUnitPv_DefFc);
        bool ValidMacAddress();
        ~CNationSettingData();
        void dtor_CNationSettingData();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CNationSettingData, 448>(), "CNationSettingData");
END_ATF_NAMESPACE

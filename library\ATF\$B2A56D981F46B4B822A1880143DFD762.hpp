// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IOleInPlaceSite.hpp>
#include <IOleInPlaceSiteEx.hpp>
#include <IOleInPlaceSiteWindowless.hpp>


START_ATF_NAMESPACE
    union $B2A56D981F46B4B822A1880143DFD762
    {
        IOleInPlaceSite *m_pInPlaceSite;
        IOleInPlaceSiteEx *m_pInPlaceSiteEx;
        IOleInPlaceSiteWindowless *m_pInPlaceSiteWndless;
    };
END_ATF_NAMESPACE

#include <_nuclear_bomb_explosion_result_zocl.hpp>


START_ATF_NAMESPACE
    _nuclear_bomb_explosion_result_zocl::_nuclear_bomb_explosion_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_bomb_explosion_result_zocl*);
        (org_ptr(0x14013e680L))(this);
    };
    void _nuclear_bomb_explosion_result_zocl::ctor__nuclear_bomb_explosion_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_bomb_explosion_result_zocl*);
        (org_ptr(0x14013e680L))(this);
    };
    int _nuclear_bomb_explosion_result_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _nuclear_bomb_explosion_result_zocl*);
        return (org_ptr(0x14013e6d0L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPostSystemManager.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CPostSystemManagerctor_CPostSystemManager2_ptr = void (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerctor_CPostSystemManager2_clbk = void (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerctor_CPostSystemManager2_ptr);
        using CPostSystemManagerCheckRegister4_ptr = char (WINAPIV*)(struct CPostSystemManager*, struct CPlayer*, struct _STORAGE_POS_INDIV*, unsigned int, struct _STORAGE_LIST::_db_con**);
        using CPostSystemManagerCheckRegister4_clbk = char (WINAPIV*)(struct CPostSystemManager*, struct CPlayer*, struct _STORAGE_POS_INDIV*, unsigned int, struct _STORAGE_LIST::_db_con**, CPostSystemManagerCheckRegister4_ptr);
        using CPostSystemManagerCompletePostReceiverCheck6_ptr = void (WINAPIV*)(struct CPostSystemManager*, char*);
        using CPostSystemManagerCompletePostReceiverCheck6_clbk = void (WINAPIV*)(struct CPostSystemManager*, char*, CPostSystemManagerCompletePostReceiverCheck6_ptr);
        using CPostSystemManagerCompleteRegist8_ptr = void (WINAPIV*)(struct CPostSystemManager*, char*);
        using CPostSystemManagerCompleteRegist8_clbk = void (WINAPIV*)(struct CPostSystemManager*, char*, CPostSystemManagerCompleteRegist8_ptr);
        using CPostSystemManagerCompleteSend10_ptr = void (WINAPIV*)(struct CPostSystemManager*, char*);
        using CPostSystemManagerCompleteSend10_clbk = void (WINAPIV*)(struct CPostSystemManager*, char*, CPostSystemManagerCompleteSend10_ptr);
        using CPostSystemManagerDestroy12_ptr = void (WINAPIV*)();
        using CPostSystemManagerDestroy12_clbk = void (WINAPIV*)(CPostSystemManagerDestroy12_ptr);
        using CPostSystemManagerInit14_ptr = bool (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerInit14_clbk = bool (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerInit14_ptr);
        using CPostSystemManagerInitLogger16_ptr = bool (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerInitLogger16_clbk = bool (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerInitLogger16_ptr);
        using CPostSystemManagerInsertDefaultPSRecord18_ptr = bool (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerInsertDefaultPSRecord18_clbk = bool (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerInsertDefaultPSRecord18_ptr);
        using CPostSystemManagerInstace20_ptr = struct CPostSystemManager* (WINAPIV*)();
        using CPostSystemManagerInstace20_clbk = struct CPostSystemManager* (WINAPIV*)(CPostSystemManagerInstace20_ptr);
        using CPostSystemManagerLoad22_ptr = bool (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerLoad22_clbk = bool (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerLoad22_ptr);
        using CPostSystemManagerLog24_ptr = void (WINAPIV*)(struct CPostSystemManager*, char*);
        using CPostSystemManagerLog24_clbk = void (WINAPIV*)(struct CPostSystemManager*, char*, CPostSystemManagerLog24_ptr);
        using CPostSystemManagerLog26_ptr = void (WINAPIV*)(struct CPostSystemManager*, wchar_t*);
        using CPostSystemManagerLog26_clbk = void (WINAPIV*)(struct CPostSystemManager*, wchar_t*, CPostSystemManagerLog26_ptr);
        using CPostSystemManagerLoop28_ptr = void (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerLoop28_clbk = void (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerLoop28_ptr);
        using CPostSystemManagerPostReceiverCheck30_ptr = char (WINAPIV*)(struct CPostSystemManager*, char*);
        using CPostSystemManagerPostReceiverCheck30_clbk = char (WINAPIV*)(struct CPostSystemManager*, char*, CPostSystemManagerPostReceiverCheck30_ptr);
        using CPostSystemManagerPostRegistryLoad32_ptr = bool (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerPostRegistryLoad32_clbk = bool (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerPostRegistryLoad32_ptr);
        using CPostSystemManagerPostSend34_ptr = char (WINAPIV*)(struct CPostSystemManager*, char*);
        using CPostSystemManagerPostSend34_clbk = char (WINAPIV*)(struct CPostSystemManager*, char*, CPostSystemManagerPostSend34_ptr);
        using CPostSystemManagerPostSendRequest36_ptr = bool (WINAPIV*)(struct CPostSystemManager*, struct CPlayer*, char*, char*, char*, struct _STORAGE_POS_INDIV*, unsigned int, char);
        using CPostSystemManagerPostSendRequest36_clbk = bool (WINAPIV*)(struct CPostSystemManager*, struct CPlayer*, char*, char*, char*, struct _STORAGE_POS_INDIV*, unsigned int, char, CPostSystemManagerPostSendRequest36_ptr);
        using CPostSystemManagerSetNextWriteTime38_ptr = void (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerSetNextWriteTime38_clbk = void (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerSetNextWriteTime38_ptr);
        using CPostSystemManagerUpdateDisappearOwnerRecord40_ptr = bool (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerUpdateDisappearOwnerRecord40_clbk = bool (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerUpdateDisappearOwnerRecord40_ptr);
        using CPostSystemManagerUpdateRegist42_ptr = char (WINAPIV*)(struct CPostSystemManager*, char*);
        using CPostSystemManagerUpdateRegist42_clbk = char (WINAPIV*)(struct CPostSystemManager*, char*, CPostSystemManagerUpdateRegist42_ptr);
        
        using CPostSystemManagerdtor_CPostSystemManager46_ptr = void (WINAPIV*)(struct CPostSystemManager*);
        using CPostSystemManagerdtor_CPostSystemManager46_clbk = void (WINAPIV*)(struct CPostSystemManager*, CPostSystemManagerdtor_CPostSystemManager46_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

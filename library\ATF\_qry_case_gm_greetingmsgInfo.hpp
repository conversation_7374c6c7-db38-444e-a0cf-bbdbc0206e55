// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_gm_greetingmsg.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _qry_case_gm_greetingmsgsize2_ptr = int (WINAPIV*)(struct _qry_case_gm_greetingmsg*);
        using _qry_case_gm_greetingmsgsize2_clbk = int (WINAPIV*)(struct _qry_case_gm_greetingmsg*, _qry_case_gm_greetingmsgsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPotionMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPotionMgrApplyPotion2_ptr = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct CPlayer*, struct _skill_fld*, struct _CheckPotion_fld*, struct _PotionItem_fld*, bool);
        using CPotionMgrApplyPotion2_clbk = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct CPlayer*, struct _skill_fld*, struct _CheckPotion_fld*, struct _PotionItem_fld*, bool, CPotionMgrApplyPotion2_ptr);
        
        using CPotionMgrctor_CPotionMgr4_ptr = void (WINAPIV*)(struct CPotionMgr*);
        using CPotionMgrctor_CPotionMgr4_clbk = void (WINAPIV*)(struct CPotionMgr*, CPotionMgrctor_CPotionMgr4_ptr);
        using CPotionMgrCheckPotionUsableMap6_ptr = bool (WINAPIV*)(struct CPotionMgr*, struct _PotionItem_fld*, struct CMapData*);
        using CPotionMgrCheckPotionUsableMap6_clbk = bool (WINAPIV*)(struct CPotionMgr*, struct _PotionItem_fld*, struct CMapData*, CPotionMgrCheckPotionUsableMap6_ptr);
        using CPotionMgrComplete_RenameChar_DB_Select8_ptr = void (WINAPIV*)(struct CPotionMgr*, char, char*);
        using CPotionMgrComplete_RenameChar_DB_Select8_clbk = void (WINAPIV*)(struct CPotionMgr*, char, char*, CPotionMgrComplete_RenameChar_DB_Select8_ptr);
        using CPotionMgrComplete_RenameChar_DB_Update10_ptr = void (WINAPIV*)(struct CPotionMgr*, char, char*);
        using CPotionMgrComplete_RenameChar_DB_Update10_clbk = void (WINAPIV*)(struct CPotionMgr*, char, char*, CPotionMgrComplete_RenameChar_DB_Update10_ptr);
        using CPotionMgrDatafileInit12_ptr = bool (WINAPIV*)(struct CPotionMgr*);
        using CPotionMgrDatafileInit12_clbk = bool (WINAPIV*)(struct CPotionMgr*, CPotionMgrDatafileInit12_ptr);
        using CPotionMgrInsertMovePotionStoneEffect14_ptr = void (WINAPIV*)(struct CPotionMgr*, struct CPlayer*);
        using CPotionMgrInsertMovePotionStoneEffect14_clbk = void (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, CPotionMgrInsertMovePotionStoneEffect14_ptr);
        using CPotionMgrInsertPotionContEffect16_ptr = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct _ContPotionData*, struct _skill_fld*, unsigned int);
        using CPotionMgrInsertPotionContEffect16_clbk = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct _ContPotionData*, struct _skill_fld*, unsigned int, CPotionMgrInsertPotionContEffect16_ptr);
        using CPotionMgrInsertRenamePotion18_ptr = bool (WINAPIV*)(struct CPotionMgr*, struct CRFWorldDatabase*, char*);
        using CPotionMgrInsertRenamePotion18_clbk = bool (WINAPIV*)(struct CPotionMgr*, struct CRFWorldDatabase*, char*, CPotionMgrInsertRenamePotion18_ptr);
        using CPotionMgrIsPotionDelayUseIndex20_ptr = bool (WINAPIV*)(struct CPotionMgr*, int);
        using CPotionMgrIsPotionDelayUseIndex20_clbk = bool (WINAPIV*)(struct CPotionMgr*, int, CPotionMgrIsPotionDelayUseIndex20_ptr);
        using CPotionMgrPreCheckPotion22_ptr = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct CCharacter**, struct _PotionItem_fld*, unsigned int, struct _skill_fld*, bool);
        using CPotionMgrPreCheckPotion22_clbk = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct CCharacter**, struct _PotionItem_fld*, unsigned int, struct _skill_fld*, bool, CPotionMgrPreCheckPotion22_ptr);
        using CPotionMgrPushRenamePotionDBLog24_ptr = void (WINAPIV*)(struct CPotionMgr*, char*);
        using CPotionMgrPushRenamePotionDBLog24_clbk = void (WINAPIV*)(struct CPotionMgr*, char*, CPotionMgrPushRenamePotionDBLog24_ptr);
        using CPotionMgrRemovePotionContEffect26_ptr = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct _ContPotionData*);
        using CPotionMgrRemovePotionContEffect26_clbk = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct _ContPotionData*, CPotionMgrRemovePotionContEffect26_ptr);
        using CPotionMgrSelectDeleteBuf28_ptr = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, bool, bool);
        using CPotionMgrSelectDeleteBuf28_clbk = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, bool, bool, CPotionMgrSelectDeleteBuf28_ptr);
        using CPotionMgrSetPotionDataName30_ptr = bool (WINAPIV*)(struct CPotionMgr*);
        using CPotionMgrSetPotionDataName30_clbk = bool (WINAPIV*)(struct CPotionMgr*, CPotionMgrSetPotionDataName30_ptr);
        using CPotionMgrUpdatePotionContEffect32_ptr = void (WINAPIV*)(struct CPotionMgr*, struct CPlayer*);
        using CPotionMgrUpdatePotionContEffect32_clbk = void (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, CPotionMgrUpdatePotionContEffect32_ptr);
        using CPotionMgrUsePotion34_ptr = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct CCharacter*, struct _PotionItem_fld*, unsigned int);
        using CPotionMgrUsePotion34_clbk = int (WINAPIV*)(struct CPotionMgr*, struct CPlayer*, struct CCharacter*, struct _PotionItem_fld*, unsigned int, CPotionMgrUsePotion34_ptr);
        
        using CPotionMgrdtor_CPotionMgr36_ptr = void (WINAPIV*)(struct CPotionMgr*);
        using CPotionMgrdtor_CPotionMgr36_clbk = void (WINAPIV*)(struct CPotionMgr*, CPotionMgrdtor_CPotionMgr36_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

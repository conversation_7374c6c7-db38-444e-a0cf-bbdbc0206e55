// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitEnviromentValues.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CMoveMapLimitEnviromentValuesInit2_ptr = bool (WINAPIV*)();
        using CMoveMapLimitEnviromentValuesInit2_clbk = bool (WINAPIV*)(CMoveMapLimitEnviromentValuesInit2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

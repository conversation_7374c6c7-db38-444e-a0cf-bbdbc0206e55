// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDarkHoleDungeonQuest.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CDarkHoleDungeonQuestctor_CDarkHoleDungeonQuest2_ptr = void (WINAPIV*)(struct CDarkHoleDungeonQuest*);
        using CDarkHoleDungeonQuestctor_CDarkHoleDungeonQuest2_clbk = void (WINAPIV*)(struct CDarkHoleDungeonQuest*, CDarkHoleDungeonQuestctor_CDarkHoleDungeonQuest2_ptr);
        using CDarkHoleDungeonQuestCanOpenChannel4_ptr = struct CDarkHoleChannel* (WINAPIV*)(struct CDarkHoleDungeonQuest*, int);
        using CDarkHoleDungeonQuestCanOpenChannel4_clbk = struct CDarkHoleChannel* (WINAPIV*)(struct CDarkHoleDungeonQuest*, int, CDarkHoleDungeonQuestCanOpenChannel4_ptr);
        using CDarkHoleDungeonQuestCheckQuestOnLoop6_ptr = void (WINAPIV*)(struct CDarkHoleDungeonQuest*);
        using CDarkHoleDungeonQuestCheckQuestOnLoop6_clbk = void (WINAPIV*)(struct CDarkHoleDungeonQuest*, CDarkHoleDungeonQuestCheckQuestOnLoop6_ptr);
        using CDarkHoleDungeonQuestGetChannel8_ptr = struct CDarkHoleChannel* (WINAPIV*)(struct CDarkHoleDungeonQuest*, unsigned int);
        using CDarkHoleDungeonQuestGetChannel8_clbk = struct CDarkHoleChannel* (WINAPIV*)(struct CDarkHoleDungeonQuest*, unsigned int, CDarkHoleDungeonQuestGetChannel8_ptr);
        using CDarkHoleDungeonQuestLoadDarkHoleQuest10_ptr = bool (WINAPIV*)(struct CDarkHoleDungeonQuest*);
        using CDarkHoleDungeonQuestLoadDarkHoleQuest10_clbk = bool (WINAPIV*)(struct CDarkHoleDungeonQuest*, CDarkHoleDungeonQuestLoadDarkHoleQuest10_ptr);
        using CDarkHoleDungeonQuestOpenChannel12_ptr = struct CDarkHoleChannel* (WINAPIV*)(struct CDarkHoleDungeonQuest*, int, struct CPlayer*, struct CDarkHole*);
        using CDarkHoleDungeonQuestOpenChannel12_clbk = struct CDarkHoleChannel* (WINAPIV*)(struct CDarkHoleDungeonQuest*, int, struct CPlayer*, struct CDarkHole*, CDarkHoleDungeonQuestOpenChannel12_ptr);
        using CDarkHoleDungeonQuestSearchEmptyDarkHoleChannel14_ptr = int (WINAPIV*)(struct CDarkHoleDungeonQuest*);
        using CDarkHoleDungeonQuestSearchEmptyDarkHoleChannel14_clbk = int (WINAPIV*)(struct CDarkHoleDungeonQuest*, CDarkHoleDungeonQuestSearchEmptyDarkHoleChannel14_ptr);
        using CDarkHoleDungeonQuestSearchEmptyDarkHoleLayer16_ptr = int (WINAPIV*)(struct CDarkHoleDungeonQuest*, int);
        using CDarkHoleDungeonQuestSearchEmptyDarkHoleLayer16_clbk = int (WINAPIV*)(struct CDarkHoleDungeonQuest*, int, CDarkHoleDungeonQuestSearchEmptyDarkHoleLayer16_ptr);
        using CDarkHoleDungeonQuestSearchOncePlayedChannel18_ptr = struct CDarkHoleChannel* (WINAPIV*)(struct CDarkHoleDungeonQuest*, unsigned int);
        using CDarkHoleDungeonQuestSearchOncePlayedChannel18_clbk = struct CDarkHoleChannel* (WINAPIV*)(struct CDarkHoleDungeonQuest*, unsigned int, CDarkHoleDungeonQuestSearchOncePlayedChannel18_ptr);
        
        using CDarkHoleDungeonQuestdtor_CDarkHoleDungeonQuest23_ptr = void (WINAPIV*)(struct CDarkHoleDungeonQuest*);
        using CDarkHoleDungeonQuestdtor_CDarkHoleDungeonQuest23_clbk = void (WINAPIV*)(struct CDarkHoleDungeonQuest*, CDarkHoleDungeonQuestdtor_CDarkHoleDungeonQuest23_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

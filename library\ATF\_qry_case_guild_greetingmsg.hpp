// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_guild_greetingmsg
    {
        unsigned int in_guildserial;
        char in_guildgreetingmsg[256];
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_qry_case_guild_greetingmsg, 260>(), "_qry_case_guild_greetingmsg");
END_ATF_NAMESPACE

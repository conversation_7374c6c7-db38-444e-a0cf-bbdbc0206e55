// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _log_sheet_lv
    {
        unsigned int dwAvatorSerial;
        char byLv;
        unsigned int dwTotalPlayMin;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_log_sheet_lv, 12>(), "_log_sheet_lv");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ACL.hpp>


START_ATF_NAMESPACE
    struct _SECURITY_DESCRIPTOR
    {
        char Revision;
        char Sbz1;
        unsigned __int16 Control;
        void *Owner;
        void *Group;
        _ACL *Sacl;
        _ACL *Dacl;
    };
END_ATF_NAMESPACE

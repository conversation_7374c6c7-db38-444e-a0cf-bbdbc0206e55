// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ListHeap.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct TimeLimitJade
    {
        enum NUM
        {
            max_timejade_num = 0x64,
        };
        struct WaitCell
        {
            _STORAGE_LIST::_db_con *_pkItem;
            unsigned __int16 _wStartTime;
            unsigned int _dwUseTime;
        public:
            WaitCell(struct _STORAGE_LIST::_db_con* pItem);
            void ctor_WaitCell(struct _STORAGE_LIST::_db_con* pItem);
            WaitCell(struct _STORAGE_LIST::_db_con* pItem, uint16_t wStartTime, unsigned int dwUseTime);
            void ctor_WaitCell(struct _STORAGE_LIST::_db_con* pItem, uint16_t wStartTime, unsigned int dwUseTime);
            WaitCell();
            void ctor_WaitCell();
        };
        struct UseCell
        {
            _STORAGE_LIST::_db_con *_pkItem;
            unsigned __int16 _wStartTime;
            unsigned int _dwUseTime;
        public:
            UseCell(_STORAGE_LIST::_db_con* pItem);
            void ctor_UseCell(_STORAGE_LIST::_db_con* pItem);
            UseCell(_STORAGE_LIST::_db_con* pItem, uint16_t wStartTime, unsigned int dwUseTime);
            void ctor_UseCell(_STORAGE_LIST::_db_con* pItem, uint16_t wStartTime, unsigned int dwUseTime);
            UseCell();
            void ctor_UseCell();
        };
        struct CPlayer* _pkOwner;
        ListHeap<WaitCell> _heapWaitRow;
        ListHeap<UseCell> _heapUseRow;
    public:
        int CheckEndTime();
        int CheckStartTime();
        bool DeleteUseList(_STORAGE_LIST::_db_con* pkItem, bool bItemDel);
        bool DeleteWaitList(_STORAGE_LIST::_db_con* pkItem);
        bool Init();
        bool InitUse();
        bool InitWait();
        bool InsertUseList(_STORAGE_LIST::_db_con* pkItem, unsigned int dwStart, unsigned int dwEnd);
        bool InsertWaitList(_STORAGE_LIST::_db_con* pkItem);
        void Release();
        TimeLimitJade(struct CPlayer* p);
        void ctor_TimeLimitJade(struct CPlayer* p);
        ~TimeLimitJade();
        void dtor_TimeLimitJade();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PCBANG_FAVOR_ITEM_DB_BASE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _PCBANG_FAVOR_ITEM_DB_BASEInit2_ptr = void (WINAPIV*)(struct _PCBANG_FAVOR_ITEM_DB_BASE*);
        using _PCBANG_FAVOR_ITEM_DB_BASEInit2_clbk = void (WINAPIV*)(struct _PCBANG_FAVOR_ITEM_DB_BASE*, _PCBANG_FAVOR_ITEM_DB_BASEInit2_ptr);
        using _PCBANG_FAVOR_ITEM_DB_BASEInsertItem4_ptr = bool (WINAPIV*)(struct _PCBANG_FAVOR_ITEM_DB_BASE*, struct _STORAGE_LIST::_db_con*);
        using _PCBANG_FAVOR_ITEM_DB_BASEInsertItem4_clbk = bool (WINAPIV*)(struct _PCBANG_FAVOR_ITEM_DB_BASE*, struct _STORAGE_LIST::_db_con*, _PCBANG_FAVOR_ITEM_DB_BASEInsertItem4_ptr);
        using _PCBANG_FAVOR_ITEM_DB_BASEIsDeleteItem6_ptr = bool (WINAPIV*)(struct _PCBANG_FAVOR_ITEM_DB_BASE*, struct _STORAGE_LIST::_db_con*);
        using _PCBANG_FAVOR_ITEM_DB_BASEIsDeleteItem6_clbk = bool (WINAPIV*)(struct _PCBANG_FAVOR_ITEM_DB_BASE*, struct _STORAGE_LIST::_db_con*, _PCBANG_FAVOR_ITEM_DB_BASEIsDeleteItem6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

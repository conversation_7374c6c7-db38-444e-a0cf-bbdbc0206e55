#include <_CRYMSG_DB_BASE.hpp>


START_ATF_NAMESPACE
    void _CRYMSG_DB_BASE::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _CRYMSG_DB_BASE*);
        (org_ptr(0x140077840L))(this);
    };
    _CRYMSG_DB_BASE::_CRYMSG_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _CRYMSG_DB_BASE*);
        (org_ptr(0x140077760L))(this);
    };
    void _CRYMSG_DB_BASE::ctor__CRYMSG_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _CRYMSG_DB_BASE*);
        (org_ptr(0x140077760L))(this);
    };
    void _CRYMSG_DB_BASE::_LIST::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _CRYMSG_DB_BASE::_LIST*);
        (org_ptr(0x140077820L))(this);
    };
    _CRYMSG_DB_BASE::_LIST::_LIST()
    {
        using org_ptr = void (WINAPIV*)(struct _CRYMSG_DB_BASE::_LIST*);
        (org_ptr(0x1400777d0L))(this);
    };
    void _CRYMSG_DB_BASE::_LIST::ctor__LIST()
    {
        using org_ptr = void (WINAPIV*)(struct _CRYMSG_DB_BASE::_LIST*);
        (org_ptr(0x1400777d0L))(this);
    };
END_ATF_NAMESPACE

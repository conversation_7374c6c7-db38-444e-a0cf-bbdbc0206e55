// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _attack_tower_inform_zocl
    {
        unsigned int dwAtterSerial;
        char byAttackPart;
        bool bCritical;
        char byDstID;
        unsigned int dwDstSerial;
        unsigned __int16 wDamage;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

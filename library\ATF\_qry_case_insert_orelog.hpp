// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_insert_orelog
    {
        char byType;
        char szLogDate[17];
        int nLiveUsercnt;
        unsigned int dwOreRemain;
        unsigned int dwTAmount;
    public:
        _qry_case_insert_orelog();
        void ctor__qry_case_insert_orelog();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_insert_orelog, 32>(), "_qry_case_insert_orelog");
END_ATF_NAMESPACE

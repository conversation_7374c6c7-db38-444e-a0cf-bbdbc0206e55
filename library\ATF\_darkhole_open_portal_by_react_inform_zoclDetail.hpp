// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_darkhole_open_portal_by_react_inform_zoclInfo.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        extern ::std::array<hook_record, 1> _darkhole_open_portal_by_react_inform_zocl_functions;
    }; // end namespace Detail
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_insertitem
    {
        unsigned int dwSerial;
        unsigned int dwItemCodeK;
        unsigned int dwItemCodeD;
        unsigned int dwItemCodeU;
        char byType;
        unsigned int dwRemainTime;
        unsigned __int64 lnUID;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_insertitem, 32>(), "_qry_case_insertitem");
END_ATF_NAMESPACE

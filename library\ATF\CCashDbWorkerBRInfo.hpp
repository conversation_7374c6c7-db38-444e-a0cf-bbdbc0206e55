// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerBR.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerBRctor_CCashDbWorkerBR2_ptr = void (WINAPIV*)(struct CCashDbWorkerBR*);
        using CCashDbWorkerBRctor_CCashDbWorkerBR2_clbk = void (WINAPIV*)(struct CCashDbWorkerBR*, CCashDbWorkerBRctor_CCashDbWorkerBR2_ptr);
        using CCashDbWorkerBRGetUseCashQueryStr4_ptr = void (WINAPIV*)(struct CCashDbWorkerBR*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDbWorkerBRGetUseCashQueryStr4_clbk = void (WINAPIV*)(struct CCashDbWorkerBR*, struct _param_cash_update*, int, char*, uint64_t, CCashDbWorkerBRGetUseCashQueryStr4_ptr);
        
        using CCashDbWorkerBRdtor_CCashDbWorkerBR9_ptr = void (WINAPIV*)(struct CCashDbWorkerBR*);
        using CCashDbWorkerBRdtor_CCashDbWorkerBR9_clbk = void (WINAPIV*)(struct CCashDbWorkerBR*, CCashDbWorkerBRdtor_CCashDbWorkerBR9_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

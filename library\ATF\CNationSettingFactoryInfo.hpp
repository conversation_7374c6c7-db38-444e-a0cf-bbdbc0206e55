// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactory.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryctor_CNationSettingFactory2_ptr = void (WINAPIV*)(struct CNationSettingFactory*, int);
        using CNationSettingFactoryctor_CNationSettingFactory2_clbk = void (WINAPIV*)(struct CNationSettingFactory*, int, CNationSettingFactoryctor_CNationSettingFactory2_ptr);
        using CNationSettingFactoryGetKey4_ptr = int (WINAPIV*)(struct CNationSettingFactory*);
        using CNationSettingFactoryGetKey4_clbk = int (WINAPIV*)(struct CNationSettingFactory*, CNationSettingFactoryGetKey4_ptr);
        using CNationSettingFactoryIsExistCheat6_ptr = bool (WINAPIV*)(struct CNationSettingFactory*, char*, struct CNationSettingData*);
        using CNationSettingFactoryIsExistCheat6_clbk = bool (WINAPIV*)(struct CNationSettingFactory*, char*, struct CNationSettingData*, CNationSettingFactoryIsExistCheat6_ptr);
        using CNationSettingFactoryIsNULL8_ptr = bool (WINAPIV*)(struct CNationSettingFactory*);
        using CNationSettingFactoryIsNULL8_clbk = bool (WINAPIV*)(struct CNationSettingFactory*, CNationSettingFactoryIsNULL8_ptr);
        using CNationSettingFactoryRegistCheat10_ptr = bool (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*, char*, bool (WINAPIV*)(struct CPlayer*), int, int);
        using CNationSettingFactoryRegistCheat10_clbk = bool (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*, char*, bool (WINAPIV*)(struct CPlayer*), int, int, CNationSettingFactoryRegistCheat10_ptr);
        using CNationSettingFactoryRegistCheatEndRecord12_ptr = void (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*);
        using CNationSettingFactoryRegistCheatEndRecord12_clbk = void (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*, CNationSettingFactoryRegistCheatEndRecord12_ptr);
        using CNationSettingFactoryRegistCheatTable14_ptr = bool (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*);
        using CNationSettingFactoryRegistCheatTable14_clbk = bool (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*, CNationSettingFactoryRegistCheatTable14_ptr);
        using CNationSettingFactoryRegistCheatTableOnlyInternal16_ptr = bool (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*);
        using CNationSettingFactoryRegistCheatTableOnlyInternal16_clbk = bool (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*, CNationSettingFactoryRegistCheatTableOnlyInternal16_ptr);
        using CNationSettingFactoryRegistCheatTableUnion18_ptr = bool (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*);
        using CNationSettingFactoryRegistCheatTableUnion18_clbk = bool (WINAPIV*)(struct CNationSettingFactory*, struct CNationSettingData*, CNationSettingFactoryRegistCheatTableUnion18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

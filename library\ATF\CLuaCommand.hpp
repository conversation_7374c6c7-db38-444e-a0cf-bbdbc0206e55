// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaCommandVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CLuaCommand
    {
        enum CMD_TYPE
        {
            CMD_NONE = 0x0,
            CMD_STRING = 0x1,
            CMD_FILE = 0x2,
        };
        CLuaCommandVtbl *vfptr;
        char m_byCommand;
        char m_strBuff[2048];
    public:
        CLuaCommand();
        void ctor_CLuaCommand();
        char* GetBuffer();
        char GetType();
        void Init();
        void SetCmd(char byType, char* strName);
        ~CLuaCommand();
        void dtor_CLuaCommand();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CLuaCommand, 2064>(), "CLuaCommand");
END_ATF_NAMESPACE

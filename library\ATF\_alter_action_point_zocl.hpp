// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _alter_action_point_zocl
    {
        char byActionCode;
        unsigned int dwActionPoint;
    public:
        _alter_action_point_zocl();
        void ctor__alter_action_point_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_alter_action_point_zocl, 5>(), "_alter_action_point_zocl");
END_ATF_NAMESPACE

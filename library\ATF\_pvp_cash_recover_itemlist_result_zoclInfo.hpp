// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_pvp_cash_recover_itemlist_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _pvp_cash_recover_itemlist_result_zoclctor__pvp_cash_recover_itemlist_result_zocl2_ptr = void (WINAPIV*)(struct _pvp_cash_recover_itemlist_result_zocl*);
        using _pvp_cash_recover_itemlist_result_zoclctor__pvp_cash_recover_itemlist_result_zocl2_clbk = void (WINAPIV*)(struct _pvp_cash_recover_itemlist_result_zocl*, _pvp_cash_recover_itemlist_result_zoclctor__pvp_cash_recover_itemlist_result_zocl2_ptr);
        using _pvp_cash_recover_itemlist_result_zoclsize4_ptr = int (WINAPIV*)(struct _pvp_cash_recover_itemlist_result_zocl*);
        using _pvp_cash_recover_itemlist_result_zoclsize4_clbk = int (WINAPIV*)(struct _pvp_cash_recover_itemlist_result_zocl*, _pvp_cash_recover_itemlist_result_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

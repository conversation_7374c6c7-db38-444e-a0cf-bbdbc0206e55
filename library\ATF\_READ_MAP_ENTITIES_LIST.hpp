// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _READ_MAP_ENTITIES_LIST
    {
        unsigned __int16 ID;
        float Scale;
        float Pos[3];
        float RotX;
        float RotY;
        __int16 BBmin[3];
        __int16 BBmax[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

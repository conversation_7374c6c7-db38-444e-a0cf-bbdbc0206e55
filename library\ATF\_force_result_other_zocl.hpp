// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CHRID.hpp>


START_ATF_NAMESPACE
    struct _force_result_other_zocl
    {
        char byRetCode;
        char byForceIndex;
        char byForceLv;
        _CHRID idPerformer;
        _CHRID idDster;
        char byAttackSerial;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_MOVE_LOBBY_DELAY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _MOVE_LOBBY_DELAYProcess2_ptr = void (WINAPIV*)(struct _MOVE_LOBBY_DELAY*, unsigned int, unsigned int);
        using _MOVE_LOBBY_DELAYProcess2_clbk = void (WINAPIV*)(struct _MOVE_LOBBY_DELAY*, unsigned int, unsigned int, _MOVE_LOBBY_DELAYProcess2_ptr);
        
        using _MOVE_LOBBY_DELAYctor__MOVE_LOBBY_DELAY4_ptr = void (WINAPIV*)(struct _MOVE_LOBBY_DELAY*);
        using _MOVE_LOBBY_DELAYctor__MOVE_LOBBY_DELAY4_clbk = void (WINAPIV*)(struct _MOVE_LOBBY_DELAY*, _MOVE_LOBBY_DELAYctor__MOVE_LOBBY_DELAY4_ptr);
        
        using _MOVE_LOBBY_DELAYdtor__MOVE_LOBBY_DELAY6_ptr = void (WINAPIV*)(struct _MOVE_LOBBY_DELAY*);
        using _MOVE_LOBBY_DELAYdtor__MOVE_LOBBY_DELAY6_clbk = void (WINAPIV*)(struct _MOVE_LOBBY_DELAY*, _MOVE_LOBBY_DELAYdtor__MOVE_LOBBY_DELAY6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

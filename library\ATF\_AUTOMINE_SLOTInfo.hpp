// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_AUTOMINE_SLOT.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _AUTOMINE_SLOTctor__AUTOMINE_SLOT2_ptr = void (WINAPIV*)(struct _AUTOMINE_SLOT*);
        using _AUTOMINE_SLOTctor__AUTOMINE_SLOT2_clbk = void (WINAPIV*)(struct _AUTOMINE_SLOT*, _AUTOMINE_SLOTctor__AUTOMINE_SLOT2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>



START_ATF_NAMESPACE
    struct tagPROPBAG2
    {
        unsigned int dwType;
        unsigned __int16 vt;
        unsigned __int16 cfType;
        unsigned int dwHint;
        wchar_t *pstrName;
        _GUID clsid;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleState.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CNormalGuildBattleStateRound : CGuildBattleState
        {
        public:
            CNormalGuildBattleStateRound();
            void ctor_CNormalGuildBattleStateRound();
            int Enter(struct CGuildBattle* pkBattle);
            int Enter(struct CNormalGuildBattle* pkBattle);
            int Fin(struct CGuildBattle* pkBattle);
            int Fin(struct CNormalGuildBattle* pkBattle);
            void Log(struct CNormalGuildBattle* pkBattle, char* szFormat);
            int Loop(struct CGuildBattle* pkBattle);
            int Loop(struct CNormalGuildBattle* pkBattle);
            ~CNormalGuildBattleStateRound();
            void dtor_CNormalGuildBattleStateRound();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CNormalGuildBattleStateRound, 8>(), "GUILD_BATTLE::CNormalGuildBattleStateRound");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

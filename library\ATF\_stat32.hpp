// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _stat32
    {
        unsigned int st_dev;
        unsigned __int16 st_ino;
        unsigned __int16 st_mode;
        __int16 st_nlink;
        __int16 st_uid;
        __int16 st_gid;
        unsigned int st_rdev;
        int st_size;
        int st_atime;
        int st_mtime;
        int st_ctime;
    };
END_ATF_NAMESPACE

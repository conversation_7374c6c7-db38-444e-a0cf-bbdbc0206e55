// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Global__GlobalDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Global
    {
        namespace Register
        {
            class GlobalRegister : public IRegister
            {
                public: 
                    void Register() override
                    {
                        auto& hook_core = CATFCore::get_instance();
                        for (auto& r : Global::Detail::_functions)
                            hook_core.reg_wrapper(r.pBind, r);
                    }
            };
        }; // end namespace Register
    }; // end namespace Global
END_ATF_NAMESPACE

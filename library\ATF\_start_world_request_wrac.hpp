// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _start_world_request_wrac
    {
        unsigned int dwGateIP;
        unsigned __int16 wGatePort;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_start_world_request_wrac, 6>(), "_start_world_request_wrac");
END_ATF_NAMESPACE

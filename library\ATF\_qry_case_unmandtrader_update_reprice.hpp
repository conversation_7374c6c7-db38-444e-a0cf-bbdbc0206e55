// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_unmandtrader_update_reprice
    {
        unsigned int dwOldPrice;
        unsigned __int16 wInx;
        unsigned int dwOwnerSerial;
        unsigned __int16 wItemSerial;
        unsigned int dwTax;
        char byProcRet;
        char byType;
        unsigned int dwRegistSerial;
        unsigned int dwNewPrice;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _combine_ex_item_accept_request_clzo
    {
        struct _Select_ItemList_buff
        {
            char bySelectNum;
            char bySelectIndexList[24];
        };
        char byDlgType;
        unsigned int dwCheckKey;
        _Select_ItemList_buff SelectItemBuff;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <cStaticMember_Player.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using cStaticMember_PlayerGetLimitExp2_ptr = long double (WINAPIV*)(struct cStaticMember_Player*, int);
        using cStaticMember_PlayerGetLimitExp2_clbk = long double (WINAPIV*)(struct cStaticMember_Player*, int, cStaticMember_PlayerGetLimitExp2_ptr);
        using cStaticMember_PlayerGetMaxLv4_ptr = int (WINAPIV*)(struct cStaticMember_Player*);
        using cStaticMember_PlayerGetMaxLv4_clbk = int (WINAPIV*)(struct cStaticMember_Player*, cStaticMember_PlayerGetMaxLv4_ptr);
        using cStaticMember_PlayerInitialize6_ptr = bool (WINAPIV*)(struct cStaticMember_Player*);
        using cStaticMember_PlayerInitialize6_clbk = bool (WINAPIV*)(struct cStaticMember_Player*, cStaticMember_PlayerInitialize6_ptr);
        using cStaticMember_PlayerInstance8_ptr = struct cStaticMember_Player* (WINAPIV*)();
        using cStaticMember_PlayerInstance8_clbk = struct cStaticMember_Player* (WINAPIV*)(cStaticMember_PlayerInstance8_ptr);
        using cStaticMember_PlayerRelease10_ptr = void (WINAPIV*)();
        using cStaticMember_PlayerRelease10_clbk = void (WINAPIV*)(cStaticMember_PlayerRelease10_ptr);
        
        using cStaticMember_Playerctor_cStaticMember_Player14_ptr = void (WINAPIV*)(struct cStaticMember_Player*);
        using cStaticMember_Playerctor_cStaticMember_Player14_clbk = void (WINAPIV*)(struct cStaticMember_Player*, cStaticMember_Playerctor_cStaticMember_Player14_ptr);
        using cStaticMember_PlayerloadLimitExpData16_ptr = bool (WINAPIV*)(struct cStaticMember_Player*);
        using cStaticMember_PlayerloadLimitExpData16_clbk = bool (WINAPIV*)(struct cStaticMember_Player*, cStaticMember_PlayerloadLimitExpData16_ptr);
        
        using cStaticMember_Playerdtor_cStaticMember_Player18_ptr = void (WINAPIV*)(struct cStaticMember_Player*);
        using cStaticMember_Playerdtor_cStaticMember_Player18_clbk = void (WINAPIV*)(struct cStaticMember_Player*, cStaticMember_Playerdtor_cStaticMember_Player18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

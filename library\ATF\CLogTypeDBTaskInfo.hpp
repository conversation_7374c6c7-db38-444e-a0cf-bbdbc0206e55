// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogTypeDBTask.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CLogTypeDBTaskctor_CLogTypeDBTask2_ptr = void (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskctor_CLogTypeDBTask2_clbk = void (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskctor_CLogTypeDBTask2_ptr);
        using CLogTypeDBTaskClear4_ptr = void (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskClear4_clbk = void (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskClear4_ptr);
        using CLogTypeDBTaskGetDBRet6_ptr = char (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskGetDBRet6_clbk = char (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskGetDBRet6_ptr);
        using CLogTypeDBTaskGetData8_ptr = char* (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskGetData8_clbk = char* (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskGetData8_ptr);
        using CLogTypeDBTaskGetInx10_ptr = unsigned int (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskGetInx10_clbk = unsigned int (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskGetInx10_ptr);
        using CLogTypeDBTaskGetProcRet12_ptr = char (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskGetProcRet12_clbk = char (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskGetProcRet12_ptr);
        using CLogTypeDBTaskGetQueryType14_ptr = int (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskGetQueryType14_clbk = int (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskGetQueryType14_ptr);
        using CLogTypeDBTaskInit16_ptr = bool (WINAPIV*)(struct CLogTypeDBTask*, unsigned int, unsigned int);
        using CLogTypeDBTaskInit16_clbk = bool (WINAPIV*)(struct CLogTypeDBTask*, unsigned int, unsigned int, CLogTypeDBTaskInit16_ptr);
        using CLogTypeDBTaskSet18_ptr = bool (WINAPIV*)(struct CLogTypeDBTask*, char, char*, uint16_t);
        using CLogTypeDBTaskSet18_clbk = bool (WINAPIV*)(struct CLogTypeDBTask*, char, char*, uint16_t, CLogTypeDBTaskSet18_ptr);
        using CLogTypeDBTaskSetComplete20_ptr = void (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskSetComplete20_clbk = void (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskSetComplete20_ptr);
        using CLogTypeDBTaskSetEmpty22_ptr = void (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskSetEmpty22_clbk = void (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskSetEmpty22_ptr);
        using CLogTypeDBTaskSetRet24_ptr = void (WINAPIV*)(struct CLogTypeDBTask*, char, char);
        using CLogTypeDBTaskSetRet24_clbk = void (WINAPIV*)(struct CLogTypeDBTask*, char, char, CLogTypeDBTaskSetRet24_ptr);
        using CLogTypeDBTaskSetUse26_ptr = void (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskSetUse26_clbk = void (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskSetUse26_ptr);
        
        using CLogTypeDBTaskdtor_CLogTypeDBTask30_ptr = void (WINAPIV*)(struct CLogTypeDBTask*);
        using CLogTypeDBTaskdtor_CLogTypeDBTask30_clbk = void (WINAPIV*)(struct CLogTypeDBTask*, CLogTypeDBTaskdtor_CLogTypeDBTask30_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <BossSchedule_TBL.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using BossSchedule_TBLctor_BossSchedule_TBL2_ptr = void (WINAPIV*)(struct BossSchedule_TBL*);
        using BossSchedule_TBLctor_BossSchedule_TBL2_clbk = void (WINAPIV*)(struct BossSchedule_TBL*, BossSchedule_TBLctor_BossSchedule_TBL2_ptr);
        
        using BossSchedule_TBLdtor_BossSchedule_TBL6_ptr = void (WINAPIV*)(struct BossSchedule_TBL*);
        using BossSchedule_TBLdtor_BossSchedule_TBL6_clbk = void (WINAPIV*)(struct BossSchedule_TBL*, BossSchedule_TBLdtor_BossSchedule_TBL6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

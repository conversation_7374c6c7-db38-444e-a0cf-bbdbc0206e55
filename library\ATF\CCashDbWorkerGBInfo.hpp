// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerGB.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerGBctor_CCashDbWorkerGB2_ptr = void (WINAPIV*)(struct CCashDbWorkerGB*);
        using CCashDbWorkerGBctor_CCashDbWorkerGB2_clbk = void (WINAPIV*)(struct CCashDbWorkerGB*, CCashDbWorkerGBctor_CCashDbWorkerGB2_ptr);
        using CCashDbWorkerGBCompleteWork4_ptr = void (WINAPIV*)(struct CCashDbWorkerGB*);
        using CCashDbWorkerGBCompleteWork4_clbk = void (WINAPIV*)(struct CCashDbWorkerGB*, CCashDbWorkerGBCompleteWork4_ptr);
        using CCashDbWorkerGBDoWork6_ptr = void (WINAPIV*)(struct CCashDbWorkerGB*);
        using CCashDbWorkerGBDoWork6_clbk = void (WINAPIV*)(struct CCashDbWorkerGB*, CCashDbWorkerGBDoWork6_ptr);
        using CCashDbWorkerGBGetUseCashQueryStr8_ptr = void (WINAPIV*)(struct CCashDbWorkerGB*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDbWorkerGBGetUseCashQueryStr8_clbk = void (WINAPIV*)(struct CCashDbWorkerGB*, struct _param_cash_update*, int, char*, uint64_t, CCashDbWorkerGBGetUseCashQueryStr8_ptr);
        using CCashDbWorkerGBInitialize10_ptr = bool (WINAPIV*)(struct CCashDbWorkerGB*);
        using CCashDbWorkerGBInitialize10_clbk = bool (WINAPIV*)(struct CCashDbWorkerGB*, CCashDbWorkerGBInitialize10_ptr);
        using CCashDbWorkerGBRelease12_ptr = void (WINAPIV*)(struct CCashDbWorkerGB*);
        using CCashDbWorkerGBRelease12_clbk = void (WINAPIV*)(struct CCashDbWorkerGB*, CCashDbWorkerGBRelease12_ptr);
        using CCashDbWorkerGB_init_database14_ptr = bool (WINAPIV*)(struct CCashDbWorkerGB*);
        using CCashDbWorkerGB_init_database14_clbk = bool (WINAPIV*)(struct CCashDbWorkerGB*, CCashDbWorkerGB_init_database14_ptr);
        using CCashDbWorkerGB_wait_tsk_cash_select16_ptr = int (WINAPIV*)(struct CCashDbWorkerGB*, struct Task*, int);
        using CCashDbWorkerGB_wait_tsk_cash_select16_clbk = int (WINAPIV*)(struct CCashDbWorkerGB*, struct Task*, int, CCashDbWorkerGB_wait_tsk_cash_select16_ptr);
        using CCashDbWorkerGB_wait_tsk_cash_update18_ptr = int (WINAPIV*)(struct CCashDbWorkerGB*, struct Task*, int);
        using CCashDbWorkerGB_wait_tsk_cash_update18_clbk = int (WINAPIV*)(struct CCashDbWorkerGB*, struct Task*, int, CCashDbWorkerGB_wait_tsk_cash_update18_ptr);
        
        using CCashDbWorkerGBdtor_CCashDbWorkerGB23_ptr = void (WINAPIV*)(struct CCashDbWorkerGB*);
        using CCashDbWorkerGBdtor_CCashDbWorkerGB23_clbk = void (WINAPIV*)(struct CCashDbWorkerGB*, CCashDbWorkerGBdtor_CCashDbWorkerGB23_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

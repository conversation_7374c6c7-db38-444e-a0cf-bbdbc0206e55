// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$E38888FFFF2E8047BDBD55C921E86469.hpp>


START_ATF_NAMESPACE
    struct _NT_TIB32
    {
        unsigned int ExceptionList;
        unsigned int StackBase;
        unsigned int StackLimit;
        unsigned int SubSystemTib;
        $E38888FFFF2E8047BDBD55C921E86469 ___u4;
        unsigned int ArbitraryUserPointer;
        unsigned int Self;
    };
END_ATF_NAMESPACE

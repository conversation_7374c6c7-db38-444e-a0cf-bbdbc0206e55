// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$ACC2D7BA72D8B5F31F315901218BB50A.hpp>
#include <$C98644B53BFCB8058A8635BFBD9C27B0.hpp>
#include <$F49258853DEB26A691BF42EC51DEEB49.hpp>


START_ATF_NAMESPACE
    union $0DB6683490478B6C664578B3C20A3027
    {
        $F49258853DEB26A691BF42EC51DEEB49 SetName;
        $ACC2D7BA72D8B5F31F315901218BB50A DebuggerProbe;
        $C98644B53BFCB8058A8635BFBD9C27B0 RuntimeError;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CLID.hpp>


START_ATF_NAMESPACE
    struct CPartyPlayer
    {
        bool m_bLogin;
         _CLID m_id;
        char m_wszName[17];
        unsigned __int16 m_wZoneIndex;
        struct CPartyPlayer *m_pPartyBoss;
        struct CPartyPlayer *m_pPartyMember[8];
        bool m_bLock;
        char m_byLootShareSystem;
        struct CPartyPlayer *m_pLootAuthor;
        struct CDarkHole *m_pDarkHole;
    public:
        CPartyPlayer();
        void ctor_CPartyPlayer();
        bool DisjointParty();
        void EnterWorld(struct _WA_AVATOR_CODE* pData, uint16_t wZoneIndex);
        void ExitWorld(struct CPartyPlayer** ppoutNewBoss);
        bool FoundParty(struct CPartyPlayer* pParticiper);
        struct CPlayer* GetLootAuthor();
        int GetPopPartyMember();
        struct CPartyPlayer* GetPtrFromSerial(unsigned int dwWorldSerial);
        struct CPartyPlayer** GetPtrPartyMember();
        bool InheritBoss(struct CPartyPlayer* pSuccessor);
        void Init(uint16_t wIndex);
        bool InsertPartyMember(struct CPartyPlayer* pJoiner);
        bool IsJoinPartyLevel(int nJoinerLevel, float fProf);
        bool IsPartyBoss();
        bool IsPartyLock();
        bool IsPartyMember(struct CPlayer* pkObj);
        bool IsPartyMode();
        void PartyListInit();
        bool RemovePartyMember(struct CPartyPlayer* pExiter, struct CPartyPlayer** ppoutNewBoss);
        bool SetLockMode(bool bLock);
        bool SetLootShareMode(char byLootShareMode);
        void SetNextLootAuthor();
    };
END_ATF_NAMESPACE

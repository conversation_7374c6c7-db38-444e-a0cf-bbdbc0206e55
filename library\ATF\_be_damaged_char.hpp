// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _be_damaged_char
    {
        <PERSON>haracter *m_pChar;
        int m_nDamage;
        bool m_bActiveSucc;
        int m_nActiveDamage;
    public:
        _be_damaged_char();
        void ctor__be_damaged_char();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_be_damaged_char, 24>(), "_be_damaged_char");
END_ATF_NAMESPACE

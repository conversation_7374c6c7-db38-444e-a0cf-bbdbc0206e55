// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$E07946B2AF41395E2A15EDA40595291F.hpp>


START_ATF_NAMESPACE
    struct _OVERLAPPED
    {
        unsigned __int64 Internal;
        unsigned __int64 InternalHigh;
        $E07946B2AF41395E2A15EDA40595291F ___u2;
        void *hEvent;
    };
END_ATF_NAMESPACE

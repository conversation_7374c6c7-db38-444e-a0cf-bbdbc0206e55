// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCheckSumGuildData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCheckSumGuildDatactor_CCheckSumGuildData2_ptr = void (WINAPIV*)(struct CCheckSumGuildData*, unsigned int);
        using CCheckSumGuildDatactor_CCheckSumGuildData2_clbk = void (WINAPIV*)(struct CCheckSumGuildData*, unsigned int, CCheckSumGuildDatactor_CCheckSumGuildData2_ptr);
        using CCheckSumGuildDataCheckDiff4_ptr = int (WINAPIV*)(struct CCheckSumGuildData*, struct CRFWorldDatabase*, char*, struct CCheckSumGuildData*);
        using CCheckSumGuildDataCheckDiff4_clbk = int (WINAPIV*)(struct CCheckSumGuildData*, struct CRFWorldDatabase*, char*, struct CCheckSumGuildData*, CCheckSumGuildDataCheckDiff4_ptr);
        using CCheckSumGuildDataDecode6_ptr = void (WINAPIV*)(struct CCheckSumGuildData*, long double, long double);
        using CCheckSumGuildDataDecode6_clbk = void (WINAPIV*)(struct CCheckSumGuildData*, long double, long double, CCheckSumGuildDataDecode6_ptr);
        using CCheckSumGuildDataEncode8_ptr = void (WINAPIV*)(struct CCheckSumGuildData*, long double, long double);
        using CCheckSumGuildDataEncode8_clbk = void (WINAPIV*)(struct CCheckSumGuildData*, long double, long double, CCheckSumGuildDataEncode8_ptr);
        using CCheckSumGuildDataGetDalant10_ptr = long double (WINAPIV*)(struct CCheckSumGuildData*);
        using CCheckSumGuildDataGetDalant10_clbk = long double (WINAPIV*)(struct CCheckSumGuildData*, CCheckSumGuildDataGetDalant10_ptr);
        using CCheckSumGuildDataGetGold12_ptr = long double (WINAPIV*)(struct CCheckSumGuildData*);
        using CCheckSumGuildDataGetGold12_clbk = long double (WINAPIV*)(struct CCheckSumGuildData*, CCheckSumGuildDataGetGold12_ptr);
        using CCheckSumGuildDataInsert14_ptr = bool (WINAPIV*)(struct CCheckSumGuildData*, struct CRFWorldDatabase*);
        using CCheckSumGuildDataInsert14_clbk = bool (WINAPIV*)(struct CCheckSumGuildData*, struct CRFWorldDatabase*, CCheckSumGuildDataInsert14_ptr);
        using CCheckSumGuildDataLoad16_ptr = int (WINAPIV*)(struct CCheckSumGuildData*, struct CRFWorldDatabase*, struct CCheckSumGuildData*);
        using CCheckSumGuildDataLoad16_clbk = int (WINAPIV*)(struct CCheckSumGuildData*, struct CRFWorldDatabase*, struct CCheckSumGuildData*, CCheckSumGuildDataLoad16_ptr);
        using CCheckSumGuildDataSetValue18_ptr = void (WINAPIV*)(struct CCheckSumGuildData*, CCheckSumGuildData::COLUMN_D_TYPE, long double);
        using CCheckSumGuildDataSetValue18_clbk = void (WINAPIV*)(struct CCheckSumGuildData*, CCheckSumGuildData::COLUMN_D_TYPE, long double, CCheckSumGuildDataSetValue18_ptr);
        using CCheckSumGuildDataUpdate20_ptr = bool (WINAPIV*)(struct CCheckSumGuildData*, struct CRFWorldDatabase*);
        using CCheckSumGuildDataUpdate20_clbk = bool (WINAPIV*)(struct CCheckSumGuildData*, struct CRFWorldDatabase*, CCheckSumGuildDataUpdate20_ptr);
        
        using CCheckSumGuildDatadtor_CCheckSumGuildData22_ptr = void (WINAPIV*)(struct CCheckSumGuildData*);
        using CCheckSumGuildDatadtor_CCheckSumGuildData22_clbk = void (WINAPIV*)(struct CCheckSumGuildData*, CCheckSumGuildDatadtor_CCheckSumGuildData22_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

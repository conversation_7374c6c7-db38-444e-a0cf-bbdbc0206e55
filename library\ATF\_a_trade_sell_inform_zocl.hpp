// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _a_trade_sell_inform_zocl
    {
        unsigned __int16 wItemSerial;
        unsigned int dwAddDalant;
        unsigned int dwTaxDalant;
        unsigned int dwTotalDalant;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Container_base.hpp>
#include <std__allocator.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 1)
        template<typename _Ty, typename _Alloc = allocator<_Ty>>
        struct  _List_nod : _Container_base
        {
            struct _Node
            {
                _Node *_Next;
                _Node *_Prev;
                _Ty _Myval;
            };
            allocator<_Node> _Alnod;
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE

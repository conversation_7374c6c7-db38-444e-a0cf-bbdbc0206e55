// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _guild_manage_request_clzo
    {
        char byManageType;
        unsigned int dwManageDst;
        unsigned int dwManageObj1;
        unsigned int dwManageObj2;
        unsigned int dwManageObj3;
    public:
        _guild_manage_request_clzo();
        void ctor__guild_manage_request_clzo();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_guild_manage_request_clzo, 17>(), "_guild_manage_request_clzo");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _STAT_DB_BASE
    {
        unsigned int m_dwDamWpCnt[2];
        unsigned int m_dwDefenceCnt;
        unsigned int m_dwShieldCnt;
        unsigned int m_dwSkillCum[48];
        unsigned int m_dwForceCum[24];
        unsigned int m_dwMakeCum[3];
        unsigned int m_dwSpecialCum;
    public:
        static int GetStatIndex(char byMasteryClass, char byIndex);
        void Init();
        static bool IsRangePerMastery(char byMasteryClass, char byIndex);
        _STAT_DB_BASE();
        void ctor__STAT_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_STAT_DB_BASE, 320>(), "_STAT_DB_BASE");
END_ATF_NAMESPACE

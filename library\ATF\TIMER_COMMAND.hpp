// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum TIMER_COMMAND
    {
      TIMER_RESET = 0x0,
      TIMER_START = 0x1,
      TIMER_STOP = 0x2,
      TIMER_ADVANCE = 0x3,
      TIMER_GETABSOLUTETIME = 0x4,
      TIMER_GETAPPTIME = 0x5,
      TIMER_GETELAPSEDTIME = 0x6,
    };
END_ATF_NAMESPACE

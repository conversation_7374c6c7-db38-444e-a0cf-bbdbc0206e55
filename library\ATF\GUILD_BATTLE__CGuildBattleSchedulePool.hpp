// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleSchedule.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleSchedulePool
        {
            unsigned int m_uiMapCnt;
            unsigned int m_dwMaxScheduleCnt;
            CGuildBattleSchedule **m_ppkSchedule;
        public:
            CGuildBattleSchedulePool();
            void ctor_CGuildBattleSchedulePool();
            void ClearAll();
            void ClearByDayID(unsigned int uiDayID);
            static void Destroy();
            struct CGuildBattleSchedule* Get(unsigned int uiSLID, unsigned int dwStartInx);
            struct CGuildBattleSchedule* Get(unsigned int dwSID);
            struct CGuildBattleSchedule* GetRef(unsigned int dwSID);
            unsigned int GetSID(unsigned int uiSLID, unsigned int dwStartInx);
            bool Init(unsigned int uiMapCnt);
            static struct CGuildBattleSchedulePool* Instance();
            ~CGuildBattleSchedulePool();
            void dtor_CGuildBattleSchedulePool();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

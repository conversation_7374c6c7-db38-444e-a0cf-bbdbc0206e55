// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CAtlAutoThreadModuleT.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CAtlAutoThreadModule : CAtlAutoThreadModuleT<CAtlAutoThreadModule,CComSimpleThreadAllocator,4294967295>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _cash_discount_ini_
    {
        int m_bUseCashDiscount;
        int m_bRepeat;
        char m_byRepeatDay;
        unsigned __int16 m_wCsDiscount;
        int m_bCoEvent;
        int m_NextEventTime[3];
        int m_wYear[3];
        int m_byMonth[3];
        int m_byDay[3];
        int m_byHour[3];
        int m_byMinute[3];
        int m_cdeTime[3];
    public:
        _cash_discount_ini_();
        void ctor__cash_discount_ini_();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_cash_discount_ini_, 100>(), "_cash_discount_ini_");
END_ATF_NAMESPACE

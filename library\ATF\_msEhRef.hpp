// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _msEhRef
    {
        int Id;
        int Cnt1;
        void *__ptr32 Tbl1;
        int Cnt2;
        void *__ptr32 Tbl2;
        int Cnt3;
        void *__ptr32 Tbl3;
        int _unk;
    };    
    static_assert(ATF::checkSize<_msEhRef, 32>(), "_msEhRef");
END_ATF_NAMESPACE

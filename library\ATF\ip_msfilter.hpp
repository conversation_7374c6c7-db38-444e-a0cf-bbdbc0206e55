// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <in_addr.hpp>


START_ATF_NAMESPACE
    struct ip_msfilter
    {
        in_addr imsf_multiaddr;
        in_addr imsf_interface;
        unsigned int imsf_fmode;
        unsigned int imsf_numsrc;
        in_addr imsf_slist[1];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_guild_master_advantage_info.hpp>


START_ATF_NAMESPACE
    struct CGuildMasterEffect
    {
        char m_byAdjustableGrade;
        _guild_master_advantage_info m_EffectData[8];
    public:
        CGuildMasterEffect();
        void ctor_CGuildMasterEffect();
        static struct CGuildMasterEffect* GetInstance();
        void adjust_effect(struct CPlayer* pP, char byGrade, bool bAdd);
        bool change_player(struct CPlayer* pP, char byBeforeGrade, char byAfterGrade);
        char get_AdjustableGrade();
        char get_AttactValueByGrade(char byGrade);
        char get_DefenceValueByGrade(char byGrade);
        bool in_player(struct CPlayer* pP, char byGrade);
        bool init();
        bool out_player(struct CPlayer* pP, char byGrade);
        void show_to_all(struct CPlayer* pP, char byBeforeGrade, char byGrade, char byState);
    };    
    //static_assert(ATF::checkSize<CGuildMasterEffect, 68>(), "CGuildMasterEffect");
END_ATF_NAMESPACE

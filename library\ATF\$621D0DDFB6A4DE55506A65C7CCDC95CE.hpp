// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $621D0DDFB6A4DE55506A65C7CCDC95CE
    {
        unsigned __int32 m_CashReName : 3;
        unsigned __int32 m_CashUnused : 5;
    };    
    static_assert(ATF::checkSize<$621D0DDFB6A4DE55506A65C7CCDC95CE, 4>(), "$621D0DDFB6A4DE55506A65C7CCDC95CE");
END_ATF_NAMESPACE

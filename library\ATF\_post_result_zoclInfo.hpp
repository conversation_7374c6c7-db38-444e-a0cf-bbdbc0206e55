// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_post_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _post_result_zoclctor__post_result_zocl2_ptr = void (WINAPIV*)(struct _post_result_zocl*);
        using _post_result_zoclctor__post_result_zocl2_clbk = void (WINAPIV*)(struct _post_result_zocl*, _post_result_zoclctor__post_result_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

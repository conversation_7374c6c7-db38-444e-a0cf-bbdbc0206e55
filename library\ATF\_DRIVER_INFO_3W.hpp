// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _DRIVER_INFO_3W
    {
        unsigned int cVersion;
        wchar_t *pName;
        wchar_t *pEnvironment;
        wchar_t *pDriverPath;
        wchar_t *pDataFile;
        wchar_t *pConfigFile;
        wchar_t *pHelpFile;
        wchar_t *pDependentFiles;
        wchar_t *pMonitorName;
        wchar_t *pDefaultDataType;
    };
END_ATF_NAMESPACE

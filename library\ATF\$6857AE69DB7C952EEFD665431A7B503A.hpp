// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $6857AE69DB7C952EEFD665431A7B503A
    {
        BYTE gap0[8];
        long double *pdate;
    };    
    static_assert(ATF::checkSize<$6857AE69DB7C952EEFD665431A7B503A, 16>(), "$6857AE69DB7C952EEFD665431A7B503A");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct IMAGE_LOAD_CONFIG_DIRECTORY32
    {
        unsigned int Size;
        unsigned int TimeDateStamp;
        unsigned __int16 MajorVersion;
        unsigned __int16 MinorVersion;
        unsigned int GlobalFlagsClear;
        unsigned int GlobalFlagsSet;
        unsigned int CriticalSectionDefaultTimeout;
        unsigned int DeCommitFreeBlockThreshold;
        unsigned int DeCommitTotalFreeThreshold;
        unsigned int LockPrefixTable;
        unsigned int MaximumAllocationSize;
        unsigned int VirtualMemoryThreshold;
        unsigned int ProcessHeapFlags;
        unsigned int ProcessAffinityMask;
        unsigned __int16 CSDVersion;
        unsigned __int16 Reserved1;
        unsigned int EditList;
        unsigned int SecurityCookie;
        unsigned int SEHandlerTable;
        unsigned int SEHandlerCount;
    };
END_ATF_NAMESPACE

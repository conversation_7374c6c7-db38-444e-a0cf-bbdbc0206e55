// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FILETIME.hpp>



START_ATF_NAMESPACE
    struct _DRIVER_INFO_6W
    {
        unsigned int cVersion;
        wchar_t *pName;
        wchar_t *pEnvironment;
        wchar_t *pDriverPath;
        wchar_t *pDataFile;
        wchar_t *pConfigFile;
        wchar_t *pHelpFile;
        wchar_t *pDependentFiles;
        wchar_t *pMonitorName;
        wchar_t *pDefaultDataType;
        wchar_t *pszzPreviousNames;
        _FILETIME ftDriverDate;
        unsigned __int64 dwlDriverVersion;
        wchar_t *pszMfgName;
        wchar_t *pszOEMUrl;
        wchar_t *pszHardwareID;
        wchar_t *pszProvider;
    };
END_ATF_NAMESPACE

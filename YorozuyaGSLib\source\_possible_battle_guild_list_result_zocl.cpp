#include <_possible_battle_guild_list_result_zocl.hpp>


START_ATF_NAMESPACE
    _possible_battle_guild_list_result_zocl::_possible_battle_guild_list_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _possible_battle_guild_list_result_zocl*);
        (org_ptr(0x1403d07e0L))(this);
    };
    void _possible_battle_guild_list_result_zocl::ctor__possible_battle_guild_list_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _possible_battle_guild_list_result_zocl*);
        (org_ptr(0x1403d07e0L))(this);
    };
    int _possible_battle_guild_list_result_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _possible_battle_guild_list_result_zocl*);
        return (org_ptr(0x1403d0860L))(this);
    };
    
END_ATF_NAMESPACE

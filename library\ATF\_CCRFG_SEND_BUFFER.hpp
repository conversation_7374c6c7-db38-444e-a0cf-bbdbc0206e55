// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetProcess.hpp>
#include <CNetWorking.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _CCRFG_SEND_BUFFER
    {
        CNetProcess *pTargetProc;
        CNetWorking *pNetwork;
        unsigned __int16 wMsgSize;
        char sSend<PERSON><PERSON><PERSON>[20000];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

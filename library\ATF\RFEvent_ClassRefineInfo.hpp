// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RFEvent_ClassRefine.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using RFEvent_ClassRefineCanDoEvent2_ptr = int (WINAPIV*)(struct RFEvent_ClassRefine*, struct CPlayer*);
        using RFEvent_ClassRefineCanDoEvent2_clbk = int (WINAPIV*)(struct RFEvent_ClassRefine*, struct CPlayer*, RFEvent_ClassRefineCanDoEvent2_ptr);
        using RFEvent_ClassRefineCheckRefineEventData4_ptr = bool (WINAPIV*)(struct RFEvent_ClassRefine*);
        using RFEvent_ClassRefineCheckRefineEventData4_clbk = bool (WINAPIV*)(struct RFEvent_ClassRefine*, RFEvent_ClassRefineCheckRefineEventData4_ptr);
        using RFEvent_ClassRefineDoEvent6_ptr = int (WINAPIV*)(struct RFEvent_ClassRefine*, struct CPlayer*);
        using RFEvent_ClassRefineDoEvent6_clbk = int (WINAPIV*)(struct RFEvent_ClassRefine*, struct CPlayer*, RFEvent_ClassRefineDoEvent6_ptr);
        using RFEvent_ClassRefineGetPlayerState8_ptr = char* (WINAPIV*)(struct RFEvent_ClassRefine*, unsigned int, unsigned int);
        using RFEvent_ClassRefineGetPlayerState8_clbk = char* (WINAPIV*)(struct RFEvent_ClassRefine*, unsigned int, unsigned int, RFEvent_ClassRefineGetPlayerState8_ptr);
        using RFEvent_ClassRefineInitialzie10_ptr = bool (WINAPIV*)(struct RFEvent_ClassRefine*);
        using RFEvent_ClassRefineInitialzie10_clbk = bool (WINAPIV*)(struct RFEvent_ClassRefine*, RFEvent_ClassRefineInitialzie10_ptr);
        using RFEvent_ClassRefineIsDbUpdate12_ptr = bool (WINAPIV*)(struct RFEvent_ClassRefine*, unsigned int);
        using RFEvent_ClassRefineIsDbUpdate12_clbk = bool (WINAPIV*)(struct RFEvent_ClassRefine*, unsigned int, RFEvent_ClassRefineIsDbUpdate12_ptr);
        using RFEvent_ClassRefineIsEnable14_ptr = bool (WINAPIV*)(struct RFEvent_ClassRefine*);
        using RFEvent_ClassRefineIsEnable14_clbk = bool (WINAPIV*)(struct RFEvent_ClassRefine*, RFEvent_ClassRefineIsEnable14_ptr);
        using RFEvent_ClassRefineLoop16_ptr = void (WINAPIV*)(struct RFEvent_ClassRefine*);
        using RFEvent_ClassRefineLoop16_clbk = void (WINAPIV*)(struct RFEvent_ClassRefine*, RFEvent_ClassRefineLoop16_ptr);
        
        using RFEvent_ClassRefinector_RFEvent_ClassRefine18_ptr = void (WINAPIV*)(struct RFEvent_ClassRefine*);
        using RFEvent_ClassRefinector_RFEvent_ClassRefine18_clbk = void (WINAPIV*)(struct RFEvent_ClassRefine*, RFEvent_ClassRefinector_RFEvent_ClassRefine18_ptr);
        using RFEvent_ClassRefineReadClassRefineEventInfo20_ptr = void (WINAPIV*)(struct RFEvent_ClassRefine*);
        using RFEvent_ClassRefineReadClassRefineEventInfo20_clbk = void (WINAPIV*)(struct RFEvent_ClassRefine*, RFEvent_ClassRefineReadClassRefineEventInfo20_ptr);
        using RFEvent_ClassRefineResetRefineData22_ptr = void (WINAPIV*)(struct RFEvent_ClassRefine*);
        using RFEvent_ClassRefineResetRefineData22_clbk = void (WINAPIV*)(struct RFEvent_ClassRefine*, RFEvent_ClassRefineResetRefineData22_ptr);
        using RFEvent_ClassRefineSetEvent24_ptr = bool (WINAPIV*)(struct RFEvent_ClassRefine*, char*, int, bool);
        using RFEvent_ClassRefineSetEvent24_clbk = bool (WINAPIV*)(struct RFEvent_ClassRefine*, char*, int, bool, RFEvent_ClassRefineSetEvent24_ptr);
        using RFEvent_ClassRefineSetPlayerState26_ptr = bool (WINAPIV*)(struct RFEvent_ClassRefine*, void*, int);
        using RFEvent_ClassRefineSetPlayerState26_clbk = bool (WINAPIV*)(struct RFEvent_ClassRefine*, void*, int, RFEvent_ClassRefineSetPlayerState26_ptr);
        
        using RFEvent_ClassRefinedtor_RFEvent_ClassRefine31_ptr = void (WINAPIV*)(struct RFEvent_ClassRefine*);
        using RFEvent_ClassRefinedtor_RFEvent_ClassRefine31_clbk = void (WINAPIV*)(struct RFEvent_ClassRefine*, RFEvent_ClassRefinedtor_RFEvent_ClassRefine31_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

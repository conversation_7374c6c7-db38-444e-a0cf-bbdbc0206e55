// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct Player_TL_Status
    {
        bool m_bUse;
        bool m_bAgeLimit;
        char m_byTL_Status;
        long double m_dPercent;
        unsigned int m_dwFatigue;
        unsigned int m_dwStartTime;
        unsigned int m_dwAccountSerial;
        unsigned int m_dwLastLogoutTime;
        bool m_bUpdateLogout;
    public:
        char GetTLStatus();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<Player_TL_Status, 40>(), "Player_TL_Status");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _DOCINFOW
    {
        int cbSize;
        const wchar_t *lpszDocName;
        const wchar_t *lpszOutput;
        const wchar_t *lpszDatatype;
        unsigned int fwType;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

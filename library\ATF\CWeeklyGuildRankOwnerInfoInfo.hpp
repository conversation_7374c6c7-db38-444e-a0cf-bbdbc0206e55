// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CWeeklyGuildRankOwnerInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CWeeklyGuildRankOwnerInfoctor_CWeeklyGuildRankOwnerInfo2_ptr = void (WINAPIV*)(struct CWeeklyGuildRankOwnerInfo*);
        using CWeeklyGuildRankOwnerInfoctor_CWeeklyGuildRankOwnerInfo2_clbk = void (WINAPIV*)(struct CWeeklyGuildRankOwnerInfo*, CWeeklyGuildRankOwnerInfoctor_CWeeklyGuildRankOwnerInfo2_ptr);
        using CWeeklyGuildRankOwnerInfoClear4_ptr = void (WINAPIV*)(struct CWeeklyGuildRankOwnerInfo*);
        using CWeeklyGuildRankOwnerInfoClear4_clbk = void (WINAPIV*)(struct CWeeklyGuildRankOwnerInfo*, CWeeklyGuildRankOwnerInfoClear4_ptr);
        using CWeeklyGuildRankOwnerInfoIsEmpty6_ptr = bool (WINAPIV*)(struct CWeeklyGuildRankOwnerInfo*);
        using CWeeklyGuildRankOwnerInfoIsEmpty6_clbk = bool (WINAPIV*)(struct CWeeklyGuildRankOwnerInfo*, CWeeklyGuildRankOwnerInfoIsEmpty6_ptr);
        
        using CWeeklyGuildRankOwnerInfodtor_CWeeklyGuildRankOwnerInfo8_ptr = void (WINAPIV*)(struct CWeeklyGuildRankOwnerInfo*);
        using CWeeklyGuildRankOwnerInfodtor_CWeeklyGuildRankOwnerInfo8_clbk = void (WINAPIV*)(struct CWeeklyGuildRankOwnerInfo*, CWeeklyGuildRankOwnerInfodtor_CWeeklyGuildRankOwnerInfo8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <BossSchedule.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using BossSchedulector_BossSchedule2_ptr = void (WINAPIV*)(struct BossSchedule*);
        using BossSchedulector_BossSchedule2_clbk = void (WINAPIV*)(struct BossSchedule*, BossSchedulector_BossSchedule2_ptr);
        using BossScheduleMake_LastTimeRespawnSystemTime4_ptr = struct ATL::CTime* (WINAPIV*)(struct ATL::CTime*, char*);
        using BossScheduleMake_LastTimeRespawnSystemTime4_clbk = struct ATL::CTime* (WINAPIV*)(struct ATL::CTime*, char*, BossScheduleMake_LastTimeRespawnSystemTime4_ptr);
        using BossScheduleMake_LastTimeRespawnSystemTimeString6_ptr = bool (WINAPIV*)(struct BossSchedule*, char*, int);
        using BossScheduleMake_LastTimeRespawnSystemTimeString6_clbk = bool (WINAPIV*)(struct BossSchedule*, char*, int, BossScheduleMake_LastTimeRespawnSystemTimeString6_ptr);
        using BossScheduleMake_LiveCount8_ptr = uint16_t (WINAPIV*)(char*);
        using BossScheduleMake_LiveCount8_clbk = uint16_t (WINAPIV*)(char*, BossScheduleMake_LiveCount8_ptr);
        using BossScheduleMake_LiveCountString10_ptr = bool (WINAPIV*)(struct BossSchedule*, char*, int);
        using BossScheduleMake_LiveCountString10_clbk = bool (WINAPIV*)(struct BossSchedule*, char*, int, BossScheduleMake_LiveCountString10_ptr);
        using BossScheduleSave_LastRespawnSystemTime12_ptr = void (WINAPIV*)(struct BossSchedule*, struct ATL::CTime*);
        using BossScheduleSave_LastRespawnSystemTime12_clbk = void (WINAPIV*)(struct BossSchedule*, struct ATL::CTime*, BossScheduleSave_LastRespawnSystemTime12_ptr);
        using BossScheduleSave_LiveCount14_ptr = void (WINAPIV*)(struct BossSchedule*, uint16_t);
        using BossScheduleSave_LiveCount14_clbk = void (WINAPIV*)(struct BossSchedule*, uint16_t, BossScheduleSave_LiveCount14_ptr);
        
        using BossScheduledtor_BossSchedule18_ptr = void (WINAPIV*)(struct BossSchedule*);
        using BossScheduledtor_BossSchedule18_clbk = void (WINAPIV*)(struct BossSchedule*, BossScheduledtor_BossSchedule18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

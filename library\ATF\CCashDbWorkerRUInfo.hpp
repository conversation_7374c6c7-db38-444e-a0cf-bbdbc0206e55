// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerRU.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerRUctor_CCashDbWorkerRU2_ptr = void (WINAPIV*)(struct CCashDbWorkerRU*);
        using CCashDbWorkerRUctor_CCashDbWorkerRU2_clbk = void (WINAPIV*)(struct CCashDbWorkerRU*, CCashDbWorkerRUctor_CCashDbWorkerRU2_ptr);
        using CCashDbWorkerRURelease4_ptr = void (WINAPIV*)(struct CCashDbWorkerRU*);
        using CCashDbWorkerRURelease4_clbk = void (WINAPIV*)(struct CCashDbWorkerRU*, CCashDbWorkerRURelease4_ptr);
        using CCashDbWorkerRU_init_database6_ptr = bool (WINAPIV*)(struct CCashDbWorkerRU*);
        using CCashDbWorkerRU_init_database6_clbk = bool (WINAPIV*)(struct CCashDbWorkerRU*, CCashDbWorkerRU_init_database6_ptr);
        using CCashDbWorkerRU_wait_tsk_cash_rollback8_ptr = int (WINAPIV*)(struct CCashDbWorkerRU*, struct Task*);
        using CCashDbWorkerRU_wait_tsk_cash_rollback8_clbk = int (WINAPIV*)(struct CCashDbWorkerRU*, struct Task*, CCashDbWorkerRU_wait_tsk_cash_rollback8_ptr);
        using CCashDbWorkerRU_wait_tsk_cash_select10_ptr = int (WINAPIV*)(struct CCashDbWorkerRU*, struct Task*);
        using CCashDbWorkerRU_wait_tsk_cash_select10_clbk = int (WINAPIV*)(struct CCashDbWorkerRU*, struct Task*, CCashDbWorkerRU_wait_tsk_cash_select10_ptr);
        using CCashDbWorkerRU_wait_tsk_cash_update12_ptr = int (WINAPIV*)(struct CCashDbWorkerRU*, struct Task*);
        using CCashDbWorkerRU_wait_tsk_cash_update12_clbk = int (WINAPIV*)(struct CCashDbWorkerRU*, struct Task*, CCashDbWorkerRU_wait_tsk_cash_update12_ptr);
        
        using CCashDbWorkerRUdtor_CCashDbWorkerRU17_ptr = void (WINAPIV*)(struct CCashDbWorkerRU*);
        using CCashDbWorkerRUdtor_CCashDbWorkerRU17_clbk = void (WINAPIV*)(struct CCashDbWorkerRU*, CCashDbWorkerRUdtor_CCashDbWorkerRU17_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

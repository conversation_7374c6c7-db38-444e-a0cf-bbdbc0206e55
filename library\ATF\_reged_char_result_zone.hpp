// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_REGED_AVATOR_DB.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _reged_char_result_zone
    {
        enum LOCK_TYPE
        {
            LT_NONE = 0x0,
            LT_REQUEST_MOVE_CHARACTER = 0x1,
            LT_TOURNAMENT_CHARACTER = 0x2,
        };
        char byRetCode;
        char byCharNum;
        int iLockType[3];
        _REGED_AVATOR_DB RegedList[3];
    public:
        _reged_char_result_zone();
        void ctor__reged_char_result_zone();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_reged_char_result_zone, 221>(), "_reged_char_result_zone");
END_ATF_NAMESPACE

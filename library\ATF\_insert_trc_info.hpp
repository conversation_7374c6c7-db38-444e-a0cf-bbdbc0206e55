// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _insert_trc_info
    {
        char byRace;
         unsigned int dwGSerial;
        char szGuildName[17];
        char byMatterType;
         unsigned int dwMatterDst;
        char wszMatterDst[64];
         unsigned int dwNext;
        char byCurrTax;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_insert_trc_info, 96>(), "_insert_trc_info");
END_ATF_NAMESPACE

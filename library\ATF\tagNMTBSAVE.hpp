// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_TBBUTTON.hpp>
#include <tagNMHDR.hpp>


START_ATF_NAMESPACE
    struct tagNMTBSAVE
    {
        tagNMHDR hdr;
        unsigned int *pData;
        unsigned int *pCurrent;
        unsigned int cbData;
        int iItem;
        int cButtons;
        _TBBUTTON tbButton;
    };
END_ATF_NAMESPACE

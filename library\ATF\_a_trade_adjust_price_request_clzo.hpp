// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _a_trade_adjust_price_request_clzo
    {
        char byStoreIndex;
         unsigned __int16 wItemSerial;
         unsigned int dwNewPrice;
         unsigned int dwRegistSerial;
        char byTaxRate;
    };
END_ATF_NAMESPACE

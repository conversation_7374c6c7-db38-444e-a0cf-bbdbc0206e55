// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _linkboard_download_result_zocl
    {
        unsigned __int16 wLinkBoard[50];
        char byLinkLock;
        unsigned int dwSkill[2];
        unsigned int dwForce[2];
        unsigned int dwCharacter[2];
        unsigned int dwAnimus[2];
        unsigned int dwInven;
        unsigned int dwInvenWindow[5];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

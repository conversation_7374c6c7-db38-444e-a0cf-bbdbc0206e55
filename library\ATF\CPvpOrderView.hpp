// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CPvpOrderView
    {
        unsigned int m_dwLastAttackTime;
        unsigned int m_dwLastDamagedTime;
        int m_nKillCnt;
        int m_nDeahtCnt;
        long double m_dTodayPvpPoint;
        long double m_dOriginalPvpPoint;
        long double m_dPvpPoint;
        long double m_dPvpTempCash;
        long double m_dPvpCash;
        bool m_bAttack;
        bool m_bDamaged;
        struct _PVP_ORDER_VIEW_DB_BASE *m_pkInfo;
    public:
        long double GetPvpCash();
        long double GetPvpTempCash();
        bool Init();
        void Loop(uint16_t wIndex);
        void Notify_OrderView(uint16_t wIndex);
        void Notify_Point(uint16_t wIndex, long double dChangePoint, unsigned int dwTarSerial);
        void Notify_PvPEnd(uint16_t wIndex);
        void Notify_PvpTempCash(uint16_t wIndex);
        void ResetPvPOrderView();
        void SetOrderViewAttackState();
        void SetOrderViewDamagedState();
        void SetPvpCash(long double dAlter);
        bool SetPvpOrderView(long double dPvpPoint, struct _PVP_ORDER_VIEW_DB_BASE* pkInfo, struct CPlayer* pOne);
        void Update(int64_t tUpdateDate, int nDeath, int nKill, long double dTodayStacked, long double dPvpPoint, long double dPvpTempCash);
        void UpdatePvPDeath(uint16_t wIndex, unsigned int dwTarSerial);
        void UpdatePvPKill(uint16_t wIndex, unsigned int dwTarSerial);
        void UpdatePvPPoint(long double dUpPoint, long double dNewPoint);
        void UpdatePvpCash(long double dPvpCash);
        void Update_ContHaveCash(char byCnt);
        void Update_ContLoseCash(char byCnt);
        void Update_KillerList(unsigned int dwSerial, int nIndex);
        void Update_PvpTempCash(uint16_t wIndex, long double dTempPvpCash);
        void Update_RaceWarRecvr(bool bUse);
    };    
    static_assert(ATF::checkSize<CPvpOrderView, 72>(), "CPvpOrderView");
END_ATF_NAMESPACE

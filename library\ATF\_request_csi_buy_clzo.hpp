// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS_INDIV.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _request_csi_buy_clzo
    {
        struct __item
        {
            int nPrice;
            char byDiscount;
            char byTblCode;
            unsigned __int16 wItemIdx;
            unsigned __int16 wStoreIdx;
            char byOverlapNum;
             int nEvent[8];
            char byEventType;
        };
        char nNum;
        char bySetKind;
        char byCouponNum;
        _STORAGE_POS_INDIV CouponItem[3];
        __item item[20];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>
#include <CMapData.hpp>
#include <CPlayer.hpp>
#include <CRFWorldDatabase.hpp>
#include <CRecordData.hpp>
#include <PotionInnerData.hpp>
#include <_CheckPotion_fld.hpp>
#include <_ContPotionData.hpp>
#include <_skill_fld.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CPotionMgr
    {
        struct _ParamData
        {
            float fValue;
            struct _PotionItem_fld *pPotionFld;
            unsigned int dwCurTime;
            unsigned int dwEndTime;
            unsigned int dwNextTime;
        };
        PotionInnerData m_PotionInnerData;
        CRecordData m_tblPotionEffectData;
        CRecordData m_tblPotionCheckData;
    public:
        int ApplyPotion(struct CPlayer* pUsePlayer, struct CPlayer* pApplyPlayer, struct _skill_fld* pEffecFld, struct _CheckPotion_fld* pCheckFld, struct _PotionItem_fld* pfB, bool bCommonPotion);
        CPotionMgr();
        void ctor_CPotionMgr();
        bool CheckPotionUsableMap(struct _PotionItem_fld* pPotionFld, struct CMapData* pMap);
        void Complete_RenameChar_DB_Select(char byRet, char* p);
        void Complete_RenameChar_DB_Update(char byRet, char* p);
        bool DatafileInit();
        void InsertMovePotionStoneEffect(struct CPlayer* pApplyPlayer);
        int InsertPotionContEffect(struct CPlayer* pApplyPlayer, struct _ContPotionData* ContPotionData, struct _skill_fld* pEffecFld, unsigned int dwDurTime);
        bool InsertRenamePotion(struct CRFWorldDatabase* pkWorldDB, char* pData);
        bool IsPotionDelayUseIndex(int nIndex);
        int PreCheckPotion(struct CPlayer* pUsePlayer, struct CCharacter** pTargetCharacter, struct _PotionItem_fld* pfB, unsigned int nCurTime, struct _skill_fld* pFld, bool bCheckDist);
        void PushRenamePotionDBLog(char* pInfo);
        int RemovePotionContEffect(struct CPlayer* pApplyPlayer, struct _ContPotionData* ContPotionData);
        int SelectDeleteBuf(struct CPlayer* pOne, bool bUse, bool bRemove);
        bool SetPotionDataName();
        void UpdatePotionContEffect(struct CPlayer* pPlayer);
        int UsePotion(struct CPlayer* pUsePlayer, struct CCharacter* pTargetCharacter, struct _PotionItem_fld* pfB, unsigned int nCurTime);
        ~CPotionMgr();
        void dtor_CPotionMgr();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CPotionMgr, 392>(), "CPotionMgr");
END_ATF_NAMESPACE

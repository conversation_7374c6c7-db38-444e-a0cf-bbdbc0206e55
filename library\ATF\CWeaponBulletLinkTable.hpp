// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRecordData.hpp>
#include <CWeaponBulletLinkTableVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CWeaponBulletLinkTable
    {
        CWeaponBulletLinkTableVtbl *vfptr;
        CRecordData m_tblBullet;
        int **m_ppnWeaponIndex;
        int m_nBulletNum;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

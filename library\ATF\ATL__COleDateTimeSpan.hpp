// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__COleDateTime.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        #pragma pack(push, 8)
        struct COleDateTimeSpan
        {
            typedef COleDateTime::DateTimeStatus DateTimeSpanStatus;
            long double m_span;
            DateTimeSpanStatus m_status;
        };
        #pragma pack(pop)
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleState.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattleStatector_CGuildBattleState2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*);
            using GUILD_BATTLE__CGuildBattleStatector_CGuildBattleState2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, GUILD_BATTLE__CGuildBattleStatector_CGuildBattleState2_ptr);
            using GUILD_BATTLE__CGuildBattleStateEnter4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, struct GUILD_BATTLE::C<PERSON><PERSON>Battle*);
            using GUILD_BATTLE__CGuildBattleStateEnter4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CGuildBattleStateEnter4_ptr);
            using GUILD_BATTLE__CGuildBattleStateFin6_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CGuildBattleStateFin6_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CGuildBattleStateFin6_ptr);
            using GUILD_BATTLE__CGuildBattleStateGetTerm8_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CGuildBattleStateGetTerm8_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, struct ATL::CTimeSpan*, GUILD_BATTLE__CGuildBattleStateGetTerm8_ptr);
            using GUILD_BATTLE__CGuildBattleStateLog10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, char*);
            using GUILD_BATTLE__CGuildBattleStateLog10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, char*, GUILD_BATTLE__CGuildBattleStateLog10_ptr);
            using GUILD_BATTLE__CGuildBattleStateLoop12_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CGuildBattleStateLoop12_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CGuildBattleStateLoop12_ptr);
            
            using GUILD_BATTLE__CGuildBattleStatedtor_CGuildBattleState14_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*);
            using GUILD_BATTLE__CGuildBattleStatedtor_CGuildBattleState14_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleState*, GUILD_BATTLE__CGuildBattleStatedtor_CGuildBattleState14_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleStateVtbl
        {
            int (WINAPIV *Enter)(struct CGuildBattleState *_this, struct CGuildBattle *);
            int (WINAPIV *Loop)(struct CGuildBattleState *_this, struct CGuildBattle *);
            int (WINAPIV *Fin)(struct CGuildBattleState *_this, struct CGuildBattle *);
            struct CTimeSpan *(WINAPIV *GetTerm)(struct CGuildBattleState *_this, struct CTimeSpan *result);
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

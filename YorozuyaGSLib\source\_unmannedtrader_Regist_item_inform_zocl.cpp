#include <_unmannedtrader_Regist_item_inform_zocl.hpp>


START_ATF_NAMESPACE
    _unmannedtrader_Regist_item_inform_zocl::_unmannedtrader_Regist_item_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _unmannedtrader_Regist_item_inform_zocl*);
        (org_ptr(0x140360050L))(this);
    };
    void _unmannedtrader_Regist_item_inform_zocl::ctor__unmannedtrader_Regist_item_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _unmannedtrader_Regist_item_inform_zocl*);
        (org_ptr(0x140360050L))(this);
    };
    int _unmannedtrader_Regist_item_inform_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _unmannedtrader_Regist_item_inform_zocl*);
        return (org_ptr(0x140360070L))(this);
    };
    
END_ATF_NAMESPACE

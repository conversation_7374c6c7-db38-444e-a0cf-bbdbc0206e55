// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRecordData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRecordDatactor_CRecordData2_ptr = void (WINAPIV*)(struct CRecordData*);
        using CRecordDatactor_CRecordData2_clbk = void (WINAPIV*)(struct CRecordData*, CRecordDatactor_CRecordData2_ptr);
        using CRecordDataFileSize4_ptr = unsigned int (WINAPIV*)(struct CRecordData*, char*);
        using CRecordDataFileSize4_clbk = unsigned int (WINAPIV*)(struct CRecordData*, char*, CRecordDataFileSize4_ptr);
        using CRecordDataGetRecord6_ptr = struct _base_fld* (WINAPIV*)(struct CRecordData*, char*);
        using CRecordDataGetRecord6_clbk = struct _base_fld* (WINAPIV*)(struct CRecordData*, char*, CRecordDataGetRecord6_ptr);
        using CRecordDataGetRecord8_ptr = struct _base_fld* (WINAPIV*)(struct CRecordData*, char*, int);
        using CRecordDataGetRecord8_clbk = struct _base_fld* (WINAPIV*)(struct CRecordData*, char*, int, CRecordDataGetRecord8_ptr);
        using CRecordDataGetRecord10_ptr = struct _base_fld* (WINAPIV*)(struct CRecordData*, int);
        using CRecordDataGetRecord10_clbk = struct _base_fld* (WINAPIV*)(struct CRecordData*, int, CRecordDataGetRecord10_ptr);
        using CRecordDataGetRecordByHash12_ptr = struct _base_fld* (WINAPIV*)(struct CRecordData*, char*, int, int);
        using CRecordDataGetRecordByHash12_clbk = struct _base_fld* (WINAPIV*)(struct CRecordData*, char*, int, int, CRecordDataGetRecordByHash12_ptr);
        using CRecordDataGetRecordNum14_ptr = int (WINAPIV*)(struct CRecordData*);
        using CRecordDataGetRecordNum14_clbk = int (WINAPIV*)(struct CRecordData*, CRecordDataGetRecordNum14_ptr);
        using CRecordDataIsTableOpen16_ptr = bool (WINAPIV*)(struct CRecordData*);
        using CRecordDataIsTableOpen16_clbk = bool (WINAPIV*)(struct CRecordData*, CRecordDataIsTableOpen16_ptr);
        using CRecordDataLoadRecordData18_ptr = bool (WINAPIV*)(struct CRecordData*, void*, char*);
        using CRecordDataLoadRecordData18_clbk = bool (WINAPIV*)(struct CRecordData*, void*, char*, CRecordDataLoadRecordData18_ptr);
        using CRecordDataLoadRecordHeader20_ptr = bool (WINAPIV*)(struct CRecordData*, void*, char*);
        using CRecordDataLoadRecordHeader20_clbk = bool (WINAPIV*)(struct CRecordData*, void*, char*, CRecordDataLoadRecordHeader20_ptr);
        using CRecordDataMakeHash22_ptr = unsigned int (WINAPIV*)(char*, int);
        using CRecordDataMakeHash22_clbk = unsigned int (WINAPIV*)(char*, int, CRecordDataMakeHash22_ptr);
        using CRecordDataMakeHashTable24_ptr = bool (WINAPIV*)(struct CRecordData*, int, int, char*);
        using CRecordDataMakeHashTable24_clbk = bool (WINAPIV*)(struct CRecordData*, int, int, char*, CRecordDataMakeHashTable24_ptr);
        using CRecordDataReadRecord26_ptr = bool (WINAPIV*)(struct CRecordData*, char*, unsigned int, char*);
        using CRecordDataReadRecord26_clbk = bool (WINAPIV*)(struct CRecordData*, char*, unsigned int, char*, CRecordDataReadRecord26_ptr);
        using CRecordDataReadRecord_Ex28_ptr = bool (WINAPIV*)(struct CRecordData*, char*, char*, unsigned int, char*);
        using CRecordDataReadRecord_Ex28_clbk = bool (WINAPIV*)(struct CRecordData*, char*, char*, unsigned int, char*, CRecordDataReadRecord_Ex28_ptr);
        
        using CRecordDatadtor_CRecordData34_ptr = void (WINAPIV*)(struct CRecordData*);
        using CRecordDatadtor_CRecordData34_clbk = void (WINAPIV*)(struct CRecordData*, CRecordDatadtor_CRecordData34_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

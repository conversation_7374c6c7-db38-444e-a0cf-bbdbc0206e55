// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <FinalDecisionApplyer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using FinalDecisionApplyerDoit2_ptr = int (WINAPIV*)(struct FinalDecisionApplyer*, Cmd, struct CPlayer*, char*);
        using FinalDecisionApplyerDoit2_clbk = int (WINAPIV*)(struct FinalDecisionApplyer*, Cmd, struct CPlayer*, char*, FinalDecisionApplyerDoit2_ptr);
        
        using FinalDecisionApplyerctor_FinalDecisionApplyer4_ptr = void (WINAPIV*)(struct FinalDecisionApplyer*);
        using FinalDecisionApplyerctor_FinalDecisionApplyer4_clbk = void (WINAPIV*)(struct FinalDecisionApplyer*, FinalDecisionApplyerctor_FinalDecisionApplyer4_ptr);
        using FinalDecisionApplyerInitialize6_ptr = bool (WINAPIV*)(struct FinalDecisionApplyer*);
        using FinalDecisionApplyerInitialize6_clbk = bool (WINAPIV*)(struct FinalDecisionApplyer*, FinalDecisionApplyerInitialize6_ptr);
        using FinalDecisionApplyer_FinalDecisionApply8_ptr = void (WINAPIV*)(struct FinalDecisionApplyer*);
        using FinalDecisionApplyer_FinalDecisionApply8_clbk = void (WINAPIV*)(struct FinalDecisionApplyer*, FinalDecisionApplyer_FinalDecisionApply8_ptr);
        
        using FinalDecisionApplyerdtor_FinalDecisionApplyer13_ptr = void (WINAPIV*)(struct FinalDecisionApplyer*);
        using FinalDecisionApplyerdtor_FinalDecisionApplyer13_clbk = void (WINAPIV*)(struct FinalDecisionApplyer*, FinalDecisionApplyerdtor_FinalDecisionApplyer13_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

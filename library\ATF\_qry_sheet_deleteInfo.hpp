// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_sheet_delete.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _qry_sheet_deletesize2_ptr = int (WINAPIV*)(struct _qry_sheet_delete*);
        using _qry_sheet_deletesize2_clbk = int (WINAPIV*)(struct _qry_sheet_delete*, _qry_sheet_deletesize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

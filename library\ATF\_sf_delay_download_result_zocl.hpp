// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _sf_delay_download_result_zocl
    {
        struct  _eff_list
        {
            char byEffectCode;
            unsigned __int16 wEffectIndex;
            unsigned int dwRemainTime;
        public:
            _eff_list();
            void ctor__eff_list();
        };
        struct  _mas_list
        {
            char byEffectCode;
            char byMastery;
            unsigned int dwRemainTime;
        public:
            _mas_list();
            void ctor__mas_list();
        };
        _eff_list EFF[10];
        _mas_list MAS[10];
    public:
        _sf_delay_download_result_zocl();
        void ctor__sf_delay_download_result_zocl();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_sf_delay_download_result_zocl, 130>(), "_sf_delay_download_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMsgDataVtbl.hpp>
#include <CMyCriticalSection.hpp>
#include <_message.hpp>


START_ATF_NAMESPACE
    struct CMsgData
    {
        CMsgDataVtbl *vfptr;
        int m_nObjNum;
        int m_nMaxBufNum;
        _message m_gmListHead;
        _message m_gmListTail;
        _message *m_gmBuf;
        _message m_gmListEmptyHead;
        _message m_gmListEmptyTail;
        CMyCriticalSection m_csList;
        CMyCriticalSection m_csEmpty;
    public:
        CMsgData(int nObjNum);
        void ctor_CMsgData(int nObjNum);
        CMsgData();
        void ctor_CMsgData();
        void Init(int nObjNum);
        bool PackingMsg(unsigned int dwMessage, unsigned int dwKey1, unsigned int dwKey2, unsigned int dwKey3);
        struct _message* PopEmptyBuf();
        struct _message* PopMsg();
        void ProcessMessage(struct _message* pMsg);
        void PumpMsgList();
        void PushEmptyBuf(struct _message* pMsg);
        void PushMsg(struct _message* pMsg);
        ~CMsgData();
        void dtor_CMsgData();
    };    
    static_assert(ATF::checkSize<CMsgData, 248>(), "CMsgData");
END_ATF_NAMESPACE

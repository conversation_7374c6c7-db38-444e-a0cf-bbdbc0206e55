// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CObjectListVtbl.hpp>
#include <_object_list_point.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CObjectList
    {
        CObjectListVtbl *vfptr;
        _object_list_point m_Head;
        _object_list_point m_Tail;
        int m_nSize;
    public:
        CObjectList();
        void ctor_CObjectList();
        struct CGameObject* CopyItem(unsigned int dwIndex);
        bool DeleteItem(struct _object_list_point* pItem);
        int GetSize();
        void InitList();
        bool PushItem(struct _object_list_point* pItem);
        ~CObjectList();
        void dtor_CObjectList();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

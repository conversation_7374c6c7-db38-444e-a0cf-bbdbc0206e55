// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _candidate_info
    {
        enum ClassType
        {
            patriarch = 0x0,
            sub_patriarch_a = 0x1,
            attack_leader_a = 0x2,
            defence_leader_a = 0x3,
            support_leader_a = 0x4,
            sub_patriarch_b = 0x5,
            attack_leader_b = 0x6,
            defence_leader_b = 0x7,
            support_leader_b = 0x8,
            patriarch_group_num = 0x9,
            normal_user = 0xFF,
        };
        enum Status
        {
            candidate_normal = 0x0,
            candidate_1st = 0x1,
            candidate_2st = 0x2,
            candidate_appoint = 0x3,
            candidate_discharge = 0x4,
            candidate_delete = 0x5,
            candidate_type_num = 0x6,
        };
        bool bLoad;
        bool bUpdateClassType;
        bool bRefund;
        Status eStatus;
        ClassType eClassType;
        char byRace;
        char byLevel;
        unsigned int dwRank;
        unsigned int dwAvatorSerial;
        unsigned int dwGuildSerial;
        char wszName[17];
        char wszGuildName[17];
        long double dPvpPoint;
        unsigned int dwWinCnt;
        unsigned int dwScore;
        bool bValidChar;
        char byGrade;
    public:
        void _Init();
        _candidate_info();
        void ctor__candidate_info();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

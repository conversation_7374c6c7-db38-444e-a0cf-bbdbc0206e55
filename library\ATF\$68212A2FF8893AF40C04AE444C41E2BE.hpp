// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $68212A2FF8893AF40C04AE444C41E2BE
    {
      SK_Att_Lck = 0x0,
      FC_Att_Lck = 0x1,
      SK_CtHp_Lck = 0x2,
      FC_CtHp_Lck = 0x3,
      FC_CtDm_Lck = 0x4,
      Stealth = 0x5,
      Move_Lck = 0x6,
      Run_Lck = 0x7,
      Abs_Avd = 0x8,
      Abs_Crt = 0x9,
      Rev_Lck = 0xA,
      Dst_No_Shd = 0xB,
      Dst_No_Def = 0xC,
      Dst_Make_Stun = 0xD,
      Res_Att = 0xE,
      HP_Rcv_Lck = 0xF,
      FP_Rcv_Lck = 0x10,
      SP_Rcv_Lck = 0x11,
      View_Lck = 0x12,
      Tol_Minus = 0x13,
      Stone_Lck = 0x14,
      Suspend_Lck = 0x15,
      Force_Shield = 0x16,
      Find_Trap = 0x17,
      Solitude = 0x18,
      Remove_CtDm_Lck = 0x19,
      Invisible = 0x1A,
      Invincible = 0x1C,
      __END = 0x1D,
    };
END_ATF_NAMESPACE

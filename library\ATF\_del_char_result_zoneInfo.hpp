// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_del_char_result_zone.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _del_char_result_zonesize2_ptr = int (WINAPIV*)(struct _del_char_result_zone*);
        using _del_char_result_zonesize2_clbk = int (WINAPIV*)(struct _del_char_result_zone*, _del_char_result_zonesize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct   _embellish_db_load : _STORAGE_LIST
    {
        _STORAGE_LIST::_db_con m_List[7];
    public:
        _embellish_db_load();
        void ctor__embellish_db_load();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_embellish_db_load, 370>(), "_embellish_db_load");
END_ATF_NAMESPACE

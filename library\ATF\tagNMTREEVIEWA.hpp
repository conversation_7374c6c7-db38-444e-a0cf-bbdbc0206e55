// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMHDR.hpp>
#include <tagPOINT.hpp>
#include <tagTVITEMA.hpp>


START_ATF_NAMESPACE
    struct tagNMTREEVIEWA
    {
        tagNMHDR hdr;
        unsigned int action;
        tagTVITEMA itemOld;
        tagTVITEMA itemNew;
        tagPOINT ptDrag;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataCN.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataCNctor_CNationSettingDataCN2_ptr = void (WINAPIV*)(struct CNationSettingDataCN*);
        using CNationSettingDataCNctor_CNationSettingDataCN2_clbk = void (WINAPIV*)(struct CNationSettingDataCN*, CNationSettingDataCNctor_CNationSettingDataCN2_ptr);
        using CNationSettingDataCNCreateBilling4_ptr = struct CBilling* (WINAPIV*)(struct CNationSettingDataCN*);
        using CNationSettingDataCNCreateBilling4_clbk = struct CBilling* (WINAPIV*)(struct CNationSettingDataCN*, CNationSettingDataCNCreateBilling4_ptr);
        using CNationSettingDataCNCreateComplete6_ptr = void (WINAPIV*)(struct CNationSettingDataCN*, struct CPlayer*);
        using CNationSettingDataCNCreateComplete6_clbk = void (WINAPIV*)(struct CNationSettingDataCN*, struct CPlayer*, CNationSettingDataCNCreateComplete6_ptr);
        using CNationSettingDataCNCreateWorker8_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataCN*);
        using CNationSettingDataCNCreateWorker8_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataCN*, CNationSettingDataCNCreateWorker8_ptr);
        using CNationSettingDataCNGetCashItemPrice10_ptr = int (WINAPIV*)(struct CNationSettingDataCN*, struct _CashShop_str_fld*);
        using CNationSettingDataCNGetCashItemPrice10_clbk = int (WINAPIV*)(struct CNationSettingDataCN*, struct _CashShop_str_fld*, CNationSettingDataCNGetCashItemPrice10_ptr);
        using CNationSettingDataCNGetItemName12_ptr = char* (WINAPIV*)(struct CNationSettingDataCN*, struct _NameTxt_fld*);
        using CNationSettingDataCNGetItemName12_clbk = char* (WINAPIV*)(struct CNationSettingDataCN*, struct _NameTxt_fld*, CNationSettingDataCNGetItemName12_ptr);
        using CNationSettingDataCNInit14_ptr = int (WINAPIV*)(struct CNationSettingDataCN*);
        using CNationSettingDataCNInit14_clbk = int (WINAPIV*)(struct CNationSettingDataCN*, CNationSettingDataCNInit14_ptr);
        using CNationSettingDataCNLoop16_ptr = void (WINAPIV*)(struct CNationSettingDataCN*);
        using CNationSettingDataCNLoop16_clbk = void (WINAPIV*)(struct CNationSettingDataCN*, CNationSettingDataCNLoop16_ptr);
        using CNationSettingDataCNNetClose18_ptr = void (WINAPIV*)(struct CNationSettingDataCN*, struct CPlayer*);
        using CNationSettingDataCNNetClose18_clbk = void (WINAPIV*)(struct CNationSettingDataCN*, struct CPlayer*, CNationSettingDataCNNetClose18_ptr);
        using CNationSettingDataCNReadSystemPass20_ptr = bool (WINAPIV*)(struct CNationSettingDataCN*);
        using CNationSettingDataCNReadSystemPass20_clbk = bool (WINAPIV*)(struct CNationSettingDataCN*, CNationSettingDataCNReadSystemPass20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

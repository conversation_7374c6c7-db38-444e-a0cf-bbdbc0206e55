// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_update_mineore
    {
        struct __changed
        {
            char bySlot;
            unsigned int dwK;
            char byNum;
        };
        char bySubQry;
        unsigned int dwMachineIndex;
        unsigned int dwAvatorSerial;
        char byChangedNum;
        __changed change[40];
    public:
        _qry_case_update_mineore();
        void ctor__qry_case_update_mineore();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_update_mineore, 0x1f0>(), "_qry_case_update_mineore");
END_ATF_NAMESPACE

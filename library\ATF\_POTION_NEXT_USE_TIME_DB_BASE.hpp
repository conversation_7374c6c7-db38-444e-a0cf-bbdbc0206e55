// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _POTION_NEXT_USE_TIME_DB_BASE
    {
        unsigned int dwPotionNextUseTime[38];
    public:
        void Init();
        _POTION_NEXT_USE_TIME_DB_BASE();
        void ctor__POTION_NEXT_USE_TIME_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_POTION_NEXT_USE_TIME_DB_BASE, 152>(), "_POTION_NEXT_USE_TIME_DB_BASE");
END_ATF_NAMESPACE

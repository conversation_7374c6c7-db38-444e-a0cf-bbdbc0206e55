// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagSAFEARRAY.hpp>


START_ATF_NAMESPACE
    struct $6810C0431CE7B8C44277107BC1E4A02A
    {
        BYTE gap0[8];
        tagSAFEARRAY *parray;
    };    
    static_assert(ATF::checkSize<$6810C0431CE7B8C44277107BC1E4A02A, 16>(), "$6810C0431CE7B8C44277107BC1E4A02A");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct Request_Buy_Item
    {
        unsigned __int16 wRet;
        unsigned int dwDataLength;
        unsigned int dwSeq;
        int nBuyID;
        int nRemainCash;
    public:
        Request_Buy_Item();
        void ctor_Request_Buy_Item();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHonorGuildVtbl.hpp>
#include <_guild_honor_list_result_zocl.hpp>
#include <_guild_honor_set_request_clzo.hpp>


START_ATF_NAMESPACE
    struct CHonorGuild
    {
        CHonorGuildVtbl *vfptr;
        bool m_bNext[3];
        bool m_bSendInform[3];
        _guild_honor_list_result_zocl *m_pCurrHonorGuild[3];
        _guild_honor_list_result_zocl *m_pNextHonorGuild[3];
        bool m_bChageInform[3];
        unsigned int m_uiProccessIndex[3];
    public:
        CHonorGuild();
        void ctor_CHonorGuild();
        void ChangeHonorGuild(char byRace);
        bool CheckHonorGuild(char byRace, unsigned int dwSerial);
        void DQSCompleteInAtradTaxMoney(char* pdata);
        void Destroy();
        char FindHonorGuildRank(char byRace, unsigned int dwGuildSerial);
        bool Init();
        static struct CHonorGuild* Instance();
        bool LoadDB();
        void Loop();
        void LoopSubProcSendInform(char byRace);
        void SendCurrHonorGuildList(uint16_t wIndex, char byRace, char byUI);
        void SendInformChange(char byRace, uint16_t wIndex);
        void SendNextHonorGuildList(uint16_t wIndex, char byRace);
        void SetGuildMaintainMoney(char byRace, unsigned int dwTax, unsigned int dwSeller);
        char SetNextHonorGuild(char byRace, struct _guild_honor_set_request_clzo* pRecv);
        char UpdateChangeHonorGuild(char byRace);
        void UpdateHonorGuildMark(struct _guild_honor_list_result_zocl* pList, int bSet);
        char UpdateNextHonorGuild(char byRace);
        ~CHonorGuild();
        void dtor_CHonorGuild();
    };
END_ATF_NAMESPACE

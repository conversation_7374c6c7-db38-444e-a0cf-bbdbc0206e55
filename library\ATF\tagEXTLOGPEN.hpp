// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagEXTLOGPEN
    {
        unsigned int elpPenStyle;
        unsigned int elpWidth;
        unsigned int elpBrushStyle;
        unsigned int elpColor;
        unsigned __int64 elpHatch;
        unsigned int elpNumEntries;
        unsigned int elpStyleEntry[1];
    };
END_ATF_NAMESPACE

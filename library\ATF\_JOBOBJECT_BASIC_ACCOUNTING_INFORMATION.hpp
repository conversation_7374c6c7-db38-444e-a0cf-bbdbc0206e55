// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    struct _JOBOBJECT_BASIC_ACCOUNTING_INFORMATION
    {
        _LARGE_INTEGER TotalUserTime;
        _LARGE_INTEGER TotalKernelTime;
        _LARGE_INTEGER ThisPeriodTotalUserTime;
        _LARGE_INTEGER ThisPeriodTotalKernelTime;
        unsigned int TotalPageFaultCount;
        unsigned int TotalProcesses;
        unsigned int ActiveProcesses;
        unsigned int TotalTerminatedProcesses;
    };
END_ATF_NAMESPACE

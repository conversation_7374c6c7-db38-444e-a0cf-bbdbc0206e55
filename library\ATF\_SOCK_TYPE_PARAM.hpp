// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _SOCK_TYPE_PARAM
    {
        char m_byProtocolID;
        int m_bServer;
        unsigned __int16 m_wPort;
        unsigned __int16 m_wSocketMaxNum;
        int m_bAcceptIPCheck;
        unsigned int m_dwIPCheckTerm;
        unsigned int m_dwSocketRecycleTerm;
    public:
        _SOCK_TYPE_PARAM();
        void ctor__SOCK_TYPE_PARAM();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_SOCK_TYPE_PARAM, 24>(), "_SOCK_TYPE_PARAM");
END_ATF_NAMESPACE

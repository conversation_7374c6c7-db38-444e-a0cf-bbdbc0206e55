// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDummyPosTable.hpp>
#include <CExtDummy.hpp>
#include <CGameObject.hpp>
#include <CLevel.hpp>
#include <CMapDataVtbl.hpp>
#include <CMyTimer.hpp>
#include <CObjectList.hpp>
#include <CRecordData.hpp>
#include <_LAYER_SET.hpp>
#include <_MULTI_BLOCK.hpp>
#include <_bind_dummy.hpp>
#include <_bsp_info.hpp>
#include <_dummy_position.hpp>
#include <_map_fld.hpp>
#include <_mon_block.hpp>
#include <_pnt_rect.hpp>
#include <_portal_dummy.hpp>
#include <_quest_dummy.hpp>
#include <_res_dummy.hpp>
#include <_safe_dummy.hpp>
#include <_sec_info.hpp>
#include <_start_dummy.hpp>
#include <_store_dummy.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CMapData
    {
        CMapDataVtbl *vfptr;
        bool m_bUse;
        bool m_bLoad;
        int m_nMapIndex;
        CLevel m_Level;
        int m_nMapCode;
        _LAYER_SET *m_ls;
        _MULTI_BLOCK *m_mb;
        CExtDummy m_Dummy;
        int m_nMapInPlayerNum;
        int m_nMapInMonsterNum;
        int m_nMonBlockNum;
        _mon_block *m_pMonBlock;
        int m_nMonDumNum;
        int m_nPortalNum;
        _portal_dummy *m_pPortal;
        int m_nItemStoreDumNum;
        _store_dummy *m_pItemStoreDummy;
        int m_nStartDumNum;
        _start_dummy *m_pStartDummy;
        int m_nBindDumNum;
        _bind_dummy *m_pBindDummy;
        int m_nResDumNum;
        _res_dummy *m_pResDummy;
        int m_nQuestDumNum;
        _quest_dummy *m_pQuestDummy;
        _map_fld *m_pMapSet;
        CExtDummy *m_pExtDummy_Town;
        int m_nSafeDumNum;
        _safe_dummy *m_pSafeDummy;
        CDummyPosTable m_tbSafeDumPos;
        CRecordData m_tbMonBlk;
        CRecordData m_tbPortal;
        CDummyPosTable m_tbMonDumPos;
        CDummyPosTable m_tbPortalDumPos;
        CDummyPosTable m_tbStoreDumPos;
        CDummyPosTable m_tbStartDumPos;
        CDummyPosTable m_tbBindDumPos;
        CDummyPosTable m_tbResDumPosHigh;
        CDummyPosTable m_tbResDumPosMiddle;
        CDummyPosTable m_tbResDumPosLow;
        CDummyPosTable m_tbQuestDumPos;
        _bsp_info m_BspInfo;
        _sec_info m_SecInfo;
        CMyTimer m_tmrMineGradeReSet;
        int m_nMonTotalCount;
    public:
        CMapData();
        void ctor_CMapData();
        bool CheckCenterPosDummy(struct _dummy_position* pPos);
        bool ConvertLocal(struct _dummy_position* pPos);
        bool ConvertLocalToWorldDummy(struct CDummyPosTable* pPosTable, bool bCheckCenter);
        void EnterMap(struct CGameObject* pObj, unsigned int dwSecIndex);
        void ExitMap(struct CGameObject* pObj, unsigned int dwSecIndex);
        struct _bsp_info* GetBspInfo();
        struct _dummy_position* GetDummyPostion(char* pszDummyCode);
        int GetLevelLimit();
        struct _portal_dummy* GetLinkPortal(char* pPortalCode);
        char GetMapCode();
        struct _portal_dummy* GetPortal(char* pPortalCode);
        struct _portal_dummy* GetPortal(int nPortalIndex);
        int GetPortalInx(char* pPortalCode);
        char GetRaceTown(float* fPos, char byRaceCode);
        bool GetRandPosInDummy(struct _dummy_position* pPos, float* pNewPos, bool bRePos);
        bool GetRandPosInRange(float* pStdPos, int nRange, float* pNewPos);
        bool GetRandPosVirtualDum(float* pStdPos, int nRange, float* pNewPos);
        bool GetRandPosVirtualDumExcludeStdRange(float* pStdPos, int nRange, int iExcludeRange, float* pNewPos);
        void GetRectInRadius(struct _pnt_rect* pRect, int nRadius, int nSecNum);
        int GetResDummySector(int nDummyIndex, float* pCurPos);
        struct _sec_info* GetSecInfo();
        int GetSectorIndex(float* pPos);
        struct CObjectList* GetSectorListObj(uint16_t wLayerIndex, unsigned int dwSecIndex);
        struct CObjectList* GetSectorListPlayer(uint16_t wLayerIndex, unsigned int dwSecIndex);
        struct CObjectList* GetSectorListTower(uint16_t wLayerIndex, unsigned int dwSecIndex);
        int GetSectorNumByLayerIndex(uint16_t wLayerIndex);
        void Init(struct _map_fld* pMapSet);
        bool IsMapIn(float* fPos);
        bool LoadDummy(char* pszDummyCode, struct _dummy_position* pPos);
        bool LoadHolySystemDummy(char* pszDummyCode, struct _dummy_position* pPos);
        void OnLoop();
        bool OpenMap(char* pszMapCode, struct _map_fld* pMapSet, bool bUse);
        bool UpdateSecterList(struct CGameObject* pObj, unsigned int dwOldSec, unsigned int dwNewSec);
        bool _LoadBind(char* pszMapCode);
        bool _LoadBspSec(char* pszMapCode);
        bool _LoadMonBlk(char* pszMapCode, struct _map_fld* pMapFld);
        bool _LoadPortal(char* pszMapCode);
        bool _LoadQuest(char* pszMapCode);
        bool _LoadResource(char* pszMapCode);
        bool _LoadSafe(char* pszMapCode);
        bool _LoadStart(char* pszMapCode);
        bool _LoadStoreDummy(char* pszMapCode);
        ~CMapData();
        void dtor_CMapData();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

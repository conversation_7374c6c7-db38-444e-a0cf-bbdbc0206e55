// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagCY.hpp>


START_ATF_NAMESPACE
    struct $A24CCB9C650A9CC3453F37B8C46067B8
    {
        BYTE gap0[8];
        tagCY cyVal;
    };    
    static_assert(ATF::checkSize<$A24CCB9C650A9CC3453F37B8C46067B8, 16>(), "$A24CCB9C650A9CC3453F37B8C46067B8");
END_ATF_NAMESPACE

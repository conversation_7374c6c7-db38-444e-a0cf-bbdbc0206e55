// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattle.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattlector_CGuildBattle2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CGuildBattlector_CGuildBattle2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CGuildBattlector_CGuildBattle2_ptr);
            using GUILD_BATTLE__CGuildBattleGetObjType4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CGuildBattleGetObjType4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CGuildBattleGetObjType4_ptr);
            
            using GUILD_BATTLE__CGuildBattledtor_CGuildBattle6_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattle*);
            using GUILD_BATTLE__CGuildBattledtor_CGuildBattle6_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattle*, GUILD_BATTLE__CGuildBattledtor_CGuildBattle6_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

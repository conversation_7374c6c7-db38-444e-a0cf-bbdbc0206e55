// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataRU.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataRUctor_CNationSettingDataRU2_ptr = void (WINAPIV*)(struct CNationSettingDataRU*);
        using CNationSettingDataRUctor_CNationSettingDataRU2_clbk = void (WINAPIV*)(struct CNationSettingDataRU*, CNationSettingDataRUctor_CNationSettingDataRU2_ptr);
        using CNationSettingDataRUCreateBilling4_ptr = struct CBilling* (WINAPIV*)(struct CNationSettingDataRU*);
        using CNationSettingDataRUCreateBilling4_clbk = struct CBilling* (WINAPIV*)(struct CNationSettingDataRU*, CNationSettingDataRUCreateBilling4_ptr);
        using CNationSettingDataRUCreateWorker6_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataRU*);
        using CNationSettingDataRUCreateWorker6_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataRU*, CNationSettingDataRUCreateWorker6_ptr);
        using CNationSettingDataRUGetCashItemPrice8_ptr = int (WINAPIV*)(struct CNationSettingDataRU*, struct _CashShop_str_fld*);
        using CNationSettingDataRUGetCashItemPrice8_clbk = int (WINAPIV*)(struct CNationSettingDataRU*, struct _CashShop_str_fld*, CNationSettingDataRUGetCashItemPrice8_ptr);
        using CNationSettingDataRUGetItemName10_ptr = char* (WINAPIV*)(struct CNationSettingDataRU*, struct _NameTxt_fld*);
        using CNationSettingDataRUGetItemName10_clbk = char* (WINAPIV*)(struct CNationSettingDataRU*, struct _NameTxt_fld*, CNationSettingDataRUGetItemName10_ptr);
        using CNationSettingDataRUInit12_ptr = int (WINAPIV*)(struct CNationSettingDataRU*);
        using CNationSettingDataRUInit12_clbk = int (WINAPIV*)(struct CNationSettingDataRU*, CNationSettingDataRUInit12_ptr);
        using CNationSettingDataRUReadSystemPass14_ptr = bool (WINAPIV*)(struct CNationSettingDataRU*);
        using CNationSettingDataRUReadSystemPass14_clbk = bool (WINAPIV*)(struct CNationSettingDataRU*, CNationSettingDataRUReadSystemPass14_ptr);
        using CNationSettingDataRUSendCashDBDSNRequest16_ptr = void (WINAPIV*)(struct CNationSettingDataRU*);
        using CNationSettingDataRUSendCashDBDSNRequest16_clbk = void (WINAPIV*)(struct CNationSettingDataRU*, CNationSettingDataRUSendCashDBDSNRequest16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

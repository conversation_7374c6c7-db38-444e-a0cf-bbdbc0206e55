// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_unmandtrader_registsingleitem
    {
        unsigned __int16 wInx;
        unsigned __int16 wItemSerialOld;
        unsigned int dwLeftDalant;
        unsigned __int16 wSepaSerial;
        char bySepaAmount;
        unsigned int dwListIndex;
        char byOldStorageIndex;
        char byStorageIndex;
        unsigned int dwRegedSerial;
        bool bInserted;
        char byType;
        char bySellTurm;
        char byRace;
        unsigned int dwOwnerSerial;
        unsigned int dwPrice;
        char byInveninx;
        unsigned int dwK;
        unsigned __int64 dwD;
        unsigned int dwU;
        char byLv;
        char byGrade;
        char byClass1;
        char byClass2;
        char byClass3;
        unsigned int dwT;
        unsigned __int64 lnUID;
        unsigned int dwTax;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct si_effect
    {
        char byTargetItemCount;
        char byTargetEffectCount;
    public:
        char GetCountOfEffect();
        char GetCountOfItem();
        void init();
        void set_effect_count_info(char byCountOfItem, char byCountOfEffect);
        si_effect();
        void ctor_si_effect();
    };
END_ATF_NAMESPACE

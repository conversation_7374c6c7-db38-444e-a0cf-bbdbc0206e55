// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>


START_ATF_NAMESPACE
    struct tagACTCTXW
    {
        unsigned int cbSize;
        unsigned int dwFlags;
        const wchar_t *lpSource;
        unsigned __int16 wProcessorArchitecture;
        unsigned __int16 wLangId;
        const wchar_t *lpAssemblyDirectory;
        const wchar_t *lpResourceName;
        const wchar_t *lpApplicationName;
        HINSTANCE__ *hModule;
    };
END_ATF_NAMESPACE

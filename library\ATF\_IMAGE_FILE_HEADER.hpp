// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_FILE_HEADER
    {
        unsigned __int16 Machine;
        unsigned __int16 NumberOfSections;
        unsigned int TimeDateStamp;
        unsigned int PointerToSymbolTable;
        unsigned int NumberOfSymbols;
        unsigned __int16 SizeOfOptionalHeader;
        unsigned __int16 Characteristics;
    };
END_ATF_NAMESPACE

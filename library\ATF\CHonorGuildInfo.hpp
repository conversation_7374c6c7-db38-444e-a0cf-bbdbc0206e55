// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHonorGuild.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CHonorGuildctor_CHonorGuild2_ptr = void (WINAPIV*)(struct CHonorGuild*);
        using CHonorGuildctor_CHonorGuild2_clbk = void (WINAPIV*)(struct CHonorGuild*, CHonorGuildctor_CHonorGuild2_ptr);
        using CHonorGuildChangeHonorGuild4_ptr = void (WINAPIV*)(struct CHonorGuild*, char);
        using CHonorGuildChangeHonorGuild4_clbk = void (WINAPIV*)(struct CHonorGuild*, char, CHonorGuildChangeHonorGuild4_ptr);
        using CHonorGuildCheckHonorGuild6_ptr = bool (WINAPIV*)(struct CHonorGuild*, char, unsigned int);
        using CHonorGuildCheckHonorGuild6_clbk = bool (WINAPIV*)(struct CHonorGuild*, char, unsigned int, CHonorGuildCheckHonorGuild6_ptr);
        using CHonorGuildDQSCompleteInAtradTaxMoney8_ptr = void (WINAPIV*)(struct CHonorGuild*, char*);
        using CHonorGuildDQSCompleteInAtradTaxMoney8_clbk = void (WINAPIV*)(struct CHonorGuild*, char*, CHonorGuildDQSCompleteInAtradTaxMoney8_ptr);
        using CHonorGuildDestroy10_ptr = void (WINAPIV*)(struct CHonorGuild*);
        using CHonorGuildDestroy10_clbk = void (WINAPIV*)(struct CHonorGuild*, CHonorGuildDestroy10_ptr);
        using CHonorGuildFindHonorGuildRank12_ptr = char (WINAPIV*)(struct CHonorGuild*, char, unsigned int);
        using CHonorGuildFindHonorGuildRank12_clbk = char (WINAPIV*)(struct CHonorGuild*, char, unsigned int, CHonorGuildFindHonorGuildRank12_ptr);
        using CHonorGuildInit14_ptr = bool (WINAPIV*)(struct CHonorGuild*);
        using CHonorGuildInit14_clbk = bool (WINAPIV*)(struct CHonorGuild*, CHonorGuildInit14_ptr);
        using CHonorGuildInstance16_ptr = struct CHonorGuild* (WINAPIV*)();
        using CHonorGuildInstance16_clbk = struct CHonorGuild* (WINAPIV*)(CHonorGuildInstance16_ptr);
        using CHonorGuildLoadDB18_ptr = bool (WINAPIV*)(struct CHonorGuild*);
        using CHonorGuildLoadDB18_clbk = bool (WINAPIV*)(struct CHonorGuild*, CHonorGuildLoadDB18_ptr);
        using CHonorGuildLoop20_ptr = void (WINAPIV*)(struct CHonorGuild*);
        using CHonorGuildLoop20_clbk = void (WINAPIV*)(struct CHonorGuild*, CHonorGuildLoop20_ptr);
        using CHonorGuildLoopSubProcSendInform22_ptr = void (WINAPIV*)(struct CHonorGuild*, char);
        using CHonorGuildLoopSubProcSendInform22_clbk = void (WINAPIV*)(struct CHonorGuild*, char, CHonorGuildLoopSubProcSendInform22_ptr);
        using CHonorGuildSendCurrHonorGuildList24_ptr = void (WINAPIV*)(struct CHonorGuild*, uint16_t, char, char);
        using CHonorGuildSendCurrHonorGuildList24_clbk = void (WINAPIV*)(struct CHonorGuild*, uint16_t, char, char, CHonorGuildSendCurrHonorGuildList24_ptr);
        using CHonorGuildSendInformChange26_ptr = void (WINAPIV*)(struct CHonorGuild*, char, uint16_t);
        using CHonorGuildSendInformChange26_clbk = void (WINAPIV*)(struct CHonorGuild*, char, uint16_t, CHonorGuildSendInformChange26_ptr);
        using CHonorGuildSendNextHonorGuildList28_ptr = void (WINAPIV*)(struct CHonorGuild*, uint16_t, char);
        using CHonorGuildSendNextHonorGuildList28_clbk = void (WINAPIV*)(struct CHonorGuild*, uint16_t, char, CHonorGuildSendNextHonorGuildList28_ptr);
        using CHonorGuildSetGuildMaintainMoney30_ptr = void (WINAPIV*)(struct CHonorGuild*, char, unsigned int, unsigned int);
        using CHonorGuildSetGuildMaintainMoney30_clbk = void (WINAPIV*)(struct CHonorGuild*, char, unsigned int, unsigned int, CHonorGuildSetGuildMaintainMoney30_ptr);
        using CHonorGuildSetNextHonorGuild32_ptr = char (WINAPIV*)(struct CHonorGuild*, char, struct _guild_honor_set_request_clzo*);
        using CHonorGuildSetNextHonorGuild32_clbk = char (WINAPIV*)(struct CHonorGuild*, char, struct _guild_honor_set_request_clzo*, CHonorGuildSetNextHonorGuild32_ptr);
        using CHonorGuildUpdateChangeHonorGuild34_ptr = char (WINAPIV*)(struct CHonorGuild*, char);
        using CHonorGuildUpdateChangeHonorGuild34_clbk = char (WINAPIV*)(struct CHonorGuild*, char, CHonorGuildUpdateChangeHonorGuild34_ptr);
        using CHonorGuildUpdateHonorGuildMark36_ptr = void (WINAPIV*)(struct CHonorGuild*, struct _guild_honor_list_result_zocl*, int);
        using CHonorGuildUpdateHonorGuildMark36_clbk = void (WINAPIV*)(struct CHonorGuild*, struct _guild_honor_list_result_zocl*, int, CHonorGuildUpdateHonorGuildMark36_ptr);
        using CHonorGuildUpdateNextHonorGuild38_ptr = char (WINAPIV*)(struct CHonorGuild*, char);
        using CHonorGuildUpdateNextHonorGuild38_clbk = char (WINAPIV*)(struct CHonorGuild*, char, CHonorGuildUpdateNextHonorGuild38_ptr);
        
        using CHonorGuilddtor_CHonorGuild43_ptr = void (WINAPIV*)(struct CHonorGuild*);
        using CHonorGuilddtor_CHonorGuild43_clbk = void (WINAPIV*)(struct CHonorGuild*, CHonorGuilddtor_CHonorGuild43_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

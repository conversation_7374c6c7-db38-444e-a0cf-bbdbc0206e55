// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <DnBuffNode.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using DnBuffNodector_DnBuffNode2_ptr = void (WINAPIV*)(struct DnBuffNode*);
        using DnBuffNodector_DnBuffNode2_clbk = void (WINAPIV*)(struct DnBuffNode*, DnBuffNodector_DnBuffNode2_ptr);
        using DnBuffNodeDnNodeClear4_ptr = void (WINAPIV*)(struct DnBuffNode*);
        using DnBuffNodeDnNodeClear4_clbk = void (WINAPIV*)(struct DnBuffNode*, DnBuffNodeDnNodeClear4_ptr);
        using DnBuffNodeDnNodeClose6_ptr = void (WINAPIV*)(struct DnBuffNode*);
        using DnBuffNodeDnNodeClose6_clbk = void (WINAPIV*)(struct DnBuffNode*, DnBuffNodeDnNodeClose6_ptr);
        using DnBuffNodeDnNodeOpen8_ptr = void (WINAPIV*)(struct DnBuffNode*, unsigned int);
        using DnBuffNodeDnNodeOpen8_clbk = void (WINAPIV*)(struct DnBuffNode*, unsigned int, DnBuffNodeDnNodeOpen8_ptr);
        using DnBuffNodeGetBuffIndex10_ptr = unsigned int (WINAPIV*)(struct DnBuffNode*);
        using DnBuffNodeGetBuffIndex10_clbk = unsigned int (WINAPIV*)(struct DnBuffNode*, DnBuffNodeGetBuffIndex10_ptr);
        
        using DnBuffNodedtor_DnBuffNode15_ptr = void (WINAPIV*)(struct DnBuffNode*);
        using DnBuffNodedtor_DnBuffNode15_clbk = void (WINAPIV*)(struct DnBuffNode*, DnBuffNodedtor_DnBuffNode15_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

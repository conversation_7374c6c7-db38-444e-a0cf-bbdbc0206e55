// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CObject.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CObjectIsKindOf1_ptr = int64_t (WINAPIV*)(struct CObject*, struct CRuntimeClass*);
        using CObjectIsKindOf1_clbk = int64_t (WINAPIV*)(struct CObject*, struct CRuntimeClass*, CObjectIsKindOf1_ptr);
        using CObjectSerialize2_ptr = void (WINAPIV*)(struct CObject*, struct CArchive*);
        using CObjectSerialize2_clbk = void (WINAPIV*)(struct CObject*, struct CArchive*, CObjectSerialize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

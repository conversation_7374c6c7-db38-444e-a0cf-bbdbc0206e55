// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBuffManager.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRaceBuffManagerctor_CRaceBuffManager2_ptr = void (WINAPIV*)(struct CRaceBuffManager*);
        using CRaceBuffManagerctor_CRaceBuffManager2_clbk = void (WINAPIV*)(struct CRaceBuffManager*, CRaceBuffManagerctor_CRaceBuffManager2_ptr);
        using CRaceBuffManagerCancelPlayerRaceBuff4_ptr = int (WINAPIV*)(struct CRaceBuffManager*, struct CPlayer*, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE, unsigned int);
        using CRaceBuffManagerCancelPlayerRaceBuff4_clbk = int (WINAPIV*)(struct CRaceBuffManager*, struct CPlayer*, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE, unsigned int, CRaceBuffManagerCancelPlayerRaceBuff4_ptr);
        using CRaceBuffManagerCreateComplete6_ptr = bool (WINAPIV*)(struct CRaceBuffManager*, struct CPlayer*);
        using CRaceBuffManagerCreateComplete6_clbk = bool (WINAPIV*)(struct CRaceBuffManager*, struct CPlayer*, CRaceBuffManagerCreateComplete6_ptr);
        using CRaceBuffManagerDestroy8_ptr = void (WINAPIV*)();
        using CRaceBuffManagerDestroy8_clbk = void (WINAPIV*)(CRaceBuffManagerDestroy8_ptr);
        using CRaceBuffManagerGetRaceBuffLevel10_ptr = int (WINAPIV*)(struct CRaceBuffManager*, struct CPlayer*);
        using CRaceBuffManagerGetRaceBuffLevel10_clbk = int (WINAPIV*)(struct CRaceBuffManager*, struct CPlayer*, CRaceBuffManagerGetRaceBuffLevel10_ptr);
        using CRaceBuffManagerInit12_ptr = bool (WINAPIV*)(struct CRaceBuffManager*);
        using CRaceBuffManagerInit12_clbk = bool (WINAPIV*)(struct CRaceBuffManager*, CRaceBuffManagerInit12_ptr);
        using CRaceBuffManagerInstance14_ptr = struct CRaceBuffManager* (WINAPIV*)();
        using CRaceBuffManagerInstance14_clbk = struct CRaceBuffManager* (WINAPIV*)(CRaceBuffManagerInstance14_ptr);
        using CRaceBuffManagerLoop16_ptr = void (WINAPIV*)(struct CRaceBuffManager*);
        using CRaceBuffManagerLoop16_clbk = void (WINAPIV*)(struct CRaceBuffManager*, CRaceBuffManagerLoop16_ptr);
        using CRaceBuffManagerRequestHolyQuestRaceBuff18_ptr = bool (WINAPIV*)(struct CRaceBuffManager*, int);
        using CRaceBuffManagerRequestHolyQuestRaceBuff18_clbk = bool (WINAPIV*)(struct CRaceBuffManager*, int, CRaceBuffManagerRequestHolyQuestRaceBuff18_ptr);
        
        using CRaceBuffManagerdtor_CRaceBuffManager22_ptr = void (WINAPIV*)(struct CRaceBuffManager*);
        using CRaceBuffManagerdtor_CRaceBuffManager22_clbk = void (WINAPIV*)(struct CRaceBuffManager*, CRaceBuffManagerdtor_CRaceBuffManager22_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

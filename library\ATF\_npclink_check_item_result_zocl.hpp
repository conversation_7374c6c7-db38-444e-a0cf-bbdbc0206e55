// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS_INDIV.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _npclink_check_item_result_zocl
    {
        char byRet;
        _STORAGE_POS_INDIV storage;
    public:
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

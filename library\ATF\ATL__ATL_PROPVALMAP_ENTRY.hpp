// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagVARIANT.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct ATL_PROPVALMAP_ENTRY
        {
            int dispid;
            tagVARIANT val;
            const wchar_t *szDesc;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

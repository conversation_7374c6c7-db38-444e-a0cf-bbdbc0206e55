// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct   _monster_sp_group : _base_fld
    {
        union MonsterSP_Fld
        {
            struct _skill_fld *m_SkillFld;
            struct _force_fld *m_ForceFld;
            struct _skill_fld *m_ClassSkillFld;
        };
        unsigned __int16 m_wCount;
        struct _monster_sp_fld *m_pSPData[15];
        MonsterSP_Fld m_SPDataFld[15];
    public:
        _monster_sp_group();
        void ctor__monster_sp_group();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

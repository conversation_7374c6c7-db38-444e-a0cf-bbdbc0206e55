// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <ATL__ATLTRACESTATUS.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct CTrace
        {
            HINSTANCE__ *m_hInst;
            unsigned __int64 m_dwModule;
        public:
            CTrace(int (WINAPIV* pfnCrtDbgReport)(int, char*, int, char*, char*));
            void ctor_CTrace(int (WINAPIV* pfnCrtDbgReport)(int, char*, int, char*, char*));
            bool ChangeCategory(uint64_t dwCategory, unsigned int nLevel, ATLTRACESTATUS eStatus);
            uint64_t RegisterCategory(char* pszCategory);
            void TraceV(char* pszFileName, int nLine, uint64_t dwCategory, unsigned int nLevel, char* pszFmt, char* args);
            ~CTrace();
            void dtor_CTrace();
        };    
        static_assert(ATF::checkSize<ATL::CTrace, 16>(), "ATL::CTrace");
    }; // end namespace ATL
END_ATF_NAMESPACE

#include <_cash_discount_ini_.hpp>


START_ATF_NAMESPACE
    _cash_discount_ini_::_cash_discount_ini_()
    {
        using org_ptr = void (WINAPIV*)(struct _cash_discount_ini_*);
        (org_ptr(0x1403044f0L))(this);
    };
    void _cash_discount_ini_::ctor__cash_discount_ini_()
    {
        using org_ptr = void (WINAPIV*)(struct _cash_discount_ini_*);
        (org_ptr(0x1403044f0L))(this);
    };
END_ATF_NAMESPACE

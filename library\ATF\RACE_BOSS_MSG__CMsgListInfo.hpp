// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RACE_BOSS_MSG__CMsgList.hpp>


START_ATF_NAMESPACE
    namespace RACE_BOSS_MSG
    {
        namespace Info
        {
            using RACE_BOSS_MSG__CMsgListAddEmpty2_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgListAddEmpty2_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgListAddEmpty2_ptr);
            using RACE_BOSS_MSG__CMsgListAddUse4_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgListAddUse4_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgListAddUse4_ptr);
            
            using RACE_BOSS_MSG__CMsgListctor_CMsgList6_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, char, unsigned int);
            using RACE_BOSS_MSG__CMsgListctor_CMsgList6_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, char, unsigned int, RACE_BOSS_MSG__CMsgListctor_CMsgList6_ptr);
            using RACE_BOSS_MSG__CMsgListCancel8_ptr = int (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, unsigned int, struct RACE_BOSS_MSG::CMsg**);
            using RACE_BOSS_MSG__CMsgListCancel8_clbk = int (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, unsigned int, struct RACE_BOSS_MSG::CMsg**, RACE_BOSS_MSG__CMsgListCancel8_ptr);
            using RACE_BOSS_MSG__CMsgListCleanUp10_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*);
            using RACE_BOSS_MSG__CMsgListCleanUp10_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, RACE_BOSS_MSG__CMsgListCleanUp10_ptr);
            using RACE_BOSS_MSG__CMsgListGetEmpty12_ptr = struct RACE_BOSS_MSG::CMsg* (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*);
            using RACE_BOSS_MSG__CMsgListGetEmpty12_clbk = struct RACE_BOSS_MSG::CMsg* (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, RACE_BOSS_MSG__CMsgListGetEmpty12_ptr);
            using RACE_BOSS_MSG__CMsgListGetRemainCnt14_ptr = char (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*);
            using RACE_BOSS_MSG__CMsgListGetRemainCnt14_clbk = char (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, RACE_BOSS_MSG__CMsgListGetRemainCnt14_ptr);
            using RACE_BOSS_MSG__CMsgListGetSendMsg16_ptr = struct RACE_BOSS_MSG::CMsg* (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*);
            using RACE_BOSS_MSG__CMsgListGetSendMsg16_clbk = struct RACE_BOSS_MSG::CMsg* (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, RACE_BOSS_MSG__CMsgListGetSendMsg16_ptr);
            using RACE_BOSS_MSG__CMsgListInit18_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*);
            using RACE_BOSS_MSG__CMsgListInit18_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, RACE_BOSS_MSG__CMsgListInit18_ptr);
            using RACE_BOSS_MSG__CMsgListLoad20_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, unsigned int);
            using RACE_BOSS_MSG__CMsgListLoad20_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, unsigned int, RACE_BOSS_MSG__CMsgListLoad20_ptr);
            using RACE_BOSS_MSG__CMsgListLoadIndexList22_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, int, struct CNetIndexList*);
            using RACE_BOSS_MSG__CMsgListLoadIndexList22_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, int, struct CNetIndexList*, RACE_BOSS_MSG__CMsgListLoadIndexList22_ptr);
            using RACE_BOSS_MSG__CMsgListLoadMsgList24_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct CNetIndexList*, unsigned int);
            using RACE_BOSS_MSG__CMsgListLoadMsgList24_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct CNetIndexList*, unsigned int, RACE_BOSS_MSG__CMsgListLoadMsgList24_ptr);
            using RACE_BOSS_MSG__CMsgListRefresh26_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*);
            using RACE_BOSS_MSG__CMsgListRefresh26_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, RACE_BOSS_MSG__CMsgListRefresh26_ptr);
            using RACE_BOSS_MSG__CMsgListRelease28_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgListRelease28_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgListRelease28_ptr);
            using RACE_BOSS_MSG__CMsgListRollBack30_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*);
            using RACE_BOSS_MSG__CMsgListRollBack30_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, RACE_BOSS_MSG__CMsgListRollBack30_ptr);
            using RACE_BOSS_MSG__CMsgListSave32_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*);
            using RACE_BOSS_MSG__CMsgListSave32_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, RACE_BOSS_MSG__CMsgListSave32_ptr);
            using RACE_BOSS_MSG__CMsgListSaveIndexList34_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, int, struct CNetIndexList*);
            using RACE_BOSS_MSG__CMsgListSaveIndexList34_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, int, struct CNetIndexList*, RACE_BOSS_MSG__CMsgListSaveIndexList34_ptr);
            using RACE_BOSS_MSG__CMsgListSaveMsgList36_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct CNetIndexList*);
            using RACE_BOSS_MSG__CMsgListSaveMsgList36_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, struct CNetIndexList*, RACE_BOSS_MSG__CMsgListSaveMsgList36_ptr);
            
            using RACE_BOSS_MSG__CMsgListdtor_CMsgList40_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*);
            using RACE_BOSS_MSG__CMsgListdtor_CMsgList40_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgList*, RACE_BOSS_MSG__CMsgListdtor_CMsgList40_ptr);
        }; // end namespace Info
    }; // end namespace RACE_BOSS_MSG
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CMyTimerBeginTimer2_ptr = void (WINAPIV*)(struct CMyTimer*, unsigned int);
        using CMyTimerBeginTimer2_clbk = void (WINAPIV*)(struct CMyTimer*, unsigned int, CMyTimerBeginTimer2_ptr);
        using CMyTimerBeginTimerAddLapse4_ptr = void (WINAPIV*)(struct CMyTimer*, unsigned int, unsigned int);
        using CMyTimerBeginTimerAddLapse4_clbk = void (WINAPIV*)(struct CMyTimer*, unsigned int, unsigned int, CMyTimerBeginTimerAddLapse4_ptr);
        
        using CMyTimerctor_CMyTimer6_ptr = void (WINAPIV*)(struct CMyTimer*);
        using CMyTimerctor_CMyTimer6_clbk = void (WINAPIV*)(struct CMyTimer*, CMyTimerctor_CMyTimer6_ptr);
        using CMyTimerCountingAddTickOld8_ptr = void (WINAPIV*)(struct CMyTimer*, unsigned int);
        using CMyTimerCountingAddTickOld8_clbk = void (WINAPIV*)(struct CMyTimer*, unsigned int, CMyTimerCountingAddTickOld8_ptr);
        using CMyTimerCountingTimer10_ptr = bool (WINAPIV*)(struct CMyTimer*);
        using CMyTimerCountingTimer10_clbk = bool (WINAPIV*)(struct CMyTimer*, CMyTimerCountingTimer10_ptr);
        using CMyTimerGetTerm12_ptr = unsigned int (WINAPIV*)(struct CMyTimer*);
        using CMyTimerGetTerm12_clbk = unsigned int (WINAPIV*)(struct CMyTimer*, CMyTimerGetTerm12_ptr);
        using CMyTimerNextTimeRun14_ptr = void (WINAPIV*)(struct CMyTimer*);
        using CMyTimerNextTimeRun14_clbk = void (WINAPIV*)(struct CMyTimer*, CMyTimerNextTimeRun14_ptr);
        using CMyTimerStopTimer16_ptr = void (WINAPIV*)(struct CMyTimer*);
        using CMyTimerStopTimer16_clbk = void (WINAPIV*)(struct CMyTimer*, CMyTimerStopTimer16_ptr);
        using CMyTimerTermTimeRun18_ptr = void (WINAPIV*)(struct CMyTimer*);
        using CMyTimerTermTimeRun18_clbk = void (WINAPIV*)(struct CMyTimer*, CMyTimerTermTimeRun18_ptr);
        
        using CMyTimerdtor_CMyTimer23_ptr = void (WINAPIV*)(struct CMyTimer*);
        using CMyTimerdtor_CMyTimer23_clbk = void (WINAPIV*)(struct CMyTimer*, CMyTimerdtor_CMyTimer23_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

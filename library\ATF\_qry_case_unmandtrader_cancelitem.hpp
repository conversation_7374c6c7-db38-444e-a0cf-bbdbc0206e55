// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_unmandtrader_cancelitem
    {
        unsigned __int16 wInx;
        unsigned __int16 wItemSerial;
        unsigned int dwOwnerSerial;
        char szAccount[13];
        char wszName[17];
        char byType;
        unsigned int dwRegistSerial;
        char byState;
        char byProcRet;
        unsigned int dwK;
        unsigned int dwD;
        unsigned int dwU;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_selfleave.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _qry_case_selfleavesize2_ptr = int (WINAPIV*)(struct _qry_case_selfleave*);
        using _qry_case_selfleavesize2_clbk = int (WINAPIV*)(struct _qry_case_selfleave*, _qry_case_selfleavesize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

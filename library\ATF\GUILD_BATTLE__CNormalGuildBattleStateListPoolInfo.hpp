// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateListPool.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolctor_CNormalGuildBattleStateListPool2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*);
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolctor_CNormalGuildBattleStateListPool2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*, GUILD_BATTLE__CNormalGuildBattleStateListPoolctor_CNormalGuildBattleStateListPool2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListPool<PERSON><PERSON>r4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*);
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolClear4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*, GUILD_BATTLE__CNormalGuildBattleStateListPoolClear4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolDestroy6_ptr = void (WINAPIV*)();
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolDestroy6_clbk = void (WINAPIV*)(GUILD_BATTLE__CNormalGuildBattleStateListPoolDestroy6_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolGet8_ptr = struct GUILD_BATTLE::CNormalGuildBattleStateList* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*, unsigned int);
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolGet8_clbk = struct GUILD_BATTLE::CNormalGuildBattleStateList* (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*, unsigned int, GUILD_BATTLE__CNormalGuildBattleStateListPoolGet8_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolInit10_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*);
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolInit10_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*, GUILD_BATTLE__CNormalGuildBattleStateListPoolInit10_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolInstance12_ptr = struct GUILD_BATTLE::CNormalGuildBattleStateListPool* (WINAPIV*)();
            using GUILD_BATTLE__CNormalGuildBattleStateListPoolInstance12_clbk = struct GUILD_BATTLE::CNormalGuildBattleStateListPool* (WINAPIV*)(GUILD_BATTLE__CNormalGuildBattleStateListPoolInstance12_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateListPooldtor_CNormalGuildBattleStateListPool16_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*);
            using GUILD_BATTLE__CNormalGuildBattleStateListPooldtor_CNormalGuildBattleStateListPool16_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateListPool*, GUILD_BATTLE__CNormalGuildBattleStateListPooldtor_CNormalGuildBattleStateListPool16_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

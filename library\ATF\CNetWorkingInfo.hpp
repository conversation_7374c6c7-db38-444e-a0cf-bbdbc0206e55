// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetWorking.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CNetWorkingAcceptClientCheck2_ptr = void (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, unsigned int);
        using CNetWorkingAcceptClientCheck2_clbk = void (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, unsigned int, CNetWorkingAcceptClientCheck2_ptr);
        using CNetWorkingAnsyncConnectComplete4_ptr = void (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, int);
        using CNetWorkingAnsyncConnectComplete4_clbk = void (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, int, CNetWorkingAnsyncConnectComplete4_ptr);
        
        using CNetWorkingctor_CNetWorking6_ptr = void (WINAPIV*)(struct CNetWorking*);
        using CNetWorkingctor_CNetWorking6_clbk = void (WINAPIV*)(struct CNetWorking*, CNetWorkingctor_CNetWorking6_ptr);
        using CNetWorkingCloseClientCheck8_ptr = void (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, unsigned int);
        using CNetWorkingCloseClientCheck8_clbk = void (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, unsigned int, CNetWorkingCloseClientCheck8_ptr);
        using CNetWorkingCloseSocket10_ptr = void (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, bool);
        using CNetWorkingCloseSocket10_clbk = void (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, bool, CNetWorkingCloseSocket10_ptr);
        using CNetWorkingConnect12_ptr = int (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, unsigned int, uint16_t);
        using CNetWorkingConnect12_clbk = int (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, unsigned int, uint16_t, CNetWorkingConnect12_ptr);
        using CNetWorkingDataAnalysis14_ptr = bool (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, struct _MSG_HEADER*, char*);
        using CNetWorkingDataAnalysis14_clbk = bool (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, struct _MSG_HEADER*, char*, CNetWorkingDataAnalysis14_ptr);
        using CNetWorkingExpulsionSocket16_ptr = bool (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, char, void*);
        using CNetWorkingExpulsionSocket16_clbk = bool (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, char, void*, CNetWorkingExpulsionSocket16_ptr);
        using CNetWorkingGetCheckRecvTime18_ptr = unsigned int (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int);
        using CNetWorkingGetCheckRecvTime18_clbk = unsigned int (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, CNetWorkingGetCheckRecvTime18_ptr);
        using CNetWorkingGetSocket20_ptr = struct _socket* (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int);
        using CNetWorkingGetSocket20_clbk = struct _socket* (WINAPIV*)(struct CNetWorking*, unsigned int, unsigned int, CNetWorkingGetSocket20_ptr);
        using CNetWorkingOnLoop22_ptr = void (WINAPIV*)(struct CNetWorking*);
        using CNetWorkingOnLoop22_clbk = void (WINAPIV*)(struct CNetWorking*, CNetWorkingOnLoop22_ptr);
        using CNetWorkingOnLoop_Receipt24_ptr = void (WINAPIV*)(struct CNetWorking*);
        using CNetWorkingOnLoop_Receipt24_clbk = void (WINAPIV*)(struct CNetWorking*, CNetWorkingOnLoop_Receipt24_ptr);
        using CNetWorkingProcessLogFile26_ptr = void (WINAPIV*)(struct CNetWorking*, unsigned int, bool, bool, bool);
        using CNetWorkingProcessLogFile26_clbk = void (WINAPIV*)(struct CNetWorking*, unsigned int, bool, bool, bool, CNetWorkingProcessLogFile26_ptr);
        using CNetWorkingRelease28_ptr = void (WINAPIV*)(struct CNetWorking*);
        using CNetWorkingRelease28_clbk = void (WINAPIV*)(struct CNetWorking*, CNetWorkingRelease28_ptr);
        using CNetWorkingSetNetSystem30_ptr = bool (WINAPIV*)(struct CNetWorking*, unsigned int, struct _NET_TYPE_PARAM*, char*, char*);
        using CNetWorkingSetNetSystem30_clbk = bool (WINAPIV*)(struct CNetWorking*, unsigned int, struct _NET_TYPE_PARAM*, char*, char*, CNetWorkingSetNetSystem30_ptr);
        using CNetWorkingUserLoop32_ptr = void (WINAPIV*)(struct CNetWorking*);
        using CNetWorkingUserLoop32_clbk = void (WINAPIV*)(struct CNetWorking*, CNetWorkingUserLoop32_ptr);
        
        using CNetWorkingdtor_CNetWorking37_ptr = void (WINAPIV*)(struct CNetWorking*);
        using CNetWorkingdtor_CNetWorking37_clbk = void (WINAPIV*)(struct CNetWorking*, CNetWorkingdtor_CNetWorking37_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

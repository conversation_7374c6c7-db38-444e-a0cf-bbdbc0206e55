// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagCY.hpp>


START_ATF_NAMESPACE
    struct $AD8630CBC7A55BCFB532806489E2D4B2
    {
        BYTE gap0[8];
        tagCY *pcyVal;
    };    
    static_assert(ATF::checkSize<$AD8630CBC7A55BCFB532806489E2D4B2, 16>(), "$AD8630CBC7A55BCFB532806489E2D4B2");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagHH_AKLINK
    {
        int cbStruct;
        int fReserved;
        const char *pszKeywords;
        const char *pszUrl;
        const char *pszMsgText;
        const char *pszMsgTitle;
        const char *pszWindow;
        int fIndexOnFail;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

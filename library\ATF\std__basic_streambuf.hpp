// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Mutex.hpp>
#include <std__basic_streambufVtbl.hpp>
#include <std__locale.hpp>



START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct basic_streambuf<wchar_t,char_traits<wchar_t> >
        {
            basic_streambuf<wchar_t,char_traits<wchar_t> >Vtbl *vfptr;
            _Mutex _Mylock;
            wchar_t *_Gfirst;
            wchar_t *_Pfirst;
            wchar_t **_IGfirst;
            wchar_t **_IPfirst;
            wchar_t *_Gnext;
            wchar_t *_Pnext;
            wchar_t **_IGnext;
            wchar_t **_IPnext;
            int _Gcount;
            int _Pcount;
            int *_IGcount;
            int *_IPcount;
            locale *_Plocale;
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std___Mutex.hpp>
#include <std__basic_streambufVtbl.hpp>
#include <std__locale.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct basic_streambuf<char,char_traits<char> >
        {
            basic_streambuf<char,char_traits<char> >Vtbl *vfptr;
            _Mutex _Mylock;
            char *_Gfirst;
            char *_Pfirst;
            char **_IGfirst;
            char **_IPfirst;
            char *_Gnext;
            char *_Pnext;
            char **_IGnext;
            char **_IPnext;
            int _Gcount;
            int _Pcount;
            int *_IGcount;
            int *_IPcount;
            locale *_Plocale;
        };
    }; // end namespace std
END_ATF_NAMESPACE

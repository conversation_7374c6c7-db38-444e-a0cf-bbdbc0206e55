// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CD3DCamera
    {
    public:
        CD3DCamera();
        int64_t ctor_CD3DCamera();
        void SetProjParams(float arg_0, float arg_1, float arg_2, float arg_3);
        void SetViewParams(struct D3DXVECTOR3* arg_0, struct D3DXVECTOR3* arg_1, struct D3DXVECTOR3* arg_2);
    }
    ;
END_ATF_NAMESPACE

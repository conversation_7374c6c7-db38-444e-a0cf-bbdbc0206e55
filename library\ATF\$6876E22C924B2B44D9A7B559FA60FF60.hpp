// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$70D636CC6D3E102A1CB1CECA38555A4E.hpp>
#include <$E7FD15DFD778E8FFF937F659685E7981.hpp>
#include <_CACHE_DESCRIPTOR.hpp>


START_ATF_NAMESPACE
    union $6876E22C924B2B44D9A7B559FA60FF60
    {
        $70D636CC6D3E102A1CB1CECA38555A4E ProcessorCore;
        $E7FD15DFD778E8FFF937F659685E7981 NumaNode;
        _CACHE_DESCRIPTOR Cache;
        unsigned __int64 Reserved[2];
    };
END_ATF_NAMESPACE

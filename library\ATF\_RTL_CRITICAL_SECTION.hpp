// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_RTL_CRITICAL_SECTION_DEBUG.hpp>


START_ATF_NAMESPACE
    struct _RTL_CRITICAL_SECTION
    {
        _RTL_CRITICAL_SECTION_DEBUG *DebugInfo;
        int LockCount;
        int RecursionCount;
        void *OwningThread;
        void *LockSemaphore;
        unsigned __int64 SpinCount;
    };    
    static_assert(ATF::checkSize<_RTL_CRITICAL_SECTION, 40>(), "_RTL_CRITICAL_SECTION");
END_ATF_NAMESPACE

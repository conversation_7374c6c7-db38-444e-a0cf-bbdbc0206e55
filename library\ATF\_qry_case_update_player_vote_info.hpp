// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_update_player_vote_info
    {
        unsigned int dwAccumPlayTime;
        char byIsVoted;
        char byVoteEnable;
        unsigned __int16 wScaner;
        unsigned __int64 dwScanerData;
        unsigned int dwAccountSerial;
        unsigned int dwCharSerial;
        char wszCharName[17];
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_update_player_vote_info, 48>(), "_qry_case_update_player_vote_info");
END_ATF_NAMESPACE

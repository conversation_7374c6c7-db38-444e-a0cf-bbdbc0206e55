// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_be_damaged_char.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _be_damaged_charctor__be_damaged_char2_ptr = void (WINAPIV*)(struct _be_damaged_char*);
        using _be_damaged_charctor__be_damaged_char2_clbk = void (WINAPIV*)(struct _be_damaged_char*, _be_damaged_charctor__be_damaged_char2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

#include <_personal_automine_battery_extract_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_battery_extract_zocl::_personal_automine_battery_extract_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_battery_extract_zocl*);
        (org_ptr(0x1402e19f0L))(this);
    };
    void _personal_automine_battery_extract_zocl::ctor__personal_automine_battery_extract_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_battery_extract_zocl*);
        (org_ptr(0x1402e19f0L))(this);
    };
    int _personal_automine_battery_extract_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_battery_extract_zocl*);
        return (org_ptr(0x1402e1a40L))(this);
    };
END_ATF_NAMESPACE

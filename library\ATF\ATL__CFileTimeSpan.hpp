// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct CFileTimeSpan
        {
            __int64 m_nSpan;
        public:
            CFileTimeSpan(struct CFileTimeSpan* span);
            void ctor_CFileTimeSpan(struct CFileTimeSpan* span);
            CFileTimeSpan(int64_t nSpan);
            void ctor_CFileTimeSpan(int64_t nSpan);
            CFileTimeSpan();
            void ctor_CFileTimeSpan();
            int64_t GetTimeSpan();
            void SetTimeSpan(int64_t nSpan);
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _billing_alive_request_zobi
    {
        char szID[13];
        char szIP[16];
        char szCMS[7];
        __int16 iType;
        bool bIsPcBang;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

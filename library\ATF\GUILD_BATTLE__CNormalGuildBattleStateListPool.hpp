// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateList.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CNormalGuildBattleStateListPool
        {
            unsigned int m_dwMaxCount;
            CNormalGuildBattleStateList *m_pkStateList;
        public:
            CNormalGuildBattleStateListPool();
            void ctor_CNormalGuildBattleStateListPool();
            void Clear();
            static void Destroy();
            struct CNormalGuildBattleStateList* Get(unsigned int dwID);
            bool Init();
            static struct CNormalGuildBattleStateListPool* Instance();
            ~CNormalGuildBattleStateListPool();
            void dtor_CNormalGuildBattleStateListPool();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

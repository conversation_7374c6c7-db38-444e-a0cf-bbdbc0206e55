// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTime.hpp>
#include <CMyTimer.hpp>
#include <GUILD_BATTLE__CGuildBattleReservedScheduleMapGroup.hpp>
#include <GUILD_BATTLE__CGuildBattleSchedule.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattleScheduleManager
        {
            bool m_bLoad;
            ATL::CTime *m_pkOldDayTime;
            CMyTimer *m_pkTimer;
            unsigned int m_uiMapCnt;
            CGuildBattleReservedScheduleMapGroup m_kSchdule[2];
            CGuildBattleReservedScheduleMapGroup *m_pkTodaySchedule;
            CGuildBattleReservedScheduleMapGroup *m_pkTomorrowSchedule;
        public:
            char Add(unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, struct CGuildBattleSchedule** ppkSchedule, unsigned int* uiSLID);
            bool AddDefaultDBTable();
            CGuildBattleScheduleManager();
            void ctor_CGuildBattleScheduleManager();
            bool CleanUpDanglingReservedSchedule();
            void Clear();
            bool ClearTommorowScheduleByID(unsigned int uiMapID, unsigned int dwID);
            static void Destroy();
            void Flip();
            unsigned int GetCurScheduleID(unsigned int uiMapID);
            unsigned int GetTodayDayID();
            bool GetTodaySLIDByMap(unsigned int uiMap, unsigned int* uiSLID);
            unsigned int GetTomorrowDayID();
            bool GetTomorrowSLIDByMap(unsigned int uiMap, unsigned int* uiSLID);
            bool Init(unsigned int uiMapCnt);
            static struct CGuildBattleScheduleManager* Instance();
            int IsDayChanged();
            char IsEmptyTime(unsigned int uiFieldInx, unsigned int dwStartTimeInx);
            bool Load(int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTodayDayID, int iTomorrow, int iTomorrowDayID);
            void Loop();
            void SetNextEvnet();
            void UpdateDayChangedWork();
            struct CGuildBattleSchedule* UpdateUseFlag(unsigned int uiDayID, unsigned int uiMapID, unsigned int dwID);
            ~CGuildBattleScheduleManager();
            void dtor_CGuildBattleScheduleManager();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FILETIME.hpp>
#include <_GUID.hpp>
#include <_ULARGE_INTEGER.hpp>



START_ATF_NAMESPACE
    struct tagSTATSTG
    {
        wchar_t *pwcsName;
        unsigned int type;
        _ULARGE_INTEGER cbSize;
        _FILETIME mtime;
        _FILETIME ctime;
        _FILETIME atime;
        unsigned int grfMode;
        unsigned int grfLocksSupported;
        _GUID clsid;
        unsigned int grfStateBits;
        unsigned int reserved;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagELEMDESC.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagFUNCDESC
    {
        int memid;
        int *lprgscode;
        tagELEMDESC *lprgelemdescParam;
        tagFUNCKIND funckind;
        tagINVOKEKIND invkind;
        tagCALLCONV callconv;
        __int16 cParams;
        __int16 cParamsOpt;
        __int16 oVft;
        __int16 cScodes;
        tagELEMDESC elemdescFunc;
        unsigned __int16 wFuncFlags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct $B25D19047E6B4EFA2CA7947450284408
    {
        unsigned int Length;
        unsigned __int16 NumberOfRelocations;
        unsigned __int16 NumberOfLinenumbers;
        unsigned int CheckSum;
        __int16 Number;
        char Selection;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

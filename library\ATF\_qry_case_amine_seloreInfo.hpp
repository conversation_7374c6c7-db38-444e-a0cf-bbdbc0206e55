// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_amine_selore.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_amine_selorector__qry_case_amine_selore2_ptr = void (WINAPIV*)(struct _qry_case_amine_selore*);
        using _qry_case_amine_selorector__qry_case_amine_selore2_clbk = void (WINAPIV*)(struct _qry_case_amine_selore*, _qry_case_amine_selorector__qry_case_amine_selore2_ptr);
        using _qry_case_amine_seloresize4_ptr = int (WINAPIV*)(struct _qry_case_amine_selore*);
        using _qry_case_amine_seloresize4_clbk = int (WINAPIV*)(struct _qry_case_amine_selore*, _qry_case_amine_seloresize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

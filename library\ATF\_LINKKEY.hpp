// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _LINKKEY
    {
        unsigned __int16 wEffectCode;
    public:
        int16_t CovDBKey();
        uint16_t GetCode();
        uint16_t GetIndex();
        bool IsFilled();
        void LoadDBKey(int16_t pl_zKey);
        void SetData(uint16_t wCode, uint16_t wIndex);
        void SetRelease();
    };    
    static_assert(ATF::checkSize<_LINKKEY, 2>(), "_LINKKEY");
END_ATF_NAMESPACE

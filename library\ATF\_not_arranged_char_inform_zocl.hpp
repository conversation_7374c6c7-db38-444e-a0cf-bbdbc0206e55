// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_NOT_ARRANGED_AVATOR_DB.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _not_arranged_char_inform_zocl
    {
        char byCharNum;
        _NOT_ARRANGED_AVATOR_DB CharList[50];
    public:
        _not_arranged_char_inform_zocl();
        void ctor__not_arranged_char_inform_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_not_arranged_char_inform_zocl, 3451>(), "_not_arranged_char_inform_zocl");
END_ATF_NAMESPACE

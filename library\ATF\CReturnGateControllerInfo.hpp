// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CReturnGateController.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CReturnGateControllerctor_CReturnGateController2_ptr = void (WINAPIV*)(struct CReturnGateController*);
        using CReturnGateControllerctor_CReturnGateController2_clbk = void (WINAPIV*)(struct CReturnGateController*, CReturnGateControllerctor_CReturnGateController2_ptr);
        using CReturnGateControllerCleanUp4_ptr = void (WINAPIV*)(struct CReturnGateController*);
        using CReturnGateControllerCleanUp4_clbk = void (WINAPIV*)(struct CReturnGateController*, CReturnGateControllerCleanUp4_ptr);
        using CReturnGateControllerClose6_ptr = void (WINAPIV*)(struct CReturnGateController*, struct CReturnGate*);
        using CReturnGateControllerClose6_clbk = void (WINAPIV*)(struct CReturnGateController*, struct CReturnGate*, CReturnGateControllerClose6_ptr);
        using CReturnGateControllerDestroy8_ptr = void (WINAPIV*)();
        using CReturnGateControllerDestroy8_clbk = void (WINAPIV*)(CReturnGateControllerDestroy8_ptr);
        using CReturnGateControllerEnter10_ptr = bool (WINAPIV*)(struct CReturnGateController*, unsigned int, struct CPlayer*);
        using CReturnGateControllerEnter10_clbk = bool (WINAPIV*)(struct CReturnGateController*, unsigned int, struct CPlayer*, CReturnGateControllerEnter10_ptr);
        using CReturnGateControllerGetEmpty12_ptr = struct CReturnGate* (WINAPIV*)(struct CReturnGateController*);
        using CReturnGateControllerGetEmpty12_clbk = struct CReturnGate* (WINAPIV*)(struct CReturnGateController*, CReturnGateControllerGetEmpty12_ptr);
        using CReturnGateControllerGetGate14_ptr = struct CReturnGate* (WINAPIV*)(struct CReturnGateController*, unsigned int);
        using CReturnGateControllerGetGate14_clbk = struct CReturnGate* (WINAPIV*)(struct CReturnGateController*, unsigned int, CReturnGateControllerGetGate14_ptr);
        using CReturnGateControllerInit16_ptr = bool (WINAPIV*)(struct CReturnGateController*, unsigned int);
        using CReturnGateControllerInit16_clbk = bool (WINAPIV*)(struct CReturnGateController*, unsigned int, CReturnGateControllerInit16_ptr);
        using CReturnGateControllerInstance18_ptr = struct CReturnGateController* (WINAPIV*)();
        using CReturnGateControllerInstance18_clbk = struct CReturnGateController* (WINAPIV*)(CReturnGateControllerInstance18_ptr);
        using CReturnGateControllerIsExistOwner20_ptr = bool (WINAPIV*)(struct CReturnGateController*, struct CPlayer*);
        using CReturnGateControllerIsExistOwner20_clbk = bool (WINAPIV*)(struct CReturnGateController*, struct CPlayer*, CReturnGateControllerIsExistOwner20_ptr);
        using CReturnGateControllerOnLoop22_ptr = void (WINAPIV*)(struct CReturnGateController*);
        using CReturnGateControllerOnLoop22_clbk = void (WINAPIV*)(struct CReturnGateController*, CReturnGateControllerOnLoop22_ptr);
        using CReturnGateControllerOpen24_ptr = bool (WINAPIV*)(struct CReturnGateController*, struct CPlayer*);
        using CReturnGateControllerOpen24_clbk = bool (WINAPIV*)(struct CReturnGateController*, struct CPlayer*, CReturnGateControllerOpen24_ptr);
        using CReturnGateControllerProcessEnter26_ptr = int (WINAPIV*)(struct CReturnGateController*, unsigned int, struct CPlayer*);
        using CReturnGateControllerProcessEnter26_clbk = int (WINAPIV*)(struct CReturnGateController*, unsigned int, struct CPlayer*, CReturnGateControllerProcessEnter26_ptr);
        using CReturnGateControllerSendEnterResult28_ptr = void (WINAPIV*)(struct CReturnGateController*, int, struct CPlayer*);
        using CReturnGateControllerSendEnterResult28_clbk = void (WINAPIV*)(struct CReturnGateController*, int, struct CPlayer*, CReturnGateControllerSendEnterResult28_ptr);
        using CReturnGateControllerUpdateClose30_ptr = void (WINAPIV*)(struct CReturnGateController*);
        using CReturnGateControllerUpdateClose30_clbk = void (WINAPIV*)(struct CReturnGateController*, CReturnGateControllerUpdateClose30_ptr);
        
        using CReturnGateControllerdtor_CReturnGateController34_ptr = void (WINAPIV*)(struct CReturnGateController*);
        using CReturnGateControllerdtor_CReturnGateController34_clbk = void (WINAPIV*)(struct CReturnGateController*, CReturnGateControllerdtor_CReturnGateController34_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  DLGITEMTEMPLATE
    {
        unsigned int style;
        unsigned int dwExtendedStyle;
        __int16 x;
        __int16 y;
        __int16 cx;
        __int16 cy;
        unsigned __int16 id;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

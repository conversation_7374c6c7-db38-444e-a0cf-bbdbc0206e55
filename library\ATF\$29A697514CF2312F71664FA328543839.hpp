// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_D3DR3VERTEX_TEX1.hpp>
#include <_D3DR3VERTEX_TEX2.hpp>


START_ATF_NAMESPACE
    union $29A697514CF2312F71664FA328543839
    {
        struct IDirect3DVertexBuffer8 *m_lpVertexBuffer;
        _D3DR3VERTEX_TEX1 *m_VertexBufferTex1;
        _D3DR3VERTEX_TEX2 *m_VertexBufferTex2;
    };
END_ATF_NAMESPACE

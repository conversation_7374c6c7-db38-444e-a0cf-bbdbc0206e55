// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataUS.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataUSctor_CNationSettingDataUS2_ptr = void (WINAPIV*)(struct CNationSettingDataUS*);
        using CNationSettingDataUSctor_CNationSettingDataUS2_clbk = void (WINAPIV*)(struct CNationSettingDataUS*, CNationSettingDataUSctor_CNationSettingDataUS2_ptr);
        using CNationSettingDataUSCreateWorker4_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataUS*);
        using CNationSettingDataUSCreateWorker4_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataUS*, CNationSettingDataUSCreateWorker4_ptr);
        using CNationSettingDataUSGetCashItemPrice6_ptr = int (WINAPIV*)(struct CNationSettingDataUS*, struct _CashShop_str_fld*);
        using CNationSettingDataUSGetCashItemPrice6_clbk = int (WINAPIV*)(struct CNationSettingDataUS*, struct _CashShop_str_fld*, CNationSettingDataUSGetCashItemPrice6_ptr);
        using CNationSettingDataUSGetItemName8_ptr = char* (WINAPIV*)(struct CNationSettingDataUS*, struct _NameTxt_fld*);
        using CNationSettingDataUSGetItemName8_clbk = char* (WINAPIV*)(struct CNationSettingDataUS*, struct _NameTxt_fld*, CNationSettingDataUSGetItemName8_ptr);
        using CNationSettingDataUSInit10_ptr = int (WINAPIV*)(struct CNationSettingDataUS*);
        using CNationSettingDataUSInit10_clbk = int (WINAPIV*)(struct CNationSettingDataUS*, CNationSettingDataUSInit10_ptr);
        using CNationSettingDataUSReadSystemPass12_ptr = bool (WINAPIV*)(struct CNationSettingDataUS*);
        using CNationSettingDataUSReadSystemPass12_clbk = bool (WINAPIV*)(struct CNationSettingDataUS*, CNationSettingDataUSReadSystemPass12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HBITMAP__.hpp>



START_ATF_NAMESPACE
    struct _HD_ITEMW
    {
        unsigned int mask;
        int cxy;
        wchar_t *pszText;
        HBITMAP__ *hbm;
        int cchTextMax;
        int fmt;
        __int64 lParam;
        int iImage;
        int iOrder;
        unsigned int type;
        void *pvFilter;
    };
END_ATF_NAMESPACE

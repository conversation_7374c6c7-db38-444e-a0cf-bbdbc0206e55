// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CHRID.hpp>


START_ATF_NAMESPACE
    struct _class_skill_result_other_zocl
    {
        char byRetCode;
        _CHRID idPerformer;
        _CHRID idDster;
         unsigned __int16 wSkillIndex;
        char byAttackSerial;
    };
END_ATF_NAMESPACE

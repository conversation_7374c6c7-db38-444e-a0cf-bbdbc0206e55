// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LTD_EXPEND.hpp>
#include <_LTD_ITEMINFO.hpp>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _LTD
    {
        _SYSTEMTIME m_timeLocal;
        unsigned int m_dwLogSerial;
        char m_byMainLogType;
        char m_bySubLogType;
        unsigned int m_dwAccountSerial;
        unsigned int m_dwCharSerial;
        char m_szAccount[13];
        char m_wszAvatorName[17];
        _LTD_ITEMINFO m_ItemInfo;
        _LTD_EXPEND m_Expend;
    public:
        void set(char byMainLogType, char bySubLogType);
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

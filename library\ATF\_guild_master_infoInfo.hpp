// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_guild_master_info.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _guild_master_infoIsFill2_ptr = bool (WINAPIV*)(struct _guild_master_info*);
        using _guild_master_infoIsFill2_clbk = bool (WINAPIV*)(struct _guild_master_info*, _guild_master_infoIsFill2_ptr);
        
        using _guild_master_infoctor__guild_master_info4_ptr = void (WINAPIV*)(struct _guild_master_info*);
        using _guild_master_infoctor__guild_master_info4_clbk = void (WINAPIV*)(struct _guild_master_info*, _guild_master_infoctor__guild_master_info4_ptr);
        using _guild_master_infoinit6_ptr = void (WINAPIV*)(struct _guild_master_info*);
        using _guild_master_infoinit6_clbk = void (WINAPIV*)(struct _guild_master_info*, _guild_master_infoinit6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SetItemEff_fld.hpp>
#include <si_effect.hpp>


START_ATF_NAMESPACE
    struct si_interpret
    {
        struct effect_data
        {
            int iEffectCode;
            float fEffectValue;
        };
        char byEffectTypeCount;
        si_effect effect_type[8];
        effect_data effect_info[8];
    public:
        char GetCountOfEffect(int idx);
        char GetCountOfItem(int idx);
        int GetEffectCode(int idx);
        char GetEffectTypeCount();
        float GetEffectValue(int idx);
        void init();
        bool set_effect_interpret(struct _SetItemEff_fld* pFld);
        si_interpret();
        void ctor_si_interpret();
    };
END_ATF_NAMESPACE

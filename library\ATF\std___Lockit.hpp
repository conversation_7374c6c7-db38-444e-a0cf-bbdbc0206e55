// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace std
    {
        struct _Lockit
        {
            int _Locktype;
        public:
            _Lockit(int arg_0);
            int64_t ctor__Lockit(int arg_0);
            ~_Lockit();
            int64_t dtor__Lockit();
        };
    }; // end namespace std
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ACE_HEADER.hpp>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    struct _ACCESS_DENIED_OBJECT_ACE
    {
        _ACE_HEADER Header;
        unsigned int Mask;
        unsigned int Flags;
        _GUID ObjectType;
        _GUID InheritedObjectType;
        unsigned int SidStart;
    };
END_ATF_NAMESPACE

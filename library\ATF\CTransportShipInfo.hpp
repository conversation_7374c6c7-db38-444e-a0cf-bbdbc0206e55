// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTransportShip.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CTransportShipAlterState2_ptr = void (WINAPIV*)(struct CTransportShip*, bool, char, int, int);
        using CTransportShipAlterState2_clbk = void (WINAPIV*)(struct CTransportShip*, bool, char, int, int, CTransportShipAlterState2_ptr);
        using CTransportShipApplyTicketReserver4_ptr = void (WINAPIV*)(struct CTransportShip*);
        using CTransportShipApplyTicketReserver4_clbk = void (WINAPIV*)(struct CTransportShip*, CTransportShipApplyTicketReserver4_ptr);
        
        using CTransportShipctor_CTransportShip6_ptr = void (WINAPIV*)(struct CTransportShip*);
        using CTransport<PERSON>hipctor_CTransportShip6_clbk = void (WINAPIV*)(struct CTransportShip*, CTransportShipctor_CTransportShip6_ptr);
        using CTransportShipCheckHurry8_ptr = void (WINAPIV*)(struct CTransportShip*);
        using CTransportShipCheckHurry8_clbk = void (WINAPIV*)(struct CTransportShip*, CTransportShipCheckHurry8_ptr);
        using CTransportShipCheckTicket10_ptr = void (WINAPIV*)(struct CTransportShip*);
        using CTransportShipCheckTicket10_clbk = void (WINAPIV*)(struct CTransportShip*, CTransportShipCheckTicket10_ptr);
        using CTransportShipCheckTicket_Kick12_ptr = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*, int);
        using CTransportShipCheckTicket_Kick12_clbk = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*, int, CTransportShipCheckTicket_Kick12_ptr);
        using CTransportShipCheckTicket_Pass14_ptr = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*, int);
        using CTransportShipCheckTicket_Pass14_clbk = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*, int, CTransportShipCheckTicket_Pass14_ptr);
        using CTransportShipEnterMember16_ptr = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*);
        using CTransportShipEnterMember16_clbk = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*, CTransportShipEnterMember16_ptr);
        using CTransportShipExitMember18_ptr = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*, bool);
        using CTransportShipExitMember18_clbk = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*, bool, CTransportShipExitMember18_ptr);
        using CTransportShipGetCurRideShipThisTicket20_ptr = bool (WINAPIV*)(struct CTransportShip*, struct _TicketItem_fld*);
        using CTransportShipGetCurRideShipThisTicket20_clbk = bool (WINAPIV*)(struct CTransportShip*, struct _TicketItem_fld*, CTransportShipGetCurRideShipThisTicket20_ptr);
        using CTransportShipGetEmptyNewMember22_ptr = struct CTransportShip::__mgr_member* (WINAPIV*)(struct CTransportShip*);
        using CTransportShipGetEmptyNewMember22_clbk = struct CTransportShip::__mgr_member* (WINAPIV*)(struct CTransportShip*, CTransportShipGetEmptyNewMember22_ptr);
        using CTransportShipGetLeftTicketIncludeReserNum24_ptr = int (WINAPIV*)(struct CTransportShip*, char*, int);
        using CTransportShipGetLeftTicketIncludeReserNum24_clbk = int (WINAPIV*)(struct CTransportShip*, char*, int, CTransportShipGetLeftTicketIncludeReserNum24_ptr);
        using CTransportShipGetMapCurDirect26_ptr = struct CMapData* (WINAPIV*)(struct CTransportShip*);
        using CTransportShipGetMapCurDirect26_clbk = struct CMapData* (WINAPIV*)(struct CTransportShip*, CTransportShipGetMapCurDirect26_ptr);
        using CTransportShipGetOutPortalIndex28_ptr = int (WINAPIV*)(struct CTransportShip*, int, char);
        using CTransportShipGetOutPortalIndex28_clbk = int (WINAPIV*)(struct CTransportShip*, int, char, CTransportShipGetOutPortalIndex28_ptr);
        using CTransportShipGetRideLimLevel30_ptr = int (WINAPIV*)(struct CTransportShip*);
        using CTransportShipGetRideLimLevel30_clbk = int (WINAPIV*)(struct CTransportShip*, CTransportShipGetRideLimLevel30_ptr);
        using CTransportShipGetRideUpLimLevel32_ptr = int (WINAPIV*)(struct CTransportShip*);
        using CTransportShipGetRideUpLimLevel32_clbk = int (WINAPIV*)(struct CTransportShip*, CTransportShipGetRideUpLimLevel32_ptr);
        using CTransportShipGetStartPosInShip34_ptr = void (WINAPIV*)(struct CTransportShip*, float*);
        using CTransportShipGetStartPosInShip34_clbk = void (WINAPIV*)(struct CTransportShip*, float*, CTransportShipGetStartPosInShip34_ptr);
        using CTransportShipInitShip36_ptr = bool (WINAPIV*)(struct CTransportShip*, struct CMapData*, struct CMapData*, struct CMapData*, char);
        using CTransportShipInitShip36_clbk = bool (WINAPIV*)(struct CTransportShip*, struct CMapData*, struct CMapData*, struct CMapData*, char, CTransportShipInitShip36_ptr);
        using CTransportShipInitTicketReserver38_ptr = void (WINAPIV*)(struct CTransportShip*);
        using CTransportShipInitTicketReserver38_clbk = void (WINAPIV*)(struct CTransportShip*, CTransportShipInitTicketReserver38_ptr);
        using CTransportShipIsMemberBeforeLogoff40_ptr = bool (WINAPIV*)(struct CTransportShip*, unsigned int);
        using CTransportShipIsMemberBeforeLogoff40_clbk = bool (WINAPIV*)(struct CTransportShip*, unsigned int, CTransportShipIsMemberBeforeLogoff40_ptr);
        using CTransportShipIsOldMember42_ptr = bool (WINAPIV*)(struct CTransportShip*, struct CPlayer*);
        using CTransportShipIsOldMember42_clbk = bool (WINAPIV*)(struct CTransportShip*, struct CPlayer*, CTransportShipIsOldMember42_ptr);
        using CTransportShipKickFreeMember44_ptr = void (WINAPIV*)(struct CTransportShip*);
        using CTransportShipKickFreeMember44_clbk = void (WINAPIV*)(struct CTransportShip*, CTransportShipKickFreeMember44_ptr);
        using CTransportShipKickOldMember46_ptr = void (WINAPIV*)(struct CTransportShip*, char);
        using CTransportShipKickOldMember46_clbk = void (WINAPIV*)(struct CTransportShip*, char, CTransportShipKickOldMember46_ptr);
        using CTransportShipLoop48_ptr = void (WINAPIV*)(struct CTransportShip*);
        using CTransportShipLoop48_clbk = void (WINAPIV*)(struct CTransportShip*, CTransportShipLoop48_ptr);
        using CTransportShipReEnterMember50_ptr = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*);
        using CTransportShipReEnterMember50_clbk = void (WINAPIV*)(struct CTransportShip*, struct CPlayer*, CTransportShipReEnterMember50_ptr);
        using CTransportShipRenewOldMember52_ptr = bool (WINAPIV*)(struct CTransportShip*, struct CPlayer*);
        using CTransportShipRenewOldMember52_clbk = bool (WINAPIV*)(struct CTransportShip*, struct CPlayer*, CTransportShipRenewOldMember52_ptr);
        using CTransportShipSendMsg_KickForSail54_ptr = void (WINAPIV*)(struct CTransportShip*, int);
        using CTransportShipSendMsg_KickForSail54_clbk = void (WINAPIV*)(struct CTransportShip*, int, CTransportShipSendMsg_KickForSail54_ptr);
        using CTransportShipSendMsg_TicketCheck56_ptr = void (WINAPIV*)(struct CTransportShip*, int, bool, uint16_t);
        using CTransportShipSendMsg_TicketCheck56_clbk = void (WINAPIV*)(struct CTransportShip*, int, bool, uint16_t, CTransportShipSendMsg_TicketCheck56_ptr);
        using CTransportShipSendMsg_TransportShipState58_ptr = void (WINAPIV*)(struct CTransportShip*, int);
        using CTransportShipSendMsg_TransportShipState58_clbk = void (WINAPIV*)(struct CTransportShip*, int, CTransportShipSendMsg_TransportShipState58_ptr);
        using CTransportShipTicketting60_ptr = bool (WINAPIV*)(struct CTransportShip*, struct CPlayer*);
        using CTransportShipTicketting60_clbk = bool (WINAPIV*)(struct CTransportShip*, struct CPlayer*, CTransportShipTicketting60_ptr);
        
        using CTransportShipdtor_CTransportShip62_ptr = void (WINAPIV*)(struct CTransportShip*);
        using CTransportShipdtor_CTransportShip62_clbk = void (WINAPIV*)(struct CTransportShip*, CTransportShipdtor_CTransportShip62_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

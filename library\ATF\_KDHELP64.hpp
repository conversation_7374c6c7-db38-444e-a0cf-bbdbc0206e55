// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _KDHELP64
    {
        unsigned __int64 Thread;
        unsigned int ThCallbackStack;
        unsigned int ThCallbackBStore;
        unsigned int NextCallback;
        unsigned int FramePointer;
        unsigned __int64 KiCallUserMode;
        unsigned __int64 KeUserCallbackDispatcher;
        unsigned __int64 SystemRangeStart;
        unsigned __int64 Reserved[8];
    };
END_ATF_NAMESPACE

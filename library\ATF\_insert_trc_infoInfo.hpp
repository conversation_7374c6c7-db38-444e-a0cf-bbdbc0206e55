// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_insert_trc_info.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _insert_trc_infosize2_ptr = int (WINAPIV*)(struct _insert_trc_info*);
        using _insert_trc_infosize2_clbk = int (WINAPIV*)(struct _insert_trc_info*, _insert_trc_infosize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _SECURITY_QUALITY_OF_SERVICE
    {
        unsigned int Length;
        _SECURITY_IMPERSONATION_LEVEL ImpersonationLevel;
        char ContextTrackingMode;
        char EffectiveOnly;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_NDR_CS_SIZE_CONVERT_ROUTINES.hpp>


START_ATF_NAMESPACE
    const struct _NDR_CS_ROUTINES
    {
        _NDR_CS_SIZE_CONVERT_ROUTINES *pSizeConvertRoutines;
        void (WINAPIV **pTagGettingRoutines)(void *, int, unsigned int *, unsigned int *, unsigned int *, unsigned int *);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoneySupplyMgrVtbl.hpp>
#include <_MONEY_SUPPLY_DATA.hpp>


START_ATF_NAMESPACE
    struct CMoneySupplyMgr
    {
        CMoneySupplyMgrVtbl *vfptr;
        unsigned int m_dwLastSendTime;
        unsigned int m_dwSystemOperStartTime;
        _MONEY_SUPPLY_DATA m_MS_data;
        _MONEY_SUPPLY_DATA m_MS_Senddata;
    public:
        CMoneySupplyMgr();
        void ctor_CMoneySupplyMgr();
        void Initialize();
        static struct CMoneySupplyMgr* Instance();
        void LoopMoneySupply();
        void SendMsg_MoneySupplyDataToWeb(struct _MONEY_SUPPLY_DATA* pMSData);
        void UpdateBuyData(char byRace, int nLv, char* szClass, unsigned int nAmount);
        void UpdateBuyUnitData(int nLv, unsigned int nAmount);
        void UpdateFeeMoneyData(char byRace, int nLv, unsigned int nAmount);
        void UpdateGateRewardMoneyData(char byRace, int nLv, char* szClass, unsigned int nAmount);
        void UpdateHonorGuildMoneyData(char byTradeType, char byRace, unsigned int nAmount);
        void UpdateQuestRewardMoneyData(char byRace, int nLv, char* szClass, unsigned int nAmount);
        void UpdateSellData(char byRace, int nLv, char* szClass, unsigned int nAmount);
        void UpdateUnitRepairingChargesData(int nLv, unsigned int nAmount);
        ~CMoneySupplyMgr();
        void dtor_CMoneySupplyMgr();
    };
END_ATF_NAMESPACE

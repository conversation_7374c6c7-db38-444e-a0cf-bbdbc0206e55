// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CGameObjectVtbl
    {
        void *(WINAPIV *__vecDelDtor)(struct CGameObject *_this, unsigned int);
        void (WINAPIV *SetStun)(struct CGameObject *_this, bool);
        uint16_t(WINAPIV *CalcCurHPRate)(struct CGameObject *_this);
        void (WINAPIV *SendMsg_RealFixPosition)(struct CGameObject *_this, bool);
        void (WINAPIV *Loop)(struct CGameObject *_this);
        void (WINAPIV *AlterSec)(struct CGameObject *_this);
        void (WINAPIV *OutOfSec)(struct CGameObject *_this);
        void (WINAPIV *SendMsg_FixPosition)(struct CGameObject *_this, int);
        void (WINAPIV *SendMsg_RealMovePoint)(struct CGameObject *_this, int);
        void (WINAPIV *SendMsg_StunInform)(struct CGameObject *_this);
        void (WINAPIV *SendMsg_SetHPInform)(struct CGameObject *_this);
        int (WINAPIV *GetHP)(struct CGameObject *_this);
        bool (WINAPIV *SetHP)(struct CGameObject *_this, int, bool);
        int (WINAPIV *GetMaxHP)(struct CGameObject *_this);
        void (WINAPIV *RecvKillMessage)(struct CGameObject *_this, struct CCharacter *);
        void (WINAPIV *SFContInsertMessage)(struct CGameObject *_this, char, char, bool, struct CPlayer *);
        void (WINAPIV *SFContDelMessage)(struct CGameObject *_this, char, char, bool, bool);
        void (WINAPIV *SFContUpdateTimeMessage)(struct CGameObject *_this, char, char, int);
        void (WINAPIV *BeTargeted)(struct CGameObject *_this, struct CCharacter *);
        bool (WINAPIV *RobbedHP)(struct CGameObject *_this, struct CCharacter *, int);
        bool (WINAPIV *FixTargetWhile)(struct CGameObject *_this, struct CCharacter *, unsigned int);
        void (WINAPIV *SetAttackPart)(struct CGameObject *_this, int);
        int (WINAPIV *GetGenAttackProb)(struct CGameObject *_this, struct CCharacter *, int, bool);
        int (WINAPIV *SetDamage)(struct CGameObject *_this, int, struct CCharacter *, int, bool, int, unsigned int, bool);
        int (WINAPIV *GetDefFC)(struct CGameObject *_this, int, struct CCharacter *, int *);
        int (WINAPIV *GetFireTol)(struct CGameObject *_this);
        int (WINAPIV *GetWaterTol)(struct CGameObject *_this);
        int (WINAPIV *GetSoilTol)(struct CGameObject *_this);
        int (WINAPIV *GetWindTol)(struct CGameObject *_this);
        float (WINAPIV *GetDefGap)(struct CGameObject *_this, int);
        float (WINAPIV *GetDefFacing)(struct CGameObject *_this, int);
        float (WINAPIV *GetWeaponAdjust)(struct CGameObject *_this);
        int (WINAPIV *GetLevel)(struct CGameObject *_this);
        int (WINAPIV *GetDefSkill)(struct CGameObject *_this, bool);
        float (WINAPIV *GetWidth)(struct CGameObject *_this);
        float (WINAPIV *GetAttackRange)(struct CGameObject *_this);
        int (WINAPIV *AttackableHeight)(struct CGameObject *_this);
        int (WINAPIV *GetWeaponClass)(struct CGameObject *_this);
        int (WINAPIV *GetAvoidRate)(struct CGameObject *_this);
        int (WINAPIV *GetAttackLevel)(struct CGameObject *_this);
        int (WINAPIV *GetAttackDP)(struct CGameObject *_this);
        int (WINAPIV *GetObjRace)(struct CGameObject *_this);
        char *(WINAPIV *GetObjName)(struct CGameObject *_this);
        bool (WINAPIV *IsRecvableContEffect)(struct CGameObject *_this);
        bool (WINAPIV *IsBeAttackedAble)(struct CGameObject *_this, bool);
        bool (WINAPIV *IsRewardExp)(struct CGameObject *_this);
        bool (WINAPIV *IsBeDamagedAble)(struct CGameObject *_this, struct CCharacter *);
        bool (WINAPIV *IsInTown)(struct CGameObject *_this);
        bool (WINAPIV *IsAttackableInTown)(struct CGameObject *_this);
        bool (WINAPIV *SF_AttHPtoDstFP_Once)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_ContDamageTimeInc_Once)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_Resurrect_Once)(struct CGameObject *_this, struct CCharacter *);
        bool (WINAPIV *SF_HPInc_Once)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_STInc_Once)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_ContHelpTimeInc_Once)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_OverHealing_Once)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_LateContHelpSkillRemove_Once)(struct CGameObject *_this, struct CCharacter *);
        bool (WINAPIV *SF_LateContHelpForceRemove_Once)(struct CGameObject *_this, struct CCharacter *);
        bool (WINAPIV *SF_LateContDamageRemove_Once)(struct CGameObject *_this, struct CCharacter *);
        bool (WINAPIV *SF_AllContHelpSkillRemove_Once)(struct CGameObject *_this, struct CCharacter *);
        bool (WINAPIV *SF_AllContHelpForceRemove_Once)(struct CGameObject *_this, struct CCharacter *);
        bool (WINAPIV *SF_AllContDamageForceRemove_Once)(struct CGameObject *_this, struct CCharacter *);
        bool (WINAPIV *SF_OthersContHelpSFRemove_Once)(struct CGameObject *_this, float);
        bool (WINAPIV *SF_SkillContHelpTimeInc_Once)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_ConvertMonsterTarget)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_TransMonsterHP)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_ReleaseMonsterTarget)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_IncHPCircleParty)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_Stun)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_SPDec)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_FPDec)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_DamageAndStun)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_TransDestHP)(struct CGameObject *_this, struct CCharacter *, float, char *);
        bool (WINAPIV *SF_RemoveAllContHelp_Once)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_MakePortalReturnBindPositionPartyMember)(struct CGameObject *_this, struct CCharacter *, float, char *);
        bool (WINAPIV *SF_ReturnBindPosition)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_IncreaseDP)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_ConvertTargetDest)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_RecoverAllReturnStateAnimusHPFull)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_MakeZeroAnimusRecallTimeOnce)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_SelfDestruction)(struct CGameObject *_this, struct CCharacter *, float);
        bool (WINAPIV *SF_TeleportToDestination)(struct CGameObject *_this, struct CCharacter *, bool);
        bool (WINAPIV *Is_Battle_Mode)(struct CGameObject *_this);
    };
END_ATF_NAMESPACE

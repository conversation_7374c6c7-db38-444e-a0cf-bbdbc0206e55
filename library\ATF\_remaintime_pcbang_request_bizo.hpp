// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _remaintime_pcbang_request_bizo
    {
        char szCMSCode[7];
        __int16 iType;
        int lRemainTime;
        _SYSTEMTIME stEndDate;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

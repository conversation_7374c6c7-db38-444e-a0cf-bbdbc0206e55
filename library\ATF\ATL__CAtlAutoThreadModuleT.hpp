// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CComApartment.hpp>
#include <ATL__CComSimpleThreadAllocator.hpp>
#include <ATL__IAtlAutoThreadModule.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        #pragma pack(push, 8)
        template<>
        struct  CAtlAutoThreadModuleT<CAtlAutoThreadModule,CComSimpleThreadAllocator,4294967295> : IAtlAutoThreadModule
        {
            unsigned int dwThreadID;
            int m_nThreads;
            CComApartment *m_pApartments;
            CComSimpleThreadAllocator m_Allocator;
        };
        #pragma pack(pop)
    }; // end namespace ATL
END_ATF_NAMESPACE

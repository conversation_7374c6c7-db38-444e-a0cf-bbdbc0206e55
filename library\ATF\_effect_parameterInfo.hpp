// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_effect_parameter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _effect_parameterAllocEffParam2_ptr = void (WINAPIV*)(struct _effect_parameter*);
        using _effect_parameterAllocEffParam2_clbk = void (WINAPIV*)(struct _effect_parameter*, _effect_parameterAllocEffParam2_ptr);
        using _effect_parameterGetEff_Have4_ptr = float (WINAPIV*)(struct _effect_parameter*, int);
        using _effect_parameterGetEff_Have4_clbk = float (WINAPIV*)(struct _effect_parameter*, int, _effect_parameterGetEff_Have4_ptr);
        using _effect_parameterGetEff_Plus6_ptr = float (WINAPIV*)(struct _effect_parameter*, int);
        using _effect_parameterGetEff_Plus6_clbk = float (WINAPIV*)(struct _effect_parameter*, int, _effect_parameterGetEff_Plus6_ptr);
        using _effect_parameterGetEff_Rate8_ptr = float (WINAPIV*)(struct _effect_parameter*, int);
        using _effect_parameterGetEff_Rate8_clbk = float (WINAPIV*)(struct _effect_parameter*, int, _effect_parameterGetEff_Rate8_ptr);
        using _effect_parameterGetEff_State10_ptr = bool (WINAPIV*)(struct _effect_parameter*, int);
        using _effect_parameterGetEff_State10_clbk = bool (WINAPIV*)(struct _effect_parameter*, int, _effect_parameterGetEff_State10_ptr);
        using _effect_parameterInitEffHave12_ptr = void (WINAPIV*)(struct _effect_parameter*);
        using _effect_parameterInitEffHave12_clbk = void (WINAPIV*)(struct _effect_parameter*, _effect_parameterInitEffHave12_ptr);
        using _effect_parameterInitEffParam14_ptr = void (WINAPIV*)(struct _effect_parameter*);
        using _effect_parameterInitEffParam14_clbk = void (WINAPIV*)(struct _effect_parameter*, _effect_parameterInitEffParam14_ptr);
        using _effect_parameterSetEff_Plus16_ptr = bool (WINAPIV*)(struct _effect_parameter*, int, float, bool);
        using _effect_parameterSetEff_Plus16_clbk = bool (WINAPIV*)(struct _effect_parameter*, int, float, bool, _effect_parameterSetEff_Plus16_ptr);
        using _effect_parameterSetEff_Rate18_ptr = bool (WINAPIV*)(struct _effect_parameter*, int, float, bool);
        using _effect_parameterSetEff_Rate18_clbk = bool (WINAPIV*)(struct _effect_parameter*, int, float, bool, _effect_parameterSetEff_Rate18_ptr);
        using _effect_parameterSetEff_State20_ptr = bool (WINAPIV*)(struct _effect_parameter*, int, bool);
        using _effect_parameterSetEff_State20_clbk = bool (WINAPIV*)(struct _effect_parameter*, int, bool, _effect_parameterSetEff_State20_ptr);
        using _effect_parameterSetLock22_ptr = void (WINAPIV*)(struct _effect_parameter*, bool);
        using _effect_parameterSetLock22_clbk = void (WINAPIV*)(struct _effect_parameter*, bool, _effect_parameterSetLock22_ptr);
        
        using _effect_parameterctor__effect_parameter24_ptr = void (WINAPIV*)(struct _effect_parameter*);
        using _effect_parameterctor__effect_parameter24_clbk = void (WINAPIV*)(struct _effect_parameter*, _effect_parameterctor__effect_parameter24_ptr);
        
        using _effect_parameterdtor__effect_parameter26_ptr = void (WINAPIV*)(struct _effect_parameter*);
        using _effect_parameterdtor__effect_parameter26_clbk = void (WINAPIV*)(struct _effect_parameter*, _effect_parameterdtor__effect_parameter26_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagLOGFONTA.hpp>


START_ATF_NAMESPACE
    struct tagNONCLIENTMETRICSA
    {
        unsigned int cbSize;
        int iBorderWidth;
        int iScrollWidth;
        int iScrollHeight;
        int iCaptionWidth;
        int iCaptionHeight;
        tagLOGFONTA lfCaptionFont;
        int iSmCaptionWidth;
        int iSmCaptionHeight;
        tagLOGFONTA lfSmCaptionFont;
        int iMenuWidth;
        int iMenuHeight;
        tagLOGFONTA lfMenuFont;
        tagLOGFONTA lfStatusFont;
        tagLOGFONTA lfMessageFont;
    };
END_ATF_NAMESPACE

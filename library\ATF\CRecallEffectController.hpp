// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>
#include <CMyTimer.hpp>
#include <CNetIndexList.hpp>
#include <CPlayer.hpp>
#include <CRecallRequest.hpp>


START_ATF_NAMESPACE
    struct CRecallEffectController
    {
        unsigned int m_uiInfoTotCnt;
        CRecallRequest **m_ppkReqeust;
        CMyTimer *m_pkTimer;
        CNetIndexList *m_pkEmptyInxList;
        CNetIndexList *m_pkUseInxList;
    public:
        CRecallEffectController();
        void ctor_CRecallEffectController();
        void CleanUp();
        void Close(struct CRecallRequest* pkRequest, bool bDone);
        void DecideRecall(uint16_t dwRequestID, char byAgree, struct CPlayer* pkObj);
        static void Destroy();
        struct CRecallRequest* GetEmpty();
        char GetResistedRecall(uint16_t wID, struct CRecallRequest** pkRequest);
        bool Init(unsigned int uiSize);
        static struct CRecallEffectController* Instance();
        void OnLoop();
        char ProcessRequestRecall(struct CPlayer* pkPerformer, struct CCharacter* pkDest, struct CRecallRequest** pkRequest, bool bRecallParty, bool bStone, bool bBattleModeUse);
        bool RequestRecall(struct CPlayer* pkPerformer, struct CCharacter* pkDest, bool bRecallParty, bool bStone, bool bBattleModeUse);
        void SendDecideRecallErrorResultToDest(char byErr, struct CPlayer* pkDest, int nCallerMapCode);
        void SendRecallReqeustResult(char byRet, struct CPlayer* pkObj);
        void SendRecallReqeustToDest(uint16_t wRequestID, struct CPlayer* pkPerformer, struct CPlayer* pkDest);
        void UpdateClose();
        ~CRecallEffectController();
        void dtor_CRecallEffectController();
    };
END_ATF_NAMESPACE

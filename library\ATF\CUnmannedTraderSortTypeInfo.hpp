// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSortType.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderSortTypector_CUnmannedTraderSortType2_ptr = void (WINAPIV*)(struct CUnmannedTraderSortType*, unsigned int);
        using CUnmannedTraderSortTypector_CUnmannedTraderSortType2_clbk = void (WINAPIV*)(struct CUnmannedTraderSortType*, unsigned int, CUnmannedTraderSortTypector_CUnmannedTraderSortType2_ptr);
        using CUnmannedTraderSortTypeGetID4_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderSortType*);
        using CUnmannedTraderSortTypeGetID4_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderSortType*, CUnmannedTraderSortTypeGetID4_ptr);
        using CUnmannedTraderSortTypeGetQuery6_ptr = char* (WINAPIV*)(struct CUnmannedTraderSortType*);
        using CUnmannedTraderSortTypeGetQuery6_clbk = char* (WINAPIV*)(struct CUnmannedTraderSortType*, CUnmannedTraderSortTypeGetQuery6_ptr);
        using CUnmannedTraderSortTypeLoadXML8_ptr = bool (WINAPIV*)(struct CUnmannedTraderSortType*, struct TiXmlElement*, struct CLogFile*, unsigned int);
        using CUnmannedTraderSortTypeLoadXML8_clbk = bool (WINAPIV*)(struct CUnmannedTraderSortType*, struct TiXmlElement*, struct CLogFile*, unsigned int, CUnmannedTraderSortTypeLoadXML8_ptr);
        
        using CUnmannedTraderSortTypedtor_CUnmannedTraderSortType14_ptr = void (WINAPIV*)(struct CUnmannedTraderSortType*);
        using CUnmannedTraderSortTypedtor_CUnmannedTraderSortType14_clbk = void (WINAPIV*)(struct CUnmannedTraderSortType*, CUnmannedTraderSortTypedtor_CUnmannedTraderSortType14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

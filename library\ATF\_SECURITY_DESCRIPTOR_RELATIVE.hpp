// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _SECURITY_DESCRIPTOR_RELATIVE
    {
        char Revision;
        char Sbz1;
        unsigned __int16 Control;
        unsigned int Owner;
        unsigned int Group;
        unsigned int Sacl;
        unsigned int Dacl;
    };
END_ATF_NAMESPACE

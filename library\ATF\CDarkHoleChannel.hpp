// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDarkHoleChannelVtbl.hpp>
#include <CIndexList.hpp>
#include <_dh_mission_mgr.hpp>
#include <_dh_player_mgr.hpp>
#include <EM_DH_EVENT.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CDarkHoleChannel
    {
        struct __enter_member
        {
            bool bActive;
            bool bDisnormalClose;
            unsigned int dwDisconnectTime;
        public:
            __enter_member(bool active, bool disnormal, unsigned int time);
            void ctor___enter_member(bool active, bool disnormal, unsigned int time);
            __enter_member();
            void ctor___enter_member();
        };
        CDarkHoleChannelVtbl *vfptr;
        unsigned __int16 m_wChannelIndex;
        unsigned int m_dwChannelSerial;
        struct CDarkHole *m_pHoleObj;
        unsigned int m_dwHoleSerial;
        unsigned int m_dwQuestStartTime;
        struct _dh_quest_setup *m_pQuestSetup;
        unsigned __int16 m_wLayerIndex;
        struct _LAYER_SET *m_pLayerSet;
        _dh_mission_mgr m_MissionMgr;
        char m_wszOpenerName[17];
        char m_aszOpenerName[17];
        unsigned int m_dwOpenerSerial;
        int m_nOpenerDegree;
        int m_nOpenerSubDegree;
        bool m_bCheckMemberClose;
        struct CPartyPlayer *m_pPartyMng;
        _dh_player_mgr m_Quester[32];
        _dh_player_mgr *m_pLeaderPtr;
        unsigned int m_dwEnterOrderCounter;
        unsigned int m_dwNextCloseTime;
        unsigned int m_dwSendNewMissionMsgNextTime;
        CIndexList m_listEnterMember;
        bool m_bMoveNextMission;
    public:
        void AddMonster();
        CDarkHoleChannel();
        void ctor_CDarkHoleChannel();
        bool CanYouEnterHole(struct CPlayer* pEnter);
        void ChangeMonster();
        void ChangeMonsterApparition(int nTermMSec);
        void CheckCurrentMission();
        bool CheckEvent(EM_DH_EVENT eventType, int nContentTable, int nContentIndex, int nCount, struct CGameObject* pObj);
        void CheckInnerEventDummy();
        void CheckMember();
        void CheckRespawnMonster();
        void CheckSendNewMissionMsg();
        void CheckWaitNextMission();
        bool ClearMember(struct CPlayer* pMember, bool bDisconnect, struct _dh_player_mgr::_pos* poutPlayerPos);
        void CloseDungeon();
        void CreateMonster();
        int GetAllMemberNum();
        int GetCurrentMemberNum();
        bool GetEnterNewPos(struct _ENTER_DUNGEON_NEW_POS* pNewPos);
        unsigned int GetLeaderSerial();
        int GetMonsterNumInCurMissionArea(int nMonsterRecIndex);
        struct _dh_player_mgr* GetPlayerInfo(unsigned int dwSerial);
        bool GotoNextMission();
        bool GotoNextMissionByPosition(float* pfStartPos);
        void Init();
        bool IsAllMemberNearPosition(float* pfCenterPos, int nLen);
        bool IsFill();
        bool IsMoveNextMission(int nPortalIndex);
        bool IsOpenPartyMember(struct CPlayer* pOpener);
        bool IsReEnterable(unsigned int dwEnterSerial);
        void NextMissionOtherQuester(struct CPlayer* pLeader, struct _dh_mission_setup* pNextMission);
        void OnLoop();
        void OpenDungeon(struct _dh_quest_setup* pQuestSetup, int nLayerIndex, struct CPlayer* pOpener, struct CDarkHole* pHoleObj);
        bool PushMember(struct CPlayer* pMember, bool bReconnect, struct CMapData* pOldMap, uint16_t wLastLayer, float* pfOldPos);
        struct _dh_mission_setup* SearchMissionFromPos(float* pfStartPos);
        void SendMsg_AskReEnter(struct CPlayer* pDst);
        void SendMsg_ChannelClose();
        void SendMsg_GateDestroy(char* byType, char* pSend, int nSize);
        void SendMsg_JobCount(int nJobIndex, int nCount);
        void SendMsg_JobPass(int nJobIndex);
        void SendMsg_LeaderChange(struct CPlayer* pNewLeader);
        void SendMsg_MemberInfo(struct CPlayer* pDst);
        void SendMsg_MissionInfo(struct CPlayer* pDst);
        void SendMsg_MissionPass();
        void SendMsg_NewMember(struct CPlayer* pNewMember, bool bReconnect);
        void SendMsg_NewMission();
        void SendMsg_OpenPortalByReact(int nPortalIndex);
        void SendMsg_OpenPortalByResult(int nPortalIndex);
        void SendMsg_PopMember(struct CPlayer* pPopMember, bool bDisconnect);
        void SendMsg_QuestInfo(struct CPlayer* pDst);
        void SendMsg_QuestPass();
        void SendMsg_RealAddLimTime(int nAddSec, char* pMsg);
        void SendMsg_RealMsgInform(char* pMsg);
        void SendMsg_TimeOut();
        void ShareItemToMonster();
        void WaitNextMission();
        bool _Reward();
        ~CDarkHoleChannel();
        void dtor_CDarkHoleChannel();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CDarkHoleChannel, 70928>(), "CDarkHoleChannel");
END_ATF_NAMESPACE

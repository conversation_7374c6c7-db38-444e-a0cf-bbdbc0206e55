// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _PCBANG_FAVOR_ITEM_DB_BASE
    {
        unsigned __int64 lnUID[50];
    public:
        void Init();
        bool InsertItem(struct _STORAGE_LIST::_db_con* Item);
        bool IsDeleteItem(struct _STORAGE_LIST::_db_con* Item);
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_PCBANG_FAVOR_ITEM_DB_BASE, 400>(), "_PCBANG_FAVOR_ITEM_DB_BASE");
END_ATF_NAMESPACE

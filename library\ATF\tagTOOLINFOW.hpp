// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <HWND__.hpp>
#include <tagRECT.hpp>



START_ATF_NAMESPACE
    struct tagTOOLINFOW
    {
        unsigned int cbSize;
        unsigned int uFlags;
        HWND__ *hwnd;
        unsigned __int64 uId;
        tagRECT rect;
        HINSTANCE__ *hinst;
        wchar_t *lpszText;
        __int64 lParam;
        void *lpReserved;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace std
    {
        struct _Container_base
        {
            struct _Iterator_base *_Myfirstiter;
        public:
            _Container_base(struct _Container_base* arg_0);
            int64_t ctor__Container_base(struct _Container_base* arg_0);
            _Container_base();
            int64_t ctor__Container_base();
        };    
        static_assert(ATF::checkSize<std::_Container_base, 8>(), "std::_Container_base");
    }; // end namespace std
END_ATF_NAMESPACE

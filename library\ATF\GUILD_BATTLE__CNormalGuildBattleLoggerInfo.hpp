// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleLogger.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleLoggerctor_CNormalGuildBattleLogger2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*);
            using GUILD_BATTLE__CNormalGuildBattleLoggerctor_CNormalGuildBattleLogger2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*, GUILD_BATTLE__CNormalGuildBattleLoggerctor_CNormalGuildBattleLogger2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleLoggerCreateLogFile4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*, char*);
            using GUILD_BATTLE__CNormalGuildBattleLoggerCreateLogFile4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*, char*, GUILD_BATTLE__CNormalGuildBattleLoggerCreateLogFile4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleLoggerInit6_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*);
            using GUILD_BATTLE__CNormalGuildBattleLoggerInit6_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*, GUILD_BATTLE__CNormalGuildBattleLoggerInit6_ptr);
            using GUILD_BATTLE__CNormalGuildBattleLoggerLog8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*, char*);
            using GUILD_BATTLE__CNormalGuildBattleLoggerLog8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*, char*, GUILD_BATTLE__CNormalGuildBattleLoggerLog8_ptr);
            using GUILD_BATTLE__CNormalGuildBattleLoggerLog10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*, wchar_t*);
            using GUILD_BATTLE__CNormalGuildBattleLoggerLog10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*, wchar_t*, GUILD_BATTLE__CNormalGuildBattleLoggerLog10_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleLoggerdtor_CNormalGuildBattleLogger12_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*);
            using GUILD_BATTLE__CNormalGuildBattleLoggerdtor_CNormalGuildBattleLogger12_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*, GUILD_BATTLE__CNormalGuildBattleLoggerdtor_CNormalGuildBattleLogger12_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

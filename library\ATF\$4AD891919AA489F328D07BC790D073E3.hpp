// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FOCUS_EVENT_RECORD.hpp>
#include <_KEY_EVENT_RECORD.hpp>
#include <_MENU_EVENT_RECORD.hpp>
#include <_MOUSE_EVENT_RECORD.hpp>
#include <_WINDOW_BUFFER_SIZE_RECORD.hpp>


START_ATF_NAMESPACE
    union $4AD891919AA489F328D07BC790D073E3
    {
        _KEY_EVENT_RECORD KeyEvent;
        _MOUSE_EVENT_RECORD MouseEvent;
        _WINDOW_BUFFER_SIZE_RECORD WindowBufferSizeEvent;
        _MENU_EVENT_RECORD MenuEvent;
        _FOCUS_EVENT_RECORD FocusEvent;
    };
END_ATF_NAMESPACE

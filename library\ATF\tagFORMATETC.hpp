// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagDVTARGETDEVICE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagFORMATETC
    {
        unsigned __int16 cfFormat;
        tagDVTARGETDEVICE *ptd;
        unsigned int dwAspect;
        int lindex;
        unsigned int tymed;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBattleTournamentInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CBattleTournamentInfoctor_CBattleTournamentInfo2_ptr = void (WINAPIV*)(struct CBattleTournamentInfo*);
        using CBattleTournamentInfoctor_CBattleTournamentInfo2_clbk = void (WINAPIV*)(struct CBattleTournamentInfo*, CBattleTournamentInfoctor_CBattleTournamentInfo2_ptr);
        using CBattleTournamentInfoGetWinnerGrade4_ptr = char (WINAPIV*)(struct CBattleTournamentInfo*, unsigned int, char*);
        using CBattleTournamentInfoGetWinnerGrade4_clbk = char (WINAPIV*)(struct CBattleTournamentInfo*, unsigned int, char*, CBattleTournamentInfoGetWinnerGrade4_ptr);
        using CBattleTournamentInfoInit6_ptr = void (WINAPIV*)(struct CBattleTournamentInfo*);
        using CBattleTournamentInfoInit6_clbk = void (WINAPIV*)(struct CBattleTournamentInfo*, CBattleTournamentInfoInit6_ptr);
        using CBattleTournamentInfoSetLoad8_ptr = void (WINAPIV*)(struct CBattleTournamentInfo*, bool);
        using CBattleTournamentInfoSetLoad8_clbk = void (WINAPIV*)(struct CBattleTournamentInfo*, bool, CBattleTournamentInfoSetLoad8_ptr);
        using CBattleTournamentInfoSetWinnerInfo10_ptr = bool (WINAPIV*)(struct CBattleTournamentInfo*, unsigned int, char*, char);
        using CBattleTournamentInfoSetWinnerInfo10_clbk = bool (WINAPIV*)(struct CBattleTournamentInfo*, unsigned int, char*, char, CBattleTournamentInfoSetWinnerInfo10_ptr);
        
        using CBattleTournamentInfodtor_CBattleTournamentInfo12_ptr = void (WINAPIV*)(struct CBattleTournamentInfo*);
        using CBattleTournamentInfodtor_CBattleTournamentInfo12_clbk = void (WINAPIV*)(struct CBattleTournamentInfo*, CBattleTournamentInfodtor_CBattleTournamentInfo12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

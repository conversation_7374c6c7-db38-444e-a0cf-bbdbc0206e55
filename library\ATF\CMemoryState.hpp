// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CrtMemState.hpp>


START_ATF_NAMESPACE
    struct CMemoryState
    {
        enum blockUsage
        {
            freeBlock = 0x0,
            objectBlock = 0x1,
            bitBlock = 0x2,
            crtBlock = 0x3,
            ignoredBlock = 0x4,
            nBlockUseMax = 0x5,
        };
        _CrtMemState m_memState;
        __int64 m_lCounts[5];
        __int64 m_lSizes[5];
        __int64 m_lHighWaterCount;
        __int64 m_lTotalCount;
    };
END_ATF_NAMESPACE

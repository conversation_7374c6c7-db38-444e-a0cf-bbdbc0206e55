# Loot Exchange INI Configuration Implementation Guide

## Overview
This guide explains how to migrate the Loot Exchange addon from JSON configuration in `global.json` to a dedicated INI file configuration system.

## Changes Made

### 1. Removed from global.json
The loot exchange configuration has been completely removed from `YorozuyaGS/Configuration/global.json`:

```json
// REMOVED:
{
  "name": "addon.loot_exchange",
  "config": {
    "activated": false,
    "exchange_all": false
  }
}
```

### 2. Created LootExchange.ini
A comprehensive INI configuration file has been created at `YorozuyaGS/Configuration/LootExchange.ini` with extensive options:

#### Main Settings
- **Activated**: Enable/disable the addon
- **ExchangeAll**: Exchange all items vs. processing points only
- **MoneyTypePriority**: Configurable priority order for currency types
- **MinimumValue**: Minimum item value threshold
- **MaxPickupDistance**: Maximum distance for loot exchange
- **EnableLogging**: Debug logging functionality

#### Item Type Controls
- **ExchangeEquipment**: Armor, weapons, shields, etc.
- **ExchangeConsumables**: Potions, bullets, etc.
- **ExchangeResources**: Ores and materials
- **ExchangeSpecialItems**: Maps, town items, etc.
- **ExchangeForceItems**: Force-related items
- **ExchangeAccessories**: Rings and amulets

#### Currency Controls
Individual enable/disable for each currency type:
- CP (Dalant), Gold, PvP Points, Processing Points, etc.

#### Advanced Features
- Race-specific pricing for resources
- Exchange rate modifiers
- Premium player restrictions
- Level requirements
- Daily exchange limits

### 3. Enhanced Code Implementation
The addon code has been completely rewritten to:
- Load configuration from INI file on startup
- Support all new configuration options
- Provide extensive logging capabilities
- Include validation and error checking

## Implementation Steps

### Step 1: File Replacement
```bash
# Backup original files
cp Addons/LootExchange/LootExchange.h Addons/LootExchange/LootExchange.h.backup
cp Addons/LootExchange/LootExchange.cpp Addons/LootExchange/LootExchange.cpp.backup

# Replace header file with updated version (add new static members)
# Replace implementation with INI-based version
```

### Step 2: Combine Implementation Files
The implementation is split into two files due to size. Combine them:

```cpp
// Copy content from LootExchange_INI.cpp (main implementation)
// Append content from LootExchange_INI_Part2.cpp (helper functions)
// Save as new LootExchange.cpp
```

### Step 3: Configuration Setup
1. Place `LootExchange.ini` in `YorozuyaGS/Configuration/`
2. Adjust settings as needed for your server
3. Ensure the addon is removed from `global.json`

## Configuration Examples

### Basic Setup (Conservative)
```ini
[LootExchange]
Activated=1
ExchangeAll=0
MinimumValue=0
MaxPickupDistance=100
EnableLogging=0
```

### Advanced Setup (Full Features)
```ini
[LootExchange]
Activated=1
ExchangeAll=1
MoneyTypePriority=0,1,2,3,4,5,6
MinimumValue=100
MaxPickupDistance=150
EnableLogging=1
LogFilePath=./YorozuyaGS/Logs/LootExchange.log

[ItemTypeSettings]
ExchangeEquipment=1
ExchangeConsumables=1
ExchangeResources=1
ExchangeSpecialItems=0
ExchangeForceItems=1
ExchangeAccessories=1

[CurrencySettings]
EnableCP=1
EnableGold=1
EnablePvPPoint=0
EnablePvPCashBag=0
EnableProcessingPoint=1
EnableHunterPoint=1
EnableGoldPoint=1

[AdvancedSettings]
ExchangeRateModifier=150
PremiumPlayersOnly=0
MinimumPlayerLevel=10
DailyExchangeLimit=1000
```

### Premium Server Setup
```ini
[LootExchange]
Activated=1
ExchangeAll=1
EnableLogging=1

[AdvancedSettings]
ExchangeRateModifier=200
PremiumPlayersOnly=1
MinimumPlayerLevel=30
DailyExchangeLimit=500
DailyResetTime=06:00
```

## Benefits of INI Configuration

### 1. **Separation of Concerns**
- Loot exchange settings are isolated from core server config
- Easier to manage and backup specific addon settings
- Reduces clutter in main configuration file

### 2. **Enhanced Flexibility**
- Granular control over item types and currencies
- Advanced features like rate modifiers and restrictions
- Easy to enable/disable specific features

### 3. **Better Administration**
- Human-readable configuration format
- Comments and documentation within the file
- Easy to share configurations between servers

### 4. **Debugging Support**
- Built-in logging system
- Detailed exchange tracking
- Configuration validation

## Troubleshooting

### Common Issues

1. **Addon Not Loading**
   - Ensure INI file exists in correct location
   - Check file permissions
   - Verify INI syntax is correct

2. **Items Not Exchanging**
   - Check `Activated=1` in INI file
   - Verify item type settings allow the item
   - Check currency type settings
   - Review minimum value threshold

3. **Wrong Currency Type**
   - Check `MoneyTypePriority` setting
   - Verify currency is enabled in `[CurrencySettings]`
   - Review item's money type flags

### Debug Steps

1. **Enable Logging**
   ```ini
   [LootExchange]
   EnableLogging=1
   LogFilePath=./YorozuyaGS/Logs/LootExchange.log
   ```

2. **Check Log File**
   - Configuration loading messages
   - Exchange transaction logs
   - Error messages and warnings

3. **Test Configuration**
   - Start with basic settings
   - Gradually enable advanced features
   - Test with different item types

## Migration Checklist

- [ ] Remove loot exchange from `global.json`
- [ ] Create `LootExchange.ini` configuration file
- [ ] Update `LootExchange.h` with new static members
- [ ] Replace `LootExchange.cpp` with INI-based implementation
- [ ] Rebuild the LootExchange project
- [ ] Test basic functionality
- [ ] Configure advanced settings as needed
- [ ] Enable logging for monitoring
- [ ] Document server-specific settings

## Performance Considerations

- INI file is loaded once at startup (no runtime performance impact)
- Logging can be disabled for production servers
- Configuration validation happens at load time
- Memory usage is minimal for additional settings

This implementation provides a much more flexible and maintainable configuration system for the Loot Exchange addon while maintaining backward compatibility and adding powerful new features.

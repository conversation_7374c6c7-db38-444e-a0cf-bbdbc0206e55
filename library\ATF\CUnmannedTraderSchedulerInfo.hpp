// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderScheduler.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderSchedulerctor_CUnmannedTraderScheduler2_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerctor_CUnmannedTraderScheduler2_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerctor_CUnmannedTraderScheduler2_ptr);
        using CUnmannedTraderSchedulerCheatPushLoad4_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerCheatPushLoad4_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerCheatPushLoad4_ptr);
        using CUnmannedTraderSchedulerClearAll6_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerClearAll6_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerClearAll6_ptr);
        using CUnmannedTraderSchedulerCompleteClear8_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*, char, char, char, unsigned int);
        using CUnmannedTraderSchedulerCompleteClear8_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, char, char, char, unsigned int, CUnmannedTraderSchedulerCompleteClear8_ptr);
        using CUnmannedTraderSchedulerDestroy10_ptr = void (WINAPIV*)();
        using CUnmannedTraderSchedulerDestroy10_clbk = void (WINAPIV*)(CUnmannedTraderSchedulerDestroy10_ptr);
        using CUnmannedTraderSchedulerDoDayChangedWork12_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerDoDayChangedWork12_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerDoDayChangedWork12_ptr);
        using CUnmannedTraderSchedulerFindItem14_ptr = struct std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> >* (WINAPIV*)(struct CUnmannedTraderScheduler*, struct std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> >*, char, unsigned int);
        using CUnmannedTraderSchedulerFindItem14_clbk = struct std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> >* (WINAPIV*)(struct CUnmannedTraderScheduler*, struct std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> >*, char, unsigned int, CUnmannedTraderSchedulerFindItem14_ptr);
        using CUnmannedTraderSchedulerFindWaitItem16_ptr = bool (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerFindWaitItem16_clbk = bool (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerFindWaitItem16_ptr);
        using CUnmannedTraderSchedulerInit18_ptr = bool (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerInit18_clbk = bool (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerInit18_ptr);
        using CUnmannedTraderSchedulerInstance20_ptr = struct CUnmannedTraderScheduler* (WINAPIV*)();
        using CUnmannedTraderSchedulerInstance20_clbk = struct CUnmannedTraderScheduler* (WINAPIV*)(CUnmannedTraderSchedulerInstance20_ptr);
        using CUnmannedTraderSchedulerLoad22_ptr = bool (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerLoad22_clbk = bool (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerLoad22_ptr);
        using CUnmannedTraderSchedulerLog24_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*, char*);
        using CUnmannedTraderSchedulerLog24_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, char*, CUnmannedTraderSchedulerLog24_ptr);
        using CUnmannedTraderSchedulerLoop26_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerLoop26_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerLoop26_ptr);
        using CUnmannedTraderSchedulerPushLoad28_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerPushLoad28_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerPushLoad28_ptr);
        using CUnmannedTraderSchedulerSetLogger30_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*, struct CLogFile*);
        using CUnmannedTraderSchedulerSetLogger30_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, struct CLogFile*, CUnmannedTraderSchedulerSetLogger30_ptr);
        using CUnmannedTraderSchedulerUpdate32_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*, struct _unmannedtrader_reserved_schedule_info*);
        using CUnmannedTraderSchedulerUpdate32_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, struct _unmannedtrader_reserved_schedule_info*, CUnmannedTraderSchedulerUpdate32_ptr);
        
        using CUnmannedTraderSchedulerdtor_CUnmannedTraderScheduler36_ptr = void (WINAPIV*)(struct CUnmannedTraderScheduler*);
        using CUnmannedTraderSchedulerdtor_CUnmannedTraderScheduler36_clbk = void (WINAPIV*)(struct CUnmannedTraderScheduler*, CUnmannedTraderSchedulerdtor_CUnmannedTraderScheduler36_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

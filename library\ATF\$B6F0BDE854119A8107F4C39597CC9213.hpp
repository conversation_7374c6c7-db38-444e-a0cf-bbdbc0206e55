// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $B6F0BDE854119A8107F4C39597CC9213
    {
      VERIFY_NONE = 0x0,
      VERIFY_FIRST_CHECK_REQUEST = 0x1,
      VERIFY_FIRST_CHECK_COMPLETE = 0x2,
      VERIFY_CHECK_REQUEST = 0x3,
      VERIFY_CHECK_COMPLETE = 0x4,
    };
END_ATF_NAMESPACE

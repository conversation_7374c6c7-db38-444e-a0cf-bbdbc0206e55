// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum Cmd
    {
      _eRequestInitCandidate = 0x0,
      _eReqUpdateWinCount = 0x1,
      _eRequestCandidateList = 0x2,
      _eRegCandidate = 0x3,
      _eReg2ndCandidate = 0x4,
      _eMakeVotePaper = 0x5,
      _eSendVotePaper = 0x6,
      _eVote = 0x7,
      _eVoteScore = 0x8,
      _eReqQryFinalDecision = 0x9,
      _eReqQrySetWinner = 0xA,
      _eReqQryFinalApplyer = 0xB,
      _eRetQryFinalDecision = 0xC,
      _eReqNetFinalDecision = 0xD,
      _eQryAppoint = 0xE,
      _eReqAppoint = 0xF,
      _eRespAppoint = 0x10,
      _eReqDischarge = 0x11,
      _eReqPatriarchInform = 0x12,
      _eNon = 0x13,
    };
END_ATF_NAMESPACE

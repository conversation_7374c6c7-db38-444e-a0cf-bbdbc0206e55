// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_post_list_regi.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_post_list_regictor__qry_case_post_list_regi2_ptr = void (WINAPIV*)(struct _qry_case_post_list_regi*);
        using _qry_case_post_list_regictor__qry_case_post_list_regi2_clbk = void (WINAPIV*)(struct _qry_case_post_list_regi*, _qry_case_post_list_regictor__qry_case_post_list_regi2_ptr);
        using _qry_case_post_list_regisize4_ptr = int (WINAPIV*)(struct _qry_case_post_list_regi*);
        using _qry_case_post_list_regisize4_clbk = int (WINAPIV*)(struct _qry_case_post_list_regi*, _qry_case_post_list_regisize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

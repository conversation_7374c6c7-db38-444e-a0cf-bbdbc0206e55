// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ct_change_mastery
    {
        namespace __l7
        {
            struct _eq_suk_list
            {
                char *pwszEpSuk;
                int nCode;
                int nIndex;
            };    
            static_assert(ATF::checkSize<ct_change_mastery::__l7::_eq_suk_list, 16>(), "ct_change_mastery::__l7::_eq_suk_list");
        }; // end namespace __l7
    }; // end namespace ct_change_mastery
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_golden_box_item_ini.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _golden_box_item_inictor__golden_box_item_ini2_ptr = void (WINAPIV*)(struct _golden_box_item_ini*);
        using _golden_box_item_inictor__golden_box_item_ini2_clbk = void (WINAPIV*)(struct _golden_box_item_ini*, _golden_box_item_inictor__golden_box_item_ini2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Iterator_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<typename _Ty, typename _N, typename _TyPtr = _Ty* const, typename _TyAddr = _Ty& const>
        struct  _Bidit : _Iterator_base
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE

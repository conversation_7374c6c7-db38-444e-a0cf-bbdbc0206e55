// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CEngNetworkBillEX.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CEngNetworkBillEXArrangeString2_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, char);
        using CEngNetworkBillEXArrangeString2_clbk = void (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, char, CEngNetworkBillEXArrangeString2_ptr);
        
        using CEngNetworkBillEXctor_CEngNetworkBillEX4_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*);
        using CEngNetworkBillEXctor_CEngNetworkBillEX4_clbk = void (WINAPIV*)(struct CEngNetworkBillEX*, CEngNetworkBillEXctor_CEngNetworkBillEX4_ptr);
        using CEngNetworkBillEXCalcStreamSize6_ptr = uint16_t (WINAPIV*)(struct CEngNetworkBillEX*, char*);
        using CEngNetworkBillEXCalcStreamSize6_clbk = uint16_t (WINAPIV*)(struct CEngNetworkBillEX*, char*, CEngNetworkBillEXCalcStreamSize6_ptr);
        using CEngNetworkBillEXConnectToNcash8_ptr = int (WINAPIV*)(struct CEngNetworkBillEX*);
        using CEngNetworkBillEXConnectToNcash8_clbk = int (WINAPIV*)(struct CEngNetworkBillEX*, CEngNetworkBillEXConnectToNcash8_ptr);
        using CEngNetworkBillEXFreeVectorData10_ptr = bool (WINAPIV*)(struct CEngNetworkBillEX*, unsigned int);
        using CEngNetworkBillEXFreeVectorData10_clbk = bool (WINAPIV*)(struct CEngNetworkBillEX*, unsigned int, CEngNetworkBillEXFreeVectorData10_ptr);
        using CEngNetworkBillEXInitialize12_ptr = bool (WINAPIV*)(struct CEngNetworkBillEX*);
        using CEngNetworkBillEXInitialize12_clbk = bool (WINAPIV*)(struct CEngNetworkBillEX*, CEngNetworkBillEXInitialize12_ptr);
        using CEngNetworkBillEXIsGetConnected14_ptr = bool (WINAPIV*)(struct CEngNetworkBillEX*);
        using CEngNetworkBillEXIsGetConnected14_clbk = bool (WINAPIV*)(struct CEngNetworkBillEX*, CEngNetworkBillEXIsGetConnected14_ptr);
        using CEngNetworkBillEXLoadINIFile16_ptr = int (WINAPIV*)(struct CEngNetworkBillEX*);
        using CEngNetworkBillEXLoadINIFile16_clbk = int (WINAPIV*)(struct CEngNetworkBillEX*, CEngNetworkBillEXLoadINIFile16_ptr);
        using CEngNetworkBillEXParsingBuyItem18_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*, struct Request_Buy_Item*, char*);
        using CEngNetworkBillEXParsingBuyItem18_clbk = void (WINAPIV*)(struct CEngNetworkBillEX*, struct Request_Buy_Item*, char*, CEngNetworkBillEXParsingBuyItem18_ptr);
        using CEngNetworkBillEXParsingRemainCash20_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*, struct Request_Remain_Cash*, char*);
        using CEngNetworkBillEXParsingRemainCash20_clbk = void (WINAPIV*)(struct CEngNetworkBillEX*, struct Request_Remain_Cash*, char*, CEngNetworkBillEXParsingRemainCash20_ptr);
        using CEngNetworkBillEXParsingSeqNumber22_ptr = unsigned int (WINAPIV*)(struct CEngNetworkBillEX*, char*);
        using CEngNetworkBillEXParsingSeqNumber22_clbk = unsigned int (WINAPIV*)(struct CEngNetworkBillEX*, char*, CEngNetworkBillEXParsingSeqNumber22_ptr);
        using CEngNetworkBillEXReInitialize24_ptr = bool (WINAPIV*)(struct CEngNetworkBillEX*);
        using CEngNetworkBillEXReInitialize24_clbk = bool (WINAPIV*)(struct CEngNetworkBillEX*, CEngNetworkBillEXReInitialize24_ptr);
        using CEngNetworkBillEXSend26_ptr = int (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, uint16_t);
        using CEngNetworkBillEXSend26_clbk = int (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, uint16_t, CEngNetworkBillEXSend26_ptr);
        using CEngNetworkBillEXdhExtractSubString31_ptr = char* (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, char);
        using CEngNetworkBillEXdhExtractSubString31_clbk = char* (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, char, CEngNetworkBillEXdhExtractSubString31_ptr);
        using CEngNetworkBillEXdhRExtractSubString33_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, char);
        using CEngNetworkBillEXdhRExtractSubString33_clbk = void (WINAPIV*)(struct CEngNetworkBillEX*, char*, char*, char, CEngNetworkBillEXdhRExtractSubString33_ptr);
        using CEngNetworkBillEXs_DataAnalysis35_ptr = bool (WINAPIV*)(unsigned int, unsigned int, struct _MSG_HEADER*, char*);
        using CEngNetworkBillEXs_DataAnalysis35_clbk = bool (WINAPIV*)(unsigned int, unsigned int, struct _MSG_HEADER*, char*, CEngNetworkBillEXs_DataAnalysis35_ptr);
        
        using CEngNetworkBillEXdtor_CEngNetworkBillEX37_ptr = void (WINAPIV*)(struct CEngNetworkBillEX*);
        using CEngNetworkBillEXdtor_CEngNetworkBillEX37_clbk = void (WINAPIV*)(struct CEngNetworkBillEX*, CEngNetworkBillEXdtor_CEngNetworkBillEX37_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

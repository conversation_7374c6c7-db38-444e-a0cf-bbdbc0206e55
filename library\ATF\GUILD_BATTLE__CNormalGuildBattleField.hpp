// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCircleZone.hpp>
#include <CGravityStone.hpp>
#include <CGravityStoneRegener.hpp>
#include <CMapData.hpp>
#include <CPlayer.hpp>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CNormalGuildBattleField
        {
            bool m_bInit;
            unsigned int m_uiMapInx;
            CMapData *m_pkMap;
            _dummy_position *m_pkStartPos[2];
            unsigned int m_ui1PGoalPosCnt;
            CCircleZone *m_pk1PGoalZone;
            unsigned int m_ui2PGoalPosCnt;
            CCircleZone *m_pk2PGoalZone;
            unsigned int m_uiRegenPosCnt;
            CGravityStoneRegener *m_pkRegenPos;
            CGravityStone *m_pkBall;
        public:
            CNormalGuildBattleField();
            void ctor_CNormalGuildBattleField();
            void CheatDestroyStone();
            char CheatDropStone(struct CPlayer* pkPlayer);
            bool CheatForceTakeStone(struct CPlayer* pkPlayer);
            char CheatGetStone(struct CPlayer* pkPlayer);
            int CheatRegenStone(struct CPlayer* pkPlayer);
            int CheatRegenStone(unsigned int uiPos);
            char CheatTakeStone(int iPortalInx, struct CPlayer* pkPlayer);
            bool CheckBallTakeLimitTime();
            bool CheckIsInTown();
            bool ClearBall();
            bool ClearRegen();
            bool CreateFieldObject();
            void Destroy();
            void DestroyFieldObject();
            char DropBall(struct CPlayer* pkPlayer);
            char GetBall(uint16_t wIndex, unsigned int dwObjSerial, struct CPlayer* pkPlayer);
            struct CPlayer* GetBallOwner();
            struct CCircleZone* GetCircleZone(int iInx);
            struct CMapData* GetMap();
            int GetMapCode();
            unsigned int GetMapID();
            char* GetMapStrCode();
            void GetPortalIndexInfo(int* iRedPortalInx, int* iBluePortalInx, int* piRegenPortalInx);
            struct CGravityStoneRegener* GetRegener(int iInx);
            struct CGravityStone* GetStone();
            bool Init(unsigned int uiMapInx);
            char IsGoal(struct CPlayer* pkPlayer, int iPortalInx);
            bool LoadDummys(char* szSectionName, char* szKeyName, char* szItemName, unsigned int* uiCnt, struct _dummy_position*** ppDummy);
            bool MoveStartPos(char byStartPos, char byMapOutType, struct CPlayer* pkPlayer);
            int RegenBall();
            bool SetPortalInx(struct _dummy_position** ppkDummy, int** iPortalInx, unsigned int uiCnt);
            bool Start(char byStartPos, struct CPlayer* pkPlayer);
            char TakeBall(int iPortalInx, struct CPlayer* pkPlayer);
            ~CNormalGuildBattleField();
            void dtor_CNormalGuildBattleField();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

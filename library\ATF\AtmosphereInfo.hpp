// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Atmosphere.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using AtmosphereCalculateScatteringConstants1_ptr = void (WINAPIV*)(struct Atmosphere*);
        using AtmosphereCalculateScatteringConstants1_clbk = void (WINAPIV*)(struct Atmosphere*, AtmosphereCalculateScatteringConstants1_ptr);
        using AtmosphereDump22_ptr = void (WINAPIV*)(struct Atmosphere*, struct _iobuf*);
        using AtmosphereDump22_clbk = void (WINAPIV*)(struct Atmosphere*, struct _iobuf*, AtmosphereDump22_ptr);
        using AtmosphereGetBetaDashMie3_ptr = struct D3DXVECTOR3* (WINAPIV*)(struct Atmosphere*, short);
        using AtmosphereGetBetaDashMie3_clbk = struct D3DXVECTOR3* (WINAPIV*)(struct Atmosphere*, short, AtmosphereGetBetaDashMie3_ptr);
        using AtmosphereGetBetaDashRayleigh4_ptr = struct D3DXVECTOR3* (WINAPIV*)(struct Atmosphere*, short);
        using AtmosphereGetBetaDashRayleigh4_clbk = struct D3DXVECTOR3* (WINAPIV*)(struct Atmosphere*, short, AtmosphereGetBetaDashRayleigh4_ptr);
        using AtmosphereGetBetaMie5_ptr = struct D3DXVECTOR3* (WINAPIV*)(struct Atmosphere*, short);
        using AtmosphereGetBetaMie5_clbk = struct D3DXVECTOR3* (WINAPIV*)(struct Atmosphere*, short, AtmosphereGetBetaMie5_ptr);
        using AtmosphereGetBetaRayleigh6_ptr = struct D3DXVECTOR3* (WINAPIV*)(struct Atmosphere*, short);
        using AtmosphereGetBetaRayleigh6_clbk = struct D3DXVECTOR3* (WINAPIV*)(struct Atmosphere*, short, AtmosphereGetBetaRayleigh6_ptr);
        using AtmosphereInterpolate7_ptr = void (WINAPIV*)(struct Atmosphere*, struct Atmosphere*, struct Atmosphere*, float);
        using AtmosphereInterpolate7_clbk = void (WINAPIV*)(struct Atmosphere*, struct Atmosphere*, struct Atmosphere*, float, AtmosphereInterpolate7_ptr);
        using AtmosphereRead28_ptr = void (WINAPIV*)(struct Atmosphere*, struct _iobuf*);
        using AtmosphereRead28_clbk = void (WINAPIV*)(struct Atmosphere*, struct _iobuf*, AtmosphereRead28_ptr);
        
        using Atmospheredtor_Atmosphere9_ptr = int64_t (WINAPIV*)(struct Atmosphere*);
        using Atmospheredtor_Atmosphere9_clbk = int64_t (WINAPIV*)(struct Atmosphere*, Atmospheredtor_Atmosphere9_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPathMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CPathMgrctor_CPathMgr2_ptr = void (WINAPIV*)(struct CPathMgr*);
        using CPathMgrctor_CPathMgr2_clbk = void (WINAPIV*)(struct CPathMgr*, CPathMgrctor_CPathMgr2_ptr);
        using CPathMgrCopy4_ptr = void (WINAPIV*)(struct CPathMgr*, struct CPathMgr*);
        using CPathMgrCopy4_clbk = void (WINAPIV*)(struct CPathMgr*, struct CPathMgr*, CPathMgrCopy4_ptr);
        using CPathMgrGetPathSize6_ptr = char (WINAPIV*)(struct CPathMgr*);
        using CPathMgrGetPathSize6_clbk = char (WINAPIV*)(struct CPathMgr*, CPathMgrGetPathSize6_ptr);
        using CPathMgrInit8_ptr = void (WINAPIV*)(struct CPathMgr*);
        using CPathMgrInit8_clbk = void (WINAPIV*)(struct CPathMgr*, CPathMgrInit8_ptr);
        using CPathMgrPopNextPath10_ptr = int (WINAPIV*)(struct CPathMgr*, float*);
        using CPathMgrPopNextPath10_clbk = int (WINAPIV*)(struct CPathMgr*, float*, CPathMgrPopNextPath10_ptr);
        using CPathMgrSearchPathA12_ptr = int (WINAPIV*)(struct CPathMgr*, struct CMonster*, float*, int);
        using CPathMgrSearchPathA12_clbk = int (WINAPIV*)(struct CPathMgr*, struct CMonster*, float*, int, CPathMgrSearchPathA12_ptr);
        
        using CPathMgrdtor_CPathMgr17_ptr = void (WINAPIV*)(struct CPathMgr*);
        using CPathMgrdtor_CPathMgr17_clbk = void (WINAPIV*)(struct CPathMgr*, CPathMgrdtor_CPathMgr17_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _enter_world_request_zone
    {
        unsigned int dwAccountSerial;
        unsigned int dwMasterKey[4];
        bool bFullMode;
        unsigned int dwProtocolVer;
        char szClientVerCheckKey[33];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _base_fldctor__base_fld2_ptr = void (WINAPIV*)(struct _base_fld*);
        using _base_fldctor__base_fld2_clbk = void (WINAPIV*)(struct _base_fld*, _base_fldctor__base_fld2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

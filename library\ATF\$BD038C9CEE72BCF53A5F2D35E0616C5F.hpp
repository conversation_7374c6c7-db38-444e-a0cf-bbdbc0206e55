// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $BD038C9CEE72BCF53A5F2D35E0616C5F
    {
      ERROR_APARTY_NONE = 0x0,
      ERROR_APARTY_CANT_FIND_INVITEE = 0x1,
      ERROR_APARTY_DIFF_RACE = 0x2,
      ERROR_APARTY_CANT_CONDITION = 0x3,
      ERROR_APARTY_ALREADY_PARTY = 0x4,
      ERROR_APARTY_LIMIT_LEVEL = 0x5,
      ERROR_APARTY_REFUSE_MODE = 0x6,
      ERROR_APARTY_CANT_INVITATION = 0x7,
      ERROR_APARTY_NOT_READER = 0x8,
      ERROR_APARTY_ITEM_NOTING = 0x9,
      ERROR_APARTY_REFUSE_INVITEE = 0xA,
      ERROR_APARTY_NOT_ENOUGH_MONEY = 0xB,
    };
END_ATF_NAMESPACE

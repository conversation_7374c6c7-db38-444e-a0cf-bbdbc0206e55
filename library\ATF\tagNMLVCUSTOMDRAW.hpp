// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMCUSTOMDRAWINFO.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagNMLVCUSTOMDRAW
    {
        tagNMCUSTOMDRAWINFO nmcd;
        unsigned int clrText;
        unsigned int clrTextBk;
        int iSubItem;
        unsigned int dwItemType;
        unsigned int clrFace;
        int iIconEffect;
        int iIconPhase;
        int iPartId;
        int iStateId;
        tagRECT rcText;
        unsigned int uAlign;
    };
END_ATF_NAMESPACE

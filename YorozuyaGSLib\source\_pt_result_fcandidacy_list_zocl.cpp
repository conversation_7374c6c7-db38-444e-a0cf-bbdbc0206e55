#include <_pt_result_fcandidacy_list_zocl.hpp>


START_ATF_NAMESPACE
    _pt_result_fcandidacy_list_zocl::_pt_result_fcandidacy_list_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _pt_result_fcandidacy_list_zocl*);
        (org_ptr(0x1402b7d30L))(this);
    };
    void _pt_result_fcandidacy_list_zocl::ctor__pt_result_fcandidacy_list_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _pt_result_fcandidacy_list_zocl*);
        (org_ptr(0x1402b7d30L))(this);
    };
    int _pt_result_fcandidacy_list_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _pt_result_fcandidacy_list_zocl*);
        return (org_ptr(0x1402b7e30L))(this);
    };
    
END_ATF_NAMESPACE

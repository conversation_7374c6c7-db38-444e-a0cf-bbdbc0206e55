// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagBITMAPINFOHEADER
    {
        unsigned int biSize;
        int biWidth;
        int biHeight;
        unsigned __int16 biPlanes;
        unsigned __int16 biBitCount;
        unsigned int biCompression;
        unsigned int biSizeImage;
        int biXPelsPerMeter;
        int biYPelsPerMeter;
        unsigned int biClrUsed;
        unsigned int biClrImportant;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _darkhole_open_all_portal_by_result_inform_zocl
    {
        char byCnt;
        char byPotalIndex[128];
    public:
        _darkhole_open_all_portal_by_result_inform_zocl();
        void ctor__darkhole_open_all_portal_by_result_inform_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_darkhole_open_all_portal_by_result_inform_zocl, 129>(), "_darkhole_open_all_portal_by_result_inform_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <UsPoint.hpp>


START_ATF_NAMESPACE
    struct CRFMonsterAIMgr
    {
        UsPoint m_spStateTBLPoolPtr[1];
    public:
        CRFMonsterAIMgr();
        void ctor_CRFMonsterAIMgr();
        static void Destory();
        struct UsPoint* GetStateTBL(struct UsPoint* result, int nIndex);
        static struct CRFMonsterAIMgr* Instance();
        ~CRFMonsterAIMgr();
        void dtor_CRFMonsterAIMgr();
    };
END_ATF_NAMESPACE

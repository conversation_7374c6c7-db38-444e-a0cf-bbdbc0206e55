// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _notify_gold_box_event_status_zocl
    {
        char byBoxType;
        char byTableCode;
        unsigned __int16 wItemIndex;
        char byBoxDur;
        unsigned int dwCharSerial;
        char szCharacterName[17];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

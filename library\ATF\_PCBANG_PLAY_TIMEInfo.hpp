// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PCBANG_PLAY_TIME.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _PCBANG_PLAY_TIMEInit2_ptr = void (WINAPIV*)(struct _PCBANG_PLAY_TIME*);
        using _PCBANG_PLAY_TIMEInit2_clbk = void (WINAPIV*)(struct _PCBANG_PLAY_TIME*, _PCBANG_PLAY_TIMEInit2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

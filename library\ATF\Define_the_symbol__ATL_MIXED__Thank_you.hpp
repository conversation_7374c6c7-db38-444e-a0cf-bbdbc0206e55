// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Define_the_symbol__ATL_MIXED__Thank_youVtbl.hpp>


START_ATF_NAMESPACE
    namespace Define_the_symbol__ATL_MIXED
    {
        struct Thank_you
        {
            Thank_youVtbl *vfptr;
        public:
            Thank_you();
            void ctor_Thank_you();
            void one();
        };    
        static_assert(ATF::checkSize<Define_the_symbol__ATL_MIXED::Thank_you, 8>(), "Define_the_symbol__ATL_MIXED::Thank_you");
    }; // end namespace Define_the_symbol__ATL_MIXED
END_ATF_NAMESPACE

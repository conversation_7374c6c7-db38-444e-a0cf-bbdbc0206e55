// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <US__AbstractThreadPoolVtbl.hpp>
#include <US__CNoneCopyAble.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        struct  AbstractThreadPool : CNoneCopyAble
        {
            AbstractThreadPoolVtbl *vfptr;
        };
    }; // end namespace US
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _class_skill_recall_teleport_request_clzo
    {
        unsigned __int16 wSkillIndex;
        char wszRecallName[17];
        unsigned __int16 wConsumeItemSerial[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CParkingUnit.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CParkingUnitctor_CParkingUnit2_ptr = void (WINAPIV*)(struct CParkingUnit*);
        using CParkingUnitctor_CParkingUnit2_clbk = void (WINAPIV*)(struct CParkingUnit*, CParkingUnitctor_CParkingUnit2_ptr);
        using CParkingUnitCalcCurHPRate4_ptr = uint16_t (WINAPIV*)(struct CParkingUnit*);
        using CParkingUnitCalcCurHPRate4_clbk = uint16_t (WINAPIV*)(struct CParkingUnit*, CParkingUnitCalcCurHPRate4_ptr);
        using CParkingUnitChangeOwner6_ptr = void (WINAPIV*)(struct CParkingUnit*, struct CPlayer*, char);
        using CParkingUnitChangeOwner6_clbk = void (WINAPIV*)(struct CParkingUnit*, struct CPlayer*, char, CParkingUnitChangeOwner6_ptr);
        using CParkingUnitCreate8_ptr = bool (WINAPIV*)(struct CParkingUnit*, struct _parkingunit_create_setdata*);
        using CParkingUnitCreate8_clbk = bool (WINAPIV*)(struct CParkingUnit*, struct _parkingunit_create_setdata*, CParkingUnitCreate8_ptr);
        using CParkingUnitDestroy10_ptr = bool (WINAPIV*)(struct CParkingUnit*, char);
        using CParkingUnitDestroy10_clbk = bool (WINAPIV*)(struct CParkingUnit*, char, CParkingUnitDestroy10_ptr);
        using CParkingUnitInit12_ptr = void (WINAPIV*)(struct CParkingUnit*, struct _object_id*);
        using CParkingUnitInit12_clbk = void (WINAPIV*)(struct CParkingUnit*, struct _object_id*, CParkingUnitInit12_ptr);
        using CParkingUnitIsRideRight14_ptr = bool (WINAPIV*)(struct CParkingUnit*, struct CPlayer*);
        using CParkingUnitIsRideRight14_clbk = bool (WINAPIV*)(struct CParkingUnit*, struct CPlayer*, CParkingUnitIsRideRight14_ptr);
        using CParkingUnitLoop16_ptr = void (WINAPIV*)(struct CParkingUnit*);
        using CParkingUnitLoop16_clbk = void (WINAPIV*)(struct CParkingUnit*, CParkingUnitLoop16_ptr);
        using CParkingUnitSendMsg_ChangeOwner18_ptr = void (WINAPIV*)(struct CParkingUnit*, char, struct CPlayer*);
        using CParkingUnitSendMsg_ChangeOwner18_clbk = void (WINAPIV*)(struct CParkingUnit*, char, struct CPlayer*, CParkingUnitSendMsg_ChangeOwner18_ptr);
        using CParkingUnitSendMsg_Create20_ptr = void (WINAPIV*)(struct CParkingUnit*);
        using CParkingUnitSendMsg_Create20_clbk = void (WINAPIV*)(struct CParkingUnit*, CParkingUnitSendMsg_Create20_ptr);
        using CParkingUnitSendMsg_Destroy22_ptr = void (WINAPIV*)(struct CParkingUnit*, char);
        using CParkingUnitSendMsg_Destroy22_clbk = void (WINAPIV*)(struct CParkingUnit*, char, CParkingUnitSendMsg_Destroy22_ptr);
        using CParkingUnitSendMsg_FixPosition24_ptr = void (WINAPIV*)(struct CParkingUnit*, int);
        using CParkingUnitSendMsg_FixPosition24_clbk = void (WINAPIV*)(struct CParkingUnit*, int, CParkingUnitSendMsg_FixPosition24_ptr);
        
        using CParkingUnitdtor_CParkingUnit30_ptr = void (WINAPIV*)(struct CParkingUnit*);
        using CParkingUnitdtor_CParkingUnit30_clbk = void (WINAPIV*)(struct CParkingUnit*, CParkingUnitdtor_CParkingUnit30_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

#include <_unit_pack_fill_result_zocl.hpp>


START_ATF_NAMESPACE
    _unit_pack_fill_result_zocl::_unit_pack_fill_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _unit_pack_fill_result_zocl*);
        (org_ptr(0x1400efa30L))(this);
    };
    void _unit_pack_fill_result_zocl::ctor__unit_pack_fill_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _unit_pack_fill_result_zocl*);
        (org_ptr(0x1400efa30L))(this);
    };
END_ATF_NAMESPACE

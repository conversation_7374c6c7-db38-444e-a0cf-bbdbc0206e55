// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union $00F4D6FA838FAD5B65924731E0E9CCBC
    {
        int lPitch;
        unsigned int dwLinearSize;
    };    
    static_assert(ATF::checkSize<$00F4D6FA838FAD5B65924731E0E9CCBC, 4>(), "$00F4D6FA838FAD5B65924731E0E9CCBC");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <_ITEMIDLIST.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _browseinfoW
    {
        HWND__ *hwndOwner;
        _ITEMIDLIST *pidlRoot;
        wchar_t *pszDisplayName;
        const wchar_t *lpszTitle;
        unsigned int ulFlags;
        int (WINAPIV *lpfn)(HWND__ *, unsigned int, __int64, __int64);
        __int64 lParam;
        int iImage;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

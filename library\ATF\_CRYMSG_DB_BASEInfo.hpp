// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CRYMSG_DB_BASE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _CRYMSG_DB_BASEInit2_ptr = void (WINAPIV*)(struct _CRYMSG_DB_BASE*);
        using _CRYMSG_DB_BASEInit2_clbk = void (WINAPIV*)(struct _CRYMSG_DB_BASE*, _CRYMSG_DB_BASEInit2_ptr);
        
        using _CRYMSG_DB_BASEctor__CRYMSG_DB_BASE4_ptr = void (WINAPIV*)(struct _CRYMSG_DB_BASE*);
        using _CRYMSG_DB_BASEctor__CRYMSG_DB_BASE4_clbk = void (WINAPIV*)(struct _CRYMSG_DB_BASE*, _CRYMSG_DB_BASEctor__CRYMSG_DB_BASE4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

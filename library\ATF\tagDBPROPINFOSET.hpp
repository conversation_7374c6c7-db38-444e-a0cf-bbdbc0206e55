// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <tagDBPROPINFO.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagDBPROPINFOSET
    {
        tagDBPROPINFO *rgPropertyInfos;
        unsigned int cPropertyInfos;
        _GUID guidPropertySet;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $40942CBCB8F0A1CDBC81269929B28324
    {
        float _11;
        float _12;
        float _13;
        float _14;
        float _21;
        float _22;
        float _23;
        float _24;
        float _31;
        float _32;
        float _33;
        float _34;
        float _41;
        float _42;
        float _43;
        float _44;
    };    
    static_assert(ATF::checkSize<$40942CBCB8F0A1CDBC81269929B28324, 64>(), "$40942CBCB8F0A1CDBC81269929B28324");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IOleUILinkInfoW.hpp>
#include <IOleUIObjInfoW.hpp>
#include <_PROPSHEETHEADERW.hpp>
#include <tagOLEUIGNRLPROPSW.hpp>
#include <tagOLEUILINKPROPSW.hpp>
#include <tagOLEUIVIEWPROPSW.hpp>


START_ATF_NAMESPACE
    struct tagOLEUIOBJECTPROPSW
    {
        unsigned int cbStruct;
        unsigned int dwFlags;
        _PROPSHEETHEADERW *lpPS;
        unsigned int dwObject;
        IOleUIObjInfoW *lpObjInfo;
        unsigned int dwLink;
        IOleUILinkInfoW *lpLinkInfo;
        tagOLEUIGNRLPROPSW *lpGP;
        tagOLEUIVIEWPROPSW *lpVP;
        tagOLEUILINKPROPSW *lpLP;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_Exttrunk_db_load.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _Exttrunk_db_loadctor__Exttrunk_db_load2_ptr = void (WINAPIV*)(struct _Exttrunk_db_load*);
        using _Exttrunk_db_loadctor__Exttrunk_db_load2_clbk = void (WINAPIV*)(struct _Exttrunk_db_load*, _Exttrunk_db_loadctor__Exttrunk_db_load2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

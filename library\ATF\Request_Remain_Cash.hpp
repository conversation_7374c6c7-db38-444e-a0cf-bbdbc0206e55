// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct Request_Remain_Cash
    {
        unsigned __int16 wRet;
        unsigned int dwDataLength;
        unsigned int dwSeq;
        int nBuyCash;
        int nServiceCash1;
        int nServiceCash2;
        int nServiceCash3;
    public:
        Request_Remain_Cash();
        void ctor_Request_Remain_Cash();
    };
END_ATF_NAMESPACE

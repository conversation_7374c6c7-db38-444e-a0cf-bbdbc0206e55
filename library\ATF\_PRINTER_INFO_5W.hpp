// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _PRINTER_INFO_5W
    {
        wchar_t *pPrinterName;
        wchar_t *pPortName;
        unsigned int Attributes;
        unsigned int DeviceNotSelectedTimeout;
        unsigned int TransmissionRetryTimeout;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

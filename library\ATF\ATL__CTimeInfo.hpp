// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTime.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            
            using ATL__CTimector_CTime1_ptr = void (WINAPIV*)(struct ATL::CTime*, struct _FILETIME*, int);
            using ATL__CTimector_CTime1_clbk = void (WINAPIV*)(struct ATL::CTime*, struct _FILETIME*, int, ATL__CTimector_CTime1_ptr);
            
            using ATL__CTimector_CTime2_ptr = void (WINAPIV*)(struct ATL::CTime*, struct _SYSTEMTIME*, int);
            using ATL__CTimector_CTime2_clbk = void (WINAPIV*)(struct ATL::CTime*, struct _SYSTEMTIME*, int, ATL__CTimector_CTime2_ptr);
            
            using ATL__CTimector_CTime3_ptr = void (WINAPIV*)(struct ATL::CTime*, int64_t);
            using ATL__CTimector_CTime3_clbk = void (WINAPIV*)(struct ATL::CTime*, int64_t, ATL__CTimector_CTime3_ptr);
            
            using ATL__CTimector_CTime4_ptr = void (WINAPIV*)(struct ATL::CTime*, int, int, int, int, int, int, int);
            using ATL__CTimector_CTime4_clbk = void (WINAPIV*)(struct ATL::CTime*, int, int, int, int, int, int, int, ATL__CTimector_CTime4_ptr);
            
            using ATL__CTimector_CTime5_ptr = void (WINAPIV*)(struct ATL::CTime*, uint16_t, uint16_t, int);
            using ATL__CTimector_CTime5_clbk = void (WINAPIV*)(struct ATL::CTime*, uint16_t, uint16_t, int, ATL__CTimector_CTime5_ptr);
            
            using ATL__CTimector_CTime6_ptr = void (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimector_CTime6_clbk = void (WINAPIV*)(struct ATL::CTime*, ATL__CTimector_CTime6_ptr);
            using ATL__CTimeGetAsSystemTime7_ptr = bool (WINAPIV*)(struct ATL::CTime*, struct _SYSTEMTIME*);
            using ATL__CTimeGetAsSystemTime7_clbk = bool (WINAPIV*)(struct ATL::CTime*, struct _SYSTEMTIME*, ATL__CTimeGetAsSystemTime7_ptr);
            using ATL__CTimeGetDay8_ptr = int (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimeGetDay8_clbk = int (WINAPIV*)(struct ATL::CTime*, ATL__CTimeGetDay8_ptr);
            using ATL__CTimeGetDayOfWeek9_ptr = int (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimeGetDayOfWeek9_clbk = int (WINAPIV*)(struct ATL::CTime*, ATL__CTimeGetDayOfWeek9_ptr);
            using ATL__CTimeGetGmtTm10_ptr = struct tm* (WINAPIV*)(struct ATL::CTime*, struct tm*);
            using ATL__CTimeGetGmtTm10_clbk = struct tm* (WINAPIV*)(struct ATL::CTime*, struct tm*, ATL__CTimeGetGmtTm10_ptr);
            using ATL__CTimeGetHour11_ptr = int (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimeGetHour11_clbk = int (WINAPIV*)(struct ATL::CTime*, ATL__CTimeGetHour11_ptr);
            using ATL__CTimeGetLocalTm12_ptr = struct tm* (WINAPIV*)(struct ATL::CTime*, struct tm*);
            using ATL__CTimeGetLocalTm12_clbk = struct tm* (WINAPIV*)(struct ATL::CTime*, struct tm*, ATL__CTimeGetLocalTm12_ptr);
            using ATL__CTimeGetMinute13_ptr = int (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimeGetMinute13_clbk = int (WINAPIV*)(struct ATL::CTime*, ATL__CTimeGetMinute13_ptr);
            using ATL__CTimeGetMonth14_ptr = int (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimeGetMonth14_clbk = int (WINAPIV*)(struct ATL::CTime*, ATL__CTimeGetMonth14_ptr);
            using ATL__CTimeGetSecond15_ptr = int (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimeGetSecond15_clbk = int (WINAPIV*)(struct ATL::CTime*, ATL__CTimeGetSecond15_ptr);
            using ATL__CTimeGetTickCount16_ptr = struct ATL::CTime* (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimeGetTickCount16_clbk = struct ATL::CTime* (WINAPIV*)(struct ATL::CTime*, ATL__CTimeGetTickCount16_ptr);
            using ATL__CTimeGetTime17_ptr = int64_t (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimeGetTime17_clbk = int64_t (WINAPIV*)(struct ATL::CTime*, ATL__CTimeGetTime17_ptr);
            using ATL__CTimeGetYear18_ptr = int (WINAPIV*)(struct ATL::CTime*);
            using ATL__CTimeGetYear18_clbk = int (WINAPIV*)(struct ATL::CTime*, ATL__CTimeGetYear18_ptr);
            using ATL__CTimeIsValidFILETIME19_ptr = int (WINAPIV*)(struct _FILETIME*);
            using ATL__CTimeIsValidFILETIME19_clbk = int (WINAPIV*)(struct _FILETIME*, ATL__CTimeIsValidFILETIME19_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $487E8D0D3D4A475D3A77722A47823443
    {
      conevent_type_disable = 0x0,
      conevent_type_wait = 0x1,
      conevent_type_start = 0x2,
      conevent_type_doing = 0x3,
      conevent_type_end = 0x4,
      conevent_type_error = 0x5,
      conevent_type_all = 0x6,
    };
END_ATF_NAMESPACE

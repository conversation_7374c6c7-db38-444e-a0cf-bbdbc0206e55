// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMsgData.hpp>
#include <_message.hpp>


START_ATF_NAMESPACE
    struct  CMsgProcess : CMsgData
    {
    public:
        CMsgProcess(int nObjNum);
        void ctor_CMsgProcess(int nObjNum);
        CMsgProcess();
        void ctor_CMsgProcess();
        void ProcessMessage(struct _message* pMsg);
        ~CMsgProcess();
        void dtor_CMsgProcess();
    };    
    static_assert(ATF::checkSize<CMsgProcess, 248>(), "CMsgProcess");
END_ATF_NAMESPACE

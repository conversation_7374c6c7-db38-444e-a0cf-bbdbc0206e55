// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaScriptMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CLuaScriptMgrAttachLuaScript2_ptr = bool (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaScript*, struct CLuaCommand*);
        using CLuaScriptMgrAttachLuaScript2_clbk = bool (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaScript*, struct CLuaCommand*, CLuaScriptMgrAttachLuaScript2_ptr);
        
        using CLuaScriptMgrctor_CLuaScriptMgr4_ptr = void (WINAPIV*)(struct CLuaScriptMgr*);
        using CLuaScriptMgrctor_CLuaScriptMgr4_clbk = void (WINAPIV*)(struct CLuaScriptMgr*, CLuaScriptMgrctor_CLuaScriptMgr4_ptr);
        using CLuaScriptMgrDestroy6_ptr = void (WINAPIV*)();
        using CLuaScriptMgrDestroy6_clbk = void (WINAPIV*)(CLuaScriptMgrDestroy6_ptr);
        using CLuaScriptMgrDetackLuaScript8_ptr = bool (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaScript*);
        using CLuaScriptMgrDetackLuaScript8_clbk = bool (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaScript*, CLuaScriptMgrDetackLuaScript8_ptr);
        using CLuaScriptMgrGetErrorLogFile10_ptr = struct CLogFile* (WINAPIV*)(struct CLuaScriptMgr*);
        using CLuaScriptMgrGetErrorLogFile10_clbk = struct CLogFile* (WINAPIV*)(struct CLuaScriptMgr*, CLuaScriptMgrGetErrorLogFile10_ptr);
        using CLuaScriptMgrGetStateLogFile12_ptr = struct CLogFile* (WINAPIV*)(struct CLuaScriptMgr*);
        using CLuaScriptMgrGetStateLogFile12_clbk = struct CLogFile* (WINAPIV*)(struct CLuaScriptMgr*, CLuaScriptMgrGetStateLogFile12_ptr);
        using CLuaScriptMgrInitSDM14_ptr = bool (WINAPIV*)(struct CLuaScriptMgr*);
        using CLuaScriptMgrInitSDM14_clbk = bool (WINAPIV*)(struct CLuaScriptMgr*, CLuaScriptMgrInitSDM14_ptr);
        using CLuaScriptMgrInstance16_ptr = struct CLuaScriptMgr* (WINAPIV*)();
        using CLuaScriptMgrInstance16_clbk = struct CLuaScriptMgr* (WINAPIV*)(CLuaScriptMgrInstance16_ptr);
        using CLuaScriptMgrLogStack18_ptr = void (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaScript*);
        using CLuaScriptMgrLogStack18_clbk = void (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaScript*, CLuaScriptMgrLogStack18_ptr);
        using CLuaScriptMgrLoop20_ptr = void (WINAPIV*)(struct CLuaScriptMgr*);
        using CLuaScriptMgrLoop20_clbk = void (WINAPIV*)(struct CLuaScriptMgr*, CLuaScriptMgrLoop20_ptr);
        using CLuaScriptMgrNewCommandEx22_ptr = struct CLuaCommandEx* (WINAPIV*)(struct CLuaScriptMgr*);
        using CLuaScriptMgrNewCommandEx22_clbk = struct CLuaCommandEx* (WINAPIV*)(struct CLuaScriptMgr*, CLuaScriptMgrNewCommandEx22_ptr);
        using CLuaScriptMgrNewScript24_ptr = struct CLuaScript* (WINAPIV*)(struct CLuaScriptMgr*);
        using CLuaScriptMgrNewScript24_clbk = struct CLuaScript* (WINAPIV*)(struct CLuaScriptMgr*, CLuaScriptMgrNewScript24_ptr);
        using CLuaScriptMgrRemoveCommandEx26_ptr = void (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaCommandEx*);
        using CLuaScriptMgrRemoveCommandEx26_clbk = void (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaCommandEx*, CLuaScriptMgrRemoveCommandEx26_ptr);
        using CLuaScriptMgrRemoveScript28_ptr = void (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaScript*);
        using CLuaScriptMgrRemoveScript28_clbk = void (WINAPIV*)(struct CLuaScriptMgr*, struct CLuaScript*, CLuaScriptMgrRemoveScript28_ptr);
        using CLuaScriptMgrSearchScript30_ptr = struct CLuaScript* (WINAPIV*)(struct CLuaScriptMgr*, char*);
        using CLuaScriptMgrSearchScript30_clbk = struct CLuaScript* (WINAPIV*)(struct CLuaScriptMgr*, char*, CLuaScriptMgrSearchScript30_ptr);
        using CLuaScriptMgrSearchScriptFromLuaState32_ptr = struct CLuaScript* (WINAPIV*)(struct CLuaScriptMgr*, lua_State*);
        using CLuaScriptMgrSearchScriptFromLuaState32_clbk = struct CLuaScript* (WINAPIV*)(struct CLuaScriptMgr*, lua_State*, CLuaScriptMgrSearchScriptFromLuaState32_ptr);
        using CLuaScriptMgr_Regist_Novus34_ptr = bool (WINAPIV*)(struct CLuaScriptMgr*);
        using CLuaScriptMgr_Regist_Novus34_clbk = bool (WINAPIV*)(struct CLuaScriptMgr*, CLuaScriptMgr_Regist_Novus34_ptr);
        
        using CLuaScriptMgrdtor_CLuaScriptMgr38_ptr = void (WINAPIV*)(struct CLuaScriptMgr*);
        using CLuaScriptMgrdtor_CLuaScriptMgr38_clbk = void (WINAPIV*)(struct CLuaScriptMgr*, CLuaScriptMgrdtor_CLuaScriptMgr38_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

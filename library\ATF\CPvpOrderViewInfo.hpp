// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPvpOrderView.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPvpOrderViewGetPvpCash2_ptr = long double (WINAPIV*)(struct CPvpOrderView*);
        using CPvpOrderViewGetPvpCash2_clbk = long double (WINAPIV*)(struct CPvpOrderView*, CPvpOrderViewGetPvpCash2_ptr);
        using CPvpOrderViewGetPvpTempCash4_ptr = long double (WINAPIV*)(struct CPvpOrderView*);
        using CPvpOrderViewGetPvpTempCash4_clbk = long double (WINAPIV*)(struct CPvpOrderView*, CPvpOrderViewGetPvpTempCash4_ptr);
        using CPvpOrderViewInit6_ptr = bool (WINAPIV*)(struct CPvpOrderView*);
        using CPvpOrderViewInit6_clbk = bool (WINAPIV*)(struct CPvpOrderView*, CPvpOrderViewInit6_ptr);
        using CPvpOrderViewLoop8_ptr = void (WINAPIV*)(struct CPvpOrderView*, uint16_t);
        using CPvpOrderViewLoop8_clbk = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, CPvpOrderViewLoop8_ptr);
        using CPvpOrderViewNotify_OrderView10_ptr = void (WINAPIV*)(struct CPvpOrderView*, uint16_t);
        using CPvpOrderViewNotify_OrderView10_clbk = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, CPvpOrderViewNotify_OrderView10_ptr);
        using CPvpOrderViewNotify_Point12_ptr = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, long double, unsigned int);
        using CPvpOrderViewNotify_Point12_clbk = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, long double, unsigned int, CPvpOrderViewNotify_Point12_ptr);
        using CPvpOrderViewNotify_PvPEnd14_ptr = void (WINAPIV*)(struct CPvpOrderView*, uint16_t);
        using CPvpOrderViewNotify_PvPEnd14_clbk = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, CPvpOrderViewNotify_PvPEnd14_ptr);
        using CPvpOrderViewNotify_PvpTempCash16_ptr = void (WINAPIV*)(struct CPvpOrderView*, uint16_t);
        using CPvpOrderViewNotify_PvpTempCash16_clbk = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, CPvpOrderViewNotify_PvpTempCash16_ptr);
        using CPvpOrderViewResetPvPOrderView18_ptr = void (WINAPIV*)(struct CPvpOrderView*);
        using CPvpOrderViewResetPvPOrderView18_clbk = void (WINAPIV*)(struct CPvpOrderView*, CPvpOrderViewResetPvPOrderView18_ptr);
        using CPvpOrderViewSetOrderViewAttackState20_ptr = void (WINAPIV*)(struct CPvpOrderView*);
        using CPvpOrderViewSetOrderViewAttackState20_clbk = void (WINAPIV*)(struct CPvpOrderView*, CPvpOrderViewSetOrderViewAttackState20_ptr);
        using CPvpOrderViewSetOrderViewDamagedState22_ptr = void (WINAPIV*)(struct CPvpOrderView*);
        using CPvpOrderViewSetOrderViewDamagedState22_clbk = void (WINAPIV*)(struct CPvpOrderView*, CPvpOrderViewSetOrderViewDamagedState22_ptr);
        using CPvpOrderViewSetPvpCash24_ptr = void (WINAPIV*)(struct CPvpOrderView*, long double);
        using CPvpOrderViewSetPvpCash24_clbk = void (WINAPIV*)(struct CPvpOrderView*, long double, CPvpOrderViewSetPvpCash24_ptr);
        using CPvpOrderViewSetPvpOrderView26_ptr = bool (WINAPIV*)(struct CPvpOrderView*, long double, struct _PVP_ORDER_VIEW_DB_BASE*, struct CPlayer*);
        using CPvpOrderViewSetPvpOrderView26_clbk = bool (WINAPIV*)(struct CPvpOrderView*, long double, struct _PVP_ORDER_VIEW_DB_BASE*, struct CPlayer*, CPvpOrderViewSetPvpOrderView26_ptr);
        using CPvpOrderViewUpdate28_ptr = void (WINAPIV*)(struct CPvpOrderView*, int64_t, int, int, long double, long double, long double);
        using CPvpOrderViewUpdate28_clbk = void (WINAPIV*)(struct CPvpOrderView*, int64_t, int, int, long double, long double, long double, CPvpOrderViewUpdate28_ptr);
        using CPvpOrderViewUpdatePvPDeath30_ptr = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, unsigned int);
        using CPvpOrderViewUpdatePvPDeath30_clbk = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, unsigned int, CPvpOrderViewUpdatePvPDeath30_ptr);
        using CPvpOrderViewUpdatePvPKill32_ptr = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, unsigned int);
        using CPvpOrderViewUpdatePvPKill32_clbk = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, unsigned int, CPvpOrderViewUpdatePvPKill32_ptr);
        using CPvpOrderViewUpdatePvPPoint34_ptr = void (WINAPIV*)(struct CPvpOrderView*, long double, long double);
        using CPvpOrderViewUpdatePvPPoint34_clbk = void (WINAPIV*)(struct CPvpOrderView*, long double, long double, CPvpOrderViewUpdatePvPPoint34_ptr);
        using CPvpOrderViewUpdatePvpCash36_ptr = void (WINAPIV*)(struct CPvpOrderView*, long double);
        using CPvpOrderViewUpdatePvpCash36_clbk = void (WINAPIV*)(struct CPvpOrderView*, long double, CPvpOrderViewUpdatePvpCash36_ptr);
        using CPvpOrderViewUpdate_ContHaveCash38_ptr = void (WINAPIV*)(struct CPvpOrderView*, char);
        using CPvpOrderViewUpdate_ContHaveCash38_clbk = void (WINAPIV*)(struct CPvpOrderView*, char, CPvpOrderViewUpdate_ContHaveCash38_ptr);
        using CPvpOrderViewUpdate_ContLoseCash40_ptr = void (WINAPIV*)(struct CPvpOrderView*, char);
        using CPvpOrderViewUpdate_ContLoseCash40_clbk = void (WINAPIV*)(struct CPvpOrderView*, char, CPvpOrderViewUpdate_ContLoseCash40_ptr);
        using CPvpOrderViewUpdate_KillerList42_ptr = void (WINAPIV*)(struct CPvpOrderView*, unsigned int, int);
        using CPvpOrderViewUpdate_KillerList42_clbk = void (WINAPIV*)(struct CPvpOrderView*, unsigned int, int, CPvpOrderViewUpdate_KillerList42_ptr);
        using CPvpOrderViewUpdate_PvpTempCash44_ptr = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, long double);
        using CPvpOrderViewUpdate_PvpTempCash44_clbk = void (WINAPIV*)(struct CPvpOrderView*, uint16_t, long double, CPvpOrderViewUpdate_PvpTempCash44_ptr);
        using CPvpOrderViewUpdate_RaceWarRecvr46_ptr = void (WINAPIV*)(struct CPvpOrderView*, bool);
        using CPvpOrderViewUpdate_RaceWarRecvr46_clbk = void (WINAPIV*)(struct CPvpOrderView*, bool, CPvpOrderViewUpdate_RaceWarRecvr46_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

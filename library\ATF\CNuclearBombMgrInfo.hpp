// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNuclearBombMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNuclearBombMgrctor_CNuclearBombMgr2_ptr = void (WINAPIV*)(struct CNuclearBombMgr*);
        using CNuclearBombMgrctor_CNuclearBombMgr2_clbk = void (WINAPIV*)(struct CNuclearBombMgr*, CNuclearBombMgrctor_CNuclearBombMgr2_ptr);
        using CNuclearBombMgrCheckNuclearState4_ptr = void (WINAPIV*)(struct CNuclearBombMgr*, struct CPlayer*);
        using CNuclearBombMgrCheckNuclearState4_clbk = void (WINAPIV*)(struct CNuclearBombMgr*, struct CPlayer*, C<PERSON><PERSON>learBombMgrCheckNuclearState4_ptr);
        using CNuclearBombMgrCreateMissile6_ptr = bool (WINAPIV*)(struct CNuclearBombMgr*, struct CPlayer*, float*, unsigned int, unsigned int, unsigned int);
        using CNuclearBombMgrCreateMissile6_clbk = bool (WINAPIV*)(struct CNuclearBombMgr*, struct CPlayer*, float*, unsigned int, unsigned int, unsigned int, CNuclearBombMgrCreateMissile6_ptr);
        using CNuclearBombMgrDestroy8_ptr = void (WINAPIV*)(struct CNuclearBombMgr*);
        using CNuclearBombMgrDestroy8_clbk = void (WINAPIV*)(struct CNuclearBombMgr*, CNuclearBombMgrDestroy8_ptr);
        using CNuclearBombMgrGetBossType10_ptr = char (WINAPIV*)(struct CNuclearBombMgr*, char, unsigned int);
        using CNuclearBombMgrGetBossType10_clbk = char (WINAPIV*)(struct CNuclearBombMgr*, char, unsigned int, CNuclearBombMgrGetBossType10_ptr);
        using CNuclearBombMgrInstance12_ptr = struct CNuclearBombMgr* (WINAPIV*)();
        using CNuclearBombMgrInstance12_clbk = struct CNuclearBombMgr* (WINAPIV*)(CNuclearBombMgrInstance12_ptr);
        using CNuclearBombMgrIsPatriarch14_ptr = bool (WINAPIV*)(struct CNuclearBombMgr*, struct CPlayer*);
        using CNuclearBombMgrIsPatriarch14_clbk = bool (WINAPIV*)(struct CNuclearBombMgr*, struct CPlayer*, CNuclearBombMgrIsPatriarch14_ptr);
        using CNuclearBombMgrLoadIni16_ptr = bool (WINAPIV*)(struct CNuclearBombMgr*);
        using CNuclearBombMgrLoadIni16_clbk = bool (WINAPIV*)(struct CNuclearBombMgr*, CNuclearBombMgrLoadIni16_ptr);
        using CNuclearBombMgrLoop18_ptr = void (WINAPIV*)(struct CNuclearBombMgr*);
        using CNuclearBombMgrLoop18_clbk = void (WINAPIV*)(struct CNuclearBombMgr*, CNuclearBombMgrLoop18_ptr);
        using CNuclearBombMgrMissileInit20_ptr = bool (WINAPIV*)(struct CNuclearBombMgr*);
        using CNuclearBombMgrMissileInit20_clbk = bool (WINAPIV*)(struct CNuclearBombMgr*, CNuclearBombMgrMissileInit20_ptr);
        using CNuclearBombMgrRequest_EnableNuclearControl22_ptr = bool (WINAPIV*)(struct CNuclearBombMgr*, int, char*);
        using CNuclearBombMgrRequest_EnableNuclearControl22_clbk = bool (WINAPIV*)(struct CNuclearBombMgr*, int, char*, CNuclearBombMgrRequest_EnableNuclearControl22_ptr);
        using CNuclearBombMgrRequest_SelectDropPosition24_ptr = bool (WINAPIV*)(struct CNuclearBombMgr*, int, char*);
        using CNuclearBombMgrRequest_SelectDropPosition24_clbk = bool (WINAPIV*)(struct CNuclearBombMgr*, int, char*, CNuclearBombMgrRequest_SelectDropPosition24_ptr);
        using CNuclearBombMgrSendMsg_Result26_ptr = void (WINAPIV*)(struct CNuclearBombMgr*, int, char);
        using CNuclearBombMgrSendMsg_Result26_clbk = void (WINAPIV*)(struct CNuclearBombMgr*, int, char, CNuclearBombMgrSendMsg_Result26_ptr);
        
        using CNuclearBombMgrdtor_CNuclearBombMgr31_ptr = void (WINAPIV*)(struct CNuclearBombMgr*);
        using CNuclearBombMgrdtor_CNuclearBombMgr31_clbk = void (WINAPIV*)(struct CNuclearBombMgr*, CNuclearBombMgrdtor_CNuclearBombMgr31_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

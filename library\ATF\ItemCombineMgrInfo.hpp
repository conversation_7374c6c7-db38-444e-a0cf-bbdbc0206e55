// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ItemCombineMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using ItemCombineMgrCheckLoadData2_ptr = bool (WINAPIV*)();
        using ItemCombineMgrCheckLoadData2_clbk = bool (WINAPIV*)(ItemCombineMgrCheckLoadData2_ptr);
        using ItemCombineMgrClearDB_CombineResult4_ptr = char (WINAPIV*)(struct ItemCombineMgr*);
        using ItemCombineMgrClearDB_CombineResult4_clbk = char (WINAPIV*)(struct ItemCombineMgr*, ItemCombineMgrClearDB_CombineResult4_ptr);
        using ItemCombineMgrConsumeMeterial_And_CalculateNewItems6_ptr = char (WINAPIV*)(struct ItemCombineMgr*, struct _STORAGE_LIST::_db_con**, char, struct _combine_ex_item_request_clzo::_list*, struct _combine_ex_item_result_zocl*, struct _ItemCombine_exp_fld*, char, int);
        using ItemCombineMgrConsumeMeterial_And_CalculateNewItems6_clbk = char (WINAPIV*)(struct ItemCombineMgr*, struct _STORAGE_LIST::_db_con**, char, struct _combine_ex_item_request_clzo::_list*, struct _combine_ex_item_result_zocl*, struct _ItemCombine_exp_fld*, char, int, ItemCombineMgrConsumeMeterial_And_CalculateNewItems6_ptr);
        using ItemCombineMgrInitMgr8_ptr = void (WINAPIV*)(struct ItemCombineMgr*, struct CPlayer*);
        using ItemCombineMgrInitMgr8_clbk = void (WINAPIV*)(struct ItemCombineMgr*, struct CPlayer*, ItemCombineMgrInitMgr8_ptr);
        
        using ItemCombineMgrctor_ItemCombineMgr10_ptr = void (WINAPIV*)(struct ItemCombineMgr*);
        using ItemCombineMgrctor_ItemCombineMgr10_clbk = void (WINAPIV*)(struct ItemCombineMgr*, ItemCombineMgrctor_ItemCombineMgr10_ptr);
        using ItemCombineMgrLoadDB_CombineResult12_ptr = char (WINAPIV*)(struct ItemCombineMgr*, struct _combine_ex_item_result_zocl*);
        using ItemCombineMgrLoadDB_CombineResult12_clbk = char (WINAPIV*)(struct ItemCombineMgr*, struct _combine_ex_item_result_zocl*, ItemCombineMgrLoadDB_CombineResult12_ptr);
        using ItemCombineMgrLoadData14_ptr = bool (WINAPIV*)();
        using ItemCombineMgrLoadData14_clbk = bool (WINAPIV*)(ItemCombineMgrLoadData14_ptr);
        using ItemCombineMgrMakeNewItems16_ptr = char (WINAPIV*)(struct ItemCombineMgr*, struct _ITEMCOMBINE_DB_BASE*, struct _combine_ex_item_accept_request_clzo*, struct _combine_ex_item_accept_result_zocl*);
        using ItemCombineMgrMakeNewItems16_clbk = char (WINAPIV*)(struct ItemCombineMgr*, struct _ITEMCOMBINE_DB_BASE*, struct _combine_ex_item_accept_request_clzo*, struct _combine_ex_item_accept_result_zocl*, ItemCombineMgrMakeNewItems16_ptr);
        using ItemCombineMgrOnPlayerCreateCompleteProc18_ptr = void (WINAPIV*)(struct ItemCombineMgr*);
        using ItemCombineMgrOnPlayerCreateCompleteProc18_clbk = void (WINAPIV*)(struct ItemCombineMgr*, ItemCombineMgrOnPlayerCreateCompleteProc18_ptr);
        using ItemCombineMgrRequestCombineAcceptProcess20_ptr = char (WINAPIV*)(struct ItemCombineMgr*, struct _combine_ex_item_accept_request_clzo*, struct _combine_ex_item_accept_result_zocl*);
        using ItemCombineMgrRequestCombineAcceptProcess20_clbk = char (WINAPIV*)(struct ItemCombineMgr*, struct _combine_ex_item_accept_request_clzo*, struct _combine_ex_item_accept_result_zocl*, ItemCombineMgrRequestCombineAcceptProcess20_ptr);
        using ItemCombineMgrRequestCombineProcess22_ptr = char (WINAPIV*)(struct ItemCombineMgr*, struct _combine_ex_item_request_clzo*, struct _combine_ex_item_result_zocl*);
        using ItemCombineMgrRequestCombineProcess22_clbk = char (WINAPIV*)(struct ItemCombineMgr*, struct _combine_ex_item_request_clzo*, struct _combine_ex_item_result_zocl*, ItemCombineMgrRequestCombineProcess22_ptr);
        using ItemCombineMgrUpdateDB_CombineResult24_ptr = char (WINAPIV*)(struct ItemCombineMgr*, struct _combine_ex_item_result_zocl*);
        using ItemCombineMgrUpdateDB_CombineResult24_clbk = char (WINAPIV*)(struct ItemCombineMgr*, struct _combine_ex_item_result_zocl*, ItemCombineMgrUpdateDB_CombineResult24_ptr);
        
        using ItemCombineMgrdtor_ItemCombineMgr26_ptr = void (WINAPIV*)(struct ItemCombineMgr*);
        using ItemCombineMgrdtor_ItemCombineMgr26_clbk = void (WINAPIV*)(struct ItemCombineMgr*, ItemCombineMgrdtor_ItemCombineMgr26_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

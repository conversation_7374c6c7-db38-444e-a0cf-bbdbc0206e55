// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CNetIndexList.hpp>
#include <_DropItemGroupInfo.hpp>


START_ATF_NAMESPACE
    struct CItemDropMgr
    {
        _DropItemGroupInfo m_Pool[333];
        CNetIndexList m_listEmpty;
        CNetIndexList m_listTask;
        unsigned int m_dwTotalDropCount;
        CLogFile *m_pLogFile;
    public:
        bool Drop(int nCnt);
        bool FrontDrop();
        struct _DropItemGroupInfo* GetFrontPtr();
        bool PopFront();
    };
END_ATF_NAMESPACE

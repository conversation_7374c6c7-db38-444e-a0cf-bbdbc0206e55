// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CFileTimeSpanInfo.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Detail
        {
            extern ::std::array<hook_record, 5> CFileTimeSpan_functions;
        }; // end namespace Detail
    }; // end namespace ATL
END_ATF_NAMESPACE

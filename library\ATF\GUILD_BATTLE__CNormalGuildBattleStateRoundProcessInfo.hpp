// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateRoundProcess.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateRoundProcessctor_CNormalGuildBattleStateRoundProcess2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundProcess*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundProcessctor_CNormalGuildBattleStateRoundProcess2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundProcess*, GUILD_BATTLE__CNormalGuildBattleStateRoundProcessctor_CNormalGuildBattleStateRoundProcess2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundProcessEnter4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundProcess*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundProcessEnter4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundProcess*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundProcessEnter4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundProcessLoop6_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundProcess*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundProcessLoop6_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundProcess*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundProcessLoop6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateRoundProcessdtor_CNormalGuildBattleStateRoundProcess8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundProcess*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundProcessdtor_CNormalGuildBattleStateRoundProcess8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundProcess*, GUILD_BATTLE__CNormalGuildBattleStateRoundProcessdtor_CNormalGuildBattleStateRoundProcess8_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

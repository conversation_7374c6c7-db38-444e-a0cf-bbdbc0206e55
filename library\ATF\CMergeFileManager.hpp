// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMergeFile.hpp>


START_ATF_NAMESPACE
    struct CMergeFileManager
    {
        char mPath[256];
        unsigned int mPathNameLeng;
        unsigned int mMergeFileNum;
        CMergeFile *mMergeFile;
    public:
        uint32_t GetFileSize(char* arg_0);
        void InitMergeFile(char* arg_0);
        int IsExistFile(char* arg_0);
        struct _iobuf* LoadFileOffset(char* arg_0, char* arg_1);
        void ReleaseMergeFile();
        ~CMergeFileManager();
        int64_t dtor_CMergeFileManager();
    };
END_ATF_NAMESPACE

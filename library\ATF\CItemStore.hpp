// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMapData.hpp>
#include <_StoreList_fld.hpp>
#include <_base_fld.hpp>
#include <_buy_offer.hpp>
#include <_good_storage_info.hpp>
#include <_limit_amount_info.hpp>
#include <_limit_item_info.hpp>
#include <_sell_offer.hpp>
#include <_store_dummy.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CItemStore
    {
        bool m_bLive;
        int m_nIndex;
        unsigned int m_dwSecIndex;
        char m_byNpcRaceCode;
        CMapData *m_pExistMap;
        _store_dummy *m_pDum;
        _StoreList_fld *m_pRec;
        int m_nStorageItemNum;
        _good_storage_info *m_pStorageItem;
        unsigned __int64 m_dwLimitInitTime;
        bool m_bDBDataCheck;
        unsigned int m_dwDBSerial;
        int m_nLimitStorageItemNum;
        _limit_item_info *m_pLimitStorageItem;
        bool m_bUpdate;
        unsigned int m_dwLastTradeDalant;
        unsigned int m_dwLastTradeGold;
        unsigned int m_dwLastTradePoint;
        unsigned int m_dwLastTradeActPoint[3];
    public:
        CItemStore();
        void ctor_CItemStore();
        float CalcBuyPrice(char byTableCode, uint16_t wItemIndex, char* pbyMoneyUnit);
        int CalcSecIndex(float x, float z);
        int CalcSellPrice(int nGoodIndex, char* pbyMoneyUnit);
        int GetLastTradeActPoint(char byActCode);
        int GetLastTradeDalant();
        int GetLastTradeGold();
        int GetLastTradePoint();
        struct _limit_item_info* GetLimitItem(int nIndex);
        void GetLimitItemAmount(struct _limit_amount_info* pAmountInfo);
        char* GetNpcCode();
        bool GetNpcRaceCode(char* pbyRaceCode);
        struct _base_fld* GetNpcRecord();
        float* GetStorePos();
        bool Init(int nIndex, struct CMapData* pExistMap, struct _store_dummy* pDum, struct _base_fld* pRec);
        void InitLimitItemInfo();
        char IsBuy(char byOfferNum, struct _sell_offer* pOffer, float fDiscountRate, char byRace);
        char IsSell(char byOfferNum, struct _buy_offer* pOffer, unsigned int dwHasDalant, unsigned int dwHasGold, long double dHasPoint, unsigned int* dwHasActPoint, char* pbyActCode, float fDiscountRate, char byRace, char byGrade);
        void SetLimitItemInitTime();
        void SetZeroTradeMoney();
        void SubLimitItemNum(int nLimitItemIndex, int nSubNum);
        void UpdateLimitItemNum(bool bUpdate);
        ~CItemStore();
        void dtor_CItemStore();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<CItemStore, 0x78>(), "CItemStore");
END_ATF_NAMESPACE

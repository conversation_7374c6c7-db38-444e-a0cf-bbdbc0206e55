// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_post_send.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_post_sendctor__qry_case_post_send2_ptr = void (WINAPIV*)(struct _qry_case_post_send*);
        using _qry_case_post_sendctor__qry_case_post_send2_clbk = void (WINAPIV*)(struct _qry_case_post_send*, _qry_case_post_sendctor__qry_case_post_send2_ptr);
        using _qry_case_post_sendpushdata4_ptr = bool (WINAPIV*)(struct _qry_case_post_send*, unsigned int, char, unsigned int, unsigned int, char*, char*, char*, char*, struct _INVENKEY, uint64_t, unsigned int, unsigned int, uint64_t);
        using _qry_case_post_sendpushdata4_clbk = bool (WINAPIV*)(struct _qry_case_post_send*, unsigned int, char, unsigned int, unsigned int, char*, char*, char*, char*, struct _INVENKEY, uint64_t, unsigned int, unsigned int, uint64_t, _qry_case_post_sendpushdata4_ptr);
        using _qry_case_post_sendsize6_ptr = int (WINAPIV*)(struct _qry_case_post_send*);
        using _qry_case_post_sendsize6_clbk = int (WINAPIV*)(struct _qry_case_post_send*, _qry_case_post_sendsize6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

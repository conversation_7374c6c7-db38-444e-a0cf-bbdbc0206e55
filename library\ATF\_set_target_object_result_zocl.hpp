// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _set_target_object_result_zocl
    {
        char byRetCode;
        char byKind;
        char byID;
        unsigned int dwSerial;
        unsigned __int16 wHPRate;
        char byGrade;
        bool bForce;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

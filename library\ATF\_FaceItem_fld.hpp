// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _FaceItem_fld : _base_fld
    {
        int m_bExist;
        char m_strModle[64];
        char m_strName[64];
        int m_nKindClt;
        int m_nFixPart;
        char m_strCivil[64];
        int m_nDefEffType;
    };
END_ATF_NAMESPACE

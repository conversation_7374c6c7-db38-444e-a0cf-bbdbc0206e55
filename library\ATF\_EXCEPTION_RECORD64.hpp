// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _EXCEPTION_RECORD64
    {
        unsigned int ExceptionCode;
        unsigned int ExceptionFlags;
        unsigned __int64 ExceptionRecord;
        unsigned __int64 ExceptionAddress;
        unsigned int NumberParameters;
        unsigned int __unusedAlignment;
        unsigned __int64 ExceptionInformation[15];
    };
END_ATF_NAMESPACE

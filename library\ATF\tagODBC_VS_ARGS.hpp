// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$0BC19B3D86456E0AB84C9DE48A18D228.hpp>
#include <$56DFA0DB9AC98A350AE75AB227FBEC16.hpp>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagODBC_VS_ARGS
    {
        _GUID *pguidEvent;
        unsigned int dwFlags;
        $0BC19B3D86456E0AB84C9DE48A18D228 ___u2;
        $56DFA0DB9AC98A350AE75AB227FBEC16 ___u3;
        __int16 RetCode;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

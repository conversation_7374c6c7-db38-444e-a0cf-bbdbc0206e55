// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingDataBR.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDataBRctor_CNationSettingDataBR2_ptr = void (WINAPIV*)(struct CNationSettingDataBR*);
        using CNationSettingDataBRctor_CNationSettingDataBR2_clbk = void (WINAPIV*)(struct CNationSettingDataBR*, CNationSettingDataBRctor_CNationSettingDataBR2_ptr);
        using CNationSettingDataBRCheckEnterWorldRequest4_ptr = bool (WINAPIV*)(struct CNationSettingDataBR*, int, char*);
        using CNationSettingDataBRCheckEnterWorldRequest4_clbk = bool (WINAPIV*)(struct CNationSettingDataBR*, int, char*, CNationSettingDataBRCheckEnterWorldRequest4_ptr);
        using CNationSettingDataBRCreateBilling6_ptr = struct CBilling* (WINAPIV*)(struct CNationSettingDataBR*);
        using CNationSettingDataBRCreateBilling6_clbk = struct CBilling* (WINAPIV*)(struct CNationSettingDataBR*, CNationSettingDataBRCreateBilling6_ptr);
        using CNationSettingDataBRCreateWorker8_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataBR*);
        using CNationSettingDataBRCreateWorker8_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingDataBR*, CNationSettingDataBRCreateWorker8_ptr);
        using CNationSettingDataBRGetCashItemPrice10_ptr = int (WINAPIV*)(struct CNationSettingDataBR*, struct _CashShop_str_fld*);
        using CNationSettingDataBRGetCashItemPrice10_clbk = int (WINAPIV*)(struct CNationSettingDataBR*, struct _CashShop_str_fld*, CNationSettingDataBRGetCashItemPrice10_ptr);
        using CNationSettingDataBRGetItemName12_ptr = char* (WINAPIV*)(struct CNationSettingDataBR*, struct _NameTxt_fld*);
        using CNationSettingDataBRGetItemName12_clbk = char* (WINAPIV*)(struct CNationSettingDataBR*, struct _NameTxt_fld*, CNationSettingDataBRGetItemName12_ptr);
        using CNationSettingDataBRInit14_ptr = int (WINAPIV*)(struct CNationSettingDataBR*);
        using CNationSettingDataBRInit14_clbk = int (WINAPIV*)(struct CNationSettingDataBR*, CNationSettingDataBRInit14_ptr);
        using CNationSettingDataBRLoop16_ptr = void (WINAPIV*)(struct CNationSettingDataBR*);
        using CNationSettingDataBRLoop16_clbk = void (WINAPIV*)(struct CNationSettingDataBR*, CNationSettingDataBRLoop16_ptr);
        using CNationSettingDataBRReadSystemPass18_ptr = bool (WINAPIV*)(struct CNationSettingDataBR*);
        using CNationSettingDataBRReadSystemPass18_clbk = bool (WINAPIV*)(struct CNationSettingDataBR*, CNationSettingDataBRReadSystemPass18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

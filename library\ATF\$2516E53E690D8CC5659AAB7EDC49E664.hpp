// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $2516E53E690D8CC5659AAB7EDC49E664
    {
        BYTE gap0[8];
        unsigned int *puintVal;
    };    
    static_assert(ATF::checkSize<$2516E53E690D8CC5659AAB7EDC49E664, 16>(), "$2516E53E690D8CC5659AAB7EDC49E664");
END_ATF_NAMESPACE

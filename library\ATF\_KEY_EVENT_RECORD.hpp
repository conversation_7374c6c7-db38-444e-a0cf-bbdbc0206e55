// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$D73D647711E0D18BB6EFDDF78E7AEEC6.hpp>


START_ATF_NAMESPACE
    struct _KEY_EVENT_RECORD
    {
        int bKeyDown;
        unsigned __int16 wRepeatCount;
        unsigned __int16 wVirtualKeyCode;
        unsigned __int16 wVirtualScanCode;
        $D73D647711E0D18BB6EFDDF78E7AEEC6 uChar;
        unsigned int dwControlKeyState;
    };
END_ATF_NAMESPACE

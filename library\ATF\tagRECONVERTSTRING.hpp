// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagRECONVERTSTRING
    {
        unsigned int dwSize;
        unsigned int dwVersion;
        unsigned int dwStrLen;
        unsigned int dwStrOffset;
        unsigned int dwCompStrLen;
        unsigned int dwCompStrOffset;
        unsigned int dwTargetStrLen;
        unsigned int dwTargetStrOffset;
    };
END_ATF_NAMESPACE

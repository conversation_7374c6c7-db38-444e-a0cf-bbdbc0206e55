// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <LOG_TYPE_DB_TASK_TYPE.hpp>


START_ATF_NAMESPACE
    struct CLogTypeDBTask
    {
        unsigned int m_dwInx;
        bool m_bUse;
        bool m_bLoad;
        LOG_TYPE_DB_TASK_TYPE m_eQueryType;
        char m_byDBQueryRet;
        char m_byProcRet;
        char *m_pcData;
    public:
        CLogTypeDBTask();
        void ctor_CLogTypeDBTask();
        void Clear();
        char GetDBRet();
        char* GetData();
        unsigned int GetInx();
        char GetProcRet();
        int GetQueryType();
        bool Init(unsigned int dwInx, unsigned int uiSize);
        bool Set(char byQueryType, char* pcData, uint16_t wSize);
        void SetComplete();
        void SetEmpty();
        void SetRet(char byDBRet, char byProcRet);
        void SetUse();
        ~CLogTypeDBTask();
        void dtor_CLogTypeDBTask();
    };
END_ATF_NAMESPACE

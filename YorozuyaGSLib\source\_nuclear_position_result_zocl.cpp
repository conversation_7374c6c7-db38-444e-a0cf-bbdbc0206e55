#include <_nuclear_position_result_zocl.hpp>


START_ATF_NAMESPACE
    _nuclear_position_result_zocl::_nuclear_position_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_position_result_zocl*);
        (org_ptr(0x14013e250L))(this);
    };
    void _nuclear_position_result_zocl::ctor__nuclear_position_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _nuclear_position_result_zocl*);
        (org_ptr(0x14013e250L))(this);
    };
END_ATF_NAMESPACE

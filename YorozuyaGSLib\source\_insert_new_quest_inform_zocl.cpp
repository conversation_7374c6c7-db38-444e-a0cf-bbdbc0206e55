#include <_insert_new_quest_inform_zocl.hpp>


START_ATF_NAMESPACE
    _insert_new_quest_inform_zocl::_insert_new_quest_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _insert_new_quest_inform_zocl*);
        (org_ptr(0x1400efe60L))(this);
    };
    void _insert_new_quest_inform_zocl::ctor__insert_new_quest_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _insert_new_quest_inform_zocl*);
        (org_ptr(0x1400efe60L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyCriticalSectionVtbl.hpp>
#include <_RTL_CRITICAL_SECTION.hpp>


START_ATF_NAMESPACE
    struct CMyCriticalSection
    {
        CMyCriticalSectionVtbl *vfptr;
        _RTL_CRITICAL_SECTION m_cs;
    public:
        CMyCriticalSection();
        void ctor_CMyCriticalSection();
        void Lock();
        void Unlock();
        ~CMyCriticalSection();
        void dtor_CMyCriticalSection();
    };    
    static_assert(ATF::checkSize<CMyCriticalSection, 48>(), "CMyCriticalSection");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$C25A9065B81808D2E7112BE513398F8C.hpp>


START_ATF_NAMESPACE
    union $997E4CC073C079F3CEE01C460B9DB2EB
    {
        unsigned int dwGBitMask;
        unsigned int dwUBitMask;
        unsigned int dwZBitMask;
        unsigned int dwBumpDvBitMask;
        $C25A9065B81808D2E7112BE513398F8C MultiSampleCaps;
    };    
    static_assert(ATF::checkSize<$997E4CC073C079F3CEE01C460B9DB2EB, 4>(), "$997E4CC073C079F3CEE01C460B9DB2EB");
END_ATF_NAMESPACE

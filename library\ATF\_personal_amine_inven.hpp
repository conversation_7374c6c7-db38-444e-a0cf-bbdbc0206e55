// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _personal_amine_inven
    {
        struct __list
        {
            int lK;
            char by<PERSON>um;
        };
        __list list[40];
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_personal_amine_inven, 320>(), "_personal_amine_inven");
END_ATF_NAMESPACE

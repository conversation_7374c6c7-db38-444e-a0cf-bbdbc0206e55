// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUserDB.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CUserDBAlive_Char_Complete2_ptr = void (WINAPIV*)(struct CUserDB*, char, char, unsigned int, struct _REGED*);
        using CUserDBAlive_Char_Complete2_clbk = void (WINAPIV*)(struct CUserDB*, char, char, unsigned int, struct _REGED*, CUserDBAlive_Char_Complete2_ptr);
        using CUserDBAlive_Char_Request4_ptr = bool (WINAPIV*)(struct CUserDB*, char, unsigned int, char*, char);
        using CUserDBAlive_Char_Request4_clbk = bool (WINAPIV*)(struct CUserDB*, char, unsigned int, char*, char, CUserDBAlive_Char_Request4_ptr);
        
        using <PERSON>UserDBctor_CUserDB6_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBctor_CUserDB6_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBctor_CUserDB6_ptr);
        using CUserDBCalcRadarDelay8_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBCalcRadarDelay8_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBCalcRadarDelay8_ptr);
        using CUserDBCheckDQSLoadCharacterData10_ptr = bool (WINAPIV*)(struct _AVATOR_DATA*);
        using CUserDBCheckDQSLoadCharacterData10_clbk = bool (WINAPIV*)(struct _AVATOR_DATA*, CUserDBCheckDQSLoadCharacterData10_ptr);
        using CUserDBClearBillingData12_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBClearBillingData12_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBClearBillingData12_ptr);
        using CUserDBCont_UserSave_Complete14_ptr = void (WINAPIV*)(struct CUserDB*, char, struct _AVATOR_DATA*);
        using CUserDBCont_UserSave_Complete14_clbk = void (WINAPIV*)(struct CUserDB*, char, struct _AVATOR_DATA*, CUserDBCont_UserSave_Complete14_ptr);
        using CUserDBDataValidCheckRevise16_ptr = bool (WINAPIV*)(struct _AVATOR_DATA*, bool*);
        using CUserDBDataValidCheckRevise16_clbk = bool (WINAPIV*)(struct _AVATOR_DATA*, bool*, CUserDBDataValidCheckRevise16_ptr);
        using CUserDBDelPostData18_ptr = void (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBDelPostData18_clbk = void (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBDelPostData18_ptr);
        using CUserDBDelete_Char_Complete20_ptr = void (WINAPIV*)(struct CUserDB*, char, char);
        using CUserDBDelete_Char_Complete20_clbk = void (WINAPIV*)(struct CUserDB*, char, char, CUserDBDelete_Char_Complete20_ptr);
        using CUserDBDelete_Char_Request22_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBDelete_Char_Request22_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBDelete_Char_Request22_ptr);
        using CUserDBDummyCreate24_ptr = void (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBDummyCreate24_clbk = void (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBDummyCreate24_ptr);
        using CUserDBEnter_Account26_ptr = bool (WINAPIV*)(struct CUserDB*, unsigned int, unsigned int, unsigned int, unsigned int*);
        using CUserDBEnter_Account26_clbk = bool (WINAPIV*)(struct CUserDB*, unsigned int, unsigned int, unsigned int, unsigned int*, CUserDBEnter_Account26_ptr);
        using CUserDBExit_Account_Complete28_ptr = void (WINAPIV*)(struct CUserDB*, char);
        using CUserDBExit_Account_Complete28_clbk = void (WINAPIV*)(struct CUserDB*, char, CUserDBExit_Account_Complete28_ptr);
        using CUserDBExit_Account_Request30_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBExit_Account_Request30_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBExit_Account_Request30_ptr);
        using CUserDBFirstSettingData32_ptr = bool (WINAPIV*)(struct CUserDB*);
        using CUserDBFirstSettingData32_clbk = bool (WINAPIV*)(struct CUserDB*, CUserDBFirstSettingData32_ptr);
        using CUserDBForceCloseCommand34_ptr = void (WINAPIV*)(struct CUserDB*, char, unsigned int, bool, char*);
        using CUserDBForceCloseCommand34_clbk = void (WINAPIV*)(struct CUserDB*, char, unsigned int, bool, char*, CUserDBForceCloseCommand34_ptr);
        using CUserDBGetActPoint36_ptr = unsigned int (WINAPIV*)(struct CUserDB*, char);
        using CUserDBGetActPoint36_clbk = unsigned int (WINAPIV*)(struct CUserDB*, char, CUserDBGetActPoint36_ptr);
        using CUserDBGetBillingType38_ptr = int (WINAPIV*)(struct CUserDB*);
        using CUserDBGetBillingType38_clbk = int (WINAPIV*)(struct CUserDB*, CUserDBGetBillingType38_ptr);
        using CUserDBGetPtrActPoint40_ptr = unsigned int* (WINAPIV*)(struct CUserDB*);
        using CUserDBGetPtrActPoint40_clbk = unsigned int* (WINAPIV*)(struct CUserDB*, CUserDBGetPtrActPoint40_ptr);
        using CUserDBInform_For_Exit_By_FireguardBlock42_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBInform_For_Exit_By_FireguardBlock42_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBInform_For_Exit_By_FireguardBlock42_ptr);
        using CUserDBInit44_ptr = void (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBInit44_clbk = void (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBInit44_ptr);
        using CUserDBInitClass46_ptr = bool (WINAPIV*)(struct CUserDB*, char*);
        using CUserDBInitClass46_clbk = bool (WINAPIV*)(struct CUserDB*, char*, CUserDBInitClass46_ptr);
        using CUserDBInsert_Char_Complete48_ptr = void (WINAPIV*)(struct CUserDB*, char, struct _REGED_AVATOR_DB*);
        using CUserDBInsert_Char_Complete48_clbk = void (WINAPIV*)(struct CUserDB*, char, struct _REGED_AVATOR_DB*, CUserDBInsert_Char_Complete48_ptr);
        using CUserDBInsert_Char_Request50_ptr = bool (WINAPIV*)(struct CUserDB*, char*, char, char, char*, unsigned int);
        using CUserDBInsert_Char_Request50_clbk = bool (WINAPIV*)(struct CUserDB*, char*, char, char, char*, unsigned int, CUserDBInsert_Char_Request50_ptr);
        using CUserDBIsContPushBefore52_ptr = struct _AVATOR_DATA* (WINAPIV*)(struct CUserDB*);
        using CUserDBIsContPushBefore52_clbk = struct _AVATOR_DATA* (WINAPIV*)(struct CUserDB*, CUserDBIsContPushBefore52_ptr);
        using CUserDBIsExistRequestMoveCharacterList54_ptr = char (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBIsExistRequestMoveCharacterList54_clbk = char (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBIsExistRequestMoveCharacterList54_ptr);
        using CUserDBIsReturnPostUpdate56_ptr = bool (WINAPIV*)(struct CUserDB*);
        using CUserDBIsReturnPostUpdate56_clbk = bool (WINAPIV*)(struct CUserDB*, CUserDBIsReturnPostUpdate56_ptr);
        using CUserDBLobby_Char_Complete58_ptr = void (WINAPIV*)(struct CUserDB*, char);
        using CUserDBLobby_Char_Complete58_clbk = void (WINAPIV*)(struct CUserDB*, char, CUserDBLobby_Char_Complete58_ptr);
        using CUserDBLobby_Char_Request60_ptr = bool (WINAPIV*)(struct CUserDB*);
        using CUserDBLobby_Char_Request60_clbk = bool (WINAPIV*)(struct CUserDB*, CUserDBLobby_Char_Request60_ptr);
        using CUserDBOnLoop_Static62_ptr = void (WINAPIV*)();
        using CUserDBOnLoop_Static62_clbk = void (WINAPIV*)(CUserDBOnLoop_Static62_ptr);
        using CUserDBParamInit64_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBParamInit64_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBParamInit64_ptr);
        using CUserDBReRangeClientIndex66_ptr = void (WINAPIV*)(struct _AVATOR_DATA*);
        using CUserDBReRangeClientIndex66_clbk = void (WINAPIV*)(struct _AVATOR_DATA*, CUserDBReRangeClientIndex66_ptr);
        using CUserDBReged_Char_Complete68_ptr = void (WINAPIV*)(struct CUserDB*, char, struct _REGED*, struct _NOT_ARRANGED_AVATOR_DB*);
        using CUserDBReged_Char_Complete68_clbk = void (WINAPIV*)(struct CUserDB*, char, struct _REGED*, struct _NOT_ARRANGED_AVATOR_DB*, CUserDBReged_Char_Complete68_ptr);
        using CUserDBReged_Char_Request70_ptr = bool (WINAPIV*)(struct CUserDB*);
        using CUserDBReged_Char_Request70_clbk = bool (WINAPIV*)(struct CUserDB*, CUserDBReged_Char_Request70_ptr);
        using CUserDBSelect_Char_Complete72_ptr = void (WINAPIV*)(struct CUserDB*, char, struct _AVATOR_DATA*, bool*, unsigned int, unsigned int, unsigned int, bool*, char, long double, long double, bool, bool*, char);
        using CUserDBSelect_Char_Complete72_clbk = void (WINAPIV*)(struct CUserDB*, char, struct _AVATOR_DATA*, bool*, unsigned int, unsigned int, unsigned int, bool*, char, long double, long double, bool, bool*, char, CUserDBSelect_Char_Complete72_ptr);
        using CUserDBSelect_Char_Request74_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBSelect_Char_Request74_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBSelect_Char_Request74_ptr);
        using CUserDBSendMsgAccount_UILockRefresh_Update76_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBSendMsgAccount_UILockRefresh_Update76_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBSendMsgAccount_UILockRefresh_Update76_ptr);
        using CUserDBSendMsg_BillingInfo78_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBSendMsg_BillingInfo78_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBSendMsg_BillingInfo78_ptr);
        using CUserDBSendMsg_Inform_UILock80_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBSendMsg_Inform_UILock80_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBSendMsg_Inform_UILock80_ptr);
        using CUserDBSetActPoint82_ptr = void (WINAPIV*)(struct CUserDB*, char, unsigned int);
        using CUserDBSetActPoint82_clbk = void (WINAPIV*)(struct CUserDB*, char, unsigned int, CUserDBSetActPoint82_ptr);
        using CUserDBSetBillingData84_ptr = void (WINAPIV*)(struct CUserDB*, struct _BILLING_INFO*);
        using CUserDBSetBillingData84_clbk = void (WINAPIV*)(struct CUserDB*, struct _BILLING_INFO*, CUserDBSetBillingData84_ptr);
        using CUserDBSetBillingData86_ptr = void (WINAPIV*)(struct CUserDB*, char*, int16_t, int, struct _SYSTEMTIME*);
        using CUserDBSetBillingData86_clbk = void (WINAPIV*)(struct CUserDB*, char*, int16_t, int, struct _SYSTEMTIME*, CUserDBSetBillingData86_ptr);
        using CUserDBSetBillingNoLogout88_ptr = void (WINAPIV*)(struct CUserDB*, bool);
        using CUserDBSetBillingNoLogout88_clbk = void (WINAPIV*)(struct CUserDB*, bool, CUserDBSetBillingNoLogout88_ptr);
        using CUserDBSetChatLock90_ptr = void (WINAPIV*)(struct CUserDB*, bool);
        using CUserDBSetChatLock90_clbk = void (WINAPIV*)(struct CUserDB*, bool, CUserDBSetChatLock90_ptr);
        using CUserDBSetDBPostData92_ptr = void (WINAPIV*)(struct CUserDB*, int, unsigned int, int, char, int, uint64_t, unsigned int, unsigned int, bool, uint64_t);
        using CUserDBSetDBPostData92_clbk = void (WINAPIV*)(struct CUserDB*, int, unsigned int, int, char, int, uint64_t, unsigned int, unsigned int, bool, uint64_t, CUserDBSetDBPostData92_ptr);
        using CUserDBSetNewDBPostData94_ptr = void (WINAPIV*)(struct CUserDB*, int, unsigned int, int, char, char*, char*, char*, char*, int, uint64_t, unsigned int, unsigned int, uint64_t);
        using CUserDBSetNewDBPostData94_clbk = void (WINAPIV*)(struct CUserDB*, int, unsigned int, int, char, char*, char*, char*, char*, int, uint64_t, unsigned int, unsigned int, uint64_t, CUserDBSetNewDBPostData94_ptr);
        using CUserDBSetRadarDelay96_ptr = void (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBSetRadarDelay96_clbk = void (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBSetRadarDelay96_ptr);
        using CUserDBSetRemainTime98_ptr = void (WINAPIV*)(struct CUserDB*, int);
        using CUserDBSetRemainTime98_clbk = void (WINAPIV*)(struct CUserDB*, int, CUserDBSetRemainTime98_ptr);
        using CUserDBSetWorldCLID100_ptr = void (WINAPIV*)(struct CUserDB*, unsigned int, unsigned int*);
        using CUserDBSetWorldCLID100_clbk = void (WINAPIV*)(struct CUserDB*, unsigned int, unsigned int*, CUserDBSetWorldCLID100_ptr);
        using CUserDBSetting_Class102_ptr = bool (WINAPIV*)(struct CUserDB*, char*);
        using CUserDBSetting_Class102_clbk = bool (WINAPIV*)(struct CUserDB*, char*, CUserDBSetting_Class102_ptr);
        using CUserDBStartFieldMode104_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBStartFieldMode104_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBStartFieldMode104_ptr);
        using CUserDBTotalPlayMinCheck106_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBTotalPlayMinCheck106_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBTotalPlayMinCheck106_ptr);
        using CUserDBUILockInfo_Init108_ptr = void (WINAPIV*)(struct CUserDB*, char*);
        using CUserDBUILockInfo_Init108_clbk = void (WINAPIV*)(struct CUserDB*, char*, CUserDBUILockInfo_Init108_ptr);
        using CUserDBUILockInfo_Update110_ptr = void (WINAPIV*)(struct CUserDB*, char*);
        using CUserDBUILockInfo_Update110_clbk = void (WINAPIV*)(struct CUserDB*, char*, CUserDBUILockInfo_Update110_ptr);
        using CUserDBUpdateContUserSave112_ptr = bool (WINAPIV*)(struct CUserDB*, bool);
        using CUserDBUpdateContUserSave112_clbk = bool (WINAPIV*)(struct CUserDB*, bool, CUserDBUpdateContUserSave112_ptr);
        using CUserDBUpdate_AddBuddy114_ptr = bool (WINAPIV*)(struct CUserDB*, char, unsigned int, char*);
        using CUserDBUpdate_AddBuddy114_clbk = bool (WINAPIV*)(struct CUserDB*, char, unsigned int, char*, CUserDBUpdate_AddBuddy114_ptr);
        using CUserDBUpdate_AlterPvPCashBag116_ptr = bool (WINAPIV*)(struct CUserDB*, long double);
        using CUserDBUpdate_AlterPvPCashBag116_clbk = bool (WINAPIV*)(struct CUserDB*, long double, CUserDBUpdate_AlterPvPCashBag116_ptr);
        using CUserDBUpdate_AlterPvPPoint118_ptr = bool (WINAPIV*)(struct CUserDB*, long double);
        using CUserDBUpdate_AlterPvPPoint118_clbk = bool (WINAPIV*)(struct CUserDB*, long double, CUserDBUpdate_AlterPvPPoint118_ptr);
        using CUserDBUpdate_AutoTradeAllClear120_ptr = bool (WINAPIV*)(struct CUserDB*);
        using CUserDBUpdate_AutoTradeAllClear120_clbk = bool (WINAPIV*)(struct CUserDB*, CUserDBUpdate_AutoTradeAllClear120_ptr);
        using CUserDBUpdate_BagNum122_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBUpdate_BagNum122_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBUpdate_BagNum122_ptr);
        using CUserDBUpdate_Bind124_ptr = bool (WINAPIV*)(struct CUserDB*, char*, char*, bool);
        using CUserDBUpdate_Bind124_clbk = bool (WINAPIV*)(struct CUserDB*, char*, char*, bool, CUserDBUpdate_Bind124_ptr);
        using CUserDBUpdate_BossCryMsg126_ptr = void (WINAPIV*)(struct CUserDB*, char, char*);
        using CUserDBUpdate_BossCryMsg126_clbk = void (WINAPIV*)(struct CUserDB*, char, char*, CUserDBUpdate_BossCryMsg126_ptr);
        using CUserDBUpdate_Class128_ptr = bool (WINAPIV*)(struct CUserDB*, char*, char, uint16_t);
        using CUserDBUpdate_Class128_clbk = bool (WINAPIV*)(struct CUserDB*, char*, char, uint16_t, CUserDBUpdate_Class128_ptr);
        using CUserDBUpdate_CombineExResult_Pop130_ptr = bool (WINAPIV*)(struct CUserDB*);
        using CUserDBUpdate_CombineExResult_Pop130_clbk = bool (WINAPIV*)(struct CUserDB*, CUserDBUpdate_CombineExResult_Pop130_ptr);
        using CUserDBUpdate_CombineExResult_Push132_ptr = bool (WINAPIV*)(struct CUserDB*, struct _ITEMCOMBINE_DB_BASE*);
        using CUserDBUpdate_CombineExResult_Push132_clbk = bool (WINAPIV*)(struct CUserDB*, struct _ITEMCOMBINE_DB_BASE*, CUserDBUpdate_CombineExResult_Push132_ptr);
        using CUserDBUpdate_CopyAll134_ptr = bool (WINAPIV*)(struct CUserDB*, struct _AVATOR_DATA*);
        using CUserDBUpdate_CopyAll134_clbk = bool (WINAPIV*)(struct CUserDB*, struct _AVATOR_DATA*, CUserDBUpdate_CopyAll134_ptr);
        using CUserDBUpdate_CuttingEmpty136_ptr = bool (WINAPIV*)(struct CUserDB*);
        using CUserDBUpdate_CuttingEmpty136_clbk = bool (WINAPIV*)(struct CUserDB*, CUserDBUpdate_CuttingEmpty136_ptr);
        using CUserDBUpdate_CuttingPush138_ptr = bool (WINAPIV*)(struct CUserDB*, char, struct _CUTTING_DB_BASE::_LIST*);
        using CUserDBUpdate_CuttingPush138_clbk = bool (WINAPIV*)(struct CUserDB*, char, struct _CUTTING_DB_BASE::_LIST*, CUserDBUpdate_CuttingPush138_ptr);
        using CUserDBUpdate_CuttingTrans140_ptr = bool (WINAPIV*)(struct CUserDB*, uint16_t, uint16_t);
        using CUserDBUpdate_CuttingTrans140_clbk = bool (WINAPIV*)(struct CUserDB*, uint16_t, uint16_t, CUserDBUpdate_CuttingTrans140_ptr);
        using CUserDBUpdate_DelBuddy142_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBUpdate_DelBuddy142_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBUpdate_DelBuddy142_ptr);
        using CUserDBUpdate_DelPost144_ptr = bool (WINAPIV*)(struct CUserDB*, unsigned int, int);
        using CUserDBUpdate_DelPost144_clbk = bool (WINAPIV*)(struct CUserDB*, unsigned int, int, CUserDBUpdate_DelPost144_ptr);
        using CUserDBUpdate_Exp146_ptr = bool (WINAPIV*)(struct CUserDB*, long double);
        using CUserDBUpdate_Exp146_clbk = bool (WINAPIV*)(struct CUserDB*, long double, CUserDBUpdate_Exp146_ptr);
        using CUserDBUpdate_ExtTrunkSlotNum148_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBUpdate_ExtTrunkSlotNum148_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBUpdate_ExtTrunkSlotNum148_ptr);
        using CUserDBUpdate_ItemAdd150_ptr = bool (WINAPIV*)(struct CUserDB*, char, char, struct _STORAGE_LIST::_db_con*, bool);
        using CUserDBUpdate_ItemAdd150_clbk = bool (WINAPIV*)(struct CUserDB*, char, char, struct _STORAGE_LIST::_db_con*, bool, CUserDBUpdate_ItemAdd150_ptr);
        using CUserDBUpdate_ItemDelete152_ptr = bool (WINAPIV*)(struct CUserDB*, char, char, bool);
        using CUserDBUpdate_ItemDelete152_clbk = bool (WINAPIV*)(struct CUserDB*, char, char, bool, CUserDBUpdate_ItemDelete152_ptr);
        using CUserDBUpdate_ItemDur154_ptr = bool (WINAPIV*)(struct CUserDB*, char, char, uint64_t, bool);
        using CUserDBUpdate_ItemDur154_clbk = bool (WINAPIV*)(struct CUserDB*, char, char, uint64_t, bool, CUserDBUpdate_ItemDur154_ptr);
        using CUserDBUpdate_ItemSlot156_ptr = bool (WINAPIV*)(struct CUserDB*, char, char, char);
        using CUserDBUpdate_ItemSlot156_clbk = bool (WINAPIV*)(struct CUserDB*, char, char, char, CUserDBUpdate_ItemSlot156_ptr);
        using CUserDBUpdate_ItemUpgrade158_ptr = bool (WINAPIV*)(struct CUserDB*, char, char, unsigned int, bool);
        using CUserDBUpdate_ItemUpgrade158_clbk = bool (WINAPIV*)(struct CUserDB*, char, char, unsigned int, bool, CUserDBUpdate_ItemUpgrade158_ptr);
        using CUserDBUpdate_LastAttBuff160_ptr = void (WINAPIV*)(struct CUserDB*, bool);
        using CUserDBUpdate_LastAttBuff160_clbk = void (WINAPIV*)(struct CUserDB*, bool, CUserDBUpdate_LastAttBuff160_ptr);
        using CUserDBUpdate_Level162_ptr = bool (WINAPIV*)(struct CUserDB*, char, long double);
        using CUserDBUpdate_Level162_clbk = bool (WINAPIV*)(struct CUserDB*, char, long double, CUserDBUpdate_Level162_ptr);
        using CUserDBUpdate_LinkBoardLock164_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBUpdate_LinkBoardLock164_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBUpdate_LinkBoardLock164_ptr);
        using CUserDBUpdate_LinkBoardSlot166_ptr = bool (WINAPIV*)(struct CUserDB*, char, char, uint16_t);
        using CUserDBUpdate_LinkBoardSlot166_clbk = bool (WINAPIV*)(struct CUserDB*, char, char, uint16_t, CUserDBUpdate_LinkBoardSlot166_ptr);
        using CUserDBUpdate_LossExp168_ptr = bool (WINAPIV*)(struct CUserDB*, long double);
        using CUserDBUpdate_LossExp168_clbk = bool (WINAPIV*)(struct CUserDB*, long double, CUserDBUpdate_LossExp168_ptr);
        using CUserDBUpdate_Macro170_ptr = bool (WINAPIV*)(struct CUserDB*, char*);
        using CUserDBUpdate_Macro170_clbk = bool (WINAPIV*)(struct CUserDB*, char*, CUserDBUpdate_Macro170_ptr);
        using CUserDBUpdate_Map172_ptr = bool (WINAPIV*)(struct CUserDB*, char, float*);
        using CUserDBUpdate_Map172_clbk = bool (WINAPIV*)(struct CUserDB*, char, float*, CUserDBUpdate_Map172_ptr);
        using CUserDBUpdate_MaxLevel174_ptr = void (WINAPIV*)(struct CUserDB*, char);
        using CUserDBUpdate_MaxLevel174_clbk = void (WINAPIV*)(struct CUserDB*, char, CUserDBUpdate_MaxLevel174_ptr);
        using CUserDBUpdate_Money176_ptr = bool (WINAPIV*)(struct CUserDB*, unsigned int, unsigned int);
        using CUserDBUpdate_Money176_clbk = bool (WINAPIV*)(struct CUserDB*, unsigned int, unsigned int, CUserDBUpdate_Money176_ptr);
        using CUserDBUpdate_NPCQuestHistory178_ptr = bool (WINAPIV*)(struct CUserDB*, char, struct _QUEST_DB_BASE::_NPC_QUEST_HISTORY*);
        using CUserDBUpdate_NPCQuestHistory178_clbk = bool (WINAPIV*)(struct CUserDB*, char, struct _QUEST_DB_BASE::_NPC_QUEST_HISTORY*, CUserDBUpdate_NPCQuestHistory178_ptr);
        using CUserDBUpdate_Param180_ptr = bool (WINAPIV*)(struct CUserDB*, struct _EXIT_ALTER_PARAM*);
        using CUserDBUpdate_Param180_clbk = bool (WINAPIV*)(struct CUserDB*, struct _EXIT_ALTER_PARAM*, CUserDBUpdate_Param180_ptr);
        using CUserDBUpdate_PlayTime182_ptr = bool (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBUpdate_PlayTime182_clbk = bool (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBUpdate_PlayTime182_ptr);
        using CUserDBUpdate_Post184_ptr = void (WINAPIV*)(struct CUserDB*, int, unsigned int, int, char, int, uint64_t, unsigned int, unsigned int, uint64_t);
        using CUserDBUpdate_Post184_clbk = void (WINAPIV*)(struct CUserDB*, int, unsigned int, int, char, int, uint64_t, unsigned int, unsigned int, uint64_t, CUserDBUpdate_Post184_ptr);
        using CUserDBUpdate_PotionNextUseTime186_ptr = void (WINAPIV*)(struct CUserDB*, char, unsigned int);
        using CUserDBUpdate_PotionNextUseTime186_clbk = void (WINAPIV*)(struct CUserDB*, char, unsigned int, CUserDBUpdate_PotionNextUseTime186_ptr);
        using CUserDBUpdate_PvpPointLeak188_ptr = void (WINAPIV*)(struct CUserDB*, long double);
        using CUserDBUpdate_PvpPointLeak188_clbk = void (WINAPIV*)(struct CUserDB*, long double, CUserDBUpdate_PvpPointLeak188_ptr);
        using CUserDBUpdate_QuestDelete190_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBUpdate_QuestDelete190_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBUpdate_QuestDelete190_ptr);
        using CUserDBUpdate_QuestInsert192_ptr = bool (WINAPIV*)(struct CUserDB*, char, struct _QUEST_DB_BASE::_LIST*);
        using CUserDBUpdate_QuestInsert192_clbk = bool (WINAPIV*)(struct CUserDB*, char, struct _QUEST_DB_BASE::_LIST*, CUserDBUpdate_QuestInsert192_ptr);
        using CUserDBUpdate_QuestUpdate194_ptr = bool (WINAPIV*)(struct CUserDB*, char, struct _QUEST_DB_BASE::_LIST*, bool);
        using CUserDBUpdate_QuestUpdate194_clbk = bool (WINAPIV*)(struct CUserDB*, char, struct _QUEST_DB_BASE::_LIST*, bool, CUserDBUpdate_QuestUpdate194_ptr);
        using CUserDBUpdate_RaceVoteInfoInit196_ptr = bool (WINAPIV*)(struct CUserDB*);
        using CUserDBUpdate_RaceVoteInfoInit196_clbk = bool (WINAPIV*)(struct CUserDB*, CUserDBUpdate_RaceVoteInfoInit196_ptr);
        using CUserDBUpdate_ReturnPost198_ptr = void (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBUpdate_ReturnPost198_clbk = void (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBUpdate_ReturnPost198_ptr);
        using CUserDBUpdate_SFContDelete200_ptr = bool (WINAPIV*)(struct CUserDB*, char, char);
        using CUserDBUpdate_SFContDelete200_clbk = bool (WINAPIV*)(struct CUserDB*, char, char, CUserDBUpdate_SFContDelete200_ptr);
        using CUserDBUpdate_SFContInsert202_ptr = bool (WINAPIV*)(struct CUserDB*, char, char, char, uint16_t, char, uint16_t);
        using CUserDBUpdate_SFContInsert202_clbk = bool (WINAPIV*)(struct CUserDB*, char, char, char, uint16_t, char, uint16_t, CUserDBUpdate_SFContInsert202_ptr);
        using CUserDBUpdate_SFContUpdate204_ptr = bool (WINAPIV*)(struct CUserDB*, char, char, uint16_t, bool);
        using CUserDBUpdate_SFContUpdate204_clbk = bool (WINAPIV*)(struct CUserDB*, char, char, uint16_t, bool, CUserDBUpdate_SFContUpdate204_ptr);
        using CUserDBUpdate_StartNPCQuestHistory206_ptr = bool (WINAPIV*)(struct CUserDB*, char, struct _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY*);
        using CUserDBUpdate_StartNPCQuestHistory206_clbk = bool (WINAPIV*)(struct CUserDB*, char, struct _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY*, CUserDBUpdate_StartNPCQuestHistory206_ptr);
        using CUserDBUpdate_Stat208_ptr = bool (WINAPIV*)(struct CUserDB*, char, unsigned int, bool);
        using CUserDBUpdate_Stat208_clbk = bool (WINAPIV*)(struct CUserDB*, char, unsigned int, bool, CUserDBUpdate_Stat208_ptr);
        using CUserDBUpdate_TakeLastCriTicket210_ptr = bool (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBUpdate_TakeLastCriTicket210_clbk = bool (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBUpdate_TakeLastCriTicket210_ptr);
        using CUserDBUpdate_TakeLastMentalTicket212_ptr = bool (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBUpdate_TakeLastMentalTicket212_clbk = bool (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBUpdate_TakeLastMentalTicket212_ptr);
        using CUserDBUpdate_TrunkHint214_ptr = bool (WINAPIV*)(struct CUserDB*, char, char*);
        using CUserDBUpdate_TrunkHint214_clbk = bool (WINAPIV*)(struct CUserDB*, char, char*, CUserDBUpdate_TrunkHint214_ptr);
        using CUserDBUpdate_TrunkMoney216_ptr = bool (WINAPIV*)(struct CUserDB*, long double, long double);
        using CUserDBUpdate_TrunkMoney216_clbk = bool (WINAPIV*)(struct CUserDB*, long double, long double, CUserDBUpdate_TrunkMoney216_ptr);
        using CUserDBUpdate_TrunkPassword218_ptr = bool (WINAPIV*)(struct CUserDB*, char*);
        using CUserDBUpdate_TrunkPassword218_clbk = bool (WINAPIV*)(struct CUserDB*, char*, CUserDBUpdate_TrunkPassword218_ptr);
        using CUserDBUpdate_TrunkSlotNum220_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBUpdate_TrunkSlotNum220_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBUpdate_TrunkSlotNum220_ptr);
        using CUserDBUpdate_UnitData222_ptr = bool (WINAPIV*)(struct CUserDB*, char, struct _UNIT_DB_BASE::_LIST*);
        using CUserDBUpdate_UnitData222_clbk = bool (WINAPIV*)(struct CUserDB*, char, struct _UNIT_DB_BASE::_LIST*, CUserDBUpdate_UnitData222_ptr);
        using CUserDBUpdate_UnitDelete224_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBUpdate_UnitDelete224_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBUpdate_UnitDelete224_ptr);
        using CUserDBUpdate_UnitInsert226_ptr = bool (WINAPIV*)(struct CUserDB*, char, struct _UNIT_DB_BASE::_LIST*);
        using CUserDBUpdate_UnitInsert226_clbk = bool (WINAPIV*)(struct CUserDB*, char, struct _UNIT_DB_BASE::_LIST*, CUserDBUpdate_UnitInsert226_ptr);
        using CUserDBUpdate_UserFatigue228_ptr = bool (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBUpdate_UserFatigue228_clbk = bool (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBUpdate_UserFatigue228_ptr);
        using CUserDBUpdate_UserGetScaner230_ptr = bool (WINAPIV*)(struct CUserDB*, uint16_t, uint16_t);
        using CUserDBUpdate_UserGetScaner230_clbk = bool (WINAPIV*)(struct CUserDB*, uint16_t, uint16_t, CUserDBUpdate_UserGetScaner230_ptr);
        using CUserDBUpdate_UserPlayTime232_ptr = bool (WINAPIV*)(struct CUserDB*, unsigned int);
        using CUserDBUpdate_UserPlayTime232_clbk = bool (WINAPIV*)(struct CUserDB*, unsigned int, CUserDBUpdate_UserPlayTime232_ptr);
        using CUserDBUpdate_UserTLStatus234_ptr = bool (WINAPIV*)(struct CUserDB*, char);
        using CUserDBUpdate_UserTLStatus234_clbk = bool (WINAPIV*)(struct CUserDB*, char, CUserDBUpdate_UserTLStatus234_ptr);
        using CUserDBUpdate_UserVoteData236_ptr = bool (WINAPIV*)(struct CUserDB*);
        using CUserDBUpdate_UserVoteData236_clbk = bool (WINAPIV*)(struct CUserDB*, CUserDBUpdate_UserVoteData236_ptr);
        using CUserDBUpdate_User_Action_Point238_ptr = bool (WINAPIV*)(struct CUserDB*, char, unsigned int);
        using CUserDBUpdate_User_Action_Point238_clbk = bool (WINAPIV*)(struct CUserDB*, char, unsigned int, CUserDBUpdate_User_Action_Point238_ptr);
        using CUserDBUpdate_WindowInfo240_ptr = bool (WINAPIV*)(struct CUserDB*, unsigned int*, unsigned int*, unsigned int*, unsigned int*, unsigned int, unsigned int*);
        using CUserDBUpdate_WindowInfo240_clbk = bool (WINAPIV*)(struct CUserDB*, unsigned int*, unsigned int*, unsigned int*, unsigned int*, unsigned int, unsigned int*, CUserDBUpdate_WindowInfo240_ptr);
        using CUserDBWriteLog_ChangeClassAfterInitClass242_ptr = void (WINAPIV*)(struct CUserDB*, char, char*);
        using CUserDBWriteLog_ChangeClassAfterInitClass242_clbk = void (WINAPIV*)(struct CUserDB*, char, char*, CUserDBWriteLog_ChangeClassAfterInitClass242_ptr);
        using CUserDBWriteLog_CharSelect244_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBWriteLog_CharSelect244_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBWriteLog_CharSelect244_ptr);
        using CUserDBWriteLog_Level246_ptr = void (WINAPIV*)(struct CUserDB*, char);
        using CUserDBWriteLog_Level246_clbk = void (WINAPIV*)(struct CUserDB*, char, CUserDBWriteLog_Level246_ptr);
        
        using CUserDBdtor_CUserDB252_ptr = void (WINAPIV*)(struct CUserDB*);
        using CUserDBdtor_CUserDB252_clbk = void (WINAPIV*)(struct CUserDB*, CUserDBdtor_CUserDB252_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

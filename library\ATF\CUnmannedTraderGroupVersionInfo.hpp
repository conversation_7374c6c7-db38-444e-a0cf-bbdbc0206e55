// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__pair.hpp>
#include <std__vector.hpp>
#include <CUnmannedTraderGroupDivisionVersionInfo.hpp>


START_ATF_NAMESPACE
    struct CUnmannedTraderGroupVersionInfo
    {
        std::vector<CUnmannedTraderGroupDivisionVersionInfo> m_vecVerInfo;
    public:
        CUnmannedTraderGroupVersionInfo();
        void ctor_CUnmannedTraderGroupVersionInfo();
        bool GetVersion(char byDivision, char byClass, unsigned int* dwVer);
        bool IncreaseVersion(char byDivision, char byClass);
        bool Init(struct std::vector<std::pair<unsigned long,unsigned long>>* vecInfo);
        ~CUnmannedTraderGroupVersionInfo();
        void dtor_CUnmannedTraderGroupVersionInfo();
    };
END_ATF_NAMESPACE

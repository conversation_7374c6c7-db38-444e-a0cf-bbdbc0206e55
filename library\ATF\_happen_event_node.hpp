// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_happen_event_condition_node.hpp>


START_ATF_NAMESPACE
    struct _happen_event_node
    {
        int m_bUse;
        int m_bQuestRepeat;
        int m_nQuestType;
        int m_bSelectQuestManual;
        int m_nAcepProNum;
        int m_nAcepProDen;
        _happen_event_condition_node m_CondNode[5];
        char m_strLinkQuest[5][64];
    };
END_ATF_NAMESPACE

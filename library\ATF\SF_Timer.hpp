// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct SF_Timer
    {
        unsigned int m_dwLastCheckTime;
        unsigned int m_dwGapCheckTime;
    public:
        int CheckTime(unsigned int dwLoopTime);
        SF_Timer();
        void ctor_SF_Timer();
        void Set(unsigned int dwTimeDelay);
    };
END_ATF_NAMESPACE

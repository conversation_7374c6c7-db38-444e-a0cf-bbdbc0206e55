// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$3420BFA1B98889A0C2FB4D4D327EB808.hpp>
#include <tagELEMDESC.hpp>



START_ATF_NAMESPACE
    struct tagVARDESC
    {
        int memid;
        wchar_t *lpstrSchema;
        $3420BFA1B98889A0C2FB4D4D327EB808 ___u2;
        tagELEMDESC elemdescVar;
        unsigned __int16 wVarFlags;
        tagVARKIND varkind;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SOCK_TYPE_PARAM.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _NET_TYPE_PARAM : _SOCK_TYPE_PARAM
    {
        char m_byRecvThreadNum;
        unsigned int m_dwSendBufferSize;
        unsigned int m_dwRecvBufferSize;
        bool m_bSendSafe;
        unsigned int m_dwSendSafeSize;
        int m_bSpeedHackCheck;
        int m_bRealSockCheck;
        int m_bSystemLogFile;
        int m_bRecvLogFile;
        int m_bSendLogFile;
        int m_bOddMsgWriteLog;
        int m_bOddMsgDisconnect;
        char m_byRecvSleepTime;
        char m_bySendSleepTime;
        int m_bSvrToS;
        int m_bAnSyncConnect;
        int m_bPassSendQueueFull;
        int m_bKeyCheck;
        unsigned int m_dwKeyCheckTerm;
        unsigned int m_dwProcessMsgNumPerLoop;
        char m_szModuleName[128];
    public:
        _NET_TYPE_PARAM();
        void ctor__NET_TYPE_PARAM();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_NET_TYPE_PARAM, 228>(), "_NET_TYPE_PARAM");
END_ATF_NAMESPACE

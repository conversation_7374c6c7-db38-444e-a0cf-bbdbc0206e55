// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CashDbWorker.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CashDbWorkerctor_CashDbWorker2_ptr = void (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorkerctor_CashDbWorker2_clbk = void (WINAPIV*)(struct CashDbWorker*, CashDbWorkerctor_CashDbWorker2_ptr);
        using CashDbWorkerCompleteWork4_ptr = void (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorkerCompleteWork4_clbk = void (WINAPIV*)(struct CashDbWorker*, CashDbWorkerCompleteWork4_ptr);
        using CashDbWorkerConvertErrorCode6_ptr = int (WINAPIV*)(struct CashDbWorker*, char);
        using CashDbWorkerConvertErrorCode6_clbk = int (WINAPIV*)(struct CashDbWorker*, char, CashDbWorkerConvertErrorCode6_ptr);
        using CashDbWorkerDoWork8_ptr = void (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorkerDoWork8_clbk = void (WINAPIV*)(struct CashDbWorker*, CashDbWorkerDoWork8_ptr);
        using CashDbWorkerGetBillingDBConnectionStatus10_ptr = bool (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorkerGetBillingDBConnectionStatus10_clbk = bool (WINAPIV*)(struct CashDbWorker*, CashDbWorkerGetBillingDBConnectionStatus10_ptr);
        using CashDbWorkerGetUseCashQueryStr12_ptr = void (WINAPIV*)(struct CashDbWorker*, struct _param_cash_update*, int, char*, uint64_t);
        using CashDbWorkerGetUseCashQueryStr12_clbk = void (WINAPIV*)(struct CashDbWorker*, struct _param_cash_update*, int, char*, uint64_t, CashDbWorkerGetUseCashQueryStr12_ptr);
        using CashDbWorkerInitialize14_ptr = bool (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorkerInitialize14_clbk = bool (WINAPIV*)(struct CashDbWorker*, CashDbWorkerInitialize14_ptr);
        using CashDbWorkerIsNULL16_ptr = bool (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorkerIsNULL16_clbk = bool (WINAPIV*)(struct CashDbWorker*, CashDbWorkerIsNULL16_ptr);
        using CashDbWorkerPushTask18_ptr = bool (WINAPIV*)(struct CashDbWorker*, int, char*, uint64_t);
        using CashDbWorkerPushTask18_clbk = bool (WINAPIV*)(struct CashDbWorker*, int, char*, uint64_t, CashDbWorkerPushTask18_ptr);
        using CashDbWorkerRelease20_ptr = void (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorkerRelease20_clbk = void (WINAPIV*)(struct CashDbWorker*, CashDbWorkerRelease20_ptr);
        using CashDbWorkerSendMsgSucceedBuy22_ptr = void (WINAPIV*)(struct CashDbWorker*, uint16_t, struct _param_cash_update*);
        using CashDbWorkerSendMsgSucceedBuy22_clbk = void (WINAPIV*)(struct CashDbWorker*, uint16_t, struct _param_cash_update*, CashDbWorkerSendMsgSucceedBuy22_ptr);
        using CashDbWorker_all_rollback24_ptr = void (WINAPIV*)(struct CashDbWorker*, struct _param_cash_update*);
        using CashDbWorker_all_rollback24_clbk = void (WINAPIV*)(struct CashDbWorker*, struct _param_cash_update*, CashDbWorker_all_rollback24_ptr);
        using CashDbWorker_complete_tsk_cash_rollback26_ptr = void (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_complete_tsk_cash_rollback26_clbk = void (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_complete_tsk_cash_rollback26_ptr);
        using CashDbWorker_complete_tsk_cash_select28_ptr = void (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_complete_tsk_cash_select28_clbk = void (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_complete_tsk_cash_select28_ptr);
        using CashDbWorker_complete_tsk_cash_total_selling_select30_ptr = void (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_complete_tsk_cash_total_selling_select30_clbk = void (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_complete_tsk_cash_total_selling_select30_ptr);
        using CashDbWorker_complete_tsk_cash_update32_ptr = void (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_complete_tsk_cash_update32_clbk = void (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_complete_tsk_cash_update32_ptr);
        using CashDbWorker_complete_tsk_cashitem_buy_dblog34_ptr = void (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_complete_tsk_cashitem_buy_dblog34_clbk = void (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_complete_tsk_cashitem_buy_dblog34_ptr);
        using CashDbWorker_delete_from_inven36_ptr = void (WINAPIV*)(struct CashDbWorker*, struct CPlayer*, struct _param_cash_update*, int);
        using CashDbWorker_delete_from_inven36_clbk = void (WINAPIV*)(struct CashDbWorker*, struct CPlayer*, struct _param_cash_update*, int, CashDbWorker_delete_from_inven36_ptr);
        using CashDbWorker_get_player38_ptr = struct CPlayer* (WINAPIV*)(struct CashDbWorker*, uint16_t, unsigned int);
        using CashDbWorker_get_player38_clbk = struct CPlayer* (WINAPIV*)(struct CashDbWorker*, uint16_t, unsigned int, CashDbWorker_get_player38_ptr);
        using CashDbWorker_init_database40_ptr = bool (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorker_init_database40_clbk = bool (WINAPIV*)(struct CashDbWorker*, CashDbWorker_init_database40_ptr);
        using CashDbWorker_init_loggers42_ptr = bool (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorker_init_loggers42_clbk = bool (WINAPIV*)(struct CashDbWorker*, CashDbWorker_init_loggers42_ptr);
        using CashDbWorker_insert_to_inven44_ptr = bool (WINAPIV*)(struct CashDbWorker*, struct CPlayer*, struct _param_cash_update::__item*);
        using CashDbWorker_insert_to_inven44_clbk = bool (WINAPIV*)(struct CashDbWorker*, struct CPlayer*, struct _param_cash_update::__item*, CashDbWorker_insert_to_inven44_ptr);
        using CashDbWorker_wait_tsk_cash_buy_dblog46_ptr = int (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_wait_tsk_cash_buy_dblog46_clbk = int (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_wait_tsk_cash_buy_dblog46_ptr);
        using CashDbWorker_wait_tsk_cash_rollback48_ptr = int (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_wait_tsk_cash_rollback48_clbk = int (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_wait_tsk_cash_rollback48_ptr);
        using CashDbWorker_wait_tsk_cash_select50_ptr = int (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_wait_tsk_cash_select50_clbk = int (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_wait_tsk_cash_select50_ptr);
        using CashDbWorker_wait_tsk_cash_update52_ptr = int (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_wait_tsk_cash_update52_clbk = int (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_wait_tsk_cash_update52_ptr);
        using CashDbWorker_wait_tst_cash_total_selling_select54_ptr = int (WINAPIV*)(struct CashDbWorker*, struct Task*);
        using CashDbWorker_wait_tst_cash_total_selling_select54_clbk = int (WINAPIV*)(struct CashDbWorker*, struct Task*, CashDbWorker_wait_tst_cash_total_selling_select54_ptr);
        
        using CashDbWorkerdtor_CashDbWorker59_ptr = void (WINAPIV*)(struct CashDbWorker*);
        using CashDbWorkerdtor_CashDbWorker59_clbk = void (WINAPIV*)(struct CashDbWorker*, CashDbWorkerdtor_CashDbWorker59_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

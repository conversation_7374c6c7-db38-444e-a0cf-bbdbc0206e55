// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _equip_up_item_lv_limit_zocl
    {
        char byEquipItemNum[8];
    public:
        _equip_up_item_lv_limit_zocl();
        void ctor__equip_up_item_lv_limit_zocl();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_equip_up_item_lv_limit_zocl, 8>(), "_equip_up_item_lv_limit_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct $456A8F4F1C2350C1D38CA0F139FB5400
    {
        unsigned int speed;
        unsigned int type;
        unsigned int state;
        wchar_t machineName[256];
        wchar_t sharedAdapterName[256];
    };
END_ATF_NAMESPACE

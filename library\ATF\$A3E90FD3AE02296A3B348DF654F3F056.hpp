// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagRAWHID.hpp>
#include <tagRAWKEYBOARD.hpp>
#include <tagRAWMOUSE.hpp>


START_ATF_NAMESPACE
    union $A3E90FD3AE02296A3B348DF654F3F056
    {
        tagRAWMOUSE mouse;
        tagRAWKEYBOARD keyboard;
        tagRAWH<PERSON> hid;
    };
END_ATF_NAMESPACE

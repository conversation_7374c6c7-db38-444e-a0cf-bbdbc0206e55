#include <_darkhole_open_all_portal_by_result_inform_zocl.hpp>


START_ATF_NAMESPACE
    _darkhole_open_all_portal_by_result_inform_zocl::_darkhole_open_all_portal_by_result_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _darkhole_open_all_portal_by_result_inform_zocl*);
        (org_ptr(0x14026f700L))(this);
    };
    void _darkhole_open_all_portal_by_result_inform_zocl::ctor__darkhole_open_all_portal_by_result_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _darkhole_open_all_portal_by_result_inform_zocl*);
        (org_ptr(0x14026f700L))(this);
    };
    int _darkhole_open_all_portal_by_result_inform_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _darkhole_open_all_portal_by_result_inform_zocl*);
        return (org_ptr(0x14026f750L))(this);
    };
END_ATF_NAMESPACE

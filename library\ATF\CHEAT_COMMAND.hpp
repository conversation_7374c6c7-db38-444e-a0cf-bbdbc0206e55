// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CHEAT_COMMAND
    {
        const char *pwszCommand;
        unsigned int uiCmdLen;
        bool (WINAPIV *fn)(struct CPlayer *);
        int nUseDegree;
        int nMgrDegree;
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CHEAT_COMMAND, 32>(), "CHEAT_COMMAND");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DELAY_PROCESS.hpp>


START_ATF_NAMESPACE
    struct  _BILLING_FORCE_CLOSE_DELAY : _DELAY_PROCESS
    {
    public:
        void Process(unsigned int dwIndex, unsigned int dwSerial);
        _BILLING_FORCE_CLOSE_DELAY();
        void ctor__BILLING_FORCE_CLOSE_DELAY();
        ~_BILLING_FORCE_CLOSE_DELAY();
        void dtor__BILLING_FORCE_CLOSE_DELAY();
    };
END_ATF_NAMESPACE

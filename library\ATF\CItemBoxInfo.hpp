// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CItemBox.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CItemBoxctor_CItemBox2_ptr = void (WINAPIV*)(struct CItemBox*);
        using CItemBoxctor_CItemBox2_clbk = void (WINAPIV*)(struct CItemBox*, CItemBoxctor_CItemBox2_ptr);
        using CItemBoxCreate4_ptr = bool (WINAPIV*)(struct CItemBox*, struct _itembox_create_setdata*, bool);
        using CItemBoxCreate4_clbk = bool (WINAPIV*)(struct CItemBox*, struct _itembox_create_setdata*, bool, CItemBoxCreate4_ptr);
        using CItemBoxDestroy6_ptr = bool (WINAPIV*)(struct CItemBox*);
        using CItemBoxDestroy6_clbk = bool (WINAPIV*)(struct CItemBox*, CItemBoxDestroy6_ptr);
        using CItemBoxInit8_ptr = void (WINAPIV*)(struct CItemBox*, struct _object_id*);
        using CItemBoxInit8_clbk = void (WINAPIV*)(struct CItemBox*, struct _object_id*, CItemBoxInit8_ptr);
        using CItemBoxIsTakeRight10_ptr = bool (WINAPIV*)(struct CItemBox*, struct CPlayer*);
        using CItemBoxIsTakeRight10_clbk = bool (WINAPIV*)(struct CItemBox*, struct CPlayer*, CItemBoxIsTakeRight10_ptr);
        using CItemBoxLoop12_ptr = void (WINAPIV*)(struct CItemBox*);
        using CItemBoxLoop12_clbk = void (WINAPIV*)(struct CItemBox*, CItemBoxLoop12_ptr);
        using CItemBoxSendMsg_Create14_ptr = void (WINAPIV*)(struct CItemBox*);
        using CItemBoxSendMsg_Create14_clbk = void (WINAPIV*)(struct CItemBox*, CItemBoxSendMsg_Create14_ptr);
        using CItemBoxSendMsg_Destroy16_ptr = void (WINAPIV*)(struct CItemBox*);
        using CItemBoxSendMsg_Destroy16_clbk = void (WINAPIV*)(struct CItemBox*, CItemBoxSendMsg_Destroy16_ptr);
        using CItemBoxSendMsg_FixPosition18_ptr = void (WINAPIV*)(struct CItemBox*, int);
        using CItemBoxSendMsg_FixPosition18_clbk = void (WINAPIV*)(struct CItemBox*, int, CItemBoxSendMsg_FixPosition18_ptr);
        using CItemBoxSendMsg_StateChange20_ptr = void (WINAPIV*)(struct CItemBox*);
        using CItemBoxSendMsg_StateChange20_clbk = void (WINAPIV*)(struct CItemBox*, CItemBoxSendMsg_StateChange20_ptr);
        
        using CItemBoxdtor_CItemBox26_ptr = void (WINAPIV*)(struct CItemBox*);
        using CItemBoxdtor_CItemBox26_clbk = void (WINAPIV*)(struct CItemBox*, CItemBoxdtor_CItemBox26_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>


START_ATF_NAMESPACE
    struct CAggroNode
    {
        CCharacter *m_pCharacter;
        unsigned int m_dwObjectSerial;
        int m_nAggroData;
        int m_nDamageData;
        int m_nKingPowerDamage;
    public:
        CAggroNode();
        void ctor_CAggroNode();
        void Init();
        int IsLive();
        void Set(struct CCharacter* pCharacter);
        void SetAggro(int nDam, float fAdd, int nAttackType, unsigned int dwAttackSerial, int bOtherPlayerSupport, int bFirstAttack, int bTempSkill);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$A8180673F9B76483EDD3F62C23833383.hpp>


START_ATF_NAMESPACE
    struct _RPC_SECURITY_QOS_V2_W
    {
        unsigned int Version;
        unsigned int Capabilities;
        unsigned int IdentityTracking;
        unsigned int ImpersonationType;
        unsigned int AdditionalSecurityInfoType;
        $A8180673F9B76483EDD3F62C23833383 u;
    };
END_ATF_NAMESPACE

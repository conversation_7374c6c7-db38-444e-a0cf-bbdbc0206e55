#include <_POTION_NEXT_USE_TIME_DB_BASE.hpp>


START_ATF_NAMESPACE
    void _POTION_NEXT_USE_TIME_DB_BASE::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _POTION_NEXT_USE_TIME_DB_BASE*);
        (org_ptr(0x1400779b0L))(this);
    };
    _POTION_NEXT_USE_TIME_DB_BASE::_POTION_NEXT_USE_TIME_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _POTION_NEXT_USE_TIME_DB_BASE*);
        (org_ptr(0x140077960L))(this);
    };
    void _POTION_NEXT_USE_TIME_DB_BASE::ctor__POTION_NEXT_USE_TIME_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _POTION_NEXT_USE_TIME_DB_BASE*);
        (org_ptr(0x140077960L))(this);
    };
END_ATF_NAMESPACE

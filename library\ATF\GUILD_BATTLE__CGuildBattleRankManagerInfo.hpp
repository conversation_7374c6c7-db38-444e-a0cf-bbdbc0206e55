// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleRankManager.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*);
            using GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, unsigned int);
            using GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, unsigned int, GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*);
            using GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerClear8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char);
            using GUILD_BATTLE__CGuildBattleRankManagerClear8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, GUILD_BATTLE__CGuildBattleRankManagerClear8_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerClear10_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*);
            using GUILD_BATTLE__CGuildBattleRankManagerClear10_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, GUILD_BATTLE__CGuildBattleRankManagerClear10_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerDestroy12_ptr = void (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleRankManagerDestroy12_clbk = void (WINAPIV*)(GUILD_BATTLE__CGuildBattleRankManagerDestroy12_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerFind14_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, unsigned int, int*, char*);
            using GUILD_BATTLE__CGuildBattleRankManagerFind14_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, unsigned int, int*, char*, GUILD_BATTLE__CGuildBattleRankManagerFind14_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerInit16_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*);
            using GUILD_BATTLE__CGuildBattleRankManagerInit16_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, GUILD_BATTLE__CGuildBattleRankManagerInit16_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerInstance18_ptr = struct GUILD_BATTLE::CGuildBattleRankManager* (WINAPIV*)();
            using GUILD_BATTLE__CGuildBattleRankManagerInstance18_clbk = struct GUILD_BATTLE::CGuildBattleRankManager* (WINAPIV*)(GUILD_BATTLE__CGuildBattleRankManagerInstance18_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerLoad20_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char);
            using GUILD_BATTLE__CGuildBattleRankManagerLoad20_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, GUILD_BATTLE__CGuildBattleRankManagerLoad20_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerLoad22_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*);
            using GUILD_BATTLE__CGuildBattleRankManagerLoad22_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, GUILD_BATTLE__CGuildBattleRankManagerLoad22_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*);
            using GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*);
            using GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, char*);
            using GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, char*, GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerSend30_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, int, char, unsigned int, char, char, unsigned int);
            using GUILD_BATTLE__CGuildBattleRankManagerSend30_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, int, char, unsigned int, char, char, unsigned int, GUILD_BATTLE__CGuildBattleRankManagerSend30_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerUpdate32_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, char*);
            using GUILD_BATTLE__CGuildBattleRankManagerUpdate32_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, char*, GUILD_BATTLE__CGuildBattleRankManagerUpdate32_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, unsigned int, char, unsigned int);
            using GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, unsigned int, char, unsigned int, GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_ptr);
            using GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, unsigned int, char, unsigned int);
            using GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, char, unsigned int, char, unsigned int, GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_ptr);
            
            using GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*);
            using GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleRankManager*, GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

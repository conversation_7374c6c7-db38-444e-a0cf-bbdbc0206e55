// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $48802A31D3D0701BC13CEF9CEA041E7B
    {
        BYTE gap0[8];
        unsigned __int16 uiVal;
    };    
    static_assert(ATF::checkSize<$48802A31D3D0701BC13CEF9CEA041E7B, 10>(), "$48802A31D3D0701BC13CEF9CEA041E7B");
END_ATF_NAMESPACE

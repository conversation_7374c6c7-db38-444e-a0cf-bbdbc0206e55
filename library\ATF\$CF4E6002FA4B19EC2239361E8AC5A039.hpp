// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union $CF4E6002FA4B19EC2239361E8AC5A039
    {
        unsigned int dwRBitMask;
        unsigned int dwYBitMask;
        unsigned int dwStencilBitDepth;
        unsigned int dwLuminanceBitMask;
        unsigned int dwBumpDuBitMask;
        unsigned int dwOperations;
    };    
    static_assert(ATF::checkSize<$CF4E6002FA4B19EC2239361E8AC5A039, 4>(), "$CF4E6002FA4B19EC2239361E8AC5A039");
END_ATF_NAMESPACE

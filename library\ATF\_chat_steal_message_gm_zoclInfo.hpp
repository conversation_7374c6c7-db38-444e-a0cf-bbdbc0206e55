// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_chat_steal_message_gm_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _chat_steal_message_gm_zoclctor__chat_steal_message_gm_zocl2_ptr = void (WINAPIV*)(struct _chat_steal_message_gm_zocl*);
        using _chat_steal_message_gm_zoclctor__chat_steal_message_gm_zocl2_clbk = void (WINAPIV*)(struct _chat_steal_message_gm_zocl*, _chat_steal_message_gm_zoclctor__chat_steal_message_gm_zocl2_ptr);
        using _chat_steal_message_gm_zoclsize4_ptr = int (WINAPIV*)(struct _chat_steal_message_gm_zocl*);
        using _chat_steal_message_gm_zoclsize4_clbk = int (WINAPIV*)(struct _chat_steal_message_gm_zocl*, _chat_steal_message_gm_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___String_const_iterator.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _String_iterator<wchar_t,char_traits<wchar_t>,allocator<wchar_t> > : _String_const_iterator<wchar_t,char_traits<wchar_t>,allocator<wchar_t> >
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std___String_const_iterator.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _String_iterator<char,char_traits<char>,allocator<char> > : _String_const_iterator<char,char_traits<char>,allocator<char> >
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std___String_const_iterator.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _String_iterator<char,char_traits<char>,_DebugHeapAllocator<char> > : _String_const_iterator<char,char_traits<char>,_DebugHeapAllocator<char> >
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE

# Loot Exchange Custom Mapping System

## Overview
The enhanced Loot Exchange addon now supports a comprehensive custom mapping system that allows you to define exactly which items exchange for which currency types and values, completely overriding the default database behavior.

## Key Features

### 1. **Granular Item Control**
- Map specific items by table:index (e.g., `1:500` for table 1, index 500)
- Use wildcard patterns (e.g., `1:*` for all items in table 1)
- Set custom exchange values or use database values
- Define currency type priorities per item

### 2. **Multiple Mapping Levels**
- **Item Mappings**: Specific item codes (highest priority)
- **Table Mappings**: Entire item tables (medium priority)  
- **Category Mappings**: Groups of related tables (lower priority)
- **Exclusion List**: Items that never exchange (absolute priority)

### 3. **Advanced Features**
- Priority overrides for currency type selection
- Value multipliers and custom rates
- Wildcard pattern matching
- Comprehensive logging system

## Configuration Sections

### [ItemMappings]
Define specific item-to-currency mappings:

```ini
[ItemMappings]
; Format: ItemCode=CurrencyType:Value
; ItemCode can be:
;   - Table:Index (e.g., 1:500)
;   - Wildcard patterns (e.g., 1:* for all table 1 items)

; Specific high-value weapons exchange for Gold
1:500=1:5000                    ; Table 1, Index 500 -> 5000 Gold
1:501=1:7500                    ; Table 1, Index 501 -> 7500 Gold

; All rare materials exchange for Processing Points
15:100=4:2000                   ; Specific rare ore -> 2000 Processing Points
15:*=4:0                        ; All table 15 items -> Processing Points (DB value)

; Use database value but force currency type
2:300=1:0                       ; Table 2, Index 300 -> Gold (database value)
```

### [TableMappings]
Map entire item tables to currency types:

```ini
[TableMappings]
; Format: TableCode=CurrencyType:ValueMultiplier
; ValueMultiplier: 100=normal, 150=50% bonus, 50=50% penalty

1=0:110                         ; All weapons -> CP at 110% rate
2=1:120                         ; All armor -> Gold at 120% rate
15=4:150                        ; All resources -> Processing Points at 150% rate
```

### [CategoryMappings]
Group related tables into categories:

```ini
[CategoryMappings]
; Format: CategoryName=CurrencyType:ValueMultiplier:TableList

Equipment=0:105:1,2,3,4,5,6,7,8     ; All equipment -> CP at 105% rate
Resources=4:140:15,16,17            ; All materials -> Processing Points at 140% rate
Accessories=1:125:18,19             ; All accessories -> Gold at 125% rate
```

### [ExclusionList]
Items that should never be exchanged:

```ini
[ExclusionList]
; Items that will never be exchanged (blacklist)

1:999                           ; Never exchange this legendary weapon
30:*                            ; Never exchange any items from table 30
rare_*                          ; Never exchange items matching this pattern
```

### [PriorityOverrides]
Override currency type priority for specific items:

```ini
[PriorityOverrides]
; Format: ItemCode=Priority1,Priority2,Priority3...
; Currency types: 0=CP, 1=Gold, 2=PvP, 3=PvP2, 4=Processing, 5=Hunter, 6=GoldPoint

1:*=1,0,4,5,6,2,3              ; For weapons: try Gold first, then CP, etc.
15:*=4,5,6,0,1,2,3             ; For resources: try Processing Points first
```

## Priority System

The system evaluates mappings in this order (highest to lowest priority):

1. **Exclusion List** - Completely prevents exchange
2. **Item Mappings** - Specific item codes
3. **Table Mappings** - Entire tables
4. **Category Mappings** - Groups of tables
5. **Default Behavior** - Original database money types

## Configuration Examples

### Example 1: High-Value Items for Gold
```ini
[ItemMappings]
; Legendary weapons exchange for high Gold amounts
1:500=1:10000                   ; Legendary Sword -> 10,000 Gold
1:501=1:15000                   ; Legendary Rifle -> 15,000 Gold
1:502=1:12000                   ; Legendary Launcher -> 12,000 Gold

[TableMappings]
; All other weapons exchange for CP at bonus rate
1=0:120                         ; Regular weapons -> CP at 120% rate
```

### Example 2: Crafting-Focused Server
```ini
[CategoryMappings]
; All materials go to Processing Points
Resources=4:150:15,16,17        ; 150% bonus for all materials

[ItemMappings]
; Rare materials get even higher bonuses
15:100=4:5000                   ; Ultra Rare Ore -> 5000 Processing Points
15:101=4:7500                   ; Legendary Crystal -> 7500 Processing Points

[PriorityOverrides]
; Resources always try Processing Points first
15:*=4,5,6,0,1,2,3
16:*=4,5,6,0,1,2,3
17:*=4,5,6,0,1,2,3
```

### Example 3: PvP Server Setup
```ini
[TableMappings]
; Equipment exchanges for PvP currency
1=2:100                         ; Weapons -> PvP Points
2=2:100                         ; Armor -> PvP Points

[ItemMappings]
; Special PvP items
20:50=2:2000                    ; PvP Token -> 2000 PvP Points
20:51=3:1000                    ; PvP Cash Item -> 1000 PvP Cash

[ExclusionList]
; Protect rare PvP rewards
20:999                          ; Never exchange ultimate PvP reward
```

## Implementation Steps

### 1. Enable Custom Mappings
```ini
[AdvancedSettings]
UseCustomMappings=1             ; Enable the custom mapping system
```

### 2. Configure Your Mappings
- Start with broad category mappings
- Add specific table mappings for fine-tuning
- Use item mappings for special cases
- Add exclusions for protected items

### 3. Test and Monitor
```ini
[LootExchange]
EnableLogging=1                 ; Enable detailed logging
LogFilePath=./YorozuyaGS/Logs/LootExchange.log
```

### 4. Review Logs
The system logs all mapping applications:
```
[2024-01-15 10:30:15] Applied item mapping for item_1_500: Currency 1, Value 5000
[2024-01-15 10:30:16] Applied table mapping for table 15: Currency 4, Value 2250
[2024-01-15 10:30:17] Player TestPlayer exchanged item (Table:1, Index:500) for 5000 of currency type 1 [Custom Mapping]
```

## Advanced Features

### Wildcard Patterns
- `*` matches any characters: `1:*` matches all items in table 1
- `?` matches single character: `1:50?` matches 1:500, 1:501, etc.
- Patterns work in item codes, exclusions, and priority overrides

### Value Calculation
- **Fixed Values**: `1:500=1:5000` gives exactly 5000 Gold
- **Database Values**: `1:500=1:0` uses item's database value for Gold
- **Multipliers**: Table/Category mappings multiply database values

### Currency Type Reference
```
0 = CP (Dalant)         - Main game currency
1 = Gold                - Secondary currency
2 = PvP Point           - PvP battles currency
3 = PvP Point 2         - PvP cash bag currency
4 = Processing Point    - Crafting/processing currency
5 = Hunter Point        - Hunting activities currency
6 = Gold Point          - Premium/special currency
```

## Best Practices

### 1. **Start Simple**
- Begin with category mappings for broad control
- Add table mappings for specific item types
- Use item mappings only for special cases

### 2. **Use Exclusions Wisely**
- Protect quest items, rare rewards, and special items
- Use patterns to exclude entire categories if needed

### 3. **Monitor and Adjust**
- Enable logging during initial setup
- Review exchange patterns and player feedback
- Adjust values and mappings based on server economy

### 4. **Document Your Setup**
- Comment your INI file extensively
- Keep track of which items use which mappings
- Document the reasoning behind special cases

## Troubleshooting

### Common Issues

1. **Items Not Exchanging**
   - Check if item is in exclusion list
   - Verify `UseCustomMappings=1` is set
   - Check if currency type is enabled in `[CurrencySettings]`

2. **Wrong Currency Type**
   - Review priority overrides for the item
   - Check item/table/category mappings
   - Verify currency is enabled

3. **Unexpected Values**
   - Check if value multipliers are applied correctly
   - Verify exchange rate modifier setting
   - Review durability multiplication setting

### Debug Steps

1. **Enable Detailed Logging**
   ```ini
   [LootExchange]
   EnableLogging=1
   ```

2. **Check Log File**
   - Look for mapping application messages
   - Check for error messages
   - Verify which mappings are being used

3. **Test Specific Items**
   - Start with simple, known items
   - Test each mapping type individually
   - Verify exclusions work correctly

This custom mapping system provides complete control over your server's loot exchange economy while maintaining backward compatibility with the original system.

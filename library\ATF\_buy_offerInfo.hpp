// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_buy_offer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _buy_offerctor__buy_offer2_ptr = void (WINAPIV*)(struct _buy_offer*);
        using _buy_offerctor__buy_offer2_clbk = void (WINAPIV*)(struct _buy_offer*, _buy_offerctor__buy_offer2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CPostData
    {
        int m_nNumber;
        char m_byState;
        char m_byErrCode;
        char m_bySendRace;
        char m_bySenderDgr;
        unsigned int m_dwSenderSerial;
        char m_wszSendName[17];
        char m_wszRecvName[17];
        char m_wszTitle[21];
        char m_wszContent[201];
        _INVENKEY m_Key;
        unsigned __int64 m_dwDur;
        unsigned int m_dwUpt;
        unsigned __int64 m_lnUID;
        unsigned int m_dwGold;
        unsigned int m_dwPSSerial;
        bool m_bContentLoad;
        bool m_bUpdateIndex;
    public:
        CPostData();
        void ctor_CPostData();
        char GetState();
        void Init();
        void SetPostContent(char* wszContent);
        void SetPostData(int nNumber, unsigned int dwSenderSerial, char* wszSendName, char* wszRecvName, char* wszTitle, struct _INVENKEY Key, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, unsigned int dwPSSerial, char bySendRace, char bySenderDgr);
        void SetPostItemSerial(uint64_t lnUID);
        void SetPostTitleData(int nNumber, unsigned int dwPSSerial, char byState, char* wszSendName, char* wszTitle, struct _INVENKEY Key, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, bool bUpdateIndex);
        void SetReturnPostData(char byErrCode, unsigned int dwPostSerial, char* wszRecvName, char* wszTitle, char* wszContent, struct _INVENKEY Key, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold);
        void SetState(char byState);
        ~CPostData();
        void dtor_CPostData();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CPostData, 312>(), "CPostData");
END_ATF_NAMESPACE

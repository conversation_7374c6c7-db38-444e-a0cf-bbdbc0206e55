// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_cash_event.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _cash_eventctor__cash_event2_ptr = void (WINAPIV*)(struct _cash_event*);
        using _cash_eventctor__cash_event2_clbk = void (WINAPIV*)(struct _cash_event*, _cash_eventctor__cash_event2_ptr);
        
        using _cash_eventdtor__cash_event4_ptr = void (WINAPIV*)(struct _cash_event*);
        using _cash_eventdtor__cash_event4_clbk = void (WINAPIV*)(struct _cash_event*, _cash_eventdtor__cash_event4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

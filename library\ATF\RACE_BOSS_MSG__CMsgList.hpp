// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetIndexList.hpp>
#include <RACE_BOSS_MSG__CMsg.hpp>
#include <CUnmannedTraderSchedule.hpp>


START_ATF_NAMESPACE
    namespace RACE_BOSS_MSG
    {
        struct CMsgList
        {
            typedef CUnmannedTraderSchedule::STATE INXLIST_TYPE;
            char m_ucRace;
            CMsg **m_ppMsg;
            unsigned int m_uiSize;
            CNetIndexList m_kEmptyInxList;
            CNetIndexList m_kUseInxList;
            CNetIndexList m_kWaitInxList;
        public:
            void AddEmpty(struct CMsg* pkMsg);
            void AddUse(struct CMsg* pkMsg);
            CMsgList(char ucRace, unsigned int uiSize);
            void ctor_CMsgList(char ucRace, unsigned int uiSize);
            int Cancel(unsigned int dwMsgID, struct CMsg** pkMsg);
            void CleanUp();
            struct CMsg* GetEmpty();
            char GetRemainCnt();
            struct CMsg* GetSendMsg();
            bool Init();
            bool Load(unsigned int dwCurTime);
            bool LoadIndexList(int iType, struct CNetIndexList* kInxList);
            bool LoadMsgList(struct CNetIndexList* kInxList, unsigned int dwCurTime);
            void Refresh();
            void Release(struct CMsg* pkMsg);
            void RollBack();
            bool Save();
            bool SaveIndexList(int iType, struct CNetIndexList* kInxList);
            bool SaveMsgList(struct CNetIndexList* kInxList);
            ~CMsgList();
            void dtor_CMsgList();
        };
    }; // end namespace RACE_BOSS_MSG
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _cash_lim_sale
    {
        struct LimEventItemInfo
        {
            char byTableCode;
            unsigned int dwIndex;
            unsigned __int16 wCount;
        };
        char DCK;
        char m_byEventNum;
        LimEventItemInfo m_EventItemInfo[20];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IBinding.hpp>


START_ATF_NAMESPACE
    template<>
    struct _CIP<IBinding,&IID_IBinding>
    {
        IBinding *_pInterface;
    };
END_ATF_NAMESPACE
#include <IUnknown.hpp>


START_ATF_NAMESPACE
    template<>
    struct _CIP<IUnknown,&IID_IUnknown>
    {
        IUnknown *_pInterface;
    };
END_ATF_NAMESPACE
#include <IMoniker.hpp>


START_ATF_NAMESPACE
    template<>
    struct _CIP<IMoniker,&IID_IMoniker>
    {
        IMoniker *_pInterface;
    };
END_ATF_NAMESPACE

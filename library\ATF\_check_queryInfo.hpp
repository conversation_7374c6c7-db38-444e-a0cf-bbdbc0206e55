// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_check_query.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _check_querysize2_ptr = int (WINAPIV*)(struct _check_query*);
        using _check_querysize2_clbk = int (WINAPIV*)(struct _check_query*, _check_querysize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

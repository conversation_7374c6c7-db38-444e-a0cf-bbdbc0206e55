// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _IMAGE_VXD_HEADER
    {
        unsigned __int16 e32_magic;
        char e32_border;
        char e32_worder;
        unsigned int e32_level;
        unsigned __int16 e32_cpu;
        unsigned __int16 e32_os;
        unsigned int e32_ver;
        unsigned int e32_mflags;
        unsigned int e32_mpages;
        unsigned int e32_startobj;
        unsigned int e32_eip;
        unsigned int e32_stackobj;
        unsigned int e32_esp;
        unsigned int e32_pagesize;
        unsigned int e32_lastpagesize;
        unsigned int e32_fixupsize;
        unsigned int e32_fixupsum;
        unsigned int e32_ldrsize;
        unsigned int e32_ldrsum;
        unsigned int e32_objtab;
        unsigned int e32_objcnt;
        unsigned int e32_objmap;
        unsigned int e32_itermap;
        unsigned int e32_rsrctab;
        unsigned int e32_rsrccnt;
        unsigned int e32_restab;
        unsigned int e32_enttab;
        unsigned int e32_dirtab;
        unsigned int e32_dircnt;
        unsigned int e32_fpagetab;
        unsigned int e32_frectab;
        unsigned int e32_impmod;
        unsigned int e32_impmodcnt;
        unsigned int e32_impproc;
        unsigned int e32_pagesum;
        unsigned int e32_datapage;
        unsigned int e32_preload;
        unsigned int e32_nrestab;
        unsigned int e32_cbnrestab;
        unsigned int e32_nressum;
        unsigned int e32_autodata;
        unsigned int e32_debuginfo;
        unsigned int e32_debuglen;
        unsigned int e32_instpreload;
        unsigned int e32_instdemand;
        unsigned int e32_heapsize;
        char e32_res3[12];
        unsigned int e32_winresoff;
        unsigned int e32_winreslen;
        unsigned __int16 e32_devid;
        unsigned __int16 e32_ddkver;
    };
END_ATF_NAMESPACE

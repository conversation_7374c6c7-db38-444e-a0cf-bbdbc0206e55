// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <_FILETIME.hpp>
#include <_server_rate_ini_data.hpp>


START_ATF_NAMESPACE
    struct _server_rate_realtime_load
    {
        CMyTimer m_tmDataFileCheckTime;
        _FILETIME m_ftWrite;
        _server_rate_ini_data m_IniData;
    public:
        void Init(unsigned int dwReadTerm);
        _server_rate_realtime_load();
        void ctor__server_rate_realtime_load();
        ~_server_rate_realtime_load();
        void dtor__server_rate_realtime_load();
    };    
    static_assert(ATF::checkSize<_server_rate_realtime_load, 104>(), "_server_rate_realtime_load");
END_ATF_NAMESPACE

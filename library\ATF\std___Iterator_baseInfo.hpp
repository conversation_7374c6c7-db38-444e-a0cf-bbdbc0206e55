// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Iterator_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            using std___Iterator_base_Clean_up_iter_debug2_ptr = void (WINAPIV*)(struct std::_Iterator_base*);
            using std___Iterator_base_Clean_up_iter_debug2_clbk = void (WINAPIV*)(struct std::_Iterator_base*, std___Iterator_base_Clean_up_iter_debug2_ptr);
            
            using std___Iterator_basector__Iterator_base4_ptr = void (WINAPIV*)(struct std::_Iterator_base*, struct std::_Iterator_base*);
            using std___Iterator_basector__Iterator_base4_clbk = void (WINAPIV*)(struct std::_Iterator_base*, struct std::_Iterator_base*, std___Iterator_basector__Iterator_base4_ptr);
            
            using std___Iterator_basector__Iterator_base6_ptr = void (WINAPIV*)(struct std::_Iterator_base*);
            using std___Iterator_basector__Iterator_base6_clbk = void (WINAPIV*)(struct std::_Iterator_base*, std___Iterator_basector__Iterator_base6_ptr);
            
            using std___Iterator_basedtor__Iterator_base10_ptr = void (WINAPIV*)(struct std::_Iterator_base*);
            using std___Iterator_basedtor__Iterator_base10_clbk = void (WINAPIV*)(struct std::_Iterator_base*, std___Iterator_basedtor__Iterator_base10_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

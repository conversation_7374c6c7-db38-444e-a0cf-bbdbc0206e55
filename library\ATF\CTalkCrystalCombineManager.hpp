// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <_STORAGE_LIST.hpp>
#include <_talik_crystal_exchange_clzo.hpp>
#include <_talk_crystal_matrial_combine_node.hpp>


START_ATF_NAMESPACE
    struct CTalkCrystalCombineManager
    {
        _talk_crystal_matrial_combine_node m_NodeList[24];
        CPlayer *m_pCurrentPlayer;
    public:
        CTalkCrystalCombineManager();
        void ctor_CTalkCrystalCombineManager();
        char CheckMixItem(struct _STORAGE_LIST::_db_con* pItem, int* pMixIndex, char* pbyTableCode, uint16_t* pwItemIndex, int* pnNeedItemCount);
        char CombinePreProcess(struct CPlayer* pPlayer, char byExchange<PERSON><PERSON>, struct _talik_crystal_exchange_clzo::_list* pList);
        char CombineProcess();
        static void Destory();
        bool Doit(struct CPlayer* pPlayer, char byExchange<PERSON><PERSON>, struct _talik_crystal_exchange_clzo::_list* pList);
        struct _talk_crystal_matrial_combine_node* GetMixNode(int nMixIndex);
        void Init();
        static struct CTalkCrystalCombineManager* Instance();
        struct _talk_crystal_matrial_combine_node* MakeMixNode(int nMixIndex, int nNeedItemNum, char byTableCode, uint16_t wItemIndex);
        char Push(struct _STORAGE_LIST::_db_con* pItem, char byUseCount, char byClientIndex);
    };
END_ATF_NAMESPACE

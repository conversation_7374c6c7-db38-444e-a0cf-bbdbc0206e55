// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogTypeDBTaskManager.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CLogTypeDBTaskManagerctor_CLogTypeDBTaskManager2_ptr = void (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerctor_CLogTypeDBTaskManager2_clbk = void (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerctor_CLogTypeDBTaskManager2_ptr);
        using CLogTypeDBTaskManagerCleanUp4_ptr = void (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerCleanUp4_clbk = void (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerCleanUp4_ptr);
        using CLogTypeDBTaskManagerDBProcess6_ptr = void (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerDBProcess6_clbk = void (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerDBProcess6_ptr);
        using CLogTypeDBTaskManagerDestroy8_ptr = void (WINAPIV*)();
        using CLogTypeDBTaskManagerDestroy8_clbk = void (WINAPIV*)(CLogTypeDBTaskManagerDestroy8_ptr);
        using CLogTypeDBTaskManagerGetDBProc10_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerGetDBProc10_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerGetDBProc10_ptr);
        using CLogTypeDBTaskManagerGetDBTaskConnectionStatus12_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerGetDBTaskConnectionStatus12_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerGetDBTaskConnectionStatus12_ptr);
        using CLogTypeDBTaskManagerGetDBTaskDataStatus14_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerGetDBTaskDataStatus14_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerGetDBTaskDataStatus14_ptr);
        using CLogTypeDBTaskManagerInit16_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerInit16_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerInit16_ptr);
        using CLogTypeDBTaskManagerInitDB18_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, char*, char*);
        using CLogTypeDBTaskManagerInitDB18_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, char*, char*, CLogTypeDBTaskManagerInitDB18_ptr);
        using CLogTypeDBTaskManagerInitLogger20_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerInitLogger20_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerInitLogger20_ptr);
        using CLogTypeDBTaskManagerInstance22_ptr = struct CLogTypeDBTaskManager* (WINAPIV*)();
        using CLogTypeDBTaskManagerInstance22_clbk = struct CLogTypeDBTaskManager* (WINAPIV*)(CLogTypeDBTaskManagerInstance22_ptr);
        using CLogTypeDBTaskManagerIsInitialized24_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerIsInitialized24_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerIsInitialized24_ptr);
        using CLogTypeDBTaskManagerLog26_ptr = void (WINAPIV*)(struct CLogTypeDBTaskManager*, char*);
        using CLogTypeDBTaskManagerLog26_clbk = void (WINAPIV*)(struct CLogTypeDBTaskManager*, char*, CLogTypeDBTaskManagerLog26_ptr);
        using CLogTypeDBTaskManagerLoop28_ptr = void (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerLoop28_clbk = void (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerLoop28_ptr);
        using CLogTypeDBTaskManagerProcComplete30_ptr = void (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerProcComplete30_clbk = void (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerProcComplete30_ptr);
        using CLogTypeDBTaskManagerProcThread32_ptr = void (WINAPIV*)(void*);
        using CLogTypeDBTaskManagerProcThread32_clbk = void (WINAPIV*)(void*, CLogTypeDBTaskManagerProcThread32_ptr);
        using CLogTypeDBTaskManagerPush34_ptr = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, char, char*, uint16_t);
        using CLogTypeDBTaskManagerPush34_clbk = bool (WINAPIV*)(struct CLogTypeDBTaskManager*, char, char*, uint16_t, CLogTypeDBTaskManagerPush34_ptr);
        
        using CLogTypeDBTaskManagerdtor_CLogTypeDBTaskManager38_ptr = void (WINAPIV*)(struct CLogTypeDBTaskManager*);
        using CLogTypeDBTaskManagerdtor_CLogTypeDBTaskManager38_clbk = void (WINAPIV*)(struct CLogTypeDBTaskManager*, CLogTypeDBTaskManagerdtor_CLogTypeDBTaskManager38_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_personal_amine_errmsg_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _personal_amine_errmsg_zoclctor__personal_amine_errmsg_zocl2_ptr = void (WINAPIV*)(struct _personal_amine_errmsg_zocl*);
        using _personal_amine_errmsg_zoclctor__personal_amine_errmsg_zocl2_clbk = void (WINAPIV*)(struct _personal_amine_errmsg_zocl*, _personal_amine_errmsg_zoclctor__personal_amine_errmsg_zocl2_ptr);
        using _personal_amine_errmsg_zoclsize4_ptr = int (WINAPIV*)(struct _personal_amine_errmsg_zocl*);
        using _personal_amine_errmsg_zoclsize4_clbk = int (WINAPIV*)(struct _personal_amine_errmsg_zocl*, _personal_amine_errmsg_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

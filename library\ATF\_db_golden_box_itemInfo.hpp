// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_db_golden_box_item.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _db_golden_box_itemctor__db_golden_box_item2_ptr = void (WINAPIV*)(struct _db_golden_box_item*);
        using _db_golden_box_itemctor__db_golden_box_item2_clbk = void (WINAPIV*)(struct _db_golden_box_item*, _db_golden_box_itemctor__db_golden_box_item2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

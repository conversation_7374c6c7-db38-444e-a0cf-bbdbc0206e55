// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _ContPotionData
    {
        unsigned int m_dwPotionEffectIndex;
        unsigned int m_dwStartSec;
        unsigned __int16 m_wDurCapSec;
        unsigned int m_dwID;
    public:
        unsigned int GetEffectIndex();
        void Init();
        bool IsLive();
        void Set(unsigned int dwPotionIndex, unsigned int dwStartTime, unsigned int wDurCapSec);
        _ContPotionData();
        void ctor__ContPotionData();
    };    
    static_assert(ATF::checkSize<_ContPotionData, 16>(), "_ContPotionData");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ContPotionData.hpp>


START_ATF_NAMESPACE
    struct CPotionParam
    {
        _ContPotionData m_ContCommonPotionData[2];
        _ContPotionData m_StoneOfMovePotionData;
        unsigned int m_dwNextUseTime[38];
        struct CPlayer *m_pMaster;
    public:
        CPotionParam();
        void ctor_CPotionParam();
        void Init(struct CPlayer* pMaster);
        bool IsUsableActDelay(char byPotionClass, unsigned int dwCurrTime);
        void SetPotionActDelay(char byPotionClass, unsigned int dwCurrTime, unsigned int dwActDelay);
        ~CPotionParam();
        void dtor_CPotionParam();
    };    
    static_assert(ATF::checkSize<CPotionParam, 208>(), "CPotionParam");
END_ATF_NAMESPACE

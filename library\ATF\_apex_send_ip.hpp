// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _apex_send_ip
    {
        char m_byOnce;
        unsigned int m_dwIp;
    public:
        _apex_send_ip();
        void ctor__apex_send_ip();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_apex_send_ip, 5>(), "_apex_send_ip");
END_ATF_NAMESPACE

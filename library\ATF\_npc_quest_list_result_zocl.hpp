// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _npc_quest_list_result_zocl
    {
        char byQuestNum;
        unsigned int QuestIndexList[30];
    public:
        _npc_quest_list_result_zocl();
        void ctor__npc_quest_list_result_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_npc_quest_list_result_zocl, 121>(), "_npc_quest_list_result_zocl");
END_ATF_NAMESPACE

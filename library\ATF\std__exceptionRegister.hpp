// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <common/ATFCore.hpp>
#include <std__exceptionDetail.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Register
        {
            class exceptionRegister : public IRegister
            {
                public: 
                    void Register() override
                    {
                        auto& hook_core = CATFCore::get_instance();
                        for (auto& r : std::Detail::exception_functions)
                            hook_core.reg_wrapper(r.pBind, r);
                    }
            };
        }; // end namespace Register
    }; // end namespace std
END_ATF_NAMESPACE

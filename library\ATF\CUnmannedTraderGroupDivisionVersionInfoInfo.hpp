// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderGroupDivisionVersionInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderGroupDivisionVersionInfoctor_CUnmannedTraderGroupDivisionVersionInfo2_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, struct CUnmannedTraderGroupDivisionVersionInfo*);
        using CUnmannedTraderGroupDivisionVersionInfoctor_CUnmannedTraderGroupDivisionVersionInfo2_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, struct CUnmannedTraderGroupDivisionVersionInfo*, CUnmannedTraderGroupDivisionVersionInfoctor_CUnmannedTraderGroupDivisionVersionInfo2_ptr);
        
        using CUnmannedTraderGroupDivisionVersionInfoctor_CUnmannedTraderGroupDivisionVersionInfo4_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, int, unsigned int);
        using CUnmannedTraderGroupDivisionVersionInfoctor_CUnmannedTraderGroupDivisionVersionInfo4_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, int, unsigned int, CUnmannedTraderGroupDivisionVersionInfoctor_CUnmannedTraderGroupDivisionVersionInfo4_ptr);
        using CUnmannedTraderGroupDivisionVersionInfoGetVersion6_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, char, unsigned int*);
        using CUnmannedTraderGroupDivisionVersionInfoGetVersion6_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, char, unsigned int*, CUnmannedTraderGroupDivisionVersionInfoGetVersion6_ptr);
        using CUnmannedTraderGroupDivisionVersionInfoIncreaseVersion8_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, char);
        using CUnmannedTraderGroupDivisionVersionInfoIncreaseVersion8_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, char, CUnmannedTraderGroupDivisionVersionInfoIncreaseVersion8_ptr);
        using CUnmannedTraderGroupDivisionVersionInfoIsEmpty10_ptr = bool (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*);
        using CUnmannedTraderGroupDivisionVersionInfoIsEmpty10_clbk = bool (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, CUnmannedTraderGroupDivisionVersionInfoIsEmpty10_ptr);
        
        using CUnmannedTraderGroupDivisionVersionInfodtor_CUnmannedTraderGroupDivisionVersionInfo16_ptr = void (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*);
        using CUnmannedTraderGroupDivisionVersionInfodtor_CUnmannedTraderGroupDivisionVersionInfo16_clbk = void (WINAPIV*)(struct CUnmannedTraderGroupDivisionVersionInfo*, CUnmannedTraderGroupDivisionVersionInfodtor_CUnmannedTraderGroupDivisionVersionInfo16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _economy_calc_data
    {
        long double dTradeGold[3];
        long double dTradeDalant[3];
        long double dOreMineCount[3][3];
        long double dOreCutCount[3][3];
        float out_fPayExgRate[3];
        float out_fTexRate[3];
        float out_fOreRate[3];
        unsigned __int16 out_wEconomyGuide[3];
        unsigned int out_dwTexRate[3];
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_economy_calc_data, 248>(), "_economy_calc_data");
END_ATF_NAMESPACE

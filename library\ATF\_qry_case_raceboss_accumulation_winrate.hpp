// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_raceboss_accumulation_winrate
    {
        unsigned int dwTotalCnt[3];
        unsigned int dwWinCnt[3];
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_qry_case_raceboss_accumulation_winrate, 24>(), "_qry_case_raceboss_accumulation_winrate");
END_ATF_NAMESPACE

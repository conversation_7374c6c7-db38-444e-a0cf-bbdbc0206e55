// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union $DE343801BC75A137C516A9D9B3162490
    {
        unsigned int dwBackBufferCount;
        unsigned int dwDepth;
    };    
    static_assert(ATF::checkSize<$DE343801BC75A137C516A9D9B3162490, 4>(), "$DE343801BC75A137C516A9D9B3162490");
END_ATF_NAMESPACE

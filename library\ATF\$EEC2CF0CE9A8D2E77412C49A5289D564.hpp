// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$5CC4D6FD2BF4E5B80FE1FD714DCA0CFA.hpp>


START_ATF_NAMESPACE
    union $EEC2CF0CE9A8D2E77412C49A5289D564
    {
        $5CC4D6FD2BF4E5B80FE1FD714DCA0CFA __s0;
        unsigned __int16 signscale;
    };    
    static_assert(ATF::checkSize<$EEC2CF0CE9A8D2E77412C49A5289D564, 2>(), "$EEC2CF0CE9A8D2E77412C49A5289D564");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    const struct ImgDelayDescr
    {
        unsigned int grAttrs;
        unsigned int rvaDLLName;
        unsigned int rvaHmod;
        unsigned int rvaIAT;
        unsigned int rvaINT;
        unsigned int rvaBoundIAT;
        unsigned int rvaUnloadIAT;
        unsigned int dwTimeStamp;
    };    
    static_assert(ATF::checkSize<ImgDelayDescr, 32>(), "ImgDelayDescr");
END_ATF_NAMESPACE

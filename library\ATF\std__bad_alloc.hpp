// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__exception.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct  bad_alloc : exception
        {
        public:
            bad_alloc(char* _Message);
            void ctor_bad_alloc(char* _Message);
            bad_alloc(struct bad_alloc* __that);
            void ctor_bad_alloc(struct bad_alloc* __that);
            bad_alloc();
            int64_t ctor_bad_alloc();
            ~bad_alloc();
            void dtor_bad_alloc();
        };    
        static_assert(ATF::checkSize<std::bad_alloc, 24>(), "std::bad_alloc");
    }; // end namespace std
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _parkingunit_fixpositon_zocl
    {
        unsigned __int16 wObjIndex;
        unsigned int dwObjSerial;
        char by<PERSON>rame;
        char byPart[6];
        __int16 zPos[3];
        unsigned int dwMasterSerial;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

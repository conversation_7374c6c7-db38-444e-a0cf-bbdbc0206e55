// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <CNetwork.hpp>
#include <CPlayer.hpp>
#include <_MSG_HEADER.hpp>


START_ATF_NAMESPACE
    struct  CChiNetworkEX : CNetwork
    {
        char m_ip[16];
        unsigned __int16 m_port;
        CMyTimer m_kCheckApexLineTimer;
    public:
        void AcceptClientCheck(unsigned int dwProID, unsigned int dwIndex, unsigned int dwSerial);
        CChiNetworkEX();
        void ctor_CChiNetworkEX();
        void CheckApexLine();
        void CloseClientCheck(unsigned int dwProID, unsigned int dwIndex, unsigned int dwSerial);
        static void Destory();
        void Inform_For_Exit_By_ApexBlock(unsigned int dwAccountSerial);
        int Initialize();
        static struct CChiNetworkEX* Instance();
        int LoadINIFile();
        void Recv_ApexInform(unsigned int dwSID, unsigned int dwRecvSize, char* pMsg);
        void Recv_ApexKill(unsigned int dwSID, unsigned int dwRecvSize, char* pMsg);
        int Send(char* pbyType, unsigned int dwSID, char* szMsg, uint16_t nLen);
        void Send_ClienInform(struct CPlayer* pOne, uint16_t wSize, char* pBuf);
        void Send_IP(struct CPlayer* pOne);
        void Send_Login(struct CPlayer* pOne);
        void Send_Logout(struct CPlayer* pOne);
        void Send_Trans(struct CPlayer* pOne, unsigned int dwRet);
        static bool s_DataAnalysis(unsigned int dwProID, unsigned int dwClientIndex, struct _MSG_HEADER* pMsgHeader, char* pMsg);
        ~CChiNetworkEX();
        void dtor_CChiNetworkEX();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_insert_patriarch_comm.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_insert_patriarch_commctor__qry_case_insert_patriarch_comm2_ptr = void (WINAPIV*)(struct _qry_case_insert_patriarch_comm*);
        using _qry_case_insert_patriarch_commctor__qry_case_insert_patriarch_comm2_clbk = void (WINAPIV*)(struct _qry_case_insert_patriarch_comm*, _qry_case_insert_patriarch_commctor__qry_case_insert_patriarch_comm2_ptr);
        using _qry_case_insert_patriarch_commsize4_ptr = int (WINAPIV*)(struct _qry_case_insert_patriarch_comm*);
        using _qry_case_insert_patriarch_commsize4_clbk = int (WINAPIV*)(struct _qry_case_insert_patriarch_comm*, _qry_case_insert_patriarch_commsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union MessageMapFunctions
    {
        void (WINAPIV *pfn)(CCmdTarget *_this);
        int (WINAPIV *pfn_b_D)(CCmdTarget *_this, CDC *);
        int (WINAPIV *pfn_b_b)(CCmdTarget *_this, int);
        int (WINAPIV *pfn_b_u)(CCmdTarget *_this, unsigned int);
        int (WINAPIV *pfn_b_h)(CCmdTarget *_this, void *);
        int (WINAPIV *pfn_b_W_u_u)(CCmdTarget *_this, CWnd *, unsigned int, unsigned int);
        int (WINAPIV *pfn_b_W_COPYDATASTRUCT)(CCmdTarget *_this, CWnd *, tagCOPYDATASTRUCT *);
        int (WINAPIV *pfn_b_HELPINFO)(CCmdTarget *_this, tagHELPINFO *);
        HBRUSH__ *(WINAPIV *pfn_B_D_W_u)(CCmdTarget *_this, CDC *, CWnd *, unsigned int);
        HBRUSH__ *(WINAPIV *pfn_B_D_u)(CCmdTarget *_this, CDC *, unsigned int);
        int (WINAPIV *pfn_i_u_W_u)(CCmdTarget *_this, unsigned int, CWnd *, unsigned int);
        int (WINAPIV *pfn_i_u_u)(CCmdTarget *_this, unsigned int, unsigned int);
        int (WINAPIV *pfn_i_W_u_u)(CCmdTarget *_this, CWnd *, unsigned int, unsigned int);
        int (WINAPIV *pfn_i_s)(CWnd *_this, char *);
        __int64 (WINAPIV *pfn_l_w_l)(CWnd *_this, unsigned __int64, __int64);
        __int64 (WINAPIV *pfn_l_u_u_M)(CWnd *_this, unsigned int, unsigned int, CMenu *);
        void (WINAPIV *pfn_v_b_h)(CWnd *_this, int, void *);
        void (WINAPIV *pfn_v_h)(CWnd *_this, void *);
        void (WINAPIV *pfn_v_h_h)(CWnd *_this, void *, void *);
        void (WINAPIV *pfn_v_v)(CWnd *_this);
        int (WINAPIV *pfn_i_u)(CWnd *_this, unsigned int);
        HICON__ *(WINAPIV *pfn_C_v)(CWnd *_this);
        unsigned int (WINAPIV *pfn_u_u)(CWnd *_this, unsigned int);
        int (WINAPIV *pfn_b_v)(CWnd *_this);
        void (WINAPIV *pfn_v_u)(CWnd *_this, unsigned int);
        void (WINAPIV *pfn_v_u_u)(CWnd *_this, unsigned int, unsigned int);
        void (WINAPIV *pfn_v_i_i)(CWnd *_this, int, int);
        void (WINAPIV *pfn_v_u_u_u)(CWnd *_this, unsigned int, unsigned int, unsigned int);
        void (WINAPIV *pfn_v_u_i_i)(CWnd *_this, unsigned int, int, int);
        void (WINAPIV *pfn_v_w_l)(CWnd *_this, unsigned __int64, __int64);
        void (WINAPIV *pfn_v_b_W_W)(CWnd *_this, int, CWnd *, CWnd *);
        void (WINAPIV *pfn_v_D)(CWnd *_this, CDC *);
        void (WINAPIV *pfn_v_M)(CWnd *_this, CMenu *);
        void (WINAPIV *pfn_v_M_u_b)(CWnd *_this, CMenu *, unsigned int, int);
        void (WINAPIV *pfn_v_W)(CWnd *_this, CWnd *);
        void (WINAPIV *pfn_v_W_u_u)(CWnd *_this, CWnd *, unsigned int, unsigned int);
        void (WINAPIV *pfn_v_W_p)(CWnd *_this, CWnd *, CPoint);
        void (WINAPIV *pfn_v_W_h)(CWnd *_this, CWnd *, void *);
        void (WINAPIV *pfn_v_u_W)(CWnd *_this, unsigned int, CWnd *);
        void (WINAPIV *pfn_v_u_W_b)(CWnd *_this, unsigned int, CWnd *, int);
        void (WINAPIV *pfn_v_u_u_W)(CWnd *_this, unsigned int, unsigned int, CWnd *);
        void (WINAPIV *pfn_v_s)(CWnd *_this, char *);
        void (WINAPIV *pfn_v_u_cs)(CWnd *_this, unsigned int, const char *);
        void (WINAPIV *pfn_v_i_s)(CWnd *_this, int, char *);
        int (WINAPIV *pfn_i_i_s)(CWnd *_this, int, char *);
        unsigned int (WINAPIV *pfn_u_p)(CWnd *_this, CPoint);
        __int64 (WINAPIV *pfn_l_p)(CWnd *_this, CPoint);
        unsigned int (WINAPIV *pfn_u_v)(CWnd *_this);
        void (WINAPIV *pfn_v_b_NCCALCSIZEPARAMS)(CWnd *_this, int, tagNCCALCSIZE_PARAMS *);
        void (WINAPIV *pfn_v_v_WINDOWPOS)(CWnd *_this, tagWINDOWPOS *);
        void (WINAPIV *pfn_v_u_u_M)(CWnd *_this, unsigned int, unsigned int, HMENU__ *);
        void (WINAPIV *pfn_v_u_p)(CWnd *_this, unsigned int, CPoint);
        void (WINAPIV *pfn_v_u_pr)(CWnd *_this, unsigned int, tagRECT *);
        int (WINAPIV *pfn_b_u_s_p)(CWnd *_this, unsigned int, __int16, CPoint);
        __int64 (WINAPIV *pfn_l_v)(CWnd *_this);
        void (WINAPIV *pfn_THREAD)(CWinThread *_this, unsigned __int64, __int64);
        void (WINAPIV *pfnCmd_v_v)(CCmdTarget *_this);
        int (WINAPIV *pfnCmd_b_v)(CCmdTarget *_this);
        void (WINAPIV *pfnCmd_v_u)(CCmdTarget *_this, unsigned int);
        int (WINAPIV *pfnCmd_b_u)(CCmdTarget *_this, unsigned int);
        void (WINAPIV *pfnNotify_v_NMHDR_pl)(CCmdTarget *_this, tagNMHDR *, __int64 *);
        int (WINAPIV *pfnNotify_b_NMHDR_pl)(CCmdTarget *_this, tagNMHDR *, __int64 *);
        void (WINAPIV *pfnNotify_v_u_NMHDR_pl)(CCmdTarget *_this, unsigned int, tagNMHDR *, __int64 *);
        int (WINAPIV *pfnNotify_b_u_NMHDR_pl)(CCmdTarget *_this, unsigned int, tagNMHDR *, __int64 *);
        void (WINAPIV *pfnCmdUI_v_C)(CCmdTarget *_this, CCmdUI *);
        void (WINAPIV *pfnCmdUI_v_C_u)(CCmdTarget *_this, CCmdUI *, unsigned int);
        void (WINAPIV *pfnCmd_v_pv)(CCmdTarget *_this, void *);
        int (WINAPIV *pfnCmd_b_pv)(CCmdTarget *_this, void *);
        void (WINAPIV *pfn_vPOS)(CWnd *_this, tagWINDOWPOS *);
        void (WINAPIV *pfn_vCALC)(CWnd *_this, int, tagNCCALCSIZE_PARAMS *);
        void (WINAPIV *pfn_vwp)(CWnd *_this, unsigned int, CPoint);
        void (WINAPIV *pfn_vwwh)(CWnd *_this, unsigned int, unsigned int, void *);
        int (WINAPIV *pfn_bwsp)(CWnd *_this, unsigned int, __int16, CPoint);
    };
END_ATF_NAMESPACE

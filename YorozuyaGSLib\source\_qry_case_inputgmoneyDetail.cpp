#include <_qry_case_inputgmoneyDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        Info::_qry_case_inputgmoneysize2_ptr _qry_case_inputgmoneysize2_next(nullptr);
        Info::_qry_case_inputgmoneysize2_clbk _qry_case_inputgmoneysize2_user(nullptr);
        
        int _qry_case_inputgmoneysize2_wrapper(struct _qry_case_inputgmoney* _this)
        {
           return _qry_case_inputgmoneysize2_user(_this, _qry_case_inputgmoneysize2_next);
        };
        
        ::std::array<hook_record, 1> _qry_case_inputgmoney_functions = 
        {
            _hook_record {
                (LPVOID)0x1400ad290L,
                (LPVOID *)&_qry_case_inputgmoneysize2_user,
                (LPVOID *)&_qry_case_inputgmoneysize2_next,
                (LPVOID)cast_pointer_function(_qry_case_inputgmoneysize2_wrapper),
                (LPVOID)cast_pointer_function((int(_qry_case_inputgmoney::*)())&_qry_case_inputgmoney::size)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE

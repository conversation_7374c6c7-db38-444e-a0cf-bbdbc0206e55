// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct tagHELPWININFOA
    {
        int wStructSize;
        int x;
        int y;
        int dx;
        int dy;
        int wMax;
        char rgchMember[2];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

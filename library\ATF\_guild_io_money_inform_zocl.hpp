// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _guild_io_money_inform_zocl
    {
        unsigned int dwIOerSerial;
        char byKind;
        bool bInPut;
        long double dIODalant;
        long double dIOGold;
        long double dTotalDalant;
        long double dTotalGold;
        char byDate[4];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

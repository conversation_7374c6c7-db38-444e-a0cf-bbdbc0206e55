// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagFORMATETC.hpp>


START_ATF_NAMESPACE
    struct tagOLEUIPASTEENTRYA
    {
        tagFORMATETC fmtetc;
        const char *lpstrFormatName;
        const char *lpstrResultText;
        unsigned int dwFlags;
        unsigned int dwScratchSpace;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HDC__.hpp>
#include <HWND__.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagDRAWITEMSTRUCT
    {
        unsigned int CtlType;
        unsigned int CtlID;
        unsigned int itemID;
        unsigned int itemAction;
        unsigned int itemState;
        HWND__ *hwndItem;
        HDC__ *hDC;
        tagRECT rcItem;
        unsigned __int64 itemData;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _post_return_zocl
    {
        struct  _post_item
        {
            char byTableCode;
            unsigned __int16 wItemIndex;
            unsigned __int64 dwDur;
            unsigned int dwLv;
        };
        char byErrCode;
        unsigned int dwPostSerial;
        char wszRecvName[17];
        char wszTitle[21];
        char wszContent[201];
        _post_item Item;
        unsigned int dwGold;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

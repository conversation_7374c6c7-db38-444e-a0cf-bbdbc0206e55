#include <_chat_multi_far_trans_zocl.hpp>


START_ATF_NAMESPACE
    _chat_multi_far_trans_zocl::_chat_multi_far_trans_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _chat_multi_far_trans_zocl*);
        (org_ptr(0x140095150L))(this);
    };
    void _chat_multi_far_trans_zocl::ctor__chat_multi_far_trans_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _chat_multi_far_trans_zocl*);
        (org_ptr(0x140095150L))(this);
    };
    int _chat_multi_far_trans_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _chat_multi_far_trans_zocl*);
        return (org_ptr(0x140095170L))(this);
    };
END_ATF_NAMESPACE

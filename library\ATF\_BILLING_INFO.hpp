// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _BILLING_INFO
    {
        __int16 iType;
        char szCMS[7];
        int lRemainTime;
        _SYSTEMTIME stEndDate;
        bool bPCCheat;
        bool bIsPcBang;
        bool bAgeLimit;
    public:
        bool IsPcBangType();
        _BILLING_INFO();
        void ctor__BILLING_INFO();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_BILLING_INFO, 36>(), "_BILLING_INFO");
END_ATF_NAMESPACE

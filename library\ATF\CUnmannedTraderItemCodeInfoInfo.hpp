// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderItemCodeInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderItemCodeInfoctor_CUnmannedTraderItemCodeInfo2_ptr = void (WINAPIV*)(struct CUnmannedTraderItemCodeInfo*, struct CUnmannedTraderItemCodeInfo*);
        using CUnmannedTraderItemCodeInfoctor_CUnmannedTraderItemCodeInfo2_clbk = void (WINAPIV*)(struct CUnmannedTraderItemCodeInfo*, struct CUnmannedTraderItemCodeInfo*, CUnmannedTraderItemCodeInfoctor_CUnmannedTraderItemCodeInfo2_ptr);
        
        using CUnmannedTraderItemCodeInfoctor_CUnmannedTraderItemCodeInfo4_ptr = void (WINAPIV*)(struct CUnmannedTraderItemCodeInfo*, char*, unsigned int, unsigned int);
        using CUnmannedTraderItemCodeInfoctor_CUnmannedTraderItemCodeInfo4_clbk = void (WINAPIV*)(struct CUnmannedTraderItemCodeInfo*, char*, unsigned int, unsigned int, CUnmannedTraderItemCodeInfoctor_CUnmannedTraderItemCodeInfo4_ptr);
        
        using CUnmannedTraderItemCodeInfodtor_CUnmannedTraderItemCodeInfo12_ptr = void (WINAPIV*)(struct CUnmannedTraderItemCodeInfo*);
        using CUnmannedTraderItemCodeInfodtor_CUnmannedTraderItemCodeInfo12_clbk = void (WINAPIV*)(struct CUnmannedTraderItemCodeInfo*, CUnmannedTraderItemCodeInfodtor_CUnmannedTraderItemCodeInfo12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_event_set.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _event_setctor__event_set2_ptr = void (WINAPIV*)(struct _event_set*);
        using _event_setctor__event_set2_clbk = void (WINAPIV*)(struct _event_set*, _event_setctor__event_set2_ptr);
        using _event_setinit4_ptr = void (WINAPIV*)(struct _event_set*);
        using _event_setinit4_clbk = void (WINAPIV*)(struct _event_set*, _event_setinit4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

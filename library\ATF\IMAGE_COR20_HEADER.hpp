// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$D0FAA225C480F7E79DE0C59B79FF3D5C.hpp>
#include <_IMAGE_DATA_DIRECTORY.hpp>


START_ATF_NAMESPACE
    struct IMAGE_COR20_HEADER
    {
        unsigned int cb;
        unsigned __int16 MajorRuntimeVersion;
        unsigned __int16 MinorRuntimeVersion;
        _IMAGE_DATA_DIRECTORY MetaData;
        unsigned int Flags;
        $D0FAA225C480F7E79DE0C59B79FF3D5C ___u5;
        _IMAGE_DATA_DIRECTORY Resources;
        _IMAGE_DATA_DIRECTORY StrongNameSignature;
        _IMAGE_DATA_DIRECTORY CodeManagerTable;
        _IMAGE_DATA_DIRECTORY VTableFixups;
        _IMAGE_DATA_DIRECTORY ExportAddressTableJumps;
        _IMAGE_DATA_DIRECTORY ManagedNativeHeader;
    };
END_ATF_NAMESPACE

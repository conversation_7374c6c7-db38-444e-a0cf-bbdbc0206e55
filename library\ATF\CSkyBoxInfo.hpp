// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CSkyBox.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CSkyBoxDrawSkyBox1_ptr = void (WINAPIV*)(struct CSkyBox*, void*);
        using CSkyBoxDrawSkyBox1_clbk = void (WINAPIV*)(struct CSkyBox*, void*, CSkyBoxDrawSkyBox1_ptr);
        using CSkyBoxGetUsedVertexBufferSize2_ptr = int64_t (WINAPIV*)(struct CSkyBox*);
        using CSkyBoxGetUsedVertexBufferSize2_clbk = int64_t (WINAPIV*)(struct CSkyBox*, CSkyBoxGetUsedVertexBufferSize2_ptr);
        using CSkyBoxLoadSkyBox3_ptr = void (WINAPIV*)(struct CSkyBox*, char*);
        using CSkyBoxLoadSkyBox3_clbk = void (WINAPIV*)(struct CSkyBox*, char*, CSkyBoxLoadSkyBox3_ptr);
        using CSkyBoxReleaseSkyBox4_ptr = void (WINAPIV*)(struct CSkyBox*);
        using CSkyBoxReleaseSkyBox4_clbk = void (WINAPIV*)(struct CSkyBox*, CSkyBoxReleaseSkyBox4_ptr);
        
        using CSkyBoxdtor_CSkyBox6_ptr = int64_t (WINAPIV*)(struct CSkyBox*);
        using CSkyBoxdtor_CSkyBox6_clbk = int64_t (WINAPIV*)(struct CSkyBox*, CSkyBoxdtor_CSkyBox6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

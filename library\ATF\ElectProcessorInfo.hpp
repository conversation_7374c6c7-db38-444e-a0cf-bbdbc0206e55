// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ElectProcessor.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using ElectProcessorDoit2_ptr = int (WINAPIV*)(struct ElectProcessor*, Cmd, struct CPlayer*, char*);
        using ElectProcessorDoit2_clbk = int (WINAPIV*)(struct ElectProcessor*, Cmd, struct CPlayer*, char*, ElectProcessorDoit2_ptr);
        
        using ElectProcessorctor_ElectProcessor4_ptr = void (WINAPIV*)(struct ElectProcessor*, ElectProcessor::ProcessorType);
        using ElectProcessorctor_ElectProcessor4_clbk = void (WINAPIV*)(struct ElectProcessor*, ElectProcessor::ProcessorType, ElectProcessorctor_ElectProcessor4_ptr);
        using ElectProcessorGetProcessorType6_ptr = ElectProcessor::ProcessorType (WINAPIV*)(struct ElectProcessor*);
        using ElectProcessorGetProcessorType6_clbk = ElectProcessor::ProcessorType (WINAPIV*)(struct ElectProcessor*, ElectProcessorGetProcessorType6_ptr);
        using ElectProcessorInitialize8_ptr = bool (WINAPIV*)(struct ElectProcessor*);
        using ElectProcessorInitialize8_clbk = bool (WINAPIV*)(struct ElectProcessor*, ElectProcessorInitialize8_ptr);
        
        using ElectProcessordtor_ElectProcessor13_ptr = void (WINAPIV*)(struct ElectProcessor*);
        using ElectProcessordtor_ElectProcessor13_clbk = void (WINAPIV*)(struct ElectProcessor*, ElectProcessordtor_ElectProcessor13_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

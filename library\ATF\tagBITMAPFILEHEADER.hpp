// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  tagBITMAPFILEHEADER
    {
        unsigned __int16 bfType;
        unsigned int bfSize;
        unsigned __int16 bfReserved1;
        unsigned __int16 bfReserved2;
        unsigned int bfOffBits;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

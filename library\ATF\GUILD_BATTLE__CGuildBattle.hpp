// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleVtbl.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct CGuildBattle
        {
            enum GB_OBJ_TYPE
            {
                GB_OBJ_NONE = 0xFFFFFFFF,
                GB_OBJ_NORMAL = 0x0,
            };
            CGuildBattleVtbl *vfptr;
        public:
            CGuildBattle();
            void ctor_CGuildBattle();
            int GetObjType();
            ~CGuildBattle();
            void dtor_CGuildBattle();
        };
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

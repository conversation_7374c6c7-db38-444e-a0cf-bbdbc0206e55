// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_amine_workstate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_amine_workstatector__qry_case_amine_workstate2_ptr = void (WINAPIV*)(struct _qry_case_amine_workstate*);
        using _qry_case_amine_workstatector__qry_case_amine_workstate2_clbk = void (WINAPIV*)(struct _qry_case_amine_workstate*, _qry_case_amine_workstatector__qry_case_amine_workstate2_ptr);
        using _qry_case_amine_workstatesize4_ptr = int (WINAPIV*)(struct _qry_case_amine_workstate*);
        using _qry_case_amine_workstatesize4_clbk = int (WINAPIV*)(struct _qry_case_amine_workstate*, _qry_case_amine_workstatesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

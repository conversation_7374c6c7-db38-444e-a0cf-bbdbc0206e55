// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$01AE0C4CAA17A9DEB3CF9215716A3B58.hpp>
#include <$997E4CC073C079F3CEE01C460B9DB2EB.hpp>
#include <$CF4E6002FA4B19EC2239361E8AC5A039.hpp>
#include <$DAED55BFC94E6097478CA68F0B175AFE.hpp>
#include <$F006553FDB25FB59A59693178CCFF094.hpp>


START_ATF_NAMESPACE
    struct _DDPIXELFORMAT
    {
        unsigned int dwSize;
        unsigned int dwFlags;
        unsigned int dwFourCC;
        $DAED55BFC94E6097478CA68F0B175AFE ___u3;
        $CF4E6002FA4B19EC2239361E8AC5A039 ___u4;
        $997E4CC073C079F3CEE01C460B9DB2EB ___u5;
        $01AE0C4CAA17A9DEB3CF9215716A3B58 ___u6;
        $F006553FDB25FB59A59693178CCFF094 ___u7;
    };    
    static_assert(ATF::checkSize<_DDPIXELFORMAT, 32>(), "_DDPIXELFORMAT");
END_ATF_NAMESPACE

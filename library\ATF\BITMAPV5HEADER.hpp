// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagICEXYZTRIPLE.hpp>


START_ATF_NAMESPACE
    struct BITMAPV5HEADER
    {
        unsigned int bV5Size;
        int bV5Width;
        int bV5Height;
        unsigned __int16 bV5Planes;
        unsigned __int16 bV5BitCount;
        unsigned int bV5Compression;
        unsigned int bV5SizeImage;
        int bV5XPelsPerMeter;
        int bV5YPelsPerMeter;
        unsigned int bV5ClrUsed;
        unsigned int bV5ClrImportant;
        unsigned int bV5RedMask;
        unsigned int bV5GreenMask;
        unsigned int bV5BlueMask;
        unsigned int bV5AlphaMask;
        unsigned int bV5CSType;
        tagICEXYZTRIPLE bV5Endpoints;
        unsigned int bV5GammaRed;
        unsigned int bV5GammaGreen;
        unsigned int bV5GammaBlue;
        unsigned int bV5Intent;
        unsigned int bV5ProfileData;
        unsigned int bV5ProfileSize;
        unsigned int bV5Reserved;
    };
END_ATF_NAMESPACE

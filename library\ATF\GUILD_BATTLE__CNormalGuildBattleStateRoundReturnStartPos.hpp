// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateRound.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        struct  CNormalGuildBattleStateRoundReturnStartPos : CNormalGuildBattleStateRound
        {
            CMyTimer *m_pkTimer;
        public:
            CNormalGuildBattleStateRoundReturnStartPos();
            void ctor_CNormalGuildBattleStateRoundReturnStartPos();
            int Enter(struct CNormalGuildBattle* pkBattle);
            int Loop(struct CNormalGuildBattle* pkBattle);
            ~CNormalGuildBattleStateRoundReturnStartPos();
            void dtor_CNormalGuildBattleStateRoundReturnStartPos();
        };    
        static_assert(ATF::checkSize<GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos, 16>(), "GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos");
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

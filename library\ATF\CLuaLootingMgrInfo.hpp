// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaLootingMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CLuaLootingMgrAddNovusItem2_ptr = bool (WINAPIV*)(struct CLuaLootingMgr*, char*, struct CMapData*, uint16_t, float*, uint16_t, unsigned int, unsigned int, char);
        using CLuaLootingMgrAddNovusItem2_clbk = bool (WINAPIV*)(struct CLuaLootingMgr*, char*, struct CMapData*, uint16_t, float*, uint16_t, unsigned int, unsigned int, char, CLuaLootingMgrAddNovusItem2_ptr);
        
        using CLuaLootingMgrctor_CLuaLootingMgr4_ptr = void (WINAPIV*)(struct CLuaLootingMgr*);
        using CLuaLootingMgrctor_CLuaLootingMgr4_clbk = void (WINAPIV*)(struct CLuaLootingMgr*, CLuaLootingMgrctor_CLuaLootingMgr4_ptr);
        using CLuaLootingMgrDestroy6_ptr = void (WINAPIV*)();
        using CLuaLootingMgrDestroy6_clbk = void (WINAPIV*)(CLuaLootingMgrDestroy6_ptr);
        using CLuaLootingMgrInitSDM8_ptr = bool (WINAPIV*)(struct CLuaLootingMgr*, unsigned int, unsigned int);
        using CLuaLootingMgrInitSDM8_clbk = bool (WINAPIV*)(struct CLuaLootingMgr*, unsigned int, unsigned int, CLuaLootingMgrInitSDM8_ptr);
        using CLuaLootingMgrInstance10_ptr = struct CLuaLootingMgr* (WINAPIV*)();
        using CLuaLootingMgrInstance10_clbk = struct CLuaLootingMgr* (WINAPIV*)(CLuaLootingMgrInstance10_ptr);
        using CLuaLootingMgrLoop12_ptr = void (WINAPIV*)(struct CLuaLootingMgr*);
        using CLuaLootingMgrLoop12_clbk = void (WINAPIV*)(struct CLuaLootingMgr*, CLuaLootingMgrLoop12_ptr);
        
        using CLuaLootingMgrdtor_CLuaLootingMgr16_ptr = void (WINAPIV*)(struct CLuaLootingMgr*);
        using CLuaLootingMgrdtor_CLuaLootingMgr16_clbk = void (WINAPIV*)(struct CLuaLootingMgr*, CLuaLootingMgrdtor_CLuaLootingMgr16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

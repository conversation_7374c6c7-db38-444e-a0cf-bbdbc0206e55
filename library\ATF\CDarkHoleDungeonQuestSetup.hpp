// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDarkHoleDungeonQuestSetupVtbl.hpp>
#include <__error_info.hpp>
#include <_dh_job_setup.hpp>
#include <_dh_mission_setup.hpp>
#include <_dh_quest_setup.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CDarkHoleDungeonQuestSetup
    {
        CDarkHoleDungeonQuestSetupVtbl *vfptr;
        int m_nLoadQuest;
        _dh_quest_setup *m_QuestSetup[100];
        char m_szLoadErrMsg[256];
        char m_szLoadFileName[256];
        int m_nCurCondiKind;
        _dh_quest_setup *m_pCurLoadQuest;
        _dh_mission_setup *m_pCurLoadMission;
        _dh_job_setup *m_pCurLoadJob;
        __error_info m_ErrorInfo;
    public:
        CDarkHoleDungeonQuestSetup();
        void ctor_CDarkHoleDungeonQuestSetup();
        char* GetErrorMsg();
        struct _dh_quest_setup* GetQuestSetupPtr(unsigned int dwQuestIndex);
        bool SetupQuest(char* pszQuestFileName);
        bool _Analysis_Job_Condition(struct strFILE* fstr);
        bool _Analysis_Job_Setting(struct strFILE* fstr);
        bool _Analysis_Mission_Condition(struct strFILE* fstr);
        bool _Analysis_Mission_Setting(struct strFILE* fstr);
        bool _Analysis_Quest_Condition(struct strFILE* fstr);
        bool _Analysis_Quest_Setting(struct strFILE* fstr);
        bool _LastCheckScipt(char* pszQuestFileName);
        ~CDarkHoleDungeonQuestSetup();
        void dtor_CDarkHoleDungeonQuestSetup();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CDarkHoleDungeonQuestSetup, 3416>(), "CDarkHoleDungeonQuestSetup");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    struct TInvenSlot
    {
        _INVENKEY m_Item;
        int m_nOverlapNum;
        int m_nMaxOverlapNum;
    public:
        TInvenSlot(struct TInvenSlot* rhs)
        {
            using org_ptr = void (WINAPIV*)(struct TInvenSlot*, struct TInvenSlot*);
            (org_ptr(0x1402d4ee0L))(this, rhs);
        };
        void ctor_TInvenSlot(struct TInvenSlot* rhs)
        {
            using org_ptr = void (WINAPIV*)(struct TInvenSlot*, struct TInvenSlot*);
            (org_ptr(0x1402d4ee0L))(this, rhs);
        };
        TInvenSlot()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenSlot*);
            (org_ptr(0x1402d5670L))(this);
        };
        void ctor_TInvenSlot()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenSlot*);
            (org_ptr(0x1402d5670L))(this);
        };
        bool able_overlap(struct _INVENKEY* pItem, int nNum)
        {
            using org_ptr = bool (WINAPIV*)(struct TInvenSlot*, struct _INVENKEY*, int);
            return (org_ptr(0x1402d5a20L))(this, pItem, nNum);
        };
        void clear()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenSlot*);
            (org_ptr(0x1402d5020L))(this);
        };
        unsigned int get_overlapnum()
        {
            using org_ptr = unsigned int (WINAPIV*)(struct TInvenSlot*);
            return (org_ptr(0x1402d4d60L))(this);
        };
        struct _INVENKEY* get_pitem()
        {
            using org_ptr = struct _INVENKEY* (WINAPIV*)(struct TInvenSlot*);
            return (org_ptr(0x1402d4de0L))(this);
        };
        bool is_empty()
        {
            using org_ptr = bool (WINAPIV*)(struct TInvenSlot*);
            return (org_ptr(0x1402d4d80L))(this);
        };
        int pop(struct _INVENKEY* pItem, int nNum)
        {
            using org_ptr = int (WINAPIV*)(struct TInvenSlot*, struct _INVENKEY*, int);
            return (org_ptr(0x1402d56f0L))(this, pItem, nNum);
        };
        int push(struct _INVENKEY* pItem, int nNum)
        {
            using org_ptr = int (WINAPIV*)(struct TInvenSlot*, struct _INVENKEY*, int);
            return (org_ptr(0x1402d5aa0L))(this, pItem, nNum);
        };
        void set_overlapnum(int n)
        {
            using org_ptr = void (WINAPIV*)(struct TInvenSlot*, int);
            (org_ptr(0x1402d5000L))(this, n);
        };
        ~TInvenSlot()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenSlot*);
            (org_ptr(0x1402d4f70L))(this);
        };
        void dtor_TInvenSlot()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenSlot*);
            (org_ptr(0x1402d4f70L))(this);
        };
    };    
    static_assert(ATF::checkSize<TInvenSlot, 12>(), "TInvenSlot");
END_ATF_NAMESPACE

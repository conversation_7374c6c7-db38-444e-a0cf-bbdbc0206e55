// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HICON__.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _SHFILEINFOA
    {
        HICON__ *hIcon;
        int iIcon;
        unsigned int dwAttributes;
        char szDisplayName[260];
        char szTypeName[80];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRFCashItemDatabase.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRFCashItemDatabasector_CRFCashItemDatabase2_ptr = void (WINAPIV*)(struct CRFCashItemDatabase*);
        using CRFCashItemDatabasector_CRFCashItemDatabase2_clbk = void (WINAPIV*)(struct CRFCashItemDatabase*, CRFCashItemDatabasector_CRFCashItemDatabase2_ptr);
        using CRFCashItemDatabaseCallProc_InsertCashItemLog4_ptr = bool (WINAPIV*)(struct CRFCashItemDatabase*, unsigned int, char, char*, char*, char, unsigned int);
        using CRFCashItemDatabaseCallProc_InsertCashItemLog4_clbk = bool (WINAPIV*)(struct CRFCashItemDatabase*, unsigned int, char, char*, char*, char, unsigned int, CRFCashItemDatabaseCallProc_InsertCashItemLog4_ptr);
        using CRFCashItemDatabaseCallProc_RFONLINE_Cancel6_ptr = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_rollback::__list*);
        using CRFCashItemDatabaseCallProc_RFONLINE_Cancel6_clbk = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_rollback::__list*, CRFCashItemDatabaseCallProc_RFONLINE_Cancel6_ptr);
        using CRFCashItemDatabaseCallProc_RFONLINE_Cancel_Jap8_ptr = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_rollback*, int);
        using CRFCashItemDatabaseCallProc_RFONLINE_Cancel_Jap8_clbk = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_rollback*, int, CRFCashItemDatabaseCallProc_RFONLINE_Cancel_Jap8_ptr);
        using CRFCashItemDatabaseCallProc_RFOnlineAuth10_ptr = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_select*);
        using CRFCashItemDatabaseCallProc_RFOnlineAuth10_clbk = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_select*, CRFCashItemDatabaseCallProc_RFOnlineAuth10_ptr);
        using CRFCashItemDatabaseCallProc_RFOnlineAuth_Jap12_ptr = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_select*);
        using CRFCashItemDatabaseCallProc_RFOnlineAuth_Jap12_clbk = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_select*, CRFCashItemDatabaseCallProc_RFOnlineAuth_Jap12_ptr);
        using CRFCashItemDatabaseCallProc_RFOnlineAvg_Event14_ptr = int (WINAPIV*)(struct CRFCashItemDatabase*, unsigned int*);
        using CRFCashItemDatabaseCallProc_RFOnlineAvg_Event14_clbk = int (WINAPIV*)(struct CRFCashItemDatabase*, unsigned int*, CRFCashItemDatabaseCallProc_RFOnlineAvg_Event14_ptr);
        using CRFCashItemDatabaseCallProc_RFOnlineUse16_ptr = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_update*, int);
        using CRFCashItemDatabaseCallProc_RFOnlineUse16_clbk = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_update*, int, CRFCashItemDatabaseCallProc_RFOnlineUse16_ptr);
        using CRFCashItemDatabaseCallProc_RFOnlineUse_Jap18_ptr = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_update*, int);
        using CRFCashItemDatabaseCallProc_RFOnlineUse_Jap18_clbk = int (WINAPIV*)(struct CRFCashItemDatabase*, struct _param_cash_update*, int, CRFCashItemDatabaseCallProc_RFOnlineUse_Jap18_ptr);
        using CRFCashItemDatabasedhRExtractSubString23_ptr = void (WINAPIV*)(struct CRFCashItemDatabase*, char*, char*, int);
        using CRFCashItemDatabasedhRExtractSubString23_clbk = void (WINAPIV*)(struct CRFCashItemDatabase*, char*, char*, int, CRFCashItemDatabasedhRExtractSubString23_ptr);
        
        using CRFCashItemDatabasedtor_CRFCashItemDatabase25_ptr = void (WINAPIV*)(struct CRFCashItemDatabase*);
        using CRFCashItemDatabasedtor_CRFCashItemDatabase25_clbk = void (WINAPIV*)(struct CRFCashItemDatabase*, CRFCashItemDatabasedtor_CRFCashItemDatabase25_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

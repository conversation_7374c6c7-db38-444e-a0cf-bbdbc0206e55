// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__basic_string.hpp>
#include <std__exception.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct  logic_error : exception
        {
            basic_string<char> _Str;
        public:
            logic_error(basic_string<char>* _Message);
            void ctor_logic_error(basic_string<char>* _Message);
            logic_error(struct logic_error* __that);
            void ctor_logic_error(struct logic_error* __that);
            char* what();
            ~logic_error();
            void dtor_logic_error();
        };    
        static_assert(ATF::checkSize<std::logic_error, 72>(), "std::logic_error");
    }; // end namespace std
END_ATF_NAMESPACE

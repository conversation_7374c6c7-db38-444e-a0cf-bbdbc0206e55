// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <UsRefObjectVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct UsRefObject
    {
        UsRefObjectVtbl *vfptr;
        unsigned int m_uiRefCount;
    public:
        void DecRefCount();
        void IncRefCount();
        UsRefObject();
        void ctor_UsRefObject();
        ~UsRefObject();
        void dtor_UsRefObject();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

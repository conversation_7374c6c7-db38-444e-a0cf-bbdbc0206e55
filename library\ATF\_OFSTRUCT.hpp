// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _OFSTRUCT
    {
        char cBytes;
        char fFixedDisk;
        unsigned __int16 nErrCode;
        unsigned __int16 Reserved1;
        unsigned __int16 Reserved2;
        char szPathName[128];
    };
END_ATF_NAMESPACE

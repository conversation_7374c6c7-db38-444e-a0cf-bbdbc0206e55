// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTotalGuildRankManager.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CTotalGuildRankManagerctor_CTotalGuildRankManager2_ptr = void (WINAPIV*)(struct CTotalGuildRankManager*);
        using CTotalGuildRankManagerctor_CTotalGuildRankManager2_clbk = void (WINAPIV*)(struct CTotalGuildRankManager*, CTotalGuildRankManagerctor_CTotalGuildRankManager2_ptr);
        using CTotalGuildRankManagerDestroy4_ptr = void (WINAPIV*)();
        using CTotalGuildRankManagerDestroy4_clbk = void (WINAPIV*)(CTotalGuildRankManagerDestroy4_ptr);
        using CTotalGuildRankManagerInit6_ptr = bool (WINAPIV*)(struct CTotalGuildRankManager*);
        using CTotalGuildRankManagerInit6_clbk = bool (WINAPIV*)(struct CTotalGuildRankManager*, CTotalGuildRankManagerInit6_ptr);
        using CTotalGuildRankManagerInstance8_ptr = struct CTotalGuildRankManager* (WINAPIV*)();
        using CTotalGuildRankManagerInstance8_clbk = struct CTotalGuildRankManager* (WINAPIV*)(CTotalGuildRankManagerInstance8_ptr);
        using CTotalGuildRankManagerLoad10_ptr = int (WINAPIV*)(struct CTotalGuildRankManager*, char, struct _total_guild_rank_info*);
        using CTotalGuildRankManagerLoad10_clbk = int (WINAPIV*)(struct CTotalGuildRankManager*, char, struct _total_guild_rank_info*, CTotalGuildRankManagerLoad10_ptr);
        using CTotalGuildRankManagerLoad12_ptr = bool (WINAPIV*)(struct CTotalGuildRankManager*);
        using CTotalGuildRankManagerLoad12_clbk = bool (WINAPIV*)(struct CTotalGuildRankManager*, CTotalGuildRankManagerLoad12_ptr);
        using CTotalGuildRankManagerLoop14_ptr = void (WINAPIV*)(struct CTotalGuildRankManager*);
        using CTotalGuildRankManagerLoop14_clbk = void (WINAPIV*)(struct CTotalGuildRankManager*, CTotalGuildRankManagerLoop14_ptr);
        using CTotalGuildRankManagerOrderRank16_ptr = void (WINAPIV*)(struct CTotalGuildRankManager*, struct _total_guild_rank_info*);
        using CTotalGuildRankManagerOrderRank16_clbk = void (WINAPIV*)(struct CTotalGuildRankManager*, struct _total_guild_rank_info*, CTotalGuildRankManagerOrderRank16_ptr);
        using CTotalGuildRankManagerSend18_ptr = void (WINAPIV*)(struct CTotalGuildRankManager*, unsigned int, char, struct CPlayer*);
        using CTotalGuildRankManagerSend18_clbk = void (WINAPIV*)(struct CTotalGuildRankManager*, unsigned int, char, struct CPlayer*, CTotalGuildRankManagerSend18_ptr);
        using CTotalGuildRankManagerUpdate20_ptr = bool (WINAPIV*)(struct CTotalGuildRankManager*, char*);
        using CTotalGuildRankManagerUpdate20_clbk = bool (WINAPIV*)(struct CTotalGuildRankManager*, char*, CTotalGuildRankManagerUpdate20_ptr);
        using CTotalGuildRankManagerUpdateComlete22_ptr = void (WINAPIV*)(struct CTotalGuildRankManager*, char, char*);
        using CTotalGuildRankManagerUpdateComlete22_clbk = void (WINAPIV*)(struct CTotalGuildRankManager*, char, char*, CTotalGuildRankManagerUpdateComlete22_ptr);
        
        using CTotalGuildRankManagerdtor_CTotalGuildRankManager26_ptr = void (WINAPIV*)(struct CTotalGuildRankManager*);
        using CTotalGuildRankManagerdtor_CTotalGuildRankManager26_clbk = void (WINAPIV*)(struct CTotalGuildRankManager*, CTotalGuildRankManagerdtor_CTotalGuildRankManager26_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

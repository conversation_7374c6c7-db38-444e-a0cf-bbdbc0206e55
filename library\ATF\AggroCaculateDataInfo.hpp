// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AggroCaculateData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using AggroCaculateDatactor_AggroCaculateData2_ptr = void (WINAPIV*)(struct AggroCaculateData*);
        using AggroCaculateDatactor_AggroCaculateData2_clbk = void (WINAPIV*)(struct AggroCaculateData*, AggroCaculateDatactor_AggroCaculateData2_ptr);
        using AggroCaculateDataGetDefault4_ptr = int (WINAPIV*)(struct AggroCaculateData*, unsigned int);
        using AggroCaculateDataGetDefault4_clbk = int (WINAPIV*)(struct AggroCaculateData*, unsigned int, AggroCaculateDataGetDefault4_ptr);
        using AggroCaculateDataGetSize6_ptr = int (WINAPIV*)(struct AggroCaculateData*);
        using AggroCaculateDataGetSize6_clbk = int (WINAPIV*)(struct AggroCaculateData*, AggroCaculateDataGetSize6_ptr);
        using AggroCaculateDataGetSpecialData8_ptr = int (WINAPIV*)(struct AggroCaculateData*, char, uint16_t);
        using AggroCaculateDataGetSpecialData8_clbk = int (WINAPIV*)(struct AggroCaculateData*, char, uint16_t, AggroCaculateDataGetSpecialData8_ptr);
        using AggroCaculateDataInit10_ptr = void (WINAPIV*)(struct AggroCaculateData*);
        using AggroCaculateDataInit10_clbk = void (WINAPIV*)(struct AggroCaculateData*, AggroCaculateDataInit10_ptr);
        using AggroCaculateDataLoad12_ptr = int (WINAPIV*)(struct AggroCaculateData*, char*);
        using AggroCaculateDataLoad12_clbk = int (WINAPIV*)(struct AggroCaculateData*, char*, AggroCaculateDataLoad12_ptr);
        using AggroCaculateDataPushSpecialData14_ptr = int (WINAPIV*)(struct AggroCaculateData*, char, uint16_t, int);
        using AggroCaculateDataPushSpecialData14_clbk = int (WINAPIV*)(struct AggroCaculateData*, char, uint16_t, int, AggroCaculateDataPushSpecialData14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

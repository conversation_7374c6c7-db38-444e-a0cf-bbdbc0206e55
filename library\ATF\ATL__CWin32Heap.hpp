// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__IAtlMemMgr.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        #pragma pack(push, 8)
        struct  CWin32Heap : IAtlMemMgr
        {
            void *m_hHeap;
            bool m_bOwnHeap;
        };
        #pragma pack(pop)
    }; // end namespace ATL
END_ATF_NAMESPACE

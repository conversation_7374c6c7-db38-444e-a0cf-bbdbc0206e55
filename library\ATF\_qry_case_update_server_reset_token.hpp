// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_update_server_reset_token
    {
        unsigned int dwServerToken;
        unsigned int dwESerial;
        unsigned __int16 wProcType;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_update_server_reset_token, 12>(), "_qry_case_update_server_reset_token");
END_ATF_NAMESPACE

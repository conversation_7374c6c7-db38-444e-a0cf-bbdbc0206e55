// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerUS.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerUSctor_CCashDbWorkerUS2_ptr = void (WINAPIV*)(struct CCashDbWorkerUS*);
        using CCashDbWorkerUSctor_CCashDbWorkerUS2_clbk = void (WINAPIV*)(struct CCashDbWorkerUS*, CCashDbWorkerUSctor_CCashDbWorkerUS2_ptr);
        using CCashDbWorkerUSGetUseCashQueryStr4_ptr = void (WINAPIV*)(struct CCashDbWorkerUS*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDbWorkerUSGetUseCashQueryStr4_clbk = void (WINAPIV*)(struct CCashDbWorkerUS*, struct _param_cash_update*, int, char*, uint64_t, CCashDbWorkerUSGetUseCashQueryStr4_ptr);
        
        using CCashDbWorkerUSdtor_CCashDbWorkerUS9_ptr = void (WINAPIV*)(struct CCashDbWorkerUS*);
        using CCashDbWorkerUSdtor_CCashDbWorkerUS9_clbk = void (WINAPIV*)(struct CCashDbWorkerUS*, CCashDbWorkerUSdtor_CCashDbWorkerUS9_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__basic_istream.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 128)
        template<>
        struct  basic_ifstream<char,char_traits<char> > : basic_istream<char,char_traits<char> >
        {
            BYTE _Filebuffer[48];
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE
#include <std__basic_istream.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        #pragma pack(push, 128)
        template<>
        struct  basic_ifstream<wchar_t,char_traits<wchar_t> > : basic_istream<wchar_t,char_traits<wchar_t> >
        {
            BYTE _Filebuffer[48];
        };
        #pragma pack(pop)
    }; // end namespace std
END_ATF_NAMESPACE

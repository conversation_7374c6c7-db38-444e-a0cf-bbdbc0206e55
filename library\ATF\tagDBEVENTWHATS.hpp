// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum tagDBEVENTWHATS
    {
      DBEVENT_CURRENT_ROW_CHANGED = 0x1,
      DBEVENT_CURRENT_ROW_DATA_CHANGED = 0x2,
      DBEVENT_NONCURRENT_ROW_DATA_CHANGED = 0x4,
      DBEVENT_SET_OF_COLUMNS_CHANGED = 0x8,
      DBEVENT_ORDER_OF_COLUMNS_CHANGED = 0x10,
      DBEVENT_SET_OF_ROWS_CHANGED = 0x20,
      DBEVENT_ORDER_OF_ROWS_CHANGED = 0x40,
      DBEVENT_METADATA_CHANGED = 0x80,
      DBEVENT_ASYNCH_OP_FINISHED = 0x100,
      DBEVENT_FIND_CRITERIA_CHANGED = 0x200,
    };
END_ATF_NAMESPACE

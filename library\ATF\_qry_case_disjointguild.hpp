// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_disjointguild
    {
        unsigned int in_guildserial;
        unsigned int tmp_guildindex;
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_qry_case_disjointguild, 8>(), "_qry_case_disjointguild");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _con_event_ini
    {
        int m_bUseConEvent;
        unsigned int m_dwCashMin;
        int m_iEventTime;
        char m_byEventKind;
        char m_szStartMsg[8];
        char m_szMiddletMsg[8];
        char m_szEndMsg[8];
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_con_event_ini, 40>(), "_con_event_ini");
END_ATF_NAMESPACE

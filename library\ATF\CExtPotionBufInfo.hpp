// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CExtPotionBuf.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CExtPotionBufctor_CExtPotionBuf2_ptr = void (WINAPIV*)(struct CExtPotionBuf*);
        using CExtPotionBufctor_CExtPotionBuf2_clbk = void (WINAPIV*)(struct CExtPotionBuf*, CExtPotionBufctor_CExtPotionBuf2_ptr);
        using CExtPotionBufCalcRemainTime4_ptr = void (WINAPIV*)(struct CExtPotionBuf*, uint16_t, bool);
        using CExtPotionBufCalcRemainTime4_clbk = void (WINAPIV*)(struct CExtPotionBuf*, uint16_t, bool, CExtPotionBufCalcRemainTime4_ptr);
        using CExtPotionBufCheckPotionTime6_ptr = void (WINAPIV*)(struct CExtPotionBuf*, struct CPlayer*);
        using CExtPotionBufCheckPotionTime6_clbk = void (WINAPIV*)(struct CExtPotionBuf*, struct CPlayer*, CExtPotionBufCheckPotionTime6_ptr);
        using CExtPotionBufIsExtPotionUse8_ptr = bool (WINAPIV*)(struct CExtPotionBuf*);
        using CExtPotionBufIsExtPotionUse8_clbk = bool (WINAPIV*)(struct CExtPotionBuf*, CExtPotionBufIsExtPotionUse8_ptr);
        using CExtPotionBufSednMsg_RemovePotionContEffect10_ptr = void (WINAPIV*)(struct CExtPotionBuf*, uint16_t, uint16_t);
        using CExtPotionBufSednMsg_RemovePotionContEffect10_clbk = void (WINAPIV*)(struct CExtPotionBuf*, uint16_t, uint16_t, CExtPotionBufSednMsg_RemovePotionContEffect10_ptr);
        using CExtPotionBufSendMsg_RemainBufUseTime12_ptr = void (WINAPIV*)(struct CExtPotionBuf*, bool, uint16_t, int, int, int);
        using CExtPotionBufSendMsg_RemainBufUseTime12_clbk = void (WINAPIV*)(struct CExtPotionBuf*, bool, uint16_t, int, int, int, CExtPotionBufSendMsg_RemainBufUseTime12_ptr);
        using CExtPotionBufSetExtPotionBufUse14_ptr = void (WINAPIV*)(struct CExtPotionBuf*, bool);
        using CExtPotionBufSetExtPotionBufUse14_clbk = void (WINAPIV*)(struct CExtPotionBuf*, bool, CExtPotionBufSetExtPotionBufUse14_ptr);
        using CExtPotionBufSetExtPotionEndTime16_ptr = void (WINAPIV*)(struct CExtPotionBuf*, unsigned int);
        using CExtPotionBufSetExtPotionEndTime16_clbk = void (WINAPIV*)(struct CExtPotionBuf*, unsigned int, CExtPotionBufSetExtPotionEndTime16_ptr);
        using CExtPotionBufUseBuffPotion18_ptr = void (WINAPIV*)(struct CExtPotionBuf*, struct CPlayer*);
        using CExtPotionBufUseBuffPotion18_clbk = void (WINAPIV*)(struct CExtPotionBuf*, struct CPlayer*, CExtPotionBufUseBuffPotion18_ptr);
        
        using CExtPotionBufdtor_CExtPotionBuf20_ptr = void (WINAPIV*)(struct CExtPotionBuf*);
        using CExtPotionBufdtor_CExtPotionBuf20_clbk = void (WINAPIV*)(struct CExtPotionBuf*, CExtPotionBufdtor_CExtPotionBuf20_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

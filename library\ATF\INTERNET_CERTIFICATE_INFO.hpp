// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FILETIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct INTERNET_CERTIFICATE_INFO
    {
        _FILETIME ftExpiry;
        _FILETIME ftStart;
        char *lpszSubjectInfo;
        char *lpszIssuerInfo;
        char *lpszProtocolName;
        char *lpszSignatureAlgName;
        char *lpszEncryptionAlgName;
        unsigned int dwKeySize;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

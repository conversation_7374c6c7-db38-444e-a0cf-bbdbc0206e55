// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ChatStealTargetInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _ChatStealTargetInfoctor__ChatStealTargetInfo2_ptr = void (WINAPIV*)(struct _ChatStealTargetInfo*);
        using _ChatStealTargetInfoctor__ChatStealTargetInfo2_clbk = void (WINAPIV*)(struct _ChatStealTargetInfo*, _ChatStealTargetInfoctor__ChatStealTargetInfo2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

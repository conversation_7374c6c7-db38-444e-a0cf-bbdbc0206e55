// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagMEASUREITEMSTRUCT
    {
        unsigned int CtlType;
        unsigned int CtlID;
        unsigned int itemID;
        unsigned int itemWidth;
        unsigned int itemHeight;
        unsigned __int64 itemData;
    };
END_ATF_NAMESPACE

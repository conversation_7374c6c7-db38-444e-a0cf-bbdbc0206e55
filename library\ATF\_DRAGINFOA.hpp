// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _DRAGINFOA
    {
        unsigned int uSize;
        tagPOINT pt;
        int fNC;
        char *lpFileList;
        unsigned int grfKeyState;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <tagDBPROP.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagDBPROPSET
    {
        tagDBPROP *rgProperties;
        unsigned int cProperties;
        _GUID guidPropertySet;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

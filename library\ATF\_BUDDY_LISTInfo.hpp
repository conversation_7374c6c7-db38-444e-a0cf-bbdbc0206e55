// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_BUDDY_LIST.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _BUDDY_LISTGetBuddyNum2_ptr = int (WINAPIV*)(struct _BUDDY_LIST*);
        using _BUDDY_LISTGetBuddyNum2_clbk = int (WINAPIV*)(struct _BUDDY_LIST*, _BUDDY_LISTGetBuddyNum2_ptr);
        using _BUDDY_LISTGetEmptyData4_ptr = struct _BUDDY_LIST::__list* (WINAPIV*)(struct _BUDDY_LIST*);
        using _BUDDY_LISTGetEmptyData4_clbk = struct _BUDDY_LIST::__list* (WINAPIV*)(struct _BUDDY_LIST*, _BUDDY_LISTGetEmptyData4_ptr);
        using _BUDDY_LISTInit6_ptr = void (WINAPIV*)(struct _BUDDY_LIST*);
        using _BUDDY_LISTInit6_clbk = void (WINAPIV*)(struct _BUDDY_LIST*, _BUDDY_LISTInit6_ptr);
        using _BUDDY_LISTIsBuddy8_ptr = bool (WINAPIV*)(struct _BUDDY_LIST*, unsigned int);
        using _BUDDY_LISTIsBuddy8_clbk = bool (WINAPIV*)(struct _BUDDY_LIST*, unsigned int, _BUDDY_LISTIsBuddy8_ptr);
        using _BUDDY_LISTIsPushLastApply10_ptr = bool (WINAPIV*)(struct _BUDDY_LIST*, unsigned int);
        using _BUDDY_LISTIsPushLastApply10_clbk = bool (WINAPIV*)(struct _BUDDY_LIST*, unsigned int, _BUDDY_LISTIsPushLastApply10_ptr);
        using _BUDDY_LISTPopBuddy12_ptr = int (WINAPIV*)(struct _BUDDY_LIST*, unsigned int, struct CPlayer**);
        using _BUDDY_LISTPopBuddy12_clbk = int (WINAPIV*)(struct _BUDDY_LIST*, unsigned int, struct CPlayer**, _BUDDY_LISTPopBuddy12_ptr);
        using _BUDDY_LISTPopLastApplyTemp14_ptr = void (WINAPIV*)(struct _BUDDY_LIST*, unsigned int);
        using _BUDDY_LISTPopLastApplyTemp14_clbk = void (WINAPIV*)(struct _BUDDY_LIST*, unsigned int, _BUDDY_LISTPopLastApplyTemp14_ptr);
        using _BUDDY_LISTPushBuddy16_ptr = int (WINAPIV*)(struct _BUDDY_LIST*, unsigned int, char*, struct CPlayer*);
        using _BUDDY_LISTPushBuddy16_clbk = int (WINAPIV*)(struct _BUDDY_LIST*, unsigned int, char*, struct CPlayer*, _BUDDY_LISTPushBuddy16_ptr);
        using _BUDDY_LISTPushLastApplyTemp18_ptr = void (WINAPIV*)(struct _BUDDY_LIST*, unsigned int);
        using _BUDDY_LISTPushLastApplyTemp18_clbk = void (WINAPIV*)(struct _BUDDY_LIST*, unsigned int, _BUDDY_LISTPushLastApplyTemp18_ptr);
        using _BUDDY_LISTSearchBuddyLogin20_ptr = bool (WINAPIV*)(struct _BUDDY_LIST*, struct CPlayer*, unsigned int, char*);
        using _BUDDY_LISTSearchBuddyLogin20_clbk = bool (WINAPIV*)(struct _BUDDY_LIST*, struct CPlayer*, unsigned int, char*, _BUDDY_LISTSearchBuddyLogin20_ptr);
        using _BUDDY_LISTSearchBuddyLogoff22_ptr = bool (WINAPIV*)(struct _BUDDY_LIST*, unsigned int);
        using _BUDDY_LISTSearchBuddyLogoff22_clbk = bool (WINAPIV*)(struct _BUDDY_LIST*, unsigned int, _BUDDY_LISTSearchBuddyLogoff22_ptr);
        
        using _BUDDY_LISTctor__BUDDY_LIST24_ptr = void (WINAPIV*)(struct _BUDDY_LIST*);
        using _BUDDY_LISTctor__BUDDY_LIST24_clbk = void (WINAPIV*)(struct _BUDDY_LIST*, _BUDDY_LISTctor__BUDDY_LIST24_ptr);
        
        using _BUDDY_LISTdtor__BUDDY_LIST26_ptr = void (WINAPIV*)(struct _BUDDY_LIST*);
        using _BUDDY_LISTdtor__BUDDY_LIST26_clbk = void (WINAPIV*)(struct _BUDDY_LIST*, _BUDDY_LISTdtor__BUDDY_LIST26_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

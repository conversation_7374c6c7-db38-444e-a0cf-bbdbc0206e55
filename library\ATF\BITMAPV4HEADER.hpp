// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagICEXYZTRIPLE.hpp>


START_ATF_NAMESPACE
    struct BITMAPV4HEADER
    {
        unsigned int bV4Size;
        int bV4Width;
        int bV4Height;
        unsigned __int16 bV4Planes;
        unsigned __int16 bV4BitCount;
        unsigned int bV4V4Compression;
        unsigned int bV4SizeImage;
        int bV4XPelsPerMeter;
        int bV4YPelsPerMeter;
        unsigned int bV4ClrUsed;
        unsigned int bV4ClrImportant;
        unsigned int bV4RedMask;
        unsigned int bV4GreenMask;
        unsigned int bV4BlueMask;
        unsigned int bV4AlphaMask;
        unsigned int bV4CSType;
        tagICEXYZTRIPLE bV4Endpoints;
        unsigned int bV4GammaRed;
        unsigned int bV4GammaGreen;
        unsigned int bV4GammaBlue;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $BF8CC88CDF129BD6E4FA4ABA13F521AD
    {
        BYTE gap0[8];
        float *pfltVal;
    };    
    static_assert(ATF::checkSize<$BF8CC88CDF129BD6E4FA4ABA13F521AD, 16>(), "$BF8CC88CDF129BD6E4FA4ABA13F521AD");
END_ATF_NAMESPACE

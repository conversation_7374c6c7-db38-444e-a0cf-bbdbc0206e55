// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CLogTypeDBTask.hpp>
#include <CNetIndexList.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    struct CLogTypeDBTaskPool
    {
        bool m_bInit;
        CNetIndexList m_kInxProc;
        CNetIndexList m_kInxEmpty;
        CNetIndexList m_kInxComplete;
        std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > m_vecDat;
    public:
        CLogTypeDBTaskPool();
        void ctor_CLogTypeDBTaskPool();
        void Destroy();
        struct CLogTypeDBTask* GetComplete();
        struct CLogTypeDBTask* GetEmpty();
        struct CLogTypeDBTask* GetProc();
        bool Init(unsigned int uiBuffSize, unsigned int uiMaxCnt, struct CLogFile* kLogger);
        bool SetComplete(struct CLogTypeDBTask* pTask, struct CLogFile* kLogger);
        bool SetEmpty(struct CLogTypeDBTask* pTask, struct CLogFile* kLogger);
        bool SetProc(struct CLogTypeDBTask* pTask, struct CLogFile* kLogger);
        ~CLogTypeDBTaskPool();
        void dtor_CLogTypeDBTaskPool();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$1C655A77475F7A225F26C1B6F47A56AC.hpp>
#include <$BACACF5120E77F555196CA7B80D706BC.hpp>
#include <$E0865CEF4220CF19FC01DDE32B522B5B.hpp>
#include <$F79D820703B0BA0A2671AA6DE90403AB.hpp>


START_ATF_NAMESPACE
    union $D982EECAFFAC29366875EEA476639BD1
    {
        $E0865CEF4220CF19FC01DDE32B522B5B bmp;
        $F79D820703B0BA0A2671AA6DE90403AB wmf;
        $BACACF5120E77F555196CA7B80D706BC icon;
        $1C655A77475F7A225F26C1B6F47A56AC emf;
    };
END_ATF_NAMESPACE

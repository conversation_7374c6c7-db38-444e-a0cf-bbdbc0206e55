// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagMOUSEKEYS
    {
        unsigned int cbSize;
        unsigned int dwFlags;
        unsigned int iMaxSpeed;
        unsigned int iTimeToMaxSpeed;
        unsigned int iCtrlSpeed;
        unsigned int dwReserved1;
        unsigned int dwReserved2;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDarkHoleChannel.hpp>
#include <CDarkHoleDungeonQuestSetup.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CDarkHoleDungeonQuest : CDarkHoleDungeonQuestSetup
    {
        bool m_bLoad;
        CDarkHoleChannel m_Channel[128];
        unsigned int m_dwCheckLastTime;
        unsigned int m_dwChannelSerialCounter;
    public:
        CDarkHoleDungeonQuest();
        void ctor_CDarkHoleDungeonQuest();
        struct CDarkHoleChannel* CanOpenChannel(int nQuestIndex);
        void CheckQuestOnLoop();
        struct CDarkHoleChannel* GetChannel(unsigned int dwChannelIndex);
        bool LoadDarkHoleQuest();
        struct CDarkHoleChannel* OpenChannel(int nQuestIndex, struct CPlayer* pOpener, struct CDarkHole* pHoleObj);
        int SearchEmptyDarkHoleChannel();
        int SearchEmptyDarkHoleLayer(int nQuestIndex);
        struct CDarkHoleChannel* SearchOncePlayedChannel(unsigned int dwMemberSerial);
        ~CDarkHoleDungeonQuest();
        void dtor_CDarkHoleDungeonQuest();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CDarkHoleDungeonQuest, 9082216>(), "CDarkHoleDungeonQuest");
END_ATF_NAMESPACE

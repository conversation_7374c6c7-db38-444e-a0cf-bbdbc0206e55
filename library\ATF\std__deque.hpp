// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RECV_DATA.hpp>
#include <std___Deque_val.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        const struct  deque<RECV_DATA,allocator<RECV_DATA> > : _Deque_val<RECV_DATA,allocator<RECV_DATA> >
        {
            RECV_DATA **_Map;
            unsigned __int64 _Mapsize;
            unsigned __int64 _Myoff;
            unsigned __int64 _Mysize;
        };    
        static_assert(ATF::checkSize<std::deque<RECV_DATA,std::allocator<RECV_DATA> >, 56>(), "std::deque<RECV_DATA,std::allocator<RECV_DATA> >");
    }; // end namespace std
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDllIsolationWrapperBase.hpp>


START_ATF_NAMESPACE
    struct  CComCtlWrapper : CDllIsolationWrapperBase
    {
        struct InitCommonControls_Type
        {
            void (WINAPIV *p)();
        };
        struct InitCommonControlsEx_Type
        {
            int (WINAPIV *p)(struct tagINITCOMMONCONTROLSEX *);
        };
        struct ImageList_Create_Type
        {
            struct _IMAGELIST *(WINAPIV *p)(int, int, unsigned int, int, int);
        };
        struct ImageList_Destroy_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *);
        };
        struct ImageList_GetImageCount_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *);
        };
        struct ImageList_SetImageCount_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, unsigned int);
        };
        struct ImageList_Add_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, HBITMAP__ *, HBITMAP__ *);
        };
        struct ImageList_ReplaceIcon_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, HICON__ *);
        };
        struct ImageList_SetBkColor_Type
        {
            unsigned int (WINAPIV *p)(struct _IMAGELIST *, unsigned int);
        };
        struct ImageList_GetBkColor_Type
        {
            unsigned int (WINAPIV *p)(struct _IMAGELIST *);
        };
        struct ImageList_SetOverlayImage_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, int);
        };
        struct ImageList_Draw_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, HDC__ *, int, int, unsigned int);
        };
        struct ImageList_Replace_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, HBITMAP__ *, HBITMAP__ *);
        };
        struct ImageList_AddMasked_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, HBITMAP__ *, unsigned int);
        };
        struct ImageList_DrawEx_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, HDC__ *, int, int, int, int, unsigned int, unsigned int, unsigned int);
        };
        struct ImageList_DrawIndirect_Type
        {
            int (WINAPIV *p)(struct _IMAGELISTDRAWPARAMS *);
        };
        struct ImageList_Remove_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int);
        };
        struct ImageList_GetIcon_Type
        {
            HICON__ *(WINAPIV *p)(struct _IMAGELIST *, int, unsigned int);
        };
        struct ImageList_LoadImageA_Type
        {
            struct _IMAGELIST *(WINAPIV *p)(HINSTANCE__ *, const char *, int, int, unsigned int, unsigned int, unsigned int);
        };
        struct ImageList_LoadImageW_Type
        {
            struct _IMAGELIST *(WINAPIV *p)(HINSTANCE__ *, const wchar_t *, int, int, unsigned int, unsigned int, unsigned int);
        };
        struct ImageList_Copy_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, struct _IMAGELIST *, int, unsigned int);
        };
        struct ImageList_BeginDrag_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, int, int);
        };
        struct ImageList_EndDrag_Type
        {
            void (WINAPIV *p)();
        };
        struct ImageList_DragEnter_Type
        {
            int (WINAPIV *p)(HWND__ *, int, int);
        };
        struct ImageList_DragLeave_Type
        {
            int (WINAPIV *p)(HWND__ *);
        };
        struct ImageList_DragMove_Type
        {
            int (WINAPIV *p)(int, int);
        };
        struct ImageList_SetDragCursorImage_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, int, int);
        };
        struct ImageList_DragShowNolock_Type
        {
            int (WINAPIV *p)(int);
        };
        struct ImageList_GetDragImage_Type
        {
            struct _IMAGELIST *(WINAPIV *p)(tagPOINT *, tagPOINT *);
        };
        struct ImageList_Read_Type
        {
            struct _IMAGELIST *(WINAPIV *p)(IStream *);
        };
        struct ImageList_Write_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, IStream *);
        };
        struct ImageList_ReadEx_Type
        {
            HRESULT (WINAPIV *p)(unsigned int, IStream *, _GUID *, void **);
        };
        struct ImageList_WriteEx_Type
        {
            HRESULT (WINAPIV *p)(struct _IMAGELIST *, unsigned int, IStream *);
        };
        struct ImageList_GetIconSize_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int *, int *);
        };
        struct ImageList_SetIconSize_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, int);
        };
        struct ImageList_GetImageInfo_Type
        {
            int (WINAPIV *p)(struct _IMAGELIST *, int, struct _IMAGEINFO *);
        };
        struct ImageList_Merge_Type
        {
            struct _IMAGELIST *(WINAPIV *p)(struct _IMAGELIST *, int, struct _IMAGELIST *, int, int, int);
        };
        struct ImageList_Duplicate_Type
        {
            struct _IMAGELIST *(WINAPIV *p)(struct _IMAGELIST *);
        };
        struct CreateToolbarEx_Type
        {
            HWND__ *(WINAPIV *p)(HWND__ *, unsigned int, unsigned int, int, HINSTANCE__ *, unsigned __int64, struct _TBBUTTON *, int, int, int, int, int, unsigned int);
        };
        struct CreateMappedBitmap_Type
        {
            HBITMAP__ *(WINAPIV *p)(HINSTANCE__ *, __int64, unsigned int, struct _COLORMAP *, int);
        };
        struct DrawStatusTextA_Type
        {
            void (WINAPIV *p)(HDC__ *, tagRECT *, const char *, unsigned int);
        };
        struct DrawStatusTextW_Type
        {
            void (WINAPIV *p)(HDC__ *, tagRECT *, const wchar_t *, unsigned int);
        };
        struct CreateStatusWindowA_Type
        {
            HWND__ *(WINAPIV *p)(int, const char *, HWND__ *, unsigned int);
        };
        struct CreateStatusWindowW_Type
        {
            HWND__ *(WINAPIV *p)(int, const wchar_t *, HWND__ *, unsigned int);
        };
        struct MenuHelp_Type
        {
            void (WINAPIV *p)(unsigned int, unsigned __int64, __int64, HMENU__ *, HINSTANCE__ *, HWND__ *, unsigned int *);
        };
        struct ShowHideMenuCtl_Type
        {
            int (WINAPIV *p)(HWND__ *, unsigned __int64, int *);
        };
        struct GetEffectiveClientRect_Type
        {
            void (WINAPIV *p)(HWND__ *, tagRECT *, int *);
        };
        struct MakeDragList_Type
        {
            int (WINAPIV *p)(HWND__ *);
        };
        struct DrawInsert_Type
        {
            void (WINAPIV *p)(HWND__ *, HWND__ *, int);
        };
        struct LBItemFromPt_Type
        {
            int (WINAPIV *p)(HWND__ *, tagPOINT, int);
        };
        struct CreateUpDownControl_Type
        {
            HWND__ *(WINAPIV *p)(unsigned int, int, int, int, int, HWND__ *, int, HINSTANCE__ *, HWND__ *, int, int, int);
        };
        struct InitMUILanguage_Type
        {
            void (WINAPIV *p)(unsigned __int16);
        };
        struct GetMUILanguage_Type
        {
            unsigned __int16 (WINAPIV *p)();
        };
        struct DSA_Create_Type
        {
            struct _DSA *(WINAPIV *p)(int, int);
        };
        struct DSA_Destroy_Type
        {
            int (WINAPIV *p)(_DSA *);
        };
        struct DSA_DestroyCallback_Type
        {
            void (WINAPIV *p)(_DSA *, int (WINAPIV *)(void *, void *), void *);
        };
        struct DSA_GetItemPtr_Type
        {
            void *(WINAPIV *p)(_DSA *, int);
        };
        struct DSA_InsertItem_Type
        {
            int (WINAPIV *p)(_DSA *, int, void *);
        };
        struct DPA_Create_Type
        {
            struct _DPA *(WINAPIV *p)(int);
        };
        struct DPA_Destroy_Type
        {
            int (WINAPIV *p)(struct _DPA *);
        };
        struct DPA_DeletePtr_Type
        {
            void *(WINAPIV *p)(struct _DPA *, int);
        };
        struct DPA_DeleteAllPtrs_Type
        {
            int (WINAPIV *p)(struct _DPA *);
        };
        struct DPA_EnumCallback_Type
        {
            void (WINAPIV *p)(struct _DPA *, int (WINAPIV *)(void *, void *), void *);
        };
        struct DPA_DestroyCallback_Type
        {
            void (WINAPIV *p)(struct _DPA *, int (WINAPIV *)(void *, void *), void *);
        };
        struct DPA_SetPtr_Type
        {
            int (WINAPIV *p)(struct _DPA *, int, void *);
        };
        struct DPA_InsertPtr_Type
        {
            int (WINAPIV *p)(struct _DPA *, int, void *);
        };
        struct DPA_GetPtr_Type
        {
            void *(WINAPIV *p)(struct _DPA *, __int64);
        };
        struct DPA_Sort_Type
        {
            int (WINAPIV *p)(struct _DPA *, int (WINAPIV *)(void *, void *, __int64), __int64);
        };
        struct DPA_Search_Type
        {
            int (WINAPIV *p)(struct _DPA *, void *, int, int (WINAPIV *)(void *, void *, __int64), __int64, unsigned int);
        };
        struct Str_SetPtrW_Type
        {
            int (WINAPIV *p)(wchar_t **, const wchar_t *);
        };
        struct _TrackMouseEvent_Type
        {
            int (WINAPIV *p)(tagTRACKMOUSEEVENT *);
        };
        struct FlatSB_EnableScrollBar_Type
        {
            int (WINAPIV *p)(HWND__ *, int, unsigned int);
        };
        struct FlatSB_ShowScrollBar_Type
        {
            int (WINAPIV *p)(HWND__ *, int, int);
        };
        struct FlatSB_GetScrollRange_Type
        {
            int (WINAPIV *p)(HWND__ *, int, int *, int *);
        };
        struct FlatSB_GetScrollInfo_Type
        {
            int (WINAPIV *p)(HWND__ *, int, tagSCROLLINFO *);
        };
        struct FlatSB_GetScrollPos_Type
        {
            int (WINAPIV *p)(HWND__ *, int);
        };
        struct FlatSB_GetScrollProp_Type
        {
            int (WINAPIV *p)(HWND__ *, int, int *);
        };
        struct FlatSB_GetScrollPropPtr_Type
        {
            int (WINAPIV *p)(HWND__ *, int, __int64 *);
        };
        struct FlatSB_SetScrollPos_Type
        {
            int (WINAPIV *p)(HWND__ *, int, int, int);
        };
        struct FlatSB_SetScrollInfo_Type
        {
            int (WINAPIV *p)(HWND__ *, int, tagSCROLLINFO *, int);
        };
        struct FlatSB_SetScrollRange_Type
        {
            int (WINAPIV *p)(HWND__ *, int, int, int, int);
        };
        struct FlatSB_SetScrollProp_Type
        {
            int (WINAPIV *p)(HWND__ *, unsigned int, __int64, int);
        };
        struct InitializeFlatSB_Type
        {
            int (WINAPIV *p)(HWND__ *);
        };
        struct UninitializeFlatSB_Type
        {
            HRESULT (WINAPIV *p)(HWND__ *);
        };
        struct SetWindowSubclass_Type
        {
            int (WINAPIV *p)(HWND__ *, __int64 (WINAPIV *)(HWND__ *, unsigned int, unsigned __int64, __int64, unsigned __int64, unsigned __int64), unsigned __int64, unsigned __int64);
        };
        struct GetWindowSubclass_Type
        {
            int (WINAPIV *p)(HWND__ *, __int64 (WINAPIV *)(HWND__ *, unsigned int, unsigned __int64, __int64, unsigned __int64, unsigned __int64), unsigned __int64, unsigned __int64 *);
        };
        struct RemoveWindowSubclass_Type
        {
            int (WINAPIV *p)(HWND__ *, __int64 (WINAPIV *)(HWND__ *, unsigned int, unsigned __int64, __int64, unsigned __int64, unsigned __int64), unsigned __int64);
        };
        struct DefSubclassProc_Type
        {
            __int64 (WINAPIV *p)(HWND__ *, unsigned int, unsigned __int64, __int64);
        };
        struct DrawShadowText_Type
        {
            int (WINAPIV *p)(HDC__ *, const wchar_t *, unsigned int, tagRECT *, unsigned int, unsigned int, unsigned int, int, int);
        };
        struct CreatePropertySheetPageA_Type
        {
            struct _PSP *(WINAPIV *p)(_PROPSHEETPAGEA *);
        };
        struct CreatePropertySheetPageW_Type
        {
            struct _PSP *(WINAPIV *p)(_PROPSHEETPAGEW *);
        };
        struct DestroyPropertySheetPage_Type
        {
            int (WINAPIV *p)(struct _PSP *);
        };
        struct PropertySheetA_Type
        {
            __int64 (WINAPIV *p)(struct PROPSHEETHEADERA *);
        };
        struct PropertySheetW_Type
        {
            __int64 (WINAPIV *p)(struct PROPSHEETHEADERW *);
        };
        InitCommonControls_Type m__InitCommonControls;
        InitCommonControlsEx_Type m__InitCommonControlsEx;
        ImageList_Create_Type m__ImageList_Create;
        ImageList_Destroy_Type m__ImageList_Destroy;
        ImageList_GetImageCount_Type m__ImageList_GetImageCount;
        ImageList_SetImageCount_Type m__ImageList_SetImageCount;
        ImageList_Add_Type m__ImageList_Add;
        ImageList_ReplaceIcon_Type m__ImageList_ReplaceIcon;
        ImageList_SetBkColor_Type m__ImageList_SetBkColor;
        ImageList_GetBkColor_Type m__ImageList_GetBkColor;
        ImageList_SetOverlayImage_Type m__ImageList_SetOverlayImage;
        ImageList_Draw_Type m__ImageList_Draw;
        ImageList_Replace_Type m__ImageList_Replace;
        ImageList_AddMasked_Type m__ImageList_AddMasked;
        ImageList_DrawEx_Type m__ImageList_DrawEx;
        ImageList_DrawIndirect_Type m__ImageList_DrawIndirect;
        ImageList_Remove_Type m__ImageList_Remove;
        ImageList_GetIcon_Type m__ImageList_GetIcon;
        ImageList_LoadImageA_Type m__ImageList_LoadImageA;
        ImageList_LoadImageW_Type m__ImageList_LoadImageW;
        ImageList_Copy_Type m__ImageList_Copy;
        ImageList_BeginDrag_Type m__ImageList_BeginDrag;
        ImageList_EndDrag_Type m__ImageList_EndDrag;
        ImageList_DragEnter_Type m__ImageList_DragEnter;
        ImageList_DragLeave_Type m__ImageList_DragLeave;
        ImageList_DragMove_Type m__ImageList_DragMove;
        ImageList_SetDragCursorImage_Type m__ImageList_SetDragCursorImage;
        ImageList_DragShowNolock_Type m__ImageList_DragShowNolock;
        ImageList_GetDragImage_Type m__ImageList_GetDragImage;
        ImageList_Read_Type m__ImageList_Read;
        ImageList_Write_Type m__ImageList_Write;
        ImageList_ReadEx_Type m__ImageList_ReadEx;
        ImageList_WriteEx_Type m__ImageList_WriteEx;
        ImageList_GetIconSize_Type m__ImageList_GetIconSize;
        ImageList_SetIconSize_Type m__ImageList_SetIconSize;
        ImageList_GetImageInfo_Type m__ImageList_GetImageInfo;
        ImageList_Merge_Type m__ImageList_Merge;
        ImageList_Duplicate_Type m__ImageList_Duplicate;
        CreateToolbarEx_Type m__CreateToolbarEx;
        CreateMappedBitmap_Type m__CreateMappedBitmap;
        DrawStatusTextA_Type m__DrawStatusTextA;
        DrawStatusTextW_Type m__DrawStatusTextW;
        CreateStatusWindowA_Type m__CreateStatusWindowA;
        CreateStatusWindowW_Type m__CreateStatusWindowW;
        MenuHelp_Type m__MenuHelp;
        ShowHideMenuCtl_Type m__ShowHideMenuCtl;
        GetEffectiveClientRect_Type m__GetEffectiveClientRect;
        MakeDragList_Type m__MakeDragList;
        DrawInsert_Type m__DrawInsert;
        LBItemFromPt_Type m__LBItemFromPt;
        CreateUpDownControl_Type m__CreateUpDownControl;
        InitMUILanguage_Type m__InitMUILanguage;
        GetMUILanguage_Type m__GetMUILanguage;
        DSA_Create_Type m__DSA_Create;
        DSA_Destroy_Type m__DSA_Destroy;
        DSA_DestroyCallback_Type m__DSA_DestroyCallback;
        DSA_GetItemPtr_Type m__DSA_GetItemPtr;
        DSA_InsertItem_Type m__DSA_InsertItem;
        DPA_Create_Type m__DPA_Create;
        DPA_Destroy_Type m__DPA_Destroy;
        DPA_DeletePtr_Type m__DPA_DeletePtr;
        DPA_DeleteAllPtrs_Type m__DPA_DeleteAllPtrs;
        DPA_EnumCallback_Type m__DPA_EnumCallback;
        DPA_DestroyCallback_Type m__DPA_DestroyCallback;
        DPA_SetPtr_Type m__DPA_SetPtr;
        DPA_InsertPtr_Type m__DPA_InsertPtr;
        DPA_GetPtr_Type m__DPA_GetPtr;
        DPA_Sort_Type m__DPA_Sort;
        DPA_Search_Type m__DPA_Search;
        Str_SetPtrW_Type m__Str_SetPtrW;
        _TrackMouseEvent_Type m___TrackMouseEvent;
        FlatSB_EnableScrollBar_Type m__FlatSB_EnableScrollBar;
        FlatSB_ShowScrollBar_Type m__FlatSB_ShowScrollBar;
        FlatSB_GetScrollRange_Type m__FlatSB_GetScrollRange;
        FlatSB_GetScrollInfo_Type m__FlatSB_GetScrollInfo;
        FlatSB_GetScrollPos_Type m__FlatSB_GetScrollPos;
        FlatSB_GetScrollProp_Type m__FlatSB_GetScrollProp;
        FlatSB_GetScrollPropPtr_Type m__FlatSB_GetScrollPropPtr;
        FlatSB_SetScrollPos_Type m__FlatSB_SetScrollPos;
        FlatSB_SetScrollInfo_Type m__FlatSB_SetScrollInfo;
        FlatSB_SetScrollRange_Type m__FlatSB_SetScrollRange;
        FlatSB_SetScrollProp_Type m__FlatSB_SetScrollProp;
        InitializeFlatSB_Type m__InitializeFlatSB;
        UninitializeFlatSB_Type m__UninitializeFlatSB;
        SetWindowSubclass_Type m__SetWindowSubclass;
        GetWindowSubclass_Type m__GetWindowSubclass;
        RemoveWindowSubclass_Type m__RemoveWindowSubclass;
        DefSubclassProc_Type m__DefSubclassProc;
        DrawShadowText_Type m__DrawShadowText;
        CreatePropertySheetPageA_Type m__CreatePropertySheetPageA;
        CreatePropertySheetPageW_Type m__CreatePropertySheetPageW;
        DestroyPropertySheetPage_Type m__DestroyPropertySheetPage;
        PropertySheetA_Type m__PropertySheetA;
        PropertySheetW_Type m__PropertySheetW;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_attack_param.hpp>


START_ATF_NAMESPACE
    struct SKILL
    {
        int m_Type;
        int m_Element;
        int m_MinDmg;
        int m_StdDmg;
        int m_MaxDmg;
        int m_CritDmg;
        int m_MinProb;
        int m_MaxProb;
        int m_IsCritical;
        _attack_param m_param;
        unsigned int m_Len;
        unsigned int m_CastDelay;
        unsigned int m_Delay;
        unsigned int m_bLoad;
        unsigned int m_Active;
        unsigned int m_BefTime;
    public:
        int GetDmg(float fDamRate);
        void Init(int type, int dmg, int minprob, int maxprob, int len, int castdelay, int delay, int el);
        SKILL();
        void ctor_SKILL();
    };
END_ATF_NAMESPACE

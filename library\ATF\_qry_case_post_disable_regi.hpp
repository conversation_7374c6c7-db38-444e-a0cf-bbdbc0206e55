// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_post_disable_regi
    {
        struct __list
        {
            bool bProcRet;
            unsigned int dwRegiSerial;
        };
        unsigned int dwCnt;
        __list List[15];
    };
END_ATF_NAMESPACE

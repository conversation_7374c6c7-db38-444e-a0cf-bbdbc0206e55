// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_select_avator_report_wrac.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _select_avator_report_wracsize2_ptr = int (WINAPIV*)(struct _select_avator_report_wrac*);
        using _select_avator_report_wracsize2_clbk = int (WINAPIV*)(struct _select_avator_report_wrac*, _select_avator_report_wracsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

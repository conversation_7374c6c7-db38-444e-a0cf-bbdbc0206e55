// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _post_result_zocl
    {
        char byErrCode;
        unsigned int dwGold;
    public:
        _post_result_zocl();
        void ctor__post_result_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_post_result_zocl, 5>(), "_post_result_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pt_automine_charge_money_db_update_fail_zocl
    {
        int nCharge;
    public:
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_pt_automine_charge_money_db_update_fail_zocl, 4>(), "_pt_automine_charge_money_db_update_fail_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTraceCategory.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            
            using ATL__CTraceCategoryctor_CTraceCategory1_ptr = void (WINAPIV*)(struct ATL::CTraceCategory*, char*, unsigned int);
            using ATL__CTraceCategoryctor_CTraceCategory1_clbk = void (WINAPIV*)(struct ATL::CTraceCategory*, char*, unsigned int, ATL__CTraceCategoryctor_CTraceCategory1_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _last_attacker_for_the_keeper_inform_zowb
    {
        int nWorldCode;
        char byNumOfTime;
        unsigned __int16 wStartYear;
        char byStartMonth;
        char byStartDay;
        char uszKeeperHitter[17];
        int bByAnimus;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

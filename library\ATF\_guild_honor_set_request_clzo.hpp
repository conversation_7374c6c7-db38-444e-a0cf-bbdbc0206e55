// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _guild_honor_set_request_clzo
    {
        struct __list
        {
            char wszGuildName[17];
            char byTaxRate;
        };
        char by<PERSON>ist<PERSON>um;
        __list GuildList[5];
    };
END_ATF_NAMESPACE

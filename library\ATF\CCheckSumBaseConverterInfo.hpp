// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCheckSumBaseConverter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CCheckSumBaseConverterProcCode2_ptr = long double (WINAPIV*)(struct CCheckSumBaseConverter*, char, unsigned int, long double);
        using CCheckSumBaseConverterProcCode2_clbk = long double (WINAPIV*)(struct CCheckSumBaseConverter*, char, unsigned int, long double, CCheckSumBaseConverterProcCode2_ptr);
        using CCheckSumBaseConverterProcCode4_ptr = unsigned int (WINAPIV*)(struct CCheckSumBaseConverter*, char, unsigned int, unsigned int);
        using CCheckSumBaseConverterProcCode4_clbk = unsigned int (WINAPIV*)(struct CCheckSumBaseConverter*, char, unsigned int, unsigned int, CCheckSumBaseConverterProcCode4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

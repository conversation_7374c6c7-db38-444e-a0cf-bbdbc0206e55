// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_event_set_looting.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _event_set_lootingctor__event_set_looting2_ptr = void (WINAPIV*)(struct _event_set_looting*);
        using _event_set_lootingctor__event_set_looting2_clbk = void (WINAPIV*)(struct _event_set_looting*, _event_set_lootingctor__event_set_looting2_ptr);
        using _event_set_lootinginit4_ptr = void (WINAPIV*)(struct _event_set_looting*);
        using _event_set_lootinginit4_clbk = void (WINAPIV*)(struct _event_set_looting*, _event_set_lootinginit4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct _ATL_INTMAP_ENTRY
        {
            _GUID *piid;
            unsigned __int64 dw;
            HRESULT (WINAPIV *pFunc)(void *, _GUID *, void **, unsigned __int64);
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

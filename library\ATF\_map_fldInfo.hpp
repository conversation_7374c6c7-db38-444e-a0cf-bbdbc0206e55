// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_map_fld.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _map_fldctor__map_fld2_ptr = void (WINAPIV*)(struct _map_fld*);
        using _map_fldctor__map_fld2_clbk = void (WINAPIV*)(struct _map_fld*, _map_fldctor__map_fld2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

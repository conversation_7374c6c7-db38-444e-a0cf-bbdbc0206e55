// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CTrace.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        namespace Info
        {
            
            using ATL__CTracector_CTrace1_ptr = void (WINAPIV*)(struct ATL::CTrace*, int (WINAPIV*)(int, char*, int, char*, char*));
            using ATL__CTracector_CTrace1_clbk = void (WINAPIV*)(struct ATL::CTrace*, int (WINAPIV*)(int, char*, int, char*, char*), ATL__CTracector_CTrace1_ptr);
            using ATL__CTraceChangeCategory2_ptr = bool (WINAPIV*)(struct ATL::CTrace*, uint64_t, unsigned int, ATLTRACESTATUS);
            using ATL__CTraceChangeCategory2_clbk = bool (WINAPIV*)(struct ATL::CTrace*, uint64_t, unsigned int, ATLTRACESTATUS, ATL__CTraceChangeCategory2_ptr);
            using ATL__CTraceRegisterCategory3_ptr = uint64_t (WINAPIV*)(struct ATL::CTrace*, char*);
            using ATL__CTraceRegisterCategory3_clbk = uint64_t (WINAPIV*)(struct ATL::CTrace*, char*, ATL__CTraceRegisterCategory3_ptr);
            using ATL__CTraceTraceV5_ptr = void (WINAPIV*)(struct ATL::CTrace*, char*, int, uint64_t, unsigned int, char*, char*);
            using ATL__CTraceTraceV5_clbk = void (WINAPIV*)(struct ATL::CTrace*, char*, int, uint64_t, unsigned int, char*, char*, ATL__CTraceTraceV5_ptr);
            
            using ATL__CTracedtor_CTrace6_ptr = void (WINAPIV*)(struct ATL::CTrace*);
            using ATL__CTracedtor_CTrace6_clbk = void (WINAPIV*)(struct ATL::CTrace*, ATL__CTracedtor_CTrace6_ptr);
        }; // end namespace Info
    }; // end namespace ATL
END_ATF_NAMESPACE

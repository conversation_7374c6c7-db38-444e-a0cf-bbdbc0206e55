// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_unmandtrader_cheat_updateregisttime
    {
        struct __list
        {
            char byProcRet;
            char byState;
            unsigned int dwRegistSerial;
        };
        char byType;
        char byNum;
        unsigned __int16 wInx;
        unsigned int dwOwnerSerial;
        __list List[10];
    public:
        int size();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__ios_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        namespace Info
        {
            using std__ios_baseeof1_ptr = bool (WINAPIV*)(struct std::ios_base*);
            using std__ios_baseeof1_clbk = bool (WINAPIV*)(struct std::ios_base*, std__ios_baseeof1_ptr);
            using std__ios_baseflags2_ptr = int64_t (WINAPIV*)(struct std::ios_base*);
            using std__ios_baseflags2_clbk = int64_t (WINAPIV*)(struct std::ios_base*, std__ios_baseflags2_ptr);
            using std__ios_basegood3_ptr = bool (WINAPIV*)(struct std::ios_base*);
            using std__ios_basegood3_clbk = bool (WINAPIV*)(struct std::ios_base*, std__ios_basegood3_ptr);
            using std__ios_basewidth4_ptr = int64_t (WINAPIV*)(struct std::ios_base*, int64_t);
            using std__ios_basewidth4_clbk = int64_t (WINAPIV*)(struct std::ios_base*, int64_t, std__ios_basewidth4_ptr);
            using std__ios_basewidth5_ptr = int64_t (WINAPIV*)(struct std::ios_base*);
            using std__ios_basewidth5_clbk = int64_t (WINAPIV*)(struct std::ios_base*, std__ios_basewidth5_ptr);
        }; // end namespace Info
    }; // end namespace std
END_ATF_NAMESPACE

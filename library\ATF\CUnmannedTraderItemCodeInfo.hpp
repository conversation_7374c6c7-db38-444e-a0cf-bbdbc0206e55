// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CUnmannedTraderItemCodeInfo
    {
        unsigned int m_dwStartInx;
        unsigned int m_dwEndInx;
        char m_szCode[64];
    public:
        CUnmannedTraderItemCodeInfo(struct CUnmannedTraderItemCodeInfo* lhs);
        void ctor_CUnmannedTraderItemCodeInfo(struct CUnmannedTraderItemCodeInfo* lhs);
        CUnmannedTraderItemCodeInfo(char* szCode, unsigned int dwStartIndex, unsigned int dwEndIndex);
        void ctor_CUnmannedTraderItemCodeInfo(char* szCode, unsigned int dwStartIndex, unsigned int dwEndIndex);
        ~CUnmannedTraderItemCodeInfo();
        void dtor_CUnmannedTraderItemCodeInfo();
    };    
    static_assert(ATF::checkSize<CUnmannedTraderItemCodeInfo, 72>(), "CUnmannedTraderItemCodeInfo");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$0267EABC91DDCC621C08D74192F36445.hpp>
#include <$72A87AE534D1DC1A4B7CCA877B33F564.hpp>


START_ATF_NAMESPACE
    union $0CADDF7DF877E1D606D32956C3462939
    {
        $0267EABC91DDCC621C08D74192F36445 Function;
        $72A87AE534D1DC1A4B7CCA877B33F564 Array;
    };
END_ATF_NAMESPACE

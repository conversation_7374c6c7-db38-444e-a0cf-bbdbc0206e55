// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _pt_result_change_tax_rate_zocl
    {
        char byRet;
        char byNextTax;
    public:
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_pt_result_change_tax_rate_zocl, 2>(), "_pt_result_change_tax_rate_zocl");
END_ATF_NAMESPACE

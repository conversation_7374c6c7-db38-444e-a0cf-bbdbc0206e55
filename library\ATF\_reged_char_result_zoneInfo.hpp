// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_reged_char_result_zone.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _reged_char_result_zonector__reged_char_result_zone2_ptr = void (WINAPIV*)(struct _reged_char_result_zone*);
        using _reged_char_result_zonector__reged_char_result_zone2_clbk = void (WINAPIV*)(struct _reged_char_result_zone*, _reged_char_result_zonector__reged_char_result_zone2_ptr);
        using _reged_char_result_zonesize4_ptr = int (WINAPIV*)(struct _reged_char_result_zone*);
        using _reged_char_result_zonesize4_clbk = int (WINAPIV*)(struct _reged_char_result_zone*, _reged_char_result_zonesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

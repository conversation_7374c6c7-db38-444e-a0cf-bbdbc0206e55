// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _flowspec
    {
        unsigned int TokenRate;
        unsigned int TokenBucketSize;
        unsigned int PeakBandwidth;
        unsigned int Latency;
        unsigned int DelayVariation;
        unsigned int ServiceType;
        unsigned int MaxSduSize;
        unsigned int MinimumPolicedSize;
    };
END_ATF_NAMESPACE

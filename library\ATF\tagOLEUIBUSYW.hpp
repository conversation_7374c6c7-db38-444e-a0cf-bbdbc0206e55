// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>
#include <HRSRC__.hpp>
#include <HTASK__.hpp>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    struct tagOLEUIBUSYW
    {
        unsigned int cbStruct;
        unsigned int dwFlags;
        HWND__ *hWndOwner;
        const wchar_t *lpszCaption;
        unsigned int (WINAPIV *lpfnHook)(HWND__ *, unsigned int, unsigned __int64, __int64);
        __int64 lCustData;
        HINSTANCE__ *hInstance;
        const wchar_t *lpszTemplate;
        HRSRC__ *hResource;
        HTASK__ *hTask;
        HWND__ **lphWndDialog;
    };
END_ATF_NAMESPACE

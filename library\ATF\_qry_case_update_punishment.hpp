// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_update_punishment
    {
        char byType;
        unsigned int dwValue;
        char wszCharName[17];
        unsigned int dwAvatorSerial;
        unsigned int dwElectSerial;
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_update_punishment, 36>(), "_qry_case_update_punishment");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <SKILL.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using SKILLGetDmg2_ptr = int (WINAPIV*)(struct SKILL*, float);
        using SKILLGetDmg2_clbk = int (WINAPIV*)(struct SKILL*, float, SKILLGetDmg2_ptr);
        using SKILLInit4_ptr = void (WINAPIV*)(struct SKILL*, int, int, int, int, int, int, int, int);
        using SKILLInit4_clbk = void (WINAPIV*)(struct SKILL*, int, int, int, int, int, int, int, int, SKILLInit4_ptr);
        
        using SKILLctor_SKILL6_ptr = void (WINAPIV*)(struct SKILL*);
        using SKILLctor_SKILL6_clbk = void (WINAPIV*)(struct SKILL*, SKILLctor_SKILL6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

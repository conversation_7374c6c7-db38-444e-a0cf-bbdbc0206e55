// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ANIMUSKEY.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _ANIMUS_DB_BASE
    {
        struct  _LIST
        {
            _ANIMUSKEY Key;
            unsigned __int64 dwExp;
            unsigned int dwParam;
            unsigned int dwItemETSerial;
            unsigned __int64 lnUID;
            char byCsMethod;
            unsigned int dwT;
            unsigned int dwLendRegdTime;
        public:
            void Init();
            bool Release();
            bool Set(_STORAGE_LIST::_db_con* pItem);
            _LIST();
            void ctor__LIST();
        };
        struct _animus_param
        {
            unsigned __int16 wHP;
            unsigned __int16 wFP;
        };
        _LIST m_List[4];
    public:
        void Init();
        _ANIMUS_DB_BASE();
        void ctor__ANIMUS_DB_BASE();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_ANIMUS_DB_BASE, 136>(), "_ANIMUS_DB_BASE");
END_ATF_NAMESPACE

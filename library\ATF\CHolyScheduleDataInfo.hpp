// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHolyScheduleData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CHolyScheduleDatactor_CHolyScheduleData2_ptr = void (WINAPIV*)(struct CHolyScheduleData*);
        using CHolyScheduleDatactor_CHolyScheduleData2_clbk = void (WINAPIV*)(struct CHolyScheduleData*, CHolyScheduleDatactor_CHolyScheduleData2_ptr);
        using CHolyScheduleDataGetIndex4_ptr = struct CHolyScheduleData::__HolyScheduleNode* (WINAPIV*)(struct CHolyScheduleData*, int);
        using CHolyScheduleDataGetIndex4_clbk = struct CHolyScheduleData::__HolyScheduleNode* (WINAPIV*)(struct CHolyScheduleData*, int, CHolyScheduleDataGetIndex4_ptr);
        using CHolyScheduleDataGetTotalSceduleTerm6_ptr = unsigned int (WINAPIV*)(struct CHolyScheduleData*, int);
        using CHolyScheduleDataGetTotalSceduleTerm6_clbk = unsigned int (WINAPIV*)(struct CHolyScheduleData*, int, CHolyScheduleDataGetTotalSceduleTerm6_ptr);
        using CHolyScheduleDataInit8_ptr = void (WINAPIV*)(struct CHolyScheduleData*);
        using CHolyScheduleDataInit8_clbk = void (WINAPIV*)(struct CHolyScheduleData*, CHolyScheduleDataInit8_ptr);
        
        using CHolyScheduleDatadtor_CHolyScheduleData10_ptr = void (WINAPIV*)(struct CHolyScheduleData*);
        using CHolyScheduleDatadtor_CHolyScheduleData10_clbk = void (WINAPIV*)(struct CHolyScheduleData*, CHolyScheduleDatadtor_CHolyScheduleData10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

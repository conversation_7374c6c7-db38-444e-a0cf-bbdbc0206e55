// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _STORAGE_LISTAlterCurDur2_ptr = bool (WINAPIV*)(struct _STORAGE_LIST*, int, int, uint64_t*);
        using _STORAGE_LISTAlterCurDur2_clbk = bool (WINAPIV*)(struct _STORAGE_LIST*, int, int, uint64_t*, _STORAGE_LISTAlterCurDur2_ptr);
        using _STORAGE_LISTEmptyCon4_ptr = bool (WINAPIV*)(struct _STORAGE_LIST*, int);
        using _STORAGE_LISTEmptyCon4_clbk = bool (WINAPIV*)(struct _STORAGE_LIST*, int, _STORAGE_LISTEmptyCon4_ptr);
        using _STORAGE_LISTGetIndexEmptyCon6_ptr = int (WINAPIV*)(struct _STORAGE_LIST*);
        using _STORAGE_LISTGetIndexEmptyCon6_clbk = int (WINAPIV*)(struct _STORAGE_LIST*, _STORAGE_LISTGetIndexEmptyCon6_ptr);
        using _STORAGE_LISTGetIndexFromSerial8_ptr = int (WINAPIV*)(struct _STORAGE_LIST*, uint16_t);
        using _STORAGE_LISTGetIndexFromSerial8_clbk = int (WINAPIV*)(struct _STORAGE_LIST*, uint16_t, _STORAGE_LISTGetIndexFromSerial8_ptr);
        using _STORAGE_LISTGetNumEmptyCon10_ptr = int (WINAPIV*)(struct _STORAGE_LIST*);
        using _STORAGE_LISTGetNumEmptyCon10_clbk = int (WINAPIV*)(struct _STORAGE_LIST*, _STORAGE_LISTGetNumEmptyCon10_ptr);
        using _STORAGE_LISTGetNumUseCon12_ptr = int (WINAPIV*)(struct _STORAGE_LIST*);
        using _STORAGE_LISTGetNumUseCon12_clbk = int (WINAPIV*)(struct _STORAGE_LIST*, _STORAGE_LISTGetNumUseCon12_ptr);
        using _STORAGE_LISTGetPtrFromItemCode14_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct _STORAGE_LIST*, char*);
        using _STORAGE_LISTGetPtrFromItemCode14_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct _STORAGE_LIST*, char*, _STORAGE_LISTGetPtrFromItemCode14_ptr);
        using _STORAGE_LISTGetPtrFromItemInfo16_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct _STORAGE_LIST*, char, uint16_t);
        using _STORAGE_LISTGetPtrFromItemInfo16_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct _STORAGE_LIST*, char, uint16_t, _STORAGE_LISTGetPtrFromItemInfo16_ptr);
        using _STORAGE_LISTGetPtrFromSerial18_ptr = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct _STORAGE_LIST*, uint16_t);
        using _STORAGE_LISTGetPtrFromSerial18_clbk = struct _STORAGE_LIST::_db_con* (WINAPIV*)(struct _STORAGE_LIST*, uint16_t, _STORAGE_LISTGetPtrFromSerial18_ptr);
        using _STORAGE_LISTGetRemainLendTime20_ptr = unsigned int (WINAPIV*)(struct _STORAGE_LIST*, int, int);
        using _STORAGE_LISTGetRemainLendTime20_clbk = unsigned int (WINAPIV*)(struct _STORAGE_LIST*, int, int, _STORAGE_LISTGetRemainLendTime20_ptr);
        using _STORAGE_LISTGradeDown22_ptr = bool (WINAPIV*)(struct _STORAGE_LIST*, int, unsigned int);
        using _STORAGE_LISTGradeDown22_clbk = bool (WINAPIV*)(struct _STORAGE_LIST*, int, unsigned int, _STORAGE_LISTGradeDown22_ptr);
        using _STORAGE_LISTGradeUp24_ptr = bool (WINAPIV*)(struct _STORAGE_LIST*, int, unsigned int);
        using _STORAGE_LISTGradeUp24_clbk = bool (WINAPIV*)(struct _STORAGE_LIST*, int, unsigned int, _STORAGE_LISTGradeUp24_ptr);
        using _STORAGE_LISTSetAllEmpty26_ptr = void (WINAPIV*)(struct _STORAGE_LIST*);
        using _STORAGE_LISTSetAllEmpty26_clbk = void (WINAPIV*)(struct _STORAGE_LIST*, _STORAGE_LISTSetAllEmpty26_ptr);
        using _STORAGE_LISTSetClientIndexFromSerial28_ptr = char (WINAPIV*)(struct _STORAGE_LIST*, uint16_t, char, char*);
        using _STORAGE_LISTSetClientIndexFromSerial28_clbk = char (WINAPIV*)(struct _STORAGE_LIST*, uint16_t, char, char*, _STORAGE_LISTSetClientIndexFromSerial28_ptr);
        using _STORAGE_LISTSetGrade30_ptr = bool (WINAPIV*)(struct _STORAGE_LIST*, int, char, unsigned int);
        using _STORAGE_LISTSetGrade30_clbk = bool (WINAPIV*)(struct _STORAGE_LIST*, int, char, unsigned int, _STORAGE_LISTSetGrade30_ptr);
        using _STORAGE_LISTSetLimCurDur32_ptr = void (WINAPIV*)(struct _STORAGE_LIST*, int, unsigned int);
        using _STORAGE_LISTSetLimCurDur32_clbk = void (WINAPIV*)(struct _STORAGE_LIST*, int, unsigned int, _STORAGE_LISTSetLimCurDur32_ptr);
        using _STORAGE_LISTSetLock34_ptr = void (WINAPIV*)(struct _STORAGE_LIST*, int, bool);
        using _STORAGE_LISTSetLock34_clbk = void (WINAPIV*)(struct _STORAGE_LIST*, int, bool, _STORAGE_LISTSetLock34_ptr);
        using _STORAGE_LISTSetMemory36_ptr = void (WINAPIV*)(struct _STORAGE_LIST*, struct _STORAGE_LIST::_db_con*, int, int, int);
        using _STORAGE_LISTSetMemory36_clbk = void (WINAPIV*)(struct _STORAGE_LIST*, struct _STORAGE_LIST::_db_con*, int, int, int, _STORAGE_LISTSetMemory36_ptr);
        using _STORAGE_LISTSetUseListNum38_ptr = bool (WINAPIV*)(struct _STORAGE_LIST*, int);
        using _STORAGE_LISTSetUseListNum38_clbk = bool (WINAPIV*)(struct _STORAGE_LIST*, int, _STORAGE_LISTSetUseListNum38_ptr);
        using _STORAGE_LISTTransInCon40_ptr = unsigned int (WINAPIV*)(struct _STORAGE_LIST*, struct _STORAGE_LIST::_storage_con*);
        using _STORAGE_LISTTransInCon40_clbk = unsigned int (WINAPIV*)(struct _STORAGE_LIST*, struct _STORAGE_LIST::_storage_con*, _STORAGE_LISTTransInCon40_ptr);
        using _STORAGE_LISTUpdateCurDur42_ptr = bool (WINAPIV*)(struct _STORAGE_LIST*, int, int);
        using _STORAGE_LISTUpdateCurDur42_clbk = bool (WINAPIV*)(struct _STORAGE_LIST*, int, int, _STORAGE_LISTUpdateCurDur42_ptr);
        
        using _STORAGE_LISTctor__STORAGE_LIST44_ptr = void (WINAPIV*)(struct _STORAGE_LIST*);
        using _STORAGE_LISTctor__STORAGE_LIST44_clbk = void (WINAPIV*)(struct _STORAGE_LIST*, _STORAGE_LISTctor__STORAGE_LIST44_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

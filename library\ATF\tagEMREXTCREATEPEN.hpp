// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagEMR.hpp>
#include <tagEXTLOGPEN.hpp>


START_ATF_NAMESPACE
    struct tagEMREXTCREATEPEN
    {
        tagEMR emr;
        unsigned int ihPen;
        unsigned int offBmi;
        unsigned int cbBmi;
        unsigned int offBits;
        unsigned int cbBits;
        tagEXTLOGPEN elp;
    };
END_ATF_NAMESPACE

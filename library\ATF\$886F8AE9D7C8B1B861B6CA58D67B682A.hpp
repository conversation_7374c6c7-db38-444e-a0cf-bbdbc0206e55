// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $886F8AE9D7C8B1B861B6CA58D67B682A
    {
        BYTE gap0[8];
        unsigned int *pulVal;
    };    
    static_assert(ATF::checkSize<$886F8AE9D7C8B1B861B6CA58D67B682A, 16>(), "$886F8AE9D7C8B1B861B6CA58D67B682A");
END_ATF_NAMESPACE

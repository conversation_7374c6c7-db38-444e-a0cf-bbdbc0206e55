// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $76732A64405C0E287FCB302687644550
    {
        BYTE gap0[8];
        __int16 *piVal;
    };    
    static_assert(ATF::checkSize<$76732A64405C0E287FCB302687644550, 16>(), "$76732A64405C0E287FCB302687644550");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $5669F5A4AD19EF2CB42A5602080CB0A6
    {
        BYTE gap0[8];
        unsigned __int16 *puiVal;
    };    
    static_assert(ATF::checkSize<$5669F5A4AD19EF2CB42A5602080CB0A6, 16>(), "$5669F5A4AD19EF2CB42A5602080CB0A6");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDummyPosTable.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CDummyPosTablector_CDummyPosTable2_ptr = void (WINAPIV*)(struct CDummyPosTable*);
        using CDummyPosTablector_CDummyPosTable2_clbk = void (WINAPIV*)(struct CDummyPosTable*, CDummyPosTablector_CDummyPosTable2_ptr);
        using CDummyPosTableCheckHeroesDummy4_ptr = bool (WINAPIV*)(struct CGameObject*, char);
        using CDummyPosTableCheckHeroesDummy4_clbk = bool (WINAPIV*)(struct CGameObject*, char, CDummyPosTableCheckHeroesDummy4_ptr);
        using CDummyPosTableFindDummy6_ptr = bool (WINAPIV*)(char*, char*, struct _dummy_position*);
        using CDummyPosTableFindDummy6_clbk = bool (WINAPIV*)(char*, char*, struct _dummy_position*, CDummyPosTableFindDummy6_ptr);
        using CDummyPosTableGetRecord8_ptr = struct _dummy_position* (WINAPIV*)(struct CDummyPosTable*, char*);
        using CDummyPosTableGetRecord8_clbk = struct _dummy_position* (WINAPIV*)(struct CDummyPosTable*, char*, CDummyPosTableGetRecord8_ptr);
        using CDummyPosTableGetRecord10_ptr = struct _dummy_position* (WINAPIV*)(struct CDummyPosTable*, int);
        using CDummyPosTableGetRecord10_clbk = struct _dummy_position* (WINAPIV*)(struct CDummyPosTable*, int, CDummyPosTableGetRecord10_ptr);
        using CDummyPosTableGetRecordNum12_ptr = int (WINAPIV*)(struct CDummyPosTable*);
        using CDummyPosTableGetRecordNum12_clbk = int (WINAPIV*)(struct CDummyPosTable*, CDummyPosTableGetRecordNum12_ptr);
        using CDummyPosTableLoadDummyPosition14_ptr = bool (WINAPIV*)(struct CDummyPosTable*, char*, char*);
        using CDummyPosTableLoadDummyPosition14_clbk = bool (WINAPIV*)(struct CDummyPosTable*, char*, char*, CDummyPosTableLoadDummyPosition14_ptr);
        
        using CDummyPosTabledtor_CDummyPosTable19_ptr = void (WINAPIV*)(struct CDummyPosTable*);
        using CDummyPosTabledtor_CDummyPosTable19_clbk = void (WINAPIV*)(struct CDummyPosTable*, CDummyPosTabledtor_CDummyPosTable19_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

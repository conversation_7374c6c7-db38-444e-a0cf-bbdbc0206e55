// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <Us_HFSM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using Us_HFSMAddLoopDelayTime2_ptr = void (WINAPIV*)(struct Us_HFSM*, int, unsigned int);
        using Us_HFSMAddLoopDelayTime2_clbk = void (WINAPIV*)(struct Us_HFSM*, int, unsigned int, Us_HFSMAddLoopDelayTime2_ptr);
        using Us_HFSMCleanUp4_ptr = void (WINAPIV*)(struct Us_HFSM*);
        using Us_HFSMCleanUp4_clbk = void (WINAPIV*)(struct Us_HFSM*, Us_HFSMCleanUp4_ptr);
        using Us_HFSMGetIndex6_ptr = unsigned int (WINAPIV*)(struct Us_HFSM*, struct Us_FSM_Node*);
        using Us_HFSMGetIndex6_clbk = unsigned int (WINAPIV*)(struct Us_HFSM*, struct Us_FSM_Node*, Us_HFSMGetIndex6_ptr);
        using Us_HFSMGetNode8_ptr = struct Us_FSM_Node* (WINAPIV*)(struct Us_HFSM*, unsigned int);
        using Us_HFSMGetNode8_clbk = struct Us_FSM_Node* (WINAPIV*)(struct Us_HFSM*, unsigned int, Us_HFSMGetNode8_ptr);
        using Us_HFSMGetObjectA10_ptr = void* (WINAPIV*)(struct Us_HFSM*);
        using Us_HFSMGetObjectA10_clbk = void* (WINAPIV*)(struct Us_HFSM*, Us_HFSMGetObjectA10_ptr);
        using Us_HFSMGetState12_ptr = unsigned int (WINAPIV*)(struct Us_HFSM*, unsigned int);
        using Us_HFSMGetState12_clbk = unsigned int (WINAPIV*)(struct Us_HFSM*, unsigned int, Us_HFSMGetState12_ptr);
        using Us_HFSMInit14_ptr = void (WINAPIV*)(struct Us_HFSM*);
        using Us_HFSMInit14_clbk = void (WINAPIV*)(struct Us_HFSM*, Us_HFSMInit14_ptr);
        using Us_HFSMLink16_ptr = int (WINAPIV*)(struct Us_HFSM*, struct Us_FSM_Node*, struct Us_FSM_Node*);
        using Us_HFSMLink16_clbk = int (WINAPIV*)(struct Us_HFSM*, struct Us_FSM_Node*, struct Us_FSM_Node*, Us_HFSMLink16_ptr);
        using Us_HFSMOnProcess18_ptr = void (WINAPIV*)(struct Us_HFSM*, unsigned int);
        using Us_HFSMOnProcess18_clbk = void (WINAPIV*)(struct Us_HFSM*, unsigned int, Us_HFSMOnProcess18_ptr);
        using Us_HFSMSendExternMsg20_ptr = void (WINAPIV*)(struct Us_HFSM*, unsigned int, void*, int);
        using Us_HFSMSendExternMsg20_clbk = void (WINAPIV*)(struct Us_HFSM*, unsigned int, void*, int, Us_HFSMSendExternMsg20_ptr);
        using Us_HFSMSendMsg22_ptr = void (WINAPIV*)(struct Us_HFSM*, unsigned int, unsigned int, void*);
        using Us_HFSMSendMsg22_clbk = void (WINAPIV*)(struct Us_HFSM*, unsigned int, unsigned int, void*, Us_HFSMSendMsg22_ptr);
        using Us_HFSMSetLoopTime24_ptr = void (WINAPIV*)(struct Us_HFSM*, int, unsigned int);
        using Us_HFSMSetLoopTime24_clbk = void (WINAPIV*)(struct Us_HFSM*, int, unsigned int, Us_HFSMSetLoopTime24_ptr);
        using Us_HFSMSetMyData26_ptr = int (WINAPIV*)(struct Us_HFSM*, struct UsStateTBL*, void*);
        using Us_HFSMSetMyData26_clbk = int (WINAPIV*)(struct Us_HFSM*, struct UsStateTBL*, void*, Us_HFSMSetMyData26_ptr);
        
        using Us_HFSMctor_Us_HFSM28_ptr = void (WINAPIV*)(struct Us_HFSM*);
        using Us_HFSMctor_Us_HFSM28_clbk = void (WINAPIV*)(struct Us_HFSM*, Us_HFSMctor_Us_HFSM28_ptr);
        
        using Us_HFSMdtor_Us_HFSM33_ptr = void (WINAPIV*)(struct Us_HFSM*);
        using Us_HFSMdtor_Us_HFSM33_clbk = void (WINAPIV*)(struct Us_HFSM*, Us_HFSMdtor_Us_HFSM33_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

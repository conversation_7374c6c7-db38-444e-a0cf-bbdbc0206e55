// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DELPOST_DB_BASE.hpp>
#include <_POSTSTORAGE_DB_BASE.hpp>
#include <_RETURNPOST_DB_BASE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _POSTDATA_DB_BASE
    {
        _POSTSTORAGE_DB_BASE dbPost;
        _RETURNPOST_DB_BASE dbRetPost;
        _DELPOST_DB_BASE dbDelPost;
    public:
        void Init();
        void UpdateInit();
        _POSTDATA_DB_BASE();
        void ctor__POSTDATA_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_POSTDATA_DB_BASE, 15389>(), "_POSTDATA_DB_BASE");
END_ATF_NAMESPACE

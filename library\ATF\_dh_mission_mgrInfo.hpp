// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dh_mission_mgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _dh_mission_mgrGetLimMSecTime2_ptr = unsigned int (WINAPIV*)(struct _dh_mission_mgr*);
        using _dh_mission_mgrGetLimMSecTime2_clbk = unsigned int (WINAPIV*)(struct _dh_mission_mgr*, _dh_mission_mgrGetLimMSecTime2_ptr);
        using _dh_mission_mgrGetMissionCont4_ptr = struct _dh_mission_mgr::_if_change* (WINAPIV*)(struct _dh_mission_mgr*, struct _dh_mission_setup*);
        using _dh_mission_mgrGetMissionCont4_clbk = struct _dh_mission_mgr::_if_change* (WINAPIV*)(struct _dh_mission_mgr*, struct _dh_mission_setup*, _dh_mission_mgrGetMissionCont4_ptr);
        using _dh_mission_mgrInit6_ptr = void (WINAPIV*)(struct _dh_mission_mgr*);
        using _dh_mission_mgrInit6_clbk = void (WINAPIV*)(struct _dh_mission_mgr*, _dh_mission_mgrInit6_ptr);
        using _dh_mission_mgrIsOpenPortal8_ptr = bool (WINAPIV*)(struct _dh_mission_mgr*, int);
        using _dh_mission_mgrIsOpenPortal8_clbk = bool (WINAPIV*)(struct _dh_mission_mgr*, int, _dh_mission_mgrIsOpenPortal8_ptr);
        using _dh_mission_mgrNextMission10_ptr = void (WINAPIV*)(struct _dh_mission_mgr*, struct _dh_mission_setup*);
        using _dh_mission_mgrNextMission10_clbk = void (WINAPIV*)(struct _dh_mission_mgr*, struct _dh_mission_setup*, _dh_mission_mgrNextMission10_ptr);
        using _dh_mission_mgrOpenPortal12_ptr = void (WINAPIV*)(struct _dh_mission_mgr*, int);
        using _dh_mission_mgrOpenPortal12_clbk = void (WINAPIV*)(struct _dh_mission_mgr*, int, _dh_mission_mgrOpenPortal12_ptr);
        using _dh_mission_mgrSearchCurMissionCont14_ptr = struct _dh_mission_mgr::_if_change* (WINAPIV*)(struct _dh_mission_mgr*);
        using _dh_mission_mgrSearchCurMissionCont14_clbk = struct _dh_mission_mgr::_if_change* (WINAPIV*)(struct _dh_mission_mgr*, _dh_mission_mgrSearchCurMissionCont14_ptr);
        
        using _dh_mission_mgrctor__dh_mission_mgr16_ptr = void (WINAPIV*)(struct _dh_mission_mgr*);
        using _dh_mission_mgrctor__dh_mission_mgr16_clbk = void (WINAPIV*)(struct _dh_mission_mgr*, _dh_mission_mgrctor__dh_mission_mgr16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CHolyKeeper.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CHoly<PERSON>eeperctor_CHolyKeeper2_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperctor_CHolyKeeper2_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperctor_CHolyKeeper2_ptr);
        using CHolyKeeperCheckAttack4_ptr = bool (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperCheckAttack4_clbk = bool (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperCheckAttack4_ptr);
        using CHolyKeeperCheckCurPos6_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperCheckCurPos6_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperCheckCurPos6_ptr);
        using CHolyKeeperCheckExit8_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperCheckExit8_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperCheckExit8_ptr);
        using CHolyKeeperCheckMove10_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperCheckMove10_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperCheckMove10_ptr);
        using CHolyKeeperCreate12_ptr = bool (WINAPIV*)(struct CHolyKeeper*, struct _keeper_create_setdata*, int);
        using CHolyKeeperCreate12_clbk = bool (WINAPIV*)(struct CHolyKeeper*, struct _keeper_create_setdata*, int, CHolyKeeperCreate12_ptr);
        using CHolyKeeperDestroy14_ptr = bool (WINAPIV*)(struct CHolyKeeper*, char, struct CCharacter*);
        using CHolyKeeperDestroy14_clbk = bool (WINAPIV*)(struct CHolyKeeper*, char, struct CCharacter*, CHolyKeeperDestroy14_ptr);
        using CHolyKeeperDropItem16_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperDropItem16_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperDropItem16_ptr);
        using CHolyKeeperExit18_ptr = bool (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperExit18_clbk = bool (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperExit18_ptr);
        using CHolyKeeperGetAttackDP20_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetAttackDP20_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetAttackDP20_ptr);
        using CHolyKeeperGetAttackPivot22_ptr = float* (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetAttackPivot22_clbk = float* (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetAttackPivot22_ptr);
        using CHolyKeeperGetAttackRange24_ptr = float (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetAttackRange24_clbk = float (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetAttackRange24_ptr);
        using CHolyKeeperGetDefFC26_ptr = int (WINAPIV*)(struct CHolyKeeper*, int, struct CCharacter*, int*);
        using CHolyKeeperGetDefFC26_clbk = int (WINAPIV*)(struct CHolyKeeper*, int, struct CCharacter*, int*, CHolyKeeperGetDefFC26_ptr);
        using CHolyKeeperGetDefFacing28_ptr = float (WINAPIV*)(struct CHolyKeeper*, int);
        using CHolyKeeperGetDefFacing28_clbk = float (WINAPIV*)(struct CHolyKeeper*, int, CHolyKeeperGetDefFacing28_ptr);
        using CHolyKeeperGetDefGap30_ptr = float (WINAPIV*)(struct CHolyKeeper*, int);
        using CHolyKeeperGetDefGap30_clbk = float (WINAPIV*)(struct CHolyKeeper*, int, CHolyKeeperGetDefGap30_ptr);
        using CHolyKeeperGetDefSkill32_ptr = int (WINAPIV*)(struct CHolyKeeper*, bool);
        using CHolyKeeperGetDefSkill32_clbk = int (WINAPIV*)(struct CHolyKeeper*, bool, CHolyKeeperGetDefSkill32_ptr);
        using CHolyKeeperGetFireTol34_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetFireTol34_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetFireTol34_ptr);
        using CHolyKeeperGetGenAttackProb36_ptr = int (WINAPIV*)(struct CHolyKeeper*, struct CCharacter*, int, bool);
        using CHolyKeeperGetGenAttackProb36_clbk = int (WINAPIV*)(struct CHolyKeeper*, struct CCharacter*, int, bool, CHolyKeeperGetGenAttackProb36_ptr);
        using CHolyKeeperGetHP38_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetHP38_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetHP38_ptr);
        using CHolyKeeperGetLevel40_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetLevel40_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetLevel40_ptr);
        using CHolyKeeperGetMaxHP42_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetMaxHP42_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetMaxHP42_ptr);
        using CHolyKeeperGetObjName44_ptr = char* (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetObjName44_clbk = char* (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetObjName44_ptr);
        using CHolyKeeperGetObjRace46_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetObjRace46_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetObjRace46_ptr);
        using CHolyKeeperGetSoilTol48_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetSoilTol48_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetSoilTol48_ptr);
        using CHolyKeeperGetWaterTol50_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetWaterTol50_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetWaterTol50_ptr);
        using CHolyKeeperGetWeaponAdjust52_ptr = float (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetWeaponAdjust52_clbk = float (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetWeaponAdjust52_ptr);
        using CHolyKeeperGetWeaponClass54_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetWeaponClass54_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetWeaponClass54_ptr);
        using CHolyKeeperGetWidth56_ptr = float (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetWidth56_clbk = float (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetWidth56_ptr);
        using CHolyKeeperGetWindTol58_ptr = int (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperGetWindTol58_clbk = int (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperGetWindTol58_ptr);
        using CHolyKeeperInit60_ptr = bool (WINAPIV*)(struct CHolyKeeper*, struct _object_id*);
        using CHolyKeeperInit60_clbk = bool (WINAPIV*)(struct CHolyKeeper*, struct _object_id*, CHolyKeeperInit60_ptr);
        using CHolyKeeperIsBeAttackedAble62_ptr = bool (WINAPIV*)(struct CHolyKeeper*, bool);
        using CHolyKeeperIsBeAttackedAble62_clbk = bool (WINAPIV*)(struct CHolyKeeper*, bool, CHolyKeeperIsBeAttackedAble62_ptr);
        using CHolyKeeperIsBeDamagedAble64_ptr = bool (WINAPIV*)(struct CHolyKeeper*, struct CCharacter*);
        using CHolyKeeperIsBeDamagedAble64_clbk = bool (WINAPIV*)(struct CHolyKeeper*, struct CCharacter*, CHolyKeeperIsBeDamagedAble64_ptr);
        using CHolyKeeperLoop66_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperLoop66_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperLoop66_ptr);
        using CHolyKeeperOutOfSec68_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperOutOfSec68_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperOutOfSec68_ptr);
        using CHolyKeeperSearchAttackTarget70_ptr = struct CCharacter* (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperSearchAttackTarget70_clbk = struct CCharacter* (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperSearchAttackTarget70_ptr);
        using CHolyKeeperSearchMoveTarget72_ptr = struct CPlayer* (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperSearchMoveTarget72_clbk = struct CPlayer* (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperSearchMoveTarget72_ptr);
        using CHolyKeeperSendMsg_Attack74_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperSendMsg_Attack74_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperSendMsg_Attack74_ptr);
        using CHolyKeeperSendMsg_Create76_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperSendMsg_Create76_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperSendMsg_Create76_ptr);
        using CHolyKeeperSendMsg_Destroy78_ptr = void (WINAPIV*)(struct CHolyKeeper*, char);
        using CHolyKeeperSendMsg_Destroy78_clbk = void (WINAPIV*)(struct CHolyKeeper*, char, CHolyKeeperSendMsg_Destroy78_ptr);
        using CHolyKeeperSendMsg_FixPosition80_ptr = void (WINAPIV*)(struct CHolyKeeper*, int);
        using CHolyKeeperSendMsg_FixPosition80_clbk = void (WINAPIV*)(struct CHolyKeeper*, int, CHolyKeeperSendMsg_FixPosition80_ptr);
        using CHolyKeeperSendMsg_Move82_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperSendMsg_Move82_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperSendMsg_Move82_ptr);
        using CHolyKeeperSendMsg_RealMovePoint84_ptr = void (WINAPIV*)(struct CHolyKeeper*, int);
        using CHolyKeeperSendMsg_RealMovePoint84_clbk = void (WINAPIV*)(struct CHolyKeeper*, int, CHolyKeeperSendMsg_RealMovePoint84_ptr);
        using CHolyKeeperSetDamage86_ptr = int (WINAPIV*)(struct CHolyKeeper*, int, struct CCharacter*, int, bool, int, unsigned int, bool);
        using CHolyKeeperSetDamage86_clbk = int (WINAPIV*)(struct CHolyKeeper*, int, struct CCharacter*, int, bool, int, unsigned int, bool, CHolyKeeperSetDamage86_ptr);
        using CHolyKeeperSetDamageAbleState88_ptr = void (WINAPIV*)(struct CHolyKeeper*, bool);
        using CHolyKeeperSetDamageAbleState88_clbk = void (WINAPIV*)(struct CHolyKeeper*, bool, CHolyKeeperSetDamageAbleState88_ptr);
        using CHolyKeeperSetDropItem90_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperSetDropItem90_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperSetDropItem90_ptr);
        using CHolyKeeperSetHP92_ptr = void (WINAPIV*)(struct CHolyKeeper*, int);
        using CHolyKeeperSetHP92_clbk = void (WINAPIV*)(struct CHolyKeeper*, int, CHolyKeeperSetHP92_ptr);
        using CHolyKeeperSetMaxHP94_ptr = void (WINAPIV*)(struct CHolyKeeper*, int);
        using CHolyKeeperSetMaxHP94_clbk = void (WINAPIV*)(struct CHolyKeeper*, int, CHolyKeeperSetMaxHP94_ptr);
        using CHolyKeeperSetStateChaos96_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperSetStateChaos96_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperSetStateChaos96_ptr);
        
        using CHolyKeeperdtor_CHolyKeeper102_ptr = void (WINAPIV*)(struct CHolyKeeper*);
        using CHolyKeeperdtor_CHolyKeeper102_clbk = void (WINAPIV*)(struct CHolyKeeper*, CHolyKeeperdtor_CHolyKeeper102_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

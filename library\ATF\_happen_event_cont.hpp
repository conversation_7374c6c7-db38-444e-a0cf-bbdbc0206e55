// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <QUEST_HAPPEN.hpp>
#include <_happen_event_node.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _happen_event_cont
    {
        _happen_event_node *m_pEvent;
        QUEST_HAPPEN m_QtHpType;
        int m_nIndexInType;
        int m_nRaceCode;
    public:
        _happen_event_cont();
        void ctor__happen_event_cont();
        void init();
        bool isset();
        void set(struct _happen_event_node* pPoint, QUEST_HAPPEN QtHpType, int nIndexInType, int nRaceCode);
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_happen_event_cont, 24>(), "_happen_event_cont");
END_ATF_NAMESPACE

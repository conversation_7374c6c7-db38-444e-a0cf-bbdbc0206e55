// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRusiaBillingMgr.hpp>
#include <CashDbWorker.hpp>
#include <Task.hpp>


START_ATF_NAMESPACE
    struct  CCashDbWorkerRU : CashDbWorker
    {
        CRusiaBillingMgr *_pkBill;
    public:
        CCashDbWorkerRU();
        void ctor_CCashDbWorkerRU();
        void Release();
        bool _init_database();
        int _wait_tsk_cash_rollback(struct Task* pkTsk);
        int _wait_tsk_cash_select(struct Task* pkTsk);
        int _wait_tsk_cash_update(struct Task* pkTsk);
        ~CCashDbWorkerRU();
        void dtor_CCashDbWorkerRU();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CSimpleMap.hpp>
#include <ATL__CExpansionVectorEqualHelper.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct  CExpansionVector : CSimpleMap<char *,wchar_t *,CExpansionVectorEqualHelper>
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

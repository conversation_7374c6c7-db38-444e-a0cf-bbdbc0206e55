// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _alter_link_slot_request_clzo
    {
        struct __list
        {
            char bySlotIndex;
            char byLinkCode;
            unsigned __int16 wIndex;
        };
        char byLinkLock;
        char byNum;
        __list list[50];
    };
END_ATF_NAMESPACE

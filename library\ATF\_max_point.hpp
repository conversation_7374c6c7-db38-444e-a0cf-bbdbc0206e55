// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _max_point
    {
        int nLv;
        int nMaxPoint;
        int nMinPoint;
        int nPremiumMaxPoint;
    public:
        _max_point();
        void ctor__max_point();
        void init();
        int size();
    };
END_ATF_NAMESPACE

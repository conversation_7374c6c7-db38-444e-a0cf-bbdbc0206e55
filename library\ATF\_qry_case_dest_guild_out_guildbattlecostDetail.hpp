// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_dest_guild_out_guildbattlecostInfo.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        extern ::std::array<hook_record, 1> _qry_case_dest_guild_out_guildbattlecost_functions;
    }; // end namespace Detail
END_ATF_NAMESPACE

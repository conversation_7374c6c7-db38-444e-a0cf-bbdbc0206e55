// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ControllerTaxRateVtbl.hpp>


START_ATF_NAMESPACE
    struct ControllerTaxRate
    {
        ControllerTaxRateVtbl *vfptr;
        bool m_bInit;
        float m_fMinTaxRate;
        float m_fMaxTaxRate;
        float m_fCurTaxRate;
    public:
        ControllerTaxRate();
        void ctor_ControllerTaxRate();
        unsigned int calcTaxRate(unsigned int dalant);
        bool checkLimitTaxRate(float fTaxRate);
        float getCurTaxRate();
        void setCurTaxRate(float fTaxRate);
        void setLimitTaxRate(float fMin, float fMax);
        ~ControllerTaxRate();
        void dtor_ControllerTaxRate();
    };
END_ATF_NAMESPACE

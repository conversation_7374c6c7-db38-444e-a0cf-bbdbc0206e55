// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct C_SCOPE_TABLE
    {
        void *__ptr32 Begin;
        void *__ptr32 End;
        void *__ptr32 Handler;
        void *__ptr32 Target;
    };    
    static_assert(ATF::checkSize<C_SCOPE_TABLE, 16>(), "C_SCOPE_TABLE");
END_ATF_NAMESPACE

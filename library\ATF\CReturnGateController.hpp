// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <CNetIndexList.hpp>
#include <CPlayer.hpp>
#include <CReturnGate.hpp>


START_ATF_NAMESPACE
    struct CReturnGateController
    {
        CMyTimer *m_pkTimer;
        CNetIndexList *m_pkEmptyInxList;
        CNetIndexList *m_pkUseInxList;
        unsigned int m_uiGateTotCnt;
        CReturnGate **m_ppkGatePool;
    public:
        CReturnGateController();
        void ctor_CReturnGateController();
        void CleanUp();
        void Close(struct CReturnGate* pkGate);
        static void Destroy();
        bool Enter(unsigned int uiGateInx, struct CPlayer* pkObj);
        struct CReturnGate* GetEmpty();
        struct CReturnGate* GetGate(unsigned int uiInx);
        bool Init(unsigned int uiSize);
        static struct CReturnGateController* Instance();
        bool IsExistOwner(struct CPlayer* pkObj);
        void OnLoop();
        bool Open(struct CPlayer* pkOwner);
        int ProcessEnter(unsigned int uiGateInx, struct CPlayer* pkObj);
        void SendEnterResult(int iResult, struct CPlayer* pkObj);
        void UpdateClose();
        ~CReturnGateController();
        void dtor_CReturnGateController();
    };
END_ATF_NAMESPACE

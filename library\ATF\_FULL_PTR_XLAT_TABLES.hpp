// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$14BF9AA2EF21CC35F0ACBBD33CBBC311.hpp>
#include <$74724AF476F582D33CFD87FFD3FA7737.hpp>
#include <XLAT_SIDE.hpp>


START_ATF_NAMESPACE
    struct _FULL_PTR_XLAT_TABLES
    {
        $74724AF476F582D33CFD87FFD3FA7737 RefIdToPointer;
        $14BF9AA2EF21CC35F0ACBBD33CBBC311 PointerToRefId;
        unsigned int NextRefId;
        XLAT_SIDE XlatSide;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <US__AbstractThread.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        namespace Info
        {
            
            using US__AbstractThreadctor_AbstractThread2_ptr = void (WINAPIV*)(struct US::AbstractThread*);
            using US__AbstractThreadctor_AbstractThread2_clbk = void (WINAPIV*)(struct US::AbstractThread*, US__AbstractThreadctor_AbstractThread2_ptr);
            
            using US__AbstractThreaddtor_AbstractThread7_ptr = void (WINAPIV*)(struct US::AbstractThread*);
            using US__AbstractThreaddtor_AbstractThread7_clbk = void (WINAPIV*)(struct US::AbstractThread*, US__AbstractThreaddtor_AbstractThread7_ptr);
        }; // end namespace Info
    }; // end namespace US
END_ATF_NAMESPACE

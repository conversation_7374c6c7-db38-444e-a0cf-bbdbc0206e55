// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union $01AE0C4CAA17A9DEB3CF9215716A3B58
    {
        unsigned int dwBBitMask;
        unsigned int dwVBitMask;
        unsigned int dwStencilBitMask;
        unsigned int dwBumpLuminanceBitMask;
    };    
    static_assert(ATF::checkSize<$01AE0C4CAA17A9DEB3CF9215716A3B58, 4>(), "$01AE0C4CAA17A9DEB3CF9215716A3B58");
END_ATF_NAMESPACE

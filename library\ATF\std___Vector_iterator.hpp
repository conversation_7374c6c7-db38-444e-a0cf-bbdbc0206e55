// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Vector_const_iterator.hpp>
#include <std__allocator.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<typename _Ty, typename _Alloc = allocator<_Ty>>
        struct  _Vector_iterator : _Vector_const_iterator<_Ty, _Alloc>
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE

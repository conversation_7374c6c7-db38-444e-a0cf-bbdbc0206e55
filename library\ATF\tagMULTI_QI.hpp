// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HRESULT.hpp>
#include <IUnknown.hpp>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagMULTI_QI
    {
        _GUID *pIID;
        IUnknown *pItf;
        HRESULT hr;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

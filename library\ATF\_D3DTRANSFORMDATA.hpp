// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_D3DHVERTEX.hpp>
#include <_D3DRECT.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _D3DTRANSFORMDATA
    {
        unsigned int dwSize;
        void *lpIn;
        unsigned int dwInSize;
        void *lpOut;
        unsigned int dwOutSize;
        _D3DHVERTEX *lpHOut;
        unsigned int dwClip;
        unsigned int dwClipIntersection;
        unsigned int dwClipUnion;
        _D3DRECT drExtent;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

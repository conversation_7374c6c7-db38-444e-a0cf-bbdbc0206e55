// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _other_new_view_zocl
    {
        unsigned __int16 wIndex;
        unsigned int dwSerial;
        unsigned __int16 wEquipVer;
        __int16 zPos[3];
        char byRaceCode;
        char byViewType;
        unsigned __int64 dwStateFlag;
        unsigned __int16 wLastEffectCode;
        char byColor;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

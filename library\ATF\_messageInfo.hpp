// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_message.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _messageGetKey12_ptr = unsigned int (WINAPIV*)(struct _message*);
        using _messageGetKey12_clbk = unsigned int (WINAPIV*)(struct _message*, _messageGetKey12_ptr);
        using _messageGetMessageA4_ptr = unsigned int (WINAPIV*)(struct _message*);
        using _messageGetMessageA4_clbk = unsigned int (WINAPIV*)(struct _message*, _messageGetMessageA4_ptr);
        using _messageSetMsg6_ptr = void (WINAPIV*)(struct _message*, unsigned int, unsigned int, unsigned int, unsigned int);
        using _messageSetMsg6_clbk = void (WINAPIV*)(struct _message*, unsigned int, unsigned int, unsigned int, unsigned int, _messageSetMsg6_ptr);
        
        using _messagector__message8_ptr = void (WINAPIV*)(struct _message*);
        using _messagector__message8_clbk = void (WINAPIV*)(struct _message*, _messagector__message8_ptr);
        
        using _messagedtor__message12_ptr = void (WINAPIV*)(struct _message*);
        using _messagedtor__message12_clbk = void (WINAPIV*)(struct _message*, _messagedtor__message12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

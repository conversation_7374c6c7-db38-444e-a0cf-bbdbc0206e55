// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct CTimeSpan
        {
            __int64 m_timeSpan;
        public:
            CTimeSpan(int64_t time);
            void ctor_CTimeSpan(int64_t time);
            CTimeSpan(int lDays, int nHours, int nMins, int nSecs);
            void ctor_CTimeSpan(int lDays, int nHours, int nMins, int nSecs);
            CTimeSpan();
            void ctor_CTimeSpan();
            int64_t GetDays();
            int GetHours();
            int GetMinutes();
            int GetSeconds();
            int64_t GetTimeSpan();
            int64_t GetTotalHours();
            int64_t GetTotalMinutes();
            int64_t GetTotalSeconds();
        };    
        static_assert(ATF::checkSize<ATL::CTimeSpan, 8>(), "ATL::CTimeSpan");
    }; // end namespace ATL
END_ATF_NAMESPACE

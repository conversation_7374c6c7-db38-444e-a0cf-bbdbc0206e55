// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_param_cash_select.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _param_cash_selectctor__param_cash_select2_ptr = void (WINAPIV*)(struct _param_cash_select*, unsigned int, unsigned int, uint16_t);
        using _param_cash_selectctor__param_cash_select2_clbk = void (WINAPIV*)(struct _param_cash_select*, unsigned int, unsigned int, uint16_t, _param_cash_selectctor__param_cash_select2_ptr);
        using _param_cash_selectsize4_ptr = int (WINAPIV*)(struct _param_cash_select*);
        using _param_cash_selectsize4_clbk = int (WINAPIV*)(struct _param_cash_select*, _param_cash_selectsize4_ptr);
        
        using _param_cash_selectdtor__param_cash_select6_ptr = void (WINAPIV*)(struct _param_cash_select*);
        using _param_cash_selectdtor__param_cash_select6_clbk = void (WINAPIV*)(struct _param_cash_select*, _param_cash_selectdtor__param_cash_select6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

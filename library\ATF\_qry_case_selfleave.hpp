// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _qry_case_selfleave
    {
        unsigned int in_leaverserial;
        int in_MemberNum;
        unsigned int tmp_leaverindex;
        unsigned int tmp_guildindex;
        unsigned int tmp_guildserial;
    public:
        int size();
    };    
    static_assert(ATF::checkSize<_qry_case_selfleave, 20>(), "_qry_case_selfleave");
END_ATF_NAMESPACE

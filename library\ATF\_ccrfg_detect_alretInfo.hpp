// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ccrfg_detect_alret.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _ccrfg_detect_alretsize2_ptr = int (WINAPIV*)(struct _ccrfg_detect_alret*);
        using _ccrfg_detect_alretsize2_clbk = int (WINAPIV*)(struct _ccrfg_detect_alret*, _ccrfg_detect_alretsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <Cmd.hpp>
#include <ElectProcessor.hpp>
#include <_pt_notify_vote_score_zocl.hpp>
#include <_pt_trans_votepaper_zocl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  Voter : ElectProcessor
    {
        _pt_trans_votepaper_zocl _kCandidateInfo[3];
        _pt_notify_vote_score_zocl _kVoteScoreInfo[3];
    public:
        int Doit(Cmd eCmd, struct CPlayer* pOne, char* pdata);
        bool Initialize();
        bool IsRegistedVotePaper(char byRace, char* pwszName);
        Voter();
        void ctor_Voter();
        void _MakeVotePaper();
        int _SendVotePaper(struct CPlayer* pOne);
        void _SendVotePaperAll();
        void _SendVoteScore(struct CPlayer* pOne);
        void _SendVoteScoreAll(char byRace);
        void _SetVoteScoreInfo(char byRace, char* wszName, bool bAbstention);
        int _Vote(struct CPlayer* pOne, char* pdata);
        ~Voter();
        void dtor_Voter();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<Voter, 1608>(), "Voter");
END_ATF_NAMESPACE

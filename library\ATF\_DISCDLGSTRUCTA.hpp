// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _DISCDLGSTRUCTA
    {
        unsigned int cbStructure;
        HWND__ *hwndOwner;
        char *lpLocalName;
        char *lpRemoteName;
        unsigned int dwFlags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

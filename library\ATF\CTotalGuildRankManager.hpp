// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <CTotalGuildRankInfo.hpp>
#include <_total_guild_rank_info.hpp>


START_ATF_NAMESPACE
    struct CTotalGuildRankManager
    {
        int m_iOldDay;
        CTotalGuildRankInfo m_kInfo;
    public:
        CTotalGuildRankManager();
        void ctor_CTotalGuildRankManager();
        static void Destroy();
        bool Init();
        static struct CTotalGuildRankManager* Instance();
        int Load(char byDayAfter, struct _total_guild_rank_info* pkInfo);
        bool Load();
        void Loop();
        void OrderRank(struct _total_guild_rank_info* pkInfo);
        void Send(unsigned int dwVer, char byTabRace, struct CPlayer* pkPlayer);
        bool Update(char* pLoadData);
        void UpdateComlete(char byRet, char* pLoadData);
        ~CTotalGuildRankManager();
        void dtor_CTotalGuildRankManager();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <_NETRESOURCEA.hpp>


START_ATF_NAMESPACE
    struct _CONNECTDLGSTRUCTA
    {
        unsigned int cbStructure;
        HWND__ *hwndOwner;
        _NETRESOURCEA *lpConnRes;
        unsigned int dwFlags;
        unsigned int dwDevNum;
    };
END_ATF_NAMESPACE

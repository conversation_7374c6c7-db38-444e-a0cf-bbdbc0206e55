// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CashDbWorker.hpp>


START_ATF_NAMESPACE
    struct  CCashDbWorkerPH : CashDbWorker
    {
    public:
        CCashDbWorkerPH();
        void ctor_CCashDbWorkerPH();
        ~CCashDbWorkerPH();
        void dtor_CCashDbWorkerPH();
    };
END_ATF_NAMESPACE

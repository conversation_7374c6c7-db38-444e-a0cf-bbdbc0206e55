// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CItemStore.hpp>
#include <_character_create_setdata.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _npc_create_setdata : _character_create_setdata
    {
        CItemStore *m_pLinkItemStore;
        char m_byRaceCode;
    public:
        _npc_create_setdata();
        void ctor__npc_create_setdata();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_npc_create_setdata, 48>(), "_npc_create_setdata");
END_ATF_NAMESPACE

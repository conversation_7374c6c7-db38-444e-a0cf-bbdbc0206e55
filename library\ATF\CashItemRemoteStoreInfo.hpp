// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CashItemRemoteStore.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CashItemRemoteStoreBuy2_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*);
        using CashItemRemoteStoreBuy2_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*, CashItemRemoteStoreBuy2_ptr);
        using CashItemRemoteStoreBuyByCash4_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*);
        using CashItemRemoteStoreBuyByCash4_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*, CashItemRemoteStoreBuyByCash4_ptr);
        using CashItemRemoteStoreBuyByGold6_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*);
        using CashItemRemoteStoreBuyByGold6_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*, CashItemRemoteStoreBuyByGold6_ptr);
        using CashItemRemoteStoreBuyLimSale8_ptr = uint16_t (WINAPIV*)(struct CashItemRemoteStore*, char, unsigned int);
        using CashItemRemoteStoreBuyLimSale8_clbk = uint16_t (WINAPIV*)(struct CashItemRemoteStore*, char, unsigned int, CashItemRemoteStoreBuyLimSale8_ptr);
        
        using CashItemRemoteStorector_CashItemRemoteStore10_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStorector_CashItemRemoteStore10_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStorector_CashItemRemoteStore10_ptr);
        using CashItemRemoteStoreChangeDiscountEventTime12_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreChangeDiscountEventTime12_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreChangeDiscountEventTime12_ptr);
        using CashItemRemoteStoreChangeEventTime14_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreChangeEventTime14_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreChangeEventTime14_ptr);
        using CashItemRemoteStoreChange_Conditional_Event_Status16_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreChange_Conditional_Event_Status16_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreChange_Conditional_Event_Status16_ptr);
        using CashItemRemoteStoreCheatBuy18_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*, int);
        using CashItemRemoteStoreCheatBuy18_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*, int, CashItemRemoteStoreCheatBuy18_ptr);
        using CashItemRemoteStoreCheatLoadCashAmount20_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, int);
        using CashItemRemoteStoreCheatLoadCashAmount20_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, int, CashItemRemoteStoreCheatLoadCashAmount20_ptr);
        using CashItemRemoteStoreCheckCouponType22_ptr = int (WINAPIV*)(struct CashItemRemoteStore*, struct _STORAGE_POS_INDIV*, struct CPlayer*, char);
        using CashItemRemoteStoreCheckCouponType22_clbk = int (WINAPIV*)(struct CashItemRemoteStore*, struct _STORAGE_POS_INDIV*, struct CPlayer*, char, CashItemRemoteStoreCheckCouponType22_ptr);
        using CashItemRemoteStoreCheck_CashEvent_INI24_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreCheck_CashEvent_INI24_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreCheck_CashEvent_INI24_ptr);
        using CashItemRemoteStoreCheck_CashEvent_Status26_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreCheck_CashEvent_Status26_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreCheck_CashEvent_Status26_ptr);
        using CashItemRemoteStoreCheck_Conditional_Event_INI28_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreCheck_Conditional_Event_INI28_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreCheck_Conditional_Event_INI28_ptr);
        using CashItemRemoteStoreCheck_Conditional_Event_Status30_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreCheck_Conditional_Event_Status30_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreCheck_Conditional_Event_Status30_ptr);
        using CashItemRemoteStoreCheck_Grosssales32_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, unsigned int);
        using CashItemRemoteStoreCheck_Grosssales32_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, unsigned int, CashItemRemoteStoreCheck_Grosssales32_ptr);
        using CashItemRemoteStoreCheck_Loaded_Event_Status34_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreCheck_Loaded_Event_Status34_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreCheck_Loaded_Event_Status34_ptr);
        using CashItemRemoteStoreCheck_Total_Selling36_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreCheck_Total_Selling36_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreCheck_Total_Selling36_ptr);
        using CashItemRemoteStoreFindCashRec38_ptr = struct _CashShop_fld* (WINAPIV*)(int, int);
        using CashItemRemoteStoreFindCashRec38_clbk = struct _CashShop_fld* (WINAPIV*)(int, int, CashItemRemoteStoreFindCashRec38_ptr);
        using CashItemRemoteStoreGetEvnetTime40_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_time*, int);
        using CashItemRemoteStoreGetEvnetTime40_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_time*, int, CashItemRemoteStoreGetEvnetTime40_ptr);
        using CashItemRemoteStoreGetLimDiscout42_ptr = char (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreGetLimDiscout42_clbk = char (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreGetLimDiscout42_ptr);
        using CashItemRemoteStoreGetRemainNumOfGood44_ptr = int (WINAPIV*)(struct CashItemRemoteStore*, char*);
        using CashItemRemoteStoreGetRemainNumOfGood44_clbk = int (WINAPIV*)(struct CashItemRemoteStore*, char*, CashItemRemoteStoreGetRemainNumOfGood44_ptr);
        using CashItemRemoteStoreGetRemainNumOfGood46_ptr = int (WINAPIV*)(struct CashItemRemoteStore*, uint16_t);
        using CashItemRemoteStoreGetRemainNumOfGood46_clbk = int (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, CashItemRemoteStoreGetRemainNumOfGood46_ptr);
        using CashItemRemoteStoreGetSetDiscout48_ptr = char (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreGetSetDiscout48_clbk = char (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreGetSetDiscout48_ptr);
        using CashItemRemoteStoreGet_CashEvent_Status50_ptr = char (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreGet_CashEvent_Status50_clbk = char (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreGet_CashEvent_Status50_ptr);
        using CashItemRemoteStoreGet_Conditional_Event_Name52_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char, char*);
        using CashItemRemoteStoreGet_Conditional_Event_Name52_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, char*, CashItemRemoteStoreGet_Conditional_Event_Name52_ptr);
        using CashItemRemoteStoreGet_Conditional_Event_Status54_ptr = char (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreGet_Conditional_Event_Status54_clbk = char (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreGet_Conditional_Event_Status54_ptr);
        using CashItemRemoteStoreGoodsList56_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*);
        using CashItemRemoteStoreGoodsList56_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*, CashItemRemoteStoreGoodsList56_ptr);
        using CashItemRemoteStoreGoodsListBuyByCash58_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*);
        using CashItemRemoteStoreGoodsListBuyByCash58_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*, CashItemRemoteStoreGoodsListBuyByCash58_ptr);
        using CashItemRemoteStoreGoodsListBuyByGold60_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*);
        using CashItemRemoteStoreGoodsListBuyByGold60_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*, CashItemRemoteStoreGoodsListBuyByGold60_ptr);
        using CashItemRemoteStoreInform_CashEvent62_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, uint16_t);
        using CashItemRemoteStoreInform_CashEvent62_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, CashItemRemoteStoreInform_CashEvent62_ptr);
        using CashItemRemoteStoreInform_CashEvent_Status_All64_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char, char, struct _cash_event_ini*);
        using CashItemRemoteStoreInform_CashEvent_Status_All64_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, char, struct _cash_event_ini*, CashItemRemoteStoreInform_CashEvent_Status_All64_ptr);
        using CashItemRemoteStoreInform_ConditionalEvent66_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, uint16_t);
        using CashItemRemoteStoreInform_ConditionalEvent66_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, CashItemRemoteStoreInform_ConditionalEvent66_ptr);
        using CashItemRemoteStoreInform_ConditionalEvent_Status_All68_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char, char, char*);
        using CashItemRemoteStoreInform_ConditionalEvent_Status_All68_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, char, char*, CashItemRemoteStoreInform_ConditionalEvent_Status_All68_ptr);
        using CashItemRemoteStoreInitialize70_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreInitialize70_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreInitialize70_ptr);
        using CashItemRemoteStoreInstance72_ptr = struct CashItemRemoteStore* (WINAPIV*)();
        using CashItemRemoteStoreInstance72_clbk = struct CashItemRemoteStore* (WINAPIV*)(CashItemRemoteStoreInstance72_ptr);
        using CashItemRemoteStoreIsBuyCashItemByGold74_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreIsBuyCashItemByGold74_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreIsBuyCashItemByGold74_ptr);
        using CashItemRemoteStoreIsEventTime76_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreIsEventTime76_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreIsEventTime76_ptr);
        using CashItemRemoteStoreIsUsableCoupon78_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, struct _request_csi_buy_clzo*, struct _STORAGE_POS_INDIV, struct CPlayer*, bool*);
        using CashItemRemoteStoreIsUsableCoupon78_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, struct _request_csi_buy_clzo*, struct _STORAGE_POS_INDIV, struct CPlayer*, bool*, CashItemRemoteStoreIsUsableCoupon78_ptr);
        using CashItemRemoteStoreLimitedSale_check_count80_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, char, unsigned int);
        using CashItemRemoteStoreLimitedSale_check_count80_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, char, unsigned int, CashItemRemoteStoreLimitedSale_check_count80_ptr);
        using CashItemRemoteStoreLoadBuyCashMode82_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreLoadBuyCashMode82_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreLoadBuyCashMode82_ptr);
        using CashItemRemoteStoreLoadNationalPrice84_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, struct CRecordData*);
        using CashItemRemoteStoreLoadNationalPrice84_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, struct CRecordData*, CashItemRemoteStoreLoadNationalPrice84_ptr);
        using CashItemRemoteStoreLoad_Cash_Event86_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreLoad_Cash_Event86_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreLoad_Cash_Event86_ptr);
        using CashItemRemoteStoreLoad_Conditional_Event88_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreLoad_Conditional_Event88_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreLoad_Conditional_Event88_ptr);
        using CashItemRemoteStoreLoad_Event_INI90_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_ini*, struct _FILETIME*, char*);
        using CashItemRemoteStoreLoad_Event_INI90_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_ini*, struct _FILETIME*, char*, CashItemRemoteStoreLoad_Event_INI90_ptr);
        using CashItemRemoteStoreLoad_LimitedSale_Event_INI92_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_ini*, struct _FILETIME*, char*);
        using CashItemRemoteStoreLoad_LimitedSale_Event_INI92_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_ini*, struct _FILETIME*, char*, CashItemRemoteStoreLoad_LimitedSale_Event_INI92_ptr);
        using CashItemRemoteStoreLoop_Cash_Event94_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreLoop_Cash_Event94_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreLoop_Cash_Event94_ptr);
        using CashItemRemoteStoreLoop_Check_Total_Selling96_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreLoop_Check_Total_Selling96_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreLoop_Check_Total_Selling96_ptr);
        using CashItemRemoteStoreLoop_ContEvent98_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreLoop_ContEvent98_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreLoop_ContEvent98_ptr);
        using CashItemRemoteStoreLoop_TatalCashEvent100_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreLoop_TatalCashEvent100_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreLoop_TatalCashEvent100_ptr);
        using CashItemRemoteStoreSell102_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*);
        using CashItemRemoteStoreSell102_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, char*, CashItemRemoteStoreSell102_ptr);
        using CashItemRemoteStoreSetNextDiscountEventTime104_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreSetNextDiscountEventTime104_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreSetNextDiscountEventTime104_ptr);
        using CashItemRemoteStoreSetNextEventTime106_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreSetNextEventTime106_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreSetNextEventTime106_ptr);
        using CashItemRemoteStoreSet_CashEvent_Status108_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char, char);
        using CashItemRemoteStoreSet_CashEvent_Status108_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, char, CashItemRemoteStoreSet_CashEvent_Status108_ptr);
        using CashItemRemoteStoreSet_Conditional_Evnet_Status110_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreSet_Conditional_Evnet_Status110_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreSet_Conditional_Evnet_Status110_ptr);
        using CashItemRemoteStoreSet_DB_LimitedSale_Event112_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreSet_DB_LimitedSale_Event112_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreSet_DB_LimitedSale_Event112_ptr);
        using CashItemRemoteStoreSet_FROMDB_LimitedSale_Event114_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _db_cash_limited_sale*);
        using CashItemRemoteStoreSet_FROMDB_LimitedSale_Event114_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _db_cash_limited_sale*, CashItemRemoteStoreSet_FROMDB_LimitedSale_Event114_ptr);
        using CashItemRemoteStoreSet_LimitedSale_DCK116_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char, char);
        using CashItemRemoteStoreSet_LimitedSale_DCK116_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, char, CashItemRemoteStoreSet_LimitedSale_DCK116_ptr);
        using CashItemRemoteStoreSet_LimitedSale_Event118_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreSet_LimitedSale_Event118_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreSet_LimitedSale_Event118_ptr);
        using CashItemRemoteStoreSet_LimitedSale_Event_Ini120_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_ini*);
        using CashItemRemoteStoreSet_LimitedSale_Event_Ini120_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_ini*, CashItemRemoteStoreSet_LimitedSale_Event_Ini120_ptr);
        using CashItemRemoteStoreSet_LimitedSale_count122_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char, unsigned int);
        using CashItemRemoteStoreSet_LimitedSale_count122_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, unsigned int, CashItemRemoteStoreSet_LimitedSale_count122_ptr);
        using CashItemRemoteStoreUpdate_INI124_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_ini*, char);
        using CashItemRemoteStoreUpdate_INI124_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_event_ini*, char, CashItemRemoteStoreUpdate_INI124_ptr);
        using CashItemRemoteStoreUseDiscountCoupon126_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, struct _param_cash_update*, struct _STORAGE_POS_INDIV, struct CPlayer*);
        using CashItemRemoteStoreUseDiscountCoupon126_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, struct _param_cash_update*, struct _STORAGE_POS_INDIV, struct CPlayer*, CashItemRemoteStoreUseDiscountCoupon126_ptr);
        using CashItemRemoteStore_InitLoggers128_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStore_InitLoggers128_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStore_InitLoggers128_ptr);
        using CashItemRemoteStore_MakeLinkTable130_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, char*, int);
        using CashItemRemoteStore_MakeLinkTable130_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, char*, int, CashItemRemoteStore_MakeLinkTable130_ptr);
        using CashItemRemoteStore_ReadGoods132_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStore_ReadGoods132_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStore_ReadGoods132_ptr);
        using CashItemRemoteStore__CheckGoods134_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, struct CRecordData*);
        using CashItemRemoteStore__CheckGoods134_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, struct CRecordData*, CashItemRemoteStore__CheckGoods134_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item136_ptr = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, bool*, struct _result_csi_buy_zocl*);
        using CashItemRemoteStore_buybygold_buy_single_item136_clbk = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, bool*, struct _result_csi_buy_zocl*, CashItemRemoteStore_buybygold_buy_single_item136_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_additional_process138_ptr = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _result_csi_buy_zocl*);
        using CashItemRemoteStore_buybygold_buy_single_item_additional_process138_clbk = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _result_csi_buy_zocl*, CashItemRemoteStore_buybygold_buy_single_item_additional_process138_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price140_ptr = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _CashShop_fld*, bool*, struct _result_csi_buy_zocl*, unsigned int*);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price140_clbk = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _CashShop_fld*, bool*, struct _result_csi_buy_zocl*, unsigned int*, CashItemRemoteStore_buybygold_buy_single_item_calc_price140_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price_coupon142_ptr = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, char, int, bool*, unsigned int*);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price_coupon142_clbk = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, char, int, bool*, unsigned int*, CashItemRemoteStore_buybygold_buy_single_item_calc_price_coupon142_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price_discount144_ptr = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, struct _CashShop_fld*, char);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price_discount144_clbk = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, struct _CashShop_fld*, char, CashItemRemoteStore_buybygold_buy_single_item_calc_price_discount144_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price_limitsale146_ptr = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, int, char);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price_limitsale146_clbk = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, int, char, CashItemRemoteStore_buybygold_buy_single_item_calc_price_limitsale146_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price_one_n_one148_ptr = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, char, int, char);
        using CashItemRemoteStore_buybygold_buy_single_item_calc_price_one_n_one148_clbk = unsigned int (WINAPIV*)(struct CashItemRemoteStore*, char, int, char, CashItemRemoteStore_buybygold_buy_single_item_calc_price_one_n_one148_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_check_item150_ptr = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _CashShop_fld**);
        using CashItemRemoteStore_buybygold_buy_single_item_check_item150_clbk = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _CashShop_fld**, CashItemRemoteStore_buybygold_buy_single_item_check_item150_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_give_item152_ptr = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo::__item*, struct _STORAGE_LIST::_db_con*);
        using CashItemRemoteStore_buybygold_buy_single_item_give_item152_clbk = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo::__item*, struct _STORAGE_LIST::_db_con*, CashItemRemoteStore_buybygold_buy_single_item_give_item152_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_proc_complete154_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _CashShop_fld*, struct _STORAGE_LIST::_db_con*, struct _result_csi_buy_zocl*, unsigned int, unsigned int, bool*, bool*);
        using CashItemRemoteStore_buybygold_buy_single_item_proc_complete154_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _CashShop_fld*, struct _STORAGE_LIST::_db_con*, struct _result_csi_buy_zocl*, unsigned int, unsigned int, bool*, bool*, CashItemRemoteStore_buybygold_buy_single_item_proc_complete154_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_proc_price156_ptr = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _CashShop_fld*, bool*, struct _result_csi_buy_zocl*, unsigned int*, unsigned int*);
        using CashItemRemoteStore_buybygold_buy_single_item_proc_price156_clbk = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, struct _CashShop_fld*, bool*, struct _result_csi_buy_zocl*, unsigned int*, unsigned int*, CashItemRemoteStore_buybygold_buy_single_item_proc_price156_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_setbuydblog158_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _param_cashitem_dblog*, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int);
        using CashItemRemoteStore_buybygold_buy_single_item_setbuydblog158_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _param_cashitem_dblog*, struct _STORAGE_LIST::_db_con*, unsigned int, unsigned int, CashItemRemoteStore_buybygold_buy_single_item_setbuydblog158_ptr);
        using CashItemRemoteStore_buybygold_buy_single_item_setsenddata160_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _STORAGE_LIST::_db_con*, struct _result_csi_buy_zocl*);
        using CashItemRemoteStore_buybygold_buy_single_item_setsenddata160_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _STORAGE_LIST::_db_con*, struct _result_csi_buy_zocl*, CashItemRemoteStore_buybygold_buy_single_item_setsenddata160_ptr);
        using CashItemRemoteStore_buybygold_check_coupon162_ptr = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _param_cashitem_dblog*);
        using CashItemRemoteStore_buybygold_check_coupon162_clbk = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _param_cashitem_dblog*, CashItemRemoteStore_buybygold_check_coupon162_ptr);
        using CashItemRemoteStore_buybygold_check_valid164_ptr = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _param_cashitem_dblog*);
        using CashItemRemoteStore_buybygold_check_valid164_clbk = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _request_csi_buy_clzo*, struct _param_cashitem_dblog*, CashItemRemoteStore_buybygold_check_valid164_ptr);
        using CashItemRemoteStore_buybygold_complete166_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _result_csi_buy_zocl*, struct _request_csi_buy_clzo*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, bool);
        using CashItemRemoteStore_buybygold_complete166_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _result_csi_buy_zocl*, struct _request_csi_buy_clzo*, struct _request_csi_buy_clzo::__item*, struct _param_cashitem_dblog*, bool, CashItemRemoteStore_buybygold_complete166_ptr);
        using CashItemRemoteStore_buybygold_set_cashitem_dblog_sheet168_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _param_cashitem_dblog*);
        using CashItemRemoteStore_buybygold_set_cashitem_dblog_sheet168_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct CPlayer*, struct _param_cashitem_dblog*, CashItemRemoteStore_buybygold_set_cashitem_dblog_sheet168_ptr);
        using CashItemRemoteStore_check_buyitem170_ptr = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, char, struct _request_csi_buy_clzo::__item*, struct _CashShop_fld*);
        using CashItemRemoteStore_check_buyitem170_clbk = CS_RCODE (WINAPIV*)(struct CashItemRemoteStore*, char, struct _request_csi_buy_clzo::__item*, struct _CashShop_fld*, CashItemRemoteStore_check_buyitem170_ptr);
        using CashItemRemoteStorecheck_cash_discount_ini172_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStorecheck_cash_discount_ini172_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStorecheck_cash_discount_ini172_ptr);
        using CashItemRemoteStorecheck_cash_discount_status174_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStorecheck_cash_discount_status174_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStorecheck_cash_discount_status174_ptr);
        using CashItemRemoteStorecheck_loaded_cde_status176_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStorecheck_loaded_cde_status176_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStorecheck_loaded_cde_status176_ptr);
        using CashItemRemoteStoreforce_endup_cash_discount_event178_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreforce_endup_cash_discount_event178_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreforce_endup_cash_discount_event178_ptr);
        using CashItemRemoteStoreget_cde_status180_ptr = char (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreget_cde_status180_clbk = char (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreget_cde_status180_ptr);
        using CashItemRemoteStoreinform_cashdiscount_event182_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, uint16_t);
        using CashItemRemoteStoreinform_cashdiscount_event182_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, uint16_t, CashItemRemoteStoreinform_cashdiscount_event182_ptr);
        using CashItemRemoteStoreinform_cashdiscount_status_all184_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char, struct _cash_discount_ini_*);
        using CashItemRemoteStoreinform_cashdiscount_status_all184_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, struct _cash_discount_ini_*, CashItemRemoteStoreinform_cashdiscount_status_all184_ptr);
        using CashItemRemoteStoreisConEventTime186_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreisConEventTime186_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreisConEventTime186_ptr);
        using CashItemRemoteStoreis_cde_time188_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreis_cde_time188_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreis_cde_time188_ptr);
        using CashItemRemoteStoreload_cash_discount_event190_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreload_cash_discount_event190_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreload_cash_discount_event190_ptr);
        using CashItemRemoteStoreload_cde_ini192_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_discount_ini_*, struct _FILETIME*);
        using CashItemRemoteStoreload_cde_ini192_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_discount_ini_*, struct _FILETIME*, CashItemRemoteStoreload_cde_ini192_ptr);
        using CashItemRemoteStoreload_con_event_ini194_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _con_event_ini*, struct _FILETIME*);
        using CashItemRemoteStoreload_con_event_ini194_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _con_event_ini*, struct _FILETIME*, CashItemRemoteStoreload_con_event_ini194_ptr);
        using CashItemRemoteStorelog_about_cash_event196_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char*, struct _cash_discount_ini_*);
        using CashItemRemoteStorelog_about_cash_event196_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char*, struct _cash_discount_ini_*, CashItemRemoteStorelog_about_cash_event196_ptr);
        using CashItemRemoteStoreloop_cash_discount_event198_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoreloop_cash_discount_event198_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoreloop_cash_discount_event198_ptr);
        using CashItemRemoteStoreset_cde_status200_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, char);
        using CashItemRemoteStoreset_cde_status200_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, char, CashItemRemoteStoreset_cde_status200_ptr);
        using CashItemRemoteStorestart_cashevent202_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, int, int, int, int, char);
        using CashItemRemoteStorestart_cashevent202_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, int, int, int, int, char, CashItemRemoteStorestart_cashevent202_ptr);
        using CashItemRemoteStorestart_cde204_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, int, int, int, int);
        using CashItemRemoteStorestart_cde204_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, int, int, int, int, CashItemRemoteStorestart_cde204_ptr);
        using CashItemRemoteStorestart_conevent206_ptr = bool (WINAPIV*)(struct CashItemRemoteStore*, int, int, char);
        using CashItemRemoteStorestart_conevent206_clbk = bool (WINAPIV*)(struct CashItemRemoteStore*, int, int, char, CashItemRemoteStorestart_conevent206_ptr);
        using CashItemRemoteStoreupdate_ini208_ptr = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_discount_ini_*);
        using CashItemRemoteStoreupdate_ini208_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, struct _cash_discount_ini_*, CashItemRemoteStoreupdate_ini208_ptr);
        
        using CashItemRemoteStoredtor_CashItemRemoteStore210_ptr = void (WINAPIV*)(struct CashItemRemoteStore*);
        using CashItemRemoteStoredtor_CashItemRemoteStore210_clbk = void (WINAPIV*)(struct CashItemRemoteStore*, CashItemRemoteStoredtor_CashItemRemoteStore210_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HINSTANCE__.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct TBREPLACEBITMAP
    {
        HINSTANCE__ *hInstOld;
        unsigned __int64 nIDOld;
        HINSTANCE__ *hInstNew;
        unsigned __int64 nIDNew;
        int nButtons;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

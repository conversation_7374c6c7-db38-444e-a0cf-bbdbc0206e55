// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_EMBELLKEY.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _EQUIP_DB_BASE
    {
        struct  _EMBELLISH_LIST
        {
            _EMBELLKEY Key;
            unsigned __int16 wAmount;
            unsigned int dwItemETSerial;
            unsigned __int64 lnUID;
            char byCsMethod;
            unsigned int dwT;
            unsigned int dwLendRegdTime;
        public:
            void Init();
            bool Release();
            bool Set(_STORAGE_LIST::_db_con* pItem);
            _EMBELLISH_LIST();
            void ctor__EMBELLISH_LIST();
        };
        _EMBELLISH_LIST m_EmbellishList[7];
    public:
        void Init();
        _EQUIP_DB_BASE();
        void ctor__EQUIP_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_EQUIP_DB_BASE, 189>(), "_EQUIP_DB_BASE");
END_ATF_NAMESPACE

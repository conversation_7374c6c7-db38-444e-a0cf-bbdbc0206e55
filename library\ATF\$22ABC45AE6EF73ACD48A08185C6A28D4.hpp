// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CREATE_PROCESS_DEBUG_INFO.hpp>
#include <_CREATE_THREAD_DEBUG_INFO.hpp>
#include <_EXCEPTION_DEBUG_INFO.hpp>
#include <_EXIT_PROCESS_DEBUG_INFO.hpp>
#include <_EXIT_THREAD_DEBUG_INFO.hpp>
#include <_LOAD_DLL_DEBUG_INFO.hpp>
#include <_OUTPUT_DEBUG_STRING_INFO.hpp>
#include <_RIP_INFO.hpp>
#include <_UNLOAD_DLL_DEBUG_INFO.hpp>


START_ATF_NAMESPACE
    union $22ABC45AE6EF73ACD48A08185C6A28D4
    {
        _EXCEPTION_DEBUG_INFO Exception;
        _CREATE_THREAD_DEBUG_INFO CreateThread;
        _CREATE_PROCESS_DEBUG_INFO CreateProcessInfo;
        _EXIT_THREAD_DEBUG_INFO ExitThread;
        _EXIT_PROCESS_DEBUG_INFO ExitProcess;
        _LOAD_DLL_DEBUG_INFO LoadDll;
        _UNLOAD_DLL_DEBUG_INFO UnloadDll;
        _OUTPUT_DEBUG_STRING_INFO DebugString;
        _RIP_INFO RipInfo;
    };
END_ATF_NAMESPACE

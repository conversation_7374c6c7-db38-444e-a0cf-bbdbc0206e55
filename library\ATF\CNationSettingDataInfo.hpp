// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingDatactor_CNationSettingData2_ptr = void (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDatactor_CNationSettingData2_clbk = void (WINAPIV*)(struct CNationSettingData*, CNationSettingDatactor_CNationSettingData2_ptr);
        using CNationSettingDataCheckDBCSCompleteString4_ptr = bool (WINAPIV*)(struct CNationSettingData*, int, char*, uint64_t*);
        using CNationSettingDataCheckDBCSCompleteString4_clbk = bool (WINAPIV*)(struct CNationSettingData*, int, char*, uint64_t*, CNationSettingDataCheckDBCSCompleteString4_ptr);
        using CNationSettingDataCheckEnterWorldRequest6_ptr = bool (WINAPIV*)(struct CNationSettingData*, int, char*);
        using CNationSettingDataCheckEnterWorldRequest6_clbk = bool (WINAPIV*)(struct CNationSettingData*, int, char*, CNationSettingDataCheckEnterWorldRequest6_ptr);
        using CNationSettingDataCreateBilling8_ptr = struct CBilling* (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataCreateBilling8_clbk = struct CBilling* (WINAPIV*)(struct CNationSettingData*, CNationSettingDataCreateBilling8_ptr);
        using CNationSettingDataCreateComplete10_ptr = void (WINAPIV*)(struct CNationSettingData*, struct CPlayer*);
        using CNationSettingDataCreateComplete10_clbk = void (WINAPIV*)(struct CNationSettingData*, struct CPlayer*, CNationSettingDataCreateComplete10_ptr);
        using CNationSettingDataCreateWorker12_ptr = struct CashDbWorker* (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataCreateWorker12_clbk = struct CashDbWorker* (WINAPIV*)(struct CNationSettingData*, CNationSettingDataCreateWorker12_ptr);
        using CNationSettingDataGetCashItemPrice14_ptr = int (WINAPIV*)(struct CNationSettingData*, struct _CashShop_str_fld*);
        using CNationSettingDataGetCashItemPrice14_clbk = int (WINAPIV*)(struct CNationSettingData*, struct _CashShop_str_fld*, CNationSettingDataGetCashItemPrice14_ptr);
        using CNationSettingDataGetFireGuardEnableSetting16_ptr = bool (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataGetFireGuardEnableSetting16_clbk = bool (WINAPIV*)(struct CNationSettingData*, CNationSettingDataGetFireGuardEnableSetting16_ptr);
        using CNationSettingDataGetGameGuardSystem18_ptr = struct INationGameGuardSystem* (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataGetGameGuardSystem18_clbk = struct INationGameGuardSystem* (WINAPIV*)(struct CNationSettingData*, CNationSettingDataGetGameGuardSystem18_ptr);
        using CNationSettingDataGetItemName20_ptr = char* (WINAPIV*)(struct CNationSettingData*, struct _NameTxt_fld*);
        using CNationSettingDataGetItemName20_clbk = char* (WINAPIV*)(struct CNationSettingData*, struct _NameTxt_fld*, CNationSettingDataGetItemName20_ptr);
        using CNationSettingDataGetNoneString22_ptr = char* (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataGetNoneString22_clbk = char* (WINAPIV*)(struct CNationSettingData*, CNationSettingDataGetNoneString22_ptr);
        using CNationSettingDataGetTimeLimitEnableSetting24_ptr = bool (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataGetTimeLimitEnableSetting24_clbk = bool (WINAPIV*)(struct CNationSettingData*, CNationSettingDataGetTimeLimitEnableSetting24_ptr);
        using CNationSettingDataInit26_ptr = int (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataInit26_clbk = int (WINAPIV*)(struct CNationSettingData*, CNationSettingDataInit26_ptr);
        using CNationSettingDataIsApplyPcbangPrimium28_ptr = bool (WINAPIV*)(struct CNationSettingData*, struct CPlayer*);
        using CNationSettingDataIsApplyPcbangPrimium28_clbk = bool (WINAPIV*)(struct CNationSettingData*, struct CPlayer*, CNationSettingDataIsApplyPcbangPrimium28_ptr);
        using CNationSettingDataIsCashDBDSNSetted30_ptr = bool (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataIsCashDBDSNSetted30_clbk = bool (WINAPIV*)(struct CNationSettingData*, CNationSettingDataIsCashDBDSNSetted30_ptr);
        using CNationSettingDataIsCashDBInit32_ptr = bool (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataIsCashDBInit32_clbk = bool (WINAPIV*)(struct CNationSettingData*, CNationSettingDataIsCashDBInit32_ptr);
        using CNationSettingDataIsCashDBUseExtRef34_ptr = bool (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataIsCashDBUseExtRef34_clbk = bool (WINAPIV*)(struct CNationSettingData*, CNationSettingDataIsCashDBUseExtRef34_ptr);
        using CNationSettingDataIsNormalChar36_ptr = bool (WINAPIV*)(struct CNationSettingData*, wchar_t);
        using CNationSettingDataIsNormalChar36_clbk = bool (WINAPIV*)(struct CNationSettingData*, wchar_t, CNationSettingDataIsNormalChar36_ptr);
        using CNationSettingDataIsNormalString38_ptr = bool (WINAPIV*)(struct CNationSettingData*, char*);
        using CNationSettingDataIsNormalString38_clbk = bool (WINAPIV*)(struct CNationSettingData*, char*, CNationSettingDataIsNormalString38_ptr);
        using CNationSettingDataIsNormalString40_ptr = bool (WINAPIV*)(struct CNationSettingData*, wchar_t*);
        using CNationSettingDataIsNormalString40_clbk = bool (WINAPIV*)(struct CNationSettingData*, wchar_t*, CNationSettingDataIsNormalString40_ptr);
        using CNationSettingDataIsNormalStringDefProc42_ptr = bool (WINAPIV*)(struct CNationSettingData*, char*, char*);
        using CNationSettingDataIsNormalStringDefProc42_clbk = bool (WINAPIV*)(struct CNationSettingData*, char*, char*, CNationSettingDataIsNormalStringDefProc42_ptr);
        using CNationSettingDataIsNormalStringDefProc44_ptr = bool (WINAPIV*)(struct CNationSettingData*, wchar_t*, wchar_t*);
        using CNationSettingDataIsNormalStringDefProc44_clbk = bool (WINAPIV*)(struct CNationSettingData*, wchar_t*, wchar_t*, CNationSettingDataIsNormalStringDefProc44_ptr);
        using CNationSettingDataIsPersonalFreeFixedAmountBillingType46_ptr = bool (WINAPIV*)(struct CNationSettingData*, int16_t*, int16_t*);
        using CNationSettingDataIsPersonalFreeFixedAmountBillingType46_clbk = bool (WINAPIV*)(struct CNationSettingData*, int16_t*, int16_t*, CNationSettingDataIsPersonalFreeFixedAmountBillingType46_ptr);
        using CNationSettingDataLoop48_ptr = void (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataLoop48_clbk = void (WINAPIV*)(struct CNationSettingData*, CNationSettingDataLoop48_ptr);
        using CNationSettingDataNetClose50_ptr = void (WINAPIV*)(struct CNationSettingData*, struct CPlayer*);
        using CNationSettingDataNetClose50_clbk = void (WINAPIV*)(struct CNationSettingData*, struct CPlayer*, CNationSettingDataNetClose50_ptr);
        using CNationSettingDataReadSystemPass52_ptr = bool (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataReadSystemPass52_clbk = bool (WINAPIV*)(struct CNationSettingData*, CNationSettingDataReadSystemPass52_ptr);
        using CNationSettingDataSendCashDBDSNRequest54_ptr = void (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataSendCashDBDSNRequest54_clbk = void (WINAPIV*)(struct CNationSettingData*, CNationSettingDataSendCashDBDSNRequest54_ptr);
        using CNationSettingDataSetCahsDBUseExtRefFlag56_ptr = void (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataSetCahsDBUseExtRefFlag56_clbk = void (WINAPIV*)(struct CNationSettingData*, CNationSettingDataSetCahsDBUseExtRefFlag56_ptr);
        using CNationSettingDataSetCashDBDSN58_ptr = void (WINAPIV*)(struct CNationSettingData*, char*, char*, char*, char*, unsigned int);
        using CNationSettingDataSetCashDBDSN58_clbk = void (WINAPIV*)(struct CNationSettingData*, char*, char*, char*, char*, unsigned int, CNationSettingDataSetCashDBDSN58_ptr);
        using CNationSettingDataSetCashDBDSNSetFlag60_ptr = void (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataSetCashDBDSNSetFlag60_clbk = void (WINAPIV*)(struct CNationSettingData*, CNationSettingDataSetCashDBDSNSetFlag60_ptr);
        using CNationSettingDataSetCashDBInitFlag62_ptr = void (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataSetCashDBInitFlag62_clbk = void (WINAPIV*)(struct CNationSettingData*, CNationSettingDataSetCashDBInitFlag62_ptr);
        using CNationSettingDataSetUnitPassiveValue64_ptr = void (WINAPIV*)(struct CNationSettingData*, float*);
        using CNationSettingDataSetUnitPassiveValue64_clbk = void (WINAPIV*)(struct CNationSettingData*, float*, CNationSettingDataSetUnitPassiveValue64_ptr);
        using CNationSettingDataValidMacAddress66_ptr = bool (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDataValidMacAddress66_clbk = bool (WINAPIV*)(struct CNationSettingData*, CNationSettingDataValidMacAddress66_ptr);
        
        using CNationSettingDatadtor_CNationSettingData70_ptr = void (WINAPIV*)(struct CNationSettingData*);
        using CNationSettingDatadtor_CNationSettingData70_clbk = void (WINAPIV*)(struct CNationSettingData*, CNationSettingDatadtor_CNationSettingData70_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

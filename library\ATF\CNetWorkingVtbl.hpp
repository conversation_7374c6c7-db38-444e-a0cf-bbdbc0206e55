// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CNetWorkingVtbl
    {
        void *(WINAPIV *__vecDelDtor)(struct CNetWorking *_this, unsigned int);
        void (WIN<PERSON>IV *UserLoop)(struct CNetWorking *_this);
        bool (WINAPIV *DataAnalysis)(struct CNetWorking *_this, unsigned int, unsigned int, struct _MSG_HEADER *, char *);
        void (WINAPIV *AcceptClientCheck)(struct CNetWorking *_this, unsigned int, unsigned int, unsigned int);
        void (WINAPIV *CloseClientCheck)(struct CNetWorking *_this, unsigned int, unsigned int, unsigned int);
        bool (WINAPIV *ExpulsionSocket)(struct CNetWorking *_this, unsigned int, unsigned int, char, void *);
        void (WINAPIV *AnsyncConnectComplete)(struct CNetWorking *_this, unsigned int, unsigned int, int);
    };
END_ATF_NAMESPACE

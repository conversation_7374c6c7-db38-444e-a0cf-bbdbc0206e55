// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDummyPosTableVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CDummyPosTable
    {
        CDummyPosTableVtbl *vfptr;
        struct _dummy_position *m_pDumPos;
        int m_nDumPosDataNum;
    public:
        CDummyPosTable();
        void ctor_CDummyPosTable();
        static bool CheckHeroesDummy(struct CGameObject* pObj, char byRaceCode);
        static bool FindDummy(char* pszTextFileName, char* pszDummyCode, struct _dummy_position* pDummyPos);
        struct _dummy_position* GetRecord(char* szCode);
        struct _dummy_position* GetRecord(int i);
        int GetRecordNum();
        bool LoadDummyPosition(char* szTextFileName, char* szPrefix);
        ~CDummyPosTable();
        void dtor_CDummyPosTable();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CDummyPosTable, 24>(), "CDummyPosTable");
END_ATF_NAMESPACE

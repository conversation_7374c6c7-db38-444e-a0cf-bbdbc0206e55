// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <MyTimer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using MyTimerGetTime2_ptr = struct MyTimer::TIME* (WINAPIV*)();
        using MyTimerGetTime2_clbk = struct MyTimer::TIME* (WINAPIV*)(MyTimerGetTime2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

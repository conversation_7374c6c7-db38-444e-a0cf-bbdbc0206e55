// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $A04CD8C0331024E133CCEC4B0AB36956
    {
        unsigned int Lo32;
        unsigned int Mid32;
    };    
    static_assert(ATF::checkSize<$A04CD8C0331024E133CCEC4B0AB36956, 8>(), "$A04CD8C0331024E133CCEC4B0AB36956");
END_ATF_NAMESPACE

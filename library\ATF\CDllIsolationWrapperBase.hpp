// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CNoTrackObject.hpp>
#include <HINSTANCE__.hpp>


START_ATF_NAMESPACE
    struct  CDllIsolationWrapperBase : CNoTrackObject
    {
        HINSTANCE__ *m_hModule;
        bool m_bFreeLib;
        ATL::CStringT<char> m_strModuleName;
    };
END_ATF_NAMESPACE

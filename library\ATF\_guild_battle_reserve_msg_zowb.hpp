// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _guild_battle_reserve_msg_zowb
    {
        char byTimeID;
        char byRace;
        unsigned int dw1PGuildSerial;
        char by1PGuildGrade;
        char wsz1PName[17];
        unsigned int dw2PGuildSerial;
        char by2PGuildGrade;
        char wsz2PName[17];
        char byJoinLimit;
        unsigned int dwGuildBattleCost;
        char szBattleMapCode[12];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

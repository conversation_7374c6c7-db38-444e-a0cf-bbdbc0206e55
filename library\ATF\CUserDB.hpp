// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <CRadarItemMgr.hpp>
#include <CUserDBVtbl.hpp>
#include <_AVATOR_DATA.hpp>
#include <_BILLING_INFO.hpp>
#include <_CLID.hpp>
#include <_CUTTING_DB_BASE.hpp>
#include <_DB_QRY_SYN_DATA.hpp>
#include <_EXIT_ALTER_PARAM.hpp>
#include <_GLBID.hpp>
#include <_ITEMCOMBINE_DB_BASE.hpp>
#include <_NOT_ARRANGED_AVATOR_DB.hpp>
#include <_QUEST_DB_BASE.hpp>
#include <_REGED.hpp>
#include <_REGED_AVATOR_DB.hpp>
#include <_STORAGE_LIST.hpp>
#include <_SYNC_STATE.hpp>
#include <_SYSTEMTIME.hpp>
#include <_UNIT_DB_BASE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CUserDB
    {
        CUserDBVtbl *vfptr;
        _GLBID m_gidGlobal;
        _CLID m_idWorld;
        unsigned int m_dwIP;
        unsigned int m_dwTotalPlayMin;
        char m_szAccountID[13];
        unsigned int m_dwAccountSerial;
        unsigned int m_ipAddress;
        char m_byUserDgr;
        char m_bySubDgr;
        char m_wszAvatorName[17];
        char m_aszAvatorName[17];
        unsigned int m_dwSerial;
        char m_byNameLen;
        _REGED m_RegedList[3];
        _AVATOR_DATA m_AvatorData;
        _AVATOR_DATA m_AvatorData_bk;
        _NOT_ARRANGED_AVATOR_DB m_NotArrangedChar[50];
        unsigned int m_dwArrangePassCase0[50];
        bool m_bActive;
        bool m_bField;
        bool m_bWndFullMode;
        bool m_bDBWaitState;
        _DB_QRY_SYN_DATA *m_pDBPushData;
        bool m_bChatLock;
        _SYNC_STATE m_ss;
        unsigned int m_dwMessengerKey[4];
        unsigned int m_dwOperLobbyTime;
        bool m_bCreateTrunkFree;
        CMyTimer m_tmrCheckPlayMin;
        bool m_bDataUpdate;
        unsigned int m_dwTermContSaveTime;
        unsigned int m_dwLastContSaveTime;
        bool m_bNoneUpdateData;
        _BILLING_INFO m_BillingInfo;
        bool m_bBillingNoLogout;
        int m_nTrans;
        CRadarItemMgr m_RadarItemMgr;
        char m_byUILock;
        bool m_bUILock_Updated;
        char m_byUILock_InitFailCnt;
        char m_byUILock_FailCnt;
        char m_szUILock_PW[13];
        char m_szAccount_PW[13];
        char m_byUILock_HintIndex;
        char m_uszUILock_HintAnswer[17];
        char m_byUILock_InitFindPassFailCount;
        char m_byUILockFindPassFailCount;
        unsigned int m_dwRequestMoveCharacterSerialList[3];
        unsigned int m_dwTournamentCharacterSerialList[3];
        char m_szLobbyHistoryFileName[64];
    public:
        void Alive_Char_Complete(char byRetCode, char byCase, unsigned int dwSerial, struct _REGED* pAliveAvator);
        bool Alive_Char_Request(char byCase, unsigned int dwSerial, char* pwszName, char bySlotIndex);
        CUserDB();
        void ctor_CUserDB();
        void CalcRadarDelay();
        static bool CheckDQSLoadCharacterData(struct _AVATOR_DATA* pData);
        void ClearBillingData();
        void Cont_UserSave_Complete(char byResult, struct _AVATOR_DATA* pAvatorData);
        static bool DataValidCheckRevise(struct _AVATOR_DATA* pData, bool* pDataUpdated);
        void DelPostData(unsigned int dwIndex);
        void Delete_Char_Complete(char byRetCode, char bySlotIndex);
        bool Delete_Char_Request(char bySlotIndex);
        void DummyCreate(unsigned int dwSerial);
        bool Enter_Account(unsigned int dwAccountSerial, unsigned int dwIP, unsigned int dwProtocolVer, unsigned int* pdwMasterKey);
        void Exit_Account_Complete(char byRetCode);
        void Exit_Account_Request();
        bool FirstSettingData();
        void ForceCloseCommand(char byKickType, unsigned int dwPushIP, bool bSlow, char* pszCause);
        unsigned int GetActPoint(char byCode);
        int GetBillingType();
        unsigned int* GetPtrActPoint();
        void Inform_For_Exit_By_FireguardBlock();
        void Init(unsigned int dwIndex);
        bool InitClass(char* pszClassCode);
        void Insert_Char_Complete(char byRetCode, struct _REGED_AVATOR_DB* pInsertData);
        bool Insert_Char_Request(char* pwszCharName, char bySlotIndex, char byRaceSexCode, char* pszClassCode, unsigned int dwBaseShape);
        struct _AVATOR_DATA* IsContPushBefore();
        char IsExistRequestMoveCharacterList(unsigned int dwCharSerial);
        bool IsReturnPostUpdate();
        void Lobby_Char_Complete(char byRetCode);
        bool Lobby_Char_Request();
        static void OnLoop_Static();
        void ParamInit();
        static void ReRangeClientIndex(struct _AVATOR_DATA* pData);
        void Reged_Char_Complete(char byRetCode, struct _REGED* pRegedList, struct _NOT_ARRANGED_AVATOR_DB* pArrangedList);
        bool Reged_Char_Request();
        void Select_Char_Complete(char byRetCode, struct _AVATOR_DATA* pLoadData, bool* pbAddItem, unsigned int dwAddDalant, unsigned int dwAddGold, unsigned int dwCheckSum, bool* pbTrunkAddItem, char byTrunkOldSlot, long double dTrunkOldDalant, long double dTrunkOldGold, bool bCreateTrunkFree, bool* pbExtTrunkAddItem, char byExtTrunkOldSlot);
        bool Select_Char_Request(char bySlotIndex);
        void SendMsgAccount_UILockRefresh_Update();
        void SendMsg_BillingInfo();
        void SendMsg_Inform_UILock();
        void SetActPoint(char byCode, unsigned int dwLeftPoint);
        void SetBillingData(struct _BILLING_INFO* pBillingInfo);
        void SetBillingData(char* szCMSCode, int16_t iType, int lRemainTime, struct _SYSTEMTIME* pstEndDate);
        void SetBillingNoLogout(bool bNoLogout);
        void SetChatLock(bool bLock);
        void SetDBPostData(int n, unsigned int dwSerial, int nNumber, char byState, int nKey, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, bool bUpdateIndex, uint64_t lnUID);
        void SetNewDBPostData(int n, unsigned int dwSerial, int nNumber, char byState, char* wszSendName, char* wszRecvName, char* wszTitle, char* wszContent, int nKey, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, uint64_t lnUID);
        void SetRadarDelay(unsigned int dwDelay);
        void SetRemainTime(int lRemainTime);
        void SetWorldCLID(unsigned int dwSerial, unsigned int* pipAddr);
        bool Setting_Class(char* pszClassCode);
        void StartFieldMode();
        void TotalPlayMinCheck();
        void UILockInfo_Init(char* pMsg);
        void UILockInfo_Update(char* pMsg);
        bool UpdateContUserSave(bool bDirect);
        bool Update_AddBuddy(char bySlotIndex, unsigned int dwAdderSerial, char* pwszAdderName);
        bool Update_AlterPvPCashBag(long double dNewPoint);
        bool Update_AlterPvPPoint(long double dNewPoint);
        bool Update_AutoTradeAllClear();
        bool Update_BagNum(char bagnum);
        bool Update_Bind(char* pszMapCode, char* pDummyCode, bool bUpdate);
        void Update_BossCryMsg(char bySlot, char* pwszCryMsg);
        bool Update_Class(char* pszClassCode, char byHistoryRecordNum, uint16_t wHistoryClassIndex);
        bool Update_CombineExResult_Pop();
        bool Update_CombineExResult_Push(struct _ITEMCOMBINE_DB_BASE* pItemCombineDB_IN);
        bool Update_CopyAll(struct _AVATOR_DATA* pSrc);
        bool Update_CuttingEmpty();
        bool Update_CuttingPush(char resnum, struct _CUTTING_DB_BASE::_LIST* plist);
        bool Update_CuttingTrans(uint16_t wResItemIndex, uint16_t wLeftAmt);
        bool Update_DelBuddy(char bySlotIndex);
        bool Update_DelPost(unsigned int dwSerial, int nIndex);
        bool Update_Exp(long double exp);
        bool Update_ExtTrunkSlotNum(char byExtSlotNum);
        bool Update_ItemAdd(char storage, char slot, struct _STORAGE_LIST::_db_con* pItem, bool bUpdate);
        bool Update_ItemDelete(char storage, char slot, bool bUpdate);
        bool Update_ItemDur(char storage, char slot, uint64_t amount, bool bUpdate);
        bool Update_ItemSlot(char storage, char slot, char clientpos);
        bool Update_ItemUpgrade(char storage, char slot, unsigned int upg, bool bUpdate);
        void Update_LastAttBuff(bool bSet);
        bool Update_Level(char lv, long double exp);
        bool Update_LinkBoardLock(char byLBLock);
        bool Update_LinkBoardSlot(char bySlot, char byLinkCode, uint16_t wIndex);
        bool Update_LossExp(long double dLossExp);
        bool Update_Macro(char* pBuf);
        bool Update_Map(char map, float* pos);
        void Update_MaxLevel(char byMaxLevel);
        bool Update_Money(unsigned int dalant, unsigned int gold);
        bool Update_NPCQuestHistory(char byIndex, struct _QUEST_DB_BASE::_NPC_QUEST_HISTORY* pHisData);
        bool Update_Param(struct _EXIT_ALTER_PARAM* pCon);
        bool Update_PlayTime(unsigned int dwTotalTimeMin);
        void Update_Post(int n, unsigned int dwSerial, int nNumber, char byState, int nKey, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, uint64_t lnUID);
        void Update_PotionNextUseTime(char byPotionClass, unsigned int dwNextUseTime);
        void Update_PvpPointLeak(long double dValue);
        bool Update_QuestDelete(char bySlotIndex);
        bool Update_QuestInsert(char bySlotIndex, struct _QUEST_DB_BASE::_LIST* pSlotData);
        bool Update_QuestUpdate(char bySlotIndex, struct _QUEST_DB_BASE::_LIST* pSlotData, bool bUpdate);
        bool Update_RaceVoteInfoInit();
        void Update_ReturnPost(unsigned int dwSerial);
        bool Update_SFContDelete(char byContCode, char bySlotIndex);
        bool Update_SFContInsert(char byContCode, char bySlotIndex, char byEffectCode, uint16_t wEffectIndex, char byLv, uint16_t wDurSec);
        bool Update_SFContUpdate(char byContCode, char bySlotIndex, uint16_t wTime, bool bUpdate);
        bool Update_StartNPCQuestHistory(char byIndex, struct _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY* pHisData);
        bool Update_Stat(char byStatIndex, unsigned int dwNewCum, bool bUpdate);
        bool Update_TakeLastCriTicket(unsigned int dwCriTicket);
        bool Update_TakeLastMentalTicket(unsigned int dwMentalTicket);
        bool Update_TrunkHint(char byHintIndex, char* pwszHintAnswer);
        bool Update_TrunkMoney(long double dGold, long double dDalant);
        bool Update_TrunkPassword(char* pwszPassword);
        bool Update_TrunkSlotNum(char bySlotNum);
        bool Update_UnitData(char bySlotIndex, struct _UNIT_DB_BASE::_LIST* pData);
        bool Update_UnitDelete(char bySlotIndex);
        bool Update_UnitInsert(char bySlotIndex, struct _UNIT_DB_BASE::_LIST* pSlotData);
        bool Update_UserFatigue(unsigned int dwFatigue);
        bool Update_UserGetScaner(uint16_t wScanerCnt, uint16_t wBattleTime);
        bool Update_UserPlayTime(unsigned int dwAccPlayTime);
        bool Update_UserTLStatus(char byStatus);
        bool Update_UserVoteData();
        bool Update_User_Action_Point(char byActionCode, unsigned int dwPoint);
        bool Update_WindowInfo(unsigned int* pdwSkill, unsigned int* pdwForce, unsigned int* pdwChar, unsigned int* pdwAnimus, unsigned int dwInven, unsigned int* pdwInvenBag);
        void WriteLog_ChangeClassAfterInitClass(char byType, char* szPrevClass);
        void WriteLog_CharSelect();
        void WriteLog_Level(char byLv);
        ~CUserDB();
        void dtor_CUserDB();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

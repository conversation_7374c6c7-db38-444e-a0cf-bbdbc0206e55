// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ATTACK_DELAY_CHECKER.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _ATTACK_DELAY_CHECKERInit2_ptr = void (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*);
        using _ATTACK_DELAY_CHECKERInit2_clbk = void (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*, _ATTACK_DELAY_CHECKERInit2_ptr);
        using _ATTACK_DELAY_CHECKERIsDelay4_ptr = bool (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*, char, uint16_t, char);
        using _ATTACK_DELAY_CHECKERIsDelay4_clbk = bool (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*, char, uint16_t, char, _ATTACK_DELAY_CHECKERIsDelay4_ptr);
        using _ATTACK_DELAY_CHECKERSetDelay6_ptr = void (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*, unsigned int);
        using _ATTACK_DELAY_CHECKERSetDelay6_clbk = void (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*, unsigned int, _ATTACK_DELAY_CHECKERSetDelay6_ptr);
        
        using _ATTACK_DELAY_CHECKERctor__ATTACK_DELAY_CHECKER8_ptr = void (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*);
        using _ATTACK_DELAY_CHECKERctor__ATTACK_DELAY_CHECKER8_clbk = void (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*, _ATTACK_DELAY_CHECKERctor__ATTACK_DELAY_CHECKER8_ptr);
        using _ATTACK_DELAY_CHECKER_delay_check10_ptr = bool (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*, char, uint16_t, char);
        using _ATTACK_DELAY_CHECKER_delay_check10_clbk = bool (WINAPIV*)(struct _ATTACK_DELAY_CHECKER*, char, uint16_t, char, _ATTACK_DELAY_CHECKER_delay_check10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

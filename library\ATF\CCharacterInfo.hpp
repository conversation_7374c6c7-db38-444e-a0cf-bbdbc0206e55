// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CCharacterAlterContDurSec2_ptr = void (WINAPIV*)(struct CCharacter*, char, uint16_t, unsigned int, uint16_t);
        using CCharacterAlterContDurSec2_clbk = void (WINAPIV*)(struct CCharacter*, char, uint16_t, unsigned int, uint16_t, CCharacterAlterContDurSec2_ptr);
        using CCharacterAssistForce4_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, struct _force_fld*, int, char*, bool*);
        using CCharacterAssistForce4_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, struct _force_fld*, int, char*, bool*, CCharacterAssistForce4_ptr);
        using CCharacterAssistForceToOne6_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, struct _force_fld*, int);
        using CCharacterAssistForceToOne6_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, struct _force_fld*, int, CCharacterAssistForceToOne6_ptr);
        using CCharacterAssistSkill8_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, int, struct _skill_fld*, int, char*, bool*);
        using CCharacterAssistSkill8_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, int, struct _skill_fld*, int, char*, bool*, CCharacterAssistSkill8_ptr);
        using CCharacterAssistSkillToOne10_ptr = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, int, struct _skill_fld*, int);
        using CCharacterAssistSkillToOne10_clbk = bool (WINAPIV*)(struct CCharacter*, struct CCharacter*, int, struct _skill_fld*, int, CCharacterAssistSkillToOne10_ptr);
        using CCharacterBreakStealth12_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterBreakStealth12_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterBreakStealth12_ptr);
        
        using CCharacterctor_CCharacter14_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterctor_CCharacter14_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterctor_CCharacter14_ptr);
        using CCharacterCalcDistForSec16_ptr = float (WINAPIV*)(struct CCharacter*, float, float);
        using CCharacterCalcDistForSec16_clbk = float (WINAPIV*)(struct CCharacter*, float, float, CCharacterCalcDistForSec16_ptr);
        using CCharacterCalcEffectBit18_ptr = uint16_t (WINAPIV*)(struct CCharacter*, uint16_t, uint16_t);
        using CCharacterCalcEffectBit18_clbk = uint16_t (WINAPIV*)(struct CCharacter*, uint16_t, uint16_t, CCharacterCalcEffectBit18_ptr);
        using CCharacterCreate20_ptr = bool (WINAPIV*)(struct CCharacter*, struct _character_create_setdata*);
        using CCharacterCreate20_clbk = bool (WINAPIV*)(struct CCharacter*, struct _character_create_setdata*, CCharacterCreate20_ptr);
        using CCharacterDestroy22_ptr = bool (WINAPIV*)(struct CCharacter*);
        using CCharacterDestroy22_clbk = bool (WINAPIV*)(struct CCharacter*, CCharacterDestroy22_ptr);
        using CCharacterFindEffectDst24_ptr = int (WINAPIV*)(struct CCharacter*, int, int, int, bool, struct CCharacter*, char*, struct CCharacter**);
        using CCharacterFindEffectDst24_clbk = int (WINAPIV*)(struct CCharacter*, int, int, int, bool, struct CCharacter*, char*, struct CCharacter**, CCharacterFindEffectDst24_ptr);
        using CCharacterFindPotionEffectDst26_ptr = int (WINAPIV*)(struct CCharacter*, int, int, bool, struct CCharacter*, char*, struct CCharacter**, bool*);
        using CCharacterFindPotionEffectDst26_clbk = int (WINAPIV*)(struct CCharacter*, int, int, bool, struct CCharacter*, char*, struct CCharacter**, bool*, CCharacterFindPotionEffectDst26_ptr);
        using CCharacterGetAttackDamPoint28_ptr = int (WINAPIV*)(struct CCharacter*, int, int, int, struct CCharacter*, bool);
        using CCharacterGetAttackDamPoint28_clbk = int (WINAPIV*)(struct CCharacter*, int, int, int, struct CCharacter*, bool, CCharacterGetAttackDamPoint28_ptr);
        using CCharacterGetAttackRandomPart30_ptr = int (WINAPIV*)(struct CCharacter*);
        using CCharacterGetAttackRandomPart30_clbk = int (WINAPIV*)(struct CCharacter*, CCharacterGetAttackRandomPart30_ptr);
        using CCharacterGetInvisible32_ptr = bool (WINAPIV*)(struct CCharacter*);
        using CCharacterGetInvisible32_clbk = bool (WINAPIV*)(struct CCharacter*, CCharacterGetInvisible32_ptr);
        using CCharacterGetNearEmptySlot34_ptr = int (WINAPIV*)(struct CCharacter*, int, float, float*, float*);
        using CCharacterGetNearEmptySlot34_clbk = int (WINAPIV*)(struct CCharacter*, int, float, float*, float*, CCharacterGetNearEmptySlot34_ptr);
        using CCharacterGetNextGenAttTime36_ptr = unsigned int (WINAPIV*)(struct CCharacter*);
        using CCharacterGetNextGenAttTime36_clbk = unsigned int (WINAPIV*)(struct CCharacter*, CCharacterGetNextGenAttTime36_ptr);
        using CCharacterGetSlot38_ptr = int (WINAPIV*)(struct CCharacter*, struct CCharacter*);
        using CCharacterGetSlot38_clbk = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, CCharacterGetSlot38_ptr);
        using CCharacterGetStealth40_ptr = bool (WINAPIV*)(struct CCharacter*, bool);
        using CCharacterGetStealth40_clbk = bool (WINAPIV*)(struct CCharacter*, bool, CCharacterGetStealth40_ptr);
        using CCharacterGetTotalTol42_ptr = int (WINAPIV*)(struct CCharacter*, char, int);
        using CCharacterGetTotalTol42_clbk = int (WINAPIV*)(struct CCharacter*, char, int, CCharacterGetTotalTol42_ptr);
        using CCharacterGo44_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterGo44_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterGo44_ptr);
        using CCharacterInit46_ptr = void (WINAPIV*)(struct CCharacter*, struct _object_id*);
        using CCharacterInit46_clbk = void (WINAPIV*)(struct CCharacter*, struct _object_id*, CCharacterInit46_ptr);
        using CCharacterInsertSFContEffect48_ptr = char (WINAPIV*)(struct CCharacter*, char, char, unsigned int, uint16_t, char, bool*, struct CCharacter*);
        using CCharacterInsertSFContEffect48_clbk = char (WINAPIV*)(struct CCharacter*, char, char, unsigned int, uint16_t, char, bool*, struct CCharacter*, CCharacterInsertSFContEffect48_ptr);
        using CCharacterInsertSlot50_ptr = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, int);
        using CCharacterInsertSlot50_clbk = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, int, CCharacterInsertSlot50_ptr);
        using CCharacterIsDamageEffect52_ptr = bool (WINAPIV*)(struct CCharacter*, unsigned int, uint16_t);
        using CCharacterIsDamageEffect52_clbk = bool (WINAPIV*)(struct CCharacter*, unsigned int, uint16_t, CCharacterIsDamageEffect52_ptr);
        using CCharacterIsEffectableDst54_ptr = bool (WINAPIV*)(struct CCharacter*, char*, struct CCharacter*);
        using CCharacterIsEffectableDst54_clbk = bool (WINAPIV*)(struct CCharacter*, char*, struct CCharacter*, CCharacterIsEffectableDst54_ptr);
        using CCharacterIsPotionEffectableDst56_ptr = bool (WINAPIV*)(struct CCharacter*, char*, struct CCharacter*);
        using CCharacterIsPotionEffectableDst56_clbk = bool (WINAPIV*)(struct CCharacter*, char*, struct CCharacter*, CCharacterIsPotionEffectableDst56_ptr);
        using CCharacterMove58_ptr = void (WINAPIV*)(struct CCharacter*, float);
        using CCharacterMove58_clbk = void (WINAPIV*)(struct CCharacter*, float, CCharacterMove58_ptr);
        using CCharacterMoveBreak60_ptr = void (WINAPIV*)(struct CCharacter*, float);
        using CCharacterMoveBreak60_clbk = void (WINAPIV*)(struct CCharacter*, float, CCharacterMoveBreak60_ptr);
        using CCharacterRemoveAllContinousEffect62_ptr = bool (WINAPIV*)(struct CCharacter*);
        using CCharacterRemoveAllContinousEffect62_clbk = bool (WINAPIV*)(struct CCharacter*, CCharacterRemoveAllContinousEffect62_ptr);
        using CCharacterRemoveAllContinousEffectGroup64_ptr = bool (WINAPIV*)(struct CCharacter*, unsigned int);
        using CCharacterRemoveAllContinousEffectGroup64_clbk = bool (WINAPIV*)(struct CCharacter*, unsigned int, CCharacterRemoveAllContinousEffectGroup64_ptr);
        using CCharacterRemoveSFContEffect66_ptr = void (WINAPIV*)(struct CCharacter*, char, uint16_t, bool, bool);
        using CCharacterRemoveSFContEffect66_clbk = void (WINAPIV*)(struct CCharacter*, char, uint16_t, bool, bool, CCharacterRemoveSFContEffect66_ptr);
        using CCharacterRemoveSFContHelpByEffect68_ptr = void (WINAPIV*)(struct CCharacter*, int, int);
        using CCharacterRemoveSFContHelpByEffect68_clbk = void (WINAPIV*)(struct CCharacter*, int, int, CCharacterRemoveSFContHelpByEffect68_ptr);
        using CCharacterRemoveSlot70_ptr = int (WINAPIV*)(struct CCharacter*, struct CCharacter*);
        using CCharacterRemoveSlot70_clbk = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, CCharacterRemoveSlot70_ptr);
        using CCharacterResetSlot72_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterResetSlot72_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterResetSlot72_ptr);
        using CCharacterSFContInit74_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterSFContInit74_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterSFContInit74_ptr);
        using CCharacterSendMsg_AttackActEffect76_ptr = void (WINAPIV*)(struct CCharacter*, char, struct CCharacter*);
        using CCharacterSendMsg_AttackActEffect76_clbk = void (WINAPIV*)(struct CCharacter*, char, struct CCharacter*, CCharacterSendMsg_AttackActEffect76_ptr);
        using CCharacterSendMsg_LastEffectChangeInform78_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterSendMsg_LastEffectChangeInform78_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterSendMsg_LastEffectChangeInform78_ptr);
        using CCharacterSendMsg_RobedHP80_ptr = void (WINAPIV*)(struct CCharacter*, struct CCharacter*, uint16_t);
        using CCharacterSendMsg_RobedHP80_clbk = void (WINAPIV*)(struct CCharacter*, struct CCharacter*, uint16_t, CCharacterSendMsg_RobedHP80_ptr);
        using CCharacterSendMsg_StunInform82_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterSendMsg_StunInform82_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterSendMsg_StunInform82_ptr);
        using CCharacterSetNextGenAttTime84_ptr = void (WINAPIV*)(struct CCharacter*, unsigned int);
        using CCharacterSetNextGenAttTime84_clbk = void (WINAPIV*)(struct CCharacter*, unsigned int, CCharacterSetNextGenAttTime84_ptr);
        using CCharacterSetTarPos86_ptr = bool (WINAPIV*)(struct CCharacter*, float*, bool);
        using CCharacterSetTarPos86_clbk = bool (WINAPIV*)(struct CCharacter*, float*, bool, CCharacterSetTarPos86_ptr);
        using CCharacterStop88_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterStop88_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterStop88_ptr);
        using CCharacterUpdateSFCont90_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterUpdateSFCont90_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterUpdateSFCont90_ptr);
        using CCharacter_GetAreaEffectMember92_ptr = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, bool, int, float*, char*, struct CCharacter**);
        using CCharacter_GetAreaEffectMember92_clbk = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, bool, int, float*, char*, struct CCharacter**, CCharacter_GetAreaEffectMember92_ptr);
        using CCharacter_GetFlashEffectMember94_ptr = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, bool, int, int, struct CCharacter*, char*, struct CCharacter**);
        using CCharacter_GetFlashEffectMember94_clbk = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, bool, int, int, struct CCharacter*, char*, struct CCharacter**, CCharacter_GetFlashEffectMember94_ptr);
        using CCharacter_GetPartyEffectMember96_ptr = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, bool, struct CCharacter**);
        using CCharacter_GetPartyEffectMember96_clbk = int (WINAPIV*)(struct CCharacter*, struct CCharacter*, bool, struct CCharacter**, CCharacter_GetPartyEffectMember96_ptr);
        using CCharacter_set_sf_cont98_ptr = void (WINAPIV*)(struct CCharacter*, struct _sf_continous*, char, uint16_t, char, unsigned int, uint16_t, int);
        using CCharacter_set_sf_cont98_clbk = void (WINAPIV*)(struct CCharacter*, struct _sf_continous*, char, uint16_t, char, unsigned int, uint16_t, int, CCharacter_set_sf_cont98_ptr);
        
        using CCharacterdtor_CCharacter103_ptr = void (WINAPIV*)(struct CCharacter*);
        using CCharacterdtor_CCharacter103_clbk = void (WINAPIV*)(struct CCharacter*, CCharacterdtor_CCharacter103_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

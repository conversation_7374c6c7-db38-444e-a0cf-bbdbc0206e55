// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagFORMATETC.hpp>


START_ATF_NAMESPACE
    struct tagOLEUIPASTEENTRYW
    {
        tagFORMATETC fmtetc;
        const wchar_t *lpstrFormatName;
        const wchar_t *lpstrResultText;
        unsigned int dwFlags;
        unsigned int dwScratchSpace;
    };
END_ATF_NAMESPACE

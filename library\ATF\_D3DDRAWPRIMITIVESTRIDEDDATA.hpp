// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_D3DDP_PTRSTRIDE.hpp>


START_ATF_NAMESPACE
    struct _D3DDRAWPRIMITIVESTRIDEDDATA
    {
        _D3DDP_PTRSTRIDE position;
        _D3DDP_PTRSTRIDE normal;
        _D3DDP_PTRSTRIDE diffuse;
        _D3DDP_PTRSTRIDE specular;
        _D3DDP_PTRSTRIDE textureCoords[8];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct tagGCP_RESULTSW
    {
        unsigned int lStructSize;
        wchar_t *lpOutString;
        unsigned int *lpOrder;
        int *lpDx;
        int *lpCaretPos;
        char *lpClass;
        wchar_t *lpGlyphs;
        unsigned int nGlyphs;
        int nMaxFit;
    };
END_ATF_NAMESPACE

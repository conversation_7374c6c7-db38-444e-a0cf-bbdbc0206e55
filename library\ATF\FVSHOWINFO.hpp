// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <IUnknown.hpp>
#include <tagRECT.hpp>



START_ATF_NAMESPACE
    struct FVSHOWINFO
    {
        unsigned int cbSize;
        HWND__ *hwndOwner;
        int iShow;
        unsigned int dwFlags;
        tagRECT rect;
        IUnknown *punkRel;
        wchar_t strNewFile[260];
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagPANOSE
    {
        char bFamilyType;
        char bSerifStyle;
        char bWeight;
        char bProportion;
        char bContrast;
        char bStrokeVariation;
        char bArmStyle;
        char bLetterform;
        char bMidline;
        char bXHeight;
    };
END_ATF_NAMESPACE

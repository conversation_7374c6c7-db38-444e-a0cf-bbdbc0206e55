// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_COMBINEKEY.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _combine_ex_item_result_zocl
    {
        struct __item
        {
            _COMBINEKEY Key;
            unsigned int dwDur;
            unsigned int dwUpt;
        public:
            void Init();
            __item();
            void ctor___item();
        };
        struct  _Result_ItemList_Buff
        {
            char byItemListNum;
            __item RewardItemList[24];
        public:
            void Init();
            _Result_ItemList_Buff();
            void ctor__Result_ItemList_Buff();
        };
        char byErrCode;
        char byDlgType;
         unsigned int dwDalant;
         unsigned int dwCheckKey;
        char bySelectItemCount;
        _Result_ItemList_Buff ItemBuff;
        unsigned int dwResultEffectType;
        unsigned int dwResultEffectMsgCode;
    public:
        _combine_ex_item_result_zocl();
        void ctor__combine_ex_item_result_zocl();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_combine_ex_item_result_zocl, 308>(), "_combine_ex_item_result_zocl");
END_ATF_NAMESPACE

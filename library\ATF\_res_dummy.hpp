// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _res_dummy
    {
        _dummy_position *m_pDumPos;
        float m_fMinLocal[3][3];
        float m_fMaxLocal[3][3];
        char m_byGrade[3];
        unsigned int m_dwDelay[3][2];
        char m_byQualityGrade;
    public:
        int GetDelay(char bySector, bool bIsPcbang);
        char GetQualityGrade();
        bool SetDummy(struct _dummy_position* pDumPos, char byQualityGrade);
        void SetRangeGrade();
        _res_dummy();
        void ctor__res_dummy();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

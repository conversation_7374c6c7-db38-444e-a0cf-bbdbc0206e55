// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _open_return_gate_inform_zocl
    {
        unsigned __int16 wGateInx;
        unsigned int dwObjSerial;
        unsigned int dwOpenerSerial;
        char wszOpenerName[17];
        __int16 zPos[3];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

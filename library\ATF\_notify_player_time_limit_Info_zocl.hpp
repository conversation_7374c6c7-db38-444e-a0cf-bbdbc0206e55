// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _notify_player_time_limit_Info_zocl
    {
        unsigned int dwFatigue;
        unsigned __int16 wStatus;
        unsigned int dwRemainTime;
        unsigned int dwAccumPlayTime;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoneySupplyMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMoneySupplyMgrctor_CMoneySupplyMgr2_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*);
        using CMoneySupplyMgrctor_CMoneySupplyMgr2_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, CMoneySupplyMgrctor_CMoneySupplyMgr2_ptr);
        using CMoneySupplyMgrInitialize4_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*);
        using CMoneySupplyMgrInitialize4_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, CMoneySupplyMgrInitialize4_ptr);
        using CMoneySupplyMgrInstance6_ptr = struct CMoneySupplyMgr* (WINAPIV*)();
        using CMoneySupplyMgrInstance6_clbk = struct CMoneySupplyMgr* (WINAPIV*)(CMoneySupplyMgrInstance6_ptr);
        using CMoneySupplyMgrLoopMoneySupply8_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*);
        using CMoneySupplyMgrLoopMoneySupply8_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, CMoneySupplyMgrLoopMoneySupply8_ptr);
        using CMoneySupplyMgrSendMsg_MoneySupplyDataToWeb10_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*, struct _MONEY_SUPPLY_DATA*);
        using CMoneySupplyMgrSendMsg_MoneySupplyDataToWeb10_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, struct _MONEY_SUPPLY_DATA*, CMoneySupplyMgrSendMsg_MoneySupplyDataToWeb10_ptr);
        using CMoneySupplyMgrUpdateBuyData12_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, char*, unsigned int);
        using CMoneySupplyMgrUpdateBuyData12_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, char*, unsigned int, CMoneySupplyMgrUpdateBuyData12_ptr);
        using CMoneySupplyMgrUpdateBuyUnitData14_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*, int, unsigned int);
        using CMoneySupplyMgrUpdateBuyUnitData14_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, int, unsigned int, CMoneySupplyMgrUpdateBuyUnitData14_ptr);
        using CMoneySupplyMgrUpdateFeeMoneyData16_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, unsigned int);
        using CMoneySupplyMgrUpdateFeeMoneyData16_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, unsigned int, CMoneySupplyMgrUpdateFeeMoneyData16_ptr);
        using CMoneySupplyMgrUpdateGateRewardMoneyData18_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, char*, unsigned int);
        using CMoneySupplyMgrUpdateGateRewardMoneyData18_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, char*, unsigned int, CMoneySupplyMgrUpdateGateRewardMoneyData18_ptr);
        using CMoneySupplyMgrUpdateHonorGuildMoneyData20_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*, char, char, unsigned int);
        using CMoneySupplyMgrUpdateHonorGuildMoneyData20_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, char, char, unsigned int, CMoneySupplyMgrUpdateHonorGuildMoneyData20_ptr);
        using CMoneySupplyMgrUpdateQuestRewardMoneyData22_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, char*, unsigned int);
        using CMoneySupplyMgrUpdateQuestRewardMoneyData22_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, char*, unsigned int, CMoneySupplyMgrUpdateQuestRewardMoneyData22_ptr);
        using CMoneySupplyMgrUpdateSellData24_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, char*, unsigned int);
        using CMoneySupplyMgrUpdateSellData24_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, char, int, char*, unsigned int, CMoneySupplyMgrUpdateSellData24_ptr);
        using CMoneySupplyMgrUpdateUnitRepairingChargesData26_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*, int, unsigned int);
        using CMoneySupplyMgrUpdateUnitRepairingChargesData26_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, int, unsigned int, CMoneySupplyMgrUpdateUnitRepairingChargesData26_ptr);
        
        using CMoneySupplyMgrdtor_CMoneySupplyMgr31_ptr = void (WINAPIV*)(struct CMoneySupplyMgr*);
        using CMoneySupplyMgrdtor_CMoneySupplyMgr31_clbk = void (WINAPIV*)(struct CMoneySupplyMgr*, CMoneySupplyMgrdtor_CMoneySupplyMgr31_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

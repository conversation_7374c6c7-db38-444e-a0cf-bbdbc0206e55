// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _ADMINISTRATOR_POWER_POLICY
    {
        _SYSTEM_POWER_STATE MinSleep;
        _SYSTEM_POWER_STATE MaxSleep;
        unsigned int MinVideoTimeout;
        unsigned int MaxVideoTimeout;
        unsigned int MinSpindownTimeout;
        unsigned int MaxSpindownTimeout;
    };
END_ATF_NAMESPACE

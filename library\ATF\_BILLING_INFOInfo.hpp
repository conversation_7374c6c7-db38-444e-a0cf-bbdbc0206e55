// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_BILLING_INFO.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _BILLING_INFOIsPcBangType2_ptr = bool (WINAPIV*)(struct _BILLING_INFO*);
        using _BILLING_INFOIsPcBangType2_clbk = bool (WINAPIV*)(struct _BILLING_INFO*, _BILLING_INFOIsPcBangType2_ptr);
        
        using _BILLING_INFOctor__BILLING_INFO4_ptr = void (WINAPIV*)(struct _BILLING_INFO*);
        using _BILLING_INFOctor__BILLING_INFO4_clbk = void (WINAPIV*)(struct _BILLING_INFO*, _BILLING_INFOctor__BILLING_INFO4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

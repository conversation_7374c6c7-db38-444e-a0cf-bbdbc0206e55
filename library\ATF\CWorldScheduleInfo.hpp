// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CWorldSchedule.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CWorldSchedulector_CWorldSchedule2_ptr = void (WINAPIV*)(struct CWorldSchedule*);
        using CWorldSchedulector_CWorldSchedule2_clbk = void (WINAPIV*)(struct CWorldSchedule*, CWorldSchedulector_CWorldSchedule2_ptr);
        using CWorldScheduleCalcScheduleCursor4_ptr = int (WINAPIV*)(struct CWorldSchedule*, int, int);
        using CWorldScheduleCalcScheduleCursor4_clbk = int (WINAPIV*)(struct CWorldSchedule*, int, int, CWorldScheduleCalcScheduleCursor4_ptr);
        using CWorldScheduleChangeSchCursor6_ptr = void (WINAPIV*)(struct CWorldSchedule*, struct _WorldSchedule_fld*, int);
        using CWorldScheduleChangeSchCursor6_clbk = void (WINAPIV*)(struct CWorldSchedule*, struct _WorldSchedule_fld*, int, CWorldScheduleChangeSchCursor6_ptr);
        using CWorldScheduleCheckSch8_ptr = void (WINAPIV*)(struct CWorldSchedule*);
        using CWorldScheduleCheckSch8_clbk = void (WINAPIV*)(struct CWorldSchedule*, CWorldScheduleCheckSch8_ptr);
        using CWorldScheduleDataCheck10_ptr = bool (WINAPIV*)(struct CWorldSchedule*);
        using CWorldScheduleDataCheck10_clbk = bool (WINAPIV*)(struct CWorldSchedule*, CWorldScheduleDataCheck10_ptr);
        using CWorldScheduleInit12_ptr = bool (WINAPIV*)(struct CWorldSchedule*);
        using CWorldScheduleInit12_clbk = bool (WINAPIV*)(struct CWorldSchedule*, CWorldScheduleInit12_ptr);
        using CWorldScheduleLoop14_ptr = void (WINAPIV*)(struct CWorldSchedule*);
        using CWorldScheduleLoop14_clbk = void (WINAPIV*)(struct CWorldSchedule*, CWorldScheduleLoop14_ptr);
        using CWorldSchedulePassOneStep16_ptr = void (WINAPIV*)(struct CWorldSchedule*);
        using CWorldSchedulePassOneStep16_clbk = void (WINAPIV*)(struct CWorldSchedule*, CWorldSchedulePassOneStep16_ptr);
        
        using CWorldScheduledtor_CWorldSchedule18_ptr = void (WINAPIV*)(struct CWorldSchedule*);
        using CWorldScheduledtor_CWorldSchedule18_clbk = void (WINAPIV*)(struct CWorldSchedule*, CWorldScheduledtor_CWorldSchedule18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

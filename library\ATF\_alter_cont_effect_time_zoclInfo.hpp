// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_alter_cont_effect_time_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _alter_cont_effect_time_zoclctor__alter_cont_effect_time_zocl2_ptr = void (WINAPIV*)(struct _alter_cont_effect_time_zocl*);
        using _alter_cont_effect_time_zoclctor__alter_cont_effect_time_zocl2_clbk = void (WINAPIV*)(struct _alter_cont_effect_time_zocl*, _alter_cont_effect_time_zoclctor__alter_cont_effect_time_zocl2_ptr);
        using _alter_cont_effect_time_zoclsize4_ptr = int (WINAPIV*)(struct _alter_cont_effect_time_zocl*);
        using _alter_cont_effect_time_zoclsize4_clbk = int (WINAPIV*)(struct _alter_cont_effect_time_zocl*, _alter_cont_effect_time_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_post_send
    {
        struct __list
        {
            char byErr;
            unsigned int dwIndex;
            unsigned int dwPSSerial;
            unsigned int dwReceiverSerial;
            unsigned int dwSenderSerial;
            char wszSendName[17];
            char wszRecvName[17];
            char wszTitle[21];
            char wszContent[201];
            _INVENKEY key;
            unsigned __int64 dwDur;
            unsigned int dwUpt;
            unsigned __int64 lnUID;
            unsigned int dwGold;
        public:
            __list();
            void ctor___list();
        };
        unsigned int dwCount;
        __list List[15];
    public:
        _qry_case_post_send();
        void ctor__qry_case_post_send();
        bool pushdata(unsigned int dwIndex, char byErr, unsigned int dwReceiverSerial, unsigned int dwSenderSerial, char* wszSendName, char* wszRecvName, char* wszTitle, char* wszContent, struct _INVENKEY key, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, uint64_t lnUID);
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_post_send, 4688>(), "_qry_case_post_send");
END_ATF_NAMESPACE

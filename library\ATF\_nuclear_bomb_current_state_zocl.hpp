// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _nuclear_bomb_current_state_zocl
    {
        struct  __nuclear
        {
            char byRaceCode;
            char byUseClass;
            float zPos[3];
        };
        char nNum;
        __nuclear nuclear[9];
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_nuclear_bomb_current_state_zocl, 127>(), "_nuclear_bomb_current_state_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _dh_player_mgr
    {
        struct _pos
        {
            struct CMapData *pMap;
            unsigned __int16 wLayer;
            float fPos[3];
        public:
            void init();
            void set(struct CMapData* map, uint16_t layer, float* pos);
        };
        struct CPlayer *pOne;
        unsigned int dwSerial;
        _pos LastPos;
        int nEnterOrder;
    public:
        void Init();
        bool IsFill();
        _dh_player_mgr();
        void ctor__dh_player_mgr();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_dh_player_mgr, 48>(), "_dh_player_mgr");
END_ATF_NAMESPACE

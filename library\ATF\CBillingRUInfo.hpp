// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CBillingRU.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CBillingRUctor_CBillingRU2_ptr = void (WINAPIV*)(struct CBillingRU*);
        using CBillingRUctor_CBillingRU2_clbk = void (WINAPIV*)(struct CBillingRU*, CBillingRUctor_CBillingRU2_ptr);
        
        using CBillingRUdtor_CBillingRU7_ptr = void (WINAPIV*)(struct CBillingRU*);
        using CBillingRUdtor_CBillingRU7_clbk = void (WINAPIV*)(struct CBillingRU*, CBillingRUdtor_CBillingRU7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

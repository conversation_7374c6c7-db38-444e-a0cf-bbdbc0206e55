// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _MEMORY_BASIC_INFORMATION64
    {
        unsigned __int64 BaseAddress;
        unsigned __int64 AllocationBase;
        unsigned int AllocationProtect;
        unsigned int __alignment1;
        unsigned __int64 RegionSize;
        unsigned int State;
        unsigned int Protect;
        unsigned int Type;
        unsigned int __alignment2;
    };
END_ATF_NAMESPACE

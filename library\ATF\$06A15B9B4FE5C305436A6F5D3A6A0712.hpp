// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DDCOLORKEY.hpp>


START_ATF_NAMESPACE
    union $06A15B9B4FE5C305436A6F5D3A6A0712
    {
        _DDCOLORKEY ddckCKDestOverlay;
        unsigned int dwEmptyFaceColor;
    };    
    static_assert(ATF::checkSize<$06A15B9B4FE5C305436A6F5D3A6A0712, 8>(), "$06A15B9B4FE5C305436A6F5D3A6A0712");
END_ATF_NAMESPACE

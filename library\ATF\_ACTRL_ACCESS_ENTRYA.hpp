// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_TRUSTEE_A.hpp>


START_ATF_NAMESPACE
    struct _ACTRL_ACCESS_ENTRYA
    {
        _TRUSTEE_A Trustee;
        unsigned int fAccessFlags;
        unsigned int Access;
        unsigned int ProvSpecificAccess;
        unsigned int Inheritance;
        char *lpInheritProperty;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagEMR.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct tagEMRSETICMPROFILE
    {
        tagEMR emr;
        unsigned int dwFlags;
        unsigned int cbName;
        unsigned int cbData;
        char Data[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

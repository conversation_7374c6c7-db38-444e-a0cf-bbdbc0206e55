// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct CMoveMapLimitInfoVtbl
    {
        bool (WINAPIV *Init)(struct CMoveMapLimitInfo *_this);
        char (WINAPIV *Request)(struct CMoveMapLimitInfo *_this, int, int, char *, struct CMoveMapLimitRightInfo *);
        void (WINAPIV *Load)(struct CMoveMapLimitInfo *_this, struct CPlayer *, struct CMoveMapLimitRightInfo *);
        void (WINAPIV *LogIn)(struct CMoveMapLimitInfo *_this, struct CPlayer *, struct CMoveMapLimitRightInfo *);
        void (WINAPIV *LogOut)(struct CMoveMapLimitInfo *_this, struct CPlayer *, struct CMoveMapLimitRightInfo *);
        void (WINAPIV *Loop)(struct CMoveMapLimitInfo *_this);
    };
END_ATF_NAMESPACE

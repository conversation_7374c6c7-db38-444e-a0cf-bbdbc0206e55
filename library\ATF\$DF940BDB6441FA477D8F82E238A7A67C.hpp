// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <CLongBinary.hpp>
#include <tagTIMESTAMP_STRUCT.hpp>


START_ATF_NAMESPACE
    union $DF940BDB6441FA477D8F82E238A7A67C
    {
        int m_boolVal;
        char m_chVal;
        __int16 m_iVal;
        int m_lVal;
        float m_fltVal;
        long double m_dblVal;
        tagTIMESTAMP_STRUCT *m_pdate;
        ATL::CStringT<char> *m_pstring;
        CLongBinary *m_pbinary;
        ATL::CStringT<char> *m_pstringA;
        ATL::CStringT<wchar_t,StrTraitMFC_DLL<wchar_t,ATL::ChTraitsCRT<wchar_t> > > *m_pstringW;
    };
END_ATF_NAMESPACE

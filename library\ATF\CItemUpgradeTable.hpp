// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CItemUpgradeTableVtbl.hpp>
#include <CRecordData.hpp>
#include <_ItemUpgrade_fld.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CItemUpgradeTable
    {
        CItemUpgradeTableVtbl *vfptr;
        CRecordData m_tblItemUpgrade;
        int m_nResNum;
        unsigned __int16 *m_pwResIndex;
    public:
        CItemUpgradeTable();
        void ctor_CItemUpgradeTable();
        struct _ItemUpgrade_fld* GetRecord(unsigned int dwIndex);
        struct _ItemUpgrade_fld* GetRecordFromRes(unsigned int dwResIndex);
        int GetSize();
        bool Indexing(struct CRecordData* pResRec, char* pszErrMsg);
        bool ReadRecord(char* szFile, struct CRecordData* pResRec, char* pszErrMsg);
        ~CItemUpgradeTable();
        void dtor_CItemUpgradeTable();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<CItemUpgradeTable, 200>(), "CItemUpgradeTable");
END_ATF_NAMESPACE

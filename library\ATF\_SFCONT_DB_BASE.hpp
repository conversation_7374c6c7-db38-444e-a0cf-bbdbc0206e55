// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _SFCONT_DB_BASE
    {
        struct _LIST
        {
            unsigned int dwKey;
        public:
            char GetEffectCode();
            uint16_t GetEffectIndex();
            uint16_t GetLeftTime();
            char GetLv();
            char GetOrder();
            void Init();
            bool IsFilled();
            void SetKey(char pl_byOrder, char pl_byEffectCode, uint16_t pl_wEffectIndex, char pl_byLv, uint16_t pl_wLeftTime);
            void SetLeftTime(uint16_t pl_wLeftTime);
            void SetOrder(char pl_byOrder);
        };
        _LIST m_List[2][8];
    public:
        void Init();
        _SFCONT_DB_BASE();
        void ctor__SFCONT_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_SFCONT_DB_BASE, 64>(), "_SFCONT_DB_BASE");
END_ATF_NAMESPACE

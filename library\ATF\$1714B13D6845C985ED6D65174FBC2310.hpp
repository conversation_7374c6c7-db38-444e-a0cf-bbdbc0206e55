// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$0D82EA2D0DF06110CA4E8168362A78E2.hpp>
#include <$CCA3F194DBE49AED13B34531CC14A2D6.hpp>
#include <$CD8FF0FB5A5CE55A7A7E3BEAF20EAAD0.hpp>
#include <$DCDE890E7FC72610D03B0EE8287C9EA0.hpp>


START_ATF_NAMESPACE
    union $1714B13D6845C985ED6D65174FBC2310
    {
        char rawData[1];
        $0D82EA2D0DF06110CA4E8168362A78E2 interfaceData;
        $CCA3F194DBE49AED13B34531CC14A2D6 locationData;
        $DCDE890E7FC72610D03B0EE8287C9EA0 connectivity;
        $CD8FF0FB5A5CE55A7A7E3BEAF20EAAD0 ICS;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $ADBB94A21537A8461C6168A5B871A740
    {
        BYTE gap0[8];
        unsigned __int64 *pullVal;
    };    
    static_assert(ATF::checkSize<$ADBB94A21537A8461C6168A5B871A740, 16>(), "$ADBB94A21537A8461C6168A5B871A740");
END_ATF_NAMESPACE

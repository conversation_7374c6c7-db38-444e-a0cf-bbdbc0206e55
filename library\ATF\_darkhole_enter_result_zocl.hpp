// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _darkhole_enter_result_zocl
    {
        char byRetCode;
         unsigned int dwHoleSerial;
        char byMapCode;
        __int16 zPos[3];
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_darkhole_enter_result_zocl, 12>(), "_darkhole_enter_result_zocl");
END_ATF_NAMESPACE

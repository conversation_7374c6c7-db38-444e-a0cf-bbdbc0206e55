// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagNMCUSTOMDRAWINFO.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagNMTVCUSTOMDRAW
    {
        tagNMCUSTOMDRAWINFO nmcd;
        unsigned int clrText;
        unsigned int clrTextBk;
        int iLevel;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

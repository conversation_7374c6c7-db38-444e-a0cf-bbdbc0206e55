// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTypedSimpleList.hpp>
#include <_RTL_CRITICAL_SECTION.hpp>


START_ATF_NAMESPACE
    struct CThreadSlotData
    {
        unsigned int m_tlsIndex;
        int m_nAlloc;
        int m_nRover;
        int m_nMax;
        struct CSlotData *m_pSlotData;
        CTypedSimpleList<CThreadData *> m_list;
        _RTL_CRITICAL_SECTION m_sect;
    };
END_ATF_NAMESPACE

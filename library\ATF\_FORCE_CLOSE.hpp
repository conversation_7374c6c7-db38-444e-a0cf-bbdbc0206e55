// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNetIndexList.hpp>
#include <CNetTimer.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _FORCE_CLOSE
    {
        struct __FD_NODE
        {
            unsigned int dwObjIndex;
            unsigned int dwObjSerial;
            unsigned int dwEventCreateTime;
        };
        unsigned int m_dwNodeNum;
        __FD_NODE *m_pFDData;
        CNetTimer m_tmFD;
        CNetIndexList m_listFD;
        CNetIndexList m_listFDEmpty;
    public:
        bool Init(unsigned int dwNodeNum);
        bool PushNode(unsigned int dwIndex, unsigned int dwSerial);
        _FORCE_CLOSE();
        void ctor__FORCE_CLOSE();
        ~_FORCE_CLOSE();
        void dtor__FORCE_CLOSE();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_FORCE_CLOSE, 352>(), "_FORCE_CLOSE");
END_ATF_NAMESPACE

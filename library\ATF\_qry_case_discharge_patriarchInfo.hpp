// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_discharge_patriarch.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_discharge_patriarchctor__qry_case_discharge_patriarch2_ptr = void (WINAPIV*)(struct _qry_case_discharge_patriarch*, char, unsigned int, unsigned int);
        using _qry_case_discharge_patriarchctor__qry_case_discharge_patriarch2_clbk = void (WINAPIV*)(struct _qry_case_discharge_patriarch*, char, unsigned int, unsigned int, _qry_case_discharge_patriarchctor__qry_case_discharge_patriarch2_ptr);
        using _qry_case_discharge_patriarchsize4_ptr = int (WINAPIV*)(struct _qry_case_discharge_patriarch*);
        using _qry_case_discharge_patriarchsize4_clbk = int (WINAPIV*)(struct _qry_case_discharge_patriarch*, _qry_case_discharge_patriarchsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

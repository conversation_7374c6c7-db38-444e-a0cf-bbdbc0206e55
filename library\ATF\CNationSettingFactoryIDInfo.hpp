// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CNationSettingFactoryID.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CNationSettingFactoryIDctor_CNationSettingFactoryID2_ptr = void (WINAPIV*)(struct CNationSettingFactoryID*);
        using CNationSettingFactoryIDctor_CNationSettingFactoryID2_clbk = void (WINAPIV*)(struct CNationSettingFactoryID*, CNationSettingFactoryIDctor_CNationSettingFactoryID2_ptr);
        using CNationSettingFactoryIDCreate4_ptr = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryID*, int, char*, bool);
        using CNationSettingFactoryIDCreate4_clbk = struct CNationSettingData* (WINAPIV*)(struct CNationSettingFactoryID*, int, char*, bool, CNationSettingFactoryIDCreate4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

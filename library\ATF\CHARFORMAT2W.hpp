// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_charformatw.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  CHARFORMAT2W : _charformatw
    {
        unsigned __int16 wWeight;
        __int16 sSpacing;
        unsigned int crBackColor;
        unsigned int lcid;
        unsigned int dwReserved;
        __int16 sStyle;
        unsigned __int16 wKerning;
        char bUnderlineType;
        char bAnimation;
        char bRevAuthor;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

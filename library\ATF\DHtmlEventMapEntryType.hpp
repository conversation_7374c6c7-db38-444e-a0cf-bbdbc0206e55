// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum DHtmlEventMapEntryType
    {
      DHTMLEVENTMAPENTRY_NAME = 0x0,
      DHTML<PERSON>VENTMAPENTRY_CLASS = 0x1,
      DHTMLEVENTMAPENTRY_TAG = 0x2,
      DHTMLEVENTMAPENTRY_ELEMENT = 0x3,
      DHTMLEVENTMAPENTRY_CONTROL = 0x4,
      DHTMLEVENTMAPENTRY_END = 0x5,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAnimus.hpp>
#include <CAttack.hpp>
#include <CCharacter.hpp>
#include <CCouponMgr.hpp>
#include <CDarkHoleChannel.hpp>
#include <CEquipItemSFAgent.hpp>
#include <CExtPotionBuf.hpp>
#include <CGuardTower.hpp>
#include <CGuild.hpp>
#include <CItemBox.hpp>
#include <CItemStore.hpp>
#include <CMapData.hpp>
#include <CMerchant.hpp>
#include <CMonster.hpp>
#include <CMyTimer.hpp>
#include <CParkingUnit.hpp>
#include <CPartyModeKillMonsterExpNotify.hpp>
#include <CPartyPlayer.hpp>
#include <CPlayerAttack.hpp>
#include <CPlayerDB.hpp>
#include <CPotionParam.hpp>
#include <CPvpCashPoint.hpp>
#include <CPvpOrderView.hpp>
#include <CPvpPointLimiter.hpp>
#include <CQuestMgr.hpp>
#include <CRealMoveRequestDelayChecker.hpp>
#include <CSetItemEffect.hpp>
#include <CTrap.hpp>
#include <CUserDB.hpp>
#include <ItemCombineMgr.hpp>
#include <MiningTicket.hpp>
#include <PVP_ALTER_TYPE.hpp>
#include <PVP_MONEY_ALTER_TYPE.hpp>
#include <QUEST_HAPPEN.hpp>
#include <_100_per_random_table.hpp>
#include <_ATTACK_DELAY_CHECKER.hpp>
#include <_BUDDY_LIST.hpp>
#include <_BulletItem_fld.hpp>
#include <_CHRID.hpp>
#include <_CLID.hpp>
#include <_CRYMSG_LIST.hpp>
#include <_DB_QRY_SYN_DATA.hpp>
#include <_DTRADE_PARAM.hpp>
#include <_ITEM_EFFECT.hpp>
#include <_MASTERY_PARAM.hpp>
#include <_MEM_PAST_WHISPER.hpp>
#include <_NPCQuestIndexTempData.hpp>
#include <_NameChangeBuddyInfo.hpp>
#include <_QUEST_DB_BASE.hpp>
#include <_Quest_fld.hpp>
#include <_RENAME_POTION_USE_INFO.hpp>
#include <_ResourceItem_fld.hpp>
#include <_SFCONT_DB_BASE.hpp>
#include <_STORAGE_LIST.hpp>
#include <_STORAGE_POS_INDIV.hpp>
#include <_SYSTEMTIME.hpp>
#include <_TOWER_PARAM.hpp>
#include <_TRAP_PARAM.hpp>
#include <_UNIT_DB_BASE.hpp>
#include <_UnitBullet_fld.hpp>
#include <_UnitPart_fld.hpp>
#include <_WEAPON_PARAM.hpp>
#include <_alter_item_slot_request_clzo.hpp>
#include <_alter_link_slot_request_clzo.hpp>
#include <_attack_param.hpp>
#include <_base_fld.hpp>
#include <_be_damaged_char.hpp>
#include <_buy_offer.hpp>
#include <_buy_store_request_clzo.hpp>
#include <_class_fld.hpp>
#include <_combine_ex_item_accept_request_clzo.hpp>
#include <_combine_ex_item_accept_result_zocl.hpp>
#include <_combine_ex_item_request_clzo.hpp>
#include <_combine_ex_item_result_zocl.hpp>
#include <_consume_item_list.hpp>
#include <_dh_reward_sub_setup.hpp>
#include <_dummy_position.hpp>
#include <_force_fld.hpp>
#include <_guild_honor_set_request_clzo.hpp>
#include <_guildroom_enter_request_clzo.hpp>
#include <_guildroom_out_request_clzo.hpp>
#include <_guildroom_rent_request_clzo.hpp>
#include <_guildroom_resttime_request_clzo.hpp>
#include <_happen_event_cont.hpp>
#include <_limit_amount_info.hpp>
#include <_make_tower_request_clzo.hpp>
#include <_object_id.hpp>
#include <_other_shape_all_zocl.hpp>
#include <_other_shape_part_zocl.hpp>
#include <_sell_store_request_clzo.hpp>
#include <_sf_continous.hpp>
#include <_skill_fld.hpp>
#include <_talik_crystal_exchange_clzo.hpp>
#include <_tuning_data.hpp>
#include <_unit_bullet_param.hpp>
#include <_unit_pack_fill_request_clzo.hpp>
#include <_w_name.hpp>
#include <si_interpret.hpp>
#include <$621D0DDFB6A4DE55506A65C7CCDC95CE.hpp>
#include <_target_monster_contsf_allinform_zocl.hpp>
#include <_target_player_damage_contsf_allinform_zocl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CPlayer : CCharacter
    {
        enum CHAOS_MODE_STATE
        {
            chaos_mode_remain_time_0 = 0x0,
            chaos_mode_remain_time_10per_up = 0x1,
            chaos_mode_remain_time_10per_down = 0x2,
        };
        enum PVP_LOSE
        {
            race_battle = 0x0,
            no_connect_long_time = 0x1,
        };
        enum HAVE_EFFECT_TIME_TYPE
        {
            time_check = 0x0,
            insert_time = 0x1,
            delete_time = 0x2,
        };
        union CashChangeStateFlag
        {
            $621D0DDFB6A4DE55506A65C7CCDC95CE __s0;
            char m_byStateFlag;
        public:
            CashChangeStateFlag(int cashrename);
            void ctor_CashChangeStateFlag(int cashrename);
        };
        struct __target
        {
            struct CGameObject *pObject;
            char byKind;
            char byID;
            unsigned int dwSerial;
            unsigned __int16 wHPRate;
            _target_monster_contsf_allinform_zocl m_PrevTargetMonsterContInfo;
            _target_player_damage_contsf_allinform_zocl m_PrevTargetPlayerDamageContInfo;
        public:
            __target();
            void ctor___target();
            void init();
        };
        bool m_bLoad;
        bool m_bOper;
        bool m_bPostLoad;
        bool m_bFullMode;
        char m_byUserDgr;
        char m_bySubDgr;
        bool m_bFirstStart;
        bool m_bOutOfMap;
        char m_byMoveDirect;
        char m_byPlusKey;
        unsigned __int16 m_wXorKey;
        unsigned int m_dwMoveCount;
        unsigned int m_dwTargetCount;
        unsigned int m_dwAttackCount;
        bool m_bBaseDownload;
        bool m_bInvenDownload;
        bool m_bForceDownload;
        bool m_bCumDownload;
        bool m_bQuestDownload;
        bool m_bSpecialDownload;
        bool m_bLinkBoardDownload;
        bool m_bAMPInvenDownload;
        bool m_bGuildListDownload;
        bool m_bGuildDownload;
        bool m_bBuddyListDownload;
        bool m_bBlockParty;
        bool m_bBlockWhisper;
        bool m_bBlockTrade;
        bool m_bCreateComplete;
        unsigned int m_dwSelfDestructionTime;
        float m_fSelfDestructionDamage;
        bool m_bTakeGravityStone;
        bool m_bBlockGuildBattleMsg;
        bool m_bInGuildBattle;
        bool m_bNotifyPosition;
        char m_byGuildBattleColorInx;
        bool m_bTakeSoccerBall;
        _STORAGE_LIST::_db_con *m_pSoccerItem;
        struct CUserDB *m_pUserDB;
        struct CPartyPlayer *m_pPartyMgr;
        CPlayerDB m_Param;
        _CLID m_id;
        char m_byMoveType;
        char m_byModeType;
        char m_byStandType;
        CRealMoveRequestDelayChecker m_kMoveDelayChecker;
        _WEAPON_PARAM m_pmWpn;
        _DTRADE_PARAM m_pmTrd;
        _MASTERY_PARAM m_pmMst;
        _TOWER_PARAM m_pmTwr;
        _TRAP_PARAM m_pmTrp;
        _BUDDY_LIST m_pmBuddy;
        CQuestMgr m_QuestMgr;
        ItemCombineMgr m_ItemCombineMgr;
        char m_byMapInModeBuffer;
        int m_nVoteSerial;
        unsigned int m_dwLastCheckRegionTime;
        unsigned __int16 m_wRegionMapIndex;
        unsigned __int16 m_wRegionIndex;
        char m_byHSKQuestCode;
        MiningTicket m_MinigTicket;
        int m_nHSKPvpPoint;
        unsigned __int16 m_wKillPoint;
        char m_byHSKTime;
        unsigned __int16 m_wDiePoint;
        char m_byCristalBattleDBInfo;
        CSetItemEffect m_clsSetItem;
        struct CDarkHoleChannel *m_pDHChannel;
        float m_fTalik_DefencePoint;
        float m_fTalik_AvoidPoint;
        bool m_bCheat_100SuccMake;
        bool m_bCheat_makeitem_no_use_matrial;
        bool m_bCheat_Matchless;
        bool m_bFreeRecallWaitTime;
        bool m_bFreeSFByClass;
        bool m_bLootFree;
        bool m_bNeverDie;
        int m_nMaxAttackPnt;
        bool m_bSpyGM;
        int m_nAnimusAttackPnt;
        int m_nTrapMaxAttackPnt;
        char m_byDamagePart;
        bool m_bRecvMapChat;
        bool m_bRecvAllChat;
        CEquipItemSFAgent EquipItemSFAgent;
        _CRYMSG_LIST m_pmCryMsg;
        bool m_bSnowMan;
        char m_byStoneMapMoveInfo;
        unsigned int m_dwPatriarchAppointTime;
        char m_byPatriarchAppointPropose;
        bool m_bAfterEffect;
        bool m_bSFDelayNotCheck;
        _RENAME_POTION_USE_INFO m_ReNamePotionUseInfo;
        CashChangeStateFlag m_CashChangeStateFlag;
        _NPCQuestIndexTempData m_NPCQuestIndexTempData;
        unsigned __int16 m_wVisualVer;
        int m_nLastBeatenPart;
        unsigned __int64 m_dwLastState;
        unsigned int m_dwExpRate;
        int m_nAddDfnMstByClass;
        int m_nAddPointByClass[4];
        int m_nMaxPoint[4];
        int m_nTolValue[4];
        int m_nMaxDP;
        __int16 m_zLastTol[4];
        float m_fEquipSpeed;
        int m_nOldPoint[4];
        int m_nOldMaxDP;
        float m_fSendTarPos[2];
        char m_byLastDirect;
        float m_fLastRecvPos[3];
        char m_byLastRecvMapIndex;
        unsigned int m_dwLastTakeItemTime;
        int m_nCheckMovePacket;
        bool m_bCheckMovePacket;
        char m_byDefMatCount;
        _100_per_random_table m_MakeRandTable;
        struct CMapData *m_pBindMapData;
        struct _dummy_position *m_pBindDummyData;
        unsigned int m_dwNextTimeDungeonDie;
        unsigned int m_dwLastTimeCheckUnitViewOver;
        unsigned int m_dwUnitViewOverTime;
        struct _UNIT_DB_BASE::_LIST *m_pUsingUnit;
        struct CParkingUnit *m_pParkingUnit;
        char m_byUsingWeaponPart;
        int m_nUnitDefFc;
        _STORAGE_LIST::_db_con *m_pSiegeItem;
        bool m_bIsSiegeActing;
        CMyTimer m_tmrSiegeTime;
        _STORAGE_LIST::_db_con *m_pRecalledAnimusItem;
        struct CAnimus *m_pRecalledAnimusChar;
        unsigned int m_dwLastRecallTime;
        char m_byNextRecallReturn;
        unsigned __int16 m_wTimeFreeRecallSerial;
        CMyTimer m_tmrIntervalSec;
        unsigned int m_dwLastSetPointTime;
        CMyTimer m_tmrBilling;
        float m_fBeforeDungeonPos[3];
        struct CMapData *m_pBeforeDungeonMap;
        _MEM_PAST_WHISPER m_PastWhiper[10];
        unsigned int m_dwContItemEffEndTime;
        CPotionParam m_PotionParam;
        CExtPotionBuf m_PotionBufUse;
        int m_bCntEnable;
        _SYSTEMTIME m_tmLoginTime;
        _SYSTEMTIME m_tmCalcTime;
        CMyTimer m_tmrAccumPlayingTime;
        bool m_bUpCheckEquipEffect;
        bool m_bDownCheckEquipEffect;
        char m_byEffectEquipCode[15];
        char m_byPosRaceTown;
        struct CMapData *m_pBeforeTownCheckMap;
        float m_fBeforeTownCheckPos[2];
        __target m_TargetObject;
        __target m_GroupTargetObject[3];
        CMyTimer m_tmrGroupTargeting;
        unsigned __int16 m_wPointRate_PartySend[4];
        bool m_bMineMode;
        unsigned int m_dwMineNextTime;
        unsigned __int16 m_wBatterySerialTmp;
        char m_bySelectOreIndex;
        char m_byDelaySec;
        __int16 m_zMinePos[2];
        _ATTACK_DELAY_CHECKER m_AttDelayChker;
        float m_fUnitPv_AttFc;
        float m_fUnitPv_DefFc;
        float m_fUnitPv_RepPr;
        CPvpPointLimiter m_kPvpPointLimiter;
        int m_nChaosMode;
        unsigned int m_dwChaosModeTime10Per;
        unsigned int m_dwChaosModeEndTime;
        int m_nCashAmount;
        float m_fGroupMapPoint[3][2];
        char m_byGroupMapPointMapCode[3];
        unsigned __int16 m_wGroupMapPointLayerIndex[3];
        unsigned int m_dwLastGroupMapPointTime[3];
        CPvpOrderView m_kPvpOrderView;
        char m_byBattleMode;
        unsigned int m_dwBattleTime;
        CMyTimer m_tmrAuraSkill;
        CPvpCashPoint m_kPvpCashPoint;
        CCouponMgr m_kPcBangCoupon;
        CMyTimer m_tmrEffectStartTime;
        CMyTimer m_tmrEffectEndTime;
        char m_byBattleTournamentGrade;
        _NameChangeBuddyInfo m_NameChangeBuddyInfo;
        unsigned int m_dwPcBangGiveItemListIndex;
        MiningTicket::_AuthKeyTicket m_dwRaceBuffClearKey;
        CMyTimer m_tmrPremiumPVPInform;
        char m_szItemHistoryFileName[64];
        char m_szLvHistoryFileName[64];
        unsigned int m_dwUMWHLastTime;
        _other_shape_all_zocl m_bufShapeAll;
        _other_shape_part_zocl m_bufSpapePart;
    public:
        void AddDalant(unsigned int dwPush, bool bApply);
        void AddGold(unsigned int dwPush, bool bApply);
        void AlterDalant(long double dDalant);
        void AlterExp(long double dAlterExp, bool bReward, bool bUseExpRecoverItem, bool bUseExpAdditionItem);
        void AlterExp_Animus(int64_t nAlterExp);
        void AlterExp_Potion(long double dAlterExp);
        void AlterFP_Animus(int nNewFP);
        void AlterGold(long double dGold);
        void AlterHP_Animus(int nNewHP);
        void AlterMaxLevel(char byMaxLevel);
        void AlterMode_Animus(char byMode);
        void AlterPvPCashBag(long double dAlter, PVP_MONEY_ALTER_TYPE IOCode);
        void AlterPvPPoint(long double dAlter, PVP_ALTER_TYPE AlterType, unsigned int dwDstSerial);
        void AlterPvpPointLeak(long double dAlter);
        void AlterSec();
        bool ApplyEquipItemEffect(int iItemEffectCode, bool bEquip);
        void ApplySetItemEffect(struct si_interpret* pSI, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum, bool bSetEffect);
        int AttackableHeight();
        void AutoCharge_Booster();
        void AutoRecover();
        void AutoRecover_Animus();
        void Billing_Logout();
        void BreakCloakBooster();
        CPlayer();
        void ctor_CPlayer();
        long double CalPvpCashPoint(int nDstLv, int nSrcLv, char* pSrcClass);
        void CalPvpTempCash(struct CPlayer* pDier, char byKillerObjID);
        void CalcAddPointByClass();
        uint16_t CalcCurFPRate();
        uint16_t CalcCurHPRate();
        uint16_t CalcCurSPRate();
        float CalcDPRate();
        void CalcDefTol();
        int CalcEquipAttackDelay();
        void CalcEquipMaxDP(bool bInit);
        void CalcEquipSpeed();
        void CalcExp(struct CCharacter* pDst, int nDam, struct CPartyModeKillMonsterExpNotify* kPartyExpNotify);
        void CalcPvP(struct CPlayer* pDier, char byKillerObjID);
        void CheckAlterMaxPoint();
        void CheckBattleMode();
        void CheckGroupMapPoint();
        void CheckGroupTargeting();
        void CheckMentalTakeAndUpdateLastMetalTicket(char* strItemCode);
        void CheckNameChange();
        void CheckPosInTown();
        void CheckPos_Region();
        void CheckUnitCutTime();
        void Cheet_BufEffectEnd();
        void ClearGravityStone();
        bool Corpse(struct CCharacter* pAtter);
        bool Create();
        void CreateComplete();
        void DTradeInit();
        bool DecHalfSFContDam(float fEffVal);
        void DelPostData(unsigned int dwIndex);
        void DeleteCouponItem(struct _STORAGE_POS_INDIV* CouponItem, int n);
        bool DeleteUseConsumeItem(_STORAGE_LIST::_db_con** ppConsumeItems, int* pnConsume, bool* pbOverLap);
        _STORAGE_LIST::_db_con* Emb_AddStorage(char byStorageCode, struct _STORAGE_LIST::_storage_con* pCon, bool bEquipChange, bool bAdd);
        unsigned int Emb_AlterDurPoint(char byStorageCode, char byStorageIndex, int nAlter, bool bUpdate, bool bSend);
        void Emb_AlterStat(char byMasteryClass, char byIndex, unsigned int dwAlter, char byReason, char* strErrorCodePos, bool bPcbangPrimiumFavorReward);
        void Emb_AlterStat_F(char byMasteryClass, char byIndex, float fAlter, char byReason);
        bool Emb_CheckActForQuest(int nActCode, char* pszReqCode, uint16_t wAddCount, bool bParty);
        void Emb_CheckActForQuestParty(int nActCode, char* pszReqCode, uint16_t wAddCount);
        void Emb_CompleteQuest(char byQuestDBSlot, char byRewardItemIndex, char byLinkQuestIndex);
        bool Emb_CreateNPCQuest(char* pszEventCode, unsigned int dwNPCQuestIndex);
        bool Emb_CreateQuestEvent(QUEST_HAPPEN HappenType, char* pszEventCode);
        bool Emb_DelStorage(char byStorageCode, char byStorageIndex, bool bEquipChange, bool bDelete, char* strErrorCodePos);
        void Emb_EquipLink();
        void Emb_ItemUpgrade(char byUpgradeType, char byStorageCode, char byStorageIndex, unsigned int dwGradeInfo);
        void Emb_RidindUnit(bool bRiding, struct CParkingUnit* pCreateUnit);
        bool Emb_StartQuest(char bySelectQuest, struct _happen_event_cont* pHappenEvent);
        int Emb_UpdateStat(unsigned int dwStatIndex, unsigned int dwNewData, unsigned int dwOldData);
        void ExitUpdateDataToWorld();
        void ExtractStringToTime(unsigned int dwTemp, struct _SYSTEMTIME* tm);
        struct CPlayer* FindFarChatPlayerWithTemp(char* pwszName);
        bool FixTargetWhile(struct CCharacter* pkTarget, unsigned int dwMiliSecond);
        void ForcePullUnit(bool bLogout);
        float GetAddSpeed();
        struct _sf_continous* GetAfterEffect();
        int GetAttackDP();
        int GetAttackLevel();
        float GetAttackRange();
        int GetAvoidRate();
        int GetBillingType();
        struct _dummy_position* GetBindDummy();
        struct CMapData* GetBindMap(float* pfPos, bool bIgnoreMapClass);
        struct CMapData* GetBindMapData();
        int GetCashAmount();
        int GetDP();
        int GetDamageDP(int nAttackPart);
        int GetDamageLevel(int nAttackPart);
        int GetDefFC(int nAttactPart, struct CCharacter* pAttChar, int* pnConvertPart);
        float GetDefFacing(int nPart);
        float GetDefGap(int nPart);
        int GetDefSkill(bool bBackAttackDamage);
        char GetEffectEquipCode(char byStorageCode, char bySlotIndex);
        int GetFP();
        int GetFireTol();
        int GetGauge(int nParamCode);
        int GetGenAttackProb(struct CCharacter* pDst, int nPart, bool bBackAttack);
        struct __target* GetGroupTarget(char byGroupType);
        int GetHP();
        unsigned int GetInitClassCost();
        int GetLevel();
        int GetMasteryCumAfterAttack(int nDstLv);
        int GetMaxDP();
        int GetMaxFP();
        int GetMaxHP();
        int GetMaxSP();
        unsigned int GetMoney(char byMoneyCode);
        float GetMoveSpeed();
        char* GetObjName();
        int GetObjRace();
        float GetPartyExpDistributionRate(int iPartyMemberLevel, int iMaxLevel, int i2ndLevel);
        struct CPvpOrderView* GetPvpOrderView();
        long double GetPvpPointLeak();
        struct CPvpPointLimiter* GetPvpPointLimiter(struct CPvpPointLimiter* result);
        struct CAnimus* GetRecallAnimus();
        int GetRewardItems_DarkDungeon(struct _dh_reward_sub_setup* pSetup, _STORAGE_LIST::_db_con* pItems, int bRealBoss);
        int GetSP();
        int GetSoilTol();
        uint64_t GetStateFlag();
        struct CGameObject* GetTargetObj();
        bool GetUseConsumeItem(struct _consume_item_list* pConsumeList, uint16_t* pItemSerials, _STORAGE_LIST::_db_con** ppConsumeItems, int* pnConsume, bool* pbOverLap);
        int GetVisualVer();
        int GetWaterTol();
        float GetWeaponAdjust();
        int GetWeaponClass();
        int GetWeaponRange();
        float GetWidth();
        int GetWindTol();
        static void Guild_Buy_Emblem_Complete(struct _DB_QRY_SYN_DATA* pData);
        static void Guild_Disjoint_Complete(struct _DB_QRY_SYN_DATA* pData);
        static void Guild_Force_Leave_Complete(struct _DB_QRY_SYN_DATA* pData);
        static void Guild_Insert_Complete(struct _DB_QRY_SYN_DATA* pData);
        static void Guild_Join_Accept_Complete(struct _DB_QRY_SYN_DATA* pData);
        static void Guild_Pop_Money_Complete(struct _DB_QRY_SYN_DATA* pData);
        static void Guild_Push_Money_Complete(struct _DB_QRY_SYN_DATA* pData);
        static void Guild_Self_Leave_Complete(struct _DB_QRY_SYN_DATA* pData);
        static void Guild_Update_GuildMater_Complete(struct _DB_QRY_SYN_DATA* pData);
        void HSKQuestEnd_Att(char byDestroyStoneRaceCode, struct CPlayer* pDestroyer);
        void HideNameEffect(bool bAdd);
        void IncCriEffKillPoint();
        void IncCriEffPvPCashBag(long double dAlter);
        void IncPvPPoint(long double dAlter, PVP_ALTER_TYPE AlterType, unsigned int dwDstSerial);
        bool Init(struct _object_id* pID);
        bool IntoMap(char byMapInMode);
        bool IsActingSiegeMode();
        bool IsApplyPcbangPrimium();
        bool IsBeAttackedAble(bool bFirst);
        bool IsBeDamagedAble(struct CCharacter* pAtter);
        _STORAGE_LIST::_db_con* IsBulletValidity(uint16_t wBulletSerial);
        bool IsChaosMode();
        _STORAGE_LIST::_db_con* IsEffBulletValidity(uint16_t wEffBulletSerial);
        bool IsEffectableEquip(struct _STORAGE_LIST::_storage_con* pCon);
        bool IsEquipAbleGrade(char byGradeLv);
        bool IsHaveMentalTicket();
        bool IsInTown();
        bool IsLastAttBuff();
        bool IsMapLoading();
        bool IsMineMode();
        bool IsMiningByMinigTicket();
        bool IsOutExtraStopPos(float* pfStopPos);
        bool IsOverOneDay();
        bool IsPassExpLimitLvDiff(int iDstLevel, bool* bGetAttackExp);
        bool IsPassMasteryLimitLvDiff(int iDstLevel);
        bool IsPunished(char byType, bool bSend);
        bool IsRecallAnimus();
        bool IsRecvableContEffect();
        bool IsReturnPostUpdate();
        bool IsRidingShip();
        bool IsRidingUnit();
        bool IsSFActableByClass(char byEffectCode, struct _base_fld* pSFFld);
        bool IsSFUsableGauge(char byEffectCode, uint16_t wEffectIndex, uint16_t* pwDelPoint);
        bool IsSFUsableSFMastery(char byMasteryCode, int nMasteryIndex);
        bool IsSFUseableRace(char byEffectCode, uint16_t wEffectIndex);
        bool IsSiegeMode();
        bool IsTargetObj(struct CGameObject* pkObj);
        bool IsUsableAccountType(int nCashType);
        bool IsUseCloakBooster();
        bool IsUseReleaseRaceBuffPotion();
        bool Is_Battle_Mode();
        void LimLvNpcQuestDelete(char byQuestDBSlot);
        bool Load(struct CUserDB* pUser, bool bFirstStart);
        void Loop();
        void NetClose(bool bMoveOutLobby);
        void NewViewCircleObject();
        static void OnLoop_Static();
        bool OutOfMap(struct CMapData* pIntoMap, uint16_t wLayerIndex, char byMapOutType, float* pfStartPos);
        void OutOfSec();
        void PastWhisperInit();
        void Potion_Buf_Extend();
        void PushDQSCheatPlyerVoteInfo();
        void PushDQSUpdatePlyerVoteInfo();
        void PushDQSUpdateVoteAvilable();
        void ReCalcMaxHFSP(bool bSend, bool bRatio);
        void RecallRandomPositionInRange(struct CMapData* pIntoMap, uint16_t wMapLayerIndex, float* pStartPos, int iRange);
        void RecvHSKQuest(char byHSKQuestCode, char byCristalBattleDBInfo, int nPvpPoint, uint16_t wKillPoint, uint16_t wDieCount, char byHSKTime);
        void RecvKillMessage(struct CCharacter* pDier);
        void ReservationForceClose();
        void Resurrect();
        void Return_AnimusAsk(char byReturnType);
        void RewardChangeClass(struct _class_fld* pClassFld, char bySelectRewardItem);
        void RewardChangeClassMastery(struct _class_fld* pClassFld);
        void RewardChangeClassRewardItem(struct _class_fld* pClassFld, char bySelectRewardItem);
        void RewardRaceWarPvpCash();
        void Reward_DarkDungeon(struct _dh_reward_sub_setup* pSetup, char* pszTitle, int bRealBoss, _STORAGE_LIST::_db_con* pItem, int* bIsRewarded);
        bool RobbedHP(struct CCharacter* pDst, int nDecHP);
        void SFContDelMessage(char byContCode, char byListIndex, bool bSend, bool bAura);
        void SFContInsertMessage(char byContCode, char byListIndex, bool bAuraSkill, struct CPlayer* pPlayerAct);
        void SFContUpdateTimeMessage(char byContCode, char byListIndex, int nLeftTime);
        bool SF_AllContDamageForceRemove_Once(struct CCharacter* pDstObj);
        bool SF_AllContDamageRemove_Once(struct CCharacter* pDstObj);
        bool SF_AllContHelpForceRemove_Once(struct CCharacter* pDstObj);
        bool SF_AllContHelpSkillRemove_Once(struct CCharacter* pDstObj);
        bool SF_AttHPtoDstFP_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ContDamageTimeInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ContHelpTimeInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ConvertMonsterTarget(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ConvertTargetDest(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_DamageAndStun(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_FPDec(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_HFSInc_Once(struct CCharacter* pDstObj);
        bool SF_HPInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_IncHPCircleParty(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_IncreaseDP(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_LateContDamageRemove_Once(struct CCharacter* pDstObj);
        bool SF_LateContHelpForceRemove_Once(struct CCharacter* pDstObj);
        bool SF_LateContHelpSkillRemove_Once(struct CCharacter* pDstObj);
        bool SF_MakePortalReturnBindPositionPartyMember(struct CCharacter* pDstObj, float fEffectValue, char* byRet);
        bool SF_MakeZeroAnimusRecallTimeOnce(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_OthersContHelpSFRemove_Once(float fEffectValue);
        bool SF_OverHealing_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_RecoverAllReturnStateAnimusHPFull(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_ReleaseMonsterTarget(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_RemoveAllContHelp_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_Resurrect_Once(struct CCharacter* pDstObj);
        bool SF_ReturnBindPosition(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_SPDec(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_STInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_SelfDestruction(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_SkillContHelpTimeInc_Once(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_Stun(struct CCharacter* pDstObj, float fEffectValue);
        bool SF_TeleportToDestination(struct CCharacter* pDstObj, bool bStone);
        bool SF_TransDestHP(struct CCharacter* pDstObj, float fEffectValue, char* byRet);
        bool SF_TransMonsterHP(struct CCharacter* pDstObj, float fEffectValue);
        void SendData_ChatTrans(char byChatType, unsigned int dwSenderSerial, char byRaceCode, bool bFilter, char* pwszMessage, char byPvpGrade, char* pwszSender);
        void SendData_PartyMemberEffect(char byAlterCode, uint16_t wEffectCode, char byLv);
        void SendData_PartyMemberFP();
        void SendData_PartyMemberHP();
        void SendData_PartyMemberInfo(uint16_t wDstIndex);
        void SendData_PartyMemberInfoToMembers();
        void SendData_PartyMemberLv();
        void SendData_PartyMemberMaxHFSP();
        void SendData_PartyMemberPos();
        void SendData_PartyMemberSP();
        void SendMsg_AMPInvenDownloadResult();
        void SendMsg_AddBagResult(char byErrCode);
        void SendMsg_AddEffect(uint16_t wEffectCode, char byLv, uint16_t wDurSec, unsigned int dwPlayerSerial, char* wszPlayerName);
        void SendMsg_AdjustAmountInform(char byStorageCode, uint16_t wSerial, unsigned int dwDur);
        void SendMsg_AlterBooster();
        void SendMsg_AlterContEffectTime(char byContType);
        void SendMsg_AlterEquipSPInform();
        void SendMsg_AlterExpInform();
        void SendMsg_AlterGradeInform();
        void SendMsg_AlterHPInform();
        void SendMsg_AlterItemDurInform(char byStorageCode, uint16_t wItemSerial, uint64_t dwDur);
        void SendMsg_AlterMaxDP();
        void SendMsg_AlterMoneyInform(char byReasonCode);
        void SendMsg_AlterPvPCash(int nIOCode);
        void SendMsg_AlterPvPPoint();
        void SendMsg_AlterPvPRank(uint16_t wPvpRate, unsigned int dwPvpRank);
        void SendMsg_AlterRegionInform(int nRegionIndex);
        void SendMsg_AlterSPInform();
        void SendMsg_AlterTol();
        void SendMsg_AlterTowerHP(uint16_t wItemSerial, uint16_t wLeftHP);
        void SendMsg_AlterTownOrField();
        void SendMsg_AlterUnitBulletInform(char byPart, uint16_t wLeftNum);
        void SendMsg_AlterUnitHPInform(char bySlotIndex, unsigned int dwGauge);
        void SendMsg_AlterWeaponBulletInform(uint16_t wItemSerial, uint16_t wLeftNum);
        void SendMsg_Alter_Action_Point(char byActCode, unsigned int dwActPoint);
        void SendMsg_AnimusExpInform();
        void SendMsg_AnimusFPInform();
        void SendMsg_AnimusHPInform();
        void SendMsg_AnimusInvenChange(char byErrCode);
        void SendMsg_AnimusModeInform(char byMode);
        void SendMsg_AnimusRecallResult(char byResultCode, uint16_t wLeftFP, struct CAnimus* pNewAnimus);
        void SendMsg_AnimusRecallWaitTimeFree(bool bFree);
        void SendMsg_AnimusReturnResult(char byRetCode, uint16_t wAnimusItemSerial, char byReturnType);
        void SendMsg_AnimusTargetResult(char byRetCode);
        void SendMsg_ApexInform(unsigned int dwRecvSize, char* pMsg);
        void SendMsg_AttackResult_Count(struct CAttack* pAt);
        void SendMsg_AttackResult_Error(int nErrCode);
        void SendMsg_AttackResult_Force(struct CAttack* pAt);
        void SendMsg_AttackResult_Gen(struct CAttack* pAt, uint16_t wBulletIndex);
        void SendMsg_AttackResult_SelfDestruction(struct CAttack* pAt);
        void SendMsg_AttackResult_Siege(struct CAttack* pAt, uint16_t wBulletIndex);
        void SendMsg_AttackResult_Skill(char byEffectCode, struct CPlayerAttack* pAt, uint16_t wBulletIndex);
        void SendMsg_AttackResult_Unit(struct CAttack* pAt, char byWeaponPart, uint16_t wBulletIndex);
        void SendMsg_AwayPartyInvitationQuestion(uint16_t wJoinerIndex);
        void SendMsg_AwayPartyRequestResult(char byRetCode);
        void SendMsg_BackTowerResult(char byErrCode, uint16_t wItemSerial, uint16_t wLeftHP);
        void SendMsg_BackTrapResult(char byErrCode);
        void SendMsg_BaseDownloadResult();
        void SendMsg_BillingExipreInform(char byKind, uint16_t wWaitSec);
        void SendMsg_BillingTypeChangeInform(int16_t iType, int lRemainTime, struct _SYSTEMTIME* pstEndDate, char byReason);
        void SendMsg_BreakdownEquipItem(char byPartIndex, uint16_t wSerial);
        void SendMsg_BuddhaEventMsg(char byErrorCode);
        void SendMsg_BuddyAddAnswerResult(char byRetCode, bool bAccept, unsigned int dwAskerSerial, uint16_t wIndex, unsigned int dwSerial, char* pwszCharName);
        void SendMsg_BuddyAddAsk(uint16_t wAskerIndex, unsigned int dwAskerSerial, char* pwszAskerName);
        void SendMsg_BuddyAddFail(char byRetCode, char* pwszDstName);
        void SendMsg_BuddyDelResult(char byRetCode, unsigned int dwSerial);
        void SendMsg_BuddyLoginInform(unsigned int dwObjSerial, char byMapIndex, char byPosCode);
        void SendMsg_BuddyLogoffInform(unsigned int dwObjSerial);
        void SendMsg_BuddyNameReNewal(unsigned int dwSerial, char* wszName);
        void SendMsg_BuddyPosInform(unsigned int dwDstSerial, char byMapIndex, char byPosCode);
        void SendMsg_BuyCashItemMode();
        void SendMsg_BuyItemStoreResult(struct CItemStore* pStore, int nOfferNum, struct _buy_offer* pCard, char byErrCode);
        void SendMsg_CancelSuggestResult(char byRetCode);
        void SendMsg_CastVoteResult(char byRetCode);
        void SendMsg_ChangeClassCommand();
        void SendMsg_CharacterRenameCashResult(bool bChange, char byErrCode);
        void SendMsg_ChatFarFailure(bool bBlock);
        void SendMsg_Circle_DelEffect(char byEffectCode, uint16_t wEffectIndex, char byLv, bool bToOne);
        void SendMsg_ClassSkillResult(char byErrCode, struct _CHRID* pidDst, uint16_t wSkillIndex);
        void SendMsg_ClearDarkHole(char byErrCode);
        void SendMsg_CombineItemExAcceptResult(struct _combine_ex_item_accept_result_zocl* pSend);
        void SendMsg_CombineItemExResult(struct _combine_ex_item_result_zocl* pSend);
        void SendMsg_CombineItemResult(char byErrCode, unsigned int dwFee, _STORAGE_LIST::_db_con* pNewItem);
        void SendMsg_CombineLendItemResult(char byErrCode, unsigned int dwFee, _STORAGE_LIST::_db_con* pNewItem);
        void SendMsg_CreateTowerResult(char byErrCode, unsigned int dwTowerObjSerial);
        void SendMsg_CreateTrapResult(char byErrCode, unsigned int dwTrapObjSerial);
        void SendMsg_CumDownloadResult();
        void SendMsg_CuttingCompleteResult(char byRet);
        void SendMsg_DTradeAccomplishInform(bool bSucc, uint16_t wStartSerial);
        void SendMsg_DTradeAddInform(char bySlotIndex, _STORAGE_LIST::_db_con* pItem, char byAmount);
        void SendMsg_DTradeAddResult(char byErrCode);
        void SendMsg_DTradeAnswerResult(char byErrCode);
        void SendMsg_DTradeAskInform(struct CPlayer* pAsker);
        void SendMsg_DTradeAskResult(char byErrCode);
        void SendMsg_DTradeBetInform(char byUnitCode, unsigned int dwAmount);
        void SendMsg_DTradeBetResult(char byErrCode);
        void SendMsg_DTradeCancleInform();
        void SendMsg_DTradeCancleResult(char byErrCode);
        void SendMsg_DTradeCloseInform(char byCloseCode);
        void SendMsg_DTradeDelInform(char bySlotIndex);
        void SendMsg_DTradeDelResult(char byErrCode);
        void SendMsg_DTradeLockInform();
        void SendMsg_DTradeLockResult(char byErrCode);
        void SendMsg_DTradeOKInform();
        void SendMsg_DTradeOKResult(char byErrCode);
        void SendMsg_DTradeStartInform(struct CPlayer* pAsker, struct CPlayer* pAnswer, unsigned int* pdwKey);
        void SendMsg_DTradeUnitAddInform(uint16_t wUnitKeySerial, struct _UNIT_DB_BASE::_LIST* pUnitData);
        void SendMsg_DTradeUnitInfoInform(char byTradeSlotIndex, struct _UNIT_DB_BASE::_LIST* pUnitData);
        void SendMsg_DamageResult(_STORAGE_LIST::_db_con* pItem);
        void SendMsg_DarkHoleOpenFail(int n, char byErrCode);
        void SendMsg_DarkHoleOpenResult(int n, int bPartyOnly, char byErrCode, uint16_t wHoleIndex, unsigned int dwHoleSerial);
        void SendMsg_DarkHoleRewardMessage(_STORAGE_LIST::_db_con* pItem, unsigned int dwMemberIndex, int isRewarded);
        void SendMsg_DelEffect(char byEffectCode, uint16_t wEffectIndex, char byLv);
        void SendMsg_DeleteStorageInform(char byStorageCode, uint16_t wSerial);
        void SendMsg_Destroy();
        void SendMsg_Die();
        void SendMsg_EconomyHistoryInform();
        void SendMsg_EconomyRateInform(bool bStart);
        void SendMsg_EmbellishResult(char byErrCode);
        void SendMsg_EnterDarkHole(char byErrCode, unsigned int dwHoleSerial);
        void SendMsg_EquipItemLevelLimit(int nCurPlayerLv);
        void SendMsg_EquipPartChange(char byPart);
        void SendMsg_EquipPartResult(char byErrCode);
        void SendMsg_ExchangeItemResult(char byErrCode, _STORAGE_LIST::_db_con* pNewItem);
        void SendMsg_ExchangeLendItemResult(char byErrCode, _STORAGE_LIST::_db_con* pNewItem);
        void SendMsg_ExchangeMoneyResult(char byErrCode);
        void SendMsg_ExitWorldResult(char byRetCode);
        void SendMsg_ExtTrunkExtendResult(char byRetCode, char bySlotNum, char byLackSlotNum);
        void SendMsg_FanfareItem(char byGetType, _STORAGE_LIST::_db_con* pItem, struct CItemBox* pItemBox);
        void SendMsg_FcitemInform(uint16_t wItemSerial, unsigned int dwNewStat);
        void SendMsg_FixPosition(int n);
        void SendMsg_ForceDownloadResult();
        void SendMsg_ForceInvenChange(char byErrCode);
        void SendMsg_ForceResult(char byErrCode, struct _CHRID* pidDst, _STORAGE_LIST::_db_con* pForceItem, int nSFLv);
        void SendMsg_GM_Greeting(char* wszGMName, char* wszMsg);
        void SendMsg_GUILD_Greeting(char* wszName, char* wszMsg);
        void SendMsg_GestureInform(char byGestureType);
        void SendMsg_GiveupDarkHole(char byErrCode);
        void SendMsg_GotoBasePortalResult(char byErrCode);
        void SendMsg_GotoRecallResult(char byErrCode, char byMapCode, float* pfStartPos, char byMapInType);
        void SendMsg_GroupTargetInform(char byGroupType, char* pwszName);
        void SendMsg_GuildEstablishFail(char byRetCode);
        void SendMsg_GuildForceLeaveBoradori();
        void SendMsg_GuildJoinAcceptFail(char byRetCode, unsigned int dwApplierSerial);
        void SendMsg_GuildJoinApplyCancelResult(char byRetCode);
        void SendMsg_GuildJoinApplyRejectInform();
        void SendMsg_GuildJoinApplyResult(char byRetCode, struct CGuild* pApplyGuild);
        void SendMsg_GuildJoinOtherInform();
        void SendMsg_GuildManageResult(char byRetCode);
        void SendMsg_GuildMasterEffect(char byState, char byGrade, char byEffSubAttack, char byEffSubDefence, char byEffAddAttack, char byEffAddDefence);
        void SendMsg_GuildPushMoneyResult(char byRetCode);
        void SendMsg_GuildRoomEnterResult(char byRetCode, char bySubRetCode, char byMapIndex, uint16_t wMapLayer, float* pPos, int tt);
        void SendMsg_GuildRoomOutResult(char byRetCode, char byMapIndex, uint16_t wMapLayer, float* pPos);
        void SendMsg_GuildRoomRentResult(char byRetCode, char bySubRetCode, char byRoomType);
        void SendMsg_GuildRoomRestTimeResult();
        void SendMsg_GuildSelfLeaveResult(char byRetCode);
        void SendMsg_GuildSetHonorResult(char byRetCode);
        void SendMsg_HSKQuestActCum();
        void SendMsg_HSKQuestSucc(char byQuestCode, bool bSucc);
        void SendMsg_HonorGuildMark(char byRank);
        void SendMsg_InformTaxIncome(char byRet, unsigned int dwComm, char* pwszDate);
        void SendMsg_Init_Action_Point();
        void SendMsg_InsertItemInform(char byStorageCode, _STORAGE_LIST::_db_con* pItem);
        void SendMsg_InsertNewQuest(char bySlotIndex, struct _QUEST_DB_BASE::_LIST* pQuestDB);
        void SendMsg_InsertNextQuest(char bySlotIndex, struct _QUEST_DB_BASE::_LIST* pQuestDB);
        void SendMsg_InsertQuestFailure(char byEventType, unsigned int dwEventIndex, char byEventNodeIndex);
        void SendMsg_InsertQuestItemInform(_STORAGE_LIST::_db_con* pItem);
        void SendMsg_InvenDownloadResult();
        void SendMsg_ItemDowngrade(char byErrCode);
        void SendMsg_ItemStorageRefresh(char byStorageCode);
        void SendMsg_ItemUpgrade(char byErrCode);
        void SendMsg_JadeEffectErr(char byErrorCode);
        void SendMsg_LendItemTimeExpired(char byStorageCode, uint16_t wSerial);
        void SendMsg_Level(int nLevel);
        void SendMsg_LinkBoardDownloadResult();
        void SendMsg_MacroRequest();
        void SendMsg_MadeTrapNumInform(char byNum);
        void SendMsg_MakeItemCheatSendButtonEnable(bool bEnableSendButton);
        void SendMsg_MakeItemResult(char byErrCode);
        void SendMsg_MapEnvInform(char byMapCode, unsigned int dwMapEnvCode);
        void SendMsg_MapOut(char byMapOutCode, char byNextMapCode);
        void SendMsg_MaxHFSP();
        void SendMsg_MaxPvpPointInform(int nMax);
        void SendMsg_MineCancle();
        void SendMsg_MineCompleteResult(char byErrCode, char byNewOreIndex, uint16_t dwOreSerial, char byOreDur, uint16_t dwBatteryLeftDurPoint);
        void SendMsg_MineStartResult(char byErrCode);
        void SendMsg_MonsterAggroData(struct CCharacter* pCharacter);
        void SendMsg_MoveError(char byRetCode);
        void SendMsg_MoveNext(bool bOtherSend);
        void SendMsg_MovePortal(char byMapIndex, float* pfStartPos, char byZoneCode);
        void SendMsg_MovePortal(char byRet, char byMapIndex, char byPotalIndex, float* pfStartPos, bool bEqualZone);
        void SendMsg_MoveToOwnStoneMapInform(char byStoneMapMoveInfo);
        void SendMsg_MoveToOwnStoneMapResult(char byRetCode, char byMapIndex, float* pos);
        void SendMsg_NPCLinkItemCheckResult(char byResCode, struct _STORAGE_POS_INDIV* pStorage);
        void SendMsg_NewMovePotionResult();
        void SendMsg_NewViewOther(char byViewType);
        void SendMsg_NotifyEffectForGetItem(char byBoxType, unsigned int dwCharSerial, char* szCharName, _STORAGE_LIST::_db_con* pItem, bool bCircle);
        void SendMsg_NotifyGetExpInfo(long double dOldExp, long double dAlterExp, long double dCurExp);
        void SendMsg_Notify_ExceptFromRaceRanking(int bExcepted);
        void SendMsg_Notify_Get_Golden_Box(char byBoxType, unsigned int dwCharSerial, char* szCharName, _STORAGE_LIST::_db_con* pItem, bool bCircle);
        void SendMsg_Notify_Gravity_Stone_Owner_Die();
        void SendMsg_Notify_Me_Get_Golden_Box(char byBoxType, _STORAGE_LIST::_db_con* pItem);
        void SendMsg_NpcQuestHistoryInform(char bySlotIndex);
        void SendMsg_NpcQuestListResult(struct _NPCQuestIndexTempData* pQuestIndexData);
        void SendMsg_OffPartResult(char byErrCode);
        void SendMsg_OfferSuggestResult(char byRetCode);
        void SendMsg_OreCuttingResult(char byErrCode, char byLeftOreNum, unsigned int dwConsumDalant);
        void SendMsg_OreIntoBagResult(char byErrCode, uint16_t wNewSerial, char byLendType, unsigned int dwLendTime);
        void SendMsg_OreTransferCount();
        void SendMsg_OtherShapeAll(struct CPlayer* pDst);
        void SendMsg_OtherShapeError(struct CPlayer* pDst, char byErrCode);
        void SendMsg_OtherShapePart(struct CPlayer* pDst);
        void SendMsg_OtherShapePartEx_CashChange(struct CPlayer* pDst, CashChangeStateFlag ServerData, CashChangeStateFlag ClinetData);
        void SendMsg_PartyAlterLootShareResult(char byLootShareMode);
        void SendMsg_PartyDisjointResult(char bSuccess);
        void SendMsg_PartyJoinApplicationQuestion(struct CPlayer* pJoiner);
        void SendMsg_PartyJoinFailLevel();
        void SendMsg_PartyJoinInvitationQuestion(uint16_t wJoinerIndex);
        void SendMsg_PartyJoinJoinerResult();
        void SendMsg_PartyJoinMemberResult(struct CPartyPlayer* pJoiner, char byLootShareMode);
        void SendMsg_PartyLeaveCompulsionResult(struct CPartyPlayer* pLeaver);
        void SendMsg_PartyLeaveSelfResult(struct CPartyPlayer* pLeaver, bool bWorldExit);
        void SendMsg_PartyLockResult(char byRet);
        void SendMsg_PartyLootItemInform(unsigned int dwTakerSerial, char byTableCode, uint16_t wItemIndex, char byNum);
        void SendMsg_PartySuccessResult(struct CPartyPlayer* pSuccessor);
        void SendMsg_PcRoomCharClass(unsigned int dwPcRoomClassIndex);
        void SendMsg_PcRoomError(char byErrorCode);
        void SendMsg_PostContent(char byErrCode, unsigned int dwPostSerial, char* wszContent, char byTableCode, uint16_t wItemIndex, uint64_t dwDur, unsigned int dwLv, unsigned int dwGold);
        void SendMsg_PostDelete(char byErrCode, unsigned int dwPostSerial);
        void SendMsg_PostDelivery(char byIndex, unsigned int dwPostSerial, char* wszSendName, char* wszTitle, bool bItem, bool bGold, char byState);
        void SendMsg_PostItemGold(char byErrCode);
        void SendMsg_PostReturn(char byErrCode, unsigned int dwPostSerial, char* wszRecvName, char* wszTitle, char* wszContent, char byTableCode, uint16_t wItemIndex, uint64_t dwDur, unsigned int dwLv, unsigned int dwGold);
        void SendMsg_PostReturnConfirm(char byErrCode, unsigned int dwPostSerial);
        void SendMsg_PostSendReply(char byErrCode);
        void SendMsg_PotionDelayTime(unsigned int* pdwPotionNextUseTime, unsigned int dwCurTime);
        void SendMsg_PotionDivision(uint16_t wSerial, char byParentAmount, uint16_t wChildSerial, char byChildAmount, int nRet);
        void SendMsg_PotionSeparation(uint16_t wParentSerial, char byParentAmount, uint16_t wChildSerial, char byChildAmount, int nRet);
        void SendMsg_PremiumCashItemUse(uint16_t wSerial);
        void SendMsg_ProposeVoteResult(char byRetCode);
        void SendMsg_PvpRankListVersionUp(char byVersion);
        void SendMsg_QuestComplete(char byQuestDBSlot);
        void SendMsg_QuestDownloadResult();
        void SendMsg_QuestFailure(char byFailCode, char byQuestDBSlot);
        void SendMsg_QuestGiveUpResult(char byReturnSlot);
        void SendMsg_QuestHistoryDownloadResult();
        void SendMsg_QuestProcess(char byQuestDBSlot, char byActIndex, uint16_t wCount);
        void SendMsg_RACE_Greeting(char* wszBossName, char* wszMsg);
        void SendMsg_RaceBattlePenelty(int nAlterPoint, char byAlterType);
        void SendMsg_RaceBossCryMsg();
        void SendMsg_RaceTopInform(bool bTop);
        void SendMsg_RadarCharSearchResult();
        void SendMsg_RadarDelayTime(unsigned int dwDelay);
        void SendMsg_ReEnterAsk(uint16_t wChannelIndex, unsigned int dwChannelSerial);
        void SendMsg_ReEnterDarkHoleResult(char byRetCode);
        void SendMsg_RealMovePoint(int n);
        void SendMsg_Recover();
        void SendMsg_RecvHSKQuest();
        void SendMsg_RefeshGroupTargetPosition(char byGroupType);
        void SendMsg_RegistBindResult(char byRetCode);
        void SendMsg_ReleaseGroupTargetObjectResult(char byGroupType);
        void SendMsg_ReleaseSiegeModeResult(char byRetCode);
        void SendMsg_RemainOreRate();
        void SendMsg_RemainTimeInform(int16_t iType, int lRemainTime, struct _SYSTEMTIME* pstEndDate);
        void SendMsg_ResDivision(char byErrCode, _STORAGE_LIST::_db_con* pStartOre, _STORAGE_LIST::_db_con* pTargetOre);
        void SendMsg_ResSeparation(char byErrCode, _STORAGE_LIST::_db_con* pStartOre, _STORAGE_LIST::_db_con* pNewOre);
        void SendMsg_ResultChangeTaxRate(char byRetCode, char byNextTax);
        void SendMsg_ResultNpcQuest(bool bSucc);
        void SendMsg_Resurrect(char byRet, bool bQuickPotion);
        void SendMsg_ResurrectInform();
        void SendMsg_Revival(char byRet, bool bEquialZone);
        void SendMsg_RevivalOfJade(uint16_t wSuccRate);
        void SendMsg_RewardAddItem(_STORAGE_LIST::_db_con* pItem, char byReason);
        void SendMsg_SFDelayRequest();
        void SendMsg_SelectClassResult(char byErrCode, uint16_t wSelClassIndex);
        void SendMsg_SelectQuestReward(char byQuestDBSlot);
        void SendMsg_SelectWaitedQuest(char byEventType, unsigned int dwEventIndex, char byEventNodeIndex);
        void SendMsg_SellItemStoreResult(struct CItemStore* pStore, char byErrCode);
        void SendMsg_SetDPInform();
        void SendMsg_SetFPInform();
        void SendMsg_SetGroupMapPoint(char byRetCode, char byGroupType, char byMapCode, float* pzTar, char byRemain);
        void SendMsg_SetGroupTargetObjectResult(char byRetCode, char byGroupType);
        void SendMsg_SetHPInform();
        void SendMsg_SetItemCheckResult(char byResult, unsigned int dwSetItem, char bySetEffectNum);
        void SendMsg_SetSPInform();
        void SendMsg_SetTargetObjectResult(char byRetCode, bool bForce);
        void SendMsg_SkillResult(char byErrCode, struct _CHRID* pidDst, char bySkillIndex, int nSFLv);
        void SendMsg_SpecialDownloadResult();
        void SendMsg_StartContSF(struct _sf_continous* pCont);
        void SendMsg_StartNewPos(char byMapInMode);
        void SendMsg_StartShopping();
        void SendMsg_StatInform(char byStatIndex, unsigned int dwNewStat, char byReason);
        void SendMsg_StateInform(uint64_t dwStateFlag);
        void SendMsg_Stop(bool bAll);
        void SendMsg_StoreLimitItemAmountInfo(unsigned int dwStoreIndex, struct _limit_amount_info* pAmountInfo);
        void SendMsg_StoreListResult();
        void SendMsg_TLStatusInfo(unsigned int dwFatigue, char wStatus);
        void SendMsg_TLStatusPenalty(char byErrCode);
        void SendMsg_TakeAddResult(char byErrCode, _STORAGE_LIST::_db_con* pItem);
        void SendMsg_TakeNewResult(char byErrCode, _STORAGE_LIST::_db_con* pItem);
        void SendMsg_TalikCrystalExchangeResult(char byRet, char byExchangeNum, _STORAGE_LIST::_db_con* pNewItem);
        void SendMsg_TargetObjectHPInform();
        void SendMsg_TeleportError(char byErrorCode, unsigned int dwMapIndex);
        void SendMsg_TestAttackResult(char byEffectCode, char byEffectIndex, uint16_t wBulletItemIndex, char byEffectLv, char byWeaponPart, int16_t* pzTar);
        void SendMsg_ThrowSkillResult(char byErrCode, struct _CHRID* pidDst, char bySkillIndex);
        void SendMsg_ThrowStorageResult(char byErrCode);
        void SendMsg_ThrowUnitResult(char byErrCode, struct _CHRID* pidDst, uint16_t wBulletIndex);
        void SendMsg_TowerContinue(uint16_t wItemSerial, struct CGuardTower* pTwr);
        void SendMsg_TransShipRenewTicketResult(char byErrCode);
        void SendMsg_TransformSiegeModeResult(char byRetCode);
        void SendMsg_TrunkChangPasswdResult(char byRetCode);
        void SendMsg_TrunkDownloadResult(char byRetCode);
        void SendMsg_TrunkEstResult(char byRetCode, unsigned int dwLeftDalant);
        void SendMsg_TrunkExtendResult(char byRetCode, char bySlotNum, unsigned int dwLeftDalant, unsigned int dwConsumDalant);
        void SendMsg_TrunkHintAnswerResult(char byRetCode, char* pwszPassword);
        void SendMsg_TrunkIoMoneyResult(char byRetCode, long double dTrunkDalant, long double dTrunkGold, unsigned int dwDalant, unsigned int dwGold, unsigned int dwFeeDalant);
        void SendMsg_TrunkIoResult(char byCase, char byRetCode, unsigned int dwLeftDalant, unsigned int dwConsumDanlant);
        void SendMsg_TrunkPotionDivision(uint16_t wSerial, uint16_t wParentAmount, uint16_t wChildSerial, uint16_t wChildAmount, int nRet);
        void SendMsg_TrunkPwHintIndexResult(char byRetCode, char byHintIndex);
        void SendMsg_TrunkResDivision(char byErrCode, _STORAGE_LIST::_db_con* pStartOre, _STORAGE_LIST::_db_con* pTargetOre);
        void SendMsg_UILock_FindPW_Result(char byRet, char* uszUILockPW, char byFindPassFailCount);
        void SendMsg_UILock_Init_Request_ToAccount(unsigned int dwSerial, char* uszUILockPW, uint16_t wUserIndex, char byHintIndex, char* uszHintAnswer);
        void SendMsg_UILock_Init_Result(char byRet);
        void SendMsg_UILock_Login_Result(char byRet, char byFailCount);
        void SendMsg_UILock_Update_Request_ToAccount(unsigned int dwSerial, char* uszUILockPW, uint16_t wUserIndex, char byHintIndex, char* uszHintAnswer);
        void SendMsg_UILock_Update_Result(char byRet);
        void SendMsg_UnitAlterFeeInform(char bySlotIndex, unsigned int dwPullingFee);
        void SendMsg_UnitBulletFillResult(char byRetCode, char bySlotIndex, uint16_t* pwBulletIndex, unsigned int* pdwConsumMoney);
        void SendMsg_UnitBulletReplaceResult(char byRetCode);
        void SendMsg_UnitDeliveryResult(char byRetCode, char bySlotIndex, unsigned int dwParkingUnitSerial, unsigned int dwPayDalant);
        void SendMsg_UnitDestroy(char bySlotIndex);
        void SendMsg_UnitForceReturnInform(char bySlotIndex, unsigned int dwDebt);
        void SendMsg_UnitFrameBuyResult(char byRetCode, char byFrameCode, char byUnitSlotIndex, uint16_t wKeyIndex, uint16_t wKeySerial, unsigned int* pdwConsumMoney);
        void SendMsg_UnitFrameRepairResult(char byRetCode, char bySlotIndex, unsigned int dwNewGauge, unsigned int dwConsumDalant);
        void SendMsg_UnitLeaveResult(char byRetCode);
        void SendMsg_UnitPackFillResult(char byRetCode, char bySlotIndex, char byFillNum, struct _unit_pack_fill_request_clzo::__list* pList, unsigned int* pdwConsumMoney);
        void SendMsg_UnitPartTuningResult(char byRetCode, char bySlotIndex, int* pnCost);
        void SendMsg_UnitReturnResult(char byRetCode, unsigned int dwPayDalant);
        void SendMsg_UnitRideChange(bool bTake, struct CParkingUnit* pUnit);
        void SendMsg_UnitSellResult(char byRetCode, char bySlotIndex, uint16_t wKeySerial, int nAddMoney, unsigned int dwTotalNonpay, unsigned int dwSumDalant, unsigned int dwSumGold);
        void SendMsg_UnitTakeResult(char byRetCode);
        void SendMsg_UpdateTLStatusInfo(unsigned int dwFatigue, char wStatus);
        void SendMsg_UsPotionResultOther(char byRetcode, uint16_t wPotionIndex, struct CPlayer* pUsePlayer, bool bCircle);
        void SendMsg_UseJadeResult(char byErrCode, uint16_t wItemSerial);
        void SendMsg_UsePotionResult(char byErrCode, uint16_t wSerial, char byLeftNum);
        void SendMsg_UseRadarResult(char byErrCode, uint16_t wSerial, unsigned int dwDelay);
        void SendMsg_VoteResult(unsigned int dwMatterVoteSynKey, char byRetCode);
        void SendTargetMonsterSFContInfo();
        void SendTargetPlayerDamageContInfo();
        void SenseState();
        void SetAttackPart(int nAttactPart);
        void SetBattleMode(bool bAttack);
        void SetBindDummy(struct _dummy_position* pDummy);
        void SetBindMapData(struct CMapData* pMapData);
        bool SetBindPosition(struct CMapData* pMap, struct _dummy_position* pDummy);
        void SetCashAmount(int nAmount);
        void SetCntEnable(bool bSet);
        bool SetDP(int nDP, bool bOver);
        int SetDamage(int nDamage, struct CCharacter* pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn);
        void SetEffectEquipCode(char byStorageCode, char bySlotIndex, char byCode);
        void SetEquipEffect(struct _STORAGE_LIST::_storage_con* pItem, bool bEquip);
        void SetEquipJadeEffect(int nParam, float fCurVal, bool bAdd);
        bool SetFP(int nFP, bool bOver);
        void SetGauge(int nParamCode, int nValue, bool bOver);
        void SetGrade(char byGrade);
        bool SetHP(int nHP, bool bOver);
        void SetHaveEffect(bool bLogin);
        void SetHaveEffectUseTime(_STORAGE_LIST::_db_con* pItem, bool bAdd);
        void SetLastAttBuff(bool bSet);
        void SetLevel(char byNewLevel);
        void SetLevelD(char byDownLevel);
        void SetMstHaveEffect(struct _ResourceItem_fld* pFld, _STORAGE_LIST::_db_con* pItem, bool bAdd, int nAlter);
        void SetMstPt(int nMstCode, float fVal, bool bAdd, int nWpType);
        void SetPotionActDelay(char byPotionClass, unsigned int dwCurrTime, unsigned int dwActDelay);
        void SetPvpPointLeak(long double dValue);
        void SetRankRate(uint16_t wRankRate, unsigned int dwRank);
        bool SetSP(int nSP, bool bOver);
        void SetShapeAllBuffer();
        void SetSiege(_STORAGE_LIST::_db_con* pSiegeItem);
        void SetStateFlag();
        static void SetStaticMember();
        bool SetTarPos(float* fTarPos, bool bColl);
        void SetUseReleaseRaceBuffPotion();
        void SetVote(int nSerial);
        void SortPost(int nNumber);
        void SubActPoint(char byCode, unsigned int dwSub);
        void SubDalant(unsigned int dwSub);
        void SubGold(unsigned int dwSub);
        void SubPoint(unsigned int dwSub);
        unsigned int SumMinuteBetween(struct _SYSTEMTIME* tmLast, struct _SYSTEMTIME* tmLocal);
        unsigned int SumMinuteOne(struct _SYSTEMTIME* tm);
        void TakeGravityStone();
        void UpdateAuraSFCont();
        void UpdateChaosModeState(unsigned int dwCurTime);
        bool UpdateDelPost(unsigned int dwPostSerial, int nIndex);
        void UpdateLastCriTicket(uint16_t byCurrentYear, char byCurrentMonth, char byCurrentDay, char byCurrentHour, char byNumOfTime);
        void UpdateLastMetalTicket(uint16_t byCurrentYear, char byCurrentMonth, char byCurrentDay, char byCurrentHour, char byNumOfTime);
        void UpdatePost(unsigned int dwIndex);
        void UpdatePostAddLog(unsigned int dwIndex, bool bLog, int nItemKey);
        void UpdatePvpOrderView(int64_t tCurTime);
        void UpdatePvpPointLimiter(int64_t tCurTime);
        void UpdateReturnPost(unsigned int dwSerial);
        void UpdateVisualVer(CashChangeStateFlag byChangeFlagMask);
        bool Update_GoldPoint(unsigned int dwPoint);
        void UpdatedMasteryWriteHistory();
        bool WPActiveForce(struct _be_damaged_char* pDamList, int nDamagedObjNum, struct _force_fld* pForceFld);
        bool WPActiveSkill(struct _be_damaged_char* pDamList, int nDamagedObjNum, struct _skill_fld* pSkillFld, int nEffectCode);
        bool WeaponSFActive(struct _be_damaged_char* pDamList, int* nDamagedObjNum, int* nShotNum, uint16_t wBulletSerial);
        void _AnimusReturn(char byReturnType);
        int _CalcMaxFP();
        int _CalcMaxHP();
        int _CalcMaxSP();
        void _CheckForcePullUnit();
        uint16_t _DeleteUnitKey(char bySlotIndex);
        struct _ITEM_EFFECT* _GetItemEffect(_STORAGE_LIST::_db_con* pItem);
        char _GetPartyMemberInCircle(struct CPlayer** out_ppMember, int nMax, bool bOne);
        bool _LockUnitKey(char bySlotIndex, bool bLock);
        struct _Quest_fld* _Reward_Quest(struct _Quest_fld* pQuestFld, char byRewardItemIndex);
        void _TowerAllReturn(char byDestroyType, bool bForceReturn);
        void _TowerDestroy(struct CGuardTower* pTowerObj);
        uint16_t _TowerReturn(_STORAGE_LIST::_db_con* pTowerItem);
        void _TrapDestroy(struct CTrap* pTrap, char byDestroyCode);
        void _TrapReturn(struct CTrap* pTrap, uint16_t wAddSerial);
        void _UnitDestroy(char byUnitSlot);
        void _UpdateUnitDebt(char bySlotIndex, unsigned int dwPull);
        void _check_dst_param_after_attack(int nTotalDam, struct CCharacter* pTarget);
        bool _check_embel_part(_STORAGE_LIST::_db_con* pFixingItem);
        bool _check_equip_part(_STORAGE_LIST::_db_con* pFixingItem);
        int _check_equipmastery_lim(int EquipMasteryCode);
        int _check_exp_after_attack(int nDamagedObjNum, struct _be_damaged_char* pList, struct CPartyModeKillMonsterExpNotify* kPartyExpNotify);
        void _check_guild_target_object();
        void _check_hp_send_party();
        unsigned int _check_mastery_cum_lim(char byMasteryClass, char byIndex);
        unsigned int _check_mastery_lim(char byMasteryClass, char byIndex);
        void _check_party_target_object();
        void _check_race_target_object();
        void _check_target_object();
        int _pre_check_force_attack(struct CCharacter* pDst, float* pfTarPos, uint16_t wForceItemSerial, struct _force_fld** ppForceFld, _STORAGE_LIST::_db_con** ppForceItem, uint16_t* pdwDecPoint, uint16_t wEffBtSerial, _STORAGE_LIST::_db_con** ppEffBtProp, struct _BulletItem_fld** ppfldEffBt);
        int _pre_check_in_guild_battle(struct CCharacter* pDst);
        bool _pre_check_in_guild_battle_race(struct CCharacter* pDst, bool bEqueal);
        int _pre_check_normal_attack(struct CCharacter* pDst, uint16_t wBulletSerial, bool bCount, _STORAGE_LIST::_db_con** ppBulletProp, struct _BulletItem_fld** ppfldBullet, uint16_t wEffBtSerial, _STORAGE_LIST::_db_con** ppEffBtProp, struct _BulletItem_fld** ppfldEffBt);
        int _pre_check_siege_attack(struct CCharacter* pDst, float* pfAttackPos, uint16_t wBulletSerial, _STORAGE_LIST::_db_con** ppBulletProp, struct _BulletItem_fld** ppfldBullet, uint16_t wEffBtSerial, _STORAGE_LIST::_db_con** ppEffBulletProp, struct _BulletItem_fld** ppfldEffBullet);
        int _pre_check_skill_attack(struct CCharacter* pDst, float* pfAttackPos, char byEffectCode, struct _skill_fld* pSkillFld, uint16_t wBulletSerial, _STORAGE_LIST::_db_con** ppBulletProp, struct _BulletItem_fld** ppfldBullet, int nEffectGroup, uint16_t* pdwDecPoint, uint16_t wEffBtSerial, _STORAGE_LIST::_db_con** ppEffBtProp, struct _BulletItem_fld** ppfldEffBt);
        bool _pre_check_skill_enable(struct _skill_fld* pSkillFld);
        bool _pre_check_skill_gradelimit(struct _skill_fld* pSkillFld);
        int _pre_check_unit_attack(struct CCharacter* pDst, char byWeaponPart, struct _UnitPart_fld** ppWeaponFld, struct _UnitBullet_fld** ppBulletFld, struct _unit_bullet_param** ppBulletParam);
        bool _pre_check_wpactive_force_attack();
        bool _pre_check_wpactive_skill_attack(char byEffectCode, struct _skill_fld* pSkillFld, uint16_t wBulletSerial, _STORAGE_LIST::_db_con** ppBulletProp, struct _BulletItem_fld** ppfldBullet);
        void _set_db_sf_effect(struct _SFCONT_DB_BASE* pDBBase);
        void apply_case_equip_std_effect(_STORAGE_LIST::_db_con* pItem, bool bEquip);
        void apply_case_equip_upgrade_effect(_STORAGE_LIST::_db_con* pItem, bool bEquip);
        void apply_have_item_std_effect(int nEffCode, float fVal, bool bAdd, int nDiffCnt);
        void apply_normal_item_std_effect(int nEffCode, float fVal, bool bEquip);
        bool dev_SetGuildGrade(char byGrade);
        bool dev_SetGuildGradeByGuildSerial(unsigned int dwGuildSerial, char byGrade);
        bool dev_SetGuildGradeByName(char* uszGuildName, char byGrade);
        bool dev_after_effect();
        bool dev_all_kill();
        bool dev_animus_recall_time_free(bool bFree);
        bool dev_avator_copy(char* pwszDstName);
        bool dev_change_class(char* pszClassCode);
        bool dev_cont_effect_del();
        bool dev_cont_effect_time(unsigned int dwSec);
        bool dev_dalant(unsigned int dwDalant);
        bool dev_die();
        bool dev_drop_item(char* pszItemCode, int nNum, char* pszUpTalCode, int nUpNum);
        bool dev_free_sf_by_class();
        bool dev_full_animus_gauge();
        bool dev_full_force();
        bool dev_full_point();
        bool dev_gold(unsigned int dwGold);
        bool dev_goto_monster(struct CMonster* pMon);
        bool dev_goto_npc(struct CMerchant* pNpc);
        bool dev_half_inven_amount(uint64_t dwAmount);
        bool dev_half_point();
        bool dev_init_monster();
        bool dev_inven_empty();
        bool dev_item_make_no_use_matrial(bool noUsingMatrial);
        bool dev_loot_bag();
        bool dev_loot_free(bool bFree);
        bool dev_loot_fullitem(char byLv);
        bool dev_loot_item(char* pszItemCode, int nNum, char* pszUpTalCode, int nUpNum);
        bool dev_loot_material();
        bool dev_loot_mine();
        bool dev_loot_tower();
        bool dev_lv(int nLv);
        bool dev_make_succ(bool bSucc);
        bool dev_max_level_ext(char byMaxLevel);
        bool dev_never_die(bool bSet);
        bool dev_quest_complete();
        bool dev_quest_complete_other(char* pwszCharName);
        bool dev_set_animus_exp(uint64_t dwExpPoint);
        bool dev_set_animus_lv(int nAnimusLv);
        bool dev_set_hp(float prob);
        bool dev_trap_attack_grade(int nPoint);
        bool dev_up_all(int nCum);
        bool dev_up_all_pt(int nLv);
        bool dev_up_cashbag(long double dPoint);
        bool dev_up_forceitem(int nCum);
        bool dev_up_forcemastery(int nCum);
        bool dev_up_mastery(int nMasteryCode, int nMasteryIndex, int nLv);
        bool dev_up_pvp(long double dPoint);
        bool dev_up_skill(char* pszSkillCode, int nCum);
        bool dev_view_boss();
        bool dev_view_method(char* pwszDstName);
        void make_force_attack_param(struct CCharacter* pDst, struct _force_fld* pForceFld, _STORAGE_LIST::_db_con* pForceItem, float* pTar, struct _attack_param* pAP, _STORAGE_LIST::_db_con* pEffBulletItem, float fAddEffBtFc);
        void make_gen_attack_param(struct CCharacter* pDst, char byPart, struct _BulletItem_fld* pBulletFld, float fAddBulletFc, struct _attack_param* pAP, struct _BulletItem_fld* pEffBtFld, float fAddEffBtFc);
        void make_siege_attack_param(struct CCharacter* pDst, float* pfAttackPos, char byPart, struct _BulletItem_fld* pBulletFld, float fAddBulletFc, struct _attack_param* pAP, struct _BulletItem_fld* pEffBulletFld, float fAddEffBtFc);
        void make_skill_attack_param(struct CCharacter* pDst, float* pfAttackPos, char byEffectCode, struct _skill_fld* pSkillFld, int nAttType, _STORAGE_LIST::_db_con* pBulletItem, float fAddBulletFc, struct _attack_param* pAP, _STORAGE_LIST::_db_con* pEffBulletItem, float fAddEffBulletFc);
        void make_unit_attack_param(struct CCharacter* pDst, struct _UnitPart_fld* pWeaponFld, float fAddBulletFc, struct _attack_param* pAP);
        void make_wpactive_force_attack_param(struct CCharacter* pDst, struct _force_fld* pForceFld, float* pfAttackPos, struct _attack_param* pAP);
        void make_wpactive_skill_attack_param(struct CCharacter* pDst, struct _skill_fld* pSkillFld, float* pfAttackPos, char byEffectCode, int nAttType, _STORAGE_LIST::_db_con* pBulletItem, float fAddBulletFc, struct _attack_param* pAP, int* nShotNum);
        bool mgr_MaxAttackPoint(int nMax);
        bool mgr_TrunkInit();
        bool mgr_all_item_muzi(int nLv);
        bool mgr_change_degree(int nDegree);
        bool mgr_defense_item_grace(char byItemCode, int nLv);
        bool mgr_destroy_system_tower();
        bool mgr_dungeon_pass();
        bool mgr_exit_keeper();
        bool mgr_exit_stone();
        bool mgr_free_ride_ship();
        bool mgr_gotoCoordinates(char* pszMapCode, float fX, float fY, float fZ);
        bool mgr_gotoDstCoordinates(char* pwszDstName, char* pszMapCode, float fX, float fY, float fZ);
        bool mgr_goto_mine();
        bool mgr_goto_shipport(int nRaceCode, int nPort);
        bool mgr_goto_stone(char byRaceCode);
        bool mgr_goto_store(int nRaceCode, char* pszNPCName);
        bool mgr_holykeeper_start(int nRace);
        bool mgr_holystone_start(int nNumOfTime);
        bool mgr_item_telekinesis();
        bool mgr_kick(char* pwszCharName);
        bool mgr_make_system_tower(char* pszTowerCode);
        bool mgr_matchless(bool bMatchless);
        bool mgr_pass_sch_one_step();
        bool mgr_recall_guild_player(char* wszDestCharName);
        bool mgr_recall_mon(char* pszMonCode, int nCreateNum);
        bool mgr_recall_party_player(char* wszDestCharName);
        bool mgr_recall_player(char* pwszCharName);
        bool mgr_resurrect_player(char* pwszCharName);
        bool mgr_set_animus_attack_point(int nPoint);
        bool mgr_tracing(bool bOn);
        bool mgr_user_ban(char* uszCharName, int iPeriod, char* uszReason, char byBlockType);
        bool mgr_whisper(char* pwszMsg);
        void pc_AddBag(uint16_t wBagItemSerial);
        void pc_AlterItemSlotRequest(char byNum, struct _alter_item_slot_request_clzo::__list* pList);
        void pc_AlterLinkBoardSlotRequest(char byNum, struct _alter_link_slot_request_clzo::__list* pList, char byLBLock);
        void pc_AlterWindowInfoRequest(unsigned int* pdwSkill, unsigned int* pdwForce, unsigned int* pdwChar, unsigned int* pdwAnimus, unsigned int dwInven, unsigned int* pdwInvenBag);
        void pc_AnimusCommandRequest(char byCommandCode);
        void pc_AnimusInvenChange(struct _STORAGE_POS_INDIV* pItem, uint16_t wReplaceSerial);
        void pc_AnimusRecallRequest(uint16_t wAnimusItemSerial, uint16_t wAnimusClientHP, uint16_t wAnimusClientFP);
        void pc_AnimusReturnRequest();
        void pc_AnimusTargetRequest(char byObjectID, uint16_t wObjectIndex, unsigned int dwObjectSerial);
        void pc_AwayPartyJoinInvitationAnswer(struct _CLID* pidBoss, char byRetCode);
        void pc_AwaypartyInvitationRequest(char* pwszCharName);
        void pc_BackTowerRequest(unsigned int dwTowerObjSerial);
        void pc_BackTrapRequest(unsigned int dwTrapObjSerial, uint16_t wAddSerial);
        void pc_BillingInfoRequest();
        void pc_BriefPass(char byQuestSlotIndex);
        void pc_BuddyAddAnswer(bool bAccept, uint16_t wAskerIndex, unsigned int dwAskerSerial);
        void pc_BuddyAddRequest(uint16_t wDstIndex, unsigned int dwDstSerial, char* pwszDstName);
        void pc_BuddyDelRequest(unsigned int dwSerial);
        void pc_BuddyDownloadRequest();
        void pc_BuyItemStore(struct CItemStore* pStore, char byOfferNum, struct _buy_store_request_clzo::_list* pList, int bUseNPCLinkIntem);
        char pc_CanSelectClassRequest(char* pIsRealClassUp);
        void pc_CastVoteRequest(int nVoteSerial, char byCode);
        void pc_ChangeModeType(int nModeType, int nStandType);
        bool pc_CharacterRenameCash(bool bChange, struct _STORAGE_POS_INDIV* pItem, char* strCharacterName);
        char pc_CharacterRenameCheck(char* strCharacterName);
        void pc_ChatAllRequest(char* pwszChatData);
        void pc_ChatCircleRequest(char* pwszChatData);
        void pc_ChatFarRequest(char* pwszName, char* pwszChatData);
        void pc_ChatGmNoticeRequest(char* pwszChatData);
        void pc_ChatGuildEstSenRequest(char* pwszChatData);
        void pc_ChatGuildRequest(unsigned int dwDstSerial, char* pwszChatData);
        void pc_ChatMapRequest(char* pwszChatData);
        void pc_ChatMgrWhisperRequest(char* pwszChatData);
        void pc_ChatMultiFarRequest(char byDstNum, struct _w_name* pDstName, char* pwszMsg);
        void pc_ChatOperatorRequest(char byRaceCode, char* pwszChatData);
        void pc_ChatPartyRequest(char* pwszChatData);
        void pc_ChatRaceBossCryRequest(char* pwszChatData);
        void pc_ChatRaceBossRequest(char* pwszChatData);
        void pc_ChatRaceRequest(char* pwszChatData);
        void pc_ChatRePresentationRequest(char* pwszChatData);
        void pc_ChatTradeRequestMsg(char bySubType, char* pwszTradeMsg);
        void pc_ClassSkillRequest(uint16_t wSkillIndex, struct _CHRID* pidDst, uint16_t* pConsumeSerial);
        void pc_CombineItem(uint16_t wManualIndex, char byMaterialNum, struct _STORAGE_POS_INDIV* pipMaterials, uint16_t wOverlapSerial);
        void pc_CombineItemEx(struct _combine_ex_item_request_clzo* pRecv);
        void pc_CombineItemExAccept(struct _combine_ex_item_accept_request_clzo* pRecv);
        void pc_CuttingComplete(char byNpcRace);
        void pc_DTradeAddRequest(char bySlotIndex, char byStorageCode, unsigned int dwSerial, char byAmount);
        void pc_DTradeAnswerRequest(struct _CLID* pidAsker);
        void pc_DTradeAskRequest(uint16_t wDstIndex);
        void pc_DTradeBetRequest(char byMoneyUnit, unsigned int dwBetAmount);
        void pc_DTradeCancleRequest();
        void pc_DTradeDelRequest(char bySlotIndex);
        void pc_DTradeLockRequest();
        void pc_DTradeOKRequest(unsigned int* pdwKey);
        void pc_DarkHoleAnswerReenterRequest(bool bEnter, uint16_t wChannelIndex, unsigned int dwChannelSerial);
        void pc_DarkHoleClearOutRequest();
        void pc_DarkHoleEnterRequest(uint16_t wHoleIndex, unsigned int dwHoleSerial);
        void pc_DarkHoleGiveupOutRequest();
        void pc_DarkHoleOpenRequest(unsigned int dwItemSerial);
        void pc_DowngradeItem(struct _STORAGE_POS_INDIV* pposTalik, struct _STORAGE_POS_INDIV* pposToolItem, struct _STORAGE_POS_INDIV* pposUpgItem);
        void pc_EmbellishPart(struct _STORAGE_POS_INDIV* pItem, uint16_t wChangeSerial);
        void pc_EquipPart(struct _STORAGE_POS_INDIV* pItem);
        void pc_ExchangeDalantForGold(unsigned int dwDalant);
        void pc_ExchangeGoldForDalant(unsigned int dwGold);
        void pc_ExchangeGoldForPvP(unsigned int dwGold);
        void pc_ExchangeItem(uint16_t wManualIndex, uint16_t wItemSerial);
        void pc_ExitWorldRequest();
        void pc_ForceInvenChange(struct _STORAGE_POS_INDIV* pItem, uint16_t wReplaceSerial);
        void pc_ForceRequest(uint16_t wForceSerial, struct _CHRID* pidDst, uint16_t* pConsumeSerial);
        void pc_GestureRequest(char byGestureType);
        bool pc_GiveItem(_STORAGE_LIST::_db_con* kItem, char* szReason, bool bDrop);
        void pc_GotoAvatorRequest(char* pwszAvatorName);
        void pc_GotoBasePortalRequest(uint16_t wItemSerial);
        void pc_GuildBattleBlock(bool bBlock);
        void pc_GuildCancelSuggestRequest(unsigned int dwMatterVoteSynKey);
        void pc_GuildDownLoadRequest();
        void pc_GuildEstablishRequest(char* pwszGuildName);
        void pc_GuildHonorListRequest(char byUI);
        void pc_GuildJoinAcceptRequest(unsigned int dwApplierSerial, bool bAccept);
        void pc_GuildJoinApplyCancelRequest();
        void pc_GuildJoinApplyRequest(char* pwszGuildName);
        void pc_GuildListRequest(char byPage);
        void pc_GuildManageRequest(char byType, unsigned int dwDst, unsigned int dwObj1, unsigned int dwObj2, unsigned int dwObj3);
        void pc_GuildNextHonorListRequest();
        void pc_GuildOfferSuggestRequest(char byMatterType, unsigned int dwMatterDst, char* pwszComment, unsigned int dwMatterObj1, unsigned int dwMatterObj2, unsigned int dwMatterObj3);
        void pc_GuildPushMoneyRequest(unsigned int dwPushDalant, unsigned int dwPushGold);
        void pc_GuildQueryInfoRequest(unsigned int dwGuildSerial);
        void pc_GuildRoomEnterRequest(struct _guildroom_enter_request_clzo* pProtocol);
        void pc_GuildRoomOutRequest(struct _guildroom_out_request_clzo* pProtocol);
        void pc_GuildRoomRentRequest(struct _guildroom_rent_request_clzo* pProtocol);
        void pc_GuildRoomRestTimeRequest(struct _guildroom_resttime_request_clzo* pProtocol);
        void pc_GuildSelfLeaveRequest();
        void pc_GuildSetHonorRequest(struct _guild_honor_set_request_clzo* pData);
        void pc_GuildVoteRequest(unsigned int dwMatterVoteSynKey, char byVoteCode);
        char pc_InitClass();
        char pc_InitClassRequest();
        void pc_LimitItemNumRequest(unsigned int dwStoreIndex);
        void pc_LinkBoardRequest();
        void pc_MacroUpdate(char* pBuf);
        void pc_MakeItem(struct _STORAGE_POS_INDIV* pipMakeTool, uint16_t wManualIndex, char byMaterialNum, struct _STORAGE_POS_INDIV* pipMaterials);
        void pc_MakeTowerRequest(uint16_t wSkillIndex, uint16_t wTowerItemSerial, char byMaterialNum, struct _make_tower_request_clzo::__material* pMaterial, float* pfPos, uint16_t* pConsumeSerial);
        void pc_MakeTrapRequest(uint16_t wSkillIndex, uint16_t wTrapItemSerial, float* pfPos, uint16_t* pConsumeSerial);
        void pc_MineCancle();
        void pc_MineComplete();
        void pc_MineStart(char byMineIndex, char byOreIndex, uint16_t wBatterySerial);
        void pc_MoveModeChangeRequest(char byMoveType);
        void pc_MoveNext(char byMoveType, float* pfCur, float* pfTar, char byDirect);
        void pc_MovePortal(int nPortalIndex, uint16_t* pConsumeSerial);
        void pc_MoveStop(float* pfCur);
        void pc_MoveToOwnStoneMapRequest();
        bool pc_NPCLinkCheckItemRequest(struct _STORAGE_POS_INDIV* pStorage);
        char pc_NPCLinkCheckItemRequest_Check(struct _STORAGE_POS_INDIV* pStorage);
        char pc_NPCLinkCheckItemRequest_Use(struct _STORAGE_POS_INDIV* pStorage);
        void pc_NewPosStart();
        void pc_NotifyRaceBossCryMsg();
        void pc_NuclearAfterEffect();
        void pc_OffPart(struct _STORAGE_POS_INDIV* pItem);
        void pc_OreCutting(uint16_t wOreSerial, char byProcessNum);
        void pc_OreIntoBag(uint16_t wResIndex, uint16_t wSerial, char byAddAmount);
        void pc_PartyAlterLootShareReqeuest(char byLootShareMode);
        void pc_PartyDisJointReqeuest();
        void pc_PartyJoinApplication(uint16_t wBossIndex);
        void pc_PartyJoinApplicationAnswer(struct _CLID* pidApplicant);
        void pc_PartyJoinInvitation(uint16_t wDstIndex);
        void pc_PartyJoinInvitationAnswer(struct _CLID* pidBoss);
        void pc_PartyLeaveCompulsionReqeuest(unsigned int dwExiterSerial);
        void pc_PartyLeaveSelfReqeuest();
        void pc_PartyLockReqeuest(bool bLock);
        void pc_PartyReqBlock(bool bBlock);
        void pc_PartySuccessionReqeuest(unsigned int dwSuccessorSerial);
        void pc_PlayAttack_Force(struct CCharacter* pDst, float* pfAreaPos, uint16_t wForceSerial, uint16_t* pConsumeSerial, uint16_t wEffBtSerial);
        void pc_PlayAttack_Gen(struct CCharacter* pDst, char byAttPart, uint16_t wBulletSerial, uint16_t wEffBtSerial, bool bCount);
        void pc_PlayAttack_SelfDestruction();
        void pc_PlayAttack_Siege(struct CCharacter* pDst, float* pfAttackPos, char byAttPart, uint16_t wBulletSerial, uint16_t wEffBtSerial);
        void pc_PlayAttack_Skill(struct CCharacter* pDst, float* pfAttackPos, char byEffectCode, uint16_t wSkillIndex, uint16_t wBulletSerial, uint16_t* pConsumeSerial, uint16_t wEffBtSerial);
        void pc_PlayAttack_Test(char byEffectCode, char byEffectIndex, uint16_t wBulletItemSerial, char byWeaponPart, int16_t* pzTar);
        void pc_PlayAttack_Unit(struct CCharacter* pDst, char byWeaponPart);
        void pc_PostContentRequest(unsigned int dwIndex);
        void pc_PostDeleteRequest(unsigned int dwIndex);
        void pc_PostItemGoldRequest(unsigned int dwIndex);
        void pc_PostListRequest();
        void pc_PostReturnConfirmRequest(unsigned int dwPostSerial);
        void pc_PotionDivision(uint16_t wSerial, uint16_t wTarSerial, char byAmount);
        void pc_PotionSeparation(uint16_t wSerial, char byAmount);
        void pc_PotionUseTrunkExtend();
        void pc_ProposeVoteRequest(char byLimGrade, char* pwszCont);
        void pc_PvpCashRecorver(unsigned int dwItemSerial, char byItemCnt);
        void pc_QuestGiveupRequest(char byQuestDBSlot);
        bool pc_RadarCharInfo();
        void pc_RealMovPos(float* pfCur);
        void pc_RefreshGroupTargetPosition(char byGroupType, struct CGameObject* pObject);
        void pc_RegistBind(struct CItemStore* pStore);
        void pc_ReleaseGroupTargetObjectRequest(char byGroupType);
        void pc_ReleaseSiegeModeRequest();
        void pc_ReleaseTargetObjectRequest();
        char pc_RenameItemNConditionCheck(struct _STORAGE_POS_INDIV* pItemInfo, _STORAGE_LIST::_db_con** ppItem);
        void pc_RequestChangeTaxRate(char byTaxRate);
        void pc_RequestDialogWithNPC(struct CItemStore* pStore);
        void pc_RequestPatriarchPunishment(char byType, char* pwszName, char* pwszCont);
        void pc_RequestQuestFromNPC(struct CItemStore* pStore, unsigned int dwNPCQuestIndex);
        void pc_RequestQuestListFromNPC(struct CItemStore* pStore);
        void pc_RequestTaxRate();
        void pc_RequestUILockCertify(struct CUserDB* pUserDB, char* uszUILockPW);
        void pc_RequestUILockFindPW(struct CUserDB* pUserDB, char* uszHintAnswer);
        void pc_RequestUILockInit(struct CUserDB* pUserDB, char* szUILockPW, char* szUILockPW_Confirm, char byUILock_HintIndex, char* uszUILock_HintAnswer);
        void pc_RequestUILockUpdate(char* uszUILockPWOld, char* uszUILockPW, char* uszUILockPW_Confirm, char byUILock_HintIndex, char* uszUILock_HintAnswer);
        void pc_RequestWatchingWithNPC(struct CItemStore* pStore);
        void pc_ResDivision(uint16_t wStartSerial, uint16_t wTarSerial, char byMoveAmount);
        void pc_ResSeparation(uint16_t wStartSerial, char byMoveAmount);
        bool pc_Resurrect(bool bQuickPotion);
        void pc_Revival(bool bUseableJade);
        void pc_SelectClassRequest(uint16_t wSelClassIndex, char bySelectRewardItem);
        void pc_SelectQuestAfterHappenEvent(char bySelectIndex);
        void pc_SelectQuestReward(char byQuestDBSlot, char bySelectItemSlotIndex, char bySelectLinkQuestIndex);
        void pc_SellItemStore(struct CItemStore* pStore, char byOfferNum, struct _sell_store_request_clzo::_list* pList, int bUseNPCLinkIntem);
        void pc_SetGroupMapPointRequest(char byGroupType, float* pzTar);
        void pc_SetGroupTargetObjectRequest(struct CGameObject* pTar, unsigned int dwSerial, char byGroupType);
        void pc_SetInGuildBattle(bool bInGuildBattle, char byColorInx);
        bool pc_SetItemCheckRequest(unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum, bool bSet);
        void pc_SetRaceBossCryMsg(char bySlot, char* pwszCryMsg);
        void pc_SetTargetObjectRequest(struct CGameObject* pTar, unsigned int dwSerial, bool bForce);
        void pc_SkillRequest(char bySkillIndex, struct _CHRID* pidDst, uint16_t* pConsumeSerial);
        void pc_Stop();
        void pc_TakeGroundingItem(struct CItemBox* pBox, uint16_t wAddSerial);
        void pc_TalikCrystalExchange(char byExchangeNum, struct _talik_crystal_exchange_clzo::_list* pList);
        void pc_ThrowSkillRequest(uint16_t wBulletSerial, struct _CHRID* pidDst, uint16_t* pConsumeSerial);
        void pc_ThrowStorageItem(struct _STORAGE_POS_INDIV* pItem);
        void pc_ThrowUnitRequest(struct _CHRID* pidDst, uint16_t* pConsumeSerial);
        void pc_TradeBlock(bool bBlock);
        void pc_TransShipRenewTicketRequest(uint16_t wTicketItemSerial);
        void pc_TransformSiegeModeRequest(uint16_t wItemSerial);
        void pc_TrunkAlterItemSlotRequest(unsigned int dwItemSerial, char byClientSlotIndex, char byStorageIndex);
        void pc_TrunkChangePasswdRequest(char* pwszPrevPassword, char* pwszChngPassword, char byHintIndex, char* pwszHintAnswer);
        char pc_TrunkCreateCostIsFreeRequest();
        void pc_TrunkDownloadRequest(char* pwszPassword);
        void pc_TrunkEstRequest(char* pwszPassword, char byHintIndex, char* pwszHintAnswer);
        void pc_TrunkExtendRequest();
        void pc_TrunkHintAnswerRequest(char* pwszAnswer);
        void pc_TrunkIoMergeRequest(char byStartStorageIndex, char byTarStorageIndex, uint16_t wStartItemSerial, uint16_t wTarItemSerial, uint16_t wMoveAmount);
        void pc_TrunkIoMoneyRequest(char byCase, unsigned int dwDalant, unsigned int dwGold);
        void pc_TrunkIoMoveRequest(char byStartStorageIndex, char byTarStorageIndex, uint16_t wItemSerial, char byClientSlotIndex);
        void pc_TrunkIoSwapRequest(char byStartStorageIndex, char byTarStorageIndex, uint16_t wStartItemSerial, uint16_t wTarItemSerial);
        void pc_TrunkPotionDivision(uint16_t wStartSerial, uint16_t wTarSerial, uint16_t wMoveAmount, char byStorageIndex);
        void pc_TrunkPwHintIndexRequest();
        void pc_TrunkResDivision(uint16_t wStartSerial, uint16_t wTarSerial, uint16_t wMoveAmount, char byStorageIndex);
        void pc_UnitBulletFillRequest(char bySlotIndex, uint16_t* pwBulletIndex, int bUseNPCLinkIntem);
        void pc_UnitBulletReplaceRequest(char bySlotIndex, char byPackIndex, char byBulletPart);
        void pc_UnitDeliveryRequest(char bySlotIndex, struct CItemStore* pStore, bool bPayFee, float* pfNewPos, int bUseNPCLinkIntem);
        void pc_UnitFrameBuyRequest(char byFrameCode, int bUseNPCLinkIntem);
        void pc_UnitFrameRepairRequest(char bySlotIndex, int bUseNPCLinkIntem);
        void pc_UnitLeaveRequest(float* pfNewPos);
        void pc_UnitPackFillRequest(char bySlotIndex, char byFillNum, struct _unit_pack_fill_request_clzo::__list* pList, int bUseNPCLinkIntem);
        void pc_UnitPartTuningRequest(char bySlotIndex, char byTuningNum, struct _tuning_data* pTuningData, int bUseNPCLinkIntem);
        void pc_UnitReturnRequest();
        void pc_UnitSellRequest(char bySlotIndex, int bUseNPCLinkIntem);
        void pc_UnitTakeRequest();
        void pc_UpdateDataForPostSend();
        void pc_UpdateDataForTrade(struct CPlayer* pTrader);
        void pc_UpgradeItem(struct _STORAGE_POS_INDIV* pposTalik, struct _STORAGE_POS_INDIV* pposToolItem, struct _STORAGE_POS_INDIV* pposUpgItem, char byJewelNum, struct _STORAGE_POS_INDIV* pposUpgJewel);
        int pc_UseFireCracker(uint16_t wItemSerial);
        void pc_UsePotionItem(struct CPlayer* pTargetPlayer, struct _STORAGE_POS_INDIV* pItem);
        bool pc_UseRadarItem(struct _STORAGE_POS_INDIV* pItem, uint16_t* pConsumeSerial);
        char pc_UseRecoverLossExpItem(uint16_t wItemSerial);
        char pc_UserSoccerBall(uint16_t wItemSerial, uint16_t* wItemIndex);
        bool pc_WPActiveAttack_Force(struct _be_damaged_char* pDamList, int* nDamagedObjNum, struct _force_fld* pForceFld);
        bool pc_WPActiveAttack_Skill(struct _be_damaged_char* pDamList, int* nDamagedObjNum, int* nShotNum, struct _skill_fld* pSkillFld, char byEffectCode, uint16_t wBulletSerial);
        void pc_WhisperBlock(bool bBlock);
        char skill_process(int nEffectCode, int nSkillIndex, struct _CHRID* pidDst, uint16_t* pConsumeSerial, int* pnLv);
        void skill_process_for_aura(int nSkillIndex);
        char skill_process_for_item(int nSkillIndex, struct _CHRID* pidDst, int* pnLv);
        ~CPlayer();
        void dtor_CPlayer();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<CPlayer, 50856>(), "CPlayer");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$BA3C93127357C9E3D9600ED773D0AAAA.hpp>


START_ATF_NAMESPACE
    struct CRectTrackerVtbl
    {
        void (WINAPIV *DrawTrackerRect)(struct CRectTracker *_this, struct tagRECT *, struct CWnd *, struct CDC *, struct CWnd *);
        void (WINAPIV *AdjustRect)(struct CRectTracker *_this, int, struct tagRECT *);
        void (WINAPIV *OnChangedRect)(struct CRectTracker *_this, struct CRect *);
        unsigned int (WINAPIV *GetHandleMask)(struct CRectTracker *_this);
        $BA3C93127357C9E3D9600ED773D0AAAA ___u4;
        int (WINAPIV *GetHandleSize)(struct CRectTracker *_this, struct tagRECT *);
    };
END_ATF_NAMESPACE

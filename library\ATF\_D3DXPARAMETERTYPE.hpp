// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum _D3DXPARAMETERTYPE
    {
      D3DXPTDWORD = 0x0,
      D3DXPT_FLOAT = 0x1,
      D3DXPT_VECTOR = 0x2,
      D3DXPT_MATRIX = 0x3,
      D3DXPT_TEXTURE = 0x4,
      D3DXPT_VERTEXSHADER = 0x5,
      D3DXPT_PIXELSHADER = 0x6,
      D3DXPT_CONSTANT = 0x7,
      D3DXPT_STRING = 0x8,
      D3DXPT_FORCEDWORD = 0x7FFFFFFF,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _LTD_ITEMINFO
    {
        struct  _iteminfo
        {
            unsigned int m_dwItemSerial;
            char m_szItemCode[10];
            char m_szUpgradeCode[10];
            char m_szItemName[64];
            unsigned int m_dwDur;
            char m_byOverlapNum;
        };
        bool m_bExist;
        _SYSTEMTIME m_timeLocal;
        char m_bySubLogType;
        char m_byCnt;
        _iteminfo m_ItemInfo[64];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

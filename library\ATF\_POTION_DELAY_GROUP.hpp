// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum _POTION_DELAY_GROUP
    {
      eHP = 0x0,
      eFP = 0x1,
      eSP = 0x2,
      eCURE = 0x3,
      eONE_CONT = 0x4,
      eTWO_CONT = 0x5,
      eTHREE_CONT = 0x6,
      eFOUR_CONT = 0x7,
      eCHOSE = 0x8,
      eCHOSE_CURE = 0x9,
      eADRENALIN = 0xA,
      eDEFENCE = 0xB,
      eEVENT = 0xC,
      eClassRefine = 0xD,
      eAllRemove_CtDm = 0xE,
      eCharCall = 0xF,
      eTeleport = 0x10,
      eRENAME = 0x11,
      eEXP_INCREASE = 0x12,
      eRune = 0x13,
      eExpRate = 0x14,
      eMasteryRate = 0x15,
      eItemDropRate = 0x16,
      eMineSpeedRate = 0x17,
      eRevivePotion = 0x18,
      eBufExtPotion = 0x19,
      eTrunkExtendPotion = 0x1A,
      eRaceBuffClearPotion = 0x1B,
      eGoldCapsulePotion = 0x1C,
      eRecallAfterStone = 0x21,
      eTeleportAfterStone = 0x22,
      eInvisibleState = 0x23,
      eHFSFullRecover = 0x24,
      eContDamageRemove = 0x25,
      eMax_PotionClass = 0x26,
    };
END_ATF_NAMESPACE

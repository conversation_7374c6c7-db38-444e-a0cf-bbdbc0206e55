// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _reward_add_item_zocl
    {
        char byTableCode;
        unsigned __int16 wItemIndex;
        unsigned __int64 dwDur;
        unsigned int dwLv;
        unsigned __int16 wItemSerial;
        char byReason;
        char byCsMethod;
        unsigned int dwT;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

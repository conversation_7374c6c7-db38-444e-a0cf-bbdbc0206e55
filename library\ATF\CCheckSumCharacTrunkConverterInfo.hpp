// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCheckSumCharacTrunkConverter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CCheckSumCharacTrunkConverterConvert2_ptr = void (WINAPIV*)(struct CCheckSumCharacTrunkConverter*, struct _AVATOR_DATA*, struct CCheckSumCharacAccountTrunkData*);
        using CCheckSumCharacTrunkConverterConvert2_clbk = void (WINAPIV*)(struct CCheckSumCharacTrunkConverter*, struct _AVATOR_DATA*, struct CCheckSumCharacAccountTrunkData*, CCheckSumCharacTrunkConverterConvert2_ptr);
        using CCheckSumCharacTrunkConverterConvertTrunk4_ptr = void (WINAPIV*)(struct CCheckSumCharacTrunkConverter*, unsigned int, long double*);
        using CCheckSumCharacTrunkConverterConvertTrunk4_clbk = void (WINAPIV*)(struct CCheckSumCharacTrunkConverter*, unsigned int, long double*, CCheckSumCharacTrunkConverterConvertTrunk4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

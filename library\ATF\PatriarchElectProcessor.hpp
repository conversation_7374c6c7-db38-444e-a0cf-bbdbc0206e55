// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CPlayer.hpp>
#include <Cmd.hpp>
#include <ElectProcessor.hpp>
#include <_DB_QRY_SYN_DATA.hpp>
#include <_candidate_info.hpp>


START_ATF_NAMESPACE
    struct PatriarchElectProcessor
    {
        unsigned int m_dwNonvoteCnt[3];
        unsigned int m_dwTotalVoteCnt[3];
        unsigned int m_dwHighGradeNum[3];
        ElectProcessor::ProcessorType _eProcessType;
        ElectProcessor *_kRunningProcessor;
        ElectProcessor *_kProcessor[6];
        bool _bTimeCheck;
        bool _bInitProce;
        unsigned int _dwNextCheckTime;
        unsigned int _dwNextCheckDay;
        unsigned int _dwNextScoreUpdateTime;
        unsigned int _dwElectSerial;
        unsigned int _dwCurrPatriarchElectSerial;
        CLogFile _kSysLog;
    public:
        bool CheatClearPatriarch();
        bool CheatSetPatriarch(struct CPlayer* pOne, _candidate_info::ClassType eClass);
        void CompleteCheckInvalidChar(char byProc);
        void CompleteInsertElect();
        void CompleteInsertPatriarch(struct _DB_QRY_SYN_DATA* pData);
        void CompleteItemChargeRefund(struct _DB_QRY_SYN_DATA* pData);
        void CompleteRequestRefund(struct _DB_QRY_SYN_DATA* pData);
        void CompleteSelectElect();
        void Destroy();
        bool Doit(Cmd eCmd, struct CPlayer* pOne, char* pdata);
        bool ForceChangeProcessor(ElectProcessor::ProcessorType eProc);
        unsigned int GetCurrPatriarchElectSerial();
        unsigned int GetElectSerial();
        ElectProcessor::ProcessorType GetProcessorType();
        bool GetTimeCheck();
        bool InitProcess();
        bool Initialize();
        int Insert_Elect();
        int Insert_PatrirchItemChargeRefund(char* pData);
        static struct PatriarchElectProcessor* Instance();
        bool LoadDatabae();
        bool LoadElectState();
        void Loop();
        PatriarchElectProcessor();
        void ctor_PatriarchElectProcessor();
        void PushDQSCheckInvalidChar();
        int Request_Refund(char* pData);
        void SendMsg_ConnectNewUser(struct CPlayer* pOne);
        void SendMsg_ResultCode(unsigned int n, char byCode);
        void SetCurrPatriarchElectSerial(unsigned int dwSerial);
        void SetTimeCheck(bool bFlag);
        void TimeCheck(uint16_t wDayOfWeek, uint16_t wHour);
        int Update_Elect();
        ~PatriarchElectProcessor();
        void dtor_PatriarchElectProcessor();
    };
END_ATF_NAMESPACE

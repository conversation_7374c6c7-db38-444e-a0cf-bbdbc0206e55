// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _PVP_RANK_DATA
    {
        char byRank;
        char byLv;
        unsigned __int16 wRate;
        char wszName[17];
        char byGrade;
        char wszGuildName[17];
        unsigned int dwAvatorSerial;
        unsigned int dwGuildSerial;
        long double dPvpPoint;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

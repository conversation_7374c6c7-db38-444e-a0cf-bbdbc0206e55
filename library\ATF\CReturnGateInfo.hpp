// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CReturnGate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CReturnGatector_CReturnGate2_ptr = void (WINAPIV*)(struct CReturnGate*, struct _object_id*);
        using CReturnGatector_CReturnGate2_clbk = void (WINAPIV*)(struct CReturnGate*, struct _object_id*, CReturnGatector_CReturnGate2_ptr);
        using CReturnGateClear4_ptr = void (WINAPIV*)(struct CReturnGate*);
        using CReturnGateClear4_clbk = void (WINAPIV*)(struct CReturnGate*, CReturnGateClear4_ptr);
        using CReturnGateClose6_ptr = void (WINAPIV*)(struct CReturnGate*);
        using CReturnGateClose6_clbk = void (WINAPIV*)(struct CReturnGate*, CReturnGateClose6_ptr);
        using CReturnGateEnter8_ptr = int (WINAPIV*)(struct CReturnGate*, struct CPlayer*);
        using CReturnGateEnter8_clbk = int (WINAPIV*)(struct CReturnGate*, struct CPlayer*, CReturnGateEnter8_ptr);
        using CReturnGateGetIndex10_ptr = uint16_t (WINAPIV*)(struct CReturnGate*);
        using CReturnGateGetIndex10_clbk = uint16_t (WINAPIV*)(struct CReturnGate*, CReturnGateGetIndex10_ptr);
        using CReturnGateGetInfo12_ptr = void (WINAPIV*)(struct CReturnGate*, struct _open_return_gate_inform_zocl*);
        using CReturnGateGetInfo12_clbk = void (WINAPIV*)(struct CReturnGate*, struct _open_return_gate_inform_zocl*, CReturnGateGetInfo12_ptr);
        using CReturnGateGetOwner14_ptr = struct CPlayer* (WINAPIV*)(struct CReturnGate*);
        using CReturnGateGetOwner14_clbk = struct CPlayer* (WINAPIV*)(struct CReturnGate*, CReturnGateGetOwner14_ptr);
        using CReturnGateIsClose16_ptr = bool (WINAPIV*)(struct CReturnGate*);
        using CReturnGateIsClose16_clbk = bool (WINAPIV*)(struct CReturnGate*, CReturnGateIsClose16_ptr);
        using CReturnGateIsOpen18_ptr = bool (WINAPIV*)(struct CReturnGate*);
        using CReturnGateIsOpen18_clbk = bool (WINAPIV*)(struct CReturnGate*, CReturnGateIsOpen18_ptr);
        using CReturnGateIsValidOwner20_ptr = bool (WINAPIV*)(struct CReturnGate*);
        using CReturnGateIsValidOwner20_clbk = bool (WINAPIV*)(struct CReturnGate*, CReturnGateIsValidOwner20_ptr);
        using CReturnGateIsValidPosition22_ptr = bool (WINAPIV*)(struct CReturnGate*, float*);
        using CReturnGateIsValidPosition22_clbk = bool (WINAPIV*)(struct CReturnGate*, float*, CReturnGateIsValidPosition22_ptr);
        using CReturnGateOpen24_ptr = bool (WINAPIV*)(struct CReturnGate*, struct CReturnGateCreateParam*);
        using CReturnGateOpen24_clbk = bool (WINAPIV*)(struct CReturnGate*, struct CReturnGateCreateParam*, CReturnGateOpen24_ptr);
        using CReturnGateSendMsg_Create26_ptr = void (WINAPIV*)(struct CReturnGate*);
        using CReturnGateSendMsg_Create26_clbk = void (WINAPIV*)(struct CReturnGate*, CReturnGateSendMsg_Create26_ptr);
        using CReturnGateSendMsg_Destroy28_ptr = void (WINAPIV*)(struct CReturnGate*);
        using CReturnGateSendMsg_Destroy28_clbk = void (WINAPIV*)(struct CReturnGate*, CReturnGateSendMsg_Destroy28_ptr);
        using CReturnGateSendMsg_FixPosition30_ptr = void (WINAPIV*)(struct CReturnGate*, int);
        using CReturnGateSendMsg_FixPosition30_clbk = void (WINAPIV*)(struct CReturnGate*, int, CReturnGateSendMsg_FixPosition30_ptr);
        using CReturnGateSendMsg_MovePortal32_ptr = void (WINAPIV*)(struct CReturnGate*, struct CPlayer*);
        using CReturnGateSendMsg_MovePortal32_clbk = void (WINAPIV*)(struct CReturnGate*, struct CPlayer*, CReturnGateSendMsg_MovePortal32_ptr);
        
        using CReturnGatedtor_CReturnGate37_ptr = void (WINAPIV*)(struct CReturnGate*);
        using CReturnGatedtor_CReturnGate37_clbk = void (WINAPIV*)(struct CReturnGate*, CReturnGatedtor_CReturnGate37_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

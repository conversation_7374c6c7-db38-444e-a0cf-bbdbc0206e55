// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _move_to_own_stonemap_result_zocl
    {
        char byRetCode;
        char byMapIndex;
        __int16 sNewPos[3];
    public:
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_move_to_own_stonemap_result_zocl, 8>(), "_move_to_own_stonemap_result_zocl");
END_ATF_NAMESPACE

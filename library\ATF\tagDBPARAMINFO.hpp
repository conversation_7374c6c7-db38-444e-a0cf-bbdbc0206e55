// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ITypeInfo.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagDBPARAMINFO
    {
        unsigned int dwFlags;
        unsigned __int64 iOrdinal;
        wchar_t *pwszName;
        ITypeInfo *pTypeInfo;
        unsigned __int64 ulParamSize;
        unsigned __int16 wType;
        char bPrecision;
        char bScale;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

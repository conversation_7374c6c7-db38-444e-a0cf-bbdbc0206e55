// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum RF_DEFALUT_MON_AI_TBL
    {
      DF_NONE = 0x0,
      DF_ATP_SEARCH_START = 0x1,
      DF_ATP_SEARCH_STOP = 0x2,
      DF_M<PERSON>_SEARCH_START = 0x3,
      DF_MON_SEARCH_STOP = 0x4,
      DF_MV_STOP = 0x5,
      DF_MV_GO = 0x6,
      DF_ACTION_WAIT = 0x7,
      DF_ACTION_PATROL = 0x8,
      DF_ACTION_ATTACK = 0x9,
      DF_ACTION_RUNAWAY = 0xA,
      DF_EM_NORMAL = 0xB,
      DF_EM_DISCOMPORT = 0xC,
      DF_EM_ANGER = 0xD,
      DF_EM_FURY = 0xE,
      DF_EM_MAD = 0xF,
      DF_CON_VERYGOOD = 0x10,
      DF_CON_GOOD = 0x11,
      DF_CON_NORMAL = 0x12,
      DF_CON_BAD = 0x13,
      DF_CON_VERYBAD = 0x14,
      DF_CON_DEAD = 0x15,
      DF_ASSIST_ON = 0x16,
      DF_ASSIST_OFF = 0x17,
      DF_ASSIST_LOCK = 0x18,
      DF_AI_STATE_MAX = 0x19,
      EV_START_ATP = 0x1A,
      EV_STOP_ATP = 0x1B,
      EV_SEARCHED_ATP = 0x1C,
      EV_LOST_ATP = 0x1D,
      EV_START_MON = 0x1E,
      EV_STOP_MON = 0x1F,
      EV_SEARCHED_MON = 0x20,
      EV_STOP_MV = 0x21,
      EV_GO_MV = 0x22,
      EV_EM_BAD = 0x23,
      EV_EM_BETTER = 0x24,
      EV_ACTION_WAIT = 0x25,
      EV_ACTION_PATROL = 0x26,
      EV_CON_VERYGOOD = 0x27,
      EV_CON_GOOD = 0x28,
      EV_CON_NORMAL = 0x29,
      EV_CON_BAD = 0x2A,
      EV_CON_VERYBAD = 0x2B,
      EV_CON_DEAD = 0x2C,
      EV_ASSIST_ON = 0x2D,
      EV_ASSIST_OFF = 0x2E,
      EV_ASSIST_LOCK = 0x2F,
      DF_AI_MSG_MAX = 0x30,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Lockit.hpp>
#include <std__basic_string.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct _Locinfo
        {
            _Lockit _Lock;
            basic_string<char,char_traits<char>,allocator<char> > _Days;
            basic_string<char,char_traits<char>,allocator<char> > _Months;
            basic_string<char,char_traits<char>,allocator<char> > _Oldlocname;
            basic_string<char,char_traits<char>,allocator<char> > _Newlocname;
        };
    }; // end namespace std
END_ATF_NAMESPACE

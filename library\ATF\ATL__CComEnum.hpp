// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  CComEnum<IEnumUnknown,&_GUID_00000100_0000_0000_c000_000000000046,IUnknown *,_CopyInterface<IUnknown>,CComMultiThreadModel> : struct CComEnumImpl<IEnumUnknown,&_GUID_00000100_0000_0000_c000_000000000046,IUnknown *,_CopyInterface<IUnknown> >, CComObjectRootEx<CComMultiThreadModel>
        {
            BYTE gap0[48];
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

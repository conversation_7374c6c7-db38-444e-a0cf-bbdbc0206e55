// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <SF_Timer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using SF_TimerCheckTime2_ptr = int (WINAPIV*)(struct SF_Timer*, unsigned int);
        using SF_TimerCheckTime2_clbk = int (WINAPIV*)(struct SF_Timer*, unsigned int, SF_TimerCheckTime2_ptr);
        
        using SF_Timerctor_SF_Timer4_ptr = void (WINAPIV*)(struct SF_Timer*);
        using SF_Timerctor_SF_Timer4_clbk = void (WINAPIV*)(struct SF_Timer*, SF_Timerctor_SF_Timer4_ptr);
        using SF_TimerSet6_ptr = void (WINAPIV*)(struct SF_Timer*, unsigned int);
        using SF_TimerSet6_clbk = void (WINAPIV*)(struct SF_Timer*, unsigned int, SF_TimerSet6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

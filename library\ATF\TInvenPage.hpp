// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <TInvenSlot.hpp>


START_ATF_NAMESPACE
    struct TInvenPage
    {
        int m_nMaxSlotNum;
        int m_nMaxOverlapNum;
        TInvenSlot *m_pSlot;
    public:
        TInvenPage()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenPage*);
            (org_ptr(0x1402d5080L))(this);
        };
        void ctor_TInvenPage()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenPage*);
            (org_ptr(0x1402d5080L))(this);
        };
        void clear()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenPage*);
            (org_ptr(0x1402d5520L))(this);
        };
        bool create(int nMaxSlotNum, int nMaxOverlapNum)
        {
            using org_ptr = bool (WINAPIV*)(struct TInvenPage*, int, int);
            return (org_ptr(0x1402d5160L))(this, nMaxSlotNum, nMaxOverlapNum);
        };
        int find_empty(struct _INVENKEY* pItem, int nNum)
        {
            using org_ptr = int (WINAPIV*)(struct TInvenPage*, struct _INVENKEY*, int);
            return (org_ptr(0x1402d5340L))(this, pItem, nNum);
        };
        int find_pos_empty()
        {
            using org_ptr = int (WINAPIV*)(struct TInvenPage*);
            return (org_ptr(0x1402d58c0L))(this);
        };
        int find_pos_overlap(struct _INVENKEY* pItem, int nNum)
        {
            using org_ptr = int (WINAPIV*)(struct TInvenPage*, struct _INVENKEY*, int);
            return (org_ptr(0x1402d5960L))(this, pItem, nNum);
        };
        struct TInvenSlot* get_slot(int n)
        {
            using org_ptr = struct TInvenSlot* (WINAPIV*)(struct TInvenPage*, int);
            return (org_ptr(0x1402d5300L))(this, n);
        };
        int pop(struct _INVENKEY* pItem, int nS, int nNum)
        {
            using org_ptr = int (WINAPIV*)(struct TInvenPage*, struct _INVENKEY*, int, int);
            return (org_ptr(0x1402d5480L))(this, pItem, nS, nNum);
        };
        int push(struct _INVENKEY* pItem, int nS, int nNum)
        {
            using org_ptr = int (WINAPIV*)(struct TInvenPage*, struct _INVENKEY*, int, int);
            return (org_ptr(0x1402d53d0L))(this, pItem, nS, nNum);
        };
        int push_normal(struct _INVENKEY* pItem, int nS)
        {
            using org_ptr = int (WINAPIV*)(struct TInvenPage*, struct _INVENKEY*, int);
            return (org_ptr(0x1402d57a0L))(this, pItem, nS);
        };
        int push_overlap(struct _INVENKEY* pItem, int nNum, int nS)
        {
            using org_ptr = int (WINAPIV*)(struct TInvenPage*, struct _INVENKEY*, int, int);
            return (org_ptr(0x1402d5830L))(this, pItem, nNum, nS);
        };
        ~TInvenPage()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenPage*);
            (org_ptr(0x1402d50b0L))(this);
        };
        void dtor_TInvenPage()
        {
            using org_ptr = void (WINAPIV*)(struct TInvenPage*);
            (org_ptr(0x1402d50b0L))(this);
        };
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _limit_amount_info
    {
        struct _item_info
        {
            unsigned int dwLimitItemIndex;
            unsigned __int16 wLimitNum;
        };
        char byItemNum;
        _item_info ItemInfo[16];
    public:
        _limit_amount_info();
        void ctor__limit_amount_info();
    };    
    static_assert(ATF::checkSize<_limit_amount_info, 132>(), "_limit_amount_info");
END_ATF_NAMESPACE

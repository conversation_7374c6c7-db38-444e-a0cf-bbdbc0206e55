// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_ANIMUSKEY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _ANIMUSKEYCovDBKey2_ptr = char (WINAPIV*)(struct _ANIMUSKEY*);
        using _ANIMUSKEYCovDBKey2_clbk = char (WINAPIV*)(struct _ANIMUSKEY*, _ANIMUSKEYCovDBKey2_ptr);
        using _ANIMUSKEYIsFilled4_ptr = bool (WINAPIV*)(struct _ANIMUSKEY*);
        using _ANIMUSKEYIsFilled4_clbk = bool (WINAPIV*)(struct _ANIMUSKEY*, _ANIMUSKEYIsFilled4_ptr);
        using _ANIMUSKEYLoadDBKey6_ptr = void (WINAPIV*)(struct _ANIMUSKEY*, char);
        using _ANIMUSKEYLoadDBKey6_clbk = void (WINAPIV*)(struct _ANIMUSKEY*, char, _ANIMUSKEYLoadDBKey6_ptr);
        using _ANIMUSKEYSetRelease8_ptr = void (WINAPIV*)(struct _ANIMUSKEY*);
        using _ANIMUSKEYSetRelease8_clbk = void (WINAPIV*)(struct _ANIMUSKEY*, _ANIMUSKEYSetRelease8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

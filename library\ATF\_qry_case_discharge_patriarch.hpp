// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _qry_case_discharge_patriarch
    {
        char byRace;
        unsigned int dwAvatorSerial;
        unsigned int dwElectSerial;
    public:
        _qry_case_discharge_patriarch(char byR, unsigned int dwS, unsigned int dwE);
        void ctor__qry_case_discharge_patriarch(char byR, unsigned int dwS, unsigned int dwE);
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_qry_case_discharge_patriarch, 12>(), "_qry_case_discharge_patriarch");
END_ATF_NAMESPACE

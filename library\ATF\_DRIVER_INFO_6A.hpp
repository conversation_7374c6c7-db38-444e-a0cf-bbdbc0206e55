// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FILETIME.hpp>


START_ATF_NAMESPACE
    struct _DRIVER_INFO_6A
    {
        unsigned int cVersion;
        char *pName;
        char *pEnvironment;
        char *pDriverPath;
        char *pDataFile;
        char *pConfigFile;
        char *pHelpFile;
        char *pDependentFiles;
        char *pMonitorName;
        char *pDefaultDataType;
        char *pszzPreviousNames;
        _FILETIME ftDriverDate;
        unsigned __int64 dwlDriverVersion;
        char *pszMfgName;
        char *pszOEMUrl;
        char *pszHardwareID;
        char *pszProvider;
    };
END_ATF_NAMESPACE

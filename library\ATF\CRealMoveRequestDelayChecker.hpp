// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CIndexList.hpp>
#include <std__vector.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CRealMoveRequestDelayChecker
    {
        enum DEFINE_TAG
        {
            CHECK_LIMIT_DELAY = 0x1F4,
            TOTAL_DELAY_LIST_COUNT = 0xA,
            CONTINUE_MISS_LIMIT_COUNT = 0x3,
            SINGLE_MISS_LIMIT_COUNT = 0xA,
            PROC_CLOSE_LIMIT_COUNT = 0x5,
            UNIT_FAIL_COUNT_CLEAR_COUNT = 0x3,
            TOTAL_COUNT_CLEAR_UNIT_COUNT = 0x3,
            TOTAL_COUNT_CLEAR_COUNT = 0x2,
        };
        CIndexList m_kNodeInxOrderList;
        std::vector<unsigned long> m_vecDelayList;
        unsigned __int16 m_wTotalMissCount;
        bool m_bPrevRet;
        unsigned __int16 m_wContinueMissCount;
        unsigned __int16 m_wSingleMissCount;
        unsigned __int16 m_wContinueValiedCount;
        unsigned __int16 m_wTotalContinueValiedCount;
    public:
        CRealMoveRequestDelayChecker();
        void ctor_CRealMoveRequestDelayChecker();
        bool Check(struct CPlayer* pkUser);
        bool CheckDelay(unsigned int dwCurTime, unsigned int dwIndex);
        bool GetCurInx(unsigned int* pInx);
        void IncNodeIndex();
        bool Init(unsigned int dwListCnt);
        void Reset();
        ~CRealMoveRequestDelayChecker();
        void dtor_CRealMoveRequestDelayChecker();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CRealMoveRequestDelayChecker, 304>(), "CRealMoveRequestDelayChecker");
END_ATF_NAMESPACE

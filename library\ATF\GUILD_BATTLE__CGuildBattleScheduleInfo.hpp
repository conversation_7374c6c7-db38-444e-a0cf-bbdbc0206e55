// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CGuildBattleSchedule.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, unsigned int);
            using GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, unsigned int, GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleCheck4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CG<PERSON>BattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleCheck4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleCheck4_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleClear6_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleClear6_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleClear6_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleClearDB8_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleClearDB8_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleClearDB8_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_ptr = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, struct ATL::CTimeSpan*);
            using GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_clbk = struct ATL::CTimeSpan* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, struct ATL::CTimeSpan*, GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, char*, char*, char*);
            using GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, char*, char*, char*, GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_ptr = int64_t (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_clbk = int64_t (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleGetSID18_ptr = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleGetSID18_clbk = unsigned int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleGetSID18_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleGetState20_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleGetState20_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleGetState20_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleGetTime22_ptr = struct ATL::CTime* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, struct ATL::CTime*);
            using GUILD_BATTLE__CGuildBattleScheduleGetTime22_clbk = struct ATL::CTime* (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, struct ATL::CTime*, GUILD_BATTLE__CGuildBattleScheduleGetTime22_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleIsDone24_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleIsDone24_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleIsDone24_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleIsProc28_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleIsProc28_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleIsProc28_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleIsWait30_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleIsWait30_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleIsWait30_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleLoad32_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, bool, unsigned int, char, int64_t, uint16_t);
            using GUILD_BATTLE__CGuildBattleScheduleLoad32_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, bool, unsigned int, char, int64_t, uint16_t, GUILD_BATTLE__CGuildBattleScheduleLoad32_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleProcess34_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleProcess34_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleProcess34_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleSet36_ptr = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, unsigned int, unsigned int);
            using GUILD_BATTLE__CGuildBattleScheduleSet36_clbk = char (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, unsigned int, unsigned int, GUILD_BATTLE__CGuildBattleScheduleSet36_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleSetProcState38_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduleSetProcState38_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduleSetProcState38_ptr);
            using GUILD_BATTLE__CGuildBattleScheduleSetStateList40_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, struct GUILD_BATTLE::CGuildBattleStateList*);
            using GUILD_BATTLE__CGuildBattleScheduleSetStateList40_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, struct GUILD_BATTLE::CGuildBattleStateList*, GUILD_BATTLE__CGuildBattleScheduleSetStateList40_ptr);
            
            using GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*);
            using GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CGuildBattleSchedule*, GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

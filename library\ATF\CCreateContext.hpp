// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CDocTemplate.hpp>
#include <CDocument.hpp>
#include <CFrameWnd.hpp>
#include <CRuntimeClass.hpp>
#include <CView.hpp>


START_ATF_NAMESPACE
    struct CCreateContext
    {
        CRuntimeClass *m_pNewViewClass;
        CDocument *m_pCurrentDoc;
        CDocTemplate *m_pNewDocTemplate;
        CView *m_pLastView;
        CFrameWnd *m_pCurrentFrame;
    };
END_ATF_NAMESPACE

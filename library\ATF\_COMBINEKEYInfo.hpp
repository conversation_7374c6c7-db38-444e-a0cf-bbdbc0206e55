// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_COMBINEKEY.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _COMBINEKEYCovDBKey2_ptr = int (WINAPIV*)(struct _COMBINEKEY*);
        using _COMBINEKEYCovDBKey2_clbk = int (WINAPIV*)(struct _COMBINEKEY*, _COMBINEKEYCovDBKey2_ptr);
        using _COMBINEKEYIsFilled4_ptr = bool (WINAPIV*)(struct _COMBINEKEY*);
        using _COMBINEKEYIsFilled4_clbk = bool (WINAPIV*)(struct _COMBINEKEY*, _COMBINEKEYIsFilled4_ptr);
        using _COMBINEKEYLoadDBKey6_ptr = void (WINAPIV*)(struct _COMBINEKEY*, int);
        using _COMBINEKEYLoadDBKey6_clbk = void (WINAPIV*)(struct _COMBINEKEY*, int, _COMBINEKEYLoadDBKey6_ptr);
        using _COMBINEKEYSetRelease8_ptr = void (WINAPIV*)(struct _COMBINEKEY*);
        using _COMBINEKEYSetRelease8_clbk = void (WINAPIV*)(struct _COMBINEKEY*, _COMBINEKEYSetRelease8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMonsterHierarchy.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMonsterHierarchyctor_CMonsterHierarchy2_ptr = void (WINAPIV*)(struct CMonsterHierarchy*);
        using CMonsterHierarchyctor_CMonsterHierarchy2_clbk = void (WINAPIV*)(struct CMonsterHierarchy*, CMonsterHierarchyctor_CMonsterHierarchy2_ptr);
        using CMonsterHierarchyChildKindCount4_ptr = char (WINAPIV*)(struct CMonsterHierarchy*);
        using CMonsterHierarchyChildKindCount4_clbk = char (WINAPIV*)(struct CMonsterHierarchy*, CMonsterHierarchyChildKindCount4_ptr);
        using CMonsterHierarchyGetChild6_ptr = struct CMonster* (WINAPIV*)(struct CMonsterHierarchy*, int, int);
        using CMonsterHierarchyGetChild6_clbk = struct CMonster* (WINAPIV*)(struct CMonsterHierarchy*, int, int, CMonsterHierarchyGetChild6_ptr);
        using CMonsterHierarchyGetChildCount8_ptr = unsigned int (WINAPIV*)(struct CMonsterHierarchy*, int);
        using CMonsterHierarchyGetChildCount8_clbk = unsigned int (WINAPIV*)(struct CMonsterHierarchy*, int, CMonsterHierarchyGetChildCount8_ptr);
        using CMonsterHierarchyGetParent10_ptr = struct CMonster* (WINAPIV*)(struct CMonsterHierarchy*);
        using CMonsterHierarchyGetParent10_clbk = struct CMonster* (WINAPIV*)(struct CMonsterHierarchy*, CMonsterHierarchyGetParent10_ptr);
        using CMonsterHierarchyInit12_ptr = void (WINAPIV*)(struct CMonsterHierarchy*);
        using CMonsterHierarchyInit12_clbk = void (WINAPIV*)(struct CMonsterHierarchy*, CMonsterHierarchyInit12_ptr);
        using CMonsterHierarchyOnChildMonsterCreate14_ptr = void (WINAPIV*)(struct CMonsterHierarchy*, struct _monster_create_setdata*);
        using CMonsterHierarchyOnChildMonsterCreate14_clbk = void (WINAPIV*)(struct CMonsterHierarchy*, struct _monster_create_setdata*, CMonsterHierarchyOnChildMonsterCreate14_ptr);
        using CMonsterHierarchyOnChildMonsterDestroy16_ptr = void (WINAPIV*)(struct CMonsterHierarchy*);
        using CMonsterHierarchyOnChildMonsterDestroy16_clbk = void (WINAPIV*)(struct CMonsterHierarchy*, CMonsterHierarchyOnChildMonsterDestroy16_ptr);
        using CMonsterHierarchyOnChildRegenLoop18_ptr = void (WINAPIV*)(struct CMonsterHierarchy*);
        using CMonsterHierarchyOnChildRegenLoop18_clbk = void (WINAPIV*)(struct CMonsterHierarchy*, CMonsterHierarchyOnChildRegenLoop18_ptr);
        using CMonsterHierarchyOnlyOnceInit20_ptr = void (WINAPIV*)(struct CMonsterHierarchy*, struct CMonster*);
        using CMonsterHierarchyOnlyOnceInit20_clbk = void (WINAPIV*)(struct CMonsterHierarchy*, struct CMonster*, CMonsterHierarchyOnlyOnceInit20_ptr);
        using CMonsterHierarchyPopChildMon22_ptr = int (WINAPIV*)(struct CMonsterHierarchy*, struct CMonster*);
        using CMonsterHierarchyPopChildMon22_clbk = int (WINAPIV*)(struct CMonsterHierarchy*, struct CMonster*, CMonsterHierarchyPopChildMon22_ptr);
        using CMonsterHierarchyPopChildMonAll24_ptr = void (WINAPIV*)(struct CMonsterHierarchy*);
        using CMonsterHierarchyPopChildMonAll24_clbk = void (WINAPIV*)(struct CMonsterHierarchy*, CMonsterHierarchyPopChildMonAll24_ptr);
        using CMonsterHierarchyPushChildMon26_ptr = int (WINAPIV*)(struct CMonsterHierarchy*, int, struct CMonster*);
        using CMonsterHierarchyPushChildMon26_clbk = int (WINAPIV*)(struct CMonsterHierarchy*, int, struct CMonster*, CMonsterHierarchyPushChildMon26_ptr);
        using CMonsterHierarchySearchChildMon28_ptr = int (WINAPIV*)(struct CMonsterHierarchy*, struct CMonster*);
        using CMonsterHierarchySearchChildMon28_clbk = int (WINAPIV*)(struct CMonsterHierarchy*, struct CMonster*, CMonsterHierarchySearchChildMon28_ptr);
        using CMonsterHierarchySetParent30_ptr = int (WINAPIV*)(struct CMonsterHierarchy*, struct CMonster*);
        using CMonsterHierarchySetParent30_clbk = int (WINAPIV*)(struct CMonsterHierarchy*, struct CMonster*, CMonsterHierarchySetParent30_ptr);
        
        using CMonsterHierarchydtor_CMonsterHierarchy35_ptr = void (WINAPIV*)(struct CMonsterHierarchy*);
        using CMonsterHierarchydtor_CMonsterHierarchy35_clbk = void (WINAPIV*)(struct CMonsterHierarchy*, CMonsterHierarchydtor_CMonsterHierarchy35_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

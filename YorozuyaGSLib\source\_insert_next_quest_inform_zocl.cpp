#include <_insert_next_quest_inform_zocl.hpp>


START_ATF_NAMESPACE
    _insert_next_quest_inform_zocl::_insert_next_quest_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _insert_next_quest_inform_zocl*);
        (org_ptr(0x1400efeb0L))(this);
    };
    void _insert_next_quest_inform_zocl::ctor__insert_next_quest_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _insert_next_quest_inform_zocl*);
        (org_ptr(0x1400efeb0L))(this);
    };
END_ATF_NAMESPACE

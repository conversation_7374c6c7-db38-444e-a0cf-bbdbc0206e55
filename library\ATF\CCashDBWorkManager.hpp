// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTSingleton.hpp>
#include <CashDbWorker.hpp>
#include <_param_cash_update.hpp>


START_ATF_NAMESPACE
    struct  CCashDBWorkManager : CTSingleton<CCashDBWorkManager>
    {
        CashDbWorker *m_pWorker;
    public:
        CCashDBWorkManager()
        {
            using org_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
            (org_ptr(0x1402f31b0L))(this);
        };
        void ctor_CCashDBWorkManager()
        {
            using org_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
            (org_ptr(0x1402f31b0L))(this);
        };
        void CompleteWork()
        {
            using org_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
            (org_ptr(0x1402f33a0L))(this);
        };
        bool GetBillingDBConnectionStatus()
        {
            using org_ptr = bool (WINAPIV*)(struct CCashDBWorkManager*);
            return (org_ptr(0x1402f33f0L))(this);
        };
        void GetUseCashQueryStr(struct _param_cash_update* rParam, int nIdx, char* wszQuery, uint64_t tBufferSize)
        {
            using org_ptr = void (WINAPIV*)(struct CCashDBWorkManager*, struct _param_cash_update*, int, char*, uint64_t);
            (org_ptr(0x1402f3430L))(this, rParam, nIdx, wszQuery, tBufferSize);
        };
        static struct CCashDBWorkManager* Instance()
        {
            using org_ptr = CCashDBWorkManager * (WINAPIV*)();
            return (org_ptr(0x140009971L))();
        }
        bool Initialize()
        {
            using org_ptr = bool (WINAPIV*)(struct CCashDBWorkManager*);
            return (org_ptr(0x1402f3210L))(this);
        };
        bool InitializeWorker()
        {
            using org_ptr = bool (WINAPIV*)(struct CCashDBWorkManager*);
            return (org_ptr(0x1402f32a0L))(this);
        };
        bool PushTask(int nTaskCode, char* p, uint64_t size)
        {
            using org_ptr = bool (WINAPIV*)(struct CCashDBWorkManager*, int, char*, uint64_t);
            return (org_ptr(0x1402f3340L))(this, nTaskCode, p, size);
        };
        void Start()
        {
            using org_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
            (org_ptr(0x1402f3300L))(this);
        };
        ~CCashDBWorkManager()
        {
            using org_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
            (org_ptr(0x1402f35d0L))(this);
        };
        void dtor_CCashDBWorkManager()
        {
            using org_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
            (org_ptr(0x1402f35d0L))(this);
        };
    };
END_ATF_NAMESPACE

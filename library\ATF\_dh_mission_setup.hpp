// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__add_loot_item.hpp>
#include <__add_monster.hpp>
#include <__add_time.hpp>
#include <__change_monster.hpp>
#include <__dp_mission_potal.hpp>
#include <__inner_check.hpp>
#include <__respawn_monster.hpp>
#include <__respond_check.hpp>
#include <_dh_job_setup.hpp>
#include <_dh_mission_setupVtbl.hpp>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _dh_mission_setup
    {
        _dh_mission_setupVtbl *vfptr;
        char szMissionTitle[33];
        _dummy_position *pAreaDummy;
        _dummy_position *pStartDummy;
        char byJobOrder;
        unsigned int dwLimTimeMSec;
        char byResultType;
        char *pszNextMissionTitle;
        char szDescirptCode[17];
        char szCompleteMsg[17];
        int nAddMonsterNum;
        __add_monster *pAddMonster[32];
        int nLootItemNum;
        __add_loot_item *pLootItem[32];
        int nChangeMonsterNum;
        __change_monster *pChangeMonster[32];
        int nInnerCheckNum;
        __inner_check *pInnerCheck[64];
        int nRespondCheckNum;
        __respond_check *pRespondCheck[32];
        int nRespawnMonsterNum;
        __respawn_monster *pRespawnMonster[32];
        int nAddSecCheckNum;
        __add_time *pAddSecCheck[32];
        int nEmbJobSetupNum;
        _dh_job_setup *EmbJobSetup[8];
        _dh_mission_setup *pNextMission;
        __dp_mission_potal facMissionPotal;
    public:
        _dh_mission_setup();
        void ctor__dh_mission_setup();
        ~_dh_mission_setup();
        void dtor__dh_mission_setup();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

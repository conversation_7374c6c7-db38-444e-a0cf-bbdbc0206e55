// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RACE_BOSS_MSG__CMsgListManager.hpp>


START_ATF_NAMESPACE
    namespace RACE_BOSS_MSG
    {
        namespace Info
        {
            
            using RACE_BOSS_MSG__CMsgListManagerctor_CMsgListManager2_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*);
            using RACE_BOSS_MSG__CMsgListManagerctor_CMsgListManager2_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, RACE_BOSS_MSG__CMsgListManagerctor_CMsgListManager2_ptr);
            using RACE_BOSS_MSG__CMsgListManagerCancel4_ptr = int (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, unsigned int, struct RACE_BOSS_MSG::CMsg**);
            using RACE_BOSS_MSG__CMsgListManagerCancel4_clbk = int (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, unsigned int, struct RACE_BOSS_MSG::CMsg**, RACE_BOSS_MSG__CMsgListManagerCancel4_ptr);
            using RACE_BOSS_MSG__CMsgListManagerCleanUp6_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*);
            using RACE_BOSS_MSG__CMsgListManagerCleanUp6_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, RACE_BOSS_MSG__CMsgListManagerCleanUp6_ptr);
            using RACE_BOSS_MSG__CMsgListManagerCleanUpCancel8_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgListManagerCleanUpCancel8_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgListManagerCleanUpCancel8_ptr);
            using RACE_BOSS_MSG__CMsgListManagerGetRemainCnt10_ptr = char (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char);
            using RACE_BOSS_MSG__CMsgListManagerGetRemainCnt10_clbk = char (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, RACE_BOSS_MSG__CMsgListManagerGetRemainCnt10_ptr);
            using RACE_BOSS_MSG__CMsgListManagerGetSendMsg12_ptr = struct RACE_BOSS_MSG::CMsg* (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char*);
            using RACE_BOSS_MSG__CMsgListManagerGetSendMsg12_clbk = struct RACE_BOSS_MSG::CMsg* (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char*, RACE_BOSS_MSG__CMsgListManagerGetSendMsg12_ptr);
            using RACE_BOSS_MSG__CMsgListManagerInit14_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*);
            using RACE_BOSS_MSG__CMsgListManagerInit14_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, RACE_BOSS_MSG__CMsgListManagerInit14_ptr);
            using RACE_BOSS_MSG__CMsgListManagerIsHaveBeenSave16_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*);
            using RACE_BOSS_MSG__CMsgListManagerIsHaveBeenSave16_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, RACE_BOSS_MSG__CMsgListManagerIsHaveBeenSave16_ptr);
            using RACE_BOSS_MSG__CMsgListManagerLoad18_ptr = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, unsigned int);
            using RACE_BOSS_MSG__CMsgListManagerLoad18_clbk = bool (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, unsigned int, RACE_BOSS_MSG__CMsgListManagerLoad18_ptr);
            using RACE_BOSS_MSG__CMsgListManagerRefresh20_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*);
            using RACE_BOSS_MSG__CMsgListManagerRefresh20_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, RACE_BOSS_MSG__CMsgListManagerRefresh20_ptr);
            using RACE_BOSS_MSG__CMsgListManagerRelease22_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, struct RACE_BOSS_MSG::CMsg*);
            using RACE_BOSS_MSG__CMsgListManagerRelease22_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, struct RACE_BOSS_MSG::CMsg*, RACE_BOSS_MSG__CMsgListManagerRelease22_ptr);
            using RACE_BOSS_MSG__CMsgListManagerSave24_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char);
            using RACE_BOSS_MSG__CMsgListManagerSave24_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, RACE_BOSS_MSG__CMsgListManagerSave24_ptr);
            using RACE_BOSS_MSG__CMsgListManagerSave26_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*);
            using RACE_BOSS_MSG__CMsgListManagerSave26_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, RACE_BOSS_MSG__CMsgListManagerSave26_ptr);
            using RACE_BOSS_MSG__CMsgListManagerSend28_ptr = int (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, unsigned int, char*, char*, struct RACE_BOSS_MSG::CMsg**, unsigned int);
            using RACE_BOSS_MSG__CMsgListManagerSend28_clbk = int (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, char, unsigned int, char*, char*, struct RACE_BOSS_MSG::CMsg**, unsigned int, RACE_BOSS_MSG__CMsgListManagerSend28_ptr);
            
            using RACE_BOSS_MSG__CMsgListManagerdtor_CMsgListManager30_ptr = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*);
            using RACE_BOSS_MSG__CMsgListManagerdtor_CMsgListManager30_clbk = void (WINAPIV*)(struct RACE_BOSS_MSG::CMsgListManager*, RACE_BOSS_MSG__CMsgListManagerdtor_CMsgListManager30_ptr);
        }; // end namespace Info
    }; // end namespace RACE_BOSS_MSG
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagEMR.hpp>


START_ATF_NAMESPACE
    struct tagCOLORCORRECTPALETTE
    {
        tagEMR emr;
        unsigned int ihPalette;
        unsigned int nFirstEntry;
        unsigned int nPalEntries;
        unsigned int nReserved;
    };
END_ATF_NAMESPACE

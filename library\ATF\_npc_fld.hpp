// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _npc_fld : _base_fld
    {
        char m_strName[64];
        int m_nRace;
        int m_bWarType;
        float m_fTarDecType;
        float m_fLevel;
        float m_fExt;
        float m_fAttFcStd;
        float m_fMinAFSelProb;
        float m_fMaxAFSelProb;
        float m_fAttSklUnit;
        float m_fDefSklUnit;
        float m_fWeakPart;
        float m_fStdDefFc;
        float m_fFireTol;
        float m_fWaterTol;
        float m_fSoilTol;
        float m_fWindTol;
        float m_fAttTechID1;
        float m_fAttTech1UseProb;
        float m_fAttTechID2;
        float m_fAttTech2UseProb;
        float m_fAttTechID3;
        float m_fAttTech3UseProb;
        float m_fAttTechMinAFProb;
        float m_fAttTechMinAF;
        float m_fAttTechMaxAFProb;
        float m_fAttTechMaxAF;
        float m_fPSecTechID;
        float m_fMSecTechID;
        float m_fMaxHP;
        float m_fHPRecDelay;
        float m_fHPRecUnit;
        float m_fAttSpd;
        float m_fAttMoTime1;
        float m_fAttMoTime2;
        float m_fViewExt;
        float m_fAttExt;
        float m_fMRefExt;
        float m_fCopTime;
        float m_fMovSpd;
        float m_fWarMovSpd;
        float m_fScaleRate;
        int m_bScaleChange;
        float m_fWidth;
        float m_fWaitTime;
        int m_nAsitReqRate;
        int m_nAsitAptRate;
        char m_strChildMon[64];
        int m_nChildMonNum;
        float m_fEmoType;
        float m_fDamHPStd;
        float m_fEmoImpStdTime;
        float m_fGoodToOrdHPPer;
        float m_fOrdToBadHPPer;
        float m_fBadToWorseHPPer;
        float m_fEspTFProb;
        float m_fTypeCompTerms;
        float m_fPSecTechChat;
        float m_fPAttTechChat;
        float m_fEmo0Chat;
        float m_fEmo0ChatProb;
        float m_fEmo1Chat;
        float m_fEmo1ChatProb;
        float m_fEmo2Chat;
        float m_fEmo2ChatProb;
        float m_fEmo3Chat;
        float m_fEmo3ChatProb;
        float m_fEmo4Chat;
        float m_fEmo4ChatProb;
        float m_fAsitReqSteEspChat;
        float m_fAsitReqSteEspChatProb;
        float m_fAsitReqSteHelpChat;
        float m_fAsitReqSteHelpChatProb;
        float m_fAsitReqSteCopChat;
        float m_fAsitReqSteCopChatProb;
    };
END_ATF_NAMESPACE

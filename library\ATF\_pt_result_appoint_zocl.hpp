// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _pt_result_appoint_zocl
    {
        char byLevel;
        char byClassType;
        long double dPvpPoint;
        char wszAvator<PERSON>ame[17];
    public:
        _pt_result_appoint_zocl();
        void ctor__pt_result_appoint_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_pt_result_appoint_zocl, 27>(), "_pt_result_appoint_zocl");
END_ATF_NAMESPACE

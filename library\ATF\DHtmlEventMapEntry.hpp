// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <DHtmlEventMapEntryType.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 16)
    const struct DHtmlEventMapEntry
    {
        DHtmlEventMapEntryType nType;
        int dispId;
        const char *szName;
        void (WINAPIV *pfnEventFunc)(CDHtmlSinkHandler *_this);
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

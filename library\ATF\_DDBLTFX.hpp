// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$1E7AB7F19D1C3EB61B6495EB0D40538B.hpp>
#include <$246B53D799D52A4EA437232D85027ECC.hpp>
#include <$3B69F9E1E4A95E5428993AE3592CD965.hpp>
#include <$5FC8BC77704B434305F87B884184E23E.hpp>
#include <$67EDD32E34E99E34933362666673E49D.hpp>
#include <_DDCOLORKEY.hpp>


START_ATF_NAMESPACE
    struct _DDBLTFX
    {
        unsigned int dwSize;
        unsigned int dwDDFX;
        unsigned int dwROP;
        unsigned int dwDDROP;
        unsigned int dwRotationAngle;
        unsigned int dwZ<PERSON>ufferOpCode;
        unsigned int dwZ<PERSON>ufferLow;
        unsigned int dwZ<PERSON><PERSON><PERSON><PERSON><PERSON>;
        unsigned int dwZBufferBaseDest;
        unsigned int dwZDestConstBitDepth;
        $1E7AB7F19D1C3EB61B6495EB0D40538B ___u10;
        unsigned int dwZSrcConstBitDepth;
        $3B69F9E1E4A95E5428993AE3592CD965 ___u12;
        unsigned int dwAlphaEdgeBlendBitDepth;
        unsigned int dwAlphaEdgeBlend;
        unsigned int dwReserved;
        unsigned int dwAlphaDestConstBitDepth;
        $246B53D799D52A4EA437232D85027ECC ___u17;
        unsigned int dwAlphaSrcConstBitDepth;
        $67EDD32E34E99E34933362666673E49D ___u19;
        $5FC8BC77704B434305F87B884184E23E ___u20;
        _DDCOLORKEY ddckDestColorkey;
        _DDCOLORKEY ddckSrcColorkey;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateRoundList.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateRoundListctor_CNormalGuildBattleStateRoundList2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundList*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundListctor_CNormalGuildBattleStateRoundList2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundList*, GUILD_BATTLE__CNormalGuildBattleStateRoundListctor_CNormalGuildBattleStateRoundList2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundListIsInBattleRegenState4_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundList*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundListIsInBattleRegenState4_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundList*, GUILD_BATTLE__CNormalGuildBattleStateRoundListIsInBattleRegenState4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundListSetNextState6_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundList*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundListSetNextState6_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundList*, GUILD_BATTLE__CNormalGuildBattleStateRoundListSetNextState6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateRoundListdtor_CNormalGuildBattleStateRoundList8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundList*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundListdtor_CNormalGuildBattleStateRoundList8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundList*, GUILD_BATTLE__CNormalGuildBattleStateRoundListdtor_CNormalGuildBattleStateRoundList8_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

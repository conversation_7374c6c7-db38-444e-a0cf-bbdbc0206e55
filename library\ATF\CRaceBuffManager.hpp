// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRaceBuffByHolyQuestProcedure.hpp>
#include <CRaceBuffInfoByHolyQuestfGroup.hpp>


START_ATF_NAMESPACE
    struct CRaceBuffManager
    {
        CRaceBuffByHolyQuestProcedure m_kBuffByHolyQuest;
    public:
        CRaceBuffManager();
        void ctor_CRaceBuffManager();
        int CancelPlayerRaceBuff(struct CPlayer* pkPlayer, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE eReleaseType, unsigned int uiContinueCnt);
        bool CreateComplete(struct CPlayer* pkPlayer);
        static void Destroy();
        int GetRaceBuffLevel(struct CPlayer* pOne);
        bool Init();
        static struct CRaceBuffManager* Instance();
        void Loop();
        bool RequestHolyQuestRaceBuff(int iType);
        ~CRaceBuffManager();
        void dtor_CRaceBuffManager();
    };
END_ATF_NAMESPACE

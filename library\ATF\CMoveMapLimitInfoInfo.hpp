// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitInfo.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CMoveMapLimitInfoctor_CMoveMapLimitInfo2_ptr = void (WINAPIV*)(struct CMoveMapLimitInfo*, unsigned int, int);
        using CMoveMapLimitInfoctor_CMoveMapLimitInfo2_clbk = void (WINAPIV*)(struct CMoveMapLimitInfo*, unsigned int, int, CMoveMapLimitInfoctor_CMoveMapLimitInfo2_ptr);
        using CMoveMapLimitInfoCreate4_ptr = struct CMoveMapLimitInfo* (WINAPIV*)(unsigned int, int);
        using CMoveMapLimitInfoCreate4_clbk = struct CMoveMapLimitInfo* (WINAPIV*)(unsigned int, int, CMoveMapLimitInfoCreate4_ptr);
        using CMoveMapLimitInfoGetInx6_ptr = unsigned int (WINAPIV*)(struct CMoveMapLimitInfo*);
        using CMoveMapLimitInfoGetInx6_clbk = unsigned int (WINAPIV*)(struct CMoveMapLimitInfo*, CMoveMapLimitInfoGetInx6_ptr);
        using CMoveMapLimitInfoGetType8_ptr = int (WINAPIV*)(struct CMoveMapLimitInfo*);
        using CMoveMapLimitInfoGetType8_clbk = int (WINAPIV*)(struct CMoveMapLimitInfo*, CMoveMapLimitInfoGetType8_ptr);
        using CMoveMapLimitInfoIsEqualLimit10_ptr = bool (WINAPIV*)(struct CMoveMapLimitInfo*, int, int, unsigned int);
        using CMoveMapLimitInfoIsEqualLimit10_clbk = bool (WINAPIV*)(struct CMoveMapLimitInfo*, int, int, unsigned int, CMoveMapLimitInfoIsEqualLimit10_ptr);
        using CMoveMapLimitInfoLoad12_ptr = void (WINAPIV*)(struct CMoveMapLimitInfo*, struct CPlayer*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoLoad12_clbk = void (WINAPIV*)(struct CMoveMapLimitInfo*, struct CPlayer*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoLoad12_ptr);
        using CMoveMapLimitInfoLogIn14_ptr = void (WINAPIV*)(struct CMoveMapLimitInfo*, struct CPlayer*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoLogIn14_clbk = void (WINAPIV*)(struct CMoveMapLimitInfo*, struct CPlayer*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoLogIn14_ptr);
        using CMoveMapLimitInfoLogOut16_ptr = void (WINAPIV*)(struct CMoveMapLimitInfo*, struct CPlayer*, struct CMoveMapLimitRightInfo*);
        using CMoveMapLimitInfoLogOut16_clbk = void (WINAPIV*)(struct CMoveMapLimitInfo*, struct CPlayer*, struct CMoveMapLimitRightInfo*, CMoveMapLimitInfoLogOut16_ptr);
        using CMoveMapLimitInfoLoop18_ptr = void (WINAPIV*)(struct CMoveMapLimitInfo*);
        using CMoveMapLimitInfoLoop18_clbk = void (WINAPIV*)(struct CMoveMapLimitInfo*, CMoveMapLimitInfoLoop18_ptr);
        
        using CMoveMapLimitInfodtor_CMoveMapLimitInfo22_ptr = void (WINAPIV*)(struct CMoveMapLimitInfo*);
        using CMoveMapLimitInfodtor_CMoveMapLimitInfo22_clbk = void (WINAPIV*)(struct CMoveMapLimitInfo*, CMoveMapLimitInfodtor_CMoveMapLimitInfo22_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

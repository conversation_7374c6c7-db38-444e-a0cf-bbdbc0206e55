// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IPersistStream.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<IPersistStream> : IPersistStream
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ITypeInfo.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<ITypeInfo> : ITypeInfo
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <IMalloc.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<IMalloc> : IMalloc
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <IPersist.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<IPersist> : IPersist
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <IUnknown.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<IUnknown> : IUnknown
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ICreateErrorInfo.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<ICreateErrorInfo> : ICreateErrorInfo
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <IDispatch.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<IDispatch> : IDispatch
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <IProvideClassInfo2.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<IProvideClassInfo2> : IProvideClassInfo2
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ITypeLib.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<ITypeLib> : ITypeLib
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE
#include <ICatRegister.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct  _NoAddRefReleaseOnCComPtr<ICatRegister> : ICatRegister
        {
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <US__AbstractThreadVtbl.hpp>
#include <US__CNoneCopyAble.hpp>


START_ATF_NAMESPACE
    namespace US
    {
        struct  AbstractThread : CNoneCopyAble
        {
            AbstractThreadVtbl *vfptr;
        public:
            AbstractThread();
            void ctor_AbstractThread();
            ~AbstractThread();
            void dtor_AbstractThread();
        };
    }; // end namespace US
END_ATF_NAMESPACE

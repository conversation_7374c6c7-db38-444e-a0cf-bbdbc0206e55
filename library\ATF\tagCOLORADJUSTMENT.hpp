// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagCOLORADJUSTMENT
    {
        unsigned __int16 caSize;
        unsigned __int16 caFlags;
        unsigned __int16 caIlluminantIndex;
        unsigned __int16 caRedGamma;
        unsigned __int16 caGreenGamma;
        unsigned __int16 caBlueGamma;
        unsigned __int16 caReferenceBlack;
        unsigned __int16 caReferenceWhite;
        __int16 caContrast;
        __int16 caBrightness;
        __int16 caColorfulness;
        __int16 caRedGreenTint;
    };
END_ATF_NAMESPACE

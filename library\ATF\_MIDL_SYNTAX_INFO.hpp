// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <RPC_DISPATCH_TABLE.hpp>
#include <_RPC_SYNTAX_IDENTIFIER.hpp>


START_ATF_NAMESPACE
    struct _MIDL_SYNTAX_INFO
    {
        _RPC_SYNTAX_IDENTIFIER TransferSyntax;
        RPC_DISPATCH_TABLE *DispatchTable;
        const char *ProcString;
        const unsigned __int16 *FmtStringOffset;
        const char *TypeString;
        const void *aUserMarshalQuadruple;
        unsigned __int64 pReserved1;
        unsigned __int64 pReserved2;
    };
END_ATF_NAMESPACE

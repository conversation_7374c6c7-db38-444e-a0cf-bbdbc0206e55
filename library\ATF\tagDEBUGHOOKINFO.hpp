// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagDEBUGHOOKINFO
    {
        unsigned int idThread;
        unsigned int idThreadInstaller;
        __int64 lParam;
        unsigned __int64 wParam;
        int code;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

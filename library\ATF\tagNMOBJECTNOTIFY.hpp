// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HRESULT.hpp>
#include <_GUID.hpp>
#include <tagNMHDR.hpp>


START_ATF_NAMESPACE
    struct tagNMOBJECTNOTIFY
    {
        tagNMHDR hdr;
        int iItem;
        _GUID *piid;
        void *pObject;
        HRESULT hResult;
        unsigned int dwFlags;
    };
END_ATF_NAMESPACE

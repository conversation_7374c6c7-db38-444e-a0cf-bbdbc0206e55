// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CStringT.hpp>
#include <IStream.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CStreamOnCString : IStream
    {
        ATL::CStringT<char> m_strStream;
        ATL::CStringT<char> m_strAnsi;
        unsigned int m_current_index;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

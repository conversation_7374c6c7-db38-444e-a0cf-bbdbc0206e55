// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$29A697514CF2312F71664FA328543839.hpp>


START_ATF_NAMESPACE
    struct CVertexBuffer
    {
        unsigned int m_Flag;
        unsigned int m_Size;
        $29A697514CF2312F71664FA328543839 ___u2;
    public:
        CVertexBuffer();
        void ctor_CVertexBuffer();
        void InitVertexBuffer(int arg_0, int arg_1, uint32_t arg_2);
        void ReleaseVertexBuffer();
        uint8_t* VPLock(int arg_0, int arg_1, uint32_t arg_2);
        void VPUnLock();
        ~CVertexBuffer();
        void dtor_CVertexBuffer();
    };
END_ATF_NAMESPACE

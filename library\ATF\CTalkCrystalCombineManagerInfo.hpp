// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CTalkCrystalCombineManager.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CTalkCrystalCombineManagerctor_CTalkCrystalCombineManager2_ptr = void (WINAPIV*)(struct CTalkCrystalCombineManager*);
        using CTalkCrystalCombineManagerctor_CTalkCrystalCombineManager2_clbk = void (WINAPIV*)(struct CTalkCrystalCombineManager*, CTalkCrystalCombineManagerctor_CTalkCrystalCombineManager2_ptr);
        using CTalkCrystalCombineManagerCheckMixItem4_ptr = char (WINAPIV*)(struct CTalkCrystalCombineManager*, struct _STORAGE_LIST::_db_con*, int*, char*, uint16_t*, int*);
        using CTalkCrystalCombineManagerCheckMixItem4_clbk = char (WINAPIV*)(struct CTalkCrystalCombineManager*, struct _STORAGE_LIST::_db_con*, int*, char*, uint16_t*, int*, CTalkCrystalCombineManagerCheckMixItem4_ptr);
        using CTalkCrystalCombineManagerCombinePreProcess6_ptr = char (WINAPIV*)(struct CTalkCrystalCombineManager*, struct CPlayer*, char, struct _talik_crystal_exchange_clzo::_list*);
        using CTalkCrystalCombineManagerCombinePreProcess6_clbk = char (WINAPIV*)(struct CTalkCrystalCombineManager*, struct CPlayer*, char, struct _talik_crystal_exchange_clzo::_list*, CTalkCrystalCombineManagerCombinePreProcess6_ptr);
        using CTalkCrystalCombineManagerCombineProcess8_ptr = char (WINAPIV*)(struct CTalkCrystalCombineManager*);
        using CTalkCrystalCombineManagerCombineProcess8_clbk = char (WINAPIV*)(struct CTalkCrystalCombineManager*, CTalkCrystalCombineManagerCombineProcess8_ptr);
        using CTalkCrystalCombineManagerDestory10_ptr = void (WINAPIV*)();
        using CTalkCrystalCombineManagerDestory10_clbk = void (WINAPIV*)(CTalkCrystalCombineManagerDestory10_ptr);
        using CTalkCrystalCombineManagerDoit12_ptr = bool (WINAPIV*)(struct CTalkCrystalCombineManager*, struct CPlayer*, char, struct _talik_crystal_exchange_clzo::_list*);
        using CTalkCrystalCombineManagerDoit12_clbk = bool (WINAPIV*)(struct CTalkCrystalCombineManager*, struct CPlayer*, char, struct _talik_crystal_exchange_clzo::_list*, CTalkCrystalCombineManagerDoit12_ptr);
        using CTalkCrystalCombineManagerGetMixNode14_ptr = struct _talk_crystal_matrial_combine_node* (WINAPIV*)(struct CTalkCrystalCombineManager*, int);
        using CTalkCrystalCombineManagerGetMixNode14_clbk = struct _talk_crystal_matrial_combine_node* (WINAPIV*)(struct CTalkCrystalCombineManager*, int, CTalkCrystalCombineManagerGetMixNode14_ptr);
        using CTalkCrystalCombineManagerInit16_ptr = void (WINAPIV*)(struct CTalkCrystalCombineManager*);
        using CTalkCrystalCombineManagerInit16_clbk = void (WINAPIV*)(struct CTalkCrystalCombineManager*, CTalkCrystalCombineManagerInit16_ptr);
        using CTalkCrystalCombineManagerInstance18_ptr = struct CTalkCrystalCombineManager* (WINAPIV*)();
        using CTalkCrystalCombineManagerInstance18_clbk = struct CTalkCrystalCombineManager* (WINAPIV*)(CTalkCrystalCombineManagerInstance18_ptr);
        using CTalkCrystalCombineManagerMakeMixNode20_ptr = struct _talk_crystal_matrial_combine_node* (WINAPIV*)(struct CTalkCrystalCombineManager*, int, int, char, uint16_t);
        using CTalkCrystalCombineManagerMakeMixNode20_clbk = struct _talk_crystal_matrial_combine_node* (WINAPIV*)(struct CTalkCrystalCombineManager*, int, int, char, uint16_t, CTalkCrystalCombineManagerMakeMixNode20_ptr);
        using CTalkCrystalCombineManagerPush22_ptr = char (WINAPIV*)(struct CTalkCrystalCombineManager*, struct _STORAGE_LIST::_db_con*, char, char);
        using CTalkCrystalCombineManagerPush22_clbk = char (WINAPIV*)(struct CTalkCrystalCombineManager*, struct _STORAGE_LIST::_db_con*, char, char, CTalkCrystalCombineManagerPush22_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

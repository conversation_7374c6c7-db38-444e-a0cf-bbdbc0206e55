// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_other_shape_all_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _other_shape_all_zoclctor__other_shape_all_zocl2_ptr = void (WINAPIV*)(struct _other_shape_all_zocl*);
        using _other_shape_all_zoclctor__other_shape_all_zocl2_clbk = void (WINAPIV*)(struct _other_shape_all_zocl*, _other_shape_all_zoclctor__other_shape_all_zocl2_ptr);
        using _other_shape_all_zoclsize4_ptr = int (WINAPIV*)(struct _other_shape_all_zocl*);
        using _other_shape_all_zoclsize4_clbk = int (WINAPIV*)(struct _other_shape_all_zocl*, _other_shape_all_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagCY.hpp>



START_ATF_NAMESPACE
    struct tagFONTDESC
    {
        unsigned int cbSizeofstruct;
        wchar_t *lpstrName;
        tagCY cySize;
        __int16 sWeight;
        __int16 sCharset;
        int fItalic;
        int fUnderline;
        int fStrikethrough;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <Cmd.hpp>
#include <ElectProcessor.hpp>


START_ATF_NAMESPACE
    struct  SecondCandidateCrystallizer : ElectProcessor
    {
    public:
        int Doit(Cmd eCmd, struct CPlayer* pOne, char* pdata);
        bool Initialize();
        SecondCandidateCrystallizer();
        void ctor_SecondCandidateCrystallizer();
        ~SecondCandidateCrystallizer();
        void dtor_SecondCandidateCrystallizer();
    };
END_ATF_NAMESPACE

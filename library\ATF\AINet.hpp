// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AINetVtbl.hpp>


START_ATF_NAMESPACE
    struct AINet
    {
        AINetVtbl *vfptr;
        void *m_hSession;
    public:
        AINet(char* pstrAgent, unsigned int dwAccessType, char* pstrProxyName, char* pstrProxyBypass, unsigned int dwFlags);
        void ctor_AINet(char* pstrAgent, unsigned int dwAccessType, char* pstrProxyName, char* pstrProxyBypass, unsigned int dwFlags);
        ~AINet();
        void dtor_AINet();
    };
END_ATF_NAMESPACE

#include <_buddy_download_result_zoclDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        
        Info::_buddy_download_result_zoclctor__buddy_download_result_zocl2_ptr _buddy_download_result_zoclctor__buddy_download_result_zocl2_next(nullptr);
        Info::_buddy_download_result_zoclctor__buddy_download_result_zocl2_clbk _buddy_download_result_zoclctor__buddy_download_result_zocl2_user(nullptr);
        
        Info::_buddy_download_result_zoclsize4_ptr _buddy_download_result_zoclsize4_next(nullptr);
        Info::_buddy_download_result_zoclsize4_clbk _buddy_download_result_zoclsize4_user(nullptr);
        
        
        void _buddy_download_result_zoclctor__buddy_download_result_zocl2_wrapper(struct _buddy_download_result_zocl* _this)
        {
           _buddy_download_result_zoclctor__buddy_download_result_zocl2_user(_this, _buddy_download_result_zoclctor__buddy_download_result_zocl2_next);
        };
        int _buddy_download_result_zoclsize4_wrapper(struct _buddy_download_result_zocl* _this)
        {
           return _buddy_download_result_zoclsize4_user(_this, _buddy_download_result_zoclsize4_next);
        };
        
        ::std::array<hook_record, 2> _buddy_download_result_zocl_functions = 
        {
            _hook_record {
                (LPVOID)0x1400909e0L,
                (LPVOID *)&_buddy_download_result_zoclctor__buddy_download_result_zocl2_user,
                (LPVOID *)&_buddy_download_result_zoclctor__buddy_download_result_zocl2_next,
                (LPVOID)cast_pointer_function(_buddy_download_result_zoclctor__buddy_download_result_zocl2_wrapper),
                (LPVOID)cast_pointer_function((void(_buddy_download_result_zocl::*)())&_buddy_download_result_zocl::ctor__buddy_download_result_zocl)
            },
            _hook_record {
                (LPVOID)0x140090a00L,
                (LPVOID *)&_buddy_download_result_zoclsize4_user,
                (LPVOID *)&_buddy_download_result_zoclsize4_next,
                (LPVOID)cast_pointer_function(_buddy_download_result_zoclsize4_wrapper),
                (LPVOID)cast_pointer_function((int(_buddy_download_result_zocl::*)())&_buddy_download_result_zocl::size)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE

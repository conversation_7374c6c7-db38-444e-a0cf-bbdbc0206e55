// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagRID_DEVICE_INFO_HID.hpp>
#include <tagRID_DEVICE_INFO_KEYBOARD.hpp>
#include <tagRID_DEVICE_INFO_MOUSE.hpp>


START_ATF_NAMESPACE
    union $FFF01728732C38795F1E6B3A1BE44A02
    {
        tagRID_DEVICE_INFO_MOUSE mouse;
        tagRID_DEVICE_INFO_KEYBOARD keyboard;
        tagRID_DEVICE_INFO_HID hid;
    };
END_ATF_NAMESPACE

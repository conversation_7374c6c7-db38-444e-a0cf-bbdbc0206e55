// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union $F006553FDB25FB59A59693178CCFF094
    {
        unsigned int dwRGBAlphaBitMask;
        unsigned int dwYUVAlphaBitMask;
        unsigned int dwLuminanceAlphaBitMask;
        unsigned int dwRGBZBitMask;
        unsigned int dwYUVZBitMask;
    };    
    static_assert(ATF::checkSize<$F006553FDB25FB59A59693178CCFF094, 4>(), "$F006553FDB25FB59A59693178CCFF094");
END_ATF_NAMESPACE

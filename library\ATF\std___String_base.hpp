// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Container_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct  _String_base : _Container_base
        {
        public:
            _String_base(struct _String_base* arg_0);
            int64_t ctor__String_base(struct _String_base* arg_0);
        };    
        static_assert(ATF::checkSize<std::_String_base, 8>(), "std::_String_base");
    }; // end namespace std
END_ATF_NAMESPACE

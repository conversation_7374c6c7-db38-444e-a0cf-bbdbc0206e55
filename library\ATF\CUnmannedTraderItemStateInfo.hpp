// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderItemState.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderItemStatector_CUnmannedTraderItemState2_ptr = void (WINAPIV*)(struct CUnmannedTraderItemState*);
        using CUnmannedTraderItemStatector_CUnmannedTraderItemState2_clbk = void (WINAPIV*)(struct CUnmannedTraderItemState*, CUnmannedTraderItemStatector_CUnmannedTraderItemState2_ptr);
        using CUnmannedTraderItemStateClear4_ptr = void (WINAPIV*)(struct CUnmannedTraderItemState*);
        using CUnmannedTraderItemStateClear4_clbk = void (WINAPIV*)(struct CUnmannedTraderItemState*, CUnmannedTraderItemStateClear4_ptr);
        using CUnmannedTraderItemStateGetMaxStateCnt6_ptr = unsigned int (WINAPIV*)();
        using CUnmannedTraderItemStateGetMaxStateCnt6_clbk = unsigned int (WINAPIV*)(CUnmannedTraderItemStateGetMaxStateCnt6_ptr);
        using CUnmannedTraderItemStateGetState8_ptr = CUnmannedTraderItemState::STATE (WINAPIV*)(struct CUnmannedTraderItemState*);
        using CUnmannedTraderItemStateGetState8_clbk = CUnmannedTraderItemState::STATE (WINAPIV*)(struct CUnmannedTraderItemState*, CUnmannedTraderItemStateGetState8_ptr);
        using CUnmannedTraderItemStateGetStateStrList10_ptr = wchar_t** (WINAPIV*)();
        using CUnmannedTraderItemStateGetStateStrList10_clbk = wchar_t** (WINAPIV*)(CUnmannedTraderItemStateGetStateStrList10_ptr);
        using CUnmannedTraderItemStateGetStateStrW12_ptr = wchar_t* (WINAPIV*)(unsigned int);
        using CUnmannedTraderItemStateGetStateStrW12_clbk = wchar_t* (WINAPIV*)(unsigned int, CUnmannedTraderItemStateGetStateStrW12_ptr);
        using CUnmannedTraderItemStatePushUpdateState14_ptr = bool (WINAPIV*)(char, unsigned int, char, unsigned int, uint16_t, char, uint16_t);
        using CUnmannedTraderItemStatePushUpdateState14_clbk = bool (WINAPIV*)(char, unsigned int, char, unsigned int, uint16_t, char, uint16_t, CUnmannedTraderItemStatePushUpdateState14_ptr);
        using CUnmannedTraderItemStateSet16_ptr = bool (WINAPIV*)(struct CUnmannedTraderItemState*, char);
        using CUnmannedTraderItemStateSet16_clbk = bool (WINAPIV*)(struct CUnmannedTraderItemState*, char, CUnmannedTraderItemStateSet16_ptr);
        
        using CUnmannedTraderItemStatedtor_CUnmannedTraderItemState18_ptr = void (WINAPIV*)(struct CUnmannedTraderItemState*);
        using CUnmannedTraderItemStatedtor_CUnmannedTraderItemState18_clbk = void (WINAPIV*)(struct CUnmannedTraderItemState*, CUnmannedTraderItemStatedtor_CUnmannedTraderItemState18_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaEventNode.hpp>
#include <US__CArrayEx.hpp>


START_ATF_NAMESPACE
    struct CLuaEventMgr
    {
        US::CArrayEx<CLuaEventNode,CLuaEventNode::_State> m_LuaEventArEx;
    public:
        bool AttachEvent(struct CLuaEventNode* pEvent);
        CLuaEventMgr();
        void ctor_CLuaEventMgr();
        static void Destroy();
        bool DettachEvent(struct CLuaEventNode* pEvent);
        bool InitSDM();
        static struct CLuaEventMgr* Instance();
        void Loop();
        struct CLuaEventNode* NewEvent();
        void RemoveEvent(struct CLuaEventNode* pEvent);
        struct CLuaEventNode* SearchEvent(char* strFileName);
        ~CLuaEventMgr();
        void dtor_CLuaEventMgr();
    };
END_ATF_NAMESPACE

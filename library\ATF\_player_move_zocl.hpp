// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _player_move_zocl
    {
        unsigned int dwSerial;
        __int16 zCur[3];
        __int16 zTar[2];
        __int16 nAddSpeed;
        char byDirect;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateList.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            using GUILD_BATTLE__CNormalGuildBattleStateListAdvanceRegenState2_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*);
            using GUILD_BATTLE__CNormalGuildBattleStateListAdvanceRegenState2_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, GUILD_BATTLE__CNormalGuildBattleStateListAdvanceRegenState2_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateListctor_CNormalGuildBattleStateList4_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*);
            using GUILD_BATTLE__CNormalGuildBattleStateListctor_CNormalGuildBattleStateList4_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, GUILD_BATTLE__CNormalGuildBattleStateListctor_CNormalGuildBattleStateList4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListIsInBattle6_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*);
            using GUILD_BATTLE__CNormalGuildBattleStateListIsInBattle6_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, GUILD_BATTLE__CNormalGuildBattleStateListIsInBattle6_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListIsInBattleRegenState8_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*);
            using GUILD_BATTLE__CNormalGuildBattleStateListIsInBattleRegenState8_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, GUILD_BATTLE__CNormalGuildBattleStateListIsInBattleRegenState8_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListIsReadyOrCountState10_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*);
            using GUILD_BATTLE__CNormalGuildBattleStateListIsReadyOrCountState10_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, GUILD_BATTLE__CNormalGuildBattleStateListIsReadyOrCountState10_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListSetBattleTime12_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, struct ATL::CTimeSpan);
            using GUILD_BATTLE__CNormalGuildBattleStateListSetBattleTime12_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, struct ATL::CTimeSpan, GUILD_BATTLE__CNormalGuildBattleStateListSetBattleTime12_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListSetGotoRegenState14_ptr = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*);
            using GUILD_BATTLE__CNormalGuildBattleStateListSetGotoRegenState14_clbk = bool (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, GUILD_BATTLE__CNormalGuildBattleStateListSetGotoRegenState14_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateListSetNextState16_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*);
            using GUILD_BATTLE__CNormalGuildBattleStateListSetNextState16_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, GUILD_BATTLE__CNormalGuildBattleStateListSetNextState16_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateListdtor_CNormalGuildBattleStateList20_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*);
            using GUILD_BATTLE__CNormalGuildBattleStateListdtor_CNormalGuildBattleStateList20_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateList*, GUILD_BATTLE__CNormalGuildBattleStateListdtor_CNormalGuildBattleStateList20_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

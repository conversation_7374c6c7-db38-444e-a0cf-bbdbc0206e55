// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$25C8602110B22A2EB54BB3EE6752756B.hpp>
#include <$42A39AC029B3127F486DF502FF8A7E09.hpp>


START_ATF_NAMESPACE
    struct _D3DR3VERTEX_TEX2
    {
        $42A39AC029B3127F486DF502FF8A7E09 ___u0;
        float y;
        float z;
        $25C8602110B22A2EB54BB3EE6752756B ___u3;
        float ny;
        float nz;
        unsigned int color;
        float uv[2][2];
    };
END_ATF_NAMESPACE

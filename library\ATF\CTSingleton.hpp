// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    template<typename _Ty>
    struct CTSingleton
    {
        struct CTSingletonVtbl
        {
            void *(WINAPIV *__vecDelDtor)(struct CTSingleton<_Ty> *_this, unsigned int);
        };

        CTSingletonVtbl *vfptr;
    };
END_ATF_NAMESPACE

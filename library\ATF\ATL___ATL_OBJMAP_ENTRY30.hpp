// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IUnknown.hpp>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct _ATL_OBJMAP_ENTRY30
        {
            _GUID *pclsid;
            HRESULT (WINAPIV *pfnUpdateRegistry)(int);
            HRESULT (WINAPIV *pfnGetClassObject)(void *, _GUID *, void **);
            HRESULT (WINAPIV *pfnCreateInstance)(void *, _GUID *, void **);
            IUnknown *pCF;
            unsigned int dwRegister;
            const char *(WINAPIV *pfnGetObjectDescription)();
            _ATL_CATMAP_ENTRY *(WINAPIV *pfnGetCategoryMap)();
            void (WINAPIV *pfnObjectMain)(bool);
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CItemStore.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CItemStorector_CItemStore2_ptr = void (WINAPIV*)(struct CItemStore*);
        using CItemStorector_CItemStore2_clbk = void (WINAPIV*)(struct CItemStore*, CItemStorector_CItemStore2_ptr);
        using CItemStoreCalcBuyPrice4_ptr = float (WINAPIV*)(struct CItemStore*, char, uint16_t, char*);
        using CItemStoreCalcBuyPrice4_clbk = float (WINAPIV*)(struct CItemStore*, char, uint16_t, char*, CItemStoreCalcBuyPrice4_ptr);
        using CItemStoreCalcSecIndex6_ptr = int (WINAPIV*)(struct CItemStore*, float, float);
        using CItemStoreCalcSecIndex6_clbk = int (WINAPIV*)(struct CItemStore*, float, float, CItemStoreCalcSecIndex6_ptr);
        using CItemStoreCalcSellPrice8_ptr = int (WINAPIV*)(struct CItemStore*, int, char*);
        using CItemStoreCalcSellPrice8_clbk = int (WINAPIV*)(struct CItemStore*, int, char*, CItemStoreCalcSellPrice8_ptr);
        using CItemStoreGetLastTradeActPoint10_ptr = int (WINAPIV*)(struct CItemStore*, char);
        using CItemStoreGetLastTradeActPoint10_clbk = int (WINAPIV*)(struct CItemStore*, char, CItemStoreGetLastTradeActPoint10_ptr);
        using CItemStoreGetLastTradeDalant12_ptr = int (WINAPIV*)(struct CItemStore*);
        using CItemStoreGetLastTradeDalant12_clbk = int (WINAPIV*)(struct CItemStore*, CItemStoreGetLastTradeDalant12_ptr);
        using CItemStoreGetLastTradeGold14_ptr = int (WINAPIV*)(struct CItemStore*);
        using CItemStoreGetLastTradeGold14_clbk = int (WINAPIV*)(struct CItemStore*, CItemStoreGetLastTradeGold14_ptr);
        using CItemStoreGetLastTradePoint16_ptr = int (WINAPIV*)(struct CItemStore*);
        using CItemStoreGetLastTradePoint16_clbk = int (WINAPIV*)(struct CItemStore*, CItemStoreGetLastTradePoint16_ptr);
        using CItemStoreGetLimitItem18_ptr = struct _limit_item_info* (WINAPIV*)(struct CItemStore*, int);
        using CItemStoreGetLimitItem18_clbk = struct _limit_item_info* (WINAPIV*)(struct CItemStore*, int, CItemStoreGetLimitItem18_ptr);
        using CItemStoreGetLimitItemAmount20_ptr = void (WINAPIV*)(struct CItemStore*, struct _limit_amount_info*);
        using CItemStoreGetLimitItemAmount20_clbk = void (WINAPIV*)(struct CItemStore*, struct _limit_amount_info*, CItemStoreGetLimitItemAmount20_ptr);
        using CItemStoreGetNpcCode22_ptr = char* (WINAPIV*)(struct CItemStore*);
        using CItemStoreGetNpcCode22_clbk = char* (WINAPIV*)(struct CItemStore*, CItemStoreGetNpcCode22_ptr);
        using CItemStoreGetNpcRaceCode24_ptr = bool (WINAPIV*)(struct CItemStore*, char*);
        using CItemStoreGetNpcRaceCode24_clbk = bool (WINAPIV*)(struct CItemStore*, char*, CItemStoreGetNpcRaceCode24_ptr);
        using CItemStoreGetNpcRecord26_ptr = struct _base_fld* (WINAPIV*)(struct CItemStore*);
        using CItemStoreGetNpcRecord26_clbk = struct _base_fld* (WINAPIV*)(struct CItemStore*, CItemStoreGetNpcRecord26_ptr);
        using CItemStoreGetStorePos28_ptr = float* (WINAPIV*)(struct CItemStore*);
        using CItemStoreGetStorePos28_clbk = float* (WINAPIV*)(struct CItemStore*, CItemStoreGetStorePos28_ptr);
        using CItemStoreInit30_ptr = bool (WINAPIV*)(struct CItemStore*, int, struct CMapData*, struct _store_dummy*, struct _base_fld*);
        using CItemStoreInit30_clbk = bool (WINAPIV*)(struct CItemStore*, int, struct CMapData*, struct _store_dummy*, struct _base_fld*, CItemStoreInit30_ptr);
        using CItemStoreInitLimitItemInfo32_ptr = void (WINAPIV*)(struct CItemStore*);
        using CItemStoreInitLimitItemInfo32_clbk = void (WINAPIV*)(struct CItemStore*, CItemStoreInitLimitItemInfo32_ptr);
        using CItemStoreIsBuy34_ptr = char (WINAPIV*)(struct CItemStore*, char, struct _sell_offer*, float, char);
        using CItemStoreIsBuy34_clbk = char (WINAPIV*)(struct CItemStore*, char, struct _sell_offer*, float, char, CItemStoreIsBuy34_ptr);
        using CItemStoreIsSell36_ptr = char (WINAPIV*)(struct CItemStore*, char, struct _buy_offer*, unsigned int, unsigned int, long double, unsigned int*, char*, float, char, char);
        using CItemStoreIsSell36_clbk = char (WINAPIV*)(struct CItemStore*, char, struct _buy_offer*, unsigned int, unsigned int, long double, unsigned int*, char*, float, char, char, CItemStoreIsSell36_ptr);
        using CItemStoreSetLimitItemInitTime38_ptr = void (WINAPIV*)(struct CItemStore*);
        using CItemStoreSetLimitItemInitTime38_clbk = void (WINAPIV*)(struct CItemStore*, CItemStoreSetLimitItemInitTime38_ptr);
        using CItemStoreSetZeroTradeMoney40_ptr = void (WINAPIV*)(struct CItemStore*);
        using CItemStoreSetZeroTradeMoney40_clbk = void (WINAPIV*)(struct CItemStore*, CItemStoreSetZeroTradeMoney40_ptr);
        using CItemStoreSubLimitItemNum42_ptr = void (WINAPIV*)(struct CItemStore*, int, int);
        using CItemStoreSubLimitItemNum42_clbk = void (WINAPIV*)(struct CItemStore*, int, int, CItemStoreSubLimitItemNum42_ptr);
        using CItemStoreUpdateLimitItemNum44_ptr = void (WINAPIV*)(struct CItemStore*, bool);
        using CItemStoreUpdateLimitItemNum44_clbk = void (WINAPIV*)(struct CItemStore*, bool, CItemStoreUpdateLimitItemNum44_ptr);
        
        using CItemStoredtor_CItemStore48_ptr = void (WINAPIV*)(struct CItemStore*);
        using CItemStoredtor_CItemStore48_clbk = void (WINAPIV*)(struct CItemStore*, CItemStoredtor_CItemStore48_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

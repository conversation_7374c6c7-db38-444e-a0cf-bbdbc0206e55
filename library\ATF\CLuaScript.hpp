// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLuaCommand.hpp>
#include <CLuaScriptVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CLuaScript
    {
        struct _State
        {
            bool m_bExist;
            bool m_bAttached;
        public:
            _State();
            void ctor__State();
        };
        CLuaScriptVtbl *vfptr;
        struct lua_State *m_MyState;
        char m_strName[260];
    public:
        CLuaScript();
        void ctor_CLuaScript();
        lua_State* GetLuaState();
        char* GetName();
        bool RunCommand(struct CLuaCommand* pCommand);
        void SetName(char* strName);
        ~CLuaScript();
        void dtor_CLuaScript();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

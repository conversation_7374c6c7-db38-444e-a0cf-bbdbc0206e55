// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagMSG
    {
        HWND__ *hwnd;
        unsigned int message;
        unsigned __int64 wParam;
        __int64 lParam;
        unsigned int time;
        tagPOINT pt;
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<tagMSG, 48>(), "tagMSG");
END_ATF_NAMESPACE

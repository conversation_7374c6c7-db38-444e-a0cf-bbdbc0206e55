// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_record_bin_header.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _record_bin_headerctor__record_bin_header2_ptr = void (WINAPIV*)(struct _record_bin_header*);
        using _record_bin_headerctor__record_bin_header2_clbk = void (WINAPIV*)(struct _record_bin_header*, _record_bin_headerctor__record_bin_header2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

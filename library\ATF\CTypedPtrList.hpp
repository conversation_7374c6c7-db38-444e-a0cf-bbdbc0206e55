// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CTypedPtrList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CTypedPtrList<CPtrList,COleControlSiteOrWnd *> : _CTypedPtrList<CPtrList,COleControlSiteOrWnd *>
    {
    };
END_ATF_NAMESPACE
#include <_CTypedPtrList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CTypedPtrList<CPtrList,CPtrList *> : _CTypedPtrList<CPtrList,CPtrList *>
    {
    };
END_ATF_NAMESPACE
#include <_CTypedPtrList.hpp>


START_ATF_NAMESPACE
    template<>
    struct  CTypedPtrList<CObList,CObList *> : _CTypedPtrList<CObList,CObList *>
    {
    };
END_ATF_NAMESPACE

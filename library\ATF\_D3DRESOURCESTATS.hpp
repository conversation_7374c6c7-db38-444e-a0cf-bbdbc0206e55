// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _D3DRESOURCESTATS
    {
        int bThrashing;
        unsigned int ApproxBytesDownloaded;
        unsigned int NumEvicts;
        unsigned int NumVidCreates;
        unsigned int LastPri;
        unsigned int NumUsed;
        unsigned int NumUsedInVidMem;
        unsigned int WorkingSet;
        unsigned int WorkingSetBytes;
        unsigned int TotalManaged;
        unsigned int TotalBytes;
    };
END_ATF_NAMESPACE

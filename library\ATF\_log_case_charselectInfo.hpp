// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_log_case_charselect.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _log_case_charselectsize2_ptr = int (WINAPIV*)(struct _log_case_charselect*);
        using _log_case_charselectsize2_clbk = int (WINAPIV*)(struct _log_case_charselect*, _log_case_charselectsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

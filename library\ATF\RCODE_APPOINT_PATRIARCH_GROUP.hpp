// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum RCODE_APPOINT_PATRIARCH_GROUP
    {
      RET_APPOINT_SUCCESS = 0x0,
      RET_APPOINT_NOT_LOGIN = 0x1,
      RET_APPOINT_ALREADY = 0x2,
      RET_APPOINT_OVERLAP = 0x3,
      RET_APPOINT_NOT_QUALIFY = 0x4,
      RET_APPOINT_REFUSE = 0x5,
      RET_APPOINT_DB_ERROR = 0x6,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dummy_position.hpp>
#include <_portal_fld.hpp>


START_ATF_NAMESPACE
    struct _portal_dummy
    {
        _portal_fld *m_pPortalRec;
        _dummy_position *m_pDumPos;
    public:
        bool SetDummy(struct _portal_fld* pRec, struct _dummy_position* pDumPos);
        _portal_dummy();
        void ctor__portal_dummy();
    };
END_ATF_NAMESPACE

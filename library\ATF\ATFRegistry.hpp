// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <AINetFileRegister.hpp>
#include <AINetFtpRegister.hpp>
#include <AINetRegister.hpp>
#include <AP_BatterySlotRegister.hpp>
#include <ATL__CAtlExceptionRegister.hpp>
#include <ATL__CCRTAllocatorRegister.hpp>
#include <ATL__CComBSTRRegister.hpp>
#include <ATL__CFileTimeRegister.hpp>
#include <ATL__CFileTimeSpanRegister.hpp>
#include <ATL__CTimeRegister.hpp>
#include <ATL__CTimeSpanRegister.hpp>
#include <ATL__CTraceCategoryRegister.hpp>
#include <ATL__CTraceFileAndLineInfoRegister.hpp>
#include <ATL__CTraceRegister.hpp>
#include <AggroCaculateDataRegister.hpp>
#include <AreaDataRegister.hpp>
#include <AreaListRegister.hpp>
#include <AtmosphereRegister.hpp>
#include <AutoMineMachineMngRegister.hpp>
#include <AutoMineMachineRegister.hpp>
#include <AutominePersonalMgrRegister.hpp>
#include <AutominePersonalRegister.hpp>
#include <BASE_HACKSHEILD_PARAMRegister.hpp>
#include <BNetworkRegister.hpp>
#include <BossScheduleRegister.hpp>
#include <BossSchedule_MapRegister.hpp>
#include <BossSchedule_TBLRegister.hpp>
#include <C24TimerRegister.hpp>
#include <CAITimerRegister.hpp>
#include <CActionPointSystemMgrRegister.hpp>
#include <CAggroNodeRegister.hpp>
#include <CAlphaRegister.hpp>
#include <CAniCameraRegister.hpp>
#include <CAnimusRegister.hpp>
#include <CAsyncLogBufferListRegister.hpp>
#include <CAsyncLogBufferRegister.hpp>
#include <CAsyncLogInfoRegister.hpp>
#include <CAsyncLoggerRegister.hpp>
#include <CAttackRegister.hpp>
#include <CBattleTournamentInfoRegister.hpp>
#include <CBillingBRRegister.hpp>
#include <CBillingCNRegister.hpp>
#include <CBillingIDRegister.hpp>
#include <CBillingJPRegister.hpp>
#include <CBillingKRRegister.hpp>
#include <CBillingManagerRegister.hpp>
#include <CBillingNULLRegister.hpp>
#include <CBillingPHRegister.hpp>
#include <CBillingRURegister.hpp>
#include <CBillingRegister.hpp>
#include <CBossMonsterScheduleSystemRegister.hpp>
#include <CBspRegister.hpp>
#include <CCashDBWorkManagerRegister.hpp>
#include <CCashDbWorkerBRRegister.hpp>
#include <CCashDbWorkerCNRegister.hpp>
#include <CCashDbWorkerESRegister.hpp>
#include <CCashDbWorkerGBRegister.hpp>
#include <CCashDbWorkerIDRegister.hpp>
#include <CCashDbWorkerJPRegister.hpp>
#include <CCashDbWorkerKRRegister.hpp>
#include <CCashDbWorkerNULLRegister.hpp>
#include <CCashDbWorkerPHRegister.hpp>
#include <CCashDbWorkerRURegister.hpp>
#include <CCashDbWorkerTHRegister.hpp>
#include <CCashDbWorkerTWRegister.hpp>
#include <CCashDbWorkerUSRegister.hpp>
#include <CCharacterRegister.hpp>
#include <CChatStealSystemRegister.hpp>
#include <CCheckSumBaseConverterRegister.hpp>
#include <CCheckSumCharacAccountTrunkDataRegister.hpp>
#include <CCheckSumCharacTrunkConverterRegister.hpp>
#include <CCheckSumGuildConverterRegister.hpp>
#include <CCheckSumGuildDataRegister.hpp>
#include <CCheckSumRegister.hpp>
#include <CChiNetworkEXRegister.hpp>
#include <CCircleZoneRegister.hpp>
#include <CClientDCRegister.hpp>
#include <CCommandLineInfoRegister.hpp>
#include <CConnNumPHMgrRegister.hpp>
#include <CCouponMgrRegister.hpp>
#include <CD3DApplicationRegister.hpp>
#include <CD3DArcBallRegister.hpp>
#include <CD3DCameraRegister.hpp>
#include <CDarkHoleChannelRegister.hpp>
#include <CDarkHoleDungeonQuestRegister.hpp>
#include <CDarkHoleDungeonQuestSetupRegister.hpp>
#include <CDarkHoleRegister.hpp>
#include <CDummyPosTableRegister.hpp>
#include <CEngNetworkBillEXRegister.hpp>
#include <CEnglandBillingMgrRegister.hpp>
#include <CEntityRegister.hpp>
#include <CEquipItemSFAgentRegister.hpp>
#include <CEventLootTableRegister.hpp>
#include <CExchangeEventRegister.hpp>
#include <CExtDummyRegister.hpp>
#include <CExtPotionBufRegister.hpp>
#include <CFPSRegister.hpp>
#include <CFrameRateRegister.hpp>
#include <CGameObjectRegister.hpp>
#include <CGameStatisticsRegister.hpp>
#include <CGdiObjectRegister.hpp>
#include <CGoldenBoxItemMgrRegister.hpp>
#include <CGravityStoneRegenerRegister.hpp>
#include <CGravityStoneRegister.hpp>
#include <CGuardTowerRegister.hpp>
#include <CGuildBattleControllerRegister.hpp>
#include <CGuildListRegister.hpp>
#include <CGuildMasterEffectRegister.hpp>
#include <CGuildRankingRegister.hpp>
#include <CGuildRegister.hpp>
#include <CGuildRoomInfoRegister.hpp>
#include <CGuildRoomSystemRegister.hpp>
#include <CHEAT_COMMANDRegister.hpp>
#include <CHackShieldExSystemRegister.hpp>
#include <CHolyKeeperRegister.hpp>
#include <CHolyScheduleDataRegister.hpp>
#include <CHolyStoneRegister.hpp>
#include <CHolyStoneSaveDataRegister.hpp>
#include <CHolyStoneSystemDataMgrRegister.hpp>
#include <CHolyStoneSystemRegister.hpp>
#include <CHonorGuildRegister.hpp>
#include <CIndexBufferRegister.hpp>
#include <CIndexListRegister.hpp>
#include <CIniFileRegister.hpp>
#include <CItemBoxRegister.hpp>
#include <CItemDropMgrRegister.hpp>
#include <CItemLootTableRegister.hpp>
#include <CItemStoreManagerRegister.hpp>
#include <CItemStoreRegister.hpp>
#include <CItemUpgradeTableRegister.hpp>
#include <CLevelRegister.hpp>
#include <CLogFileRegister.hpp>
#include <CLogTypeDBTaskManagerRegister.hpp>
#include <CLogTypeDBTaskPoolRegister.hpp>
#include <CLogTypeDBTaskRegister.hpp>
#include <CLootingMgrRegister.hpp>
#include <CLuaCommandExRegister.hpp>
#include <CLuaCommandRegister.hpp>
#include <CLuaEventMgrRegister.hpp>
#include <CLuaEventNodeRegister.hpp>
#include <CLuaLootingMgrRegister.hpp>
#include <CLuaLooting_Novus_ItemRegister.hpp>
#include <CLuaScriptMgrRegister.hpp>
#include <CLuaScriptRegister.hpp>
#include <CLuaSignalReActorRegister.hpp>
#include <CMainThreadRegister.hpp>
#include <CMapDataRegister.hpp>
#include <CMapDataTableRegister.hpp>
#include <CMapItemStoreListRegister.hpp>
#include <CMapOperationRegister.hpp>
#include <CMerchantRegister.hpp>
#include <CMergeFileManagerRegister.hpp>
#include <CMergeFileRegister.hpp>
#include <CMgrAccountLobbyHistoryRegister.hpp>
#include <CMgrAvatorItemHistoryRegister.hpp>
#include <CMgrAvatorLvHistoryRegister.hpp>
#include <CMgrAvatorQuestHistoryRegister.hpp>
#include <CMgrGuildHistoryRegister.hpp>
#include <CMoneySupplyMgrRegister.hpp>
#include <CMonsterAIRegister.hpp>
#include <CMonsterAggroMgrRegister.hpp>
#include <CMonsterAttackRegister.hpp>
#include <CMonsterEventRespawnRegister.hpp>
#include <CMonsterEventSetRegister.hpp>
#include <CMonsterHelperRegister.hpp>
#include <CMonsterHierarchyRegister.hpp>
#include <CMonsterRegister.hpp>
#include <CMonsterSPGroupTableRegister.hpp>
#include <CMonsterSkillPoolRegister.hpp>
#include <CMonsterSkillRegister.hpp>
#include <CMoveMapLimitEnviromentValuesRegister.hpp>
#include <CMoveMapLimitInfoListRegister.hpp>
#include <CMoveMapLimitInfoPortalRegister.hpp>
#include <CMoveMapLimitInfoRegister.hpp>
#include <CMoveMapLimitManagerRegister.hpp>
#include <CMoveMapLimitRightInfoListRegister.hpp>
#include <CMoveMapLimitRightInfoRegister.hpp>
#include <CMoveMapLimitRightPortalRegister.hpp>
#include <CMoveMapLimitRightRegister.hpp>
#include <CMsgDataRegister.hpp>
#include <CMsgProcessRegister.hpp>
#include <CMyCriticalSectionRegister.hpp>
#include <CMyTimerRegister.hpp>
#include <CNationCodeStrRegister.hpp>
#include <CNationCodeStrTableRegister.hpp>
#include <CNationSettingDataBRRegister.hpp>
#include <CNationSettingDataCNRegister.hpp>
#include <CNationSettingDataESRegister.hpp>
#include <CNationSettingDataGBRegister.hpp>
#include <CNationSettingDataIDRegister.hpp>
#include <CNationSettingDataJPRegister.hpp>
#include <CNationSettingDataKRRegister.hpp>
#include <CNationSettingDataNULLRegister.hpp>
#include <CNationSettingDataPHRegister.hpp>
#include <CNationSettingDataRURegister.hpp>
#include <CNationSettingDataRegister.hpp>
#include <CNationSettingDataTHRegister.hpp>
#include <CNationSettingDataTWRegister.hpp>
#include <CNationSettingDataUSRegister.hpp>
#include <CNationSettingFactoryBRRegister.hpp>
#include <CNationSettingFactoryCNRegister.hpp>
#include <CNationSettingFactoryESRegister.hpp>
#include <CNationSettingFactoryGBRegister.hpp>
#include <CNationSettingFactoryGroupRegister.hpp>
#include <CNationSettingFactoryIDRegister.hpp>
#include <CNationSettingFactoryJPRegister.hpp>
#include <CNationSettingFactoryKRRegister.hpp>
#include <CNationSettingFactoryPHRegister.hpp>
#include <CNationSettingFactoryRURegister.hpp>
#include <CNationSettingFactoryRegister.hpp>
#include <CNationSettingFactoryTHRegister.hpp>
#include <CNationSettingFactoryTWRegister.hpp>
#include <CNationSettingFactoryUSRegister.hpp>
#include <CNationSettingManagerRegister.hpp>
#include <CNetCriticalSectionRegister.hpp>
#include <CNetFrameRateRegister.hpp>
#include <CNetIndexListRegister.hpp>
#include <CNetProcessRegister.hpp>
#include <CNetSocketRegister.hpp>
#include <CNetTimerRegister.hpp>
#include <CNetWorkingRegister.hpp>
#include <CNetworkEXRegister.hpp>
#include <CNetworkRegister.hpp>
#include <CNotifyNotifyRaceLeaderSownerUTaxrateRegister.hpp>
#include <CNuclearBombMgrRegister.hpp>
#include <CNuclearBombRegister.hpp>
#include <CObjectListRegister.hpp>
#include <CObjectRegister.hpp>
#include <COreAmountMgrRegister.hpp>
#include <COreCuttingTableRegister.hpp>
#include <CPaintDCRegister.hpp>
#include <CParkingUnitRegister.hpp>
#include <CParticleRegister.hpp>
#include <CPartyModeKillMonsterExpNotifyRegister.hpp>
#include <CPartyPlayerRegister.hpp>
#include <CPathFinderRegister.hpp>
#include <CPathMgrRegister.hpp>
#include <CPcBangFavorRegister.hpp>
#include <CPlayMP3Register.hpp>
#include <CPlayerAttackRegister.hpp>
#include <CPlayerDBRegister.hpp>
#include <CPlayerRegister.hpp>
#include <CPointRegister.hpp>
#include <CPostDataRegister.hpp>
#include <CPostReturnStorageRegister.hpp>
#include <CPostStorageRegister.hpp>
#include <CPostSystemManagerRegister.hpp>
#include <CPotionMgrRegister.hpp>
#include <CPotionParamRegister.hpp>
#include <CPvpCashMngRegister.hpp>
#include <CPvpCashPointRegister.hpp>
#include <CPvpOrderViewRegister.hpp>
#include <CPvpPointLimiterRegister.hpp>
#include <CPvpUserAndGuildRankingSystemRegister.hpp>
#include <CPvpUserRankingInfoRegister.hpp>
#include <CPvpUserRankingTargetUserListRegister.hpp>
#include <CQuestMgrRegister.hpp>
#include <CR3FontRegister.hpp>
#include <CRFCashItemDatabaseRegister.hpp>
#include <CRFDBItemLogRegister.hpp>
#include <CRFMonsterAIMgrRegister.hpp>
#include <CRFNewDatabaseRegister.hpp>
#include <CRFWorldDatabaseRegister.hpp>
#include <CRaceBossMsgControllerRegister.hpp>
#include <CRaceBossWinRateRegister.hpp>
#include <CRaceBuffByHolyQuestProcedureRegister.hpp>
#include <CRaceBuffHolyQuestResultInfoRegister.hpp>
#include <CRaceBuffInfoByHolyQuestListRegister.hpp>
#include <CRaceBuffInfoByHolyQuestRegister.hpp>
#include <CRaceBuffInfoByHolyQuestfGroupRegister.hpp>
#include <CRaceBuffManagerRegister.hpp>
#include <CRadarItemMgrRegister.hpp>
#include <CRealMoveRequestDelayCheckerRegister.hpp>
#include <CRecallEffectControllerRegister.hpp>
#include <CRecallRequestRegister.hpp>
#include <CRecordDataRegister.hpp>
#include <CRectRegister.hpp>
#include <CReturnGateControllerRegister.hpp>
#include <CReturnGateCreateParamRegister.hpp>
#include <CReturnGateRegister.hpp>
#include <CRusiaBillingMgrRegister.hpp>
#include <CSUItemSystemRegister.hpp>
#include <CSetItemEffectRegister.hpp>
#include <CSetItemTypeRegister.hpp>
#include <CSizeRegister.hpp>
#include <CSkyBoxRegister.hpp>
#include <CSyncCSRegister.hpp>
#include <CTakeOutRegister.hpp>
#include <CTalkCrystalCombineManagerRegister.hpp>
#include <CTermRegister.hpp>
#include <CTextureRenderRegister.hpp>
#include <CTimerRegister.hpp>
#include <CToolCollisionFaceRegister.hpp>
#include <CTotalGuildRankInfoRegister.hpp>
#include <CTotalGuildRankManagerRegister.hpp>
#include <CTotalGuildRankRecordRegister.hpp>
#include <CTransportShipRegister.hpp>
#include <CTrapRegister.hpp>
#include <CUnmannedTraderClassInfoFactoryRegister.hpp>
#include <CUnmannedTraderClassInfoRegister.hpp>
#include <CUnmannedTraderClassInfoTableCodeTypeRegister.hpp>
#include <CUnmannedTraderClassInfoTableTypeRegister.hpp>
#include <CUnmannedTraderControllerRegister.hpp>
#include <CUnmannedTraderDivisionInfoRegister.hpp>
#include <CUnmannedTraderGroupDivisionVersionInfoRegister.hpp>
#include <CUnmannedTraderGroupIDInfoRegister.hpp>
#include <CUnmannedTraderGroupItemInfoTableRegister.hpp>
#include <CUnmannedTraderGroupVersionInfoRegister.hpp>
#include <CUnmannedTraderItemCodeInfoRegister.hpp>
#include <CUnmannedTraderItemStateRegister.hpp>
#include <CUnmannedTraderLazyCleanerRegister.hpp>
#include <CUnmannedTraderRegistItemInfoRegister.hpp>
#include <CUnmannedTraderRequestLimiterRegister.hpp>
#include <CUnmannedTraderScheduleRegister.hpp>
#include <CUnmannedTraderSchedulerRegister.hpp>
#include <CUnmannedTraderSortTypeRegister.hpp>
#include <CUnmannedTraderSubClassFactoryRegister.hpp>
#include <CUnmannedTraderSubClassInfoCodeRegister.hpp>
#include <CUnmannedTraderSubClassInfoDefaultRegister.hpp>
#include <CUnmannedTraderSubClassInfoForceLiverGradeRegister.hpp>
#include <CUnmannedTraderSubClassInfoLevelRegister.hpp>
#include <CUnmannedTraderSubClassInfoRegister.hpp>
#include <CUnmannedTraderTaxRateManagerRegister.hpp>
#include <CUnmannedTraderTradeInfoRegister.hpp>
#include <CUnmannedTraderUserInfoRegister.hpp>
#include <CUnmannedTraderUserInfoTableRegister.hpp>
#include <CUserDBRegister.hpp>
#include <CUserRankingProcessRegister.hpp>
#include <CVertexBufferRegister.hpp>
#include <CVoteSystemRegister.hpp>
#include <CWeeklyGuildRankInfoRegister.hpp>
#include <CWeeklyGuildRankManagerRegister.hpp>
#include <CWeeklyGuildRankOwnerInfoRegister.hpp>
#include <CWeeklyGuildRankRecordRegister.hpp>
#include <CWorldScheduleRegister.hpp>
#include <CandidateMgrRegister.hpp>
#include <CandidateRegisterRegister.hpp>
#include <CashDbWorkerRegister.hpp>
#include <CashItemRemoteStoreRegister.hpp>
#include <ClassOrderProcessorRegister.hpp>
#include <ControllerTaxRateRegister.hpp>
#include <D3DXVECTOR2Register.hpp>
#include <Define_the_symbol__ATL_MIXED__Thank_youRegister.hpp>
#include <DfAIMgrRegister.hpp>
#include <DnBuffNodeRegister.hpp>
#include <ElectProcessorRegister.hpp>
#include <EmotionPresentationCheckerRegister.hpp>
#include <FONT2DVERTEXRegister.hpp>
#include <FinalDecisionApplyerRegister.hpp>
#include <FinalDecisionProcessorRegister.hpp>
#include <GMCallMgrRegister.hpp>
#include <GMRequestDataRegister.hpp>
#include <GUILD_BATTLE__CCurrentGuildBattleInfoManagerRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleLoggerRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleRankManagerRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleReservedScheduleListManagerRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleReservedScheduleRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleRewardItemManagerRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleRewardItemRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleScheduleManagerRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleSchedulePoolRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleScheduleRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleSchedulerRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleStateListRegister.hpp>
#include <GUILD_BATTLE__CGuildBattleStateRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleFieldListRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleFieldRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleGuildMemberRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleGuildRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleLoggerRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleManagerRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateCountDownRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateDivideRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateFinRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateInBattleRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateListPoolRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateListRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateNotifyRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateReadyRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateReturnRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateRoundListRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateRoundProcessRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateRoundRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosRegister.hpp>
#include <GUILD_BATTLE__CNormalGuildBattleStateRoundStartRegister.hpp>
#include <GUILD_BATTLE__CPossibleBattleGuildListManagerRegister.hpp>
#include <GUILD_BATTLE__CReservedGuildScheduleDayGroupRegister.hpp>
#include <GUILD_BATTLE__CReservedGuildScheduleMapGroupRegister.hpp>
#include <GUILD_BATTLE__CReservedGuildSchedulePageRegister.hpp>
#include <Global__GlobalRegister.hpp>
#include <GuildCreateEventInfoRegister.hpp>
#include <HACKSHEILD_PARAM_ANTICPRegister.hpp>
#include <ICsSendInterfaceRegister.hpp>
#include <INI_KeyRegister.hpp>
#include <INI_SectionRegister.hpp>
#include <INationGameGuardSystemRegister.hpp>
#include <ItemCombineMgrRegister.hpp>
#include <LendItemMngRegister.hpp>
#include <LendItemSheetRegister.hpp>
#include <LtdWriterRegister.hpp>
#include <LuaParam3Register.hpp>
#include <MD5Register.hpp>
#include <MiningTicketRegister.hpp>
#include <MonsterSFContDamageToleracneRegister.hpp>
#include <MonsterSetInfoDataRegister.hpp>
#include <MonsterStateDataRegister.hpp>
#include <MyTimerRegister.hpp>
#include <PatriarchElectProcessorRegister.hpp>
#include <Player_TL_StatusRegister.hpp>
#include <PotionInnerDataRegister.hpp>
#include <R3CameraRegister.hpp>
#include <RACE_BOSS_MSG__CMsgListManagerRegister.hpp>
#include <RACE_BOSS_MSG__CMsgListRegister.hpp>
#include <RACE_BOSS_MSG__CMsgRegister.hpp>
#include <RECV_DATARegister.hpp>
#include <RFEventBaseRegister.hpp>
#include <RFEvent_ClassRefineRegister.hpp>
#include <Request_Buy_ItemRegister.hpp>
#include <Request_Remain_CashRegister.hpp>
#include <SF_TimerRegister.hpp>
#include <SKILLRegister.hpp>
#include <ScheduleMSGRegister.hpp>
#include <SecondCandidateCrystallizerRegister.hpp>
#include <SkyRegister.hpp>
#include <SunRegister.hpp>
#include <TRC_AutoTradeRegister.hpp>
#include <TaskPoolRegister.hpp>
#include <TaskRegister.hpp>
#include <TimeItemRegister.hpp>
#include <TimeLimitJadeMngRegister.hpp>
#include <TimeLimitJadeRegister.hpp>
#include <TimeLimitMgrRegister.hpp>
#include <TournamentWinnerRegister.hpp>
#include <UIDGeneratorRegister.hpp>
#include <ULIRegister.hpp>
#include <US__AbstractThreadRegister.hpp>
#include <US__CNoneCopyAbleRegister.hpp>
#include <US__CriticalSectionRegister.hpp>
#include <UsRefObjectRegister.hpp>
#include <UsStateTBLRegister.hpp>
#include <Us_FSM_NodeRegister.hpp>
#include <Us_HFSMRegister.hpp>
#include <VoterRegister.hpp>
#include <WheatyExceptionReportRegister.hpp>
#include <WorkerRegister.hpp>
#include <_100_per_random_tableRegister.hpp>
#include <_ANIMUSKEYRegister.hpp>
#include <_ANIMUS_DB_BASERegister.hpp>
#include <_ANIMUS_RETURN_DELAYRegister.hpp>
#include <_ATTACK_DELAY_CHECKERRegister.hpp>
#include <_AUTOMINE_SLOTRegister.hpp>
#include <_AVATOR_DATARegister.hpp>
#include <_AVATOR_DB_BASERegister.hpp>
#include <_BILLING_FORCE_CLOSE_DELAYRegister.hpp>
#include <_BILLING_INFORegister.hpp>
#include <_BUDDY_DB_BASERegister.hpp>
#include <_BUDDY_LISTRegister.hpp>
#include <_COMBINEKEYRegister.hpp>
#include <_CRYMSG_DB_BASERegister.hpp>
#include <_CRYMSG_LISTRegister.hpp>
#include <_CUTTING_DB_BASERegister.hpp>
#include <_ChatStealTargetInfoRegister.hpp>
#include <_ContPotionDataRegister.hpp>
#include <_DB_LOAD_AUTOMINE_MACHINERegister.hpp>
#include <_DB_QRY_SYN_DATARegister.hpp>
#include <_DELAY_PROCESSRegister.hpp>
#include <_DELPOST_DB_BASERegister.hpp>
#include <_DTRADE_ITEMRegister.hpp>
#include <_DTRADE_PARAMRegister.hpp>
#include <_ECONOMY_SYSTEMRegister.hpp>
#include <_EMBELLKEYRegister.hpp>
#include <_EQUIPKEYRegister.hpp>
#include <_EQUIP_DB_BASERegister.hpp>
#include <_Exttrunk_db_loadRegister.hpp>
#include <_FORCEKEYRegister.hpp>
#include <_FORCE_CLOSERegister.hpp>
#include <_FORCE_DB_BASERegister.hpp>
#include <_INVENKEYRegister.hpp>
#include <_INVEN_DB_BASERegister.hpp>
#include <_ITEMCOMBINE_DB_BASERegister.hpp>
#include <_Init_action_point_zoclRegister.hpp>
#include <_LAYER_SETRegister.hpp>
#include <_LINKKEYRegister.hpp>
#include <_LINK_DB_BASERegister.hpp>
#include <_LTDRegister.hpp>
#include <_MASTERY_PARAMRegister.hpp>
#include <_MONEY_SUPPLY_DATARegister.hpp>
#include <_MOVE_LOBBY_DELAYRegister.hpp>
#include <_MULTI_BLOCKRegister.hpp>
#include <_NEAR_DATARegister.hpp>
#include <_NET_BUFFERRegister.hpp>
#include <_NET_TYPE_PARAMRegister.hpp>
#include <_NOT_ARRANGED_AVATOR_DBRegister.hpp>
#include <_NPCQuestIndexTempDataRegister.hpp>
#include <_NameChangeBuddyInfoRegister.hpp>
#include <_PARTICLE_ELEMENTRegister.hpp>
#include <_PCBANG_FAVOR_ITEM_DB_BASERegister.hpp>
#include <_PCBANG_PLAY_TIMERegister.hpp>
#include <_PERSONALAMINE_INVEN_DB_BASERegister.hpp>
#include <_POSTDATA_DB_BASERegister.hpp>
#include <_POSTSTORAGE_DB_BASERegister.hpp>
#include <_POTION_NEXT_USE_TIME_DB_BASERegister.hpp>
#include <_PVPPOINT_LIMIT_DB_BASERegister.hpp>
#include <_PVP_ORDER_VIEW_DB_BASERegister.hpp>
#include <_PVP_RANK_REFRESH_USERRegister.hpp>
#include <_QUEST_CASHRegister.hpp>
#include <_QUEST_CASH_OTHERRegister.hpp>
#include <_QUEST_DB_BASERegister.hpp>
#include <_R3ENGINE_STATERegister.hpp>
#include <_REGEDRegister.hpp>
#include <_REGED_AVATOR_DBRegister.hpp>
#include <_RENAME_POTION_USE_INFORegister.hpp>
#include <_RETURNPOST_DB_BASERegister.hpp>
#include <_SFCONT_DB_BASERegister.hpp>
#include <_SKILL_IDX_PER_MASTERYRegister.hpp>
#include <_SOCK_TYPE_PARAMRegister.hpp>
#include <_SOUND_ENTITIES_LISTRegister.hpp>
#include <_SRANDRegister.hpp>
#include <_STAT_DB_BASERegister.hpp>
#include <_STORAGE_LISTRegister.hpp>
#include <_SUPPLEMENT_DB_BASERegister.hpp>
#include <_SYNC_STATERegister.hpp>
#include <_SYN_DATARegister.hpp>
#include <_SYN_HEADERRegister.hpp>
#include <_THREAD_CONFIGRegister.hpp>
#include <_TOWER_PARAMRegister.hpp>
#include <_TRADE_DB_BASERegister.hpp>
#include <_TRAP_PARAMRegister.hpp>
#include <_TRUNK_DB_BASERegister.hpp>
#include <_UNIT_DB_BASERegister.hpp>
#include <_WAIT_ENTER_ACCOUNTRegister.hpp>
#include <_WEAPON_PARAMRegister.hpp>
#include <__TEMP_WAIT_TOWERRegister.hpp>
#include <__add_loot_itemRegister.hpp>
#include <__add_monsterRegister.hpp>
#include <__add_timeRegister.hpp>
#include <__change_monsterRegister.hpp>
#include <__dp_mission_potalRegister.hpp>
#include <__dummy_blockRegister.hpp>
#include <__error_infoRegister.hpp>
#include <__guild_list_pageRegister.hpp>
#include <__holy_keeper_dataRegister.hpp>
#include <__holy_stone_dataRegister.hpp>
#include <__inner_checkRegister.hpp>
#include <__monster_groupRegister.hpp>
#include <__respawn_monsterRegister.hpp>
#include <__respond_checkRegister.hpp>
#include <_a_trade_adjust_price_result_zoclRegister.hpp>
#include <_a_trade_clear_item_result_zoclRegister.hpp>
#include <_action_point_system_iniRegister.hpp>
#include <_add_char_result_zoneRegister.hpp>
#include <_add_lend_item_result_zoclRegister.hpp>
#include <_alive_char_result_zoclRegister.hpp>
#include <_alter_action_point_zoclRegister.hpp>
#include <_alter_cont_effect_time_zoclRegister.hpp>
#include <_animus_create_setdataRegister.hpp>
#include <_animus_db_loadRegister.hpp>
#include <_animus_download_result_zoclRegister.hpp>
#include <_announ_message_receipt_udpRegister.hpp>
#include <_apex_block_request_wracRegister.hpp>
#include <_apex_idRegister.hpp>
#include <_apex_send_ipRegister.hpp>
#include <_apex_send_loginRegister.hpp>
#include <_apex_send_logoutRegister.hpp>
#include <_apex_send_transRegister.hpp>
#include <_atrade_taxrate_result_zoclRegister.hpp>
#include <_attack_count_result_zoclRegister.hpp>
#include <_attack_force_result_zoclRegister.hpp>
#include <_attack_gen_result_zoclRegister.hpp>
#include <_attack_keeper_inform_zoclRegister.hpp>
#include <_attack_paramRegister.hpp>
#include <_attack_selfdestruction_result_zoclRegister.hpp>
#include <_attack_siege_result_zoclRegister.hpp>
#include <_attack_skill_result_zoclRegister.hpp>
#include <_attack_trap_inform_zoclRegister.hpp>
#include <_attack_unit_result_zoclRegister.hpp>
#include <_bag_db_loadRegister.hpp>
#include <_base_download_result_zoclRegister.hpp>
#include <_base_fldRegister.hpp>
#include <_be_damaged_charRegister.hpp>
#include <_be_damaged_playerRegister.hpp>
#include <_bind_dummyRegister.hpp>
#include <_buddy_download_result_zoclRegister.hpp>
#include <_buy_offerRegister.hpp>
#include <_buy_store_success_zoclRegister.hpp>
#include <_cancel_raceboss_msg_result_zoctRegister.hpp>
#include <_candidate_infoRegister.hpp>
#include <_cash_discount_Register.hpp>
#include <_cash_discount_event_inform_zoclRegister.hpp>
#include <_cash_discount_ini_Register.hpp>
#include <_cash_eventRegister.hpp>
#include <_cash_event_inform_zoclRegister.hpp>
#include <_cash_event_iniRegister.hpp>
#include <_cashdb_setting_request_wracRegister.hpp>
#include <_ccrfg_detect_alretRegister.hpp>
#include <_character_create_setdataRegister.hpp>
#include <_character_db_loadRegister.hpp>
#include <_character_disconnect_result_wracRegister.hpp>
#include <_chat_lock_inform_zoclRegister.hpp>
#include <_chat_message_receipt_udpRegister.hpp>
#include <_chat_multi_far_trans_zoclRegister.hpp>
#include <_chat_steal_message_gm_zoclRegister.hpp>
#include <_check_queryRegister.hpp>
#include <_check_speed_hack_ansRegister.hpp>
#include <_class_valueRegister.hpp>
#include <_coll_pointRegister.hpp>
#include <_combine_ex_item_result_zoclRegister.hpp>
#include <_combine_lend_item_result_zoclRegister.hpp>
#include <_con_event_Register.hpp>
#include <_conditional_event_inform_zoclRegister.hpp>
#include <_connection_status_result_zoctRegister.hpp>
#include <_create_holy_master_zoclRegister.hpp>
#include <_cum_download_result_zoclRegister.hpp>
#include <_darkhole_answer_reenter_result_zoclRegister.hpp>
#include <_darkhole_ask_reenter_inform_zoclRegister.hpp>
#include <_darkhole_channel_close_inform_zoclRegister.hpp>
#include <_darkhole_clear_out_result_zoclRegister.hpp>
#include <_darkhole_create_setdataRegister.hpp>
#include <_darkhole_create_zoclRegister.hpp>
#include <_darkhole_destroy_zoclRegister.hpp>
#include <_darkhole_enter_result_zoclRegister.hpp>
#include <_darkhole_fixpositon_zoclRegister.hpp>
#include <_darkhole_giveup_out_result_zoclRegister.hpp>
#include <_darkhole_job_count_inform_zoclRegister.hpp>
#include <_darkhole_job_pass_inform_zoclRegister.hpp>
#include <_darkhole_leader_change_inform_zoclRegister.hpp>
#include <_darkhole_member_info_inform_zoclRegister.hpp>
#include <_darkhole_mission_info_inform_zoclRegister.hpp>
#include <_darkhole_mission_pass_inform_zoclRegister.hpp>
#include <_darkhole_mission_quest_inform_zoclRegister.hpp>
#include <_darkhole_new_member_inform_zoclRegister.hpp>
#include <_darkhole_new_mission_inform_zoclRegister.hpp>
#include <_darkhole_open_all_portal_by_result_inform_zoclRegister.hpp>
#include <_darkhole_open_portal_by_react_inform_zoclRegister.hpp>
#include <_darkhole_open_result_zoclRegister.hpp>
#include <_darkhole_outof_member_inform_zoclRegister.hpp>
#include <_darkhole_quest_info_inform_zoclRegister.hpp>
#include <_darkhole_real_add_time_inform_zoclRegister.hpp>
#include <_darkhole_real_msg_inform_zoclRegister.hpp>
#include <_darkhole_state_change_zoclRegister.hpp>
#include <_darkhole_timeout_inform_zoclRegister.hpp>
#include <_db_golden_box_itemRegister.hpp>
#include <_del_char_result_zoneRegister.hpp>
#include <_detected_char_listRegister.hpp>
#include <_dh_job_setupRegister.hpp>
#include <_dh_mission_mgrRegister.hpp>
#include <_dh_mission_setupRegister.hpp>
#include <_dh_player_mgrRegister.hpp>
#include <_dh_quest_setupRegister.hpp>
#include <_dh_reward_sub_setupRegister.hpp>
#include <_dummy_positionRegister.hpp>
#include <_economy_history_dataRegister.hpp>
#include <_effect_parameterRegister.hpp>
#include <_embellish_db_loadRegister.hpp>
#include <_enter_lobby_report_wracRegister.hpp>
#include <_enter_world_request_wracRegister.hpp>
#include <_enter_world_result_zoneRegister.hpp>
#include <_equip_db_loadRegister.hpp>
#include <_equip_up_item_lv_limit_zoclRegister.hpp>
#include <_event_participant_classrefineRegister.hpp>
#include <_event_respawnRegister.hpp>
#include <_event_setRegister.hpp>
#include <_event_set_lootingRegister.hpp>
#include <_exchange_lend_item_result_zoclRegister.hpp>
#include <_fireguard_block_request_wracRegister.hpp>
#include <_force_db_loadRegister.hpp>
#include <_force_download_result_zoclRegister.hpp>
#include <_gm_msg_gmcall_list_response_zoclRegister.hpp>
#include <_goldbox_indexRegister.hpp>
#include <_golden_box_itemRegister.hpp>
#include <_golden_box_item_eventRegister.hpp>
#include <_golden_box_item_iniRegister.hpp>
#include <_good_storage_infoRegister.hpp>
#include <_guild_alter_member_grade_inform_zoclRegister.hpp>
#include <_guild_alter_member_state_inform_zoclRegister.hpp>
#include <_guild_applier_download_zoclRegister.hpp>
#include <_guild_applier_infoRegister.hpp>
#include <_guild_battle_get_gravity_stone_result_zoclRegister.hpp>
#include <_guild_battle_goal_result_zoclRegister.hpp>
#include <_guild_battle_rank_list_result_zoclRegister.hpp>
#include <_guild_battle_reserved_schedule_result_zoclRegister.hpp>
#include <_guild_battle_suggest_matterRegister.hpp>
#include <_guild_battle_suggest_request_result_zoclRegister.hpp>
#include <_guild_honor_list_result_zoclRegister.hpp>
#include <_guild_list_result_zoclRegister.hpp>
#include <_guild_manage_request_clzoRegister.hpp>
#include <_guild_master_infoRegister.hpp>
#include <_guild_member_buddy_download_zoclRegister.hpp>
#include <_guild_member_download_zoclRegister.hpp>
#include <_guild_member_infoRegister.hpp>
#include <_guild_money_io_download_zoclRegister.hpp>
#include <_guild_query_info_result_zoclRegister.hpp>
#include <_guild_vote_process_inform_zoclRegister.hpp>
#include <_guildroom_enter_result_zoclRegister.hpp>
#include <_guildroom_out_result_zoclRegister.hpp>
#include <_guildroom_rent_result_zoclRegister.hpp>
#include <_happen_event_contRegister.hpp>
#include <_holy_quest_report_wracRegister.hpp>
#include <_insert_new_quest_inform_zoclRegister.hpp>
#include <_insert_next_quest_inform_zoclRegister.hpp>
#include <_insert_trc_infoRegister.hpp>
#include <_inven_download_result_zoclRegister.hpp>
#include <_item_fanfare_zoclRegister.hpp>
#include <_itembox_create_setdataRegister.hpp>
#include <_itembox_take_add_result_zoclRegister.hpp>
#include <_itembox_take_new_result_zoclRegister.hpp>
#include <_job_sub_setupRegister.hpp>
#include <_keeper_create_setdataRegister.hpp>
#include <_limit_amount_infoRegister.hpp>
#include <_limit_item_db_dataRegister.hpp>
#include <_limit_item_infoRegister.hpp>
#include <_limit_item_num_info_zoclRegister.hpp>
#include <_limitedsale_event_inform_zoclRegister.hpp>
#include <_log_case_charselectRegister.hpp>
#include <_log_change_class_after_init_classRegister.hpp>
#include <_log_sheet_economyRegister.hpp>
#include <_log_sheet_lvRegister.hpp>
#include <_log_sheet_usernumRegister.hpp>
#include <_logout_account_request_wracRegister.hpp>
#include <_map_fldRegister.hpp>
#include <_map_rateRegister.hpp>
#include <_mastery_up_dataRegister.hpp>
#include <_max_pointRegister.hpp>
#include <_messageRegister.hpp>
#include <_mon_activeRegister.hpp>
#include <_mon_blockRegister.hpp>
#include <_money_supply_gatering_inform_zowbRegister.hpp>
#include <_monster_create_setdataRegister.hpp>
#include <_monster_sp_groupRegister.hpp>
#include <_move_to_own_stonemap_inform_zoclRegister.hpp>
#include <_move_to_own_stonemap_result_zoclRegister.hpp>
#include <_moveout_user_result_zoneRegister.hpp>
#include <_not_arranged_char_inform_zoclRegister.hpp>
#include <_notice_move_limit_map_msg_zoclRegister.hpp>
#include <_notify_cont_play_time_zoclRegister.hpp>
#include <_notify_coupon_ensure_time_zoclRegister.hpp>
#include <_notify_coupon_error_zoclRegister.hpp>
#include <_notify_local_time_result_zoclRegister.hpp>
#include <_notify_max_pvp_point_zoclRegister.hpp>
#include <_notify_not_use_premium_cashitem_zoclRegister.hpp>
#include <_notify_pvp_cash_point_error_zoclRegister.hpp>
#include <_notify_race_boss_winrate_zoclRegister.hpp>
#include <_notify_remain_coupon_zoclRegister.hpp>
#include <_npc_create_setdataRegister.hpp>
#include <_npc_quest_list_result_zoclRegister.hpp>
#include <_npclink_check_item_result_zoclRegister.hpp>
#include <_nuclear_bomb_current_state_zoclRegister.hpp>
#include <_nuclear_bomb_destruction_zoclRegister.hpp>
#include <_nuclear_bomb_drop_result_zoclRegister.hpp>
#include <_nuclear_bomb_explosion_result_zoclRegister.hpp>
#include <_nuclear_bomb_position_inform_zoclRegister.hpp>
#include <_nuclear_create_setdataRegister.hpp>
#include <_nuclear_explosion_success_zoclRegister.hpp>
#include <_nuclear_find_rader_result_zoclRegister.hpp>
#include <_nuclear_position_result_zoclRegister.hpp>
#include <_nuclear_result_code_zoclRegister.hpp>
#include <_object_create_setdataRegister.hpp>
#include <_object_idRegister.hpp>
#include <_object_list_pointRegister.hpp>
#include <_open_world_request_wracRegister.hpp>
#include <_ore_cutting_result_zoclRegister.hpp>
#include <_other_shape_all_zoclRegister.hpp>
#include <_other_shape_part_zoclRegister.hpp>
#include <_param_cashRegister.hpp>
#include <_param_cash_rollbackRegister.hpp>
#include <_param_cash_selectRegister.hpp>
#include <_param_cash_total_sellingRegister.hpp>
#include <_param_cash_updateRegister.hpp>
#include <_param_cashitem_dblogRegister.hpp>
#include <_parkingunit_create_setdataRegister.hpp>
#include <_party_join_joiner_result_zoclRegister.hpp>
#include <_party_member_info_updRegister.hpp>
#include <_personal_amine_errmsg_zoclRegister.hpp>
#include <_personal_amine_fixpos_zoclRegister.hpp>
#include <_personal_amine_infoui_open_zoclRegister.hpp>
#include <_personal_amine_inven_db_loadRegister.hpp>
#include <_personal_amine_make_storage_zoclRegister.hpp>
#include <_personal_amine_mineore_zoclRegister.hpp>
#include <_personal_automine_alter_dur_zoclRegister.hpp>
#include <_personal_automine_attacked_zoclRegister.hpp>
#include <_personal_automine_battery_extract_zoclRegister.hpp>
#include <_personal_automine_battery_insert_zoclRegister.hpp>
#include <_personal_automine_current_state_zoclRegister.hpp>
#include <_personal_automine_delbattery_zoclRegister.hpp>
#include <_personal_automine_download_zoclRegister.hpp>
#include <_personal_automine_install_zoclRegister.hpp>
#include <_personal_automine_popore_zoclRegister.hpp>
#include <_personal_automine_selore_zoclRegister.hpp>
#include <_personal_automine_stop_zoclRegister.hpp>
#include <_personal_automine_uninstall_circle_zoclRegister.hpp>
#include <_personal_automine_uninstall_zoclRegister.hpp>
#include <_portal_dummyRegister.hpp>
#include <_possible_battle_guild_list_result_zoclRegister.hpp>
#include <_post_content_result_zoclRegister.hpp>
#include <_post_result_zoclRegister.hpp>
#include <_pt_appoint_inform_request_zoclRegister.hpp>
#include <_pt_automine_charge_money_db_update_fail_zoclRegister.hpp>
#include <_pt_automine_getoutore_zoclRegister.hpp>
#include <_pt_automine_info_zoclRegister.hpp>
#include <_pt_automine_result_zoclRegister.hpp>
#include <_pt_automine_state_zoclRegister.hpp>
#include <_pt_inform_appoint_zoclRegister.hpp>
#include <_pt_inform_commission_income_zoclRegister.hpp>
#include <_pt_inform_punishment_zoclRegister.hpp>
#include <_pt_inform_tax_rate_zoclRegister.hpp>
#include <_pt_notify_final_decisionRegister.hpp>
#include <_pt_notify_vote_score_zoclRegister.hpp>
#include <_pt_propose_appoint_zoclRegister.hpp>
#include <_pt_query_appoint_zoclRegister.hpp>
#include <_pt_result_appoint_zoclRegister.hpp>
#include <_pt_result_change_tax_rate_zoclRegister.hpp>
#include <_pt_result_code_zoclRegister.hpp>
#include <_pt_result_fcandidacy_list_zoclRegister.hpp>
#include <_pt_result_punishment_zoclRegister.hpp>
#include <_pt_trans_votepaper_zoclRegister.hpp>
#include <_pvp_cash_recover_itemlist_result_zoclRegister.hpp>
#include <_pvp_order_view_end_zoclRegister.hpp>
#include <_pvp_order_view_inform_zoclRegister.hpp>
#include <_pvp_order_view_point_inform_zoclRegister.hpp>
#include <_pvp_rank_list_result_data_zoclRegister.hpp>
#include <_qry_case_addguildbattlescheduleRegister.hpp>
#include <_qry_case_addpvppointRegister.hpp>
#include <_qry_case_alive_charRegister.hpp>
#include <_qry_case_all_store_limit_itemRegister.hpp>
#include <_qry_case_amine_battery_dischargeRegister.hpp>
#include <_qry_case_amine_batterychargeRegister.hpp>
#include <_qry_case_amine_mineoreRegister.hpp>
#include <_qry_case_amine_moveoreRegister.hpp>
#include <_qry_case_amine_newownerRegister.hpp>
#include <_qry_case_amine_seloreRegister.hpp>
#include <_qry_case_amine_workstateRegister.hpp>
#include <_qry_case_buyemblemRegister.hpp>
#include <_qry_case_character_renameRegister.hpp>
#include <_qry_case_cheat_player_vote_infoRegister.hpp>
#include <_qry_case_contsaveRegister.hpp>
#include <_qry_case_dest_guild_out_guildbattlecostRegister.hpp>
#include <_qry_case_discharge_patriarchRegister.hpp>
#include <_qry_case_disjointguildRegister.hpp>
#include <_qry_case_forceleaveRegister.hpp>
#include <_qry_case_gm_greetingmsgRegister.hpp>
#include <_qry_case_guild_greetingmsgRegister.hpp>
#include <_qry_case_in_atrade_taxRegister.hpp>
#include <_qry_case_in_guildbattlecostRegister.hpp>
#include <_qry_case_in_guildbattlerewardmoneyRegister.hpp>
#include <_qry_case_inputgmoneyRegister.hpp>
#include <_qry_case_insert_candidateRegister.hpp>
#include <_qry_case_insert_orelogRegister.hpp>
#include <_qry_case_insert_patriarch_commRegister.hpp>
#include <_qry_case_insert_timelimit_infoRegister.hpp>
#include <_qry_case_insertguildRegister.hpp>
#include <_qry_case_insertitemRegister.hpp>
#include <_qry_case_joinacguildRegister.hpp>
#include <_qry_case_load_guildbattle_totalrecordRegister.hpp>
#include <_qry_case_loadguildbattlerankRegister.hpp>
#include <_qry_case_lobby_logoutRegister.hpp>
#include <_qry_case_make_storageRegister.hpp>
#include <_qry_case_outputgmoneyRegister.hpp>
#include <_qry_case_post_content_getRegister.hpp>
#include <_qry_case_post_list_regiRegister.hpp>
#include <_qry_case_post_return_list_getRegister.hpp>
#include <_qry_case_post_sendRegister.hpp>
#include <_qry_case_post_serial_checkRegister.hpp>
#include <_qry_case_post_storage_list_getRegister.hpp>
#include <_qry_case_race_greetingmsgRegister.hpp>
#include <_qry_case_raceboss_accumulation_winrateRegister.hpp>
#include <_qry_case_rank_racerank_guildrankRegister.hpp>
#include <_qry_case_request_refundRegister.hpp>
#include <_qry_case_select_charserialRegister.hpp>
#include <_qry_case_select_guild_master_lastconnRegister.hpp>
#include <_qry_case_select_patriarch_commRegister.hpp>
#include <_qry_case_select_timelimit_infoRegister.hpp>
#include <_qry_case_selfleaveRegister.hpp>
#include <_qry_case_sendwebracebosssmsRegister.hpp>
#include <_qry_case_src_guild_out_guildbattlecostRegister.hpp>
#include <_qry_case_unmandtrader_cheat_updateregisttimeRegister.hpp>
#include <_qry_case_unmandtrader_re_registsingleitemRegister.hpp>
#include <_qry_case_unmandtrader_updateitemstateRegister.hpp>
#include <_qry_case_update_data_for_post_sendRegister.hpp>
#include <_qry_case_update_data_for_tradeRegister.hpp>
#include <_qry_case_update_guildmasterRegister.hpp>
#include <_qry_case_update_honor_guildRegister.hpp>
#include <_qry_case_update_mineoreRegister.hpp>
#include <_qry_case_update_player_vote_infoRegister.hpp>
#include <_qry_case_update_punishmentRegister.hpp>
#include <_qry_case_update_server_reset_tokenRegister.hpp>
#include <_qry_case_update_user_guild_dataRegister.hpp>
#include <_qry_case_update_vote_availableRegister.hpp>
#include <_qry_case_updateclearguildbattleDayInfoRegister.hpp>
#include <_qry_case_updatedrawguildbattlerankRegister.hpp>
#include <_qry_case_updatereservedscheduleRegister.hpp>
#include <_qry_case_updateweeklyguildpvppointsumRegister.hpp>
#include <_qry_case_updatewinloseguildbattlerankRegister.hpp>
#include <_qry_sheet_deleteRegister.hpp>
#include <_qry_sheet_insertRegister.hpp>
#include <_qry_sheet_loadRegister.hpp>
#include <_qry_sheet_lobbyRegister.hpp>
#include <_qry_sheet_logoutRegister.hpp>
#include <_qry_sheet_regedRegister.hpp>
#include <_quest_check_resultRegister.hpp>
#include <_quest_download_result_zoclRegister.hpp>
#include <_quest_dummyRegister.hpp>
#include <_quest_fail_resultRegister.hpp>
#include <_quest_history_download_result_zoclRegister.hpp>
#include <_quick_linkRegister.hpp>
#include <_radar_char_list_result_zoclRegister.hpp>
#include <_react_areaRegister.hpp>
#include <_react_objRegister.hpp>
#include <_react_sub_setupRegister.hpp>
#include <_record_bin_headerRegister.hpp>
#include <_rege_char_dataRegister.hpp>
#include <_reged_char_result_zoneRegister.hpp>
#include <_res_dummyRegister.hpp>
#include <_result_csi_buy_zoclRegister.hpp>
#include <_result_csi_error_zoclRegister.hpp>
#include <_result_csi_goods_list_zoclRegister.hpp>
#include <_safe_dummyRegister.hpp>
#include <_sel_char_result_zoneRegister.hpp>
#include <_sel_patriarch_elect_stateRegister.hpp>
#include <_select_avator_report_wracRegister.hpp>
#include <_server_notify_inform_zoneRegister.hpp>
#include <_server_rate_realtime_loadRegister.hpp>
#include <_sf_continousRegister.hpp>
#include <_sf_delay_download_result_zoclRegister.hpp>
#include <_skill_lv_up_dataRegister.hpp>
#include <_socketRegister.hpp>
#include <_start_dummyRegister.hpp>
#include <_start_world_request_wracRegister.hpp>
#include <_started_vote_inform_zoclRegister.hpp>
#include <_starting_vote_inform_zoclRegister.hpp>
#include <_stone_create_setdataRegister.hpp>
#include <_stop_world_request_wracRegister.hpp>
#include <_storage_refresh_inform_zoclRegister.hpp>
#include <_store_dummyRegister.hpp>
#include <_store_list_result_zoclRegister.hpp>
#include <_suggested_matterRegister.hpp>
#include <_suggested_matter_change_taxrateRegister.hpp>
#include <_talik_crystal_exchange_zoclRegister.hpp>
#include <_talik_recvr_listRegister.hpp>
#include <_talk_crystal_matrial_combine_nodeRegister.hpp>
#include <_target_monster_aggro_inform_zoclRegister.hpp>
#include <_target_monster_contsf_allinform_zoclRegister.hpp>
#include <_target_player_damage_contsf_allinform_zoclRegister.hpp>
#include <_thread_parameterRegister.hpp>
#include <_throw_skill_result_one_zoclRegister.hpp>
#include <_throw_skill_result_other_zoclRegister.hpp>
#include <_throw_unit_result_one_zoclRegister.hpp>
#include <_throw_unit_result_other_zoclRegister.hpp>
#include <_total_countRegister.hpp>
#include <_total_guild_rank_result_zoclRegister.hpp>
#include <_tower_create_setdataRegister.hpp>
#include <_trans_account_report_wracRegister.hpp>
#include <_trans_gm_msg_inform_zoclRegister.hpp>
#include <_trap_create_setdataRegister.hpp>
#include <_trunk_db_loadRegister.hpp>
#include <_trunk_download_result_zoclRegister.hpp>
#include <_trunk_est_result_zoclRegister.hpp>
#include <_unit_bullet_paramRegister.hpp>
#include <_unit_download_result_zoclRegister.hpp>
#include <_unit_pack_fill_result_zoclRegister.hpp>
#include <_unmannedtrader_Regist_item_inform_zoclRegister.hpp>
#include <_unmannedtrader_Sell_Wait_item_inform_zoclRegister.hpp>
#include <_unmannedtrader_buy_item_result_zoclRegister.hpp>
#include <_unmannedtrader_close_item_inform_zoclRegister.hpp>
#include <_unmannedtrader_continue_item_inform_zoclRegister.hpp>
#include <_unmannedtrader_re_regist_result_zoclRegister.hpp>
#include <_unmannedtrader_regist_item_success_result_zoclRegister.hpp>
#include <_update_candidate_wincount_packingRegister.hpp>
#include <_user_num_report_wracRegister.hpp>
#include <_weekly_guild_rank_result_zoclRegister.hpp>
#include <_world_account_ping_wracRegister.hpp>
#include <_worlddb_arrange_char_infoRegister.hpp>
#include <_worlddb_npc_quest_complete_historyRegister.hpp>
#include <_worlddb_potion_delay_infoRegister.hpp>
#include <_worlddb_sf_delay_infoRegister.hpp>
#include <cStaticMember_PlayerRegister.hpp>
#include <qry_case_cash_limsaleRegister.hpp>
#include <qry_case_golden_box_itemRegister.hpp>
#include <si_effectRegister.hpp>
#include <si_interpretRegister.hpp>
#include <std___Container_baseRegister.hpp>
#include <std___Iterator_baseRegister.hpp>
#include <std___LockitRegister.hpp>
#include <std___String_baseRegister.hpp>
#include <std__bad_allocRegister.hpp>
#include <std__exceptionRegister.hpp>
#include <std__ios_baseRegister.hpp>
#include <std__length_errorRegister.hpp>
#include <std__logic_errorRegister.hpp>
#include <std__out_of_rangeRegister.hpp>
#include <strFILERegister.hpp>
#include <type_infoRegister.hpp>


START_ATF_NAMESPACE
    
    class CATFCoreRegistry
    {
    public:
        CATFCoreRegistry() {
            _registry.emplace_back(::std::make_shared<Global::Register::GlobalRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRFNewDatabaseRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNetCriticalSectionRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLogFileRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRFWorldDatabaseRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_gm_greetingmsgRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_race_greetingmsgRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_guild_greetingmsgRegister>());
            _registry.emplace_back(::std::make_shared<Register::_NOT_ARRANGED_AVATOR_DBRegister>());
            _registry.emplace_back(::std::make_shared<Register::_worlddb_arrange_char_infoRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CTimeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_INVENKEYRegister>());
            _registry.emplace_back(::std::make_shared<Register::_AUTOMINE_SLOTRegister>());
            _registry.emplace_back(::std::make_shared<Register::_DB_LOAD_AUTOMINE_MACHINERegister>());
            _registry.emplace_back(::std::make_shared<Register::CPostDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_limit_item_db_dataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_all_store_limit_itemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_sel_patriarch_elect_stateRegister>());
            _registry.emplace_back(::std::make_shared<Register::_candidate_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_request_refundRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_punishmentRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_honor_list_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_worlddb_sf_delay_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_worlddb_npc_quest_complete_historyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_PCBANG_PLAY_TIMERegister>());
            _registry.emplace_back(::std::make_shared<Register::_worlddb_potion_delay_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::TournamentWinnerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_rege_char_dataRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CTraceCategoryRegister>());
            _registry.emplace_back(::std::make_shared<Register::_SRANDRegister>());
            _registry.emplace_back(::std::make_shared<Register::_BILLING_INFORegister>());
            _registry.emplace_back(::std::make_shared<Register::_WAIT_ENTER_ACCOUNTRegister>());
            _registry.emplace_back(::std::make_shared<Register::CFrameRateRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMsgDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_messageRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMyCriticalSectionRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMsgProcessRegister>());
            _registry.emplace_back(::std::make_shared<Register::CConnNumPHMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMyTimerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_DB_QRY_SYN_DATARegister>());
            _registry.emplace_back(::std::make_shared<Register::CNetIndexListRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCheckSumRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRecordDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_record_bin_headerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CItemLootTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::CEventLootTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::COreCuttingTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::CItemUpgradeTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::_base_fldRegister>());
            _registry.emplace_back(::std::make_shared<Register::_monster_sp_groupRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterSPGroupTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMainThreadRegister>());
            _registry.emplace_back(::std::make_shared<Register::RFEventBaseRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGameObjectRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCharacterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPlayerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_object_idRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMapDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLevelRegister>());
            _registry.emplace_back(::std::make_shared<Register::CVertexBufferRegister>());
            _registry.emplace_back(::std::make_shared<Register::CIndexBufferRegister>());
            _registry.emplace_back(::std::make_shared<Register::CEntityRegister>());
            _registry.emplace_back(::std::make_shared<Register::_PARTICLE_ELEMENTRegister>());
            _registry.emplace_back(::std::make_shared<Register::CParticleRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMergeFileRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMergeFileManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_SOUND_ENTITIES_LISTRegister>());
            _registry.emplace_back(::std::make_shared<Register::CExchangeEventRegister>());
            _registry.emplace_back(::std::make_shared<Register::CExtDummyRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPathFinderRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAlphaRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBspRegister>());
            _registry.emplace_back(::std::make_shared<Register::CSkyBoxRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAniCameraRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTimerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CObjectListRegister>());
            _registry.emplace_back(::std::make_shared<Register::_object_list_pointRegister>());
            _registry.emplace_back(::std::make_shared<Register::_dummy_positionRegister>());
            _registry.emplace_back(::std::make_shared<Register::_mon_blockRegister>());
            _registry.emplace_back(::std::make_shared<Register::_mon_activeRegister>());
            _registry.emplace_back(::std::make_shared<Register::CIniFileRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::_Container_baseRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::_Iterator_baseRegister>());
            _registry.emplace_back(::std::make_shared<Register::INI_SectionRegister>());
            _registry.emplace_back(::std::make_shared<Register::INI_KeyRegister>());
            _registry.emplace_back(::std::make_shared<Register::BossScheduleRegister>());
            _registry.emplace_back(::std::make_shared<US::Register::CNoneCopyAbleRegister>());
            _registry.emplace_back(::std::make_shared<US::Register::AbstractThreadRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBossMonsterScheduleSystemRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMapOperationRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMapDataTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::_map_fldRegister>());
            _registry.emplace_back(::std::make_shared<Register::BossSchedule_MapRegister>());
            _registry.emplace_back(::std::make_shared<Register::BossSchedule_TBLRegister>());
            _registry.emplace_back(::std::make_shared<Register::ScheduleMSGRegister>());
            _registry.emplace_back(::std::make_shared<US::Register::CriticalSectionRegister>());
            _registry.emplace_back(::std::make_shared<Register::_MULTI_BLOCKRegister>());
            _registry.emplace_back(::std::make_shared<Register::_LAYER_SETRegister>());
            _registry.emplace_back(::std::make_shared<Register::_portal_dummyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_store_dummyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_start_dummyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_bind_dummyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_res_dummyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_quest_dummyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_safe_dummyRegister>());
            _registry.emplace_back(::std::make_shared<Register::CDummyPosTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::_100_per_random_tableRegister>());
            _registry.emplace_back(::std::make_shared<Register::_sf_continousRegister>());
            _registry.emplace_back(::std::make_shared<Register::_effect_parameterRegister>());
            _registry.emplace_back(::std::make_shared<Register::_STORAGE_LISTRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUserDBRegister>());
            _registry.emplace_back(::std::make_shared<Register::_EQUIPKEYRegister>());
            _registry.emplace_back(::std::make_shared<Register::_REGED_AVATOR_DBRegister>());
            _registry.emplace_back(::std::make_shared<Register::_REGEDRegister>());
            _registry.emplace_back(::std::make_shared<Register::_AVATOR_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_LINKKEYRegister>());
            _registry.emplace_back(::std::make_shared<Register::_LINK_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_EMBELLKEYRegister>());
            _registry.emplace_back(::std::make_shared<Register::_EQUIP_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_FORCEKEYRegister>());
            _registry.emplace_back(::std::make_shared<Register::_FORCE_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_ANIMUSKEYRegister>());
            _registry.emplace_back(::std::make_shared<Register::_ANIMUS_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_STAT_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_INVEN_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_CUTTING_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_QUEST_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_UNIT_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_SFCONT_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_TRADE_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_BUDDY_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_TRUNK_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_COMBINEKEYRegister>());
            _registry.emplace_back(::std::make_shared<Register::_ITEMCOMBINE_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_POSTSTORAGE_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_RETURNPOST_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_DELPOST_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_POSTDATA_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_CRYMSG_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_PERSONALAMINE_INVEN_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_PVPPOINT_LIMIT_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_PVP_ORDER_VIEW_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_SUPPLEMENT_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_POTION_NEXT_USE_TIME_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_PCBANG_FAVOR_ITEM_DB_BASERegister>());
            _registry.emplace_back(::std::make_shared<Register::_AVATOR_DATARegister>());
            _registry.emplace_back(::std::make_shared<Register::_SYNC_STATERegister>());
            _registry.emplace_back(::std::make_shared<Register::_detected_char_listRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRadarItemMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPartyPlayerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CDarkHoleChannelRegister>());
            _registry.emplace_back(::std::make_shared<Register::CDarkHoleRegister>());
            _registry.emplace_back(::std::make_shared<Register::_dh_quest_setupRegister>());
            _registry.emplace_back(::std::make_shared<Register::_dh_mission_setupRegister>());
            _registry.emplace_back(::std::make_shared<Register::__monster_groupRegister>());
            _registry.emplace_back(::std::make_shared<Register::_react_objRegister>());
            _registry.emplace_back(::std::make_shared<Register::__dummy_blockRegister>());
            _registry.emplace_back(::std::make_shared<Register::_react_areaRegister>());
            _registry.emplace_back(::std::make_shared<Register::__add_monsterRegister>());
            _registry.emplace_back(::std::make_shared<Register::__add_loot_itemRegister>());
            _registry.emplace_back(::std::make_shared<Register::__change_monsterRegister>());
            _registry.emplace_back(::std::make_shared<Register::__inner_checkRegister>());
            _registry.emplace_back(::std::make_shared<Register::__respond_checkRegister>());
            _registry.emplace_back(::std::make_shared<Register::__respawn_monsterRegister>());
            _registry.emplace_back(::std::make_shared<Register::__add_timeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_dh_job_setupRegister>());
            _registry.emplace_back(::std::make_shared<Register::_job_sub_setupRegister>());
            _registry.emplace_back(::std::make_shared<Register::_react_sub_setupRegister>());
            _registry.emplace_back(::std::make_shared<Register::__dp_mission_potalRegister>());
            _registry.emplace_back(::std::make_shared<Register::_dh_reward_sub_setupRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLootingMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAggroNodeRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterAggroMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterHierarchyRegister>());
            _registry.emplace_back(::std::make_shared<Register::MonsterSFContDamageToleracneRegister>());
            _registry.emplace_back(::std::make_shared<Register::EmotionPresentationCheckerRegister>());
            _registry.emplace_back(::std::make_shared<Register::MonsterStateDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterSkillRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterSkillPoolRegister>());
            _registry.emplace_back(::std::make_shared<Register::_event_respawnRegister>());
            _registry.emplace_back(::std::make_shared<Register::_event_setRegister>());
            _registry.emplace_back(::std::make_shared<Register::Us_HFSMRegister>());
            _registry.emplace_back(::std::make_shared<Register::UsRefObjectRegister>());
            _registry.emplace_back(::std::make_shared<Register::UsStateTBLRegister>());
            _registry.emplace_back(::std::make_shared<Register::Us_FSM_NodeRegister>());
            _registry.emplace_back(::std::make_shared<Register::SF_TimerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPathMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterAIRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLuaCommandRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLuaCommandExRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLuaSignalReActorRegister>());
            _registry.emplace_back(::std::make_shared<Register::_dh_mission_mgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_dh_player_mgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CIndexListRegister>());
            _registry.emplace_back(::std::make_shared<Register::CSyncCSRegister>());
            _registry.emplace_back(::std::make_shared<Register::_character_db_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::_bag_db_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::_equip_db_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::_embellish_db_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::_force_db_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::_animus_db_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::_trunk_db_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::_Exttrunk_db_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPostStorageRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPostReturnStorageRegister>());
            _registry.emplace_back(::std::make_shared<Register::AP_BatterySlotRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_amine_mineore_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_mineoreRegister>());
            _registry.emplace_back(::std::make_shared<Register::AutominePersonalRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_amine_inven_db_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::_quick_linkRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGuildRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_member_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_master_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_applier_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_suggested_matterRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_battle_suggest_matterRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_member_download_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_applier_download_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_query_info_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_money_io_download_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_member_buddy_download_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPlayerDBRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRealMoveRequestDelayCheckerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_WEAPON_PARAMRegister>());
            _registry.emplace_back(::std::make_shared<Register::_DTRADE_ITEMRegister>());
            _registry.emplace_back(::std::make_shared<Register::_DTRADE_PARAMRegister>());
            _registry.emplace_back(::std::make_shared<Register::_mastery_up_dataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_skill_lv_up_dataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_MASTERY_PARAMRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGuardTowerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_TOWER_PARAMRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTrapRegister>());
            _registry.emplace_back(::std::make_shared<Register::_TRAP_PARAMRegister>());
            _registry.emplace_back(::std::make_shared<Register::_BUDDY_LISTRegister>());
            _registry.emplace_back(::std::make_shared<Register::_happen_event_contRegister>());
            _registry.emplace_back(::std::make_shared<Register::CQuestMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::ItemCombineMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::MiningTicketRegister>());
            _registry.emplace_back(::std::make_shared<Register::CSetItemEffectRegister>());
            _registry.emplace_back(::std::make_shared<Register::CEquipItemSFAgentRegister>());
            _registry.emplace_back(::std::make_shared<Register::_CRYMSG_LISTRegister>());
            _registry.emplace_back(::std::make_shared<Register::_RENAME_POTION_USE_INFORegister>());
            _registry.emplace_back(::std::make_shared<Register::_NPCQuestIndexTempDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::CParkingUnitRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAITimerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_paramRegister>());
            _registry.emplace_back(::std::make_shared<Register::SKILLRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAnimusRegister>());
            _registry.emplace_back(::std::make_shared<Register::_ContPotionDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPotionParamRegister>());
            _registry.emplace_back(::std::make_shared<Register::CExtPotionBufRegister>());
            _registry.emplace_back(::std::make_shared<Register::_target_monster_contsf_allinform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_target_player_damage_contsf_allinform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_ATTACK_DELAY_CHECKERRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPvpPointLimiterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPvpOrderViewRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPvpCashPointRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCouponMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_NameChangeBuddyInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_other_shape_all_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_other_shape_part_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNotifyNotifyRaceLeaderSownerUTaxrateRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBattleTournamentInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::GuildCreateEventInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_server_rate_realtime_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::Player_TL_StatusRegister>());
            _registry.emplace_back(::std::make_shared<Register::TimeLimitMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_economy_history_dataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_guildmasterRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_sendwebracebosssmsRegister>());
            _registry.emplace_back(::std::make_shared<Register::qry_case_cash_limsaleRegister>());
            _registry.emplace_back(::std::make_shared<Register::_db_golden_box_itemRegister>());
            _registry.emplace_back(::std::make_shared<Register::PotionInnerDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPotionMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNetTimerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_golden_box_item_iniRegister>());
            _registry.emplace_back(::std::make_shared<Register::_golden_box_item_eventRegister>());
            _registry.emplace_back(::std::make_shared<Register::_golden_box_itemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_goldbox_indexRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGoldenBoxItemMgrRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CTraceRegister>());
            _registry.emplace_back(::std::make_shared<Register::CObjectRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRectRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCommandLineInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterHelperRegister>());
            _registry.emplace_back(::std::make_shared<Register::_NEAR_DATARegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_alter_member_grade_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CSizeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_announ_message_receipt_udpRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_disjointguildRegister>());
            _registry.emplace_back(::std::make_shared<Register::CHolyStoneRegister>());
            _registry.emplace_back(::std::make_shared<Register::_object_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_character_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_stone_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_animus_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_be_damaged_charRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAttackRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTakeOutRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMgrAvatorLvHistoryRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_list_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_player_vote_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_loadguildbattlerankRegister>());
            _registry.emplace_back(::std::make_shared<Register::_party_join_joiner_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::__holy_keeper_dataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_add_char_result_zoneRegister>());
            _registry.emplace_back(::std::make_shared<Register::BASE_HACKSHEILD_PARAMRegister>());
            _registry.emplace_back(::std::make_shared<Register::INationGameGuardSystemRegister>());
            _registry.emplace_back(::std::make_shared<Register::CHackShieldExSystemRegister>());
            _registry.emplace_back(::std::make_shared<Register::HACKSHEILD_PARAM_ANTICPRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_battle_reserved_schedule_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNetProcessRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNetSocketRegister>());
            _registry.emplace_back(::std::make_shared<Register::_socketRegister>());
            _registry.emplace_back(::std::make_shared<Register::_SOCK_TYPE_PARAMRegister>());
            _registry.emplace_back(::std::make_shared<Register::_total_countRegister>());
            _registry.emplace_back(::std::make_shared<Register::_FORCE_CLOSERegister>());
            _registry.emplace_back(::std::make_shared<Register::_NET_TYPE_PARAMRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNetWorkingRegister>());
            _registry.emplace_back(::std::make_shared<Register::_NET_BUFFERRegister>());
            _registry.emplace_back(::std::make_shared<Register::_thread_parameterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNetFrameRateRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_updatewinloseguildbattlerankRegister>());
            _registry.emplace_back(::std::make_shared<Register::_result_csi_error_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRFCashItemDatabaseRegister>());
            _registry.emplace_back(::std::make_shared<Register::_param_cashRegister>());
            _registry.emplace_back(::std::make_shared<Register::_param_cash_selectRegister>());
            _registry.emplace_back(::std::make_shared<Register::_param_cash_updateRegister>());
            _registry.emplace_back(::std::make_shared<Register::_param_cash_rollbackRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_count_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_cash_event_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_raceboss_accumulation_winrateRegister>());
            _registry.emplace_back(::std::make_shared<Register::_limit_item_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGuildRoomInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CHEAT_COMMANDRegister>());
            _registry.emplace_back(::std::make_shared<Register::_PVP_RANK_REFRESH_USERRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::exceptionRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::_String_baseRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::logic_errorRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::length_errorRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPvpUserRankingInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPvpUserRankingTargetUserListRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGuildRankingRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUserRankingProcessRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::out_of_rangeRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::_LockitRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::bad_allocRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_force_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_insert_orelogRegister>());
            _registry.emplace_back(::std::make_shared<Register::_itembox_take_add_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_amine_mineoreRegister>());
            _registry.emplace_back(::std::make_shared<Register::AggroCaculateDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_battle_rank_list_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_post_list_regiRegister>());
            _registry.emplace_back(::std::make_shared<Register::_a_trade_clear_item_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::D3DXVECTOR2Register>());
            _registry.emplace_back(::std::make_shared<Register::_pvp_cash_recover_itemlist_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_coll_pointRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::WorkerRegister>());
            _registry.emplace_back(::std::make_shared<Register::TaskRegister>());
            _registry.emplace_back(::std::make_shared<Register::TaskPoolRegister>());
            _registry.emplace_back(::std::make_shared<Register::CashDbWorkerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCheckSumBaseConverterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCheckSumCharacTrunkConverterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCheckSumCharacAccountTrunkDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_itembox_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_quest_history_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_trap_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::si_effectRegister>());
            _registry.emplace_back(::std::make_shared<Register::si_interpretRegister>());
            _registry.emplace_back(::std::make_shared<Register::CSetItemTypeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_con_event_Register>());
            _registry.emplace_back(::std::make_shared<Register::_cash_discount_event_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_quest_check_resultRegister>());
            _registry.emplace_back(::std::make_shared<Register::_quest_fail_resultRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_dest_guild_out_guildbattlecostRegister>());
            _registry.emplace_back(::std::make_shared<Register::_chat_steal_message_gm_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_good_storage_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_inform_commission_income_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CandidateMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_in_atrade_taxRegister>());
            _registry.emplace_back(::std::make_shared<Register::_tower_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_user_guild_dataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_in_guildbattlecostRegister>());
            _registry.emplace_back(::std::make_shared<Register::_MONEY_SUPPLY_DATARegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_updatedrawguildbattlerankRegister>());
            _registry.emplace_back(::std::make_shared<Register::_chat_lock_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_parkingunit_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_store_list_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGdiObjectRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_alive_charRegister>());
            _registry.emplace_back(::std::make_shared<Register::__TEMP_WAIT_TOWERRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_cheat_player_vote_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_bomb_current_state_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_amine_batterychargeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_server_reset_tokenRegister>());
            _registry.emplace_back(::std::make_shared<Register::__guild_list_pageRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRFMonsterAIMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_not_arranged_char_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_unit_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_alter_action_point_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pvp_order_view_end_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMgrGuildHistoryRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_selfdestruction_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLuaScriptRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLuaScriptMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::MyTimerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_exchange_lend_item_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_map_rateRegister>());
            _registry.emplace_back(::std::make_shared<Register::_force_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_buy_offerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_keeper_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_cash_event_iniRegister>());
            _registry.emplace_back(::std::make_shared<Register::_cash_eventRegister>());
            _registry.emplace_back(::std::make_shared<Register::_cum_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_buddy_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLuaEventNodeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unmannedtrader_buy_item_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_post_content_getRegister>());
            _registry.emplace_back(::std::make_shared<Register::_cash_discount_ini_Register>());
            _registry.emplace_back(::std::make_shared<Register::_cash_discount_Register>());
            _registry.emplace_back(::std::make_shared<Register::CashItemRemoteStoreRegister>());
            _registry.emplace_back(::std::make_shared<Register::_param_cashitem_dblogRegister>());
            _registry.emplace_back(::std::make_shared<Register::_result_csi_buy_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_selfleaveRegister>());
            _registry.emplace_back(::std::make_shared<Register::_class_valueRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_amine_newownerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_battle_goal_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_DELAY_PROCESSRegister>());
            _registry.emplace_back(::std::make_shared<Register::_ANIMUS_RETURN_DELAYRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_explosion_success_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_select_guild_master_lastconnRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_insert_patriarch_commRegister>());
            _registry.emplace_back(::std::make_shared<Register::_limitedsale_event_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPointRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_result_code_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_equip_up_item_lv_limit_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_lobby_logoutRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingNULLRegister>());
            _registry.emplace_back(::std::make_shared<Register::_log_sheet_usernumRegister>());
            _registry.emplace_back(::std::make_shared<Register::CItemStoreRegister>());
            _registry.emplace_back(::std::make_shared<Register::_npc_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_enter_world_result_zoneRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_manage_request_clzoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_sheet_logoutRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_in_guildbattlerewardmoneyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_reged_char_result_zoneRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unit_bullet_paramRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_amine_workstateRegister>());
            _registry.emplace_back(::std::make_shared<Register::_talik_recvr_listRegister>());
            _registry.emplace_back(::std::make_shared<Register::_max_pointRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPvpCashMngRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_addpvppointRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_amine_seloreRegister>());
            _registry.emplace_back(::std::make_shared<Register::CItemBoxRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGuildListRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guildroom_enter_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_buy_store_success_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_post_serial_checkRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGuildRoomSystemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_unmandtrader_cheat_updateregisttimeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_weekly_guild_rank_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unit_pack_fill_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_post_storage_list_getRegister>());
            _registry.emplace_back(::std::make_shared<Register::_npclink_check_item_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_throw_skill_result_one_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_server_notify_inform_zoneRegister>());
            _registry.emplace_back(::std::make_shared<Register::_keeper_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_sel_char_result_zoneRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guildroom_rent_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_discharge_patriarchRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLuaLooting_Novus_ItemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_npc_quest_list_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_load_guildbattle_totalrecordRegister>());
            _registry.emplace_back(::std::make_shared<Register::_chat_multi_far_trans_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCheckSumGuildConverterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCheckSumGuildDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_limit_amount_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pvp_rank_list_result_data_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_MOVE_LOBBY_DELAYRegister>());
            _registry.emplace_back(::std::make_shared<Register::CHolyStoneSaveDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_limit_item_num_info_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPartyModeKillMonsterExpNotifyRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLuaLootingMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_combine_ex_item_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::DfAIMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_be_damaged_playerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unmannedtrader_regist_item_success_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_result_csi_goods_list_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_update_candidate_wincount_packingRegister>());
            _registry.emplace_back(::std::make_shared<Register::_log_sheet_lvRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_trap_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guildroom_out_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNetworkEXRegister>());
            _registry.emplace_back(::std::make_shared<Register::_target_monster_aggro_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_throw_skill_result_other_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_gen_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_honor_guildRegister>());
            _registry.emplace_back(::std::make_shared<Register::_inven_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataNULLRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_notify_local_time_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_sheet_lobbyRegister>());
            _registry.emplace_back(::std::make_shared<Register::CFPSRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMgrAccountLobbyHistoryRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_vote_process_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_money_supply_gatering_inform_zowbRegister>());
            _registry.emplace_back(::std::make_shared<Register::_chat_message_receipt_udpRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unmannedtrader_close_item_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_bomb_destruction_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMgrAvatorItemHistoryRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderItemStateRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderRegistItemInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPvpUserAndGuildRankingSystemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_amine_moveoreRegister>());
            _registry.emplace_back(::std::make_shared<Register::_param_cash_total_sellingRegister>());
            _registry.emplace_back(::std::make_shared<Register::_possible_battle_guild_list_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::__holy_stone_dataRegister>());
            _registry.emplace_back(::std::make_shared<Register::CHolyScheduleDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_QUEST_CASHRegister>());
            _registry.emplace_back(::std::make_shared<Register::_QUEST_CASH_OTHERRegister>());
            _registry.emplace_back(::std::make_shared<Register::CHolyStoneSystemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_inform_tax_rate_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_position_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_result_change_tax_rate_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::TimeItemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_sheet_loadRegister>());
            _registry.emplace_back(::std::make_shared<Register::_add_lend_item_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_bomb_drop_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_combine_lend_item_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_inputgmoneyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_bomb_explosion_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_conditional_event_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_select_timelimit_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_alter_cont_effect_time_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_inform_punishment_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unit_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_throw_unit_result_other_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLuaEventMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_unmandtrader_updateitemstateRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_result_punishment_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_outputgmoneyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pvp_order_view_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_log_sheet_economyRegister>());
            _registry.emplace_back(::std::make_shared<Register::_atrade_taxrate_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_monster_create_setdataRegister>());
            _registry.emplace_back(::std::make_shared<Register::_notify_race_boss_winrate_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_trans_gm_msg_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unmannedtrader_continue_item_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_started_vote_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CHolyKeeperRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unmannedtrader_Regist_item_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPcBangFavorRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_insert_candidateRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unmannedtrader_re_regist_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_rank_racerank_guildrankRegister>());
            _registry.emplace_back(::std::make_shared<Register::_log_case_charselectRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMerchantRegister>());
            _registry.emplace_back(::std::make_shared<Register::_sf_delay_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_joinacguildRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_data_for_post_sendRegister>());
            _registry.emplace_back(::std::make_shared<Register::_SKILL_IDX_PER_MASTERYRegister>());
            _registry.emplace_back(::std::make_shared<Register::_BILLING_FORCE_CLOSE_DELAYRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPlayerAttackRegister>());
            _registry.emplace_back(::std::make_shared<Register::LendItemSheetRegister>());
            _registry.emplace_back(::std::make_shared<Register::LendItemMngRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_battle_get_gravity_stone_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_insert_timelimit_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::_base_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::ElectProcessorRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterAttackRegister>());
            _registry.emplace_back(::std::make_shared<Register::_del_char_result_zoneRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_forceleaveRegister>());
            _registry.emplace_back(::std::make_shared<Register::_trunk_est_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_move_to_own_stonemap_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_post_sendRegister>());
            _registry.emplace_back(::std::make_shared<Register::_check_queryRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_updatereservedscheduleRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_battle_suggest_request_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_talik_crystal_exchange_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::qry_case_golden_box_itemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_throw_unit_result_one_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_character_renameRegister>());
            _registry.emplace_back(::std::make_shared<Register::_itembox_take_new_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_ccrfg_detect_alretRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_select_charserialRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMgrAvatorQuestHistoryRegister>());
            _registry.emplace_back(::std::make_shared<Register::PatriarchElectProcessorRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pvp_order_view_point_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_src_guild_out_guildbattlecostRegister>());
            _registry.emplace_back(::std::make_shared<Register::_guild_alter_member_state_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_download_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_moveout_user_result_zoneRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_addguildbattlescheduleRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_vote_availableRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_post_return_list_getRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoneySupplyMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_contsaveRegister>());
            _registry.emplace_back(::std::make_shared<Register::MonsterSetInfoDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::R3CameraRegister>());
            _registry.emplace_back(::std::make_shared<Register::_a_trade_adjust_price_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_trunk_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_create_holy_master_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_gm_msg_gmcall_list_response_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_starting_vote_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_sheet_regedRegister>());
            _registry.emplace_back(::std::make_shared<Register::_radar_char_list_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_Init_action_point_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_sheet_insertRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_insertitemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_find_rader_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_total_guild_rank_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_check_speed_hack_ansRegister>());
            _registry.emplace_back(::std::make_shared<Register::_item_fanfare_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_sheet_deleteRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_select_patriarch_commRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNuclearBombRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_buyemblemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_animus_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_move_to_own_stonemap_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_unmannedtrader_Sell_Wait_item_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_nuclear_bomb_position_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_amine_battery_dischargeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_storage_refresh_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_quest_download_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_updateweeklyguildpvppointsumRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_insertguildRegister>());
            _registry.emplace_back(::std::make_shared<Register::_log_change_class_after_init_classRegister>());
            _registry.emplace_back(::std::make_shared<Register::UIDGeneratorRegister>());
            _registry.emplace_back(::std::make_shared<Register::_event_participant_classrefineRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_skill_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_unmandtrader_re_registsingleitemRegister>());
            _registry.emplace_back(::std::make_shared<Register::_party_member_info_updRegister>());
            _registry.emplace_back(::std::make_shared<Register::RFEvent_ClassRefineRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_update_data_for_tradeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_attack_siege_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_event_set_lootingRegister>());
            _registry.emplace_back(::std::make_shared<Register::_ECONOMY_SYSTEMRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_updateclearguildbattleDayInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRFDBItemLogRegister>());
            _registry.emplace_back(::std::make_shared<Register::_LTDRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDBWorkManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::type_infoRegister>());
            _registry.emplace_back(::std::make_shared<std::Register::ios_baseRegister>());
            _registry.emplace_back(::std::make_shared<Register::MD5Register>());
            _registry.emplace_back(::std::make_shared<Register::WheatyExceptionReportRegister>());
            _registry.emplace_back(::std::make_shared<Register::AINetFileRegister>());
            _registry.emplace_back(::std::make_shared<Register::AINetRegister>());
            _registry.emplace_back(::std::make_shared<Register::AINetFtpRegister>());
            _registry.emplace_back(::std::make_shared<Register::_SYN_HEADERRegister>());
            _registry.emplace_back(::std::make_shared<Register::_SYN_DATARegister>());
            _registry.emplace_back(::std::make_shared<Register::_THREAD_CONFIGRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTermRegister>());
            _registry.emplace_back(::std::make_shared<Register::_talk_crystal_matrial_combine_nodeRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTalkCrystalCombineManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAsyncLogInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAsyncLogBufferRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAsyncLogBufferListRegister>());
            _registry.emplace_back(::std::make_shared<Register::CAsyncLoggerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_action_point_system_iniRegister>());
            _registry.emplace_back(::std::make_shared<Register::CActionPointSystemMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNetworkRegister>());
            _registry.emplace_back(::std::make_shared<Register::CChiNetworkEXRegister>());
            _registry.emplace_back(::std::make_shared<Register::_apex_send_loginRegister>());
            _registry.emplace_back(::std::make_shared<Register::_apex_send_ipRegister>());
            _registry.emplace_back(::std::make_shared<Register::_apex_send_transRegister>());
            _registry.emplace_back(::std::make_shared<Register::_apex_send_logoutRegister>());
            _registry.emplace_back(::std::make_shared<Register::_apex_block_request_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_trans_account_report_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_start_world_request_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_logout_account_request_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_cashdb_setting_request_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_holy_quest_report_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_select_avator_report_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_open_world_request_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_character_disconnect_result_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_apex_idRegister>());
            _registry.emplace_back(::std::make_shared<Register::_stop_world_request_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_enter_lobby_report_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_world_account_ping_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_user_num_report_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_fireguard_block_request_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::_enter_world_request_wracRegister>());
            _registry.emplace_back(::std::make_shared<Register::LuaParam3Register>());
            _registry.emplace_back(::std::make_shared<Register::_notify_coupon_ensure_time_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_notify_cont_play_time_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_notify_coupon_error_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_notify_remain_coupon_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_notice_move_limit_map_msg_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationCodeStrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_notify_max_pvp_point_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_notify_not_use_premium_cashitem_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_notify_pvp_cash_point_error_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationCodeStrTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::TimeLimitJadeRegister>());
            _registry.emplace_back(::std::make_shared<Register::TimeLimitJadeMngRegister>());
            _registry.emplace_back(::std::make_shared<Register::COreAmountMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_ChatStealTargetInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CChatStealSystemRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGuildMasterEffectRegister>());
            _registry.emplace_back(::std::make_shared<Register::CWorldScheduleRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTransportShipRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleStateRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CTimeSpanRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleLoggerRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleGuildMemberRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleGuildRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCircleZoneRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGravityStoneRegenerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGravityStoneRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleFieldRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleStateListRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateNotifyRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateReadyRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateCountDownRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateRoundRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateRoundStartRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateRoundProcessRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateRoundReturnStartPosRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateRoundListRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateInBattleRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateDivideRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateReturnRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateFinRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateListRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleStateListPoolRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleLoggerRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleRewardItemRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleRewardItemManagerRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleScheduleRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleSchedulePoolRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleFieldListRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleReservedScheduleRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleReservedScheduleMapGroupRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleScheduleManagerRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CReservedGuildSchedulePageRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CReservedGuildScheduleMapGroupRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CReservedGuildScheduleDayGroupRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CCurrentGuildBattleInfoManagerRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CPossibleBattleGuildListManagerRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleSchedulerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLogTypeDBTaskRegister>());
            _registry.emplace_back(::std::make_shared<Register::CWeeklyGuildRankRecordRegister>());
            _registry.emplace_back(::std::make_shared<Register::CWeeklyGuildRankOwnerInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CWeeklyGuildRankInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CWeeklyGuildRankManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLogTypeDBTaskPoolRegister>());
            _registry.emplace_back(::std::make_shared<Register::CLogTypeDBTaskManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGuildBattleControllerRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CNormalGuildBattleManagerRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleRankManagerRegister>());
            _registry.emplace_back(::std::make_shared<GUILD_BATTLE::Register::CGuildBattleReservedScheduleListManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRaceBuffInfoByHolyQuestRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRaceBuffInfoByHolyQuestfGroupRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRaceBuffInfoByHolyQuestListRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRaceBuffHolyQuestResultInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRaceBuffByHolyQuestProcedureRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRaceBuffManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoveMapLimitRightRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoveMapLimitRightPortalRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoveMapLimitRightInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoveMapLimitRightInfoListRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoveMapLimitInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoveMapLimitInfoListRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoveMapLimitInfoPortalRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoveMapLimitManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMoveMapLimitEnviromentValuesRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderGroupDivisionVersionInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderGroupVersionInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderScheduleRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderRequestLimiterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderUserInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderTradeInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderUserInfoTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderSchedulerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderLazyCleanerRegister>());
            _registry.emplace_back(::std::make_shared<Register::ControllerTaxRateRegister>());
            _registry.emplace_back(::std::make_shared<Register::_suggested_matter_change_taxrateRegister>());
            _registry.emplace_back(::std::make_shared<Register::TRC_AutoTradeRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderTaxRateManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_insert_trc_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderItemCodeInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderClassInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderSortTypeRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderDivisionInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderGroupIDInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderSubClassInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderSubClassFactoryRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderSubClassInfoLevelRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderSubClassInfoDefaultRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderSubClassInfoForceLiverGradeRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderSubClassInfoCodeRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderClassInfoFactoryRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderClassInfoTableTypeRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderClassInfoTableCodeTypeRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderGroupItemInfoTableRegister>());
            _registry.emplace_back(::std::make_shared<Register::CHonorGuildRegister>());
            _registry.emplace_back(::std::make_shared<Register::CUnmannedTraderControllerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMapItemStoreListRegister>());
            _registry.emplace_back(::std::make_shared<Register::CItemStoreManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPostSystemManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerTHRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerNULLRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerESRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerUSRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerCNRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerTWRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerBRRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRusiaBillingMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerRURegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerPHRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerJPRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerIDRegister>());
            _registry.emplace_back(::std::make_shared<Register::BNetworkRegister>());
            _registry.emplace_back(::std::make_shared<Register::CEngNetworkBillEXRegister>());
            _registry.emplace_back(::std::make_shared<Register::RECV_DATARegister>());
            _registry.emplace_back(::std::make_shared<Register::Request_Remain_CashRegister>());
            _registry.emplace_back(::std::make_shared<Register::Request_Buy_ItemRegister>());
            _registry.emplace_back(::std::make_shared<Register::CEnglandBillingMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerGBRegister>());
            _registry.emplace_back(::std::make_shared<Register::CCashDbWorkerKRRegister>());
            _registry.emplace_back(::std::make_shared<Register::ICsSendInterfaceRegister>());
            _registry.emplace_back(::std::make_shared<Register::CSUItemSystemRegister>());
            _registry.emplace_back(::std::make_shared<Register::AutominePersonalMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_selore_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_battery_insert_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_battery_extract_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_stop_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_amine_infoui_open_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_popore_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_qry_case_make_storageRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_amine_make_storage_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_amine_errmsg_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_alter_dur_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_automine_getoutore_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_uninstall_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_automine_charge_money_db_update_fail_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_uninstall_circle_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_ore_cutting_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_automine_state_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_amine_fixpos_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_install_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_current_state_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_delbattery_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_personal_automine_attacked_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_automine_info_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_automine_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::AutoMineMachineRegister>());
            _registry.emplace_back(::std::make_shared<Register::AutoMineMachineMngRegister>());
            _registry.emplace_back(::std::make_shared<Register::CItemDropMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTotalGuildRankRecordRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTotalGuildRankInfoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTotalGuildRankManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_trans_votepaper_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_notify_vote_score_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::VoterRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_inform_appoint_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_result_appoint_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_propose_appoint_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_notify_final_decisionRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_appoint_inform_request_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_result_fcandidacy_list_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_result_code_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_pt_query_appoint_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::SecondCandidateCrystallizerRegister>());
            _registry.emplace_back(::std::make_shared<Register::FinalDecisionProcessorRegister>());
            _registry.emplace_back(::std::make_shared<Register::FinalDecisionApplyerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRaceBossWinRateRegister>());
            _registry.emplace_back(::std::make_shared<Register::ClassOrderProcessorRegister>());
            _registry.emplace_back(::std::make_shared<Register::CandidateRegisterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CVoteSystemRegister>());
            _registry.emplace_back(::std::make_shared<Register::GMCallMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::DnBuffNodeRegister>());
            _registry.emplace_back(::std::make_shared<Register::GMRequestDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterEventSetRegister>());
            _registry.emplace_back(::std::make_shared<Register::CMonsterEventRespawnRegister>());
            _registry.emplace_back(::std::make_shared<RACE_BOSS_MSG::Register::CMsgRegister>());
            _registry.emplace_back(::std::make_shared<RACE_BOSS_MSG::Register::CMsgListRegister>());
            _registry.emplace_back(::std::make_shared<RACE_BOSS_MSG::Register::CMsgListManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRaceBossMsgControllerRegister>());
            _registry.emplace_back(::std::make_shared<Register::_cancel_raceboss_msg_result_zoctRegister>());
            _registry.emplace_back(::std::make_shared<Register::_connection_status_result_zoctRegister>());
            _registry.emplace_back(::std::make_shared<Register::cStaticMember_PlayerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingIDRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingCNRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingBRRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingRURegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingPHRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingJPRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingKRRegister>());
            _registry.emplace_back(::std::make_shared<Register::CBillingManagerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CHolyStoneSystemDataMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::C24TimerRegister>());
            _registry.emplace_back(::std::make_shared<Register::strFILERegister>());
            _registry.emplace_back(::std::make_shared<Register::CDarkHoleDungeonQuestSetupRegister>());
            _registry.emplace_back(::std::make_shared<Register::__error_infoRegister>());
            _registry.emplace_back(::std::make_shared<Register::CDarkHoleDungeonQuestRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_new_member_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_outof_member_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_leader_change_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_quest_info_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_mission_info_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_member_info_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_job_count_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_open_portal_by_react_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_open_all_portal_by_result_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_job_pass_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_mission_pass_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_mission_quest_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_channel_close_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_new_mission_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_timeout_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_ask_reenter_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_real_msg_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_real_add_time_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_fixpositon_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_clear_out_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_open_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_giveup_out_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_enter_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_state_change_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_create_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_destroy_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_darkhole_answer_reenter_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CGameStatisticsRegister>());
            _registry.emplace_back(::std::make_shared<Register::CReturnGateRegister>());
            _registry.emplace_back(::std::make_shared<Register::CReturnGateControllerRegister>());
            _registry.emplace_back(::std::make_shared<Register::CReturnGateCreateParamRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRecallRequestRegister>());
            _registry.emplace_back(::std::make_shared<Register::CRecallEffectControllerRegister>());
            _registry.emplace_back(::std::make_shared<Register::LtdWriterRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataTHRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryTHRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryGroupRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryESRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataESRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataUSRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryUSRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryCNRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataCNRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataTWRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryTWRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryBRRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataBRRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataRURegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryRURegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataPHRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryPHRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataJPRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryJPRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataIDRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryIDRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataGBRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryGBRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingDataKRRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNationSettingFactoryKRRegister>());
            _registry.emplace_back(::std::make_shared<Register::CNuclearBombMgrRegister>());
            _registry.emplace_back(::std::make_shared<Register::_post_content_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_post_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::AreaDataRegister>());
            _registry.emplace_back(::std::make_shared<Register::AreaListRegister>());
            _registry.emplace_back(::std::make_shared<Register::_alive_char_result_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_insert_new_quest_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::_insert_next_quest_inform_zoclRegister>());
            _registry.emplace_back(::std::make_shared<Register::CClientDCRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPaintDCRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CTraceFileAndLineInfoRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CCRTAllocatorRegister>());
            _registry.emplace_back(::std::make_shared<Define_the_symbol__ATL_MIXED::Register::Thank_youRegister>());
            _registry.emplace_back(::std::make_shared<Register::ULIRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CComBSTRRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CAtlExceptionRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CFileTimeSpanRegister>());
            _registry.emplace_back(::std::make_shared<ATL::Register::CFileTimeRegister>());
            _registry.emplace_back(::std::make_shared<Register::_R3ENGINE_STATERegister>());
            _registry.emplace_back(::std::make_shared<Register::SunRegister>());
            _registry.emplace_back(::std::make_shared<Register::CTextureRenderRegister>());
            _registry.emplace_back(::std::make_shared<Register::AtmosphereRegister>());
            _registry.emplace_back(::std::make_shared<Register::CPlayMP3Register>());
            _registry.emplace_back(::std::make_shared<Register::CR3FontRegister>());
            _registry.emplace_back(::std::make_shared<Register::SkyRegister>());
            _registry.emplace_back(::std::make_shared<Register::CD3DApplicationRegister>());
            _registry.emplace_back(::std::make_shared<Register::FONT2DVERTEXRegister>());
            _registry.emplace_back(::std::make_shared<Register::CD3DArcBallRegister>());
            _registry.emplace_back(::std::make_shared<Register::CToolCollisionFaceRegister>());
            _registry.emplace_back(::std::make_shared<Register::CD3DCameraRegister>());
        };
    
        CATFCoreRegistry(const CATFCoreRegistry&){};
    
    public:
        ~CATFCoreRegistry() {
        };
    
        static CATFCoreRegistry& get_instance() {
            static CATFCoreRegistry instance;
            return instance;
        };
    
    public:
        void registry()
        {
            for (auto& r : _registry)
                r->Register();
        };
    
    private:
        ::std::vector<Register_ptr> _registry;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CChiNetworkEX.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CChiNetworkEXAcceptClientCheck2_ptr = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int, unsigned int, unsigned int);
        using CChiNetworkEXAcceptClientCheck2_clbk = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int, unsigned int, unsigned int, CChiNetworkEXAcceptClientCheck2_ptr);
        
        using CChiNetworkEXctor_CChiNetworkEX4_ptr = void (WINAPIV*)(struct CChiNetworkEX*);
        using CChiNetworkEXctor_CChiNetworkEX4_clbk = void (WINAPIV*)(struct CChiNetworkEX*, CChiNetworkEXctor_CChiNetworkEX4_ptr);
        using CChiNetworkEXCheckApexLine6_ptr = void (WINAPIV*)(struct CChiNetworkEX*);
        using CChiNetworkEXCheckApexLine6_clbk = void (WINAPIV*)(struct CChiNetworkEX*, CChiNetworkEXCheckApexLine6_ptr);
        using CChiNetworkEXCloseClientCheck8_ptr = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int, unsigned int, unsigned int);
        using CChiNetworkEXCloseClientCheck8_clbk = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int, unsigned int, unsigned int, CChiNetworkEXCloseClientCheck8_ptr);
        using CChiNetworkEXDestory10_ptr = void (WINAPIV*)();
        using CChiNetworkEXDestory10_clbk = void (WINAPIV*)(CChiNetworkEXDestory10_ptr);
        using CChiNetworkEXInform_For_Exit_By_ApexBlock12_ptr = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int);
        using CChiNetworkEXInform_For_Exit_By_ApexBlock12_clbk = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int, CChiNetworkEXInform_For_Exit_By_ApexBlock12_ptr);
        using CChiNetworkEXInitialize14_ptr = int (WINAPIV*)(struct CChiNetworkEX*);
        using CChiNetworkEXInitialize14_clbk = int (WINAPIV*)(struct CChiNetworkEX*, CChiNetworkEXInitialize14_ptr);
        using CChiNetworkEXInstance16_ptr = struct CChiNetworkEX* (WINAPIV*)();
        using CChiNetworkEXInstance16_clbk = struct CChiNetworkEX* (WINAPIV*)(CChiNetworkEXInstance16_ptr);
        using CChiNetworkEXLoadINIFile18_ptr = int (WINAPIV*)(struct CChiNetworkEX*);
        using CChiNetworkEXLoadINIFile18_clbk = int (WINAPIV*)(struct CChiNetworkEX*, CChiNetworkEXLoadINIFile18_ptr);
        using CChiNetworkEXRecv_ApexInform20_ptr = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int, unsigned int, char*);
        using CChiNetworkEXRecv_ApexInform20_clbk = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int, unsigned int, char*, CChiNetworkEXRecv_ApexInform20_ptr);
        using CChiNetworkEXRecv_ApexKill22_ptr = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int, unsigned int, char*);
        using CChiNetworkEXRecv_ApexKill22_clbk = void (WINAPIV*)(struct CChiNetworkEX*, unsigned int, unsigned int, char*, CChiNetworkEXRecv_ApexKill22_ptr);
        using CChiNetworkEXSend24_ptr = int (WINAPIV*)(struct CChiNetworkEX*, char*, unsigned int, char*, uint16_t);
        using CChiNetworkEXSend24_clbk = int (WINAPIV*)(struct CChiNetworkEX*, char*, unsigned int, char*, uint16_t, CChiNetworkEXSend24_ptr);
        using CChiNetworkEXSend_ClienInform26_ptr = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*, uint16_t, char*);
        using CChiNetworkEXSend_ClienInform26_clbk = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*, uint16_t, char*, CChiNetworkEXSend_ClienInform26_ptr);
        using CChiNetworkEXSend_IP28_ptr = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*);
        using CChiNetworkEXSend_IP28_clbk = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*, CChiNetworkEXSend_IP28_ptr);
        using CChiNetworkEXSend_Login30_ptr = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*);
        using CChiNetworkEXSend_Login30_clbk = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*, CChiNetworkEXSend_Login30_ptr);
        using CChiNetworkEXSend_Logout32_ptr = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*);
        using CChiNetworkEXSend_Logout32_clbk = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*, CChiNetworkEXSend_Logout32_ptr);
        using CChiNetworkEXSend_Trans34_ptr = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*, unsigned int);
        using CChiNetworkEXSend_Trans34_clbk = void (WINAPIV*)(struct CChiNetworkEX*, struct CPlayer*, unsigned int, CChiNetworkEXSend_Trans34_ptr);
        using CChiNetworkEXs_DataAnalysis39_ptr = bool (WINAPIV*)(unsigned int, unsigned int, struct _MSG_HEADER*, char*);
        using CChiNetworkEXs_DataAnalysis39_clbk = bool (WINAPIV*)(unsigned int, unsigned int, struct _MSG_HEADER*, char*, CChiNetworkEXs_DataAnalysis39_ptr);
        
        using CChiNetworkEXdtor_CChiNetworkEX41_ptr = void (WINAPIV*)(struct CChiNetworkEX*);
        using CChiNetworkEXdtor_CChiNetworkEX41_clbk = void (WINAPIV*)(struct CChiNetworkEX*, CChiNetworkEXdtor_CChiNetworkEX41_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

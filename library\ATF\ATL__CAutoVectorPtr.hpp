// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct CAutoVectorPtr<wchar_t>
        {
            wchar_t *m_p;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE


START_ATF_NAMESPACE
    namespace ATL
    {
        template<>
        struct CAutoVectorPtr<char>
        {
            char *m_p;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

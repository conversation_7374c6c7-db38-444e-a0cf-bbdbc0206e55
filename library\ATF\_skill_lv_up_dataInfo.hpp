// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_skill_lv_up_data.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _skill_lv_up_datainit2_ptr = void (WINAPIV*)(struct _skill_lv_up_data*);
        using _skill_lv_up_datainit2_clbk = void (WINAPIV*)(struct _skill_lv_up_data*, _skill_lv_up_datainit2_ptr);
        using _skill_lv_up_dataset4_ptr = void (WINAPIV*)(struct _skill_lv_up_data*, uint16_t, char);
        using _skill_lv_up_dataset4_clbk = void (WINAPIV*)(struct _skill_lv_up_data*, uint16_t, char, _skill_lv_up_dataset4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

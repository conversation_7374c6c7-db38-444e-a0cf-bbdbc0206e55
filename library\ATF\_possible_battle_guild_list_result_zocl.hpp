// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _possible_battle_guild_list_result_zocl
    {
        struct  __list
        {
            char wszName[17];
            char byGrade;
            char byRace;
            unsigned int dwGuildSerial;
        };
        char byPage;
        char byMaxPage;
         unsigned int dwCurVer;
         unsigned int dwBattleCost;
        char byMapCnt;
        char byMapInx[100];
        char byCount;
        __list DestGuild[4];
    public:
        _possible_battle_guild_list_result_zocl();
        void ctor__possible_battle_guild_list_result_zocl();
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _qry_case_select_patriarch_comm
    {
        unsigned int dwSerial;
        char byDBRet;
    public:
        _qry_case_select_patriarch_comm();
        void ctor__qry_case_select_patriarch_comm();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_qry_case_select_patriarch_comm, 8>(), "_qry_case_select_patriarch_comm");
END_ATF_NAMESPACE

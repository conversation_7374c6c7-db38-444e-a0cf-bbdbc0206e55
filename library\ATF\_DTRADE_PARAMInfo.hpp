// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_DTRADE_PARAM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _DTRADE_PARAMInit2_ptr = void (WINAPIV*)(struct _DTRADE_PARAM*);
        using _DTRADE_PARAMInit2_clbk = void (WINAPIV*)(struct _DTRADE_PARAM*, _DTRADE_PARAMInit2_ptr);
        using _DTRADE_PARAMSetDTradeStart4_ptr = void (WINAPIV*)(struct _DTRADE_PARAM*, uint16_t, unsigned int, int, unsigned int*);
        using _DTRADE_PARAMSetDTradeStart4_clbk = void (WINAPIV*)(struct _DTRADE_PARAM*, uint16_t, unsigned int, int, unsigned int*, _DTRADE_PARAMSetDTradeStart4_ptr);
        
        using _DTRADE_PARAMctor__DTRADE_PARAM6_ptr = void (WINAPIV*)(struct _DTRADE_PARAM*);
        using _DTRADE_PARAMctor__DTRADE_PARAM6_clbk = void (WINAPIV*)(struct _DTRADE_PARAM*, _DTRADE_PARAMctor__DTRADE_PARAM6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

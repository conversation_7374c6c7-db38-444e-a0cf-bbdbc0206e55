// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _D3DCAPS8
    {
        _D3DDEVTYPE DeviceType;
        unsigned int AdapterOrdinal;
        unsigned int Caps;
        unsigned int Caps2;
        unsigned int Caps3;
        unsigned int PresentationIntervals;
        unsigned int CursorCaps;
        unsigned int DevCaps;
        unsigned int PrimitiveMiscCaps;
        unsigned int RasterCaps;
        unsigned int ZCmpCaps;
        unsigned int SrcBlendCaps;
        unsigned int DestBlendCaps;
        unsigned int AlphaCmpCaps;
        unsigned int ShadeCaps;
        unsigned int TextureCaps;
        unsigned int TextureFilterCaps;
        unsigned int CubeTextureFilterCaps;
        unsigned int VolumeTextureFilterCaps;
        unsigned int TextureAddressCaps;
        unsigned int VolumeTextureAddressCaps;
        unsigned int LineCaps;
        unsigned int MaxTextureWidth;
        unsigned int MaxTextureHeight;
        unsigned int MaxVolumeExtent;
        unsigned int MaxTextureRepeat;
        unsigned int MaxTextureAspectRatio;
        unsigned int MaxAnisotropy;
        float MaxVertexW;
        float GuardBandLeft;
        float GuardBandTop;
        float GuardBandRight;
        float GuardBandBottom;
        float ExtentsAdjust;
        unsigned int StencilCaps;
        unsigned int FVFCaps;
        unsigned int TextureOpCaps;
        unsigned int MaxTextureBlendStages;
        unsigned int MaxSimultaneousTextures;
        unsigned int VertexProcessingCaps;
        unsigned int MaxActiveLights;
        unsigned int MaxUserClipPlanes;
        unsigned int MaxVertexBlendMatrices;
        unsigned int MaxVertexBlendMatrixIndex;
        float MaxPointSize;
        unsigned int MaxPrimitiveCount;
        unsigned int MaxVertexIndex;
        unsigned int MaxStreams;
        unsigned int MaxStreamStride;
        unsigned int VertexShaderVersion;
        unsigned int MaxVertexShaderConst;
        unsigned int PixelShaderVersion;
        float MaxPixelShaderValue;
    };
END_ATF_NAMESPACE

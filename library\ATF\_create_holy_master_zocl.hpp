// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _create_holy_master_zocl
    {
        char byHolyStoneRaceCode;
        char byPlayerRaceCode;
        char by<PERSON>ame<PERSON>en;
        char wszName[17];
        int nControlSec;
        unsigned int dwObjSerial;
    public:
        _create_holy_master_zocl();
        void ctor__create_holy_master_zocl();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_create_holy_master_zocl, 28>(), "_create_holy_master_zocl");
END_ATF_NAMESPACE

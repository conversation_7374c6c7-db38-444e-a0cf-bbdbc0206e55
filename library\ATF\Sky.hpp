// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct Sky
    {
    public:
        int32_t CreateVertexBuffer();
        int32_t FillupVertexBuffer();
        void InvalidateSky();
        int32_t Render();
        void RestoreSky();
        Sky();
        int64_t ctor_Sky();
        ~Sky();
        int64_t dtor_Sky();
    }
    ;
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum USE_POTION_ERR
    {
      error_potion_wrong_storage_pos = 0x1,
      error_potion_is_not_exist_item = 0x2,
      error_potion_is_not_potion = 0x3,
      error_potion_exceed_quantity = 0x4,
      error_potion_state_is_locked_recover = 0x5,
      error_potion_player_state_dead = 0x6,
      error_potion_in_unit = 0x7,
      error_potion_full_gage = 0x8,
      error_potion_delay_time = 0x9,
      error_potion_is_locked = 0xB,
      error_potion_state_stone = 0xC,
      error_potion_wait_self_kill = 0xD,
      error_potion_have_grivity_stone = 0xE,
      error_potion_world_is_not_adult = 0xF,
      error_potion_in_guildbattle = 0x10,
      error_potion_chaos_mode_time_many_remain = 0x11,
      error_potion_class_refine_failed = 0x12,
      error_potion_invalid_target = 0x13,
      error_potion_distance = 0x14,
      error_potion_2006_xmas_event = 0x15,
      error_potion_cant_use_boradori = 0x16,
      error_potion_aftereffect_use_fail = 0x17,
      error_potion_use_fail = 0x19,
      error_potion_character_rename = 0x1A,
      error_potion_use_war = 0x1B,
      error_potion_use_peace = 0x1C,
      error_potion_use_die = 0x1D,
      error_potion_buf_already_extended = 0x1E,
      error_potion_effect_is_same_type = 0x1F,
      error_potion_priority_lower = 0x20,
      error_potion_trunk_not_est = 0x21,
      error_potion_trunk_full_extended = 0x22,
      error_potion_no_apply_debuff = 0x23,
      error_potion_current_debuff_not_use = 0x24,
      error_potion_map_limit = 0x25,
      error_potion_not_event_period = 0x27,
      error_holystone_destroyer = 0x2D,
      error_potion_already_full_hfs = 0x2E,
      error_potion_not_cont_damage_effect = 0x2F,
      error_potion_unknown = 0x30,
    };
END_ATF_NAMESPACE

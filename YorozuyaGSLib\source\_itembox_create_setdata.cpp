#include <_itembox_create_setdata.hpp>


START_ATF_NAMESPACE
    _itembox_create_setdata::_itembox_create_setdata()
    {
        using org_ptr = void (WINAPIV*)(struct _itembox_create_setdata*);
        (org_ptr(0x140167830L))(this);
    };
    void _itembox_create_setdata::ctor__itembox_create_setdata()
    {
        using org_ptr = void (WINAPIV*)(struct _itembox_create_setdata*);
        (org_ptr(0x140167830L))(this);
    };
END_ATF_NAMESPACE

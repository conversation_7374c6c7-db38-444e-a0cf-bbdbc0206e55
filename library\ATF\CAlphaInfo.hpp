// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAlpha.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CAlphaCheckAlphaAlloc1_ptr = void (WINAPIV*)(struct CAlpha*);
        using CAlphaCheckAlphaAlloc1_clbk = void (WINAPIV*)(struct CAlpha*, CAlphaCheckAlphaAlloc1_ptr);
        using CAlphaGetAlphaFace2_ptr = uint32_t* (WINAPIV*)(struct CAlpha*);
        using CAlphaGetAlphaFace2_clbk = uint32_t* (WINAPIV*)(struct CAlpha*, CAlphaGetAlphaFace2_ptr);
        using CAlphaGetAlphaFaceCnt3_ptr = uint32_t (WINAPIV*)(struct CAlpha*);
        using CAlphaGetAlphaFaceCnt3_clbk = uint32_t (WINAPIV*)(struct CAlpha*, CAlphaGetAlphaFaceCnt3_ptr);
        using CAlphaInitAlpha4_ptr = void (WINAPIV*)(struct CAlpha*, void*);
        using CAlphaInitAlpha4_clbk = void (WINAPIV*)(struct CAlpha*, void*, CAlphaInitAlpha4_ptr);
        using CAlphaLoopInitAlphaStack5_ptr = void (WINAPIV*)(struct CAlpha*);
        using CAlphaLoopInitAlphaStack5_clbk = void (WINAPIV*)(struct CAlpha*, CAlphaLoopInitAlphaStack5_ptr);
        using CAlphaSetAlphaEntityStack6_ptr = void (WINAPIV*)(struct CAlpha*, uint16_t);
        using CAlphaSetAlphaEntityStack6_clbk = void (WINAPIV*)(struct CAlpha*, uint16_t, CAlphaSetAlphaEntityStack6_ptr);
        using CAlphaSetAlphaStack7_ptr = void (WINAPIV*)(struct CAlpha*, uint16_t);
        using CAlphaSetAlphaStack7_clbk = void (WINAPIV*)(struct CAlpha*, uint16_t, CAlphaSetAlphaStack7_ptr);
        using CAlphaSetCoronaStack8_ptr = void (WINAPIV*)(struct CAlpha*, uint16_t);
        using CAlphaSetCoronaStack8_clbk = void (WINAPIV*)(struct CAlpha*, uint16_t, CAlphaSetCoronaStack8_ptr);
        using CAlphaSortAlphaStack9_ptr = uint32_t* (WINAPIV*)(struct CAlpha*, float*);
        using CAlphaSortAlphaStack9_clbk = uint32_t* (WINAPIV*)(struct CAlpha*, float*, CAlphaSortAlphaStack9_ptr);
        
        using CAlphadtor_CAlpha10_ptr = int64_t (WINAPIV*)(struct CAlpha*);
        using CAlphadtor_CAlpha10_clbk = int64_t (WINAPIV*)(struct CAlpha*, CAlphadtor_CAlpha10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <PatriarchElectProcessor.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using PatriarchElectProcessorCheatClearPatriarch2_ptr = bool (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorCheatClearPatriarch2_clbk = bool (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorCheatClearPatriarch2_ptr);
        using PatriarchElectProcessorCheatSetPatriarch4_ptr = bool (WINAPIV*)(struct PatriarchElectProcessor*, struct CPlayer*, _candidate_info::ClassType);
        using PatriarchElectProcessorCheatSetPatriarch4_clbk = bool (WINAPIV*)(struct PatriarchElectProcessor*, struct CPlayer*, _candidate_info::ClassType, PatriarchElectProcessorCheatSetPatriarch4_ptr);
        using PatriarchElectProcessorCompleteCheckInvalidChar6_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*, char);
        using PatriarchElectProcessorCompleteCheckInvalidChar6_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, char, PatriarchElectProcessorCompleteCheckInvalidChar6_ptr);
        using PatriarchElectProcessorCompleteInsertElect8_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorCompleteInsertElect8_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorCompleteInsertElect8_ptr);
        using PatriarchElectProcessorCompleteInsertPatriarch10_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*, struct _DB_QRY_SYN_DATA*);
        using PatriarchElectProcessorCompleteInsertPatriarch10_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, struct _DB_QRY_SYN_DATA*, PatriarchElectProcessorCompleteInsertPatriarch10_ptr);
        using PatriarchElectProcessorCompleteItemChargeRefund12_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*, struct _DB_QRY_SYN_DATA*);
        using PatriarchElectProcessorCompleteItemChargeRefund12_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, struct _DB_QRY_SYN_DATA*, PatriarchElectProcessorCompleteItemChargeRefund12_ptr);
        using PatriarchElectProcessorCompleteRequestRefund14_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*, struct _DB_QRY_SYN_DATA*);
        using PatriarchElectProcessorCompleteRequestRefund14_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, struct _DB_QRY_SYN_DATA*, PatriarchElectProcessorCompleteRequestRefund14_ptr);
        using PatriarchElectProcessorCompleteSelectElect16_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorCompleteSelectElect16_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorCompleteSelectElect16_ptr);
        using PatriarchElectProcessorDestroy18_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorDestroy18_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorDestroy18_ptr);
        using PatriarchElectProcessorDoit20_ptr = bool (WINAPIV*)(struct PatriarchElectProcessor*, Cmd, struct CPlayer*, char*);
        using PatriarchElectProcessorDoit20_clbk = bool (WINAPIV*)(struct PatriarchElectProcessor*, Cmd, struct CPlayer*, char*, PatriarchElectProcessorDoit20_ptr);
        using PatriarchElectProcessorForceChangeProcessor22_ptr = bool (WINAPIV*)(struct PatriarchElectProcessor*, ElectProcessor::ProcessorType);
        using PatriarchElectProcessorForceChangeProcessor22_clbk = bool (WINAPIV*)(struct PatriarchElectProcessor*, ElectProcessor::ProcessorType, PatriarchElectProcessorForceChangeProcessor22_ptr);
        using PatriarchElectProcessorGetCurrPatriarchElectSerial24_ptr = unsigned int (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorGetCurrPatriarchElectSerial24_clbk = unsigned int (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorGetCurrPatriarchElectSerial24_ptr);
        using PatriarchElectProcessorGetElectSerial26_ptr = unsigned int (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorGetElectSerial26_clbk = unsigned int (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorGetElectSerial26_ptr);
        using PatriarchElectProcessorGetProcessorType28_ptr = ElectProcessor::ProcessorType (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorGetProcessorType28_clbk = ElectProcessor::ProcessorType (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorGetProcessorType28_ptr);
        using PatriarchElectProcessorGetTimeCheck30_ptr = bool (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorGetTimeCheck30_clbk = bool (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorGetTimeCheck30_ptr);
        using PatriarchElectProcessorInitProcess32_ptr = bool (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorInitProcess32_clbk = bool (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorInitProcess32_ptr);
        using PatriarchElectProcessorInitialize34_ptr = bool (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorInitialize34_clbk = bool (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorInitialize34_ptr);
        using PatriarchElectProcessorInsert_Elect36_ptr = int (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorInsert_Elect36_clbk = int (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorInsert_Elect36_ptr);
        using PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_ptr = int (WINAPIV*)(struct PatriarchElectProcessor*, char*);
        using PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_clbk = int (WINAPIV*)(struct PatriarchElectProcessor*, char*, PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_ptr);
        using PatriarchElectProcessorInstance40_ptr = struct PatriarchElectProcessor* (WINAPIV*)();
        using PatriarchElectProcessorInstance40_clbk = struct PatriarchElectProcessor* (WINAPIV*)(PatriarchElectProcessorInstance40_ptr);
        using PatriarchElectProcessorLoadDatabae42_ptr = bool (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorLoadDatabae42_clbk = bool (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorLoadDatabae42_ptr);
        using PatriarchElectProcessorLoadElectState44_ptr = bool (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorLoadElectState44_clbk = bool (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorLoadElectState44_ptr);
        using PatriarchElectProcessorLoop46_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorLoop46_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorLoop46_ptr);
        
        using PatriarchElectProcessorctor_PatriarchElectProcessor48_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorctor_PatriarchElectProcessor48_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorctor_PatriarchElectProcessor48_ptr);
        using PatriarchElectProcessorPushDQSCheckInvalidChar50_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorPushDQSCheckInvalidChar50_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorPushDQSCheckInvalidChar50_ptr);
        using PatriarchElectProcessorRequest_Refund52_ptr = int (WINAPIV*)(struct PatriarchElectProcessor*, char*);
        using PatriarchElectProcessorRequest_Refund52_clbk = int (WINAPIV*)(struct PatriarchElectProcessor*, char*, PatriarchElectProcessorRequest_Refund52_ptr);
        using PatriarchElectProcessorSendMsg_ConnectNewUser54_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*, struct CPlayer*);
        using PatriarchElectProcessorSendMsg_ConnectNewUser54_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, struct CPlayer*, PatriarchElectProcessorSendMsg_ConnectNewUser54_ptr);
        using PatriarchElectProcessorSendMsg_ResultCode56_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*, unsigned int, char);
        using PatriarchElectProcessorSendMsg_ResultCode56_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, unsigned int, char, PatriarchElectProcessorSendMsg_ResultCode56_ptr);
        using PatriarchElectProcessorSetCurrPatriarchElectSerial58_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*, unsigned int);
        using PatriarchElectProcessorSetCurrPatriarchElectSerial58_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, unsigned int, PatriarchElectProcessorSetCurrPatriarchElectSerial58_ptr);
        using PatriarchElectProcessorSetTimeCheck60_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*, bool);
        using PatriarchElectProcessorSetTimeCheck60_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, bool, PatriarchElectProcessorSetTimeCheck60_ptr);
        using PatriarchElectProcessorTimeCheck62_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*, uint16_t, uint16_t);
        using PatriarchElectProcessorTimeCheck62_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, uint16_t, uint16_t, PatriarchElectProcessorTimeCheck62_ptr);
        using PatriarchElectProcessorUpdate_Elect64_ptr = int (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessorUpdate_Elect64_clbk = int (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessorUpdate_Elect64_ptr);
        
        using PatriarchElectProcessordtor_PatriarchElectProcessor68_ptr = void (WINAPIV*)(struct PatriarchElectProcessor*);
        using PatriarchElectProcessordtor_PatriarchElectProcessor68_clbk = void (WINAPIV*)(struct PatriarchElectProcessor*, PatriarchElectProcessordtor_PatriarchElectProcessor68_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

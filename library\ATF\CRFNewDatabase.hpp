// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLogFile.hpp>
#include <CRFNewDatabaseVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CRFNewDatabase
    {
        CRFNewDatabaseVtbl *vfptr;
        void *m_hEnv;
        void *m_hDbc;
        void *m_hStmtSelect;
        void *m_hStmtUpdate;
        bool m_bConectionActive;
        bool m_bSaveDBLog;
        CLogFile m_ProcessLogW;
        CLogFile m_ErrorLogW;
        CLogFile m_ProcessLogA;
        CLogFile m_ErrorLogA;
        char m_byLogFileHour;
        char m_szOdbcName[32];
        char m_szAccountName[32];
        char m_szPassword[32];
        bool m_bReconnectFailExit;
        char m_szLogUpperPath[128];
    public:
        bool AllocSelectHandle();
        bool AllocUpdateHandle();
        CRFNewDatabase();
        void ctor_CRFNewDatabase();
        void CheckLogFileHour();
        bool CommitTransaction();
        bool ConfigUserODBC(char* szDSN, char* szServer, char* szDatabase, uint16_t wPort);
        void DiagRecALog(int16_t sqlRet, int16_t HandleType, void* Handle);
        void DiagRecWLog(int16_t sqlRet, int16_t HandleType, void* Handle);
        void EndDataBase();
        bool EroorActionProcSQL_ERROR(void* SQLStmt);
        void ErrFmtLog(char* fmt);
        void ErrFmtLog(wchar_t* lpcwFmt);
        void ErrLog(char* szLog);
        void ErrorAction(int16_t sqlRet, void* SQLStmt);
        void ErrorMsgLog(int16_t sqlRet, char* strQuery, char* strKind, void* SQLStmt);
        void ErrorMsgLog(int16_t sqlRet, wchar_t* strQuery, wchar_t* strKind, void* SQLStmt);
        bool ExecUpdateBinaryQuery(char* strQuery, void* buf, int size, bool bNoDataError);
        bool ExecUpdateQuery(char* strQuery, bool bNoDataError);
        bool ExecUpdateQuery(wchar_t* wstrQuery, bool bNoDataError);
        void FmtLog(char* fmt);
        void FmtLog(wchar_t* lpcwFmt);
        bool FreeSelectHandle();
        bool FreeUpdateHandle();
        char GetLocalHour();
        bool IsConectionActive();
        void Log(char* szLog);
        bool ReConnectDataBase();
        bool RollbackTransaction();
        char SQLExecDirect_RetErrCode(char* strQuery);
        char SQLFetch_RetErrCode(char* strQuery);
        char SQLGetData_Binary_RetErrCode(char* strQuery, uint16_t* ColumnNumber, char* pData, uint64_t tDataSize);
        char SQLGetData_RetErrCode(char* strQuery, uint16_t* ColumnNumber, int16_t TargetType, void* TargetValue);
        void SelectCleanUp(char* strQuery);
        bool Select_NextHourDate(char byAddHour, char* szDate);
        void SetAutoCommitMode(bool bAutoCommit);
        void SetLogFile(char* szUpperLogPath, char* szOdbcName);
        void SetReconnectFailExitFlag(bool bFlag);
        bool StartDataBase(char* odbcName, char* accountName, char* passWord);
        bool TableExist(char* szTableName);
        ~CRFNewDatabase();
        void dtor_CRFNewDatabase();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

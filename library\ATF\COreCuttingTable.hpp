// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <COreCuttingTableVtbl.hpp>
#include <CRecordData.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct COreCuttingTable
    {
        struct _ore_cut_list
        {
            struct _res_list
            {
                unsigned __int16 wResIndex;
                unsigned int dwRate;
                unsigned int dwCumRate;
            };
            int nResNum;
            unsigned int dwTotalRate;
            _res_list ResList[100];
        public:
            _ore_cut_list();
            void ctor__ore_cut_list();
        };
        COreCuttingTableVtbl *vfptr;
        CRecordData m_tblOreCutting;
        int m_nOreNum;
        _ore_cut_list *pOreList;
    public:
        COreCuttingTable();
        void ctor_COreCuttingTable();
        unsigned int GetOreIndexFromRate(unsigned int dwOreIndex, unsigned int dwRate);
        bool Indexing(struct CRecordData* pOreRec, struct CRecordData* pResRec, char* pszErrMsg);
        bool ReadRecord(char* szFile, struct CRecordData* pOreRec, struct CRecordData* pResRec, char* pszErrMsg);
        ~COreCuttingTable();
        void dtor_COreCuttingTable();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<COreCuttingTable, 200>(), "COreCuttingTable");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace US
    {
        #pragma pack(push, 8)
        template<typename _System, typename _ThreadPool>
        struct ThreadParamInterface
        {
            _System *m_pOwner;
            _ThreadPool *m_pMyThreadPool;
            int (WINAPIV *m_pOwnerMemberFunc)(_System *_this, void *);
            BYTE gap18[16];
        };
        #pragma pack(pop)
    }; // end namespace US
END_ATF_NAMESPACE

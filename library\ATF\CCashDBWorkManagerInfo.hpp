// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDBWorkManager.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDBWorkManagerctor_CCashDBWorkManager2_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
        using CCashDBWorkManagerctor_CCashDBWorkManager2_clbk = void (WINAPIV*)(struct CCashDBWorkManager*, CCashDBWorkManagerctor_CCashDBWorkManager2_ptr);
        using CCashDBWorkManagerCompleteWork4_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
        using CCashDBWorkManagerCompleteWork4_clbk = void (WINAPIV*)(struct CCashDBWorkManager*, CCashDBWorkManagerCompleteWork4_ptr);
        using CCashDBWorkManagerGetBillingDBConnectionStatus6_ptr = bool (WINAPIV*)(struct CCashDBWorkManager*);
        using CCashDBWorkManagerGetBillingDBConnectionStatus6_clbk = bool (WINAPIV*)(struct CCashDBWorkManager*, CCashDBWorkManagerGetBillingDBConnectionStatus6_ptr);
        using CCashDBWorkManagerGetUseCashQueryStr8_ptr = void (WINAPIV*)(struct CCashDBWorkManager*, struct _param_cash_update*, int, char*, uint64_t);
        using CCashDBWorkManagerGetUseCashQueryStr8_clbk = void (WINAPIV*)(struct CCashDBWorkManager*, struct _param_cash_update*, int, char*, uint64_t, CCashDBWorkManagerGetUseCashQueryStr8_ptr);
        using CCashDBWorkManagerInitialize10_ptr = bool (WINAPIV*)(struct CCashDBWorkManager*);
        using CCashDBWorkManagerInitialize10_clbk = bool (WINAPIV*)(struct CCashDBWorkManager*, CCashDBWorkManagerInitialize10_ptr);
        using CCashDBWorkManagerInitializeWorker12_ptr = bool (WINAPIV*)(struct CCashDBWorkManager*);
        using CCashDBWorkManagerInitializeWorker12_clbk = bool (WINAPIV*)(struct CCashDBWorkManager*, CCashDBWorkManagerInitializeWorker12_ptr);
        using CCashDBWorkManagerPushTask14_ptr = bool (WINAPIV*)(struct CCashDBWorkManager*, int, char*, uint64_t);
        using CCashDBWorkManagerPushTask14_clbk = bool (WINAPIV*)(struct CCashDBWorkManager*, int, char*, uint64_t, CCashDBWorkManagerPushTask14_ptr);
        using CCashDBWorkManagerStart16_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
        using CCashDBWorkManagerStart16_clbk = void (WINAPIV*)(struct CCashDBWorkManager*, CCashDBWorkManagerStart16_ptr);
        
        using CCashDBWorkManagerdtor_CCashDBWorkManager21_ptr = void (WINAPIV*)(struct CCashDBWorkManager*);
        using CCashDBWorkManagerdtor_CCashDBWorkManager21_clbk = void (WINAPIV*)(struct CCashDBWorkManager*, CCashDBWorkManagerdtor_CCashDBWorkManager21_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

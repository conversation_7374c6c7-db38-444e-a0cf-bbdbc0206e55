// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _numberfmtA
    {
        unsigned int NumDigits;
        unsigned int LeadingZero;
        unsigned int Grouping;
        char *lpDecimalSep;
        char *lpThousandSep;
        unsigned int NegativeOrder;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

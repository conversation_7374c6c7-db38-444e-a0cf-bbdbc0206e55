// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GMRequestData.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using GMRequestDatactor_GMRequestData2_ptr = void (WINAPIV*)(struct GMRequestData*);
        using GMRequestDatactor_GMRequestData2_clbk = void (WINAPIV*)(struct GMRequestData*, GMRequestDatactor_GMRequestData2_ptr);
        using GMRequestDataSet4_ptr = void (WINAPIV*)(struct GMRequestData*, unsigned int, char*, unsigned int);
        using GMRequestDataSet4_clbk = void (WINAPIV*)(struct GMRequestData*, unsigned int, char*, unsigned int, GMRequestDataSet4_ptr);
        
        using GMRequestDatadtor_GMRequestData9_ptr = void (WINAPIV*)(struct GMRequestData*);
        using GMRequestDatadtor_GMRequestData9_clbk = void (WINAPIV*)(struct GMRequestData*, GMRequestDatadtor_GMRequestData9_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <MonsterSFContDamageToleracne.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using MonsterSFContDamageToleracneGetToleranceProb2_ptr = float (WINAPIV*)(struct MonsterSFContDamageToleracne*);
        using MonsterSFContDamageToleracneGetToleranceProb2_clbk = float (WINAPIV*)(struct MonsterSFContDamageToleracne*, MonsterSFContDamageToleracneGetToleranceProb2_ptr);
        using MonsterSFContDamageToleracneInit4_ptr = void (WINAPIV*)(struct MonsterSFContDamageToleracne*, float);
        using MonsterSFContDamageToleracneInit4_clbk = void (WINAPIV*)(struct MonsterSFContDamageToleracne*, float, MonsterSFContDamageToleracneInit4_ptr);
        using MonsterSFContDamageToleracneIsSFContDamage6_ptr = bool (WINAPIV*)(struct MonsterSFContDamageToleracne*);
        using MonsterSFContDamageToleracneIsSFContDamage6_clbk = bool (WINAPIV*)(struct MonsterSFContDamageToleracne*, MonsterSFContDamageToleracneIsSFContDamage6_ptr);
        
        using MonsterSFContDamageToleracnector_MonsterSFContDamageToleracne8_ptr = void (WINAPIV*)(struct MonsterSFContDamageToleracne*);
        using MonsterSFContDamageToleracnector_MonsterSFContDamageToleracne8_clbk = void (WINAPIV*)(struct MonsterSFContDamageToleracne*, MonsterSFContDamageToleracnector_MonsterSFContDamageToleracne8_ptr);
        using MonsterSFContDamageToleracneOnlyOnceInit10_ptr = void (WINAPIV*)(struct MonsterSFContDamageToleracne*, struct CMonster*);
        using MonsterSFContDamageToleracneOnlyOnceInit10_clbk = void (WINAPIV*)(struct MonsterSFContDamageToleracne*, struct CMonster*, MonsterSFContDamageToleracneOnlyOnceInit10_ptr);
        using MonsterSFContDamageToleracneSetSFDamageToleracne_Variation12_ptr = void (WINAPIV*)(struct MonsterSFContDamageToleracne*, float);
        using MonsterSFContDamageToleracneSetSFDamageToleracne_Variation12_clbk = void (WINAPIV*)(struct MonsterSFContDamageToleracne*, float, MonsterSFContDamageToleracneSetSFDamageToleracne_Variation12_ptr);
        using MonsterSFContDamageToleracneUpdate14_ptr = void (WINAPIV*)(struct MonsterSFContDamageToleracne*);
        using MonsterSFContDamageToleracneUpdate14_clbk = void (WINAPIV*)(struct MonsterSFContDamageToleracne*, MonsterSFContDamageToleracneUpdate14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CUnmannedTraderSchedule.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CUnmannedTraderSchedulector_CUnmannedTraderSchedule2_ptr = void (WINAPIV*)(struct CUnmannedTraderSchedule*);
        using CUnmannedTraderSchedulector_CUnmannedTraderSchedule2_clbk = void (WINAPIV*)(struct CUnmannedTraderSchedule*, CUnmannedTraderSchedulector_CUnmannedTraderSchedule2_ptr);
        using CUnmannedTraderScheduleClear4_ptr = void (WINAPIV*)(struct CUnmannedTraderSchedule*);
        using CUnmannedTraderScheduleClear4_clbk = void (WINAPIV*)(struct CUnmannedTraderSchedule*, CUnmannedTraderScheduleClear4_ptr);
        using CUnmannedTraderScheduleCompleteClear6_ptr = void (WINAPIV*)(struct CUnmannedTraderSchedule*, char, char);
        using CUnmannedTraderScheduleCompleteClear6_clbk = void (WINAPIV*)(struct CUnmannedTraderSchedule*, char, char, CUnmannedTraderScheduleCompleteClear6_ptr);
        using CUnmannedTraderScheduleGetOwnerSerial8_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderSchedule*);
        using CUnmannedTraderScheduleGetOwnerSerial8_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderSchedule*, CUnmannedTraderScheduleGetOwnerSerial8_ptr);
        using CUnmannedTraderScheduleGetRegistSerial10_ptr = unsigned int (WINAPIV*)(struct CUnmannedTraderSchedule*);
        using CUnmannedTraderScheduleGetRegistSerial10_clbk = unsigned int (WINAPIV*)(struct CUnmannedTraderSchedule*, CUnmannedTraderScheduleGetRegistSerial10_ptr);
        using CUnmannedTraderScheduleGetType12_ptr = char (WINAPIV*)(struct CUnmannedTraderSchedule*);
        using CUnmannedTraderScheduleGetType12_clbk = char (WINAPIV*)(struct CUnmannedTraderSchedule*, CUnmannedTraderScheduleGetType12_ptr);
        using CUnmannedTraderScheduleIsDone14_ptr = bool (WINAPIV*)(struct CUnmannedTraderSchedule*);
        using CUnmannedTraderScheduleIsDone14_clbk = bool (WINAPIV*)(struct CUnmannedTraderSchedule*, CUnmannedTraderScheduleIsDone14_ptr);
        using CUnmannedTraderScheduleIsWait16_ptr = bool (WINAPIV*)(struct CUnmannedTraderSchedule*);
        using CUnmannedTraderScheduleIsWait16_clbk = bool (WINAPIV*)(struct CUnmannedTraderSchedule*, CUnmannedTraderScheduleIsWait16_ptr);
        using CUnmannedTraderSchedulePushClear18_ptr = void (WINAPIV*)(struct CUnmannedTraderSchedule*, bool);
        using CUnmannedTraderSchedulePushClear18_clbk = void (WINAPIV*)(struct CUnmannedTraderSchedule*, bool, CUnmannedTraderSchedulePushClear18_ptr);
        using CUnmannedTraderScheduleSet20_ptr = void (WINAPIV*)(struct CUnmannedTraderSchedule*, char, unsigned int, int64_t, unsigned int, unsigned int);
        using CUnmannedTraderScheduleSet20_clbk = void (WINAPIV*)(struct CUnmannedTraderSchedule*, char, unsigned int, int64_t, unsigned int, unsigned int, CUnmannedTraderScheduleSet20_ptr);
        
        using CUnmannedTraderScheduledtor_CUnmannedTraderSchedule24_ptr = void (WINAPIV*)(struct CUnmannedTraderSchedule*);
        using CUnmannedTraderScheduledtor_CUnmannedTraderSchedule24_clbk = void (WINAPIV*)(struct CUnmannedTraderSchedule*, CUnmannedTraderScheduledtor_CUnmannedTraderSchedule24_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

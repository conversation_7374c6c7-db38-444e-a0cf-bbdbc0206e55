// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum $FE149BD64943A35FB471F74C3D3B2245
    {
      INVEN = 0x0,
      EQUIP = 0x1,
      EMBELLISH = 0x2,
      FORCE = 0x3,
      ANIMUS = 0x4,
      TRUNK = 0x5,
      PERSONAL_AMINE = 0x6,
      EXT_TRUNK = 0x7,
      STORAGE_NUM = 0x8,
    };
    using STORAGE_POS = $FE149BD64943A35FB471F74C3D3B2245;
END_ATF_NAMESPACE

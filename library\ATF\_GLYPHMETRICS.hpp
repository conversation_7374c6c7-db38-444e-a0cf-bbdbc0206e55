// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    struct _GLYPHMETRICS
    {
        unsigned int gmBlackBoxX;
        unsigned int gmBlackBoxY;
        tagPOINT gmptGlyphOrigin;
        __int16 gmCellIncX;
        __int16 gmCellIncY;
    };
END_ATF_NAMESPACE

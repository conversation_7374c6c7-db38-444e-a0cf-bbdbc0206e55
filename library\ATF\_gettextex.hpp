// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _gettextex
    {
        unsigned int cb;
        unsigned int flags;
        unsigned int codepage;
        const char *lpDefaultChar;
        int *lpUsedDefChar;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

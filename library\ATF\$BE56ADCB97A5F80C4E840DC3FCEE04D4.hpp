// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct $BE56ADCB97A5F80C4E840DC3FCEE04D4
    {
        BYTE gap0[8];
        wchar_t **pbstrVal;
    };    
    static_assert(ATF::checkSize<$BE56ADCB97A5F80C4E840DC3FCEE04D4, 16>(), "$BE56ADCB97A5F80C4E840DC3FCEE04D4");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _cutting_complete_result_zocl
    {
        enum RESULT_TYPE
        {
            RT_SUCCESS = 0x0,
            RT_LIMIT_MAX_GOLD = 0x1,
        };
        unsigned int dwLeftGold;
        char byRet;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <_RPC_C_OPT_METADATA_DESCRIPTOR.hpp>
#include <_RPC_SYNTAX_IDENTIFIER.hpp>


START_ATF_NAMESPACE
    struct _RDR_CALLOUT_STATE
    {
        int LastError;
        void *LastEEInfo;
        _RPC_HTTP_REDIRECTOR_STAGE LastCalledStage;
        unsigned __int16 *ServerName;
        unsigned __int16 *ServerPort;
        unsigned __int16 *RemoteUser;
        unsigned __int16 *AuthType;
        char ResourceTypePresent;
        char MetadataPresent;
        char SessionIdPresent;
        char InterfacePresent;
        _GUID ResourceType;
        _RPC_C_OPT_METADATA_DESCRIPTOR Metadata;
        _GUID SessionId;
        _RPC_SYNTAX_IDENTIFIER Interface;
        void *CertContext;
    };
E<PERSON>_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_party_member_info_upd.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _party_member_info_updctor__party_member_info_upd2_ptr = void (WINAPIV*)(struct _party_member_info_upd*);
        using _party_member_info_updctor__party_member_info_upd2_clbk = void (WINAPIV*)(struct _party_member_info_upd*, _party_member_info_updctor__party_member_info_upd2_ptr);
        using _party_member_info_updsize4_ptr = int (WINAPIV*)(struct _party_member_info_upd*);
        using _party_member_info_updsize4_clbk = int (WINAPIV*)(struct _party_member_info_upd*, _party_member_info_updsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPos.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Info
        {
            
            using GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosctor_CNormalGuildBattleStateRoundReturnStartPos2_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosctor_CNormalGuildBattleStateRoundReturnStartPos2_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos*, GUI<PERSON>_BATTLE__CNormalGuildBattleStateRoundR<PERSON>urnStartPosctor_CNormalGuildBattleStateRoundReturnStartPos2_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosEnter4_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosEnter4_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosEnter4_ptr);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosLoop6_ptr = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos*, struct GUILD_BATTLE::CNormalGuildBattle*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosLoop6_clbk = int (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos*, struct GUILD_BATTLE::CNormalGuildBattle*, GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosLoop6_ptr);
            
            using GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosdtor_CNormalGuildBattleStateRoundReturnStartPos8_ptr = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos*);
            using GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosdtor_CNormalGuildBattleStateRoundReturnStartPos8_clbk = void (WINAPIV*)(struct GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos*, GUILD_BATTLE__CNormalGuildBattleStateRoundReturnStartPosdtor_CNormalGuildBattleStateRoundReturnStartPos8_ptr);
        }; // end namespace Info
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE

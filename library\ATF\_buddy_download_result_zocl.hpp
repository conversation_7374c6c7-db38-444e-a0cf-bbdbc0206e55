// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _buddy_download_result_zocl
    {
        unsigned __int16 wDataSize;
        char sData[10000];
    public:
        _buddy_download_result_zocl();
        void ctor__buddy_download_result_zocl();
        int size();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_buddy_download_result_zocl, 10002>(), "_buddy_download_result_zocl");
END_ATF_NAMESPACE

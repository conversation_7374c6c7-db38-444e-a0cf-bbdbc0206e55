// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct INationGameGuardSystemVtbl
    {
        void *(WINAPIV *__vecDelDtor)(struct INationGameGuardSystem *_this, unsigned int);
        BYTE gap8[8];
        bool (WINAPIV *OnCheckSession_FirstVerify)(struct INationGameGuardSystem *_this, int);
        void (WINAPIV *OnDisConnectSession)(struct INationGameGuardSystem *_this, int);
        void (WINAPIV *OnLoopSession)(struct INationGameGuardSystem *_this, int);
        void (WINAPIV *OnLoop)(struct INationGameGuardSystem *_this);
        bool (WINAPIV *IsActive)(struct INationGameGuardSystem *_this);
        bool (WINAPIV *IsInit)(struct INationGameGuardSystem *_this);
        bool (WINAPIV *RecvClientLine)(struct INationGameGuardSystem *_this, int, struct _MSG_HEADER *, char *);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _action_node
    {
        int m_nActType;
        char m_strActSub[64];
        char m_strActSub2[64];
        char m_strActArea[64];
        int m_nReqAct;
        int m_nSetCntPro_100;
        char m_strLinkQuestItem[64];
        int m_nOrder;
    };
END_ATF_NAMESPACE

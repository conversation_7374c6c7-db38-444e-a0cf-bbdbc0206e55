#include <_post_content_result_zocl.hpp>


START_ATF_NAMESPACE
    _post_content_result_zocl::_post_content_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _post_content_result_zocl*);
        (org_ptr(0x1400f04c0L))(this);
    };
    void _post_content_result_zocl::ctor__post_content_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _post_content_result_zocl*);
        (org_ptr(0x1400f04c0L))(this);
    };
    
END_ATF_NAMESPACE

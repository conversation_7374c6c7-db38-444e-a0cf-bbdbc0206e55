// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMyTimer.hpp>
#include <CPlayer.hpp>
#include <EventItemInfo.hpp>
#include <RFEventBase.hpp>
#include <_FILETIME.hpp>


START_ATF_NAMESPACE
    struct  CExchangeEvent : RFEventBase
    {
        bool m_bEnable;
        bool m_bDelete;
        char m_EventItemCode[4][64];
        EventItemInfo m_EventItemInfo[4];
        char m_byState;
        bool m_bGiveItem;
        bool m_bWait;
        bool m_bModifyEnable;
        bool m_bModifyDelete;
        char m_ModifyItemCode[4][64];
        unsigned int m_dwPlayerArrayNo;
        CMyTimer m_tmDataFileCheckTime;
        _FILETIME m_ftWrite;
    public:
        CExchangeEvent();
        void ctor_CExchangeEvent();
        void ChangeData();
        bool CheckBuddhaEventData(bool* pbDelete);
        void DeleteExchangeEventItem(struct CPlayer* pOne);
        static void Destroy();
        struct EventItemInfo* GetEventItemInfo(int nInfoType);
        void GiveEventItem(struct CPlayer* pOne);
        bool Initialzie();
        static struct CExchangeEvent* Instance();
        bool IsDelete();
        bool IsEnable();
        bool IsWait();
        void Loop();
        void ReadBuddhaEventInfo();
        ~CExchangeEvent();
        void dtor_CExchangeEvent();
    };
END_ATF_NAMESPACE

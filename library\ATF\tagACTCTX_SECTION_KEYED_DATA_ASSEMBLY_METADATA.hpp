// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagACTCTX_SECTION_KEYED_DATA_ASSEMBLY_METADATA
    {
        void *lpInformation;
        void *lpSectionBase;
        unsigned int ulSectionLength;
        void *lpSectionGlobalDataBase;
        unsigned int ulSectionGlobalDataLength;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRFNewDatabase.hpp>
#include <_LTD.hpp>
#include <_LTD_EXPEND.hpp>
#include <_LTD_ITEMINFO.hpp>


START_ATF_NAMESPACE
    struct  CRFDBItemLog : CRFNewDatabase
    {
        bool m_bTblLtd;
        bool m_bTblLtdItemInfo;
        bool m_bTblLtdExpend;
        unsigned int m_dwKorTime;
    public:
        CRFDBItemLog(unsigned int dwLocalDate);
        void ctor_CRFDBItemLog(unsigned int dwLocalDate);
        bool CreateTblLtd(int nKorTime);
        bool CreateTblLtd_Expend(int nKorTime);
        bool CreateTblLtd_ItemInfo(int nKorTime);
        void SetKorTime(unsigned int dwKorTime);
        bool insert_expend(struct _LTD_EXPEND* pe);
        bool insert_iteminfo(struct _LTD_ITEMINFO* pi, char byIndex);
        bool insert_ltd(struct _LTD* pl);
        ~CRFDBItemLog();
        void dtor_CRFDBItemLog();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _pt_inform_punishment_zocl
    {
        char byType;
        int nRemainMin;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_pt_inform_punishment_zocl, 5>(), "_pt_inform_punishment_zocl");
END_ATF_NAMESPACE

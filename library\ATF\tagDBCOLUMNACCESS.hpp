// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagDBID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagDBCOLUMNACCESS
    {
        void *pData;
        tagDBID columnid;
        unsigned __int64 cbDataLen;
        unsigned int dwStatus;
        unsigned __int64 cbMaxLen;
        unsigned __int64 dwReserved;
        unsigned __int16 wType;
        char bPrecision;
        char bScale;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

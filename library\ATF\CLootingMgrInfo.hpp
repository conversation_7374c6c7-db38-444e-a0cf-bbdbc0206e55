// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CLootingMgr.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CLootingMgrctor_CLootingMgr2_ptr = void (WINAPIV*)(struct CLootingMgr*);
        using CLootingMgrctor_CLootingMgr2_clbk = void (WINAPIV*)(struct CLootingMgr*, CLootingMgrctor_CLootingMgr2_ptr);
        using CLootingMgrGetLooter4_ptr = struct CPlayer* (WINAPIV*)(struct CLootingMgr*, struct CMapData*, float*, struct CPlayer*);
        using CLootingMgrGetLooter4_clbk = struct CPlayer* (WINAPIV*)(struct CLootingMgr*, struct CMapData*, float*, struct CPlayer*, CLootingMgrGetLooter4_ptr);
        using CLootingMgrInit6_ptr = void (WINAPIV*)(struct CLootingMgr*, int);
        using CLootingMgrInit6_clbk = void (WINAPIV*)(struct CLootingMgr*, int, CLootingMgrInit6_ptr);
        using CLootingMgrPushDamage8_ptr = void (WINAPIV*)(struct CLootingMgr*, struct CPlayer*, uint16_t);
        using CLootingMgrPushDamage8_clbk = void (WINAPIV*)(struct CLootingMgr*, struct CPlayer*, uint16_t, CLootingMgrPushDamage8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

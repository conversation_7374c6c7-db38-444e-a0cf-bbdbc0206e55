// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _moveout_user_result_zone
    {
        char byRetCode;
        unsigned int dwIP;
        unsigned __int16 wPort;
        unsigned int dwZoneMasterKey;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_moveout_user_result_zone, 11>(), "_moveout_user_result_zone");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_make_storage.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_make_storagector__qry_case_make_storage2_ptr = void (WINAPIV*)(struct _qry_case_make_storage*);
        using _qry_case_make_storagector__qry_case_make_storage2_clbk = void (WINAPIV*)(struct _qry_case_make_storage*, _qry_case_make_storagector__qry_case_make_storage2_ptr);
        using _qry_case_make_storagesize4_ptr = int (WINAPIV*)(struct _qry_case_make_storage*);
        using _qry_case_make_storagesize4_clbk = int (WINAPIV*)(struct _qry_case_make_storage*, _qry_case_make_storagesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

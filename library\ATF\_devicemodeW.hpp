// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$1F648CDE8B91DAD47C202DBDC1F209EC.hpp>
#include <$28F1FCCE9FF3D48243AA243A25B78D23.hpp>
#include <$309666D137CBB19809FA597AD348B415.hpp>
#include <$83459A7C9C04AACA1DBB41306C275D7C.hpp>



START_ATF_NAMESPACE
    struct _devicemodeW
    {
        wchar_t dmDeviceName[32];
        unsigned __int16 dmSpecVersion;
        unsigned __int16 dmDriverVersion;
        unsigned __int16 dmSize;
        unsigned __int16 dmDriverExtra;
        unsigned int dmFields;
        $28F1FCCE9FF3D48243AA243A25B78D23 ___u6;
        $83459A7C9C04AACA1DBB41306C275D7C ___u7;
        $1F648CDE8B91DAD47C202DBDC1F209EC ___u8;
        __int16 dmColor;
        __int16 dmDuplex;
        __int16 dmYResolution;
        __int16 dmTTOption;
        __int16 dmCollate;
        wchar_t dmFormName[32];
        unsigned __int16 dmLogPixels;
        unsigned int dmBitsPerPel;
        unsigned int dmPelsWidth;
        unsigned int dmPelsHeight;
        $309666D137CBB19809FA597AD348B415 ___u19;
        unsigned int dmDisplayFrequency;
        unsigned int dmICMMethod;
        unsigned int dmICMIntent;
        unsigned int dmMediaType;
        unsigned int dmDitherType;
        unsigned int dmReserved1;
        unsigned int dmReserved2;
        unsigned int dmPanningWidth;
        unsigned int dmPanningHeight;
    };
END_ATF_NAMESPACE

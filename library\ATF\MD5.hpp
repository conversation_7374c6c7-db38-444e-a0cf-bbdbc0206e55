// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct MD5
    {
        unsigned int state[4];
        unsigned int count[2];
        char buffer[64];
    public:
        void Decode(unsigned int* output, char* input, unsigned int size);
        void Encode(char* output, unsigned int* input, unsigned int size);
        void Finalize(char* digest);
        void Init();
        MD5();
        void ctor_MD5();
        void Transform(char* block);
        void Update(void* inputBuffer, unsigned int size);
        ~MD5();
        void dtor_MD5();
    };
END_ATF_NAMESPACE

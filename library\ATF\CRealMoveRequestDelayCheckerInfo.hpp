// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CRealMoveRequestDelayChecker.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CRealMoveRequestDelayCheckerctor_CRealMoveRequestDelayChecker2_ptr = void (WINAPIV*)(struct CRealMoveRequestDelayChecker*);
        using CRealMoveRequestDelayCheckerctor_CRealMoveRequestDelayChecker2_clbk = void (WINAPIV*)(struct CRealMoveRequestDelayChecker*, CRealMoveRequestDelayCheckerctor_CRealMoveRequestDelayChecker2_ptr);
        using CRealMoveRequestDelayCheckerCheck4_ptr = bool (WINAPIV*)(struct CRealMoveRequestDelayChecker*, struct CPlayer*);
        using CRealMoveRequestDelayCheckerCheck4_clbk = bool (WINAPIV*)(struct CRealMoveRequestDelayChecker*, struct CPlayer*, CRealMoveRequestDelayCheckerCheck4_ptr);
        using CRealMoveRequestDelayCheckerCheckDelay6_ptr = bool (WINAPIV*)(struct CRealMoveRequestDelayChecker*, unsigned int, unsigned int);
        using CRealMoveRequestDelayCheckerCheckDelay6_clbk = bool (WINAPIV*)(struct CRealMoveRequestDelayChecker*, unsigned int, unsigned int, CRealMoveRequestDelayCheckerCheckDelay6_ptr);
        using CRealMoveRequestDelayCheckerGetCurInx8_ptr = bool (WINAPIV*)(struct CRealMoveRequestDelayChecker*, unsigned int*);
        using CRealMoveRequestDelayCheckerGetCurInx8_clbk = bool (WINAPIV*)(struct CRealMoveRequestDelayChecker*, unsigned int*, CRealMoveRequestDelayCheckerGetCurInx8_ptr);
        using CRealMoveRequestDelayCheckerIncNodeIndex10_ptr = void (WINAPIV*)(struct CRealMoveRequestDelayChecker*);
        using CRealMoveRequestDelayCheckerIncNodeIndex10_clbk = void (WINAPIV*)(struct CRealMoveRequestDelayChecker*, CRealMoveRequestDelayCheckerIncNodeIndex10_ptr);
        using CRealMoveRequestDelayCheckerInit12_ptr = bool (WINAPIV*)(struct CRealMoveRequestDelayChecker*, unsigned int);
        using CRealMoveRequestDelayCheckerInit12_clbk = bool (WINAPIV*)(struct CRealMoveRequestDelayChecker*, unsigned int, CRealMoveRequestDelayCheckerInit12_ptr);
        using CRealMoveRequestDelayCheckerReset14_ptr = void (WINAPIV*)(struct CRealMoveRequestDelayChecker*);
        using CRealMoveRequestDelayCheckerReset14_clbk = void (WINAPIV*)(struct CRealMoveRequestDelayChecker*, CRealMoveRequestDelayCheckerReset14_ptr);
        
        using CRealMoveRequestDelayCheckerdtor_CRealMoveRequestDelayChecker16_ptr = void (WINAPIV*)(struct CRealMoveRequestDelayChecker*);
        using CRealMoveRequestDelayCheckerdtor_CRealMoveRequestDelayChecker16_clbk = void (WINAPIV*)(struct CRealMoveRequestDelayChecker*, CRealMoveRequestDelayCheckerdtor_CRealMoveRequestDelayChecker16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

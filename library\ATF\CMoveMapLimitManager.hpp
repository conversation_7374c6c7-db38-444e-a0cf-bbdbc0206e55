// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CMoveMapLimitInfoList.hpp>
#include <CMoveMapLimitRightInfoList.hpp>
#include <CPlayer.hpp>


START_ATF_NAMESPACE
    struct CMoveMapLimitManager
    {
        CMoveMapLimitRightInfoList m_kRightInfo;
        CMoveMapLimitInfoList m_kLimitInfo;
    public:
        CMoveMapLimitManager();
        void ctor_CMoveMapLimitManager();
        void CreateComplete(struct CPlayer* pkPlayer);
        static void Destroy();
        bool Init();
        static struct CMoveMapLimitManager* Instance();
        void Load(struct CPlayer* pkPlayer);
        void LogIn(struct CPlayer* pkPlayer);
        void LogOut(struct CPlayer* pkPlayer);
        void Loop();
        bool MoveLimitMapZoneRequest(int iUserInx, char* pRequest);
        char Request(int iLimitType, int iRequetType, int iMapInx, unsigned int dwStoreRecordIndex, int iUserInx, char* pRequest);
        char RequestElanMapUserForceMoveHQ();
        ~CMoveMapLimitManager();
        void dtor_CMoveMapLimitManager();
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _effect_add_inform_zocl
    {
        char byLv;
        unsigned __int16 wEffectCode;
        unsigned __int16 wDurSec;
        unsigned int dwPlayerSerial;
        char wszPlayerName[17];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

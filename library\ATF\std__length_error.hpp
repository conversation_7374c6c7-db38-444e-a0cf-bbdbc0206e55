// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std__basic_string.hpp>
#include <std__logic_error.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        struct  length_error : logic_error
        {
        public:
            length_error(std::basic_string<char>* _Message);
            void ctor_length_error(std::basic_string<char>* _Message);
            length_error(struct length_error* __that);
            void ctor_length_error(struct length_error* __that);
            ~length_error();
            void dtor_length_error();
        };    
        static_assert(ATF::checkSize<std::length_error, 72>(), "std::length_error");
    }; // end namespace std
END_ATF_NAMESPACE

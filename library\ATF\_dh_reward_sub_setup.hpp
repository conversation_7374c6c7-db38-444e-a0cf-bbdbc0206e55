// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>
#include <_dh_reward_sub_setupVtbl.hpp>


START_ATF_NAMESPACE
    struct _dh_reward_sub_setup
    {
        _dh_reward_sub_setupVtbl *vfptr;
        int nItemNum;
        _STORAGE_LIST::_db_con *Item[128];
        unsigned int m_dwGivePercent[128];
        long double dExp;
        unsigned int dwPvp;
        unsigned int dwDalant;
    public:
        _dh_reward_sub_setup();
        void ctor__dh_reward_sub_setup();
        ~_dh_reward_sub_setup();
        void dtor__dh_reward_sub_setup();
    };
END_ATF_NAMESPACE

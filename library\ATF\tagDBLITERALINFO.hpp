// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct tagDBLITERALINFO
    {
        wchar_t *pwszLiteralValue;
        wchar_t *pwszInvalidChars;
        wchar_t *pwszInvalidStartingChars;
        unsigned int lt;
        int fSupported;
        unsigned int cchMaxLen;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

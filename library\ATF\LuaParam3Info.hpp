// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <LuaParam3.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using LuaParam3ctor_LuaParam32_ptr = void (WINAPIV*)(struct LuaParam3*, int, int, int);
        using LuaParam3ctor_LuaParam32_clbk = void (WINAPIV*)(struct LuaParam3*, int, int, int, LuaParam3ctor_LuaParam32_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

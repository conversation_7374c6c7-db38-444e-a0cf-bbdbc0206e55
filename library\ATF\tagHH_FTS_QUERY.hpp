// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct tagHH_FTS_QUERY
    {
        int cbStruct;
        int fUniCodeStrings;
        const char *pszSearchQuery;
        int iProximity;
        int fStemmedSearch;
        int fTitleOnly;
        int fExecute;
        const char *pszWindow;
    };
END_ATF_NAMESPACE

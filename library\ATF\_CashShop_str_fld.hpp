// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _CashShop_str_fld : _base_fld
    {
        int m_nkor_Price;
        int m_nbra_Price;
        int m_nchn_Price;
        int m_ngbn_Price;
        int m_nina_Price;
        int m_njpn_Price;
        int m_nphi_Price;
        int m_nrus_Price;
        int m_ntpe_Price;
        int m_nspa_Price;
        int m_ntha_Price;
    };
END_ATF_NAMESPACE

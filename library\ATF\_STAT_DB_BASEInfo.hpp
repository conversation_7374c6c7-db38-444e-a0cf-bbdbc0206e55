// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STAT_DB_BASE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _STAT_DB_BASEGetStatIndex2_ptr = int (WINAPIV*)(char, char);
        using _STAT_DB_BASEGetStatIndex2_clbk = int (WINAPIV*)(char, char, _STAT_DB_BASEGetStatIndex2_ptr);
        using _STAT_DB_BASEInit4_ptr = void (WINAPIV*)(struct _STAT_DB_BASE*);
        using _STAT_DB_BASEInit4_clbk = void (WINAPIV*)(struct _STAT_DB_BASE*, _STAT_DB_BASEInit4_ptr);
        using _STAT_DB_BASEIsRangePerMastery6_ptr = bool (WINAPIV*)(char, char);
        using _STAT_DB_BASEIsRangePerMastery6_clbk = bool (WINAPIV*)(char, char, _STAT_DB_BASEIsRangePerMastery6_ptr);
        
        using _STAT_DB_BASEctor__STAT_DB_BASE8_ptr = void (WINAPIV*)(struct _STAT_DB_BASE*);
        using _STAT_DB_BASEctor__STAT_DB_BASE8_clbk = void (WINAPIV*)(struct _STAT_DB_BASE*, _STAT_DB_BASEctor__STAT_DB_BASE8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

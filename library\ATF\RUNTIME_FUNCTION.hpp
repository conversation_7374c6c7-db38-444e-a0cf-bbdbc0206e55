// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct RUNTIME_FUNCTION
    {
        void *__ptr32 FunctionStart;
        void *__ptr32 FunctionEnd;
        void *__ptr32 UnwindInfo;
    };    
    static_assert(ATF::checkSize<RUNTIME_FUNCTION, 12>(), "RUNTIME_FUNCTION");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__CComCriticalSection.hpp>
#include <ATL__CSimpleArray.hpp>
#include <HINSTANCE__.hpp>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        struct _ATL_BASE_MODULE70
        {
            unsigned int cbSize;
            HINSTANCE__ *m_hInst;
            HINSTANCE__ *m_hInstResource;
            bool m_bNT5orWin98;
            unsigned int dwAtlBuildVer;
            _GUID *pguidVer;
            CComCriticalSection m_csResource;
            CSimpleArray<HINSTANCE__ *,CSimpleArrayEqualHelper<HINSTANCE__ *> > m_rgResourceInstance;
        };
    }; // end namespace ATL
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _ADAPTER_STATUS
    {
        char adapter_address[6];
        char rev_major;
        char reserved0;
        char adapter_type;
        char rev_minor;
        unsigned __int16 duration;
        unsigned __int16 frmr_recv;
        unsigned __int16 frmr_xmit;
        unsigned __int16 iframe_recv_err;
        unsigned __int16 xmit_aborts;
        unsigned int xmit_success;
        unsigned int recv_success;
        unsigned __int16 iframe_xmit_err;
        unsigned __int16 recv_buff_unavail;
        unsigned __int16 t1_timeouts;
        unsigned __int16 ti_timeouts;
        unsigned int reserved1;
        unsigned __int16 free_ncbs;
        unsigned __int16 max_cfg_ncbs;
        unsigned __int16 max_ncbs;
        unsigned __int16 xmit_buf_unavail;
        unsigned __int16 max_dgram_size;
        unsigned __int16 pending_sess;
        unsigned __int16 max_cfg_sess;
        unsigned __int16 max_sess;
        unsigned __int16 max_sess_pkt_size;
        unsigned __int16 name_count;
    };
END_ATF_NAMESPACE

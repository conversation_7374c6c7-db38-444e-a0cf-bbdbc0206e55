// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CSize.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CSizector_CSize1_ptr = void (WINAPIV*)(struct CSize*, int, int);
        using CSizector_CSize1_clbk = void (WINAPIV*)(struct CSize*, int, int, CSizector_CSize1_ptr);
        
        using CSizector_CSize2_ptr = void (WINAPIV*)(struct CSize*, struct tagPOINT);
        using CSizector_CSize2_clbk = void (WINAPIV*)(struct CSize*, struct tagPOINT, CSizector_CSize2_ptr);
        
        using CSizector_CSize3_ptr = void (WINAPIV*)(struct CSize*, struct tagSIZE);
        using CSizector_CSize3_clbk = void (WINAPIV*)(struct CSize*, struct tagSIZE, CSizector_CSize3_ptr);
        
        using CS<PERSON><PERSON>_CSize4_ptr = void (WINAPIV*)(struct CSize*, unsigned int);
        using CSizector_CSize4_clbk = void (WINAPIV*)(struct CSize*, unsigned int, CSizector_CSize4_ptr);
        
        using CSizector_CSize5_ptr = void (WINAPIV*)(struct CSize*);
        using CSizector_CSize5_clbk = void (WINAPIV*)(struct CSize*, CSizector_CSize5_ptr);
        using CSizeSetSize6_ptr = void (WINAPIV*)(struct CSize*, int, int);
        using CSizeSetSize6_clbk = void (WINAPIV*)(struct CSize*, int, int, CSizeSetSize6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

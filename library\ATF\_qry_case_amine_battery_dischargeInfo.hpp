// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_qry_case_amine_battery_discharge.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _qry_case_amine_battery_dischargector__qry_case_amine_battery_discharge2_ptr = void (WINAPIV*)(struct _qry_case_amine_battery_discharge*);
        using _qry_case_amine_battery_dischargector__qry_case_amine_battery_discharge2_clbk = void (WINAPIV*)(struct _qry_case_amine_battery_discharge*, _qry_case_amine_battery_dischargector__qry_case_amine_battery_discharge2_ptr);
        using _qry_case_amine_battery_dischargesize4_ptr = int (WINAPIV*)(struct _qry_case_amine_battery_discharge*);
        using _qry_case_amine_battery_dischargesize4_clbk = int (WINAPIV*)(struct _qry_case_amine_battery_discharge*, _qry_case_amine_battery_dischargesize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

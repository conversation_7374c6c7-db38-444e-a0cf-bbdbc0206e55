// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CLID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _check_is_block_ip_result_acwr
    {
        enum RET_TYPE
        {
            RT_PASS = 0x0,
            RT_BLOCK_IP = 0x1,
            RT_PUSH_DQS_FAIL = 0x2,
            RT_DB_ERROR = 0x3,
        };
        char byRet;
        _CLID idLocal;
        unsigned int ulIP;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

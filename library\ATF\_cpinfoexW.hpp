// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _cpinfoexW
    {
        unsigned int MaxCharSize;
        char <PERSON><PERSON>ult<PERSON>har[2];
        char Lead<PERSON><PERSON>[12];
        wchar_t UnicodeDefaultChar;
        unsigned int CodePage;
        wchar_t CodePageName[260];
    };
END_ATF_NAMESPACE

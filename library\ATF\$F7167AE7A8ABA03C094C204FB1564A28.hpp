// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct $F7167AE7A8ABA03C094C204FB1564A28
    {
        BYTE gap0[8];
        int *pscode;
    };    
    static_assert(ATF::checkSize<$F7167AE7A8ABA03C094C204FB1564A28, 16>(), "$F7167AE7A8ABA03C094C204FB1564A28");
END_ATF_NAMESPACE

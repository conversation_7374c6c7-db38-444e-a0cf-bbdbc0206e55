// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _param_cash
    {
        unsigned int in_dwAccountSerial;
        unsigned int in_dwAvatorSerial;
        unsigned __int16 in_wSockIndex;
        bool in_bAdjustDiscount;
        bool in_bOneN_One;
        bool in_bSetDiscount;
        bool in_bLimited_Sale;
    public:
        _param_cash(unsigned int dwAccount, unsigned int dwAvator, uint16_t wSock);
        void ctor__param_cash(unsigned int dwAccount, unsigned int dwAvator, uint16_t wSock);
        ~_param_cash();
        void dtor__param_cash();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_param_cash, 16>(), "_param_cash");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPathMgrVtbl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CPathMgr
    {
        CPathMgrVtbl *vfptr;
        float m_PosPool[16][3];
        char m_Size;
        char m_StartPos;
    public:
        CPathMgr();
        void ctor_CPathMgr();
        void Copy(struct CPathMgr* pDst);
        char GetPathSize();
        void Init();
        int PopNextPath(float* pPos);
        int SearchPathA(struct CMonster* pMon, float* vTarPos, int bBackupRestore);
        ~CPathMgr();
        void dtor_CPathMgr();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<CPathMgr, 208>(), "CPathMgr");
END_ATF_NAMESPACE

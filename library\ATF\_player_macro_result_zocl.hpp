// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _player_macro_result_zocl
    {
        unsigned int potion[3];
        unsigned int potionvalue[3];
        unsigned int behavior[30];
        char chatting[10][81];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

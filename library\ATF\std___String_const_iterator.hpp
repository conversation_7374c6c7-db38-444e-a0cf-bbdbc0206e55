// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <std___Ranit_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _String_const_iterator<wchar_t,char_traits<wchar_t>,allocator<wchar_t> > : _Ranit_base<wchar_t,__int64,wchar_t const *,wchar_t const &,_Iterator_base>
        {
            const wchar_t *_Myptr;
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std___Ranit_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _String_const_iterator<char,char_traits<char>,allocator<char> > : _Ranit_base<char,__int64,char const *,char const &,_Iterator_base>
        {
            const char *_Myptr;
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std___Ranit_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  _String_const_iterator<char,char_traits<char>,_DebugHeapAllocator<char> > : _Ranit_base<char,__int64,char const *,char const &,_Iterator_base>
        {
            const char *_Myptr;
        };
    }; // end namespace std
END_ATF_NAMESPACE

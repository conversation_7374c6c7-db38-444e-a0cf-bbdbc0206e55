// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAniCamera.hpp>
#include <CBsp.hpp>
#include <CExtDummy.hpp>
#include <CLevelVtbl.hpp>
#include <CSkyBox.hpp>
#include <CTimer.hpp>
#include <D3DXMATRIX.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct CLevel
    {
        CLevelVtbl *vfptr;
        char mMapName[256];
        float mCamPos[3];
        D3DXMATRIX mMatView;
        int mIsLoadedBsp;
        CBsp *mBsp;
        CSkyBox *mSkyBox;
        CAniCamera mAutoAniCam;
        CTimer mTimer;
        CExtDummy mDummy;
        unsigned int mLightTexMemSize;
        unsigned int mMapTexMemSize;
        unsigned int mSkyTexMemSize;
        unsigned int mEntityTexMemSize;
        unsigned int mEnvironment;
    public:
        void CalcR3Fog();
        void DrawBBox(float* arg_0, float* arg_1, uint32_t arg_2);
        void DrawBBox(int16_t* arg_0, int16_t* arg_1, uint32_t arg_2);
        void DrawLeafBBox();
        void DrawMapAlphaRender(float* arg_0);
        void DrawMapEntitiesRender();
        void DrawMapRender();
        void DrawMatBBox();
        void DrawShadowRender(float* arg_0, float* arg_1, float* arg_2);
        void DrawSkyBoxRender();
        void DrawTestBox(float* arg_0, float* arg_1, uint32_t arg_2);
        void FrameMove();
        unsigned int GetEnvironment();
        float GetFirstYpos(float* arg_0, float* arg_1, float* arg_2);
        float GetFirstYpos(float* arg_0, int arg_1);
        float GetFirstYpos(float* arg_0, int16_t* arg_1, int16_t* arg_2);
        void GetFrustumNormalPlane(float** arg_0);
        char* GetMapName();
        int GetNextYpos(float* arg_0, float* arg_1);
        int64_t GetNextYposFar(float* arg_0, float* arg_1, float* arg_2);
        int64_t GetNextYposFarProgress(float* arg_0, float* arg_1, float* arg_2);
        int GetNextYposForServer(float* arg_0, float* arg_1);
        int64_t GetNextYposForServerFar(float* arg_0, float* arg_1, float* arg_2);
        int GetNextYposNoAttr(float* arg_0, float* arg_1);
        uint32_t GetPath(float* arg_0, float* arg_1, float** arg_2, uint32_t* arg_3, int arg_4);
        uint32_t GetPathFromDepth(float* arg_0, float* arg_1, int arg_2, float** arg_3, uint32_t* arg_4);
        int64_t GetPointFromScreenRay(int32_t arg_0, int32_t arg_1, float** arg_2);
        int64_t GetPointFromScreenRayFar(int32_t arg_0, int32_t arg_1, float** arg_2);
        void HearMapSound();
        int IsCollisionRayAABB(int32_t arg_0, int32_t arg_1, float* arg_2, float* arg_3, float** arg_4);
        int IsLoadedBsp();
        void LoadLevel(char* arg_0);
        void PrepareShadowRender(float* arg_0, void* arg_1, float arg_2, uint32_t arg_3, float arg_4, float arg_5);
        void ReLoadAllMaterial();
        void ReleaseLevel();
        void ScreenShot();
        void SetCameraPos(float* arg_0);
        void SetEnvironment(uint32_t arg_0);
        void SetViewMatrix(struct D3DXMATRIX* arg_0);
        ~CLevel();
        int64_t dtor_CLevel();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

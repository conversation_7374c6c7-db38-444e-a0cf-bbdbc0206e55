// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_cancel_raceboss_msg_result_zoct.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _cancel_raceboss_msg_result_zoctsize2_ptr = int (WINAPIV*)(struct _cancel_raceboss_msg_result_zoct*);
        using _cancel_raceboss_msg_result_zoctsize2_clbk = int (WINAPIV*)(struct _cancel_raceboss_msg_result_zoct*, _cancel_raceboss_msg_result_zoctsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

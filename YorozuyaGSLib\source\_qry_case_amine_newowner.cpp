#include <_qry_case_amine_newowner.hpp>


START_ATF_NAMESPACE
    _qry_case_amine_newowner::_qry_case_amine_newowner()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_amine_newowner*);
        (org_ptr(0x1402d41d0L))(this);
    };
    void _qry_case_amine_newowner::ctor__qry_case_amine_newowner()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_amine_newowner*);
        (org_ptr(0x1402d41d0L))(this);
    };
    int _qry_case_amine_newowner::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_amine_newowner*);
        return (org_ptr(0x1402d41f0L))(this);
    };
END_ATF_NAMESPACE

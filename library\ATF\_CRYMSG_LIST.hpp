// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _CRYMSG_LIST
    {
        struct _LIST
        {
            char wszCryMsg[65];
        public:
            void Init();
            _LIST();
            void ctor__LIST();
        };
        _LIST m_List[10];
    public:
        void Init();
        _CRYMSG_LIST();
        void ctor__CRYMSG_LIST();
    };    
    static_assert(ATF::checkSize<_CRYMSG_LIST, 650>(), "_CRYMSG_LIST");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    enum HSS_SCENE_STATE
    {
      HS_SCENE_INIT = 0x0,
      HS_SCENE_BATTLE_TIME = 0x1,
      HS_SCENE_BATTLE_END_WAIT_TIME = 0x2,
      HS_SCENE_KEEPER_ATTACKABLE_TIME = 0x3,
      HS_SCENE_KEEPER_DEATTACKABLE_TIME = 0x4,
      HS_SCENE_KEEPER_DIE_TIME = 0x5,
      HS_SCENE_KEEPER_CHAOS_TIME = 0x6,
      HS_SCENE_STATE_MAX = 0x7,
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _started_vote_inform_zocl
    {
        int nVoteSerial;
        char byLimGrade;
        unsigned __int16 wLeftSec;
        bool bActed;
        unsigned __int16 wPoint[3];
        bool bHurry;
        unsigned __int16 wContentSize;
        char wszContent[1280];
    public:
        _started_vote_inform_zocl();
        void ctor__started_vote_inform_zocl();
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

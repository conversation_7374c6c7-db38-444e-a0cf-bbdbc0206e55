// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS_INDIV.hpp>


START_ATF_NAMESPACE
    struct _RENAME_POTION_USE_INFO
    {
        _STORAGE_POS_INDIV ItemInfo;
        char wszChangeName[17];
    public:
        void Init();
        _RENAME_POTION_USE_INFO();
        void ctor__RENAME_POTION_USE_INFO();
    };    
    static_assert(ATF::checkSize<_RENAME_POTION_USE_INFO, 21>(), "_RENAME_POTION_USE_INFO");
END_ATF_NAMESPACE

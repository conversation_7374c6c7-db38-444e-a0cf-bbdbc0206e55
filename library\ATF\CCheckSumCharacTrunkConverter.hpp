// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCheckSumBaseConverter.hpp>
#include <CCheckSumCharacAccountTrunkData.hpp>
#include <_AVATOR_DATA.hpp>


START_ATF_NAMESPACE
    struct  CCheckSumCharacTrunkConverter : CCheckSumBaseConverter
    {
    public:
        void Convert(struct _AVATOR_DATA* pAvator, struct CCheckSumCharacAccountTrunkData* pkCheckSum);
        void ConvertTrunk(unsigned int dwSerial, long double* pVal);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCashDbWorkerPH.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CCashDbWorkerPHctor_CCashDbWorkerPH2_ptr = void (WINAPIV*)(struct CCashDbWorkerPH*);
        using CCashDbWorkerPHctor_CCashDbWorkerPH2_clbk = void (WINAPIV*)(struct CCashDbWorkerPH*, CCashDbWorkerPHctor_CCashDbWorkerPH2_ptr);
        
        using CCashDbWorkerPHdtor_CCashDbWorkerPH7_ptr = void (WINAPIV*)(struct CCashDbWorkerPH*);
        using CCashDbWorkerPHdtor_CCashDbWorkerPH7_clbk = void (WINAPIV*)(struct CCashDbWorkerPH*, CCashDbWorkerPHdtor_CCashDbWorkerPH7_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _monster_sp_fld : _base_fld
    {
        int m_nSpecialAttType;
        char m_strSpecialAttCode[64];
        int m_nAttLv;
        float m_fSpecialRange;
        int m_nMotiveCondition;
        int m_nMotiveValue;
        int m_nMotiveExceptionCondition;
        int m_nMotiveExceptionValue;
        int m_nMotiveTime;
        int m_nDstType;
        int m_nLimitCount;
        int m_nProbability;
    };
END_ATF_NAMESPACE

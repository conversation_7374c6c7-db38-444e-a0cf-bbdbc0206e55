// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPcBangFavor.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CPcBangFavorctor_CPcBangFavor2_ptr = void (WINAPIV*)(struct CPcBangFavor*);
        using CPcBangFavorctor_CPcBangFavor2_clbk = void (WINAPIV*)(struct CPcBangFavor*, CPcBangFavorctor_CPcBangFavor2_ptr);
        using CPcBangFavorClassCodePasing4_ptr = unsigned int (WINAPIV*)(struct CPcBangFavor*, struct _AVATOR_DATA*, struct CPlayer*);
        using CPcBangFavorClassCodePasing4_clbk = unsigned int (WINAPIV*)(struct CPcBangFavor*, struct _AVATOR_DATA*, struct CPlayer*, CPcBangFavorClassCodePasing4_ptr);
        using CPcBangFavorInitialzie6_ptr = int (WINAPIV*)(struct CPcBangFavor*);
        using CPcBangFavorInitialzie6_clbk = int (WINAPIV*)(struct CPcBangFavor*, CPcBangFavorInitialzie6_ptr);
        using CPcBangFavorInstance8_ptr = struct CPcBangFavor* (WINAPIV*)();
        using CPcBangFavorInstance8_clbk = struct CPcBangFavor* (WINAPIV*)(CPcBangFavorInstance8_ptr);
        using CPcBangFavorIsEnable10_ptr = int (WINAPIV*)(struct CPcBangFavor*);
        using CPcBangFavorIsEnable10_clbk = int (WINAPIV*)(struct CPcBangFavor*, CPcBangFavorIsEnable10_ptr);
        using CPcBangFavorLoadPcBangData12_ptr = int (WINAPIV*)(struct CPcBangFavor*);
        using CPcBangFavorLoadPcBangData12_clbk = int (WINAPIV*)(struct CPcBangFavor*, CPcBangFavorLoadPcBangData12_ptr);
        using CPcBangFavorPcBangDeleteItem14_ptr = void (WINAPIV*)(struct CPcBangFavor*, struct CPlayer*);
        using CPcBangFavorPcBangDeleteItem14_clbk = void (WINAPIV*)(struct CPcBangFavor*, struct CPlayer*, CPcBangFavorPcBangDeleteItem14_ptr);
        using CPcBangFavorPcBangGiveItem16_ptr = bool (WINAPIV*)(struct CPcBangFavor*, struct CPlayer*, unsigned int, char*, int);
        using CPcBangFavorPcBangGiveItem16_clbk = bool (WINAPIV*)(struct CPcBangFavor*, struct CPlayer*, unsigned int, char*, int, CPcBangFavorPcBangGiveItem16_ptr);
        
        using CPcBangFavordtor_CPcBangFavor21_ptr = void (WINAPIV*)(struct CPcBangFavor*);
        using CPcBangFavordtor_CPcBangFavor21_clbk = void (WINAPIV*)(struct CPcBangFavor*, CPcBangFavordtor_CPcBangFavor21_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

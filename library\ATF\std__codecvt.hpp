// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_Cvtvec.hpp>
#include <std__codecvt_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  codecvt<unsigned short,char,int> : codecvt_base
        {
            _Cvtvec _Cvt;
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <std__codecvt_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  codecvt<char,char,int> : codecvt_base
        {
        };
    }; // end namespace std
END_ATF_NAMESPACE
#include <_Cvtvec.hpp>
#include <std__codecvt_base.hpp>


START_ATF_NAMESPACE
    namespace std
    {
        template<>
        struct  codecvt<wchar_t,char,int> : codecvt_base
        {
            _Cvtvec _Cvt;
        };
    }; // end namespace std
END_ATF_NAMESPACE

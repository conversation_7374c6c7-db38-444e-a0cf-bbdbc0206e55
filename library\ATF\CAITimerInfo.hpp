// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CAITimer.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using CAITimerctor_CAITimer2_ptr = void (WINAPIV*)(struct CAITimer*);
        using CAITimerctor_CAITimer2_clbk = void (WINAPIV*)(struct CAITimer*, CAITimerctor_CAITimer2_ptr);
        using CAITimerCheck4_ptr = int (WINAPIV*)(struct CAITimer*);
        using CAITimerCheck4_clbk = int (WINAPIV*)(struct CAITimer*, CAITimerCheck4_ptr);
        using CAITimerInit6_ptr = void (WINAPIV*)(struct CAITimer*, unsigned int);
        using CAITimerInit6_clbk = void (WINAPIV*)(struct CAITimer*, unsigned int, CAITimerInit6_ptr);
        using CAITimerSet8_ptr = void (WINAPIV*)(struct CAITimer*, unsigned int);
        using CAITimerSet8_clbk = void (WINAPIV*)(struct CAITimer*, unsigned int, CAITimerSet8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

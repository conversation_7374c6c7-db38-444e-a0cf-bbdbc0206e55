// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _atrade_taxrate_result_zocl
    {
        char byTaxRate;
        unsigned int dwEmblemBack;
        unsigned int dwEmblemMark;
        char wszGuildName[17];
    public:
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _stone_enter_inform_zocl
    {
        bool bNow;
        unsigned __int16 wTotalSecTime;
        __int16 zHurrySecTime;
        __int16 zExitSecTime;
        unsigned __int16 wElapseTime;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

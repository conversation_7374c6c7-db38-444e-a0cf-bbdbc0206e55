// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPartyModeKillMonsterExpNotify.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using CPartyModeKillMonsterExpNotifyAdd2_ptr = bool (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*, struct CPlayer*, float);
        using CPartyModeKillMonsterExpNotifyAdd2_clbk = bool (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*, struct CPlayer*, float, CPartyModeKillMonsterExpNotifyAdd2_ptr);
        
        using CPartyModeKillMonsterExpNotifyctor_CPartyModeKillMonsterExpNotify4_ptr = void (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*);
        using CPartyModeKillMonsterExpNotifyctor_CPartyModeKillMonsterExpNotify4_clbk = void (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*, CPartyModeKillMonsterExpNotifyctor_CPartyModeKillMonsterExpNotify4_ptr);
        
        using CPartyModeKillMonsterExpNotifyctor_Notify6_ptr = void (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*);
        using CPartyModeKillMonsterExpNotifyctor_Notify6_clbk = void (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*, CPartyModeKillMonsterExpNotifyctor_Notify6_ptr);
        using CPartyModeKillMonsterExpNotifySetKillMonsterFlag8_ptr = void (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*);
        using CPartyModeKillMonsterExpNotifySetKillMonsterFlag8_clbk = void (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*, CPartyModeKillMonsterExpNotifySetKillMonsterFlag8_ptr);
        
        using CPartyModeKillMonsterExpNotifydtor_CPartyModeKillMonsterExpNotify10_ptr = void (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*);
        using CPartyModeKillMonsterExpNotifydtor_CPartyModeKillMonsterExpNotify10_clbk = void (WINAPIV*)(struct CPartyModeKillMonsterExpNotify*, CPartyModeKillMonsterExpNotifydtor_CPartyModeKillMonsterExpNotify10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

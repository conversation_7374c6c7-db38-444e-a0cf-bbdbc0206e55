// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <tagPOINT.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct tagWINDOWPLACEMENT
    {
        unsigned int length;
        unsigned int flags;
        unsigned int showCmd;
        tagPOINT ptMinPosition;
        tagPOINT ptMaxPosition;
        tagRECT rcNormalPosition;
    };
END_ATF_NAMESPACE

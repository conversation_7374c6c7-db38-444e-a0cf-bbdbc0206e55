// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <ATL__ClassesAllowedInStream.hpp>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    namespace ATL
    {
        #pragma pack(push, 8)
        struct ATL_PROPMAP_ENTRY
        {
            const wchar_t *szDesc;
            int dispid;
            _GUID *pclsidPropPage;
            _GUID *piidDispatch;
            unsigned int dwOffsetData;
            unsigned int dwSizeData;
            unsigned __int16 vt;
            ClassesAllowedInStream rgclsidAllowed;
            unsigned int cclsidAllowed;
        };
        #pragma pack(pop)
    }; // end namespace ATL
END_ATF_NAMESPACE

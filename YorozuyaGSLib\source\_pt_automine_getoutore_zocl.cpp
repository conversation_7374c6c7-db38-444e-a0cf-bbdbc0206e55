#include <_pt_automine_getoutore_zocl.hpp>


START_ATF_NAMESPACE
    _pt_automine_getoutore_zocl::_pt_automine_getoutore_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _pt_automine_getoutore_zocl*);
        (org_ptr(0x1402d3f50L))(this);
    };
    void _pt_automine_getoutore_zocl::ctor__pt_automine_getoutore_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _pt_automine_getoutore_zocl*);
        (org_ptr(0x1402d3f50L))(this);
    };
    int _pt_automine_getoutore_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _pt_automine_getoutore_zocl*);
        return (org_ptr(0x1402d3fa0L))(this);
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CCharacter.hpp>
#include <CMyTimer.hpp>
#include <_dummy_position.hpp>
#include <_monster_fld.hpp>
#include <_object_id.hpp>
#include <_stone_create_setdata.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  CHolyStone : CCharacter
    {
        bool m_bOper;
        int m_nHP;
        int m_nMaxHP;
        unsigned int m_dwLastDestroyTime;
        _monster_fld *m_pRec;
        _dummy_position *m_pDum;
        char m_byMasterRaceCode;
        int m_nDefPart[5];
        int m_nOldRate;
        unsigned int m_dwLastRecoverTime;
        int m_nCurrLootIndex;
        int m_nEndLootIndex;
        int m_nCurrDropIndex;
        unsigned __int16 m_wMagnifications;
        unsigned __int16 m_wRange;
        unsigned __int16 m_wDropCntOnce;
        unsigned __int16 m_wAddCountWithPlayer;
        CMyTimer m_tmrDropTime;
    public:
        void AutoRecover();
        CHolyStone();
        void ctor_CHolyStone();
        uint16_t CalcCurHPRate();
        bool Create(struct _stone_create_setdata* pData);
        bool Destroy(char byDestroyCode, struct CCharacter* pAtter);
        void DropItem();
        uint16_t GetAddCountWithPlayer();
        int GetAttackDP();
        int GetDefFC(int nAttactPart, struct CCharacter* pAttChar, int* pnConvertPart);
        float GetDefFacing(int nPart);
        float GetDefGap(int nPart);
        int GetDefSkill(bool bBackAttack);
        int GetFireTol();
        int GetHP();
        int GetLevel();
        int GetMaxHP();
        static unsigned int GetNewStoneSerial();
        char* GetObjName();
        int GetObjRace();
        int GetSoilTol();
        int GetWaterTol();
        float GetWeaponAdjust();
        float GetWidth();
        int GetWindTol();
        bool Init(struct _object_id* pID);
        bool IsBeAttackedAble(bool bFirst);
        bool IsBeDamagedAble(struct CCharacter* pAtter);
        bool IsChangedHP(uint16_t wAlterRate);
        void Loop();
        void OutOfSec();
        void SendMsg_Create();
        void SendMsg_Destroy(char byDestroyCode, unsigned int dwDestroySerial);
        void SendMsg_FixPosition(int n);
        void SendMsg_StoneAlterOper();
        int SetDamage(int nDam, struct CCharacter* pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn);
        void SetDropItem();
        bool SetHP(int nHP, bool bOver);
        void SetOper(bool bOper, float fHPRate);
        ~CHolyStone();
        void dtor_CHolyStone();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<CHolyStone, 0x7C0>(), "CHolyStone");
END_ATF_NAMESPACE

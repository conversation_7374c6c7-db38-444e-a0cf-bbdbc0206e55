// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct Sun
    {
    public:
        struct D3DXVECTOR4* ComputeAttenuation(short retstr, struct D3DXVECTOR4* arg_0, float arg_1);
        void Dump2(struct _iobuf* arg_0);
        void FrameMove();
        float GetAlpha();
        struct D3DXVECTOR4* GetColor(short retstr);
        struct D3DXVECTOR4* GetColorAndIntensity(short retstr);
        struct D3DXVECTOR4* GetColorWithIntensity(short retstr);
        void GetDirection(float* arg_0);
        struct D3DXVECTOR4* GetDirection(short retstr);
        float GetIntensity();
        float GetNightAlpha();
        void Interpolate(struct Sun* arg_0, struct Sun* arg_1, float arg_2);
        void InvalidateSun();
        void Read2(struct _iobuf* arg_0);
        void Render();
        void RestoreSun();
        void SetAlpha(float arg_0);
        void SetNightAlpha(float arg_0);
        void SetScale(float arg_0);
        void SetSunThetaPhi(float arg_0, float arg_1);
        void SetTime(float arg_0);
        ~Sun();
        int64_t dtor_Sun();
    }
    ;
END_ATF_NAMESPACE

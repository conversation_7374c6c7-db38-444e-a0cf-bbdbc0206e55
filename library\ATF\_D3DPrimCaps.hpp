// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _D3DPrimCaps
    {
        unsigned int dwSize;
        unsigned int dwMiscCaps;
        unsigned int dwRasterCaps;
        unsigned int dwZCmpCaps;
        unsigned int dwSrcBlendCaps;
        unsigned int dwDestBlendCaps;
        unsigned int dwAlphaCmpCaps;
        unsigned int dwShadeCaps;
        unsigned int dwTextureCaps;
        unsigned int dwTextureFilterCaps;
        unsigned int dwTextureBlendCaps;
        unsigned int dwTextureAddressCaps;
        unsigned int dwStippleWidth;
        unsigned int dwStippleHeight;
    };
END_ATF_NAMESPACE
